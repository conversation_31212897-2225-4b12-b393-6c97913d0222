﻿// #define USE_RUST_FP

using Combat;
using Contexts;
using Cysharp.Text;
using Effect;
using EPOOutline;
using Go.GoComp;
using Go.PureGo;
using Monster;
using Share.Common.ObjPool;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using Utilities;
using WizardGames.Soc.Common.Combat;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Const;
using WizardGames.Soc.Common.Contexts;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Data.DataItem;
using WizardGames.Soc.Common.Data.gun;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.Profile;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.State.Character;
using WizardGames.Soc.Common.Unity;
using WizardGames.Soc.Common.Unity.Animation;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.Character.State;
using WizardGames.Soc.Common.Unity.Character.State.Event;
using WizardGames.Soc.Common.Unity.Config;
using WizardGames.Soc.Common.Unity.Construction;
using WizardGames.Soc.Common.Unity.DebugLog;
using WizardGames.Soc.Common.Unity.Defines;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Extension;
using WizardGames.Soc.Common.Unity.Go;
using WizardGames.Soc.Common.Unity.HeldItem;
using WizardGames.Soc.Common.Unity.Horse;
using WizardGames.Soc.Common.Unity.Manager;
using WizardGames.Soc.Common.Unity.ObjPool;
using WizardGames.Soc.Common.Unity.Parachute;
using WizardGames.Soc.Common.Unity.Systems;
using WizardGames.Soc.Common.Unity.Systems.Weapon;
using WizardGames.Soc.Common.Unity.Utility;
using WizardGames.Soc.Common.Unity.Utility.Define;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Weapon;
using WizardGames.Soc.Procedural;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocClient.ActionExecutor;
using WizardGames.Soc.SocClient.Collection;
using WizardGames.Soc.SocClient.Control;
using WizardGames.Soc.SocClient.Fx;
using WizardGames.Soc.SocClient.Go;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Player;
using WizardGames.Soc.SocClient.Player.HeldItem;
using WizardGames.Soc.SocClient.Systems;
using WizardGames.Soc.SocClient.Ui;
using WizardGames.Soc.SocClient.Water;
using WizardGames.Soc.SocClient.Weapon;
using WizardGames.Soc.Common.Utility;
using WizardGames.SocConst.Soc.Const;
using Vector3 = UnityEngine.Vector3;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.SocClient.Utility;
using UnityEngine.AzureSky;
using WizardGames.Soc.Common.Character;
using WizardGames.Soc.SocClient;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Common.Unity.Character.Utility;
using WizardGames.Soc.Common.Unity.Contexts;
#if UNITY_EDITOR && SOC_CLIENT
using WizardGames.Soc.SocClient.Cheating;
#endif
namespace WizardGames.Soc.Common.Syncronization
{
    /// <summary>
    /// 我的角色管理器
    /// </summary>
    public partial class MgrMyPlayer : MgrBase
    {
        protected static SocLogger logger = LogHelper.GetLogger(typeof(MgrMyPlayer));
        
        public static bool ShowHitFont = false;//伤害跳字展示总开关

        /// <summary>
        /// 我的实体Id
        /// </summary>
        public long MyEntityId => MyEntityLocal?.EntityId ?? -1;
        
        /// <summary>
        /// 我的本地实体对象 观察目标也算
        /// </summary>
        /// <remarks>
        /// 这个实体对象是进行本地操作后的实体数据，有本地操作预判。
        /// 这个数据与服务端最终确认的实体数据可能会有差异。
        ///
        /// </remarks>
        public PlayerEntity MyEntityLocal = new PlayerEntity(0);

        public bool WantChangeSkin = false;

        public PlayerEntity MyEntityServer => Mc.Entity.GetPlayerEntity(MyEntityId);

        public RootNodeComponent MyRootNode => MyEntityServer?.RootNode;

        public long EquipWeaponId;

        /// <summary>
        /// 当前最新的用户操作序列号
        /// </summary>
        public long UserCmdSequence = -1;

        /// <summary>
        /// 角色操作指令序列号历史记录
        /// </summary>
        public List<long> UserCmdSequenceHistory = new List<long>();

        /// <summary>
        /// 角色位置信息历史记录
        /// </summary>
        public Dictionary<long, PlayerEntity> MyPlayerHistory = new Dictionary<long, PlayerEntity>();

        #region Go

        /// <summary>
        /// 第一人称资源代理
        /// </summary>
        //public FpPlayerGoProxy FpPlayerGoProxy;
        //public FpGoProxy FpGoProxy;

        /// <summary>
        /// 第三人称资源代理
        /// </summary>
        // public PlayerGoProxy TpPlayerGoProxy;

        public ClientPlayerFpGo FpPlayerGo;
        public ClientPlayerGo TpPlayerGo;

        /// <summary>
        /// 主角根节点
        /// </summary>
        public GameObject MyPlayer;

        public Transform MyPlayerTransform;

        /// <summary>
        /// 主角渲染根节点
        /// </summary>
        public GameObject MyPlayerView;

        /// <summary>
        /// 主角第一人称渲染预制
        /// </summary>
        //public GameObject MyPlayerViewGoFp => FpGoProxy?.GetMainGo();

        /// <summary>
        /// 主角第三人称渲染预制
        /// </summary>
        public GameObject MyPlayerViewGoTp => TpPlayerGo?.MainGo;

        /// <summary>
        /// 我的Cc对象（本地预测）
        /// </summary>
        public GameObject MyPlayerCc => TpPlayerGo?.ColliderGo;

        /// <summary>
        /// 我的Cc对象Transform（本地预测）
        /// </summary>
        public Transform MyPlayerColliderTransform => TpPlayerGo?.ColliderTransform;

        /// <summary>
        /// 我的第一人称角色状态机
        /// </summary>
        public Animator MyFpAnimator => FpPlayerGo.Animator; //FpGoProxy?.GetNowAnimator();

        /// <summary>
        /// 我的第三人称角色状态机
        /// </summary>
        public Animator MyTpAnimator => TpPlayerGo?.Animator;


        /// <summary>
        /// 我的角色控制器脚本
        /// </summary>
        public SocCharacterController MyCharacterController => TpPlayerGo?.SocCharacterController;

        /// <summary>
        /// 装备的第一人称武器,注意：由于此接口依赖CurrentWeaponId，尤其注意CurrentWeaponId变化过程前后的逻辑
        /// </summary>
        public GameObject EquipFpWeapon
        {
            get
            {
                if (TryGetCurrentHeldItemGo(out BasePlayerFpHeldItemGo go, true))
                {
                    return go.MainGo;
                }
                return null;
            }//return TryGetCurrentWeapon(out MgrMyWeapon weapon) ? weapon.FpWeaponGo : null;
        }

        /// <summary>
        /// 装备的第三人称武器
        /// </summary>
        public GameObject EquipTpWeapon
        {
            get
            {
                if (TryGetCurrentHeldItemGo(out BasePlayerFpHeldItemGo go, false))
                {
                    return go.MainGo;
                }
                return null;
            }
        } //TryGetCurrentWeapon(out MgrMyWeapon weapon) ? weapon.TpWeaponGo : null;

        public Camera GetMyCamera => Mc.Camera.SceneCamera;

        public CameraState CameraState => Mc.Camera.NowState;

        #endregion

        public CameraState ZiplineCamState = CameraState.FirstSingle;

        private float _ccOverrideSpeed = -1;
        /// <summary>
        /// 覆盖cc移速的参数（>=0才开始生效）
        /// </summary>
        public float CCOverrideSpeed
        {
            get
            {
                return _ccOverrideSpeed;
            }
            set
            {
                if (MyEntityServer != null && !MyEntityServer.DebugEnable)
                {
                    _ccOverrideSpeed = -1;
                }
                else
                {
                    _ccOverrideSpeed = value;
                }
            }
        }

        public float CurLadderBlend;

        /// <summary>
        /// 角色加载完成的回调
        /// </summary>
        public Action MyPlayerLoadSuccess;

        /// <summary>
        /// 自己角色属性变化的通知 ，目前是赋值前调用，可以自己计算变化值。 todo:规范写法
        /// </summary>
        public Action<PlayerEntity> OnEntityUpdate;

        public PlayerLogicParams playerLogicParams;

        public ulong RoleId => MyEntityLocal?.RoleId ?? 0;

        /// <summary>
        /// 主角装备的第一人称的资源，long为weaponEntityId
        /// </summary>
        // public Dictionary<long, MgrMyWeapon> MyWeapons = new Dictionary<long, MgrMyWeapon>();

        /// <summary>
        /// 主角灯光管理
        /// </summary>
        public MgrMyLights mgrMyLights = new MgrMyLights();

        public PlayerBoneManager PlayerBoneManager;


        #region scene set up

        public GameObject SceneSetUpObj;

        public Light SceneLight;

        #endregion

        /// <summary>
        /// 正在做平滑的计时器
        /// </summary>
        public long smoothRenderTime = -1;

        private HistoryPlayerEntity smoothNowModel = new HistoryPlayerEntity();

        /// <summary>
        /// 飞天开关
        /// </summary>
        private bool openFly = false;

        /// <summary>
        /// 记录玩家是否处于观战模式
        /// </summary>
        private bool isServerEntityObserver = false;

        /// <summary>
        /// 是否开启Ik功能
        /// </summary>
        public bool isIkOpen = true;

        /// <summary>
        /// 是否开启疾跑换弹融合配置功能
        /// </summary>
        public bool isLayerWeightOpen = true;

        public bool IsBeLightened = false;

        public bool StreamingLoaded { get; set; } = false;

        public bool ConstructionLoaded { get; set; } = false;

        public bool IsWaitingReconnect = false;


        public bool preloadFinish => StreamingLoaded && ConstructionLoaded;

        public bool isOpenedRespawnWindow = false;
        public bool isOpenedDyingWindow = false;

        /// <summary>
        /// 第三人称刷卡
        /// </summary>
        private PureGoItem _tpCard;

        /// <summary>
        /// 第一人称刷卡
        /// </summary>
        private PureGoItem _fpCard;

        //fp穿插开关记录变量，true表示插场景
        //public bool FpCrossSwitch = true; //默认材质应该是穿插的
        //public bool ForceUpdateCross = false;
        //protected MaterialPropertyBlock block = new MaterialPropertyBlock();

        //tp玩家脱绑镜头用变量，tp近战吸附攻击会触发
        public bool TpOffsetYawEnable = false;
        public float TpOffsetRecoverTime; //回正时间

        //上一次更新是不是处于小眼睛模式
        private bool bLastUpdateIsInLittleEyeMode = false;
        //上一次更新的相机状态
        private CameraState LastUpdateCameraState = CameraState.None;

        protected PlayerCmdPreserver CmdPreserver = new PlayerCmdPreserver();
        public PlayerBreatheViewMotionController BreatheViewMotionController = new PlayerBreatheViewMotionController();
        public FpHeldItemBreatheMotionController FpHeldItemBreatheMotionController = new FpHeldItemBreatheMotionController();
        public FpHeldItemMoveMotionController FpHeldItemMoveMotionController = new FpHeldItemMoveMotionController();
        public BagHeldItemUseController BagHeldItemUseController;//背包手持物使用
        
        public ThrowParabolaController ThrowParabolaController = new ThrowParabolaController();

        public LocalHeldItemEntityController LocalHeldItemEntityController;
        //后座力动画数据
        public RecoilData RecoilData = null;

        protected HashSet<long> SubscribeIds = new HashSet<long>();

        public long InteractiveTargetId = 0;//异步交互缓存用

        private Dictionary<PlayerActionHoldStateEnum, int> state2tips = new()
        {
            [PlayerActionHoldStateEnum.CollectWaterStart] = 3026,
            [PlayerActionHoldStateEnum.CollectWaterLoop] = 3026,
            [PlayerActionHoldStateEnum.CollectWaterEnd] = 3026,
            [PlayerActionHoldStateEnum.DrinkWater] = 3024,
            [PlayerActionHoldStateEnum.PourWater] = 3025,
        };
        
        private Dictionary<PlayerInteractiveType, int> waterstate2tips = new()
        {
            [PlayerInteractiveType.CollectWater] = 3026,
            [PlayerInteractiveType.DrinkWater] = 3024,
            [PlayerInteractiveType.PourWater] = 3025,
        };

        private const int IsFetchingBottle = 3023;

        public UiHud UiHud => Mc.Ui.GetWindow("UiHud") as UiHud;

        #region drive

        /// <summary>
        /// 上车下车
        /// </summary>
        public Action<long> onMountableChanged;

        /// <summary>
        /// 换座位
        /// </summary>
        public Action<bool, long> onSeatChanged;

        private FxUnderwater fxUnderWater;

        public FxUnderwater FxUnderwater => fxUnderWater;

        #endregion

        public bool EokaPistolAddViewkick = false; //土质手枪的额外viewkick加成

        protected Light selfNightLight;

        public bool AutoDrive { get; private set; } = false;

        public HeldItemFovController HeldItemSceneFovController = new HeldItemFovController();
        public HeldItemFovController HeldItemGunFovController = new HeldItemFovController();

        private long mantleTpModelDisplayerTimer = 0;

        public HorseGoPredict HorseGoPredict;
        
        public ParachuteGoPredict ParachuteGoPredict;

        /// <summary>
        /// 再此时间之后，才可以使用帐篷
        /// </summary>
        private float canUseCampingTentTimeSec = 0;

        /// <summary>
        /// 角色处于濒死状态时，LoadingUI等待TpAnimController加载完成才关闭
        /// </summary>
        public bool IsDyingTpAnimControllerLoaded => MyEntityLocal.PoseState != PlayerPoseStateEnum.Dying || TpPlayerGo.Animator.runtimeAnimatorController != null;

        public bool IsDeadWindowActive
        {
            get
            {
                var uiRespawn = Mc.Ui?.GetWindow("UiRespawn");
                var uiDeadInfo = Mc.Ui?.GetWindow("UiDeadInfo");
                return uiRespawn != null && uiRespawn.IsActive || uiDeadInfo != null && uiDeadInfo.IsActive;
            }
        }
        
#if UNITY_EDITOR
        public CheatingTool cheatingTool = new CheatingTool();
#endif

        /// <summary>
        /// 第一次武器加载标记
        /// </summary>
        public bool IsWeaponLoaded = false;
        
        public override void Init()
        {
            base.Init();
            PlayerBoneManager = new PlayerBoneManager();
            MyEntityLocal.bLocalPlayer = true;
            MyEntityLocal.LongRoleId = Mc.LoginStep.GameRoleID;
            MyEntityLocal.Name = Mc.Config.nickName;
            MyEntityLocal.IsOnline = true;
            MyEntityLocal.AddComponent(new PlayerDrawComponentClient());

            HeldItemSceneFovController.SetFOV(50f);
            HeldItemGunFovController.SetFOV(60);

            SocCharacterController.SetMainThreadMark();
        }

        private BaseMountableGo _currentMountableGo;

        public bool IsMountDriver => MyEntityLocal.IsMountDriver;

        /// <summary>
        /// 当前玩家所在载具Go
        /// </summary>
        public BaseMountableGo GetMountableGo()
        {
            if (MyEntityLocal.MountableId <= 0)
            {
                return null;
            }

            if (_currentMountableGo != null && _currentMountableGo.EntityId == MyEntityLocal.MountableId)
            {
                return _currentMountableGo;
            }

            if (Mc.Go.Gos.TryGetValue(MyEntityLocal.MountableId, out var go))
            {
                _currentMountableGo = (BaseMountableGo)go;
            }
            else
            {
                return null;
            }

            return _currentMountableGo;
        }

        public void SetConstructionLoaded(bool flag)
        {
            ConstructionLoaded = flag;
            CheckFinishLoading();
        }

        public void StartLoadConstruction()
        {
            ConstructionLoaded = false;
            logger.Info("preload---StartLoadConstruction");
        }

        private void OnStreamingCompleted(StreamingSource source)
        {
            Mc.Camera.LateUpdate(0, FpPlayerGo.FpCameraLocator);
            // Camera.main.transform.position = MyPlayerView.transform.position;
            logger.InfoFormat("camera pos preload---StreamingStart, camera:{0}", Camera.main.transform.position);
            source.OnStreamingCompleted += () =>
            {
                FoliageGrid.RefreshAll(true); // 相机移位后 手动刷新草
                logger.Info("preload---Streamingfinish");
                Mc.LoginStep.StepLog("StreamingSource OnStreamingCompleted");
                StreamingLoaded = true;
                CheckFinishLoading();
            };
        }

        private void AfterPreloadFinish()
        {
            bool inSleep = MyEntityLocal.CharacterState == PlayerCharacterStateEnum.UnActive && MyEntityLocal.UnAliveState == PlayerUnAliveStateEnum.Sleep;
            if (!inSleep || Mc.ObserverMode)
            {
                Mc.LoginStep.OnPlayerAwake();
            }
            CheckPlatform(MyEntityServer);

            RefreshStaicShadowMap();
            RefreshSkyRendering();
        }

        private void RefreshStaicShadowMap()
        {
            StaticShadowMapCacheManager.ForceUpdate();
            SkyVisibilityTextureCacheManager.ForceUpdate();
        }

        private void RefreshSkyRendering()
        {
            AzureCustomSkyRenderController.ForceUpdate();
        }

        private void CheckFinishLoading()
        {
            if (preloadFinish && !CombatConfig.bOffline)
                Mc.MyPlayer.MyEntityServer.RemoteCallFinishLoading(ERpcTarget.Simulator);
            if (preloadFinish)
            {
                AfterPreloadFinish();
            }
            TryRemoveUiLoading();
        }

        public void TryRemoveUiLoading()
        {
            var uiloadIng = Mc.Ui.GetWindowT<UiLoading>("UiLoading");
            bool hasLoading = uiloadIng != null && uiloadIng.IsActive;
            Mc.LoginStep.StepLog(ZString.Format("Check Streaming Finish Loading... cond = {0}", hasLoading));
            if (hasLoading&& IsWeaponLoaded)
            {
                uiloadIng.TryRemoveSelf(true);
                // logger.InfoFormat("【DEBUG】尝试Remove UiLoading!!!!\n" + Environment.StackTrace);
            }
        }

        public void CheckStreamingLoaded()
        {
            logger.InfoFormat("camera pos preload----CheckStreamingLoaded,camera:{0}", Camera.main.transform.position);
            StreamingLoaded = false;
            var mainCamera = Camera.main;
            if (mainCamera != null && mainCamera.TryGetComponent<StreamingSource>(out var source))
            {
                Mc.LoginStep.StepLog("MgrMyPlayer CheckStreamingLoad With StreamingSource");
                OnStreamingCompleted(source);
            }
            else
            {
                Mc.LoginStep.StepLog("MgrMyPlayer CheckStreamingLoad no StreamingSource");
                StreamingLoaded = true;
                CheckFinishLoading();
            }
        }

        protected void OnRecoverAllPreCreateConstruction()
        {
            ConstructionLoaded = true;
            CheckFinishLoading();
            logger.Info("preload---------OnRecoverAllPreCreateConstruction");
        }

        public override Task AfterEnterWorld()
        {
            MyPlayer = new GameObject("MyPlayer");
            MyPlayerView = new GameObject("MyPlayerView");
            MyPlayerView.transform.SetParent(MyPlayer.transform);
            MyPlayerView.transform.LocalNormalize();
            MyPlayerTransform = MyPlayer.transform;
            
            ThrowParabolaController.Init(MyPlayer.transform);

            PlayerLogicStateMachine.Instance.AddOrRemoveListeners(true);
            Mc.Msg.AddListener<DamageDataEvent>(EventDefine.Damage, OnSelfDamage);
            
#if SOC_CLIENT
            Mc.Msg.AddListener<long,Vector3,Vector3>(EventDefine.OnPartEntityTransChangeAtTransformAfter,OnTransChange);
#endif
            return base.AfterEnterWorld();
        }
        
        void OnTransChange(long entityId, Vector3 pos, Vector3 rot)
        {
            //非主动情况下，目标梯子的坐标变了
            if (MyEntityLocal != null && MyEntityLocal.ClientLadderTargetId >0 && MyEntityLocal.ClientLadderTargetId ==0)
            {
                MyEntityLocal.ClientLadderTargetId = -1;
                MyEntityLocal.ClientAdsorbTargetIndex = -1;
                
                //通知ui
                if (InteractiveIdListChecker.IsLaddderStateCan())
                {
                    Mc.Msg.FireMsgAtOnce<ELadderState>(EventDefine.LadderStateChange, ELadderState.CAN_NOT);
                }
            }
        }

        public void Bind()
        {
            SubscribeIds.Add(MyEntityLocal.SubscribePropertyChange<int>(PlayerEntity.PropertyIds.MOUNT_SEAT_INDEX, OnMountSeatIndexChanged));
            SubscribeIds.Add(MyEntityLocal.SubscribePropertyChange<int>(PlayerEntity.PropertyIds.VEHICLE_TYPE, OnVehicleTypeChange));
            SubscribeIds.Add(MyEntityLocal.SubscribePropertyChange<byte>(PlayerEntity.PropertyIds.MOVE_STATE, OnMoveStateChange));
            SubscribeIds.Add(MyEntityLocal.SubscribePropertyChange<int>(PlayerEntity.PropertyIds.HORSE_MOUNT_DIR,
                OnHorseMountDirChange));
            SubscribeIds.Add(MyEntityLocal.SubscribePropertyChange<bool>(PlayerEntity.PropertyIds.IS_HOSTILE,
                OnHostileChanged));
            Mc.Msg.AddListener<WindowComBase>(EventDefine.UiOpenEvent, UIStateEvent);
            PlayerLogicStateMachine.Event.BindEvent<PlayerEntity>(StateEventType.OnFireAction, MgrAim.ShowShotAimCallBack);
            PlayerLogicStateMachine.Event.BindEvent<PlayerEntity, PlayerCharacterStateEnum, PlayerCharacterStateEnum>(StateEventType.OnCharacterStateChange, OnCharacterStateChange);
            PlayerLogicStateMachine.Event.BindEvent<PlayerEntity, PlayerPoseStateEnum, PlayerPoseStateEnum>(StateEventType.PoseStateControllerChange, OnCharacterPoseStateChange);
            InitLittleEye();
            InitADS();
            InitModelVisible();
        }
        public void UnBind()
        {
            PlayerLogicStateMachine.Instance.CleanPlayerJobStateDict();
            foreach (long id in SubscribeIds)
            {
                MyEntityLocal.UnSubscribePropertyChange(id);
            }
            SubscribeIds.Clear();
            PlayerLogicStateMachine.Event.UnBindEvent<PlayerEntity>(StateEventType.OnFireAction, MgrAim.ShowShotAimCallBack);
            PlayerLogicStateMachine.Event.UnBindEvent<PlayerEntity, PlayerPoseStateEnum, PlayerPoseStateEnum>(StateEventType.PoseStateControllerChange, OnCharacterPoseStateChange);
            PlayerLogicStateMachine.Event.UnBindEvent<PlayerEntity, PlayerCharacterStateEnum, PlayerCharacterStateEnum>(StateEventType.OnCharacterStateChange, OnCharacterStateChange);
            ReleaseLittleEye();
            ReleaseADS();
            ReleaseModelVisible();
            Mc.Msg.RemoveListener<WindowComBase>(EventDefine.UiOpenEvent, UIStateEvent);
        }
        public override Task OnExitWorld()
        {
            MyPlayerLoadSuccess = null;
            OnEntityUpdate = null;
            MgrLifeCycle.OnChangeLifeCycleFlags -= OnChangeLifeCycleFlags;
            MoveLadderStateInLadder.LadderStepUpAction -= OnLadderStepUp;
            Mc.Msg.RemoveListener<long,Vector3,Vector3>(Unity.Event.EventDefine.OnPartEntityTransChangeAtTransformAfter,OnTransChange);
            // if (FpGoProxy != null && FpGoProxy.PartModelController != null)
            // {
            //     FpGoProxy.PartModelController.DoChangeAction -= this.OnFpEquipChange;
            // }

            mgrMyLights.Release();
            mgrMyLights = null;
            // ActionHoldStatePourWater.Instance.PourWater -= PourWater;
            // ActionHoldStateCollectWater.Instance.CollectWaterStart -= CollectWaterStart;
            // ActionHoldStateCollectWater.Instance.CollectWaterEnd -= CollectWaterEnd;
            LocalHeldItemEntityController?.Release();
            BreatheViewMotionController?.Release();
            FpHeldItemBreatheMotionController?.Release();
            FpHeldItemMoveMotionController?.Release();
            BagHeldItemUseController?.Release();
            BagHeldItemUseController = null;
            SecMoveReportInfo = null;
            ThrowParabolaController.Release();
            Mc.Camera.OnCameraStateChangeAction -= CameraChangeState;
            Mc.Construction.OnRecoverConstructionsAroudPos -= OnRecoverAllPreCreateConstruction;

            UnBind();
            
            PlayerLogicStateMachine.Instance.AddOrRemoveListeners(false);
            
            if (mantleTpModelDisplayerTimer > 0)
            {
                Mc.TimerWheel.CancelTimer(mantleTpModelDisplayerTimer);
                mantleTpModelDisplayerTimer = 0;
            }
            


            onMountableChanged -= OnMountChanged;

            // ReleaseWeapons();
            //MyFpHSScopeController.AdsOnMat = null;
            StreamingLoaded = false;
            ConstructionLoaded = false;
            if (FpPlayerGo != null)
            {
                ClientPlayerFpGo.DestroyClientPlayerFpGo(FpPlayerGo);
                FpPlayerGo = null;
            }
            if (null != PlayerBoneManager)
                PlayerBoneManager.Release();
            // if (null != FpPlayerGo)
            //     FpGoProxy.Clear();
            IsWeaponLoaded = false;
            return base.OnExitWorld();
        }

        // public void ReleaseWeapons()
        // {
        //     foreach (var pair in MyWeapons)
        //     {
        //         pair.Value.Release();
        //     }
        //
        //     MyWeapons.Clear();
        //     EquipWeaponId = -1;
        // }

        public override void CleanUp()
        {
            Mc.MyPlayer.TpPlayerGo = null;
            base.CleanUp();
            IsWaitingReconnect = true;
            StreamingLoaded = false;
            ConstructionLoaded = false;
            Mc.MyPlayer.FxUnderwater?.Clean();
            AnimWarpingSystemUtils.Clear();

            if (_loadFovAssetAsyncId != 0)
            {
                GoPool.CancelAsync(fovPath, _loadFovAssetAsyncId);
                _loadFovAssetAsyncId = 0;
            }
            if (_loadGunFovCurveAsyncId != 0)
            {
                GoPool.CancelAsync(gunFovCurvePath, _loadGunFovCurveAsyncId);
                _loadGunFovCurveAsyncId = 0;
            }
            McCommon.Tables.TbChracterParameter.UnregisterDataUpdateEvent(OnDataUpdate);
            playerLogicParams?.Clear();
            playerLogicParams = null;
        }

        public void InitOnReconnect(ClientPlayerGo playerGo)
        {
            if (IsWaitingReconnect == false)
                return;

            IsWaitingReconnect = false;

            //初始化武器动画, 以及相关动画数据
            // var ctrl = PlayerLoader.GetTpAnimator(playerGo.PlayerEntity.CurrentWeaponTableId
            //                                                             , playerGo.PlayerEntity.CurrentWeaponSkinId);
            SocAnimationManager.Init(playerGo.EntityId, MyTpAnimator, null);

            HeldItemUtility.GetAnimatorPath(playerGo.PlayerEntity.CurrentWeaponTableId, playerGo.PlayerEntity.CurrentWeaponSkinId, out _, out var path, false);
            // PlayerLoader.SwitchTpAnimatorController(playerGo.PlayerEntity.EntityId, path, delegate (AnimatorOverrideController controller)
            // {
            //     SocAnimationManager.Init(playerGo.EntityId, MyTpAnimator, controller);
            // });
            // var itemEntity = playerGo.PlayerEntity.GetCurrentHandEntity<IHeldItemEntity>();
            // Mc.PlayerRes.SwitchTpAnimatorController(playerGo.PlayerEntity.EntityId, itemEntity, path, delegate (AnimatorOverrideController controller)
            // {
            //     ProfilerApi.BeginSample(EProfileFunc.CallBack_Equip_TpAnim);
            //     //SocAnimationManager.Init(playerGo.EntityId, MyTpAnimator, controller);
            //     var runtime = SocAnimationManager.Get(playerGo.PlayerEntity.EntityId);
            //     runtime?.SwitchRuntimeAnimatorController(controller);
            //     ProfilerApi.EndSample(EProfileFunc.CallBack_Equip_TpAnim);
            // });

            PlayerBoneManager.Init();
            AnimWarpingSystemUtils.InitRes();

            PlayerLogicStateMachine.Event.Emit(StateEventType.RefreshAnim, playerGo.PlayerEntity);

            //再次调用加载完成回调
            MyPlayerLoadSuccess.Invoke();
        }

        //玩家自己初始化
        public virtual void Start(PlayerEntity serverEntity)
        {
            SubscribeIds.Clear();
            InitEntityModelByServerFullSpanShot(serverEntity); //后面的go创建流程依赖entity某些数据，因此这个函数提前调用
            //适配replay的逻辑
            if (Mc.MyPlayer.TpPlayerGo == null)
            {
                Mc.MyPlayer.TpPlayerGo = Mc.Go.GoFactory.Create(MyEntityLocal, true) as ClientPlayerGo;
            }
            MgrLifeCycle.OnChangeLifeCycleFlags -= OnChangeLifeCycleFlags;
            MgrLifeCycle.OnChangeLifeCycleFlags += OnChangeLifeCycleFlags;
            
            MoveLadderStateInLadder.LadderStepUpAction -= OnLadderStepUp;
            MoveLadderStateInLadder.LadderStepUpAction += OnLadderStepUp;

            Mc.LoginStep.StepLog("MgrMyPlayer Start");
            // 玩家第一人称直接创建，不使用工程类，因为已有玩家第三人称，他们共用EntityId
            FpPlayerGo = ClientPlayerFpGo.CreateClientPlayerFpGo(MyEntityLocal, MyPlayerView);
            //FpGoProxy = new FpGoProxy();

            SocAnimationManager.InitSelfPlayer(serverEntity.EntityId);

            //FpGoProxy.Spawn(MyEntityLocal, MyPlayerView);
            PlayerBoneManager.Init();

            playerLogicParams = new PlayerLogicParams
            {
                cc = MyCharacterController,
                entity = MyEntityLocal,
                playerViewTp = MyPlayerViewGoTp.transform,
                // cpTb = Mc.Tables.TbChracterParameterTest.GetOrDefault(MyEntityLocal.CharacterType),
                DebugParams = new PlayerDebugParams(),
                ColliderTrigger = MyPlayerCc.GetComponent<ColliderTrigger>(),
                MgrEntity = Mc.Entity,
                WeaponClipHandle = MyEntityLocal,
                cmd = new UserCmd(),
                SocCC = new SocOtherCharacterController(),
            };

            //强制刷新本地武器
            if (serverEntity.CurrentWeaponId > 0)
            {
                playerLogicParams.entity.NextWeaponId = serverEntity.CurrentWeaponId;
                playerLogicParams.entity.CurrentWeaponId = serverEntity.CurrentWeaponId;
            }

            Spawn(MyEntityId, serverEntity);
            logger.Info("[BUG:3871] MgrMyPlayer Spawn!");
            //默认角色的场景fov
            MyEntityLocal.DefaultSceneFov = Mc.Tables.TbChracterParameter.DefaultSceneFov;
            MyEntityLocal.ClientTime = serverEntity.ClientTime;
            smoothRenderTime = MyEntityLocal.ClientTime;//初始化预测渲染时间
            // Debug.LogError("初始化时间 "+MyEntityLocal.ClientTime);
            float fovValue = 0;
            var fovValueConfig = Mc.SettingClient.GetConfig("sliderFov");
            if (fovValueConfig != null)
            {
                float.TryParse(fovValueConfig.CurValue, out fovValue);
            }
            if (!Mathf.Approximately(0, fovValue))
            {
                MyEntityLocal.DefaultSceneFov = fovValue;
            }

            WorldStreamingController.EnableStreaming();
            Mc.LoginStep.StepLog(ZString.Format("MgrMyPlayer::Start, serverEntity.LifeCycleFlags = {0}", serverEntity.LifeCycleFlags));
            if (Mc.ObserverMode || serverEntity.LifeCycleFlags != LifeFlags.Init) // 重新登陆时设置一下MyPlayerView的位置，否则在流式加载结束前一直是(0,0,0)
            {
                MyPlayerView.transform.position = new Vector3(serverEntity.PosX, serverEntity.PosY, serverEntity.PosZ);
                CheckStreamingLoaded();
            }

            //先让加载出相机和允许控制
            MyPlayerLoadSuccess?.Invoke();

            //UIHUD初始化
            mgrMyLights.Init();
            var pointCom = FpPlayerGo.ObjectComponent;// FpGoProxy.GetNowPointComponent();
            var selfNightLightTran = pointCom.GetChildPoint("Light_On_MyPlayer_fp");
            if (selfNightLightTran)
            {
                selfNightLight = selfNightLightTran.GetComponent<Light>();
                mgrMyLights.AddLightControl(ELightSourceType.NightLight, ChangeNightLightHandler, MonsterUtil.NotDay());
            }
            

            Mc.LoginStep.StepLog("玩家逻辑状态机初始化");
            PlayerLogicStateMachine.Instance.Init(playerLogicParams);

            Mc.Camera.OnCameraStateChangeAction -= CameraChangeState;
            Mc.Camera.OnCameraStateChangeAction += CameraChangeState;
            Mc.Camera.OnTP2FPADSTransitionFinished -= TP2FPADSTransitionFinished;
            Mc.Camera.OnTP2FPADSTransitionFinished += TP2FPADSTransitionFinished;
            //第一次初始化，需要设置一次视角
            CameraChangeState(CameraState.None, Mc.Camera.NowState);

            //绑定事件
            Bind();

            Mc.Construction.OnRecoverConstructionsAroudPos += OnRecoverAllPreCreateConstruction;
            
            InitSceneCamera();
            InitEffects();
            // 刚上线就是死的
            if (serverEntity.IsDead)
            {
                Mc.LoginStep.StepLog("玩家上线为死亡状态, 进入复活界面");
                OpenRespawnWindow(serverEntity);
            }

            ///资源初始化
            AnimWarpingSystemUtils.InitRes();

            McCommon.Tables.TbChracterParameter.RegisterDataUpdateEvent(OnDataUpdate);
            
            onMountableChanged -= OnMountChanged;
            onMountableChanged += OnMountChanged;
            OnVehicleTypeChange(MyEntityLocal, 0, 0);
            InitReputaion();

            HeldItemAPI.Init();
            //初始化预测的马
            InitPredictHorse();
            
            // 降落伞
            InitPredictParachute();

            if (MyCharacterController != null)
            {
                Vector3 pos = new Vector3(MyCharacterController.GetPosition().x, MyCharacterController.GetPosition().y + MyCharacterController.GetHeight(), MyCharacterController.GetPosition().z);
                Mc.Water.UpdateWaterStateByPos(pos);
            }
            
            playerLogicParams.UserControllerSetting = UserControllerSetting.Get();
            playerLogicParams.UserControllerSetting.InputBackBreakMantle = false;
            MyEntityServer.RemoteCallReceiveUserControllerSetting(ERpcTarget.Simulator, playerLogicParams.UserControllerSetting);

            BagHeldItemUseController = new BagHeldItemUseController(this);
            
            SecMoveReportInfo = new();
        }

        private void OnDataUpdate()
        {
            playerLogicParams.entity.Height = playerLogicParams.cpTb.CharacterStandHeight;
            playerLogicParams.entity.CcHeight = playerLogicParams.cpTb.CcHeight;
            playerLogicParams.cc.SetHeightAndCalCenter(playerLogicParams.cpTb.CcHeight);
        }

        public void ReviewStart()
        {
            MyEntityLocal.LifeCycleFlags = LifeFlags.Alive;
            var nullhand = new MeleeCustom(IdGenerator.GenLocalUniqueId());
            nullhand.TemplateId = (int)EHeldItem.EmptyHand;
            nullhand.TableId = 0;
            MyEntityLocal.NullHand = nullhand;
            MyEntityLocal.CharacterState = PlayerCharacterStateEnum.Active;
            MyEntityLocal.SwitchHeldItem(nullhand.EntityId);
            //PlayerBaseState.SetCurrentWeaponId(MyEntityLocal, nullhand.EntityId);
            MyEntityLocal.PosX = ObserverEntity.Instance.PosX;
            MyEntityLocal.PosY = ObserverEntity.Instance.PosY;
            MyEntityLocal.PosZ = ObserverEntity.Instance.PosZ;
            MyEntityLocal.RotateY = ObserverEntity.Instance.RotateY;
            
            FpPlayerGo.StartSwitchHeldItem(nullhand);
            TpPlayerGo.StartSwitchHeldItem(nullhand);
            Mc.LoginStep.StepLog("MgrMyPlayer ReviewStart");
        }

        public void DisableAutoDrive(int disableTip = 0)
        {
            if (AutoDrive)
            {
                AutoDrive = false;
                Mc.Msg.FireMsgAtOnce(EventDefine.VehicleAutoDriveChange);
                if (disableTip > 0)
                {
                    Mc.MsgTips.ShowRealtimeWeakTip(disableTip);
                }
            }
        }

        public void SetAutoDrive(bool value)
        {
            if (AutoDrive != value)
            {
                AutoDrive = value;
                Mc.Msg.FireMsgAtOnce(EventDefine.VehicleAutoDriveChange);
            }
        }

        private void OnVehicleTypeChange(CustomTypeBase customTypeBase, int _, int __)
        {
            var entity = customTypeBase as PlayerEntity;
            if (entity == null)
            {
                return;
            }
            var camera = Mc.Camera.SceneCamera;
            if (camera != null)
            {
                camera.nearClipPlane = entity.VehicleType == (int)VehicleType.MiniCopter ? 0.1f : 0.07f;
            }
            if (!Mc.ObserverMode)
            {
                return;
            }
 
            //observer 不同步座位字段 特殊处理 相机模式
            if (entity.VehicleType != 0 && entity.VehicleType != (int)VehicleType.TrainCar)
            {
                Mc.Camera.ChangeState(CameraMode.VEHICLE_TP, true);
            }
            else
            {
                Mc.Camera.ChangeState(CameraMode.VEHICLE_TP, false);
            }
        }
        
        private void OnMoveStateChange(CustomTypeBase entity, byte oldMoveStateEnum, byte newMoveStateEnum)
        {
            // if (MyEntityLocal.MoveState == PlayerMoveStateEnum.MoveMantle)
            // {
            //     if (MyCharacterController.GetEnable())
            //     {
            //         MyCharacterController.SetEnable(false);
            //     }
            // }
            //
            // if (MyEntityLocal.MoveState != PlayerMoveStateEnum.MoveMantle)
            // {
            //     if (!MyCharacterController.GetEnable())
            //     {
            //         MyCharacterController.SetEnable(true);
            //     }
            // }
        }

        private void OnHorseMountDirChange(CustomTypeBase entity, int o, int n)
        {
            // if (n > 0 && !MyEntityLocal.IsNullHand)
            // {
            //     var itemid = MyEntityLocal.CurrentWeaponId;
            //     MyEntityLocal.DrawComponent.UnEquip();
            //     Mc.TimerWheel.AddTimerOnce((int)(McCommon.Tables.TbHorsePropertyFactor.MountTime * 1000), (id, data, delete) =>
            //     {
            //         long[] arr = data as long[];
            //         if (MyEntityLocal.IsNullHand)
            //             MyEntityLocal.DrawComponent.Equip(arr[0], EItemBreakReason.UseItem);
            //     }, new long[] { itemid });
            // }
        }
        
        private void InitReputaion()
        {
            if (Mc.MyPlayer.RoleId == Mc.Config.RoleId)
            {
                Mc.Reputation.RegisterBadgeData();
                Mc.Reputation.RegisterSlotData();
            }
        }

        protected void TP2FPADSTransitionFinished()
        {
            PlayerInvisibleBitData.CreateInvisibleBitData(MyEntityLocal.EntityId, PlayerInvisibleStateEnum.TP2FPADSTransition, MyEntityLocal.FpModelInvisibleFlag, true, true, true);
        }

        protected void ChangeNightLightHandler(bool value)
        {
            selfNightLight.gameObject.SetActive(value);
        }

        public void RefreshEquipEnable()
        {
            var serverEntity = Mc.Entity.GetPlayerEntity(MyEntityId);
            if (null == mgrMyLights || null == MyEntityLocal || serverEntity == null) return;
            mgrMyLights.ChangeLightState(ELightSourceType.LightableHat, serverEntity.IsEquipEnable(SwitchableEquip.Head));
            mgrMyLights.ChangeLightState(ELightSourceType.NightVersionLight, serverEntity.IsEquipEnable(SwitchableEquip.Head));
            MyEntityLocal.EquipEnableFlags = serverEntity.EquipEnableFlags;
        }

        /// <summary>
        /// 设置服务端的装备状态，但是要先设置本地的装备状态
        /// </summary>
        /// <param name="equipIndex"></param>
        /// <param name="enable"></param>
        public void SetRemoteEquipEnable(int equipIndex, bool enable)
        {
            MyEntityLocal.SetEquipEnableFlag(equipIndex, enable);
            MyEntityServer.RemoteCallSetEquipEnable(ERpcTarget.Simulator, equipIndex, enable);
        }

        public Action OnCheckBackWeapon;

        protected void CheckUiBug(long entityId, long weaponId)
        {
            #if UNITY_EDITOR
            if (entityId != MyEntityId || weaponId == 0)
                return;
            var entity = MyEntityLocal.GetByEntityId<IHeldItemEntity>(weaponId);
            if (entity == null)
            {
                logger.InfoFormat("获取不到武器实体{0}", weaponId);
                return;
            }

            var isEmptyHand = entity is { EntityType: EntityTypeId.MeleeCustom, TableId: 0 }; // 空手
            var isBluePrint = entity is { EntityType: EntityTypeId.HeldItemCustom, TableId: 10010001 }; // 建筑图纸

            var leftAttack = UiHud?.GetElem("attackLeft") as UiHudElemLeftAttack;
            var rightAttack = UiHud?.GetElem("attack") as UiHudElemRightAttack;
            if (leftAttack != null && rightAttack != null)
            {
                var leftAttackIsShow = leftAttack.IsNodeVisible;
                var rightAttackIsShow = rightAttack.IsNodeVisible;
                if (leftAttackIsShow && !rightAttackIsShow)
                {
                    logger.ErrorFormat("left right attack show different tableID {0} IsHandleConstruction {1} isEmptyHand {2} isBluePrint {3} isInEdit {4}", entity.TableId, Mc.Construction.IsInBuildMode, isEmptyHand, isBluePrint, UiHud.IsInEdit);
                }
            }
            #endif
        }

        public void ShowFp(bool show)
        {
            // foreach (var weaponLocatorTran in TpPlayerGoProxy.WeaponLocatorTrans)
            // {
            //     weaponLocatorTran.SetActive(!show);
            // }
            ShowFpModels(show);
            HeldItemInvisibleBitData.CreateInvisibleBitData(MyEntityId, HeldItemInvisibleStateEnum.WithHost, MyEntityLocal.FpHeldItemInvisibleFlag, show, true);

            // if (show && MyEntityLocal.ActionState == ActionStateEnum.SwipeCard)
            //     return;

            //ShowFpWeapon(show);
        }

        public void ShowTp(bool show)
        {
            ShowTpModels(show);
            HeldItemInvisibleBitData.CreateInvisibleBitData(MyEntityId, HeldItemInvisibleStateEnum.WithHost, MyEntityLocal.TpHeldItemInvisibleFlag, show, false);
            // 有可能重伤的消息先来导致先隐藏第三人称武器然后再切换到第三人称视角
            // 所以这里强制判断下
            // if (show && (MyEntityLocal.IsWounded || MyEntityLocal.ActionState == ActionStateEnum.SwipeCard || CameraState == CameraState.FirstSingle))
            // {
            //     ShowTpWeapon(false);
            // }
            // else
            // {
            //     ShowTpWeapon(show);
            // }
        }

        /// <summary>
        /// 显示所有的第一人称模型(裸模、服装)
        /// </summary>
        /// <param name="show"></param>
        private void ShowFpModels(bool show)
        {
            // PlayerLoader.ShowCloth(show, MyPlayerViewGoFp.gameObject, false);
            FpPlayerGo.ShowAllEquips(show);
        }

        /// <summary>
        /// 显示第一人称的武器
        /// </summary>
        /// <param name="show"></param>
        private void ShowFpWeapon(bool show)
        {
            if (TryGetCurrentHeldItemGo(out HeldItemGo go, true))
            {
                go.UpdateVisible();
            }

            // var weapon = Mc.MyPlayer.GetCurrentWeapon();
            // if (weapon != null)
            // {
            //     if (weapon.FpWeaponGo != null)
            //     {
            //         weapon.FpWeaponGo.SetActive(show);
            //     }
            // }
        }

        /// <summary>
        /// 显示所有的第三人称模型(裸模、服装)
        /// </summary>
        /// <param name="show"></param>
        private void ShowTpModels(bool show)
        {
            TpPlayerGo.ShowAllEquips(show);
        }

        public void CopyDataFromServerEntity(PlayerEntity local, PlayerEntity serverEntity)
        {
            local.CharacterState = serverEntity.CharacterState;
            local.UnAliveState = serverEntity.UnAliveState;
            local.MoveState = serverEntity.MoveState;
            local.MoveLadderState = serverEntity.MoveLadderState;
            local.MoveJumpState = serverEntity.MoveJumpState;
            local.MoveMantleState = serverEntity.MoveMantleState;
            local.MoveSwimState = serverEntity.MoveSwimState;
            local.MoveZiplineState = serverEntity.MoveZiplineState;
            local.PoseState = serverEntity.PoseState;
            local.PoseDyingState = serverEntity.PoseDyingState;
            local.ActionState = serverEntity.ActionState;
            local.AdsState = serverEntity.AdsState;
            local.PassiveState = serverEntity.PassiveState;
            local.ReloadState = serverEntity.ReloadState;
            local.AttackSubState = serverEntity.AttackSubState;
            local.BowState = serverEntity.BowState;
            local.AdsAnimState = serverEntity.AdsAnimState;
            local.LongRoleId = serverEntity.LongRoleId;
            local.Observer = serverEntity.Observer;
            local.ObserverLoadingFinishTime = serverEntity.ObserverLoadingFinishTime;
            local.ClientAuthorityTime = serverEntity.ClientAuthorityTime;
            local.CurrentWeaponId = serverEntity.CurrentWeaponId;
            local.PreviousWeaponId = serverEntity.PreviousWeaponId;

            local.EquipHead = CloneOrCopyData(local.EquipHead, serverEntity.EquipHead);
            local.EquipEye = CloneOrCopyData(local.EquipEye, serverEntity.EquipEye);
            local.EquipJaw = CloneOrCopyData(local.EquipJaw, serverEntity.EquipJaw);
            local.EquipUpperArmor = CloneOrCopyData(local.EquipUpperArmor, serverEntity.EquipUpperArmor);
            local.EquipShirt = CloneOrCopyData(local.EquipShirt, serverEntity.EquipShirt);
            local.EquipGlove = CloneOrCopyData(local.EquipGlove, serverEntity.EquipGlove);
            local.EquipPants = CloneOrCopyData(local.EquipPants, serverEntity.EquipPants);
            local.EquipLowerArmor = CloneOrCopyData(local.EquipLowerArmor, serverEntity.EquipLowerArmor);
            local.EquipShoe = CloneOrCopyData(local.EquipShoe, serverEntity.EquipShoe);
            local.EquipBag = CloneOrCopyData(local.EquipBag, serverEntity.EquipBag);
            local.EquipVersion = serverEntity.EquipVersion;
            local.Sex = serverEntity.Sex;
            local.FaceId = serverEntity.FaceId;
            local.HairId = serverEntity.HairId;
            local.HairColorId = serverEntity.HairColorId;
        }
        /// <summary>
        /// 通过服务端第一个快照赋值本地的localentity
        /// </summary>
        protected void InitEntityModelByServerFullSpanShot(PlayerEntity serverEntity)
        {
            //PlayerBaseState.SetCurrentWeaponId(MyEntityLocal, serverEntity.CurrentWeaponId);
            MyEntityLocal.TryInitDamageableComponent(McCommon.Tables.TbChracterParameter.CombatComponent, out PlayerDamageableComponent damageable);
            damageable.PostInit();
            serverEntity.TryGetIHitable(out IHitableEntity hitable);
            damageable.Hp = hitable.Hp;
            damageable.MaxHp = hitable.MaxHp;
            
            LocalHeldItemEntityController = new LocalHeldItemEntityController(serverEntity, MyEntityLocal);
            BreatheViewMotionController.Init();
            FpHeldItemBreatheMotionController.Init();
            FpHeldItemMoveMotionController.Init();
            
            CopyDataFromServerEntity(MyEntityLocal, serverEntity);
            //MyEntityLocal.UpdateCurrentWeapon();
            serverEntity.OpenId = Mc.Config.openId;
#if !MSDK
            serverEntity.OpenId = Mc.Config.roleId;
#endif

            if (!MyEntityServer.DebugEnable)
            {
                ToggleFly(false);
                _ccOverrideSpeed = -1;
            }
            MyEntityLocal.FrameworkInit();
            MyEntityLocal.PostInit();
        }

        /// <summary>
        /// 角色出生
        /// </summary>
        /// <param name="entityId">实体Id</param>
        public void Spawn(long entityId, PlayerEntity serverEntity)
        {
            MyEntityLocal.SetId(serverEntity.EntityId);
            //var spawnPos = SpawnPosition();
            var spawnPos = new Vector3(serverEntity.PosX, serverEntity.PosY, serverEntity.PosZ);
            MyEntityLocal.PosX = spawnPos.x;
            MyEntityLocal.PosY = spawnPos.y;
            MyEntityLocal.PosZ = spawnPos.z;

            UnityUtility.ClientSetCcPosition(MyCharacterController, spawnPos, MyEntityLocal);
            MyPlayerCc.layer = MgrConfigPhysicsLayer.LayerPlayerCapsule;
            MyPlayerCc.tag = "Player";

            SceneSetUpObj = GameObject.Find("Scene Setup");
            //寻找场景光源
            if (SceneSetUpObj != null)
            {
                SceneLight = SceneSetUpObj.GetComponentInChildren<Light>();
            }
        }

        private Vector3 SpawnPosition()
        {
            if (McCommonUnity.Scene.GameSpaceSceneId == 4) //SOC_BuildMap_GPUDriven
                return new Vector3(846f, 109f, 407f);
            if (McCommonUnity.Scene.GameSpaceSceneId == 5) //gas_station_GZH
                return new Vector3(-13.376f, 11.39f, -4.3f);

            var obj = GameObject.Find("SpawnPoint");
            return obj != null ? obj.transform.position : Vector3.zero;
        }

        /// <summary>
        /// 帧开始清理目前放到 UpdateMyPlayer 之前调用
        /// </summary>
        // public virtual void BeforeUpdateMyPlayer(int interval)
        // {
        //     PlayerBoneManager.BeforeClearDirectionLocator();
        // }

        public void OnTeleport()
        {
            Mc.Predict.MyEntityTeleportSeq = UserCmdSequence;// nowApplyCmdSequence + 1;
            Mc.Camera.LateUpdate(0, FpPlayerGo.FpCameraLocator);
            fxUnderWater?.Update(true);
        }

        public void ReviewUpdateMyPlayer(int interval)
        {
            if (FpPlayerGo == null)
            {
                return;
            }
            UpdateTpRotation();
            UpdateFpRotation();
            CalculateSmoothPlayerView(interval);
            FpPlayerGo?.Update();

            if (SceneSetUpObj != null)
            {
                var pos = MyPlayerView.transform.position;
                pos.y = Mathf.Max(pos.y, 0);
                SceneSetUpObj.transform.position = pos;
            }
        }

        /// <summary>
        /// 更新旋转信息
        /// </summary>
        public virtual void UpdateMyPlayer(int interval)
        {
            //时序可能有问题
            if (FpPlayerGo == null)
            {
                return;
            }

            if (TaskBattleUtil.TaskForceMove)
            {
                TaskBattleUtil.UpdatePlayerGoFollowTarget();
            }

            // if (Input.GetKeyDown(KeyCode.F7))
            // {
            //     FpPlayerGo.HeldItemGoController.TestLowMemory();
            // }
            
            ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_fxUnderWater);
            fxUnderWater?.Update();
            ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_fxUnderWater);
            ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_mgrMyLights);
            mgrMyLights.Update();
            ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_mgrMyLights);
            FpHeldItemBreatheMotionController.UpdateBreathe(Time.deltaTime);
            ADSUpdate(Time.deltaTime);
            ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_UpdateFpRotation);
            UpdateFpRotation();
            ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_UpdateFpRotation);

            //角色平滑移动
            // MyPlayerView.transform.position = Vector3.Lerp(MyPlayerView.transform.position,
            //     MyPlayerColliderTransform.position, speed * interval / 1000f);
            if (!TaskBattleUtil.TaskForceMove)
            {
                ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_CalculateSmoothPlayerView);
                CalculateSmoothPlayerView(interval);
                ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_CalculateSmoothPlayerView);
            }
            else
            {//#45783  【新手引导】接到击杀熊的任务后，到需要点击装备枪的时候退杀进程重进，任务动画重新播放点击装备枪后人的视角不对
                GetSmoothPlayerViewPos(out var Pos, out var offset);
                FpPlayerGo.SetLocalPosition(offset);
            }
            ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_FpPlayerGo);
            FpPlayerGo?.Update();
            ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_FpPlayerGo);

            //MyPlayerViewGoFp.transform.localPosition = new Vector3(0, MyEntityLocal.Height, 0);

            if (SceneSetUpObj != null)
            {
                var pos = MyPlayerView.transform.position;
                pos.y = Mathf.Max(pos.y, 0);
                SceneSetUpObj.transform.position = pos;
            }
            if (!Mc.Control.ControlEnabled || Mc.UserCmd.NowCmd == null)
                return;

            // ui相关
            ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_UpdateUi);
            UpdateUi();
            ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_UpdateUi);
            ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_UpdateLittleEye);
            UpdateLittleEye(Time.deltaTime);
            ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_UpdateLittleEye);

            //小眼睛使用时人物显隐
            ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_UpdateShader);
            UpdateLittleEyeVisible();
            ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_UpdateShader);
            ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_UpdateCmd);
            UpdateCmd();
            ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_UpdateCmd);

            ThrowParabolaController.Update();
            PlayerBoneManager.ResetDirectionLocator();

            #region death anim debug

            // var myPos = MyPlayerColliderTransform.position;
            // myPos.y += 1f;
            // var myForward = MyPlayerViewGoTp.transform.forward;
            // var angleOffset = 30f; // todo : 配表
            // var vec1 = Quaternion.AngleAxis(45 + angleOffset, Vector3.up) * myForward;
            // var vec2 = Quaternion.AngleAxis(135 + angleOffset, Vector3.up) * myForward;
            // var vec3 = Quaternion.AngleAxis(-45 + angleOffset, Vector3.up) * myForward;
            // var vec4 = Quaternion.AngleAxis(-135 + angleOffset, Vector3.up) * myForward;
            // var vec5 = Quaternion.AngleAxis(angleOffset, Vector3.up) * myForward;
            // DebugDraw.DrawRay(myPos, vec1 * 10f, Color.red, 1f);
            // DebugDraw.DrawRay(myPos, vec2 * 10f, Color.red, 1f);
            // DebugDraw.DrawRay(myPos, vec3 * 10f, Color.red, 1f);
            // DebugDraw.DrawRay(myPos, vec4 * 10f, Color.red, 1f);
            // DebugDraw.DrawRay(myPos, vec5 * 10f, Color.magenta, 1f);
            // DebugDraw.DrawRay(myPos, myForward * 10f, Color.green, 1f);

            #endregion
            
            SecMoveReportInfo?.Tick();
        }

        public void ComparePredictGo()
        {
            foreach (var go in Mc.Go.PredictGos.Values)
            {
                if (go is ClientThrownEntityGo thrownEntityGo)
                {
                    thrownEntityGo.ComparePredictResult();
                }
            }
        }

        public virtual void UpdatePredictGo(int interval)
        {
            foreach (var go in Mc.Go.PredictGos.Values)
            {
                if (go is ClientThrownEntityGo thrownEntityGo)
                {
                    thrownEntityGo.UpdateSmoothPosition(interval);
                }
            }
        }

        private bool isInWater = false;
        /// <summary>
        /// 在水里爬梯不能播出水的声音，所以声音的切换不能以状态为基准了，只能以当前在不在水里为基准
        /// </summary>
        private void UpdateCmd()
        {
            ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_UpdateCmd_IsInDeepWater);
            var pos = MyPlayerCc.transform.position;
            var isInDeepWater = PlayerBaseState.IsInDeepWater(MyEntityLocal, McCommon.Tables.TbChracterParameter.SwimWaterHeight)
                                && PlayerBaseState.CanDive(MyEntityLocal);
            ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_UpdateCmd_IsInDeepWater);
            if (!isInWater && isInDeepWater)
            {
                Mc.Audio.SetAudioState("WaterSurface", "Underwater");
                Mc.Audio.AddAudio("Water_Transition_In", MyPlayerCc, pos, -1, Mc.MyPlayer.MyEntityId);
                isInWater = true;
            }

            if (isInWater && !isInDeepWater)
            {
                Mc.Audio.SetAudioState("WaterSurface", "Normal");
                Mc.Audio.StopAudio("Water_Transition_In", MyPlayerCc);
                if (MyEntityLocal.CharacterState == PlayerCharacterStateEnum.Active)
                {
                    Mc.Audio.AddAudio("Water_Transition_Out", MyPlayerCc, pos, -1, Mc.MyPlayer.MyEntityId);
                }

                isInWater = false;
            }
        }

        // private bool ShowVehicleInDeepWaterTip = false;

        public virtual void UpdateVehicleCameraDebug()
        {
            UiDebugInfo.UpdateVehicleCameraDebug();
        }


        /// <summary>
        /// 使用小眼睛时人物显隐
        /// </summary>
        private void UpdateLittleEyeVisible()
        {
            //只有第一人称才有小眼睛显隐
            if (Mc.Camera.NowState == CameraState.FirstSingle && !Mc.Camera.GetMyPlayerDead() && !Mc.Photo.IsPhotoEnabled)
            {
                var tbCp = McCommon.Tables.TbChracterParameter;
                var pitch = Mathf.Abs(Mc.Camera.Controller.littleEyePitch);
                var yaw = Mathf.Abs(Mc.Camera.Controller.littleEyeYaw);
                bool isYawExceed =  yaw > tbCp.HideYawAngleMax;
                bool isPitchExceed =  pitch > tbCp.HidePitchAngleMax;
                var isExceed = isYawExceed || isPitchExceed;
                SetRenderEnabled(!isExceed);

            }
        }

        //通过Renderer的enabled来控制人物显隐,不使用接口是因为接口会隐藏光源
        //todo 需要统一管理手持物在各种情况下的什么部分的显隐，需要提需求，目前没有策划负责这个，暂时搁置等B2下,这里做临时处理
        private void SetRenderEnabled(bool enabled)
        {
            if (FpPlayerGo.PartModelController != null)
            {
                FpPlayerGo.PartModelController.SetCurrentRenderersEnable(enabled);
                // var iter = FpPlayerGo.PartModelController.ItorCurrentRenderers();
                // foreach (var renderer in iter)
                // {
                //     renderer.enabled = enabled; 
                // }
            }
            //B2下FP也改成接口控制
            if (enabled)
            {
                //PlayerInvisibleBitData.CreateInvisibleBitData(FpPlayerGo.EntityId, PlayerInvisibleStateEnum.LittleEye, FpPlayerGo.PlayerEntity.FpModelInvisibleFlag,true, true, true, true); 
                PlayerInvisibleBitData.CreateInvisibleBitData(TpPlayerGo.EntityId, PlayerInvisibleStateEnum.LittleEye, TpPlayerGo.PlayerEntity.TpModelInvisibleFlagForFp,true, false, true, true); 
                PlayerInvisibleBitData.CreateInvisibleBitData(TpPlayerGo.EntityId, PlayerInvisibleStateEnum.LittleEye, TpPlayerGo.PlayerEntity.TpModelInvisibleFlagForTp,true, false, false, true); 

            }
            else
            {
                //PlayerInvisibleBitData.CreateInvisibleBitData(FpPlayerGo.EntityId, PlayerInvisibleStateEnum.LittleEye, FpPlayerGo.PlayerEntity.FpModelInvisibleFlag,false, true, true, true); 
                PlayerInvisibleBitData.CreateInvisibleBitData(TpPlayerGo.EntityId, PlayerInvisibleStateEnum.LittleEye, TpPlayerGo.PlayerEntity.TpModelInvisibleFlagForFp,false, false, true, true); 
                PlayerInvisibleBitData.CreateInvisibleBitData(TpPlayerGo.EntityId, PlayerInvisibleStateEnum.LittleEye, TpPlayerGo.PlayerEntity.TpModelInvisibleFlagForTp,false, false, false, true); 
            }
            //fp手持物
            if (TryGetCurrentHeldItemGo(out BasePlayerFpHeldItemGo fpHeldItemGo, true))
            {
                fpHeldItemGo.SetRendererVisible(enabled);
            }
        }

        private void UpdateTpRotation()
        {
            if (MyPlayerCc == null)
            {
                return;
            }
            
            //角色朝向
            if (MyEntityLocal.MoveState != PlayerMoveStateEnum.MoveMantle || MyEntityLocal.CommonStateRecovery == (int)ECommonStateRecoveryEnum.Mantle)
            {
                if (MyEntityLocal.MoveState == PlayerMoveStateEnum.MoveLadder)
                {
                    //是否在blending阶段
                    bool isBlendLadder = MyEntityLocal.MoveLadderState == PlayerMoveLadderStateEnum.BleedInLadder || 
                                         MyEntityLocal.MoveLadderState == PlayerMoveLadderStateEnum.BleedOutLadder;
                    //cc永远朝向梯子
                    MyPlayerColliderTransform.localEulerAngles = new Vector3(0, MyEntityLocal.RotateY, 0);

                    if (!isBlendLadder)
                    {
                        //爬梯的其他模式，分视角
                        if (CameraState == CameraState.FirstSingle)
                        {
                            //跟着角色旋转
                            MyPlayerViewGoTp.transform.rotation =
                                Quaternion.Euler(new Vector3(0, Mc.UserCmd.NowCmd.Yaw, 0));
                        }
                        else if (CameraState == CameraState.Third)
                        {
                            //朝向梯子
                            MyPlayerViewGoTp.transform.rotation = MyPlayerColliderTransform.rotation;
                        } 
                    }
                    else
                    {
                        if (MyEntityLocal.MoveLadderState == PlayerMoveLadderStateEnum.BleedInLadder)
                        {
                            var config = PlayerLoader.LadderConfig;
                            int intAnimMoveTime = 0;
                            bool yawEnable = false;
                            
                            PlayerLadderAdsorbRule adsorbRule;
                            if (config != null &&
                                config.LadderPreliminaryRules.TryGetValue((LadderTestDir)MyEntityLocal.LadderEnterDir, out adsorbRule))
                            {
                                PlayerLogicState.CalculateLadderTime(MyEntityLocal, adsorbRule, out intAnimMoveTime, out var viewTime,out var speed);
                                yawEnable = adsorbRule.AnimYawEnable;
                            }
                            
                            var curTime = MyEntityLocal.RootMotionTime;

                            if (curTime >= intAnimMoveTime  && yawEnable)
                            {
                                if (CameraState == CameraState.Third)
                                {
                                    //朝向梯子
                                    MyPlayerViewGoTp.transform.rotation = MyPlayerColliderTransform.rotation;
                                } 
                            }
                        }
                    }
                }
                else if (MyEntityLocal.UnAliveState is PlayerUnAliveStateEnum.ToDie) // 死亡动画需要根据实际受击角度，旋转角色方向
                {
                    TpPlayerGo.DeathAnimRotate(MyEntityLocal, MyPlayerViewGoTp.transform);
                    // Debug.LogError(MyPlayerViewGoTp.transform.rotation.eulerAngles.y);
                }
                else if (MyEntityLocal.PoseState == PlayerPoseStateEnum.Zipline)
                {
                    MyPlayerColliderTransform.localEulerAngles = new Vector3(0, MyEntityLocal.RotateY, 0);
                    MyPlayerViewGoTp.transform.rotation = MyPlayerColliderTransform.rotation;
                }
                else if (MyEntityLocal.PoseState == PlayerPoseStateEnum.Drive)
                {
                    /* todo 载具的旋转和座位的旋转可能是不一样的，这么写不严谨*/
                    var mountableGo = playerLogicParams.mountable;
                    if (mountableGo != null && mountableGo.ApplyMountRotation())
                    {
                        var rotation = mountableGo.GetMountRotation(MyEntityLocal.MountSeatIndex);
                        PlayerBaseState.SetRotateY(MyEntityLocal, rotation.eulerAngles.y);
                    }

                    MyPlayerColliderTransform.localEulerAngles = new Vector3(0, MyEntityLocal.RotateY, 0);
                    MyPlayerViewGoTp.transform.rotation = MyPlayerColliderTransform.rotation;
                }
                else
                {
                    MyPlayerColliderTransform.eulerAngles = new Vector3(0, Mc.UserCmd.NowCmd.Yaw, 0);
                    //MyPlayerViewGoTp.transform.rotation = MyPlayerColliderTransform.rotation;
                    MyPlayerViewGoTp.transform.eulerAngles =
                        new Vector3(0, MyEntityLocal.TpAniPlayerLocalData.AnimRotation,0);
                }
            }

            if (MyEntityLocal.IsOnMount)
            {
                var mountableGo = playerLogicParams.mountable;
                if (mountableGo != null && mountableGo.ApplyMountRotation())
                {
                    var rot = mountableGo.GetMountRotation(MyEntityLocal.MountSeatIndex);
                    MyPlayerViewGoTp.transform.rotation = rot;
                }
            }
        }

        public void BlendLadderRot(float dt)
        {
            bool pitchEnable = false;
            int intAnimMoveTime =0;
            int intAnimYawTime = 0;
            bool yawEnable = false;
        
            var config = PlayerLoader.LadderConfig;
        
            PlayerLadderAdsorbRule adsorbRule;
            if (config != null &&
                config.LadderPreliminaryRules.TryGetValue((LadderTestDir)MyEntityLocal.LadderEnterDir,
                    out adsorbRule))
            {
                pitchEnable = adsorbRule.AnimPitchEnable;
                yawEnable = adsorbRule.AnimYawEnable;
                
                PlayerLogicState.CalculateLadderTime(MyEntityLocal, adsorbRule, out intAnimMoveTime, out intAnimYawTime,out var speed);
            }
        
            var curTime = MyEntityLocal.RootMotionTime;
        
            //检查是否满足视角调整阶段
            //这里不完全平滑，lerp是不靠谱但是够用，足够简单
            float percent = 1;
        
            if (curTime >= intAnimMoveTime )
            {
                if (curTime < intAnimMoveTime + intAnimYawTime)
                {
                    CurLadderBlend += dt *1000;
                    CurLadderBlend = Mathf.Min(CurLadderBlend, (curTime - intAnimMoveTime));
                    percent = Mathf.Clamp01(CurLadderBlend / intAnimYawTime);
                }
                else
                {
                    percent = 1;
                    CurLadderBlend = 0;
                }
                
                //

                if (pitchEnable && yawEnable)
                {
                    var q1 = Quaternion.Euler(MyEntityLocal.LadderStartPitch, MyEntityLocal.LadderStartYaw, 0);
                    var q2 = Quaternion.Euler(MyEntityLocal.LadderTargetPitch, MyEntityLocal.LadderTargetYaw, 0);
                    var final = Quaternion.Slerp(q1, q2, percent).eulerAngles;
                    
                    Mc.UserCmd.NowCmd.Pitch  = Mc.Control.RotationControl.Pitch = final.x;
                    var finalYaw = final.y;
                    if (finalYaw > 180)
                    {
                        finalYaw = finalYaw - 360;
                    }

                    Mc.UserCmd.NowCmd.Yaw =Mc.Control.RotationControl.Yaw = finalYaw;
                    if (CombatConfig.LadderDbg)
                    {
                        logger.InfoFormat(
                            "<ladder> LadderPitchYaw:{0}/{1}  start:{2}/{3} target:{4}/{5} percent:{6} rmtime:{7} frame:{8}", Mc.Control.RotationControl.Yaw, Mc.Control.RotationControl.Pitch,
                            MyEntityLocal.LadderStartYaw, MyEntityLocal.LadderStartPitch,MyEntityLocal.LadderTargetYaw, MyEntityLocal.LadderTargetPitch, percent,MyEntityLocal.RootMotionTime, Time.frameCount);
                    }
                }
                else
                {
                    if (pitchEnable)
                    {
                        var q1 = Quaternion.Euler(MyEntityLocal.LadderStartPitch, 0, 0);
                        var q2 = Quaternion.Euler(MyEntityLocal.LadderTargetPitch, 0, 0);
                        var finalPitch = Quaternion.Slerp(q1, q2, percent).eulerAngles.x;
                        Mc.UserCmd.NowCmd.Pitch  = Mc.Control.RotationControl.Pitch =  finalPitch;

                        if (CombatConfig.LadderDbg)
                        {
                            logger.InfoFormat(
                                "<ladder> LadderPitch: {0} start:{1} target:{2} percent:{3} rmtime:{4} frame:{5}",
                                finalPitch,
                                MyEntityLocal.LadderStartPitch, MyEntityLocal.LadderTargetPitch, percent,
                                MyEntityLocal.RootMotionTime, Time.frameCount);
                        }
                    }
                    else if (yawEnable)
                    {
                        var q1 = Quaternion.Euler(0, MyEntityLocal.LadderStartYaw, 0);
                        var q2 = Quaternion.Euler(0, MyEntityLocal.LadderTargetYaw, 0);
                        var finalYaw = Quaternion.Slerp(q1, q2, percent).eulerAngles.y;
                        if (finalYaw > 180)
                        {
                            finalYaw = finalYaw - 360;
                        }

                        Mc.UserCmd.NowCmd.Yaw = Mc.Control.RotationControl.Yaw = finalYaw;

                        if (CombatConfig.LadderDbg)
                        {
                            logger.InfoFormat(
                                "<ladder> LadderYaw: {0} start:{1} target:{2} percent:{3} rmtime:{4} frame:{5}",
                                finalYaw,
                                MyEntityLocal.LadderStartYaw, MyEntityLocal.LadderTargetYaw, percent,
                                MyEntityLocal.RootMotionTime, Time.frameCount);
                        }
                    }
                }

            }
            else
            {
                CurLadderBlend = 0;
            }

        }

        private void UpdateFpRotation()
        {
            // /*
            //  * todo 需要整理代码
            //  */
            // var punchPitch = 0f;
            // var punchYaw = 0f;
            // var rotationCtrl = Mc.Control.RotationControl;
            // var yaw = rotationCtrl.Yaw + rotationCtrl.AimOffset.y;
            // var pitch = rotationCtrl.Pitch + rotationCtrl.AimOffset.x;
            // if (Mc.Camera.NowState == CameraState.FirstSingle)
            // {
            //     if (Mathf.Approximately(MyEntityLocal.PunchPitch, 0f))
            //     {
            //         punchPitch = 0;
            //     }
            //     else
            //     {
            //         punchPitch = MyEntityLocal.PunchPitch;
            //     }
            //
            //     if (Mathf.Approximately(MyEntityLocal.PunchYaw, 0f))
            //     {
            //         punchYaw = 0;
            //     }
            //     else
            //     {
            //         punchYaw = MyEntityLocal.PunchYaw;
            //     }
            // }
            //
            // var finalPitch = pitch + punchPitch;
            // var finalYaw = yaw + punchYaw;

            var finalPitch = Mc.UserCmd.NowCmd.ViewPitch;
            var finalYaw =  Mc.UserCmd.NowCmd.ViewYaw;

            var limitPitch = 90f;
            limitPitch = McCommonUnity.Tables.TbChracterParameter.PitchLimitAngle;

            finalPitch = LimitPitch(finalPitch, limitPitch);

            // finalYaw = LimitPitch(finalYaw);
            var roll = MyEntityLocal.RotateZ;
            if (MyEntityLocal.MoveLadderState == PlayerMoveLadderStateEnum.BleedInLadder)
            {
                FpPlayerGo.SetLocalEulerAngles(new Vector3(-finalPitch, MyEntityLocal.RotateY, 0));
            }
            else if (MyEntityLocal.PoseState == PlayerPoseStateEnum.Swim || MyEntityLocal.PoseState == PlayerPoseStateEnum.Dive)
            {
                if (MyEntityLocal.CurrentWeaponTableId == 0) // 空手不转pitch
                {
                    //完整版：当角色处于水面（游泳），且相机向上仰角大于一定值时，双手停止跟随相机Pitch运动
                    if(MyEntityLocal.PoseState == PlayerPoseStateEnum.Swim )
                    {
                        if (finalPitch > McCommon.Tables.TbGlobalConfig.FpSwimPitchLimit)
                        {
                            FpPlayerGo.SetLocalEulerAngles(new Vector3(0, finalYaw, 0));
                        }
                        else
                        {
                            FpPlayerGo.SetLocalEulerAngles(new Vector3(-finalPitch, finalYaw, 0));
                        }
                    }
                    else if(MyEntityLocal.PoseState == PlayerPoseStateEnum.Dive )
                    {
                        FpPlayerGo.SetLocalEulerAngles(new Vector3(-finalPitch, finalYaw, 0));
                    }
                    else
                    {
                        //临时沿用之前的逻辑
                        FpPlayerGo.SetLocalEulerAngles(new Vector3(0, finalYaw, 0));
                        logger.ErrorFormat("not support fp rotation update PoseState = {0} ",MyEntityLocal.PoseState);
                    }
                    // logger.ErrorFormat("finalPitch = {0} ",finalPitch);
                }
                else
                {
                    FpPlayerGo.SetLocalEulerAngles(new Vector3(-finalPitch, finalYaw, 0));
                }
            }
            else
            {
                FpPlayerGo.SetLocalEulerAngles(new Vector3(-finalPitch, finalYaw, 0));
            }

            if (MyEntityLocal.IsOnMount)
            {
                var mountableGo = playerLogicParams.mountable;
                if (mountableGo != null && mountableGo.ApplyMountRotation() && IsDriverView(mountableGo as BaseMountableGo)) // 司机视角fp跟着载具转
                {
                    var rot = mountableGo.GetMountRotation(MyEntityLocal.MountSeatIndex);
                    FpPlayerGo.SetRotation(rot);
                }
                //Debug.LogFormat("CalculateSmoothPlayerView position = {0}, frame = {1}", smoothPos.ToString("F4"), Time.frameCount);
            }

        }

        private float LimitPitch(float pitch, float limitPitch = 90)
        {
            if (pitch < -limitPitch)
            {
                pitch = -limitPitch;
            }
            else if (pitch > limitPitch)
            {
                pitch = limitPitch;
            }

            return pitch;
        }

        private void OnLadderStepUp(bool stepSuc)
        {
            var LadderConfig = PlayerLoader.LadderConfig;
            if (!stepSuc)
            {
                //ui有cd，这里不做额外的cd处理了
                Mc.MsgTips.ShowRealtimeWeakTip( LadderConfig.LadderCantMoveTipId);
            }
        }
        
        protected void OnChangeLifeCycleFlags(PlayerEntity serverEntity, int newValue, int oldValue)
        {
            if (serverEntity.EntityId != Mc.MyPlayer.MyEntityId)
                return;

            logger.InfoFormat("LifeFlagChange myplayer: new:{0}, old:{1}", newValue, oldValue);

            var isAidingOther = serverEntity.IsAidingOther;
            var IsWounded = ByteUtil.HasFlag(newValue, LifeFlags.Wounded);
            var IsDead = ByteUtil.HasFlag(newValue, LifeFlags.Died);
            var IsAlive = ByteUtil.HasFlag(newValue, LifeFlags.Alive);
            var IsReborning = ByteUtil.HasFlag(newValue, LifeFlags.Reborning);

            // oldValue为Invalid则表示是强制触发的一次change事件，此时各种状态就需要完全看当前值，否则需要对比新旧值
            if (oldValue != LifeCycleFlagEvent.InvalidLifeCycleFlags)
            {
                IsWounded &= !ByteUtil.HasFlag(oldValue, LifeFlags.Wounded);
                IsDead &= !ByteUtil.HasFlag(oldValue, LifeFlags.Died);
                IsAlive &= !ByteUtil.HasFlag(oldValue, LifeFlags.Alive);
                IsReborning &= !ByteUtil.HasFlag(oldValue, LifeFlags.Reborning);
            }

            if (IsWounded && !IsDead && !isAidingOther)
            {
                Mc.Msg.FireMsgAtOnce(EventDefine.CloseAllMutexDyingWindow);
                OpenDyingWindow(serverEntity);
            }

            if (IsDead)
            {
                OpenRespawnWindow(serverEntity);
            }
            else if (IsAlive/* || IsReborning*/)
            {
                if (IsDeadWindowActive)
                {
                    logger.InfoFormat("OnChangeLifeCycleFlags: RemoveRespawnWindow");
                    // 关闭复活界面
                    RemoveRespawnWindow();
                    Mc.Msg.FireMsg(EventDefine.TryGuideRespawn);
                }
            }
        }

        private void UpdateUi()
        {
            var playerEntities = Mc.Entity.GetEntitiesViaType<PlayerEntity>();
            playerEntities.TryGetValue(Mc.MyPlayer.MyEntityId, out var serverEntity);
            if (serverEntity != null)
            {
                if (serverEntity.IsAidingOther)
                {
                    OpenDyingWindow(serverEntity, true);
                }

                if (!serverEntity.IsDead)
                {
                    var rate = (serverEntity.TryGetIHitable(out IHitableEntity hitbale) ? hitbale.Hp : 0) / 100.0f;
                    var poison = (int)serverEntity.RadiationPoison;
                    var radiation = !(poison <= 0 || !Mc.System.Context.DebugParameter.RadiationDisplayOpen);
                    if (rate <= 0.5f || radiation)
                    {
                        OpenDamageScreenEffectWindow(serverEntity);
                    }
                }

                if (MyEntityLocal.Observer != isServerEntityObserver)
                {
                    isServerEntityObserver = MyEntityLocal.Observer;
                }
            }
        }

        private void GetSmoothPlayerViewPosNew(out Vector3 pos, out Vector3 heightOffset)
        {
            if (Mc.HasObserverTarget)
            {
                var observer = Mc.Entity.GetPlayerEntity(ObserverEntity.Instance.PossessionPlayerId);
                pos = new Vector3(observer.PosX_Smooth, observer.PosY_Smooth, observer.PosZ_Smooth);
                heightOffset = new Vector3(0, observer.Height, 0);
                return;
            }
#if UNITY_EDITOR && TEST_MY_SERVER_SMOOTH//本地测试代码 位置直接使用server数据，测试网络波动平滑用
            pos = new Vector3(MyEntityServer.PosX_Smooth, MyEntityServer.PosY_Smooth, MyEntityServer.PosZ_Smooth);
            heightOffset = new Vector3(0, MyEntityServer.Height, 0);
            return;
#endif
            pos = new Vector3(MyEntityLocal.PosX, MyEntityLocal.PosY, MyEntityLocal.PosZ);
            //heightOffset = new Vector3(MyEntityLocal.PosX, MyEntityLocal.Height, MyEntityLocal.PosZ);
            long nowcmd = UserCmdSequence;
            long lastcmd = Math.Max(0, nowcmd - 1);
            long nowtime = MyEntityLocal.ClientTime;
            long lasttime = MyEntityLocal.ClientTime;
            smoothNowModel.CopyFrom(MyEntityLocal);//准备最新快照数据
            if (Mc.Predict != null && Mc.Predict.MyEntityHistory.TryGetValue(lastcmd, out var lastModel))
            {
                lasttime = lastModel.ClientTime;
            }
            else
            {
                lastModel = smoothNowModel;//没有旧快照，直接指向新的
            }

            if (smoothRenderTime < lasttime)
            {
                smoothRenderTime = lasttime;
            }
            if (smoothRenderTime > nowtime)
            {
                smoothRenderTime = nowtime;
            }
            float factor = 1;
            if (nowtime > lasttime && nowtime - lasttime < 300)//时间差小于300ms 否则不插值
            {
                factor = Mathf.Clamp01((smoothRenderTime - lasttime) * 1.0f / (nowtime - lasttime));
            }
            var lastPlatform = lastModel.PlatformId;
            var nowPlatform = smoothNowModel.PlatformId;
            if (playerLogicParams.PlatformId > 0 && nowPlatform == playerLogicParams.PlatformId && lastPlatform == nowPlatform && playerLogicParams.cc.GetTransform().parent != MyPlayerTransform)
            {
                //在平台上 位置插值用局部坐标系
                var lastPlatformPosX = lastModel.PlatformPosX;
                var lastPlatformPosY = lastModel.PlatformPosY;
                var lastPlatformPosZ = lastModel.PlatformPosZ;
                var nowPlatformPosX = smoothNowModel.PlatformPosX;
                var nowPlatformPosY = smoothNowModel.PlatformPosY;
                var nowPlatformPosZ = smoothNowModel.PlatformPosZ;
                Vector3 localPos;
                localPos.x = (1 - factor) * lastPlatformPosX + nowPlatformPosX * factor;
                localPos.y = (1 - factor) * lastPlatformPosY + nowPlatformPosY * factor;
                localPos.z = (1 - factor) * lastPlatformPosZ + nowPlatformPosZ * factor;
                pos = playerLogicParams.cc.GetTransform().parent.TransformPoint(localPos);
            }
            else
            {
                var nowPosX = smoothNowModel.PosX;
                var nowPosY = smoothNowModel.PosY;
                var nowPosZ = smoothNowModel.PosZ;
                if (Mc.Predict != null && Mc.Predict.MyEntityTeleportSeq == nowcmd)
                {
                    pos.x = nowPosX;
                    pos.y = nowPosY;
                    pos.z = nowPosZ;
                }
                // Player切换平台的那一帧，直接使用server的PlatformPos
                else if (playerLogicParams.PlatformId > 0 && nowPlatform == playerLogicParams.PlatformId)
                {
                    var nowPlatformPosX = smoothNowModel.PlatformPosX;
                    var nowPlatformPosY = smoothNowModel.PlatformPosY;
                    var nowPlatformPosZ = smoothNowModel.PlatformPosZ;
                    var nowPlatformPos = new Vector3(nowPlatformPosX, nowPlatformPosY, nowPlatformPosZ);
                    pos = playerLogicParams.cc.GetTransform().parent.TransformPoint(nowPlatformPos);
                }
                else
                {
                    var lastPosX = lastModel.PosX;
                    var lastPosY = lastModel.PosY;
                    var lastPosZ = lastModel.PosZ;

                    var linearLerpX = (1 - factor) * lastPosX + nowPosX * factor;
                    var linearLerpY = (1 - factor) * lastPosY + nowPosY * factor;
                    var linearLerpZ = (1 - factor) * lastPosZ + nowPosZ * factor;
                    pos.x = linearLerpX;
                    pos.y = linearLerpY;
                    pos.z = linearLerpZ;
                }
            }

            var lastHeight = lastModel.Height;
            var nowHeight = smoothNowModel.Height;
            var linearLerpHeight = (1 - factor) * lastHeight + nowHeight * factor;
            heightOffset.x = 0;
            heightOffset.y = linearLerpHeight;
            heightOffset.z = 0;
            //logger.Error($"GetSmoothPlayerViewPosNew nowtime:{nowtime},lasttime:{lasttime},nowcmd:{nowcmd},lastcmd:{lastcmd},pos:{pos},smoothRenderTime:{smoothRenderTime},frame:{Time.frameCount}");

        }

        private void GetSmoothPlayerViewPos(out Vector3 pos, out Vector3 heightOffset)
        {
            GetSmoothPlayerViewPosNew(out pos, out heightOffset);
            return;
#if false
            pos = default;
            heightOffset = default;

            float factor = 1;
            if (nowCmdServerTime != lastCmdServerTime)
            {
                //刚登录，会因为回滚导致nowCmdServerTime == lastCmdServerTime
                factor = Mathf.Clamp01((smoothRenderTime - lastCmdServerTime) * 1.0f / (nowCmdServerTime - lastCmdServerTime));
            }

            var lastModelBool = Mc.Predict.MyEntityHistory.TryGetValue(lastApplyCmdSequence, out var lastModel);
            var nowModelBool = Mc.Predict.MyEntityHistory.TryGetValue(nowApplyCmdSequence, out var nowModel);
            if (lastModelBool && nowModelBool)
            {
                var lastPlatform = lastModel.PlatformId;
                var nowPlatform = nowModel.PlatformId;
                if (playerLogicParams.PlatformId > 0 && nowPlatform == playerLogicParams.PlatformId && lastPlatform == nowPlatform && playerLogicParams.cc.GetTransform().parent != MyPlayerTransform)
                {
                    //在平台上 位置插值用局部坐标系
                    var lastPlatformPosX = lastModel.PlatformPosX;
                    var lastPlatformPosY = lastModel.PlatformPosY;
                    var lastPlatformPosZ = lastModel.PlatformPosZ;
                    var nowPlatformPosX = nowModel.PlatformPosX;
                    var nowPlatformPosY = nowModel.PlatformPosY;
                    var nowPlatformPosZ = nowModel.PlatformPosZ;
                    Vector3 localPos;
                    localPos.x = (1 - factor) * lastPlatformPosX + nowPlatformPosX * factor;
                    localPos.y = (1 - factor) * lastPlatformPosY + nowPlatformPosY * factor;
                    localPos.z = (1 - factor) * lastPlatformPosZ + nowPlatformPosZ * factor;
                    pos = playerLogicParams.cc.GetTransform().parent.TransformPoint(localPos);
                }
                else
                {
                    var nowPosX = nowModel.PosX;
                    var nowPosY = nowModel.PosY;
                    var nowPosZ = nowModel.PosZ;
                    if (Mc.Predict.MyEntityTeleportSeq == nowApplyCmdSequence)
                    {
                        pos.x = nowPosX;
                        pos.y = nowPosY;
                        pos.z = nowPosZ;
                    }
                    else
                    {
                        var lastPosX = lastModel.PosX;
                        var lastPosY = lastModel.PosY;
                        var lastPosZ = lastModel.PosZ;

                        var linearLerpX = (1 - factor) * lastPosX + nowPosX * factor;
                        var linearLerpY = (1 - factor) * lastPosY + nowPosY * factor;
                        var linearLerpZ = (1 - factor) * lastPosZ + nowPosZ * factor;
                        pos.x = linearLerpX;
                        pos.y = linearLerpY;
                        pos.z = linearLerpZ;
                    }
                }

                var lastHeight = lastModel.Height;
                var nowHeight = nowModel.Height;
                var linearLerpHeight = (1 - factor) * lastHeight + nowHeight * factor;
                heightOffset.x = 0;
                heightOffset.y = linearLerpHeight;
                heightOffset.z = 0;
            }
            else if (nowModelBool)
            {
                var nowPosX = nowModel.PosX;
                var nowPosY = nowModel.PosY;
                var nowPosZ = nowModel.PosZ;
                var nowHeight = nowModel.Height;
                pos.x = nowPosX;
                pos.y = nowPosY;
                pos.z = nowPosZ;
                heightOffset.x = 0;
                heightOffset.y = nowHeight;
                heightOffset.z = 0;
            }
            else if (lastModelBool)
            {
                var lastPosX = lastModel.PosX;
                var lastPosY = lastModel.PosY;
                var lastPosZ = lastModel.PosZ;
                var lastHeight = lastModel.Height;
                pos.x = lastPosX;
                pos.y = lastPosY;
                pos.z = lastPosZ;
                heightOffset.x = 0;
                heightOffset.y = lastHeight;
                heightOffset.z = 0;
            }
#endif
        }

        /// <summary>
        /// 计算主角view的位置
        /// </summary>
        private void CalculateSmoothPlayerView(int intervalTime)
        {
            //UserCmdSequence和lastCmd的Sequence是同步的
            //var lastCmd = Mc.UserCmd.LastCmd;
            //第一个输入还没有，也不可能有位移，直接跳出
            //if (lastCmd == null)
                //return;
            // if (nowApplyCmdSequence < 0)
            // {
            //     //说明还没应用过cmd的数值，直接瞬移到目标点
            //     if (Mc.Predict.MyEntityHistory.TryGetValue(UserCmdSequence, out var entityModel))
            //     {
            //         var posX = entityModel.PosX;
            //         var posY = entityModel.PosY;
            //         var posZ = entityModel.PosZ;
            //         MyPlayerView.transform.position = new Vector3(posX, posY, posZ);
            //         FpPlayerGo.SetLocalPosition(new Vector3(0, entityModel.Height, 0));
            //     }
            //
            //     nowApplyCmdSequence = lastCmd.Sequence;
            //     nowCmdServerTime = lastCmd.ClientTime;
            // }
            // else
            {
                //有了最新的cmd，进行切换
                // if (nowApplyCmdSequence != lastCmd.Sequence)
                // {
                //     //开始平滑，last和now的cmd都有了，开始计时
                //     if (lastApplyCmdSequence < 0)
                //     {
                //         smoothRenderTime = nowCmdServerTime;
                //     }
                //
                //     lastApplyCmdSequence = nowApplyCmdSequence;
                //     lastCmdServerTime = nowCmdServerTime;
                //     nowApplyCmdSequence = lastCmd.Sequence;
                //     nowCmdServerTime = lastCmd.ClientTime;
                // }
                //
                // if (lastApplyCmdSequence < 0)
                //     return;
                // smoothRenderTime += intervalTime;
                // if (smoothRenderTime < lastCmdServerTime)
                // {
                //     //赶不上最新的区间了，直接切换到上一个cmd的位置
                //     smoothRenderTime = lastCmdServerTime;
                // }
                // else if (smoothRenderTime > nowCmdServerTime)
                // {
                //     //赶不上最新的区间了，直接切换到上一个cmd的位置
                //     smoothRenderTime = nowCmdServerTime;
                // }

                if (MyEntityLocal.IsOnMount)
                {
                    //载具上，需要在late里
                    if (playerLogicParams != null&& (MyEntityLocal.MoveState== PlayerMoveStateEnum.Mountable || MyEntityLocal.MoveState== PlayerMoveStateEnum.MoveZipline))
                    {
                        GetSmoothPlayerViewPos(out var Pos, out var offset);
                        var smoothPos = Vector3.zero;
                        var mountableGo = playerLogicParams.mountable;
                        if (Mc.HasObserverTarget)
                        {
                            mountableGo = Mc.Go.GetGo(MyEntityLocal.MountableId) as BaseMountableGo;
                        }
                        if (mountableGo != null)
                        {
                            smoothPos = mountableGo.GetMountPos(MyEntityLocal.MountSeatIndex);
                            //System.Numerics.Vector3 mountoffset = mountableGo.GetMountPosFpOffset(MyEntityLocal.MountSeatIndex).ToVector3();
                            var eular = mountableGo.GetMountRotation(MyEntityLocal.MountSeatIndex).eulerAngles;
                            offset = MyEntityLocal.GetEye2PlayerInWorld(eular.x*Mathf.Deg2Rad, eular.y*Mathf.Deg2Rad, eular.z*Mathf.Deg2Rad, offset.ToVector3()).ToVector3();
                            MyPlayerView.transform.position = smoothPos;

                            // var info = mountableGo.GetMountPointInfo(MyEntityLocal.MountSeatIndex);
                            // var mountablemat = mountableGo.MainTransform.localToWorldMatrix;
                            // var moutablerot = mountableGo.MainTransform.rotation;
                            // smoothPos = mountablemat.MultiplyPoint(info.LogicSeatLocalPos);
                            // var eular1 = (moutablerot * info.LogicSeatLocalRot).eulerAngles;//mountableGo.GetMountRotation(MyEntityLocal.MountSeatIndex).eulerAngles;
                            // offset = MyEntityLocal.GetEye2PlayerInWorld(eular1.x*Mathf.Deg2Rad, eular1.y*Mathf.Deg2Rad, eular1.z*Mathf.Deg2Rad, offset.ToVector3()).ToVector3();
                            // MyPlayerView.transform.position = smoothPos;
                        }

                        FpPlayerGo.SetLocalPosition(offset);

                        if (mountableGo != null && mountableGo.ApplyMountRotation())
                        {
                            var seatrot = mountableGo.GetMountRotation(MyEntityLocal.MountSeatIndex);
                            MyPlayerViewGoTp.transform.rotation = seatrot;
                            if (IsDriverView(mountableGo as BaseMountableGo))
                            {
                                if (mountableGo.OnlyApplyYaw())
                                {
                                    seatrot = Quaternion.Euler(0, seatrot.eulerAngles.y, 0);
                                }
                                FpPlayerGo.SetRotation(seatrot);
                            }
                        }

                    }
                }
                else
                {
                    GetSmoothPlayerViewPos(out var Pos, out var offset);
                    MyPlayerView.transform.position = Pos;
                    FpPlayerGo.SetLocalPosition(offset);
                }

                if (Mc.Camera.NowState == CameraState.FirstSingle)
                {
                    if (!MyEntityLocal.IsOnMount)
                    {
                        //todo 可以尝试根据fov来动态修改偏移距离
                        /* float value = McCommon.Tables.TbChracterParameter.TpBackDisMinFov +
                                       (Camera.main.fieldOfView - 50f) * (McCommon.Tables.TbChracterParameter.TpBackDisMaxFov - McCommon.Tables.TbChracterParameter.TpBackDisMinFov) / 40f;*/
                        var dis = McCommon.Tables.TbChracterParameter.TpBackDisDefault;
                        MyPlayerViewGoTp.transform.localPosition = TpPlayerGo.CalcRelativePos(dis, MyPlayerViewGoTp.transform, Vector3.back);
                    }
                    else
                    {
                        MyPlayerViewGoTp.transform.localPosition = Vector3.zero;
                    }
                }
                else
                {
                    MyPlayerViewGoTp.transform.localPosition = Vector3.zero;
                }

                TpPlayerGo.TpRelativeFunctionMgr.OnUpdatePosition(TpPlayerGo);

                Vector3 pos = MyPlayerView.transform.position;
                Mc.MyPlayer.MyEntityLocal.PosX_Smooth = pos.x;
                Mc.MyPlayer.MyEntityLocal.PosY_Smooth = pos.y;
                Mc.MyPlayer.MyEntityLocal.PosZ_Smooth = pos.z;
            }
        }


        public void LateUpdate(Camera cam)
        {
            ProfilerApi.BeginSample(EProfileFunc.Player_ADS);
            // //载具上，需要在late里
            // if (MyEntityLocal.IsOnMount && playerLogicParams != null)
            // {
            //     GetSmoothPlayerViewPos(out var Pos, out var offset);
            //     var smoothPos = Vector3.zero;
            //     var mountableGo = playerLogicParams.mountable;
            //     if (mountableGo != null)
            //     {
            //         smoothPos = mountableGo.GetMountPos(MyEntityLocal.MountSeatIndex);
            //         //System.Numerics.Vector3 mountoffset = mountableGo.GetMountPosFpOffset(MyEntityLocal.MountSeatIndex).ToVector3();
            //         var eular = mountableGo.GetMountRotation(MyEntityLocal.MountSeatIndex).eulerAngles;
            //         offset = MyEntityLocal.GetEye2PlayerInWorld(eular.x*Mathf.Deg2Rad, eular.y*Mathf.Deg2Rad, eular.z*Mathf.Deg2Rad, offset.ToVector3()).ToVector3();
            //         MyPlayerView.transform.position = smoothPos;
            //     }
            //
            //     FpPlayerGo.SetLocalPosition(offset);
            //
            //     if (mountableGo != null && mountableGo.ApplyMountRotation())
            //     {
            //         var rot = mountableGo.GetMountRotation(MyEntityLocal.MountSeatIndex);
            //         MyPlayerViewGoTp.transform.rotation = rot;
            //         if (IsDriverView(mountableGo as BaseMountableGo))
            //         {
            //             FpPlayerGo.SetRotation(rot);
            //         }
            //     }
            //
            // }
            ADSLateUpdate(Time.deltaTime);
            PlayerBoneManager.LateUpdateDirectionLocator();

            FpPlayerGo?.LateUpdate(Time.deltaTime);
            //TryUpdateCrossSwitch();

            ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_UpdateTpRotation);
            UpdateTpRotation();
            ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_UpdateTpRotation);
            
            ProfilerApi.EndSample(EProfileFunc.Player_ADS);
        }

        private long ccParentEntityId;

        public void OnEntityGoRemoved(IEntityGo go)
        {
            if (go.EntityId == ccParentEntityId)
            {
                MyCharacterController.GetTransform().SetParent(MyPlayer.transform, true);
                ccParentEntityId = 0;
                // playerLogicParams.PlatformId = 0;
            }
        }

        public void CheckPlatform(PlayerEntity serverEntity, bool predict = true)
        {
            if (serverEntity == null || playerLogicParams == null)
            {
                return;
            }

            Transform parent = null;
            if (serverEntity.PlatformId == 0)
            {
                parent = MyPlayer.transform;
                ccParentEntityId = 0;
                playerLogicParams.PlatformId = 0;
                MyEntityLocal.PlatformId = 0;
            }
            else
            {
                var platform = Mc.Go.GetGo(serverEntity.PlatformId);
                parent = platform?.MainTransform;
                if (platform != null && playerLogicParams.cc.GetTransform().parent != parent)
                {
                    playerLogicParams.PlatformId = serverEntity.PlatformId;
                    MyEntityLocal.PlatformId = serverEntity.PlatformId;
                    playerLogicParams.PlatformEntity = platform.Entity as IPlatformEntity;
                    playerLogicParams.ChangePlatform = true;
                    ccParentEntityId = serverEntity.PlatformId;
                    parent = platform.MainTransform;
                    if (platform is IPlatformGo platformGo)
                    {
                        parent = platformGo.GetPlaneTransform();
                    }
                }
            }

            if (parent != null && playerLogicParams.cc.GetTransform().parent != parent)
            {
                playerLogicParams.cc.GetTransform().SetParent(parent, true);
            }

            playerLogicParams.lastGroundCollider = null;
            
            if (serverEntity.PlatformId > 0)
            {
                if (!predict)
                {
                    playerLogicParams.cc.GetTransform().localPosition = new Vector3(serverEntity.PlatformPosX,
                        serverEntity.PlatformPosY, serverEntity.PlatformPosZ);
                }

                MyEntityLocal.PlatformPosX = serverEntity.PlatformPosX;
                MyEntityLocal.PlatformPosY = serverEntity.PlatformPosY;
                MyEntityLocal.PlatformPosZ = serverEntity.PlatformPosZ;
            }
            else
            {
                MyEntityLocal.PlatformPosX = 0;
                MyEntityLocal.PlatformPosY = 0;
                MyEntityLocal.PlatformPosZ = 0;
            }
        }

        /// <summary>
        /// 回滚
        /// </summary>
        /// <param name="serverEntity">服务端对象</param>
        public void Rollback(PlayerEntity serverEntity, long cmdSequence)
        {
            #if !PUBLISH
            if (!CombatConfig.EnableRollBack)
            {
                return;
            }
            #endif
            if (!PlayHelper.IsNewbie)
            {
                var serverPos = new Vector3(serverEntity.PosX, serverEntity.PosY, serverEntity.PosZ);
                //因为有可能跳跃可能已经落地了，但是回滚是在空中，导致isGround回滚之后产生了落地点的问题，不能强行回滚到目标位置，会被缓存的isground为true吸附到地面
                MyPlayerColliderTransform.eulerAngles = new Vector3(0, serverEntity.RotateY, 0);
                if (serverEntity.PlatformId == 0 && serverEntity.MountableId == 0)
                {//不在平台 不在载具 才设置
                    UnityUtility.ClientSetCcPosition(MyCharacterController, serverPos, MyEntityLocal);
                }
                MyCharacterController.SyncPose();
                MyPlayerView.transform.position = MyPlayerColliderTransform.position;
                logger.InfoFormat("playerstate RollbackCmd cmdSequence :{0}, pos:x:{1},y:{2},z:{3}", cmdSequence, serverEntity.PosX.ToString("f6"), serverEntity.PosY.ToString("f6"), serverEntity.PosZ.ToString("f6"));
            }

            // if (Mathf.Abs(serverEntity.PosX) <= 1 && Mathf.Abs(serverEntity.PosY) <= 1 && Mathf.Abs(serverEntity.PosZ) <= 1 && cmdSequence > 0)
            // {
            //     Debug.LogError($"[Fall System], entityId:{serverEntity.EntityId}, 客户端回滚位置异常, pos:{serverEntity.PosX},{serverEntity.PosY},{serverEntity.PosZ}, sequence:" +
            //                    $"{cmdSequence}");
            // }

            //playerLogicParams.NowHistoryWeaponId = localWeaponId;
            //MyEntityLocal.CurrentWeaponId = serverEntity.CurrentWeaponId;
            PlayerLogicStateMachine.Instance.Rollback(playerLogicParams, serverEntity);
        }

        public void ReviewMove(UserCmd cmd)
        {
            if (!preloadFinish)
                return;
            playerLogicParams.cmd.Copy(cmd);
            MoveStateObserver.Instance.MovePlayer(playerLogicParams);
        }

        /// <summary>
        /// 本地预判移动
        /// </summary>
        public virtual void PredictMove(UserCmd cmd)
        {
            if (!preloadFinish)
                return;

            //序列号检查
            if (cmd == null || cmd.Sequence <= UserCmdSequence)
                return;
            
            if (!Mc.Control.ControlEnabled || TaskBattleUtil.TaskForceMove)
            {
                cmd.MoveForward = false;
                cmd.MoveBackward = false;
                cmd.MoveLeft = false;
                cmd.MoveRight = false;
                cmd.MoveForwardContinuous = false;
                cmd.MoveBackwardContinuous = false;
                cmd.MoveLeftContinuous = false;
                cmd.MoveRightContinuous = false;
                cmd.Jump = false;
                cmd.JumpContinus = false;
                cmd.Run = false;
                cmd.RunContinus = false;
                cmd.Sprint = false;
                cmd.SprintContinus = false;
                if (!Mc.Control.ControlEnabled) cmd.Idle = true;
            }

            CmdPreserver.PreProcessCmdRecord(MyEntityLocal, cmd,playerLogicParams.cmd);
            //执行移动
            Move(cmd, playerLogicParams.cmd);
            CmdPreserver.PostProcessCmdRecord(MyEntityLocal);

            
            
            //回滚的时候，对于武器的回滚
            //回滚的时候，只会回滚那一帧的强制修改，中间有操作cmdswitch的时候，currentWeaponId被修改,但是事件不触发，这里触发
            // if (MyEntityLocal.CurrentWeaponId != 0 && EquipWeaponId != MyEntityLocal.CurrentWeaponId)
            // {
            //     if (EquipWeaponId != 0)
            //         PlayerLogicStateMachine.Event.Emit(StateEventType.UnEquipWeapon, playerLogicParams.entity.EntityId, EquipWeaponId);
            //     PlayerBaseState.SetCurrentWeaponId(playerLogicParams.entity, MyEntityLocal.CurrentWeaponId);
            //     PlayerLogicStateMachine.Event.Emit(StateEventType.EquipWeapon,
            //         playerLogicParams.entity.EntityId, MyEntityLocal.CurrentWeaponId);
            // }
            //Move里面会执行状态机update，回滚也会执行Move 所以放到move只有没有 没有和simulator一样紧跟 PlayerLogicStateMachine.Instance.update
            PlayerLogicStateMachine.Instance.CheckEvent(playerLogicParams);
        }

        public void RollbackCmd(UserCmd cmd, int clientTime)
        {
            playerLogicParams.cmd.Copy(cmd);
            playerLogicParams.serverTime = clientTime;
        }


        /// <summary>
        /// 本地移动
        /// </summary>
        /// <param name="cmd">角色操作命令</param>
        public void Move(UserCmd cmd, UserCmd lastCmd, bool isRollBack = false)
        {
            playerLogicParams.lastCmd.Copy(lastCmd);
            playerLogicParams.cmd.Copy(cmd);
            playerLogicParams._AnyActionKeyPressed = cmd.AnyActionKeyPress();
            playerLogicParams.entity.ClientTime += cmd.Interval; //客户端时间
            cmd.ClientTime = playerLogicParams.entity.ClientTime;

            if (cmd.Fire1 && !cmd.Fire1Continus) { 
                Mc.MyPlayer.CheckDryFire();
            }

            if (playerLogicParams.entity.MoveState == PlayerMoveStateEnum.Sprint && (cmd.Fire1 || cmd.Fire1Continus) && 
                playerLogicParams.HeldItemController.CanFire(playerLogicParams) &&
                ActionStateFire.Instance.CanStateEnter(playerLogicParams.entity))
            {
                playerLogicParams.entity.ShootingRecoverySprintDelay = (int)(playerLogicParams.cpTb.ShootingRecoverySprintDelay * 1000);
            }
            if (cmd.Sprint && playerLogicParams.entity.ShootingRecoverySprintDelay > 0)
            {
                cmd.Sprint = false;
                playerLogicParams.cmd.Sprint = false;
            }

            if (playerLogicParams.entity.ShootingRecoverySprintDelay > 0)
                playerLogicParams.entity.ShootingRecoverySprintDelay -= cmd.Interval;

#if REPLAY_LOG
            var server_entity = Mc.Entity.GetEntity(Mc.MyPlayer.MyEntityLocal.EntityId) as PlayerEntity;
            logger.ErrorFormat("Move clienttime 步进 {0} interval {1} server_entity {2}", MyEntityLocal.ClientTime, cmd.Interval, server_entity.ClientTime);
#endif
            playerLogicParams.LogicRenderTime = Mc.Time.RenderWorldTime;

            playerLogicParams.serverTime = playerLogicParams.entity.ClientTime;
            playerLogicParams.IsRollBack = isRollBack;
            if (MyEntityLocal.MountableId > 0)
            {
                playerLogicParams.mountable = Mc.Go.GetGo(MyEntityLocal.MountableId) as BaseMountableGo;
            }
            else
            {
                playerLogicParams.mountable = null;
            }

            playerLogicParams.DebugParams.CCOverrideSpeed = CCOverrideSpeed;

            //cmd意图处理，意图只处理冲突情况下是否进入，真正进入还是要到逻辑状态机中
            playerLogicParams.CmdIntention.ProcessCmd(MyEntityLocal, playerLogicParams);

                ProfilerApi.BeginSample(EProfileFunc.OnFixedFps30Update_StateMachine_BeforeExecute);
                PlayerLogicJobModel.ClearCmdStateList();
                PlayerLogicStateMachine.Instance.BeforeCmdFill(playerLogicParams);
                playerLogicParams.FillStateUpdateJobData(0);
                PlayerLogicJobModel.CollectPlayerCurrentStates(playerLogicParams);
                PlayerLogicStateMachine.Instance.BeforeUpdate(playerLogicParams);
                ProfilerApi.EndSample(EProfileFunc.OnFixedFps30Update_StateMachine_BeforeExecute);
                PlayerLogicStateMachine.Instance.ThreadUpdatePlayerLogicState(1);
                ProfilerApi.BeginSample(EProfileFunc.OnFixedFps30Update_StateMachine_AfterExecute);
                PlayerLogicStateMachine.Instance.AfterUpdate(playerLogicParams);
                PlayerLogicJobModel.ClearLogicStateList();
                ProfilerApi.EndSample(EProfileFunc.OnFixedFps30Update_StateMachine_AfterExecute);
#if UNITY_EDITOR
            // 模拟无视物理
            cheatingTool.PlayerMoveNoPhysics(playerLogicParams, cmd);
            
            // 无限跳跃（飞空）
            cheatingTool.PlayerMoveJumpNoLimit(playerLogicParams, cmd);
#endif
            
            //保存操作序列号
            UserCmdSequence = cmd.Sequence;

            playerLogicParams.entity.IsRollBack = isRollBack;
        }
        
        private void HandlePlayers2SetLayer(Context context)
        {
            if (context == null)
            {
                return;
            }
            if (context.Players2SetLayer.Contains(Mc.MyPlayer.MyEntityId))
            {
                Mc.MyPlayer?.TpPlayerGo?.SocCharacterController?.GetOutVehicle();
            }
            context.Players2SetLayer.Clear();
        }
        
        /// <summary>
        /// 检查因为游戏功能执行的自动逻辑
        /// </summary>
        /// <param name="cmd"></param>
        public virtual void PreCmdChange(UserCmd cmd)
        {
            HandlePlayers2SetLayer(Mc.System.Context);
            
            if (MyEntityLocal.IsAutoFire)
            {
                cmd.Fire1 = true;
            }

            MyEntityLocal.IsAutoFire = false;

            if (MyEntityLocal.PoseState == PlayerPoseStateEnum.Dying ||
                MyEntityLocal.UnAliveState == PlayerUnAliveStateEnum.Dead ||
                MyEntityLocal.UnAliveState == PlayerUnAliveStateEnum.ToDie)
            {
                openFly = false;
            }

            cmd.FlyMaxSpeed = MoveStateFly.DebugMaxSpeedOverride;

            cmd.Observer = MyEntityLocal.Observer;
            if (cmd.Observer)
            {
                cmd.Fly = false;
            }
            else
            {
                //不是观察者模式才考虑是不是飞天模式
                cmd.Fly = openFly;
            }

            // if (Mc.Control.activedActionIsClear)
            // {
            //     Mc.UserCmd.NowCmd.Yaw = Mc.Control.RotationControl.Yaw;
            //     Mc.UserCmd.NowCmd.Pitch = Mc.Control.RotationControl.Pitch;
            // }
            
            cmd.ActivePressMantle = true;
            if (!MyEntityLocal.UseMantleBtn)
            {
                //有攀爬按钮，只判断cmd.mantle
                //没有攀爬按钮，区分是主动还是被动
                var hasMove = cmd.HasMove && !cmd.Turn;
                if (cmd.Jump)
                {
                    if(hasMove)
                        cmd.Mantle = true;
                }
                else
                {
                    //被动攀爬：空中+输入+朝向
                    //有输入移动，朝向在逻辑状态机统一判断
                    var inAir = MyEntityLocal.MoveState == PlayerMoveStateEnum.Fall ||
                                MyEntityLocal.MoveState == PlayerMoveStateEnum.Jump;
                    if (inAir && hasMove)
                    {
                        cmd.ActivePressMantle = false;
                        cmd.Mantle = true;
                    }
                }
            }

            //自动换弹逻辑，打空弹夹时背包有子弹时自动换弹，背包没子弹捡起子弹时自动换弹
            if (Mc.MyPlayer.TryGetCurrentHeldItemInterface(out IHaveBulletEntity entity))
            {
                var gunBase = Mc.Tables.TbGunBase.GetOrDefault(entity.TableId);
                if (gunBase != null && gunBase.AutoReload && entity.EmptyClip)
                {
                    CheckReloadTrigger(entity, cmd, entity.UsingAmmoId);
                }
            }

            //弓类的特殊逻辑。希望客户端在可以换弹时去触发换弹（主要因为背包信息客户端是滞后的，无法在逻辑状态机与服务器逻辑保持一致）
            //将弓箭逻辑状态机中的空仓换弹判断放到这里
            //if (this.TryGetCurrentWeapon(out var cw))
            if (this.MyEntityLocal.TryGetCurrentHeldItem(out IHeldItemEntity helditem))
            {
                if (HeldItemSystemUtils.IsBow(helditem.TableId))
                {
                    WeaponCustom weapon = helditem as WeaponCustom;
                    if (MyEntityLocal.BowState == PlayerBowStateEnum.Fire 
                        || MyEntityLocal.BowState == PlayerBowStateEnum.ChargeFire
                        || MyEntityLocal.BowState == PlayerBowStateEnum.NonArrow_Idle)
                    {
                        //NonArrow_Idle下尝试给弓类武器换弹
                        if (weapon.Clips == 0)
                        {
                            if (!weapon.IsReloading)
                            {
                                CheckReloadTrigger(weapon, cmd, weapon.UsingAmmoId);
                            }
                        }
                    }
#if STANDALONE_REAL_PC
                    if (MyEntityLocal.BowState == PlayerBowStateEnum.Fire && cmd.Fire2)
                    {
                        cmd.FireCancel = true;
                    }       
#endif
                }
                // 手持毒刺导弹时，只有 1.非长按进行瞄准 或者 2.当前玩家未锁定 时攻击会自动转为瞄准
                else if (helditem is WeaponCustom weapon
                         && TargetingLauncherData.IsTargetingLauncher(weapon)
                         && MyEntityLocal.AdsState == PlayerAdsStateEnum.AdsOff
                         && !MyEntityLocal.IsLocked
                         && !Mc.UserCmd.SetupAdsPressed
                         && !cmd.Reload
                         && cmd.Fire1)
                {
                    cmd.Fire2 = true;
                    cmd.Fire1 = false;
                }


                var medicalBase = McCommon.Tables.TbMedicalBase.GetOrDefault(helditem.TableId);
                if (medicalBase != null)
                {
                    //是治疗道具
                    if (cmd.Fire1)
                    {
                        if (helditem.TableId == 23060006)
                        {
                            InteractionUtil.TryInteract(PlayerInteractiveId.Bandage);
                        }
                        else
                        {
                            InteractionUtil.TryInteract(PlayerInteractiveId.Syringe);
                        }
                    }

                    if (cmd.Fire2 && medicalBase.CanUseForOther)
                    {
                        if (CheckUtil.TryGetSyringeTarget(this.MyEntityLocal, cmd, (UnityContext)Mc.System.Context,
                                out long targetid))
                        {
                            InteractionUtil.TryInteract(PlayerInteractiveId.SyringeOther, targetid);
                        }
                    }
                }

                // 当前手持小木锤，则屏蔽cmd.Fire1
                if (HeldItemSystemUtils.IsWoodenHammer(helditem.TableId) && cmd.Fire1)
                {
                    cmd.Fire1 = false;
                }

#if STANDALONE_REAL_PC
                if (helditem is ThrowWeaponCustom throwWeaponCustom)
                {
                    //新增右键按压状态时输入左键点击取消投掷动作：
                    bool canThrowStart = MyEntityLocal.ThrowState >= PlayerThrowState.ThrowFarStart ||
                                         MyEntityLocal.ThrowState <= PlayerThrowState.ThrowReady;
                    if (canThrowStart && !cmd.FireCancel)
                    {
                        if (!throwWeaponCustom.FarThrow && cmd.Fire1)
                        {
                            cmd.FireCancel = true;
                        }
                    }
                    //PC当前为鼠标左键近抛投掷、鼠标右键远抛投掷；需要更改为鼠标左键远抛投掷，鼠标右键近抛投掷
                    if (cmd.Fire1)
                    {
                        cmd.Fire2 = true;
                        cmd.Fire1 = false;
                        ActionStateThrow.IsFarThrow = true;
                    }
                    else if (cmd.Fire2)
                    {
                        cmd.Fire1 = true;
                        cmd.Fire2 = false;
                        ActionStateThrow.IsFarThrow = false;
                    }
                }
#elif UNITY_EDITOR
                if (helditem is ThrowWeaponCustom)
                {
                    if (ActionStateThrow.IsFarThrow && cmd.Fire1)
                    {
                        cmd.Fire2 = true;
                        cmd.Fire1 = false;
                    }
                    else if (!ActionStateThrow.IsFarThrow && cmd.Fire2)
                    {
                        cmd.Fire1 = true;
                        cmd.Fire2 = false;
                    }
                }
#endif
            }
            this.BagHeldItemUseController?.CmdTick(cmd);

            //尝试自动潜水
            if (playerLogicParams.entity.PoseState == PlayerPoseStateEnum.Swim ||
                playerLogicParams.entity.PoseState == PlayerPoseStateEnum.Dive)
            {
                PlayerBaseState.RunAutoDive(playerLogicParams, cmd);
            }

            if (playerLogicParams.entity.MoveLadderState == PlayerMoveLadderStateEnum.InLadder)
            {
                SetSprintInLadder(cmd);

                if (cmd.Sprint)
                {
                    if (InteractiveIdListChecker.LadderState != ELadderState.SPEED_UP)
                    {
                        Mc.Msg.FireMsgAtOnce<ELadderState>(EventDefine.LadderStateChange, ELadderState.SPEED_UP);
                    }
                }
                else
                {
                    if (InteractiveIdListChecker.LadderState != ELadderState.CLIMPING)
                    {
                        Mc.Msg.FireMsgAtOnce<ELadderState>(EventDefine.LadderStateChange, ELadderState.CLIMPING);
                    }
                }
            }
            else
            {
                playerLogicParams.entity.ClientWantLadderSprint = false;
            }

            if (playerLogicParams.entity.ClientWantLadder)
            {
                playerLogicParams.entity.ClientWantLadder = false;
                cmd.Ladder = true;
            }

            
            //校验 梯子数据
            if (playerLogicParams.entity.ClientLadderTargetId > 0)
            {
                var go = Mc.Go.GetGo(playerLogicParams.entity.ClientLadderTargetId) as ILadderGo;
                if (go == null)
                {
                    playerLogicParams.entity.ClientLadderTargetId = -1;
                    playerLogicParams.entity.ClientAdsorbTargetIndex = -1;
                    
                    if (InteractiveIdListChecker.IsLaddderStateCan())
                        Mc.Msg.FireMsgAtOnce<ELadderState>(EventDefine.LadderStateChange, ELadderState.CAN_NOT);
                }
                else
                {
                    if (go == null || go.adsorbCollideConfigs == null ||
                        playerLogicParams.entity.ClientAdsorbTargetIndex >= go.adsorbCollideConfigs.Length)
                    {
                        playerLogicParams.entity.ClientLadderTargetId = -1;
                        playerLogicParams.entity.ClientAdsorbTargetIndex = -1;
                        
                        if (InteractiveIdListChecker.IsLaddderStateCan())
                            Mc.Msg.FireMsgAtOnce<ELadderState>(EventDefine.LadderStateChange, ELadderState.CAN_NOT);
                    }
                }
            }
            
            {//接水区域判断
                var interactiveTb = McCommon.Tables.TbPlayerInteractiveState.GetOrDefault(Mc.MyPlayer.MyEntityLocal.InteractiveId);
                bool collecting = interactiveTb != null && interactiveTb.InteractiveType == PlayerInteractiveType.CollectWater;
                if (collecting && Mc.MyPlayer.MyEntityLocal.InteractiveTargetId <= 0 && Mc.Water.playerInWaterState == MgrWater.EPlayerInWaterState.Ground)
                {
                    InteractionUtil.StopInteract();
                    // InteractionUtil.TryCollectWater(false);
                }
            }

            
            //自动切空手
            var player = playerLogicParams.entity;
            player?.DrawComponent?.SwitchCollection(cmd);

            //之前代码如此
            //ClientUseBagItemSystem.BagUseTick(MyEntityLocal);

            // ValidateSkin(MyEntityLocal, cmd);


            //拍照模式下不能开镜
            if (Mc.Photo != null && Mc.Photo.IsPhotoEnabled)
            {
                cmd.Fire2 = false;
            }
            
#if STANDALONE_REAL_PC
            cmd.CmdPc = true;
#else
           cmd.CmdPc = false;
#endif
        }

        /// <summary>
        /// 检查因为游戏功能执行的自动逻辑
        /// </summary>
        /// <param name="cmd"></param>
        public virtual void AfterCmdChange(UserCmd cmd)
        {
            if (cmd == null || playerLogicParams == null || playerLogicParams.cmd == null)
                return;
            //playerLogicParams.cmd.RootMotionData.MoveLadderTopState = playerLogicParams.entity.MoveLadderTopState;
            playerLogicParams.cmd.RootMotionData.DeepCopy(playerLogicParams.RootMotionDataCache);
            cmd.RootMotionData.DeepCopy(playerLogicParams.cmd.RootMotionData);
            var pos = playerLogicParams.cc.GetPosition();
            cmd.PosX = pos.x;//playerLogicParams.entity.PosX;
            cmd.PosY = pos.y;
            cmd.PosZ = pos.z;
            cmd.CollisionFlags = playerLogicParams.entity.CollisionFlags;
            if (!IsMountDriver)
            {
                cmd.IsGrounded = playerLogicParams.entity.IsGround || playerLogicParams.entity.InCampingTent;   
            }
            cmd.NormalX = playerLogicParams.entity.GroundNormalX;
            cmd.NormalY = playerLogicParams.entity.GroundNormalY;
            cmd.NormalZ = playerLogicParams.entity.GroundNormalZ;
            cmd.ClientAuthorityTimeAck = playerLogicParams.entity.ClientAuthorityTime;
            // cmd.LogicState = playerLogicParams.entity.LogicState;
            cmd.PoseState = (byte)playerLogicParams.entity.PoseState;
            cmd.MoveState = (byte)playerLogicParams.entity.MoveState;
            cmd.MoveJumpState = (byte)playerLogicParams.entity.MoveJumpState;
            cmd.MoveLadderState = (byte)playerLogicParams.entity.MoveLadderState;
            cmd.MoveSwimState = (byte)playerLogicParams.entity.MoveSwimState;
            cmd.MoveMantleState = (byte)playerLogicParams.entity.MoveMantleState;
            cmd.AdsState = (byte)playerLogicParams.entity.AdsState;

            // cmd.SwipeCardIndex = playerLogicParams.cmd.SwipeCardIndex;
            cmd.EyePosition = playerLogicParams.cmd.EyePosition;
            var resaonkey = PlayerBaseState.CanUseByItemArea(playerLogicParams.entity);
            
            if (resaonkey.Item1 != EItemDisableReason.None && resaonkey.Item2)
            {
                PlayerBaseState.ExecuteDisableRule(playerLogicParams.entity,ItemDisableRule.EnterRaise);
            }
            
            //移动之后，对进行爬梯检测
            bool notInLadder = playerLogicParams.entity.MoveState != PlayerMoveStateEnum.MoveLadder;
            bool inCd = playerLogicParams.entity.LadderNextEnterTime != 0 &&
                        playerLogicParams.entity.LadderNextEnterTime > playerLogicParams.entity.ClientTime;
            bool hasInput = cmd.HasMove || cmd.Jump;
            bool posChange = !Mathf.Approximately(playerLogicParams.entity.ClientLadderTestPos.X,pos.x) || 
                             !Mathf.Approximately(playerLogicParams.entity.ClientLadderTestPos.Y,pos.y) ||
                             !Mathf.Approximately(playerLogicParams.entity.ClientLadderTestPos.Z,pos.z);
            
            playerLogicParams.entity.ClientLadderTestPos.X = pos.x;
            playerLogicParams.entity.ClientLadderTestPos.Y = pos.y;
            playerLogicParams.entity.ClientLadderTestPos.Z = pos.z;
            
            //只是客户端检测触发
            if (!inCd && notInLadder && (hasInput || posChange ||  !Mathf.Approximately(playerLogicParams.entity.lastYaw, cmd.Yaw)))
            {
                //ui不显示的时候才能进行自动检测
                var ladderlist = Pool.GetList<PlayerLadderAdsorbResult>();

                if (CombatConfig.LadderDbg)
                {
                    logger.InfoFormat("<ladder>开始执行爬梯初筛检测 {0}",cmd.Sequence);
                }
                //先找到潜在列表
                if (PlayerLogicState.TryFindAdsorbLadder(playerLogicParams, cmd, ref ladderlist,false,false,false))
                {
                    //更新ladderlist
                    for (int i = ladderlist.Count - 1; i >= 0; i--)
                    {
                        var preladderResult = ladderlist[i];

                        bool shouldAngleTest = true;
                        bool enableSpeedTendency = false;
                        //有ui的情况下，如果已经在区域内了，那就不做角度检查了
                        if (preladderResult.ladderCache.testDir == LadderTestDir.Top)
                        {
                            shouldAngleTest = false;
                        }
                        else if (preladderResult.ladderCache.testDir == LadderTestDir.Bottom)
                        {
                            if (CombatConfig.LadderBottomUIOn)
                            {
                                shouldAngleTest = false;
                            }
                            else
                            {
                                enableSpeedTendency = true;
                            }
                        }
                        else if (preladderResult.ladderCache.testDir == LadderTestDir.Fly)
                        {
                            enableSpeedTendency = true;
                        }

                        if (!PlayerLogicState.LadderEnterTest(playerLogicParams, preladderResult.ladderCache, PlayerLoader.LadderConfig, preladderResult.adsorbCollideConfig,shouldAngleTest,enableSpeedTendency))
                        {
                            if (CombatConfig.LadderDbg)
                            {
                                logger.InfoFormat("<ladder> TryFindAdsorbLadder 进入检测失败 :{0} path:{1}",preladderResult.ladderCache.testDir,PlayerLogicState.GetTransformPath(preladderResult.adsorbCollideConfig.transform));
                            }
                                
                            ladderlist.RemoveAt(i);
                        }
                        else
                        {
                            if (preladderResult.ladderCache.testDir == LadderTestDir.Top)
                            {
                                //额外检查可以移动到
                                //玩家吸附点,第一次吸附的时候定位点
                                Vector3 adsorbPlayerPos = Vector3.zero;
                                //确定吸附目标点(脚的位置)
                                Vector3 adsorbFinalPos = Vector3.zero;
                                if (CombatConfig.LadderDbg)
                                {
                                    logger.InfoFormat("<ladder> 检测上方进入是否能够通过 :{0}", PlayerLogicState.GetTransformPath(preladderResult.ladderCache.adsorbCollideConfig.transform));
                                }

                                if (PlayerLogicState.LadderTopMoveTest(playerLogicParams, preladderResult.ladderCache,
                                        ref adsorbPlayerPos, ref adsorbFinalPos,false))
                                {
                                    if (CombatConfig.LadderDbg)
                                    {
                                        logger.InfoFormat("<ladder> 上方进入通过 :{0}", PlayerLogicState.GetTransformPath(preladderResult.adsorbCollideConfig.transform));
                                    }
                                }
                                else
                                {
                                    if (CombatConfig.LadderDbg)
                                    {
                                        logger.InfoFormat("<ladder> 上方进入失败 :{0}", PlayerLogicState.GetTransformPath(preladderResult.adsorbCollideConfig.transform));
                                    }
                                    
                                    ladderlist.RemoveAt(i);
                                }
                            }
                            else if (preladderResult.ladderCache.testDir == LadderTestDir.Bottom)
                            {
                                //额外检查可以移动到
                                //玩家吸附点,第一次吸附的时候定位点
                                Vector3 adsorbPlayerPos = Vector3.zero;
                                //确定吸附目标点(脚的位置)
                                Vector3 adsorbFinalPos = Vector3.zero;
                                if (CombatConfig.LadderDbg)
                                {
                                    logger.InfoFormat("<ladder> 检测下方进入是否能够通过 :{0}", PlayerLogicState.GetTransformPath(preladderResult.adsorbCollideConfig.transform));
                                }

                                if (PlayerLogicState.LadderBottomMoveTest(playerLogicParams, preladderResult.ladderCache, ref adsorbPlayerPos, ref adsorbFinalPos))
                                {
                                    if (CombatConfig.LadderDbg)
                                    {
                                        logger.InfoFormat("<ladder> 下方进入通过 :{0}", PlayerLogicState.GetTransformPath(preladderResult.adsorbCollideConfig.transform));
                                    }
                                }
                                else
                                {
                                    if (CombatConfig.LadderDbg)
                                    {
                                        logger.InfoFormat("<ladder> 下方进入失败 :{0}", PlayerLogicState.GetTransformPath(preladderResult.adsorbCollideConfig.transform));
                                    }
                                    
                                    ladderlist.RemoveAt(i);
                                }
                            }
                        }
                    }
                    
                    //找到最近的
                    PlayerLadderAdsorbResult adsorbData = new();
                    if (CombatConfig.LadderDbg)
                    {
                        logger.InfoFormat("<ladder>尝试找到最近的吸附梯子");
                    }
                    if (ladderlist.Count >0 && PlayerLogicState.TryFindNearestAdsorbLadder(playerLogicParams, ladderlist, ref adsorbData))
                    {
                        if (adsorbData.ladderCache.testDir == LadderTestDir.Top)
                        {
                            //通知ui
                            if (InteractiveIdListChecker.LadderState != ELadderState.CAN)
                            {
                                Mc.Msg.FireMsgAtOnce<ELadderState>(EventDefine.LadderStateChange, ELadderState.CAN);
                            }

                            //靠ui触发爬梯，不自动触发爬梯
                            playerLogicParams.entity.ClientLadderTargetId = adsorbData.ladderEntity.EntityId;
                            playerLogicParams.entity.ClientAdsorbTargetIndex = adsorbData.adsorbConfigIdx;

                        }
                        else if (CombatConfig.LadderBottomUIOn && adsorbData.ladderCache.testDir == LadderTestDir.Bottom)
                        {
                            //通知ui
                            if (InteractiveIdListChecker.LadderState != ELadderState.CAN_BOTTOM)
                            {
                                Mc.Msg.FireMsgAtOnce<ELadderState>(EventDefine.LadderStateChange, ELadderState.CAN_BOTTOM);
                            }

                            //靠ui触发爬梯，不自动触发爬梯
                            playerLogicParams.entity.ClientLadderTargetId = adsorbData.ladderEntity.EntityId;
                            playerLogicParams.entity.ClientAdsorbTargetIndex = adsorbData.adsorbConfigIdx;
                        }
                        else if (adsorbData.ladderCache.testDir == LadderTestDir.Fly)
                        {
                            playerLogicParams.entity.ClientLadderTargetId = adsorbData.ladderEntity.EntityId;
                            playerLogicParams.entity.ClientAdsorbTargetIndex = adsorbData.adsorbConfigIdx;
                            //自动触发爬梯
                            playerLogicParams.entity.ClientWantLadder = true;
                            
                            if (CombatConfig.LadderDbg)
                            {
                                logger.InfoFormat("<ladder> 空中梯子进入通过 :{0}", PlayerLogicState.GetTransformPath(adsorbData.adsorbCollideConfig.transform));
                            }
                        }
                        else
                        {
                            playerLogicParams.entity.ClientLadderTargetId = adsorbData.ladderEntity.EntityId;
                            playerLogicParams.entity.ClientAdsorbTargetIndex = adsorbData.adsorbConfigIdx;
                            //自动触发爬梯
                            playerLogicParams.entity.ClientWantLadder = true;
                            
                            if (CombatConfig.LadderDbg)
                            {
                                logger.InfoFormat("<ladder> {0} 梯子进入通过 :{1}",adsorbData.ladderCache.testDir, PlayerLogicState.GetTransformPath(adsorbData.adsorbCollideConfig.transform));
                            }
                        }
                    }
                    else
                    {
                        if (CombatConfig.LadderDbg)
                        {
                            logger.InfoFormat("<ladder> 没有找到最近的吸附梯子，爬梯失败 cmd序列号：{0}", cmd.Sequence);
                        }
                        LadderFailed();
                    }
                }
                else
                {
                    if (CombatConfig.LadderDbg)
                    {
                        logger.InfoFormat("<ladder> 没有找到潜在吸附梯子，爬梯失败 cmd序列号：{0}", cmd.Sequence);
                    }
                    LadderFailed();
                }

                Pool.ReleaseList(ladderlist);
                
                if (CombatConfig.LadderDbg)
                {
                    logger.InfoFormat("<ladder>结束执行爬梯初筛检测 {0}",cmd.Sequence);
                }
            }

            playerLogicParams.entity.lastYaw = cmd.Yaw;
            if (!cmd.IsFinite())
            {
                logger.WarnFormat("send cmd is not finite, nowMoveState:{0}, cmdPosX:{1}, cmdPosY:{2}, cmdPosZ:{3}", playerLogicParams.entity.MoveState, cmd.PosX, cmd.PosY, cmd.PosZ);
            }
        }

        private void LadderFailed()
        {
            playerLogicParams.entity.ClientLadderTargetId = -1;
            playerLogicParams.entity.ClientAdsorbTargetIndex = -1;
            if (InteractiveIdListChecker.LadderState != ELadderState.CAN_NOT)
                Mc.Msg.FireMsgAtOnce<ELadderState>(EventDefine.LadderStateChange, ELadderState.CAN_NOT);
        }

        public bool TryGetCurrentHeldItemGo<T>(out T heldItemGo, bool fp) where T : HeldItemGo
        {
            heldItemGo = null;
            if (fp)
            {
                if (FpPlayerGo == null) return false;
                return FpPlayerGo.TryGetHeldItemGo(MyEntityLocal.CurrentWeaponId, out heldItemGo);
            }
            else
            {
                if (TpPlayerGo == null) return false;
                return TpPlayerGo.TryGetHeldItemGo(MyEntityLocal.CurrentWeaponId, out heldItemGo);
            }
        }


        public bool TryGetHeldItemInterface<T>(long id, out T entity) where T : class, IItemEntity
        {
            entity = null;
            if (LocalHeldItemEntityController != null && LocalHeldItemEntityController.TryGetHeldItem(id, out var held))
            {
                entity = held as T;
            }

            return entity != null;
        }

        public bool TryGetCurrentHeldItemInterface<T>(out T entity) where T : class, IItemEntity
        {
            entity = null;
            if (LocalHeldItemEntityController != null && LocalHeldItemEntityController.TryGetHeldItem(MyEntityLocal.CurrentWeaponId, out var held))
            {
                entity = held as T;
            }

            return entity != null;
        }
        
        public void OnCurrentHeldItemChange(long oldId, long newId, IHeldItemEntity newHeld)
        {
            if (EquipWeaponId == newId)
                return;
            MyEntityLocal.UseExtraDraw = GameEnumUtils.GetItemEnum(newHeld.TableId) == TableItemEnum.Melee_paddle &&
                                                  Mc.Entity.GetEntity(MyEntityLocal.MountableId) is IBaseMountableEntity
                                                      mountableEntity && mountableEntity.VehicleType == (int)VehicleType.Kayak;

            // RefreshWaterBucketState();
            if (newId != MyEntityLocal.CurrentWeaponId)
            {
                logger.InfoFormat("武器id不一致 old:{0} new:{1}，CurrentWeaponId：{2}", oldId, newId, MyEntityLocal.CurrentWeaponId);
            }

            if (!TryGetCurrentHeldItemInterface(out IHeldItemEntity heldItem))
            {
                logger.InfoFormat("获取不到武器实体{0}", newId);
                return;
            }
            EquipWeaponId = newId;
            Mc.MyPlayer.EokaPistolAddViewkick = false;
            if (heldItem.EntityType == EntityTypeId.ThrowWeaponCustom)
            {
                var throwWeapon = (ThrowWeaponCustom)heldItem;
                throwWeapon.DelayThrow = false;
                throwWeapon.NextFireTime = MyEntityLocal.ClientTime + throwWeapon.TakeOutTime;
                MyEntityLocal.ThrowState = PlayerThrowState.Idle;
            }
            UpdatePlayerPropertyWithHeldItem(heldItem);
            CmdPreserver.OnHeldItemChanged(heldItem);
            CameraChangeState(CameraState.None, Mc.Camera.NowState);
            var gunFov = GetGunFov(heldItem);
            if (Mathf.Approximately(gunFov, 0f))
            {
                gunFov = 50f;
            }
            playerLogicParams.entity.GunFov = gunFov;
            // todo 判断武器类型id
            try
            {
                MgrAim.SetWeaponCallBack(MyEntityId);
            }
            catch (Exception e)
            {
                logger.Error(e);
            }

            MyEntityLocal.DrawScale = 1;
            MyEntityLocal.ReloadEndScale = 1;
            MyEntityLocal.FireScale = 1;
            MyEntityLocal.FireHitScale = 1;
            MyEntityLocal.AltFireScale = 1;
            MyEntityLocal.AltFireHitScale = 1;
            MyEntityLocal.InspectScale = 1;
            MyEntityLocal.ReloadScale = 1;
            MyEntityLocal.AdsOnScale = 1;
            MyEntityLocal.AdsOffScale = 1;

            OnMoveStateChangeActionVisible(MyEntityLocal.EntityId, MyEntityLocal.LastMoveState, MyEntityLocal.MoveState);
        }

        public void TryBagUseHeldItem(BaseItemNode item, long targetId = -1,
            BagUsePurposes purpose = BagUsePurposes.Normal)
        {
            this.BagHeldItemUseController?.TryUseBagHeldItem(item, targetId, purpose);
        }

        /// <summary>
        /// 设置某个go下全部render的shadowcast
        /// </summary>
        /// <param name="go"></param>
        /// <param name="mode"></param>


        private void CompareEquipHead(PlayerEntity entity)
        {
            var oldHeadId = MyEntityLocal.EquipHead?.TableId ?? 0;
            var newHeadId = entity.EquipHead?.TableId ?? 0;
            if (oldHeadId != newHeadId && newHeadId == EquipTableIds.HeavyDutyHelmets)
                Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.ObscureView);
        }
        public static T CloneOrCopyData<T>(T target, T src) where T : class, IEntity
        {
            if (src == null) return null;
            if (target == null || target.EntityId != src.EntityId) return (src as IPredictType).Clone() as T;
            var ctb = target as CustomTypeBase;
            ctb.DataSet.UpdateFromArrayDataSetLocal((src as CustomTypeBase).DataSet);
            return target;
        }
        public void SyncServerEntityModel(PlayerEntity entity)
        {
            if (MyEntityLocal == null || entity == null)
                return;
            OnEntityUpdate?.Invoke(entity);
            CompareEquipHead(entity);

            MyEntityLocal.MountableType = entity.MountableType;

            if (MyEntityLocal.TryGetIHitable(out IHitableEntity hitable))
            {
                hitable.Hp = entity.TryGetIHitable(out IHitableEntity server) ? server.Hp : 0;
            }

            //MyEntityLocal.Hp = entity.Hp;
            // MyEntityLocal.PlatformId = entity.PlatformId;
            MyEntityLocal.EquipHead = CloneOrCopyData(MyEntityLocal.EquipHead, entity.EquipHead);
            MyEntityLocal.EquipEye = CloneOrCopyData(MyEntityLocal.EquipEye, entity.EquipEye);
            MyEntityLocal.EquipJaw = CloneOrCopyData(MyEntityLocal.EquipJaw, entity.EquipJaw);
            MyEntityLocal.EquipUpperArmor = CloneOrCopyData(MyEntityLocal.EquipUpperArmor, entity.EquipUpperArmor);
            MyEntityLocal.EquipShirt = CloneOrCopyData(MyEntityLocal.EquipShirt, entity.EquipShirt);
            MyEntityLocal.EquipGlove = CloneOrCopyData(MyEntityLocal.EquipGlove, entity.EquipGlove);
            MyEntityLocal.EquipPants = CloneOrCopyData(MyEntityLocal.EquipPants, entity.EquipPants);
            MyEntityLocal.EquipLowerArmor = CloneOrCopyData(MyEntityLocal.EquipLowerArmor, entity.EquipLowerArmor);
            MyEntityLocal.EquipShoe = CloneOrCopyData(MyEntityLocal.EquipShoe, entity.EquipShoe);
            MyEntityLocal.EquipBag = CloneOrCopyData(MyEntityLocal.EquipBag, entity.EquipBag);
            MyEntityLocal.EquipVersion = entity.EquipVersion;
            MyEntityLocal.SafetyAreaState = entity.SafetyAreaState;

            //MyEntityLocal.NullHand = entity.NullHand;
            if (MyEntityLocal.LifeCycleFlags != entity.LifeCycleFlags)
            {
                logger.InfoFormat("LifeFlagChange SyncServer, local:{0}, server:{1}", MyEntityLocal.LifeCycleFlags, entity.LifeCycleFlags);
            }

            MyEntityLocal.LifeCycleFlags = entity.LifeCycleFlags;
            MyEntityLocal.IsDieFromCrawl = entity.IsDieFromCrawl;

            MyEntityLocal.AidedSourceEntityId = entity.AidedSourceEntityId;

            MyEntityLocal.Hydration = entity.Hydration;
            MyEntityLocal.Oxygen = entity.Oxygen;
            MyEntityLocal.RadiationPoison = entity.RadiationPoison;
            MyEntityLocal.RadiationLevel = entity.RadiationLevel;

            MyEntityLocal.Observer = entity.Observer;
            MyEntityLocal.ObserverLoadingFinishTime = entity.ObserverLoadingFinishTime;
            MyEntityLocal.ReduceSpeedRate = entity.ReduceSpeedRate;
            MyEntityLocal.ProhibitJumpCount = entity.ProhibitJumpCount;

            MyEntityLocal.DeathAnimValue = entity.DeathAnimValue;

            MyEntityLocal.ClientAuthorityTime = entity.ClientAuthorityTime;
            MyEntityLocal.MountableId = entity.MountableId;
            MyEntityLocal.HorseMountDir = entity.HorseMountDir;
            MyEntityLocal.MonumentTypeId = entity.MonumentTypeId;
            MyEntityLocal.VehicleType = entity.VehicleType;
            MyEntityLocal.MountSeatIndex = entity.MountSeatIndex;
            MyEntityLocal.IsDriver = entity.IsDriver;
            MyEntityLocal.NearbyRebornValidTime = entity.NearbyRebornValidTime;
            MyEntityLocal.NearbyRebornPVPValidTime = entity.NearbyRebornPVPValidTime;
            MyEntityLocal.EscapeFromStuckCount = entity.EscapeFromStuckCount;
            MyEntityLocal.EscapeFromStuckValidTime = entity.EscapeFromStuckValidTime;

            if (MyEntityLocal.MonumentId != entity.MonumentId)
            {
                InvokeMonumentChange(entity.MonumentId);
            }
            
            MyEntityLocal.MonumentId = entity.MonumentId;
            MyEntityLocal.ProhibitMountHorse = entity.ProhibitMountHorse;
            MyEntityLocal.OutpostRebornValidTime = entity.OutpostRebornValidTime;

            if (MyEntityLocal.LastMountableId != MyEntityLocal.MountableId)
            {
                InvokeMountChange();
            }

            if (MyEntityLocal.LastIsDriver != MyEntityLocal.IsDriver)
            {
                InvokeSeatChange();
            }

            if (Mc.VehicleInfo != null && Mc.VehicleInfo.MountUIStatus != GetMountStatus())
            {
                InvokeMountChange();
                InvokeSeatChange();
            }

            MyEntityLocal.IsInterior = entity.IsInterior;
            MyEntityLocal.IsHideSea = entity.IsHideSea;
            MyEntityLocal.IsOpenShadowFunc = entity.IsOpenShadowFunc;
            MyEntityLocal.IsLightVolumeTriggerChanged = entity.IsLightVolumeTriggerChanged;
            MyEntityLocal.MainLightIntensity = entity.MainLightIntensity;
            MyEntityLocal.EnvironmentIntensity = entity.EnvironmentIntensity;

            MyEntityLocal.ClothingBlocksAiming = entity.ClothingBlocksAiming;
            MyEntityLocal.ClothingMoveSpeedReduction = entity.ClothingMoveSpeedReduction;
            MyEntityLocal.ClothingWaterSpeedBonus = entity.ClothingWaterSpeedBonus;

            //MyEntityLocal.Horizontal = entity.Horizontal;
            //MyEntityLocal.Vertical = entity.Vertical;
            //MyEntityLocal.Slope = entity.Slope;
            //MyEntityLocal.State = entity.State;
            MyEntityLocal.TodType = entity.TodType;
            MyEntityLocal.MonumentLightVolumeId = entity.MonumentLightVolumeId;

            MyEntityLocal.CurrentLockingEntityId = entity.CurrentLockingEntityId;
            MyEntityLocal.CurrentLockingEntityToken = entity.CurrentLockingEntityToken;

            //额外速度
            MyEntityLocal.ExtraSpeedX = entity.ExtraSpeedX;
            MyEntityLocal.ExtraSpeedY = entity.ExtraSpeedY;
            MyEntityLocal.ExtraSpeedZ = entity.ExtraSpeedZ;
            
            MyEntityLocal.LastIsWounded = entity.LastIsWounded;
            
            //威胁值
            MyEntityLocal.IsHostile = entity.IsHostile;
        }

        private void InvokeMonumentChange(int monumentId)
        {
           Mc.Photo.OnMonumentChange(monumentId != -1);
        }

        public bool IsInSafetyArea()
        {
            return MyEntityLocal.SafetyAreaState != (int)ESafeAreaType.Outside;
        }
        
        private void InvokeMountChange()
        {
            RefreshMountableState();
            onMountableChanged?.Invoke(MyEntityLocal.MountableId);
            if ((MyEntityLocal.LastMountableId == 0 && MyEntityLocal.MountableId != 0 && MyEntityLocal.MountableType != EntityTypeId.ZiplineEntity && !MyEntityLocal.IsDriver) ||// todo: 临时处理，后面滑索入表就不需要了
                MyEntityLocal.MountableId != 0)
                onSeatChanged?.Invoke(MyEntityLocal.IsDriver, MyEntityLocal.MountableId);

            mgrMyLights.SetMountableId(MyEntityLocal.MountableId);
            MyEntityLocal.LastMountableId = MyEntityLocal.MountableId;
            if (Mc.Entity.TryGetEntity(MyEntityLocal.MountableId, out ZiplineEntity zipline))
            {
                //Debug.LogError($"[littleeye]  zipline :{zipline.RotateX},{zipline.RotateY},smooth:{zipline.RotateX_Smooth},{zipline.RotateY_Smooth},frame:{Time.frameCount}");
                Mc.Control.RotationControl.Yaw = zipline.RotateY;
                Mc.Control.RotationControl.Pitch = -zipline.RotateX;
            }
            // Debug.LogError("InvokeMountChange");
        }

        private void InvokeSeatChange()
        {
            RefreshMountableState();
            if (MyEntityLocal.MountableId != 0 && MyEntityLocal.MountableType != EntityTypeId.ZiplineEntity) // todo: 临时处理，后面滑索入表就不需要了
                onSeatChanged?.Invoke(MyEntityLocal.IsDriver, MyEntityLocal.MountableId);
            MyEntityLocal.LastIsDriver = MyEntityLocal.IsDriver;

            // Debug.LogError("InvokeSeatChange");
        }

        private void RefreshMountableState()
        {
            // MyEntityLocal的字段同步顺序不一样，所以为了保证数值肯定是最新的，所以用serverPlayer
            var serverPlayer = Mc.Entity.GetPlayerEntity(Mc.MyPlayer.MyEntityId);
            if (serverPlayer == null)
            {
                return;
            }
            if (serverPlayer.MountableId > 0)
            {
                SocDebug.VehicleLog($"MyEntityLocal.MountableId:{MyEntityLocal.MountableId}");
                GetMountableGo()?.ResetViewWhenOnMount(serverPlayer);
            }
        }

        public long GetMountStatus()
        {
            if (Mc.Entity.GetEntity(MyEntityLocal.MountableId) is IBaseMountableEntity baseMountableEntity)
            {
                var vehicleTableId = baseMountableEntity.TemplateId;
                if (baseMountableEntity is IVehicleModuleCustom moduleEntity) // 模块载具特殊一些，要拿到底盘
                {
                    if (Mc.Entity.GetEntity(moduleEntity.ModularCarId) is ModularCarEntity modularCarEntity)
                        vehicleTableId = modularCarEntity.TemplateId;
                }
                return vehicleTableId * 10 + (MyEntityLocal.IsDriver ? 1 : 0);
            }

            return 0;
        }

        /// <summary>
        /// 相机状态改变
        /// </summary>
        /// <param name="lastCameraState"></param>
        /// <param name="nowCameraState"></param>
        protected void CameraChangeState(CameraState lastCameraState, CameraState nowCameraState)
        {
            if (MyEntityLocal.MoveState == PlayerMoveStateEnum.Observer)
                return;
            CameraChangeStateVisible(lastCameraState, nowCameraState);

            switch (nowCameraState)
            {
                case CameraState.FirstSingle:
                    _fpCard?.Show();
                    _tpCard?.Hide();
                    break;
                case CameraState.Third:
                    _fpCard?.Hide();
                    _tpCard?.Show();
                    break;
            }

        }


        protected void OnCharacterStateChange(PlayerCharacterStateEnum oldState, PlayerCharacterStateEnum newState)
        {
            if (Mc.Camera.NowState != CameraState.FirstSingle)
                return;
            PlayerLoader.SetFPShadow(MyPlayerViewGoTp.gameObject, newState == PlayerCharacterStateEnum.UnActive);
        }
        

        protected void InitSceneCamera()
        {
            var cameraData = GetMyCamera.GetUniversalAdditionalCameraData();
            cameraData.renderPostProcessing = true;
            Mc.Camera.SceneCamera.gameObject.AddComponent<GameDraw>();
            Mc.Camera.SceneCamera.gameObject.AddComponent<Outliner>();
        }

        /// <summary>
        /// 初始化跟随人物的粒子特效
        /// </summary>
        protected void InitEffects()
        {
            fxUnderWater = new FxUnderwater();
            fxUnderWater.Start();

            //初始化草地扰动，-_-算作特效初始化吧
            if (MyPlayerViewGoTp != null)
            {
                var grassManager = MyPlayerViewGoTp.AddComponent<PlayerGrassInteractiveManager>();
                grassManager.Init();
            }
        }


        #region 界面相关

        /// <summary>
        /// 开启濒死界面
        /// </summary>
        /// <param name="serverEntity"></param>
        /// <param name="isAidingOther">是否是救援他人</param>
        private void OpenDyingWindow(PlayerEntity serverEntity, bool isAidingOther = false)
        {
            if (serverEntity != null)
            {
                var win = Mc.Ui.GetWindow("UiDying");
                if (win == null || !win.IsActive)
                {
                    Mc.Ui.OpenWindowT<UiDying>("UiDying", uiDyingWin => uiDyingWin.InAidingOtherState(isAidingOther));
                }
            }
            Mc.Msg.FireMsg(EventDefine.BreakGuide);
        }


        /// <summary>
        /// 开启复活界面
        /// </summary>
        /// <param name="serverEntity"></param>
        protected void OpenRespawnWindow(PlayerEntity serverEntity, string windowName = "UiDeadInfo")
        {
            Mc.Msg.FireMsg(EventDefine.BreakGuide);
            if (serverEntity != null)
            {
                Mc.Msg.FireMsgAtOnce(EventDefine.CloseAllMutexRespawnWindow, false);
                var win = Mc.Ui.GetWindow(windowName);
                if (win == null || !win.IsActive)
                {
                    Mc.Ui.OpenWindow(windowName);
                }
            }
        }
        
        /// <summary>
        /// 移除复活界面
        /// </summary>
        public void RemoveRespawnWindow()
        {
            Mc.Ui.RemoveWindow("UiCloseEyes", true);
            Mc.Ui.RemoveWindow("UiDeadInfo", true);
            Mc.Ui.RemoveWindow("UiRespawn", true);
        }

        /// <summary>
        /// 开启攻击效果界面
        /// </summary>
        /// <param name="serverEntity"></param>
        private void OpenDamageScreenEffectWindow(PlayerEntity serverEntity)
        {
            if (serverEntity != null)
            {
                var win = Mc.Ui.GetWindow("UiDamageScreenEffect");
                if (win == null || !win.IsActive)
                {
                    Mc.Ui.OpenWindow("UiDamageScreenEffect");
                }
            }
        }

        #endregion


        #region for rust

        /// <summary>
        /// 找到角色 面前 锥形范围内 最近 的一个 player
        /// </summary>
        /// <param name="maxAngle">最大角度范围</param>
        /// <param name="maxDistance">最远距离</param>
        /// <returns>没有则返回null</returns>
        public PlayerEntity GetPlayerEntityInConeRange(float maxAngle = 120f, float maxDistance = 3f)
        {
            var allPlayers = Mc.Entity.GetEntitiesViaType<PlayerEntity>(); // 所有人的entity

            var myForward = MyPlayerViewGoTp.transform.forward; // 自己的前向向量
            myForward.y = 0f;

            // Debug.LogError(myForward);

            var myPos = MyPlayerColliderTransform.position;

            PlayerEntity res = null;

            foreach (var kvp in allPlayers)
            {
                if (kvp.Key == MyEntityId) // 过滤掉自己
                    continue;

                var otherPos = new Vector3(kvp.Value.PosX, kvp.Value.PosY, kvp.Value.PosZ);

                var self2OtherVec = otherPos - myPos;
                self2OtherVec.y = 0f;

                var dis = Vector3.Distance(otherPos, myPos);
                var angle = Vector3.Angle(myForward, self2OtherVec);

                // Debug.LogError(kvp.Value.EntityId + " " + dis + " " + angle);

                // DebugDraw.DrawRay(new Vector3(myPos.x, myPos.y + 1f, myPos.z), myForward * 10f, Color.red,5f);
                // DebugDraw.DrawRay(new Vector3(myPos.x, myPos.y + 1f, myPos.z), self2OtherVec * 10f, Color.blue,5f);
                // Debug.LogError(kvp.Value.Name + " " + dis + " " + angle);
                if (dis < maxDistance && angle < maxAngle / 2.0f)
                {
                    maxDistance = dis;
                    res = kvp.Value;
                }
            }

            return res;
        }


        #endregion

        #region 换弹相关

        /// <summary>
        /// 尝试触发换弹操作
        /// </summary>
        public int CheckReloadTrigger(IHaveBulletEntity havebullet,UserCmd cmd, int expectAmmo = 0,long ammoNodeId = 0)
        {
            if (MyEntityLocal == null)
                return 0;
            if(MyEntityLocal.IsAidingOther)
                return 0;
            if (havebullet != null)
            {
                if (havebullet is WeaponCustom custom && !HeldItemSystemUtils.ClientReloadWorkbenchCheck(MyEntityLocal, custom, cmd))
                {
                    return 0;
                }
                
                var reloadAmmoId = PredictAmmoID(havebullet, expectAmmo);
                if (reloadAmmoId > 0)
                {
                    cmd.ReloadAmmoId = reloadAmmoId;
                    cmd.ReloadAmmoNodeId = ammoNodeId;
                    cmd.Reload = true;
                    InterruptBagReload();
                    return reloadAmmoId;
                }
                else
                {
                    if (GameEnumUtils.GetItemEnum(havebullet.TableId) == TableItemEnum.Melee_chainsaw)
                    {
                        Mc.MsgTips.ShowRealtimeWeakTip(24089);
                    }
                }
            }
            return 0;
        }

        /// <summary>
        /// 换弹预测的弹药id
        /// </summary>
        /// <param name="ammoId"></param>
        /// <returns></returns>
        public int PredictAmmoID(IHaveBulletEntity weapon, int ammoId = 0)
        {
            if (weapon == null)
            {
                logger.Error("cannot reach here");
                return 0;
            }

            if (weapon.ClipCapacity <= 0)
                return 0;

            var Availablebullet = weapon.AvailableAmmos;
            if (Availablebullet == null || Availablebullet.Length == 0)
            {
                logger.ErrorFormat("{0}没有对应子弹列表：", weapon.TableId);
                return 0;
            }

            if (ammoId != 0)
            {
                bool valid = false;
                for (int i = 0; i < Availablebullet.Length; i++)
                {
                    if (ammoId == Availablebullet[i])
                    {
                        valid = true;
                        break;
                    }
                }

                if (!valid)
                {
                    logger.ErrorFormat("武器表{0} 不能装 {1} 的子弹", weapon.TableId, ammoId);
                    return 0;
                }
            }

            if (CombatConfig.bOffline)
            {
                return ammoId;
            }

            if (ammoId != 0 && Mc.CollectionItem.GetAmount(ammoId) > 0)
                return ammoId;

            //下面是自动选择逻辑
            int usingAmmo = weapon.UsingAmmoId;
            int ammoInClip = weapon.Clips;
            if (ammoInClip > 0 && Mc.CollectionItem.GetAmount(usingAmmo) > 0)
            {
                //如果弹夹还有弹药，且这种弹药背包有存量，则直接使用这种弹药
                return usingAmmo;
            }

            //否则，选择一个可以提供最多弹夹子弹量的子弹，如果能提供的子弹量相同，选一个优先级高的（优先级使用表格配置顺序）
            int targetAmmoId = -1;
            int maxSupply = 0;
            var mgrItems = Mc.CollectionItem;
            for (int i = 0; i < Availablebullet.Length; i++)
            {
                int id = Availablebullet[i];
                int keep = Math.Min(weapon.ClipCapacity, mgrItems.GetAmount(id));
                if (keep <= ammoInClip) //不高于当前弹夹子弹量的子弹忽略
                    continue;
                if (keep > maxSupply)
                {
                    //本身就是按优先级排序，优先级低的，只有数量更多才会被选择
                    maxSupply = keep;
                    targetAmmoId = id;
                }
            }

            if (targetAmmoId == -1) //没找到合适子弹，不执行换弹
                return 0;
            return targetAmmoId;
        }
        
        #endregion

        public void CheckDryFire() {
            if (Mc.MyPlayer.TryGetCurrentHeldItemInterface(out WeaponCustom weapon) && weapon.Clips == 0)
            {
                if (!CheckBagAvailableAmmo(weapon) && weapon.TryGetWeaponDisplay(out var dis))
                {
                    Mc.Audio.Add2DAudio(dis.DryfireSoundId);
                }
            }
        }

        public bool CheckBagAvailableAmmo(IHaveBulletEntity haveBulletEntity)
        {
            int bag_count = 0;
            if (haveBulletEntity.AvailableAmmos != null && haveBulletEntity.AvailableAmmos.Length > 0)
            {
                foreach (var bizId in haveBulletEntity.AvailableAmmos)
                {
                    bag_count += this.MyEntityServer.InventoryComponent.GetAmount(bizId);

                }
            }
            return bag_count > 0;
        }

        #region 武器配件相关

        protected void UIStateEvent(WindowComBase win)
        {
            if (null != win && win.uiName == "UiInventory")
            {
                if (MyEntityLocal.SightStateEnum != SightStateEnum.Out)
                {
                    //如果不处在腰射，退出机瞄
                    Mc.Control.ExecuteActionAtName(ActionName.Fire2);
                }
            }
        }

        #endregion


        string fovPath, gunFovCurvePath;
        ulong _loadFovAssetAsyncId, _loadGunFovCurveAsyncId;
        /// <summary>
        /// 手持物（包括配件）变化带来的玩家属性变化
        /// </summary>
        public void UpdatePlayerPropertyWithHeldItem(IHeldItemEntity helditem)
        {
            if (helditem.EntityId != MyEntityLocal.CurrentWeaponId)
                return;//配件的回调不一定是当前手持物

            if (RecoilData == null)
            {
                RecoilData = new RecoilData();
            }
            RecoilData.Init(null);
            System.Numerics.Vector3 pos = System.Numerics.Vector3.Zero;
            System.Numerics.Vector3 rot = System.Numerics.Vector3.Zero;

            if (helditem.EntityType == EntityTypeId.WeaponCustom)
            {
                WeaponCustom weapon = helditem as WeaponCustom;

                //获取ads相关参数
                var weaponTb = Mc.Tables.TbGunBase.GetOrDefault(weapon.TableId);
                if (weaponTb == null)
                    return;

                MyEntityLocal.AdsFov = weaponTb.ADSFOV;

                int scopeId = 0;
                if (weapon.SelectWeaponAccessory(HeldItemUtility.ScopeSelector, out var scope))
                {
                    scopeId = (int)scope.TableId;
                }

                if (HeldItemUtility.FindAdsOffsetData(weapon.TableId, scopeId, out GunOffset tb))
                {//镜子没耐久
                    pos += new System.Numerics.Vector3(tb.Hipxoffset, tb.Hipyoffset, tb.Hipzoffset);
                    rot += new System.Numerics.Vector3(tb.Hippitchoffset, tb.Hipyawoffset, tb.Hiprolloffset);
                }

                if (HeldItemUtility.FindWeaponRecoilData(weapon.TableId, scopeId, out var recoilTb))
                {
                    RecoilData.Init(recoilTb);
                }

                int adsUpAnimTime = weaponTb.AdsUpAnimTime;
                int adsUpGunFOVTime = weaponTb.AdsUpGunFovTime;
                int adsDownAnimTime = weaponTb.AdsDownAnimTime;
                int adsDownGunFOVTime = 400;//weaponTb.AdsDownGunFovTime;

                var partTb = McCommon.Tables.TbPartBase.GetOrDefault(scopeId);
                if (partTb != null)
                {
                    //只有瞄具才被认为带有fov属性
                    MyEntityLocal.AdsFov = partTb.ADSFOV;
                    adsUpAnimTime = partTb.AdsUpAnimTime;
                    adsDownAnimTime = partTb.AdsDownAnimTime;
                    adsUpGunFOVTime = partTb.AdsUpGunFOVTime;
                    adsDownGunFOVTime = partTb.AdsDownGunFOVTime;
                }

                int fovLerpTime = 0;
                float gunFovTarget = MyEntityLocal.GunFov;// 第一人称gunfov目标值
                float gunFovOffset = 0;// 第一人称gunfov导致武器的偏移值 gunkick要用
                CharacterFovConfig nowCharacterFovConfig = null;
                CharacterFovConfig nowGunFovConfig = null;

                if (HeldItemUtility.FindWeaponFovData(weapon.TableId, scopeId, out var fovTb))
                {
                    adsUpAnimTime = (int)(adsUpAnimTime * fovTb.AdsUpTimeScale);
                    adsUpGunFOVTime = (int)(adsUpGunFOVTime * fovTb.AdsUpTimeScale);
                    adsDownAnimTime = (int)(adsDownAnimTime * fovTb.AdsDownTimeScale);
                    adsDownGunFOVTime = (int)(adsDownGunFOVTime * fovTb.AdsDownTimeScale);
                }

                MyEntityLocal.AdsFovOnTime = weapon.AdsUpTime;
                MyEntityLocal.AdsFovOffTime = weapon.AdsDownTime;
                MyEntityLocal.AdsUpAniTime = adsUpAnimTime;
                MyEntityLocal.AdsDownAniTime = adsDownAnimTime;
                MyEntityLocal.AdsUpGunFovTime = adsUpGunFOVTime;
                MyEntityLocal.AdsDownGunFovTime = adsDownGunFOVTime;

                ((ClientContext)Mc.System.Context).MgrGunkickFormula.UpdateGunkickFormula(weapon.EntityId);

                //Debug.LogError($"weapon:{weapon.GetHashCode()},AdsFovOnTime:{MyEntityLocal.AdsFovOnTime },AdsFovOffTime：{MyEntityLocal.AdsFovOffTime},AdsUpAniTime:{MyEntityLocal.AdsUpAniTime}" +$"AdsDownAniTime：{MyEntityLocal.AdsDownAniTime},AdsUpGunFovTime:{MyEntityLocal.AdsUpGunFovTime}，AdsDownGunFovTime：{MyEntityLocal.AdsDownGunFovTime}");
            }


            MyEntityLocal.FpPositionOffset = pos;
            MyEntityLocal.FpRotationOffset = rot;
            OnUpdateADS();
            BreatheViewMotionController.OnHeldItemChange(helditem);
            FpHeldItemBreatheMotionController.OnHeldItemChange(helditem);
            FpHeldItemMoveMotionController.OnHeldItemChange(helditem);
            MyEntityLocal.UpdateClipRate(this.MyFpAnimator.runtimeAnimatorController as AnimatorOverrideController, helditem.TableId, helditem.SkinId);
        }

        /// <summary>
        /// 飞天开关
        /// </summary>
        /// <param name="active"></param>
        public void ToggleFly(bool active)
        {
            if (MyEntityServer != null)
            {
                if (MyEntityServer.DebugEnable && active)
                {
                    openFly = true;
                }
                else
                {
                    openFly = false;
                }
            }
            else
            {
                openFly = active;
            }
        }

        private float GetGunFov(IItemEntity entity)
        {
            if (entity == null)
                return 0;
            var tableId = entity.TableId;

            if (tableId == 0)
            {
                return McCommonUnity.Tables.TbHoldItemBase.GetOrDefault(tableId).GunFovHip;
            }
            else
            {
                var tb = McCommonUnity.Tables.TbItemConfig.GetOrDefault(tableId);

                if (tb != null)
                {
                    switch (tb.Type)
                    {
                        case ItemEntityType.Weapon: //远程
                            return McCommonUnity.Tables.TbGunBase.GetOrDefault(tableId).GunFovHip;
                        case ItemEntityType.ThrowItem: //投掷物
                            return McCommonUnity.Tables.TbThrowWeapon.GetOrDefault(tableId).GunFovHip;
                        case ItemEntityType.HoldItem: //手持道具
                        case ItemEntityType.Melee: //近战
                        case ItemEntityType.Use: //手持
                            return McCommonUnity.Tables.TbHoldItemBase.GetOrDefault(tableId).GunFovHip;
                    }
                }
            }

            return 0;
        }

        void SetTpAnimator(bool enable)
        {
            if (TpPlayerGo != null && TpPlayerGo.Animator != null)
                TpPlayerGo.Animator.enabled = enable;
        }

        public void AfterWaitReconnectInGame()
        {
            var serverEntity = Mc.Entity.GetPlayerEntity(playerLogicParams.entity.EntityId);
            if (playerLogicParams != null && serverEntity != null)
            {
                Mc.MyPlayer.Rollback(serverEntity, serverEntity.CmdAck);
            }
            SetTpAnimator(true);
            if (MyEntityServer.IsDead)
            {
                Mc.LoginStep.StepLog("玩家软掉线重连为死亡状态, 进入复活界面");
                OpenRespawnWindow(MyEntityServer);
            }
        }

        /// <summary>
        /// 快速重连解析完增量快照后
        /// </summary>
        public void AfterFastReconnectParseIncremental()
        {
            AddLifeCycleFlagEvent(Mc.MyPlayer.MyEntityServer?.LifeCycleFlags ?? LifeCycleFlagEvent.InvalidLifeCycleFlags);
        }

        private void AddLifeCycleFlagEvent(int flags = LifeCycleFlagEvent.InvalidLifeCycleFlags)
        {
            LifeCycleFlagEvent e = LifeCycleFlagEvent.NewEvent();
            e.LifeCycleFlags = flags;
            e.SourceId = Mc.MyPlayer.MyEntityId;
            McCommon.LocationBasedEvent.AddEvent(e);
        }

        public override void OnFastDisconnected(DisconnectReason reason)
        {
            base.OnFastDisconnected(reason);
            SetTpAnimator(false);
        }

        public override void OnFastReconnected()
        {
            base.OnFastReconnected();
        }

        public override Task OnEnterWorld()
        {
            return base.OnEnterWorld();
        }

        public bool CanMakeWaterCmd()
        {
            if (MyEntityLocal.ActionState != PlayerActionStateEnum.Hold)
            {
                Mc.MsgTips.ShowRealtimeWeakTip(IsFetchingBottle);
                return false;
            }

            // if (MyEntityLocal.ActionHoldState != PlayerActionHoldStateEnum.Normal)
            // {
            //     Mc.MsgTips.ShowRealtimeWeakTip(state2tips[MyEntityLocal.ActionHoldState]);
            //     return false;
            // }
            var tb = McCommon.Tables.TbPlayerInteractiveState.GetOrDefault(MyEntityLocal.InteractiveId);
            if (tb != null)
            {
                if (waterstate2tips.TryGetValue(tb.InteractiveType, out int tipId))
                {
                    Mc.MsgTips.ShowRealtimeWeakTip(tipId);
                    return false;
                }
            }

            return true;
        }

        public bool DrinkWaterCmd(WaterBottleItemNode item = null)
        {
            bool useCurSelectItem = item == null;
            if (useCurSelectItem && !CanMakeWaterCmd())
                return false;

            if (useCurSelectItem)
                item = UiHudElemShortcuts.GetCurSelectItem() as WaterBottleItemNode;
            BaseItemNode waterItem = null;
            if (null != item) waterItem = item.GetWaterItem();
            if (null == waterItem || waterItem.Amount <= 0)
            {
                Mc.MsgTips.ShowRealtimeWeakTip(3020);
                return false;
            }

            // 喝盐水需要额外提示
            if (waterItem.BizId == ItemConst.SaltWaterItemId)
            {
                Mc.MsgTips.ShowRealtimeWeakTip(3022);
                Mc.Audio.AddAudio("Svl_Vomit", MyEntityId);

                var forward = Mc.MyPlayer.GetMyCamera.transform.forward;
                var pos = new Vector3(MyEntityLocal.PosX, MyEntityLocal.PosY, MyEntityLocal.PosZ) + forward;
                var cameraHitShakeData = CameraHitShakeData.Generate();
                cameraHitShakeData.PosX = pos.x;
                cameraHitShakeData.PosY = pos.y;
                cameraHitShakeData.PosZ = pos.z;
                cameraHitShakeData.WeaponTableId = -3;
                cameraHitShakeData.DirX = -forward.x;
                cameraHitShakeData.DirY = -forward.y;
                cameraHitShakeData.DirZ = -forward.z;
                McCommon.SystemRequestMgr.AddRequest(ESystemRequest.CameraHitShakeDataSet, cameraHitShakeData);
            }
            InteractionUtil.TryInteract(PlayerInteractiveId.DrinkWater);
            // InteractionUtil.TryDrinkWater(0);
            // if (useCurSelectItem)
            // {
            //     InteractionUtil.TryDrinkWater(0);
            //     // Mc.UserCmd.NowCmd.DrinkWater = true;
            // }
            // else
            // {
            //     Mc.Audio.AddAudio("Svl_Drink", MyEntityId);
            //     DrinkWaterWithItem(item);
            // }

            return true;
            // logger.Info($"water cmd: DrinkWater");
        }

        // protected void CollectWaterStart()
        // {
        //     var itemBottle = UiHudElemShortcuts.GetCurSelectItem() as WaterBottleItemNode;
        //     if (null == itemBottle) return;
        //     var curBottleId = itemBottle.Id;
        //
        //     int waterType = 0;
        //     var curWaterState = Mc.Water.playerInWaterState;
        //     if (curWaterState == MgrWater.EPlayerInWaterState.Sea) waterType = ItemConst.SaltWaterItemId;
        //     else if (curWaterState == MgrWater.EPlayerInWaterState.River) waterType = ItemConst.PureWaterItemId;
        //
        //     if (waterType <= 0) return;
        //     MyEntityServer.RemoteCallStartWaterBottleCollect(ERpcTarget.Simulator, waterType);
        //     logger.Error($"CollectWaterStart :{curBottleId}");
        // }
        //
        // protected void CollectWaterEnd()
        // {
        //     //var itemBottle = UiHudElemShortcuts.GetCurSelectItem() as WaterBottleItemNode;
        //     var itemBottle = Mc.CollectionItem.PlayerRoot.GetNodeById(MyEntityLocal.CurrentWeaponId) as WaterBottleItemNode;
        //     Mc.Audio.StopAudio("Svl_WaterBottle_Fill_Loop", MyEntityLocal.EntityId);
        //     if (null == itemBottle) return;
        //     var getWater = (int)Mc.MyPlayer.MyEntityLocal.getWaterSum;
        //     if (getWater <= 0) return;
        //     var curBottleId = itemBottle.Id;
        //     MyEntityServer.RemoteCallWaterBottleCollect(ERpcTarget.Simulator, curBottleId, getWater);
        //     Mc.MyPlayer.MyEntityLocal.getWaterSum = 0;
        //     logger.Error($"CollectWaterEnd :{curBottleId}");
        // }

        // private void DrinkWaterWithItem(WaterBottleItemNode item)
        // {
        //     if (item == null)
        //         return;
        //     BaseItemNode waterItem = item.GetWaterItem();
        //     if (null == waterItem || waterItem.Amount <= 0)
        //         return;
        //
        //     Mc.CollectionItem.InventoryCom.RemoteCallWaterBottleDrink(ERpcTarget.World, item.Id, Mathf.Min(waterItem.Amount, Mc.Tables.TbGlobalConfig.DrinkWaterEveryTime));
        // }

        // public bool PourWaterCmd(Func<bool> extraCheck)
        // {
        //     if (!CanMakeWaterCmd())
        //         return false;
        //
        //     if (extraCheck != null && !extraCheck())
        //         return false;
        //     // Mc.UserCmd.NowCmd.PourWater = true;
        //     return true;
        // }

        public void DropWaterItem(long curWaterId)
        {
            var forward = Mc.Camera.SceneCamera.transform.forward;
            forward.y = 0;
            forward = forward.normalized;
            // forward = MyPlayerViewGoTp.transform.forward;

            float velocityX = MyEntityLocal.SpeedX + forward.x;
            float velocityY = MyEntityLocal.SpeedY + 2; //Mathf.Max(MyEntityLocal.SpeedY, 0);
            float velocityZ = MyEntityLocal.SpeedZ + forward.z;
            Mc.CollectionItem.PourWater(curWaterId, velocityX, velocityY, velocityZ);
        }

        // protected void PourWater()
        // {
        //     if (MyEntityLocal.bagUsePurposes == BagUsePurposes.PlantBoxAddWater)
        //     {
        //         MyEntityLocal.bagUseAction?.Invoke();
        //         MyEntityLocal.bagUseAction = null;
        //         MyEntityLocal.bagUsePurposes = BagUsePurposes.Normal;
        //         return;
        //     }
        //
        //     long curWaterId = -1;
        //     var itemBottle = UiHudElemShortcuts.GetCurSelectItem() as WaterBottleItemNode;
        //     if (null == itemBottle) return;
        //     var waterItem = itemBottle.GetWaterItem();
        //     if (waterItem == null || waterItem.Amount <= 0)
        //         return;
        //     curWaterId = waterItem.Id;
        //     // Mc.Audio.AddAudio("Svl_WaterBottle_Slosh", MyEntityId);
        //     DropWaterItem(curWaterId);
        //     // logger.Info($"water cmd: PourWater");
        // }

        protected void OnCharacterStateChange(PlayerEntity entity, PlayerCharacterStateEnum oldState, PlayerCharacterStateEnum newState)
        {
        }

        protected void OnCharacterPoseStateChange(PlayerEntity entity, PlayerPoseStateEnum oldState, PlayerPoseStateEnum newState)
        {
            if (MyEntityId != entity.EntityId)
                return;

            if (TerrainMeta.WaterMap == null)
                return;
            var height = PlayerBaseState.CalcWaterHeight(MyEntityLocal);
            var pos = MyPlayerColliderTransform.position;
            pos.y = height;
            if (newState is PlayerPoseStateEnum.Swim or PlayerPoseStateEnum.Dive)
            {
                if (playerLogicParams.entity.SpeedY <= -McCommon.Tables.TbChracterParameter.PlaySwimSprayFxSpeedY) //假如速度比较大，则发一个水花的快照事件
                {
                    Mc.Effect.PlayEffect<EffectItem>(7007, MyEntityId, pos);
                }
            }
        }

        public void OnHurt(DamageDataEvent damageData)
        {
            if (Mc.Tables.TbGlobalConfig.HurtPutAwayPickup && (damageData.WeaponTableId > 0 || damageData.SourceType == EntityTypeId.MonsterEntity))
            {
                Mc.Msg.FireMsg(EventDefine.PackUpPickList);
            }

            var isEnvironmentHurt = (DamageType)damageData.DamageType is DamageType.Hunger or DamageType.Thirst
                or DamageType.Cold or DamageType.Heat or DamageType.Bleeding or DamageType.Poison 
                or DamageType.Radiation or DamageType.RadiationExposure or DamageType.Decay;
            if (isEnvironmentHurt == false)
            {
                getPlayerDamgeTime = Time.time;
                StopEscapeLoading();
            }

            Mc.Msg.FireMsg(EventDefine.MyPlayerOnHurt, damageData);
        }

        /// <summary>
        /// 获取全身属性
        /// </summary>
        public float GetWholeAttrValue(DamageType type, int factor = 100)
        {
            var playerEntity = Mc.MyPlayer.MyEntityLocal;
            if (null == playerEntity) return 0;
            int index = (int)type;
            if (null == playerEntity.ArmorProtection || index >= playerEntity.ArmorProtection.Length || index < 0) return 0;
            return playerEntity.ArmorProtection[index] * factor;
        }

        /// <summary>
        /// 获取部位属性
        /// </summary>
        public float GetPartAttrValue(DamageType type, int bodyIndex, int factor = 100)
        {
            var playerEntity = Mc.MyPlayer.MyEntityLocal;
            if (null == playerEntity || !playerEntity.TryGetComponent(EComponentIdEnum.Damageable,out DamageableComponent dc)) return 0;
            if (bodyIndex >= dc.AreaProtections.Length) return 0;
            var bodyAttrs = dc.AreaProtections[bodyIndex];
            var index = (int)type;
            if (null == bodyAttrs || 0 == bodyAttrs.Length || index >= bodyAttrs.Length || index < 0) return 0;
            return bodyAttrs[index] * factor;
        }

        public static BasePlayerGo InitLocalPlayer(IEntity entity)
        {
            PlayerEntity serverPlayer = (PlayerEntity)entity;

            SocAnimationManager.InitSelfPlayer(serverPlayer.EntityId);
            PlayerLogicStateMachine.Event.Emit(StateEventType.RefreshAnim, serverPlayer);

            // Mc.MyPlayer.InitEntityModelByServerFullSpanShot(serverPlayer); //后面的go创建流程依赖entity某些数据，因此这个函数提前调用
            // PlayerGo go = PlayerGo.CreatePlayerGo(entity, Mc.Go.GetRoot("Players")); //先得初始化tp，后面Start里面初始化Fp要依赖这个tpproxy。。
            // Mc.MyPlayer.TpPlayerGo = go.goProxy;
            Mc.MyPlayer.Start(serverPlayer);
            var cmdAck = Mc.MyPlayer.UserCmdSequence <= 0 ? 1 : (int)serverPlayer.CmdAck+100;//UserCmdSequence有值局内断线重连
            Mc.UserCmd.InitCmdSequence(cmdAck);
            Mc.Predict.LastCompareCmdSequence = -1;
            Mc.Predict.LastAddCmdSequence = -1;
            Mc.MyPlayer.UserCmdSequence = cmdAck;
            if (Mc.ObserverMode)
            {
                Mc.MyPlayer.ReviewStart();
                ObserverEntity.Instance.InitStartPlayer();
            }
            Mc.Ui.OpenWindow("UiHudMarker");
            return Mc.MyPlayer.TpPlayerGo; //保持以前的流程，这个要加到MgrEntityGo里
        }

        public Vector3 GetPlayerForwardPos(float forwardScale, float xOffset = 0f, float yOffset = 0f, float zOffset = 0f)
        {
            var playerEntity = MyEntityLocal;
            var forward = Quaternion.Euler(0, playerEntity.RotateY, 0) * Vector3.forward;
            var pos = new Vector3(playerEntity.PosX + xOffset, playerEntity.PosY + yOffset, playerEntity.PosZ + zOffset) + forward * forwardScale;
            return pos;
        }

        private int _timeSinceLastFire = 4000; // 得大一点，不然第一次在马上会被误判为乘客视角
        public bool IsDriverView(BaseMountableGo baseMountableGo)
        {
            var playerEntity = MyEntityLocal;
            var isDriver = playerEntity.IsDriver;
            var actionState = playerEntity.ActionState;
            var sightState = playerEntity.SightStateEnum;
            var vehicleType = baseMountableGo.MountableEntity.VehicleType;
            var param = baseMountableGo.VehicleCameraParams;

            bool res = false;
            switch ((VehicleType)vehicleType)
            {
                case VehicleType.Kayak:
                    res = HeldItemSystemUtils.IsPaddle(MyEntityLocal.CurrentWeaponTableId);
                    break;
                case VehicleType.Horse:
                    bool isFireOrAdsOn = playerEntity.InAttackState || (sightState != SightStateEnum.None && sightState != SightStateEnum.Out);
                    _timeSinceLastFire = isFireOrAdsOn ? 0 : Mathf.Min(_timeSinceLastFire + (int)(Time.deltaTime * 1000f), param.timeToDriverViewOnHorseAfterFire);
                    res = !isFireOrAdsOn && _timeSinceLastFire >= param.timeToDriverViewOnHorseAfterFire; // 既不在开火和开镜，并且开火过了一段时间，才算司机位
                    break;
                case VehicleType.Parachute:
                    res = HeldItemSystemUtils.IsParachute(MyEntityLocal.CurrentWeaponTableId);
                    break;
                default:
                    res = isDriver;
                    break;
            }

            return res;
        }

        #region get oil

        public static readonly List<long> OilItemPath = new() { NodeSystemType.Storage, VehicleContainerIndex.VehicleOilContainerIndex, 0 };
        public static readonly List<long> OilContainerPath = new() { NodeSystemType.Storage, VehicleContainerIndex.VehicleOilContainerIndex };
        public static readonly List<long> ModuleSlotContainerPath = new() { NodeSystemType.Storage, 0 };


        public int GetOilCount()
        {
            var itemNode = MyRootNode?.GetNodeByPath(OilItemPath) as BaseItemNode;
            if (itemNode == null)
            {
                return 0;
            }

            return itemNode.GetAmount(itemNode.BizId);
        }

        public ItemContainerNode GetOilItemContainerNode()
        {
            return MyRootNode?.GetNodeByPath(OilContainerPath) as ItemContainerNode;
        }

        public ItemContainerNode GetModuleSlotContainerNode(IBaseMountableEntity CurVehicleEntity)
        {
            var root = MyRootNode;
            if (root == null)
                return null;
            if (CurVehicleEntity is not ModularCarEntity CurModularCarEntity)
                return null;

            var index = -1;
            foreach (var IVehicleModuleCustom in CurModularCarEntity.GetModuleEntities())
            {
                if (IVehicleModuleCustom == null)
                {
                    continue;
                }

                var tb = McCommon.Tables.TbVehicleModule.GetOrDefault(IVehicleModuleCustom.TemplateId);
                if (tb == null)
                    continue;

                foreach (var type in tb.ModuleType)
                {
                    if (type == VehicleModuleType.Engine)
                    {
                        index = IVehicleModuleCustom.SocketIndex;
                        break;
                    }
                }
                if (index >= 0)
                    break;
            }

            ModuleSlotContainerPath[1] = index;

            return root.GetNodeByPath(ModuleSlotContainerPath) as ItemContainerNode;
        }


        private List<IAlpha3ItemContainerWrap> allModuleSlotContainerNodes;
        public List<IAlpha3ItemContainerWrap> GetAllModuleSlotContainerNodes(IBaseMountableEntity CurVehicleEntity)
        {
            var rootNode = MyRootNode;
            if (rootNode == null)
                return null;
            if (CurVehicleEntity is not ModularCarEntity CurModularCarEntity)
                return null;

            allModuleSlotContainerNodes ??= new List<IAlpha3ItemContainerWrap>();
            allModuleSlotContainerNodes.Clear();
            var e = rootNode.GetNodeById(NodeConst.ModularContainerNodeId) as ItemContainerNode;
            if (e != null)
            {
                foreach (var (_, itemNode) in e)
                {
                    if (itemNode is EngineItemNode engineNode)
                    {
                        allModuleSlotContainerNodes.Add(engineNode);
                    }

                }
            }
            return allModuleSlotContainerNodes;
        }

        public ItemContainerNode GetHorseEquipContainerNode(IBaseMountableEntity CurVehicleEntity)
        {
            var rootNode = MyRootNode;
            if (rootNode == null)
                return null;
            if (CurVehicleEntity is not HorseEntity curHorseEntity)
                return null;

            if (rootNode.GetNodeByPath(NodeSystemType.Storage) is not SystemRootNode storageNode) return null;

            foreach (var (_, node) in storageNode)
            {
                if (node is not ItemContainerNode container || container.BizId != VehicleContainerTableId.HorseEquip)
                    continue;

                return container;
            }

            return null;
        }

        public ItemContainerNode GetVehicleModularContainerNode(IBaseMountableEntity curModularEntity)
        {
            var rootNode = MyRootNode;
            if (rootNode == null)
                return null;
            if (curModularEntity is not ModularCarEntity entity)
                return null;

            if (rootNode.GetNodeByPath(NodeSystemType.Storage) is not SystemRootNode storageNode) return null;

            foreach (var (_, node) in storageNode)
            {
                if (node is not ItemContainerNode container || container.BizId != NodeConst.ModularContainerNodeId)
                    continue;
                return container;
            }

            return null;
        }

        public ItemContainerNode GetHorseStorageContainerNode(IBaseMountableEntity CurVehicleEntity)
        {
            var player = MyEntityServer;
            if (player == null)
                return null;
            if (CurVehicleEntity is not HorseEntity curHorseEntity)
                return null;

            if (player.RootNode.GetNodeByPath(NodeSystemType.Storage) is not SystemRootNode storageNode) return null;

            foreach (var (_, node) in storageNode)
            {
                if (node is not ItemContainerNode container || container.BizId == VehicleContainerTableId.HorseEquip)
                    continue;

                return container;
            }

            return null;
        }

        public int GetOilCapacity()
        {
            var containerNode = GetOilItemContainerNode();
            if (containerNode == null)
            {
                // Debug.LogError(1);
                return 100;
            }
            var config = McCommon.Tables.TbContainer.GetOrDefault(containerNode.BizId);
            if (config == null)
            {
                // Debug.LogError(2);
                return 100;
            }
            // Debug.LogError(config.Stack);
            return config.Stack;
        }

        #endregion

        public bool IsOpenFly()
        {
            return openFly;
        }

        protected void OnMountChanged(long mountedId)
        {
            UiFocusInfo.ForceUpdate = true;
            // MyEntityLocal的字段同步顺序不一样，所以为了保证数值肯定是最新的，所以用serverPlayer
            var serverPlayer = Mc.Entity.GetPlayerEntity(Mc.MyPlayer.MyEntityId);
            if (MyEntityLocal.LastMountableId > 0)
            {
                if (Mc.Go.GetGo(MyEntityLocal.LastMountableId) is BaseMountableGo mountableGo)
                {
                    mountableGo.OnClientPlayerDismounted(serverPlayer);
                }
                else
                {
                    BaseMountableGo.ResetViewWhenOnDismount(serverPlayer);
                }
            }
            if (serverPlayer.MountableId > 0)
            {
                if (Mc.Go.GetGo(serverPlayer.MountableId) is BaseMountableGo mountableGo)
                {
                    mountableGo.OnClientPlayerMounted(serverPlayer);
                }
            }


            if (Mc.Entity.GetEntity(mountedId) is not IBaseMountableEntity baseMountableEntity)
                return;

            var tb = McCommon.Tables.TbVehicleInfo.GetOrDefault(baseMountableEntity.TemplateId);
            if (tb == null || tb.VehicleType != VehicleType.Kayak)
                return;

            Mc.MsgTips.ShowRealtimeWeakTip(22123);
        }
        protected void OnMountSeatIndexChanged(CustomTypeBase customTypeBase, int oldValue, int newValue)
        {
            // MyEntityLocal的字段同步顺序不一样，所以为了保证数值肯定是最新的，所以用serverPlayer
            var serverPlayer = Mc.Entity.GetPlayerEntity(Mc.MyPlayer.MyEntityId);
            if (Mc.Go.GetGo(serverPlayer.MountableId) is BaseMountableGo mountableGo)
            {
                /* 玩家座位发生变化时*/
                mountableGo.OnClientSwitchSeat(serverPlayer, oldValue, newValue);
            }
            SetAutoDrive(false);
        }

        // private void EquipFpAniCallBack(MgrWeapon.WeaponFinishPara weaponFinishPara)
        // {
        //     //只有当前装备的武器id等于回调的id，才触发回调
        //     //避免先装备的武器的回调后触发，导致覆盖当前装备的武器
        //     var finish_skinid = weaponFinishPara.CurrentSkinId;
        //     var cur_skinid = weaponFinishPara.PlayerEntity.CurrentWeaponSkinId;
        //     bool entity_equal = weaponFinishPara.ItemEntity.EntityId == weaponFinishPara.PlayerEntity.CurrentWeaponId;
        //
        //     if (entity_equal && finish_skinid == cur_skinid)
        //     {
        //         ProfilerApi.BeginSample(EProfileFunc.CallBack_Equip_FpAnim);
        //         var runtime = SocAnimationManager.GetFp(weaponFinishPara.PlayerEntity.EntityId);
        //         runtime?.SwitchRuntimeAnimatorController(weaponFinishPara.AOverrideController);
        //         ProfilerApi.EndSample(EProfileFunc.CallBack_Equip_FpAnim);
        //     }
        //
        //     // if (weaponFinishPara.ShowEquipPos == ShowEquipPos.Hand)
        //     // {
        //     //     var myweapon = GetCurrentWeapon();
        //     //     myweapon.EquipFpAniCallBack();
        //     // }
        // }

        // private void EquipTpAniCallBack(MgrWeapon.WeaponFinishPara weaponFinishPara)
        // {
        //
        //     var finish_skinid = weaponFinishPara.CurrentSkinId;
        //     var cur_skinid = weaponFinishPara.PlayerEntity.CurrentWeaponSkinId;
        //     bool entity_equal = weaponFinishPara.ItemEntity.EntityId == weaponFinishPara.PlayerEntity.CurrentWeaponId;
        //
        //     if (entity_equal && finish_skinid == cur_skinid)
        //     {
        //         ProfilerApi.BeginSample(EProfileFunc.CallBack_Equip_TpAnim);
        //         var runtime = SocAnimationManager.Get(weaponFinishPara.PlayerEntity.EntityId);
        //         runtime?.SwitchRuntimeAnimatorController(weaponFinishPara.AOverrideController);
        //         MyTpAnimator.cullingMode = AnimatorCullingMode.CullUpdateTransforms;
        //         ProfilerApi.EndSample(EProfileFunc.CallBack_Equip_TpAnim);
        //     }
        //
        // }

        /* 预测载具移动*/
        /// <summary>
        ///
        /// </summary>
        /// <param name="frameTime"></param>
        /// <param name="timeSinceLastUpdate"></param>
        /// <param name="cmd"></param>
        /// <param name="physicsUpdateCount"></param>
        /// <param name="tickTime">物理tick次数</param>
        /// <param name="allFramteTime">这一个渲染帧run 物理tick的总时长</param>
        public void PredictMountableMove(int frameTime, int timeSinceLastUpdate, UserCmd cmd, int physicsUpdateCount, int tickTime, int allPhysicTime, out BaseMountableGo mountableGo)
        {
            mountableGo = null;
            if (!preloadFinish)
                return;

            if (!Mc.Control.ControlEnabled)
                return;

            //序列号检查
            if (cmd == null || cmd.Sequence <= UserCmdSequence - 1)
                return;

            if (IsMountDriver == false)
            {
                return;
            }

            var mountable = GetMountableGo();
            if (mountable == null || mountable.LoadFinished == false)
            {
                return;
            }
            if (AutoDrive)
            {
                cmd.MoveForward = true;
            }
            mountable.AuthoritySide = mountable.GetAuthoritySide(cmd, null);
            if (mountable.IsAuthority == false)
            {
                return;
            }

            //马的化跳出
            if(mountable is ClientSocHorseGo)
                return;

            // 现在的物理是由脚本调用的，间隔就是frameTime，所以大部分载具的fixedDeltaTime就是frameTime参数
            if (mountable.UseFixedFrameTimeWhenPredict)
            {
                for (int i = 0; i < physicsUpdateCount; i++)
                {
                    mountable.Predict(frameTime, cmd, tickTime, allPhysicTime);
                }
            }
            else
            {
                mountable.Predict(timeSinceLastUpdate, cmd, tickTime, allPhysicTime);
            }

            mountable.UpdateCmd(cmd, Mc.MyPlayer.MyEntityLocal);

            //载具预测移动后,需更新载具平台上的人的位置,否则人会抖动。
            // LocalMountablePredictAfter();

            mountableGo = mountable;
        }
        /*

        public List<MountableChildSmoothData> mountableChildSmoothDatas = new List<MountableChildSmoothData>();

        public bool Add2MountableChild(ISmoothCloneEntity smoothEntity, SmoothComp smoothComp,
            Vector3 localPos,
            Quaternion localRot)
        {
            if (!Mc.MyPlayer.IsMountDriver)
            {
                return false;
            }

            IEntityGo go = null;
            if (smoothEntity.MountID > 0)
            {
                if (smoothEntity is PlayerEntity playerEntity)
                {
                    go = Mc.Go.GetGo(playerEntity.MountableId);
                }
            }
            else
            {
                go = Mc.Go.GetGo(smoothEntity.ParentId);
            }

            var mountableId = Mc.MyPlayer.MyEntityLocal.MountableId;
            var entity = Mc.Entity.GetEntity(mountableId);
            Transform transform = go?.MainTransform;
            if (entity != null && entity.RootParent != null && go != null && go.Entity != null &&
                go.Entity.RootParent != null && transform != null)
            {
                BaseMountableEntity bme = go.Entity.RootParent as BaseMountableEntity;
                if (bme != null)
                {
                    if (Mc.Go.GetGo(bme.EntityId) is BaseMountableGo baseMountableGo)
                    {
                        if (baseMountableGo.IsAuthority)
                        {
                            if (entity.RootParent.EntityId == bme.EntityId)
                            {
                                MountableChildSmoothData data = MountableChildSmoothData.NewRequest();
                                data.child = smoothComp.Transform;
                                data.parent = transform;
                                data.localPos = localPos;
                                data.localRot = localRot;
                                data.childRot = false;
                                data.smoothCloneEntity = smoothEntity;
                                Mc.MyPlayer.mountableChildSmoothDatas.Add(data);
                                return true;
                            }
                        }
                    }
                }
            }

            return false;
        }

        public void LocalMountablePredictAfter()
        {
            for (int i = 0; i < mountableChildSmoothDatas.Count; i++)
            {
                var dataItem = mountableChildSmoothDatas[i];
                if (dataItem != null)
                {
                    if (dataItem.child != null && dataItem.parent != null)
                    {
                        Vector3 pos = dataItem.parent.TransformPoint(dataItem.localPos);
                        dataItem.child.position = pos;
                        dataItem.smoothCloneEntity.PosX_Smooth = pos.x;
                        dataItem.smoothCloneEntity.PosY_Smooth = pos.y;
                        dataItem.smoothCloneEntity.PosZ_Smooth = pos.z;
                        if (dataItem.childRot)
                        {
                            dataItem.child.rotation = dataItem.parent.rotation * dataItem.localRot;
                        }
                        else
                        {
                            dataItem.child.rotation = dataItem.localRot;
                        }
                        Vector3 rot = dataItem.child.rotation.eulerAngles;
                        dataItem.smoothCloneEntity.RotateX_Smooth = rot.x;
                        dataItem.smoothCloneEntity.RotateY_Smooth = rot.y;
                        dataItem.smoothCloneEntity.RotateZ_Smooth = rot.z;
                    }
                    MidApi.Free(dataItem.Mid);
                }
            }

            mountableChildSmoothDatas.Clear();
        }
        */

        public void UpdateEquips(List<long> equips, Dictionary<long, long> skins = null)
        {
            if (null != FpPlayerGo) FpPlayerGo.UpdateEquips(equips, skins);
            Mc.FullScreenEffect?.CheckEquipFullScreenEffect();
        }

        public void OnRespawnResetCollider()
        {
            var gos = Mc.Go.GetTargetTypeGos(EntityTypeId.CarshredderEntity);
            if (gos == null) return;

            foreach (var (_, carshredder) in gos)
            {
                if (carshredder is CarshredderGo shredderGo)
                {
                    shredderGo.ManualRemoveCollider(MyEntityServer);
                }
            }
        }
        public static NodeBase GetNodeBase(IAlpha3ItemContainerWrap ContainerNode, long pos)
        {
            if (ContainerNode is ItemContainerNode node1)
            {

                return node1.GetChild(pos);
            }
            else if (ContainerNode is DirectoryItemNode node2)
            {
                return node2.GetChild(pos);
            }
            else
            {
                return null;
            }
        }

        public static void GetNodePath(IAlpha3ItemContainerWrap ContainerNode, List<long> output)
        {
            if (ContainerNode is ItemContainerNode node1)
            {

                node1.GetPath(output);
            }
            else if (ContainerNode is DirectoryItemNode node2)
            {
                node2.GetPath(output);
            }
        }

        public void InitPredictHorse()
        {
            if(HorseGoPredict!=null)
                return;
            HorseGoPredict = new HorseGoPredict();
            HorseGoPredict.PredictInit();
        }

        public void PredictHorse(UserCmd cmd)
        {
            if (!preloadFinish)
                return;

            if (!Mc.Control.ControlEnabled)
                return;

            //序列号检查
            if (cmd == null || cmd.Sequence <= UserCmdSequence - 1)
                return;
            if (IsMountDriver == false)
            {
                HorseGoPredict.PredictLeave();
                return;
            }
            var mountable = GetMountableGo();
            if (mountable == null || mountable.LoadFinished == false)
            {
                return;
            }
            //不是马的化跳出
            ClientSocHorseGo clientHorseGo = mountable as ClientSocHorseGo;
            if (clientHorseGo == null)
                return;
            if (AutoDrive)
            {
                cmd.MoveForward = true;
            }
            mountable.AuthoritySide = mountable.GetAuthoritySide(cmd, null);
            if (mountable.IsAuthority == false)
            {
                return;
            }
            //预测
            HorseGoPredict.PredictUpdate(cmd,false, clientHorseGo);
            //刷新cmd数据
            mountable.UpdateCmd(cmd, Mc.MyPlayer.MyEntityLocal);
            cmd.CarDataPhyxSimulateTime = cmd.Interval;
            mountable.RecordPosAndRot(cmd.Interval, cmd.Sequence);
        }

        public void InitPredictParachute()
        {
            if (ParachuteGoPredict != null)
                return;
            ParachuteGoPredict = new ParachuteGoPredict();
            ParachuteGoPredict.PredictInit();
        }

        public void PredictParachute(UserCmd cmd)
        {
            if (!preloadFinish || !Mc.Control.ControlEnabled)
                return;

            if (cmd == null || cmd.Sequence <= UserCmdSequence - 1)
                return;
            if (IsMountDriver == false)
            {
                ParachuteGoPredict.PredictLeave();
                return;
            }
            
            var mountable = GetMountableGo();
            if (mountable == null || mountable.LoadFinished == false)
                return;
            
            ClientParachuteGo clientParachuteGo = mountable as ClientParachuteGo;
            if (clientParachuteGo == null)
                return;

            mountable.AuthoritySide = mountable.GetAuthoritySide(cmd, null);
            if (mountable.IsAuthority == false)
                return;

            ParachuteGoPredict.PredictUpdate(cmd, clientParachuteGo);
            // 刷新cmd数据
            //mountable.UpdateCmd(cmd, Mc.MyPlayer.MyEntityLocal);
            cmd.CarDataPhyxSimulateTime = cmd.Interval;
            //mountable.RecordPosAndRot(cmd.Interval);
        }

        /// <summary>
        /// 获取debug组件
        /// </summary>
        /// <returns></returns>
        public PlayerDebugComponent GetPlayerDebugComponent()
        {
            if (MyEntityServer.GetComponent(EComponentIdEnum.Debug) is PlayerDebugComponent debugComponent)
            {
                return debugComponent;
            }

            return null;
        }
        
        public PlayerConstructionComponent GetPlayerConstructionComponent()
        {
            if (MyEntityServer.GetComponent(EComponentIdEnum.PlayerConstruction) is PlayerConstructionComponent constructionComponent)
            {
                return constructionComponent;
            }

            return null;
        }

        /// <summary>
        /// 玩家自身收到伤害
        /// </summary>
        private void OnSelfDamage(DamageDataEvent data)
        {
            if (data.DamageRelation == (int)DamageRelation.Teammate || data.SourcePlayerId == Mc.MyPlayer.MyEntityLocal.EntityId) return;
            if (data.SourceType != EntityTypeId.MonsterEntity && data.SourceType != EntityTypeId.PlayerEntity &&
                data.SourceType != EntityTypeId.PartEntity && data.SourceType != EntityTypeId.TrapEntity) return;
            canUseCampingTentTimeSec = TimeStampUtil.GetNowSec() + Mc.Tables.TbGlobalConfig.CampingTentCDAfterHitted;
        }

        /// <summary>
        /// 玩家当前是否可以使用帐篷
        /// </summary>
        public bool CanCampingTentUse(bool showReasonTips = true)
        {
            if (null == MyEntityServer) return false;
            // 玩家受击后的N秒内不能使用帐篷
            if (TimeStampUtil.GetNowSec() <= canUseCampingTentTimeSec)
            {
                if (showReasonTips) Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.CampingTentCDAfterHittedTips);
                return false;
            }
            // 判断帐篷使用时间
            var com = MyEntityServer.SafeOfflineComp;
            if (null == com) return false;
            var endDateTime = TimeStampUtil.MSec2LocalDateTime(com.LastEndTime);
            var curMs = TimeStampUtil.GetNowTimeStampMSec();
            var curDateTime = TimeStampUtil.MSec2LocalDateTime(curMs);
            // 如果当前使用事件和场次帐篷结束事件不在同一天，直接认为可用，因为帐篷隔天会刷新
            if (endDateTime.Date != curDateTime.Date) return true;
            // 如果是在同一天，则需要判断todayRemain是否大于0
            if (com.TodayRemainingSafeTime <= 0)
            {
                if (showReasonTips) Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.CampingTentTimeOut);
                return false;
            }
            return true;
        }

        /// <summary>
        /// 比较毒刺导弹锁定token预测
        /// </summary>
        /// <param name="serverEntity"></param>
        public void CompareTargetingLauncherToken(PlayerEntity serverEntity)
        {
            var weaponId = serverEntity.CurrentWeaponId;
            var weapon = serverEntity.GetHeldItemByEntityId(weaponId) as WeaponCustom;

            if (weapon == null || !TargetingLauncherData.IsTargetingLauncher(weapon))
                return;
            
            var weaponType = GameEnumUtils.GetItemEnum(weapon.TableId);
            var tokenDatas = MyEntityLocal.TargetingLauncherTokens;
            if (tokenDatas.TryGetValue(weaponType, out var tokenData))
            {
                tokenData.SyncServerTargetToken(serverEntity);
            }
            else
            {
                // 如果没有对应武器的TokenData，则创建一个
                tokenData = new TargetingLauncherData(weapon, MyEntityLocal);
                tokenDatas.TryAdd(weaponType, tokenData);
                tokenData.SyncServerTargetToken(serverEntity);
            }
        }

        private void OnHostileChanged(CustomTypeBase entity, bool oldValue, bool newValue)
        {
            if (!oldValue && newValue)
            {
                if (Mc.Photo is null) return;
                Mc.Photo.OnHostileChanged();
            }
        }
    }
}
/*
public class MountableChildSmoothData : Alpha3PooledObjectBase
{
    public Transform child;
    public Transform parent;
    public Vector3 localPos;
    public Quaternion localRot;
    public bool childRot;
    public ISmoothCloneEntity smoothCloneEntity;
    //public PlayerEntity playerEntity;

    public override void Dispose()
    {
        child = null;
        parent = null;
    }

    public static MountableChildSmoothData NewRequest()
    {
        return New<MountableChildSmoothData>();
    }
}
*/