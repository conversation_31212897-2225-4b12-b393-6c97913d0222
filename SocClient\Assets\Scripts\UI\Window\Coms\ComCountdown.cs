using System;
using FairyGUI;
using FairyGUI.Utils;
using Cysharp.Text;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Profile;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Ui.Binder.CommonGlobal;
using UnityEngine;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.SocClient.Control;
using WizardGames.Soc.SocClient.Manager;
using System.Diagnostics;

namespace WizardGames.Soc.SocClient.Ui
{
    public class ComCountdown : GComponent
    {
        private ComCountdownBinder binder;
        private int curMs = -1;
        private int maxMs = -1;
        private EventCallback0 onFinishCall;
        private EventCallback0 onCancelCall;
        private EventCallback0 onAbortCall;
        private string secPostfix;
        private long timerUpdate;

        private int MS_UPDATE_OFFSET = 50;//更新间隔，单位毫秒
        private Stopwatch stopwatch = new Stopwatch();//TimerWheel 平均间隔过大，使用 Stopwatch 进行精确计时

        public bool AutoClearOnCall = true;
        public bool AutoHideWhenCancel = true;//点击取消时自动隐藏
        public override void ConstructFromXML(XML xml)
        {
            base.ConstructFromXML(xml);

            binder = new ComCountdownBinder(asCom);
            binder.Cancel.onClick.Set(OnCancel);
            //#70229  【hud】通用进度条显示时间单位为：S
            //secPostfix = LanguageManager.GetTextConst(LanguageConst.UnitSec);
            secPostfix = "s";

            binder.BinderRoot.InitKeyTips("key", ActionName.RightButton);
        }
        public void CallAbort()
        {
            var call = onAbortCall;
            if (AutoClearOnCall) Clear();
            call?.Invoke();
        }
        public void CallCancel()
        {
            binder.Cancel.onClick.Call();
        }

        private void SetCountdown(int ms)
        {
            curMs = ms;
            binder.Content.Time.text = ZString.Concat(Mathf.CeilToInt(ms / 1000f), secPostfix);
            binder.Content.Bar.fillAmount = (float)curMs / maxMs;
            LogHelper.GetLogger(typeof(MgrHudCommon)).Info($"UiHudElemCountDown curMs:{curMs}  maxMs:{maxMs}");
            if (curMs <= 0) OnFinish();
        }

        public void Clear()
        {
            curMs = -1;
            maxMs = -1;
            onFinishCall = null;
            onCancelCall = null;
            if (timerUpdate > 0) Mc.TimerWheel.CancelTimer(timerUpdate);
            timerUpdate = -1;
            stopwatch.Stop();
        }

        private void OnFinish()
        {
            var call = onFinishCall;
            if (AutoClearOnCall) Clear();
            call?.Invoke();
        }

        private void OnCancel()
        {
            var call = onCancelCall;
            if (AutoHideWhenCancel) Clear();
            call?.Invoke();
        }

        private void OnTimerUpdate(long timerId, object data = null, bool delete = false)
        {
            if (curMs <= 0) return;
            ProfilerApi.BeginSample(EProfileFunc.Timer_UI_ComCountdown);
            SetCountdown(maxMs - (int)stopwatch.ElapsedMilliseconds);
            ProfilerApi.EndSample(EProfileFunc.Timer_UI_ComCountdown);
        }

        public void SetData(int msCountdown, string tips, EventCallback0 onFinish = null, EventCallback0 onClickCancel = null, EventCallback0 onAbort = null, bool autoHideWhenCancel = true)
        {
            Clear();
            stopwatch.Restart();
            curMs = msCountdown;
            maxMs = curMs;
            onCancelCall = onClickCancel;
            onAbortCall = onAbort;
            onFinishCall = onFinish;
            SetCountdown(curMs);
            binder.Content.Tips.text = tips;
            bool showCancel = null != onClickCancel;
            binder.CtrlStyle.SetSelectedPage(showCancel ? "withCancel" : "normal");
            timerUpdate = Mc.TimerWheel.AddTimerRepeat(MS_UPDATE_OFFSET, MS_UPDATE_OFFSET, OnTimerUpdate);
            this.AutoHideWhenCancel = autoHideWhenCancel;
        }
        /// <summary>
        /// 设置Icon，传入空字符串则不显示图标，默认状态不显示图标.
        /// </summary>
        /// <param name="iconUrl"></param>
        public void SetIcon(string iconUrl)
        {
            if (string.IsNullOrEmpty(iconUrl))
            {
                binder.Content.Icon.url = "";
                binder.Content.CtrlShowIcon.SetSelectedPage("false");
                return;
            }
            binder.Content.Icon.url = iconUrl;
            binder.Content.CtrlShowIcon.SetSelectedPage("true");
        }

        public void SetKeyTipsAction()
        {
            binder.BinderRoot.SetKeyTipsAction("key", CallCancel);
        }
        public void ClearKeyTipsAction()
        {
            binder.BinderRoot.ClearKeyTipsAction("key");
        }

        public override void Dispose()
        {
            Clear();
            base.Dispose();
        }
    }
}