using Assets.Scripts.Plant;
using Facepunch;
using Systems;
using UnityEngine;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Data.tips;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.Unity.Extend;
using WizardGames.Soc.Common.Unity.ObjPool;
using WizardGames.Soc.Common.Unity.Utility;
using WizardGames.Soc.SocClient.GoLoader;
using WizardGames.Soc.SocClient.Manager;

namespace WizardGames.Soc.SocClient.Ui
{
    public class PlantCtrl : IRecyclable
    {
        private long seedId;
        public long SeedId => seedId;
        private PlantStage stage;
        public PlantStage Stage => stage;
        private bool isManured;

        private bool isWatered;

        private Transform root;

        private GameObject plantObj;

        private OutlineConfig outlineConfig => Mc.Tables.TbOutlineConfig.GetOrDefault(2001);

        private ulong asyncId = 0;

        public void RefreshModel(PlantData plantData, Transform parent, string layer, bool watered = false)
        {
            //如果不是同样的seed或者生长stage发生变化，释放go对象
            if (seedId != plantData.SeedId || stage != plantData.Stage || isManured != plantData.IsManure || isWatered != watered)
            {
                ReleaseGO();
            }

            seedId = plantData.SeedId;
            stage = plantData.Stage;
            isManured = plantData.IsManure;
            isWatered = watered;

            root = parent;
            if (plantObj == null && asyncId == 0)
            {
                var modelCfg = PlantUtils.GetPlantModelCfg(seedId, stage);
                if (modelCfg != null)
                {
                    asyncId = GoPool.GetAsync(modelCfg.Path, (go, objs) =>
                    {
                        asyncId = 0;
                        plantObj = go;
                    
                        if (plantObj == null)
                        {
                            return;
                        }
            
                        EnhancedDisplayEffect.AddOutlineComponent(plantObj, false);
                        plantObj.layer = LayerMask.NameToLayer(layer);
            
                        if (root != null)
                        {
                            plantObj.transform.SetParent(root.transform);
                            plantObj.transform.localPosition = PlantUtils.GetVec(modelCfg.Offset, Vector3.zero);
                            plantObj.transform.localRotation = Quaternion.Euler(PlantUtils.GetVec(modelCfg.Rotation, Vector3.zero));
                            plantObj.transform.localScale = modelCfg.Scale == 0 ? Vector3.one : modelCfg.Scale * Vector3.one;
                        
                        }
                        RefreshTag(plantData,layer);
                        
                    }, InstanceTypeId.DEFAULT_INSTANCE_TYPE_ID);
                }
            }else if (plantObj != null)
            {
                RefreshTag(plantData,layer);
            }
        }

        private void RefreshTag(PlantData plantData,string layer)
        {
            if (!string.IsNullOrEmpty(layer))
            {
                Mc.UiModel.SetGoLayer(plantObj, LayerMask.NameToLayer(layer), 1 << 5);
            }
            else
            {
                Mc.UiModel.SetGoLayer(plantObj, LayerMask.NameToLayer("Default"), 1 << 0);
            }
        }
        
        

        public void RefreshModelPrePlant(long seedId, PlantStage stage, Transform parent, string layer)
        {
            this.seedId = seedId;
            this.stage = stage;
            root = parent;
            if (plantObj == null && asyncId == 0)
            {
                var modelCfg = PlantUtils.GetPlantModelCfg(seedId, stage);
                if (modelCfg != null)
                {
                    asyncId = GoPool.GetAsync(modelCfg.Path, (go, objs) =>
                    {
                        asyncId = 0;
                        plantObj = go;
                    
                        if (plantObj == null)
                        {
                            return;
                        }
            
                        if (root != null)
                        {
                            plantObj.transform.SetParent(root.transform);
                            plantObj.transform.localPosition = PlantUtils.GetVec(modelCfg.Offset, Vector3.zero);
                            plantObj.transform.localRotation = Quaternion.Euler(PlantUtils.GetVec(modelCfg.Rotation, Vector3.zero));
                            plantObj.transform.localScale = modelCfg.Scale == 0 ? Vector3.one : modelCfg.Scale * Vector3.one;
                            if (!string.IsNullOrEmpty(layer))
                            {
                                Mc.UiModel.SetGoLayer(plantObj, LayerMask.NameToLayer(layer), 1 << 5);
                            }
                            else
                            {
                                Mc.UiModel.SetGoLayer(plantObj, LayerMask.NameToLayer("Default"), 1 << 0);
                            }
                        }
                        
                    }, InstanceTypeId.DEFAULT_INSTANCE_TYPE_ID);
                }
            }
        }

        public void EnableOutline()
        {
            EnhancedDisplayEffect.EnableOutline(plantObj, outlineConfig.OutlineColor, outlineConfig.OutlineBoldness, outlineConfig.OutlineOpacity);
        }

        public void DisableOutline()
        {
            EnhancedDisplayEffect.DisableOutline(plantObj);
        }

        private void ReleaseGO()
        {
            if (asyncId != 0)
            {
                var modelCfg = PlantUtils.GetPlantModelCfg(seedId, stage);
                if (!string.IsNullOrWhiteSpace(modelCfg.Path))
                {
                    GoPool.CancelAsync(modelCfg.Path, asyncId);
                    asyncId = 0;
                }
            }
            if (plantObj != null)
            {
                var modelCfg = PlantUtils.GetPlantModelCfg(seedId, stage);
                if (modelCfg != null)
                {
                    GoPool.Release(modelCfg.Path, plantObj);
                }
                else
                {
                    GameObject.Destroy(plantObj);
                }
                plantObj = null;
            }
        }

        public void OnGet() 
        {

        }

        public void OnRelease()
        {
            root = null;
            ReleaseGO();
            seedId = -1;
        }

        public void OnDestroy() 
        {
            root = null;
            ReleaseGO();
            seedId = -1;
        }
    }
}
