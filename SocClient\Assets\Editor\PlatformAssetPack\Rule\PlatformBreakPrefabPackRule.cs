﻿using Soc.Common.Unity.PlatformResOpt;
using System;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace PackTool
{
    [CreateAssetMenu(fileName = "PlatformBreakPrefabPackRule", menuName = "平台资产配置/规则/断开预制体的资产规则", order = 0)]
    public class PlatformBreakPrefabPackRule : PlatformPackPrefabSearchRule
    {
        public override bool ShouldExecute(PackInputParams packInputParams)
        {
            if (!base.ShouldExecute(packInputParams))
            {
                return false;
            }
            var buildAssetPath = packInputParams.processSourcePath;
            GameObject targetGo = null;
            if (packInputParams.assetTarget is GameObject)
            {
                targetGo = packInputParams.assetTarget as GameObject;
            }
            else if (packInputParams.assetTarget is Component comp)
            {
                targetGo = comp.gameObject;
            }
            
            if (targetGo == null)
            {
                ErrorFormat(packInputParams.context,"<PackPrefabRule> 资源不是gameobject:{0}", buildAssetPath);
                return false;
            }
            
            return true;
        }
        
        public override int ExecuteMainRule(PackInputParams packInputParams)
        {
            GameObject targetGo = null;
            if (packInputParams.assetTarget is GameObject)
            {
                targetGo = packInputParams.assetTarget as GameObject;
            }
            else if (packInputParams.assetTarget is Component comp)
            {
                targetGo = comp.gameObject;
            }
            var buildAssetPath = packInputParams.processSourcePath;
            var prefabType = PrefabUtility.GetPrefabAssetType(targetGo);
            if (prefabType == PrefabAssetType.Variant || prefabType == PrefabAssetType.Regular)
            {
                //如果是prefab实例，断开链接
                //PrefabUtility.IsPartOfNonAssetPrefabInstance((UnityEngine.Object) instanceRoot)
                //PrefabUtility.IsOutermostPrefabInstanceRoot(instanceRoot)
                var assetPath = AssetDatabase.GetAssetPath(targetGo);

                var cloned = PrefabUtility.InstantiatePrefab(targetGo) as GameObject;

                if (PrefabUtility.IsPartOfNonAssetPrefabInstance(cloned) &&
                    PrefabUtility.IsOutermostPrefabInstanceRoot(cloned))
                {
                    var oldGuid = AssetDatabase.GUIDFromAssetPath(assetPath);
                    //但是meta不能变
                    var oldContent = File.ReadAllText(assetPath + ".meta");
                    
                    AssetDatabase.DeleteAsset(assetPath);

                    var finalGo = PrefabUtility.SaveAsPrefabAsset(cloned, assetPath, out bool success);

                    if (success)
                    {
                        AssetDatabase.SaveAssetIfDirty(finalGo);
                        
                        var newGuid = AssetDatabase.GUIDFromAssetPath(assetPath);
                        if (newGuid != oldGuid)
                        {
                            StepOptimizePlatformRes.logger.InfoFormat("断开prefab链接成功，旧guid:{0} 新guid:{1}", oldGuid, newGuid);
                        }
                        else
                        {
                            StepOptimizePlatformRes.logger.InfoFormat("断开prefab链接成功，guid未变更:{0}", newGuid);
                        }
                
                        GameObject.DestroyImmediate(cloned);
                    }
                    else
                    {
                        ErrorFormat(packInputParams.context, "断开prefab链接失败:{0}", buildAssetPath);
                    }
                    
                    //重新写入
                    File.WriteAllText(assetPath + ".meta", oldContent);
                    //刷新下
                    AssetDatabase.Refresh();
                }
                else
                {
                    ErrorFormat(packInputParams.context, "当前资源不是prefab实例，无法断开链接:{0}", buildAssetPath);
                }
            }
            else
            {
                StepOptimizePlatformRes.logger.InfoFormat("当前资源不是目标prefab 类型，无法断开链接:{0} prefabType {1}",
                    buildAssetPath, prefabType);
            }
            
            return (int)EPlatformErrorCode.Suc;
        }
    }
}