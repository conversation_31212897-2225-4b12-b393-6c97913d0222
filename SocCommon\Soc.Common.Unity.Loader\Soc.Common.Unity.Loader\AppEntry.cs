using System;
using UnityEngine;
using UnityEngine.SceneManagement;
using WizardGames.Soc.Common.Download;
using WizardGames.Soc.Common.Unity.Loader.Logger;
using WizardGames.Soc.Common.Unity.Loader.CrashSight;
using WizardGames.Soc.Common.Unity.Loader.PerfSight;
using WizardGames.Soc.Common.Unity;
using WizardGames.Soc.SDK;
using WizardGames.Soc.SocClient.Data;
#if UNITY_IOS
using Unity.Advertisement.IosSupport;
#endif

namespace WizardGames.Soc.Common.Unity.Loader
{
    /// <summary>
    /// 应用程序主入口点，负责管理应用程序的生命周期和初始化
    /// 注意：非入口相关的代码请放在其他类中
    /// </summary>
    public class AppEntry : MonoBehaviour
    {
        private SocLogger Logger { get; set; } = LogHelper.GetLogger(typeof(AppEntry));
        public static AppEntry Instance;

        private void Awake()
        {
            Instance = this;
            
            InitLogging();
            InitSDKWrapper();
            InitGlobalSettings();
            InitLanguage();
        }
        
        private void InitLogging()
        {
            LogCollector.Init();

            //debug:1;warning:2;info:3;error\Fatal\ReleaseCritical:4
#if UNITY_EDITOR
            SocLogger.SetAllLogEnable(true);
#elif PUBLISH || POCO
            SocLogger.SetErrorLogEnableOnly(true);
#else
            SocLogger.SetAllLogEnable(true);
#endif
            Logger.ReleaseCriticalFormat("[OnFullSnapshotReceived]当前日志等级: {0}", SocLogger.LogLevel);
            Logger.ReleaseCritical("========AppEntry Awake========");
            VersionCodeUtils.PrintAppVersion();
            Logger.Info("==============================");
        }
        
        private void InitSDKWrapper()
        {
#if ENABLE_CRASHSIGHT && !UNITY_EDITOR
            CrashSightWrapper.InitCrashSight();
#endif
#if ENABLE_PERF_SIGHT
            PerfSightWrapper.InitPerfSight();
#endif

        }

        private void InitGlobalSettings()
        {
#if SOC_CLIENT && ENABLE_MALLOC_AUTOHOOK //内存工具使用
            SystemInfo.SetGCCurMemorySign(29998); // 29998 is EMallocSignExtra.ExtraGameLogicAwake
#endif
            
#if UNITY_IOS
            // iOS 14+ 的隐私设置
            var status = ATTrackingStatusBinding.GetAuthorizationTrackingStatus();
            Debug.Log($"ATTrackingStatusBinding status:{status}");
            if (status == ATTrackingStatusBinding.AuthorizationTrackingStatus.NOT_DETERMINED)
            {
                ATTrackingStatusBinding.RequestAuthorizationTracking();
            }
#endif
            //prepare阶段
            GlobalOptimizationParameters.DisablePrepareInFK();
            //关闭动画eval阶段的排序
            GlobalOptimizationParameters.EnableEvalJobSort();
#if FRAME_SEP
            GlobalOptimizationParameters.DisablePrepareFrameIdSeparation();
#endif

#if UNITY_EDITOR
            Animator.SetGlobalEnableStaticBindings(false);
#else
            Animator.SetGlobalEnableStaticBindings(true);
#endif
        }

        private void InitLanguage()
        {
            // 用本地资源初始化多语言
            var curSetLan = LanguageManager.GetSettingLan();
            LanguageManager.Instance.Init(curSetLan, false);
            FairyGUI.GTextTranslator.SetTranslator(LanguageManager.Instance.GetText, null);
        }
        
        private void OnDestroy()
        {
            Logger.ReleaseCritical("[AppEntry]OnDestroy...");
            Instance = null;
        }

        public void OnApplicationQuit()
        {
            // 退出应用程序
            Logger.ReleaseCritical("[AppEntry]Application is quitting.");
        }

        public void OnApplicationPause(bool isPaused)
        {
            Logger.ReleaseCritical("[AppEntry]Application is paused: " + isPaused);
        }

        public static void RestartApp()
        {
#if UNITY_ANDROID
            AndroidJavaClass unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
            AndroidJavaObject currentActivity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");
            AndroidJavaObject pm = currentActivity.Call<AndroidJavaObject>("getPackageManager");
            AndroidJavaObject intent = pm.Call<AndroidJavaObject>("getLaunchIntentForPackage", currentActivity.Call<string>("getPackageName"));
            intent.Call<AndroidJavaObject>("addFlags", 0x20000000); //Intent.FLAG_ACTIVITY_SINGLE_TOP
            currentActivity.Call("startActivity", intent);
            currentActivity.Call("finish");
            System.Diagnostics.Process.GetCurrentProcess().Kill();
#elif UNITY_IOS
            // Terminate the application
            UnityEngine.iOS.Device.RequestStoreReview(); // This is a workaround to exit the app
            Application.Quit();
    
            // Relaunch the application
            var url = Application.absoluteURL;
            Application.OpenURL(url);
#endif
        }
    }
} 