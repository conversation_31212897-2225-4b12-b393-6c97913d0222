using System;
using WizardGames.Soc.SocWorld.Framework.Hotfix;
using WizardGames.Soc.Common.Utility;
using System.Collections.Generic;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Config;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocWorld;

namespace WizardGames.Soc.Common.Entity
{
    public partial class ReputationUtil
    {
        public static readonly  BasicPropertyChangeCallback<ServerInstanceEntity, int> OnStoryStageChangeHotfix = new(OnStoryStageChange, 39584720);


        public static void RegisterHotfix()
        {
            HotfixDelegateHelper.Register(39584720, new Action<ServerInstanceEntity, int, int>(OnStoryStageChange));



        }
    }
}
