﻿using System.Collections.Generic;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.RuleGraph;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.Event;
using WizardGames.Soc.SocWorld.RuleGraph;
using WizardGames.Soc.SocWorld.RuleGraph.Function;

namespace WizardGames.Soc.Common.Component
{
    [HotfixClass]
    public static partial class RuleGraphComponentHotfix
    {
        [Hotfix]
        public static void Init(this RuleGraphComponent self)
        {
            self.ContextSync = new GraphContextSync();
            self.GraphContext = new GraphContext(GraphUtils.GenId(self.GetType().Name));
            self.FlowContexts = new CustomValueDictionary<long, FlowContext>();

            // TODO 开始执行graph
            self.graph = MgrGraph.Instance.StartGraph(self, self.GraphName, self.GraphContext);
            if (self.graph != null)
            {
                self.Logger.Info($"[RuleGraph] Graph({self.GraphName}) launch success");
                
                foreach (VariableCfg v in self.GraphContext.GraphCfg.VarList)
                {
                    v.PutIntoContext(self.GraphContext);
                }
            }
            else
            {
                self.Logger.Warn($"[RuleGraph] Graph({self.GraphName}) launch failed");
            }
        }

        [Hotfix]
        public static void InitFromDb(this RuleGraphComponent self)
        {
            // 从数据库里恢复
            //开始执行graph
            self.graph = MgrGraph.Instance.StartGraph(self, self.GraphName, self.GraphContext);
            if (self.graph != null)
            {
                self.Logger.Info($"[RuleGraph] InitFromDb Graph({self.GraphName}) launch success");
                
                foreach (KeyValuePair<long, FlowContext> kv in self.FlowContexts)
                {
                    kv.Value.InitFromDb(self.GraphContext);
                }
            }
            else
            {
                self.Logger.Warn($"[RuleGraph] InitFromDb Graph({self.GraphName}) launch failed");
            }
        }

        [Hotfix]
        public static void Cleanup(this RuleGraphComponent self)
        {
            MgrGraph.Instance.StopGraph(self.ParentEntity);

            self.FlowContexts = null;
            self.GraphContext = null;
            self.graph = null;
            self.ContextSync = null;
        }

        public static void AddFlowContext(this RuleGraphComponent self, FlowContext context)
        {
            if (!self.FlowContexts.ContainsKey(context.Id))
            {
                self.FlowContexts.Add(context.Id, context);
            }
        }

        public static void RemoveFlowContext(this RuleGraphComponent self, FlowContext context)
        {
            self.FlowContexts.Remove(context.Id);
        }

        public static FlowContext GetFlowContext(this RuleGraphComponent self, long id)
        {
            if (self.FlowContexts.TryGetValue(id, out FlowContext pick))
            {
                return pick;
            }
            return default;
        }
        
        [RpcHandler(ExposeToClient = true)]
        public static void SendCustomEventGlobalWithRoleId(this RuleGraphComponent self, string eventName, ulong roleId, List<CustomEventParamCfg> eventParams)
        {
            PlayerEntity playerEntity = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (playerEntity != null)
            {
                CustomEventLibrary.SendCustomEventGlobal(eventName, playerEntity.EntityId, (long)roleId, eventParams.ToCustomType<CustomEventParamCfg, CustomEventParam>());   
            }
            else
            {
                self.Logger.Info($"[RuleGraph] Can not find player entity. (roleId = {roleId})");
            }
        }
    }
}