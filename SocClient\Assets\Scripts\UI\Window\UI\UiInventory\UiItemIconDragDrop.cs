using FairyGUI;
using System;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.Common.Data.resource;
using UnityEngine;

namespace WizardGames.Soc.SocClient.Ui
{
    public enum EIconDragDir
    {
        None,
        Up,
        Down,
        Left,
        Right
    }

    /// <summary>
    /// 物品图标拖拽信息
    /// </summary>
    public class ItemDragInfo
    {
        /// <summary>
        /// 拖拽的物品实例
        /// </summary>
        public BaseItemNode item;

        /// <summary>
        /// 拖拽的资源物品
        /// </summary>
        public OBJGenaral resItem;

        /// <summary>
        /// 自定义拖拽数据
        /// </summary>
        public object customData = null;

        /// <summary>
        /// 拖拽方向指示
        /// </summary>
        public EIconDragDir dir = EIconDragDir.None;
    }

    /// <summary>
    /// 物品图标通用拖拽逻辑
    /// </summary>
    public partial class UiItemIconDragDrop : WindowComBase
    {
        /// <summary>
        /// 开始拖拽时，图标的透明度
        /// </summary>
        private const float START_DRAG_ALPHA = 0.3f;

        /// <summary>
        /// 界面根节点
        /// </summary>
        private GComponent comRoot;

        /// <summary>
        /// 当前拖拽的图标
        /// </summary>
        private ComAutoIcon dragIcon;

        /// <summary>
        /// 当前拖拽的组件
        /// </summary>
        private GComponent dragCom;

        /// <summary>
        /// 方向指示控制器
        /// </summary>
        private Controller dirCtrl;

        /// <summary>
        /// 发起拖拽的图标
        /// </summary>
        private ComBaseIcon dragFom;

        /// <summary>
        /// 开始拖拽时的透明度
        /// </summary>
        private float dragFromStartAlpha;

        /// <summary>
        /// 拖拽附加的数据
        /// </summary>
        private ItemDragInfo dragData;

        /// <summary>
        /// OnEnable的时候检查是否需要发起拖拽行为
        /// </summary>
        private bool needDoDragOnEnable = false;

        /// <summary>
        /// 拖动没有对象接收时的处理
        /// </summary>
        private System.Action<object> onNoReceiverCall = null;

        /// <summary>
        /// 拖拽结束或取消时执行的回调
        /// </summary>
        private System.Action onDragEndCall = null;

        /// <summary>
        /// 当前拖拽的 touchid
        /// </summary>
        private int iCurTouchID = -1;

        /// <summary>
        /// 是否正在拖动
        /// </summary>
        public static bool IsDragging { get; private set; } = false;

        /// <summary>
        /// 当前拖拽的物品图标
        /// </summary>
        public static ComBaseIcon CurDragIcon { get; private set; } = null;

        /// <summary>
        /// 当拖拽结束时, 执行这个操作, 而不是抛出拖拽事件
        /// </summary>
        public static Action<ItemDragInfo> OnDragReceive = null;

        /// <summary>
        /// 拖拽过程中，调用这个操作, 传入当前拖拽的位置
        /// </summary>
        public static Action<float, float> OnDraggingIcon = null;

        /// <summary>
        /// 使用拖拽源的图标大小
        /// </summary>
        public static bool UseFromIconSize = false;

        protected override void OnInit()
        {
            base.OnInit();

            comRoot = ContentPane.GetChild("root").asCom;
            dragCom = comRoot.GetChild("dragIcon").asCom;
            dirCtrl = dragCom.GetController("direction");
            SetDirSign(EIconDragDir.None);
            dragIcon = dragCom.GetChild("icon") as ComAutoIcon;
            dragCom.visible = false;
            dragCom.touchable = false; //important
            dragCom.draggable = true;
            dragCom.onDragMove.Add(OnDragMove);
            dragCom.onDragEnd.Add(OnDragEnd);

            RegisterEvent<BaseItemNode>(EventDefine.RemoveItemNode, OnRemoveItemNode);
        }

        private void SetDirSign(EIconDragDir dir = EIconDragDir.None)
        {
            if (null == dirCtrl) return;
            dirCtrl.SetSelectedPage(dir.ToString());
        }

        private void HideDragIcon()
        {
            dragIcon.AsBaseIcon?.SetEmpty();
            dragCom.visible = false;
        }

        protected override void OnEnable()
        {
            base.OnEnable();

            if (!needDoDragOnEnable) return;
            needDoDragOnEnable = false;
            if (null != dragFom) DoStartDrag(dragFom, dragData, onNoReceiverCall, onDragEndCall, iCurTouchID);
        }

        protected override void OnDisable()
        {
            base.OnDisable();

            IsDragging = false;
            CurDragIcon = null;
            OnDragReceive = null;
            OnDraggingIcon = null;
            UseFromIconSize = false;
        }

        /// <summary>
        /// 物品节点移除回调
        /// </summary>
        private void OnRemoveItemNode(BaseItemNode itemNode)
        {
            var curDragIcon = dragIcon.AsBaseIcon;
            if (null == itemNode || null == curDragIcon || curDragIcon is not ComItemIcon itemIcon) return;
            // 如果移除的物品就是当前拖拽的物品, 则取消拖拽
            if (null != itemIcon.InstItem && itemIcon.InstItem.Id == itemNode.Id)
            {
                DoCancelDrag();
            }
        }

        /// <summary>
        /// 触发图标拖拽事件
        /// </summary>
        protected void FireDragEvent(bool dragStart, bool isDragValid)
        {
            if (null == dragData || null == dragData.item)  return;
            if (dragStart) Mc.Msg.FireMsg(EventDefine.UiItemDragStart, dragData.item);
            else Mc.Msg.FireMsg(EventDefine.UiItemDragEnd, dragData.item, isDragValid);
        }

        /// <summary>
        /// 开始拖动
        /// </summary>
        /// <param name="from">发起拖拽的图标</param>
        /// <param name="data">拖拽附加数据</param>
        /// <param name="onNoReceiver">没有接收者时的处理函数</param>
        /// <param name="touchPointID">指定touchPointID</param>
        public void DoStartDrag(ComBaseIcon from, ItemDragInfo data, System.Action<object> onNoReceiver = null, System.Action onDragEnd = null, int touchPointID = -1)
        {
            // 快速掠夺时不允许拖动图标操作
            if (null != Mc.QuickLoot && Mc.QuickLoot.IsQuickAction) return;
            if (null == from || null == data) return;
            // 如果当前正在拖拽中，则需要结束上一个拖拽
            if (IsDragging)
            {
                IsDragging = false;
                DoCancelDrag(false);
            }
            CurDragIcon = from;
            iCurTouchID = touchPointID;
            // 发起拖拽的图标基于一个透明度的变化
            dragFromStartAlpha = from.alpha;
            from.SetAlpha(START_DRAG_ALPHA);
            dragFom = from;
            dragData = data;
            onNoReceiverCall = onNoReceiver;
            onDragEndCall = onDragEnd;
            needDoDragOnEnable = false;
            if (null == dragIcon || !IsActive)
            {
                needDoDragOnEnable = true;
                return;
            }
            ComBaseIcon curDrag = dragIcon.SwithToSameTypeWith(from);
            curDrag.CopyFromIcon(from);
            curDrag.SetShowIconOnly(true);
            curDrag.BacAlpha = 0.6f;
            dragCom.xy = comRoot.GlobalToLocal(Stage.inst.GetTouchPosition(touchPointID));
            dragCom.visible = true;
            SetDragIconSize(from);
            dragCom.StartDrag(touchPointID);
            SetDirSign(data.dir);
            IsDragging = true;
            // 触发事件
            FireDragEvent(true, false);
        }

        /// <summary>
        /// 取消拖动
        /// </summary>
        public void DoCancelDrag(bool hideAfterCancel = true)
        {
            if (!dragCom.visible) return;

            // 触发事件
            FireDragEvent(false, false);
            CurDragIcon?.SetAlpha(dragFromStartAlpha);
            iCurTouchID = -1;
            dragFom.SetAlpha(dragFromStartAlpha);
            dragFom = null;
            dragCom.StopDrag();
            HideDragIcon();
            dragData = null;
            onNoReceiverCall = null;
            IsDragging = false;
            CurDragIcon = null;
            OnDragReceive = null;

            onDragEndCall?.Invoke();
            onDragEndCall = null;
            UseFromIconSize = false;
            OnDraggingIcon = null;

            SetDirSign(EIconDragDir.None);
            if (hideAfterCancel) HideSelf();
        }

        /// <summary>
        /// 拖拽中
        /// </summary>
        /// <param name="evt"></param>
        private void OnDragMove(EventContext evt)
        {
            if (!IsDragging) return;
            // 主要为了防止Editor上产生规则外的拖拽，当图标拖出屏幕时取消拖拽
            float curPosX = dragCom.x;
            float curPosY = dragCom.y;
            if (curPosX < 0 || curPosY < 0 || curPosX > comRoot.width || curPosY > comRoot.height)
            {
                DoCancelDrag();
            }
            else
            {
                // 回调处理都在Stage坐标空间，而这里的坐标是在comRoot下，
                // 不过DragMove的时候一直调用FGUI的坐标换算太费了
                // 这里简单认为坐标差异只有root的缩放有影响，简化坐标换算
                float stageX = comRoot.x + curPosX * comRoot.scaleX;
                float stageY = comRoot.y + curPosY * comRoot.scaleY;
                OnDraggingIcon?.Invoke(stageX, stageY);
            }
        }

        /// <summary>
        /// 执行正常的拖拽逻辑
        /// </summary>
        private bool DoNormalDragEnd()
        {
            bool hasReceiver = false;
            GObject target = GRoot.inst.touchTarget;
            while (target != null)
            {
                if (target.hasEventListeners("onItemDrop"))
                {
                    hasReceiver = true;
                    target.RequestFocus();
                    target.DispatchEvent("onItemDrop", dragData, this);
                    break;
                }
                target = target.parent;
            }
            if (!hasReceiver)
            {
                if (null != onNoReceiverCall) onNoReceiverCall.Invoke(dragData);
                else if (null != dragFom) dragFom.SetAlpha(dragFromStartAlpha);
            }
            CurDragIcon?.SetAlpha(dragFromStartAlpha);
            return hasReceiver;
        }

        /// <summary>
        /// 拖动结束
        /// </summary>
        private void OnDragEnd(EventContext evt)
        {
            if (!dragCom.visible) return;
            if (iCurTouchID != -1 && evt.inputEvent.touchId != iCurTouchID) return;

            bool hasReceiver = true;
            if (null != OnDragReceive)
            {
                OnDragReceive(dragData);
                OnDragReceive = null;
                if (null != dragFom) dragFom.SetAlpha(dragFromStartAlpha);
            }
            else
            {
                hasReceiver = DoNormalDragEnd();
            }
            CurDragIcon?.SetAlpha(dragFromStartAlpha);
            onDragEndCall?.Invoke();
            onDragEndCall = null;
            OnDraggingIcon = null;
            UseFromIconSize = false;

            // 触发事件
            if (null != dragFom) FireDragEvent(false, hasReceiver || null != onNoReceiverCall);

            iCurTouchID = -1;
            HideDragIcon();
            dragData = null;
            dragFom = null;
            IsDragging = false;
            CurDragIcon = null;

            SetDirSign(EIconDragDir.None);
            HideSelf();
        }

        /// <summary>
        /// 开始拖动
        /// </summary>
        /// <param name="from">发起拖拽的图标</param>
        /// <param name="data">拖拽附加数据</param>
        /// <param name="touchPointID">指定touchPointID</param>
        public static void StartItemDrag(ComBaseIcon from, ItemDragInfo data, System.Action<object> onNoReceiver = null, System.Action onDragEnd = null, int touchPointID = -1)
        {
            var win = Mc.Ui.OpenWindowT<UiItemIconDragDrop>("UiItemIconDragDrop");
            if (null == win) return;
            win.DoStartDrag(from, data, onNoReceiver, onDragEnd, touchPointID);
        }

        /// <summary>
        /// 取消拖动
        /// </summary>
        public static void CancelItemDrag()
        {
            var win = Mc.Ui.GetWindowT<UiItemIconDragDrop>("UiItemIconDragDrop");
            if (null == win || !win.IsActive) return;
            win.DoCancelDrag();
        }

        /// <summary>
        /// 设置拖拽方向指示
        /// </summary>
        public static void SetDragIconDir(EIconDragDir dir = EIconDragDir.None)
        {
            var win = Mc.Ui.GetWindowT<UiItemIconDragDrop>("UiItemIconDragDrop");
            if (null == win || !win.IsActive) return;
            win.SetDirSign(dir);
        }

		/// <summary>
		/// 产生一个安全的拖拽回调
		/// </summary>
		public static EventCallback1 SafeDragCallback(System.Func<EventContext, bool> callback)
		{
			return ctx =>
			{
				if (!callback(ctx)) CancelItemDrag();
			};
		}

        /// <summary>
        /// 编辑拖动中的icon显示属性
        /// </summary>
        public void SetValueOnDragStart(string value)
        {
            dragIcon.AsBaseIcon?.SetValue(value);
        }
        
        /// <summary>
        /// 编辑拖动中的icon显示属性
        /// </summary>
        public void SetValueOnDragEnd()
        {
            dragIcon.AsBaseIcon?.SetValue(null);
            dragIcon.AsBaseIcon?.SetShowIconOnly();
        }
    }
}