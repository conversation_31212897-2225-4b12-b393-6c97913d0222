using Cysharp.Text;
using FairyGUI;
using System;
using System.Collections.Generic;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Const;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data.DataItem;
using WizardGames.Soc.Common.Data.resource;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.Syncronization;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Main;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocClient.Audio;
using WizardGames.Soc.SocClient.ClientItem;
using WizardGames.Soc.SocClient.Collection;
using WizardGames.Soc.SocClient.Collection.Common;
using WizardGames.Soc.SocClient.Construction;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Systems;
using WizardGames.Soc.SocClient.Talent;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.SocClient.Ui
{
    public partial class UiItemTipsBoardBtns : IItemTipsBoard
    {
        private class TipsBtnInfo
        {
            // 读表的部分
            public ItemTIPSButtonManagement Cfg { get; private set; } = null;
            public string iconURL => Cfg?.Icon ?? null;
            public string name => Cfg?.Name ?? null;
            public string soundEvent => Cfg?.Sound ?? null;
            public int order => Cfg?.Order ?? 0;
            // 手动注册的部分

            //在GList中的位置
            public int glistIndex;
            //在全部中的位置（对应ItemTipsBtnConst枚举）
            public int allBtnIndex;
            public string nameDir = null;
            public Func<BaseItemNode, bool> onClickInst;
            public Func<ItemConfig, bool> onClickConfig;
            public Func<BaseItemNode, bool> stateCond;
            public TipsBtnInfo(int index)
            {
                Cfg = Mc.Tables.TbItemTIPSButtonManagement.GetOrDefault(index);
            }
        }

        public static SocLogger logger = LogHelper.GetLogger(typeof(UiItemTipsBoardBtns));

        private GList listBtns;
        private Controller listController;
        private BaseItemNode curInstItem = null;
        private ItemConfig curItemConfig = null;
        private bool isSubTips = false;
        private List<TipsBtnInfo> curBtns;
        private Dictionary<int, TipsBtnInfo> btnMap = new();

        private List<TipsBtnInfo> allBtns = new()
        {
            new(ItemTipsBtnConst.SpillWater)
            {
                onClickInst = OnClickDropWater,
                stateCond = CondWaterBottleBtn,
            },
            new(ItemTipsBtnConst.Eat)
            {
                onClickInst = OnClickNormalUse,
            },
            new(ItemTipsBtnConst.Drink)
            {
                onClickInst = OnClickHoldModelUse,
                stateCond = CondWaterBottleBtn,
            },
            new(ItemTipsBtnConst.Learning)
            {
                onClickInst = OnClickItemLearnBlueprint,
            },
            new(ItemTipsBtnConst.Treat)
            {
                onClickInst = OnClickNormalUse,
            },
            new(ItemTipsBtnConst.Equip)
            {
                onClickInst = OnClickItemUseEquip,
            },
            new(ItemTipsBtnConst.Use)
            {
                onClickInst = OnClickNormalUse,
            },
            new(ItemTipsBtnConst.UseHoldModel)
            {
                onClickInst = OnClickHoldModelUse,
            },
            new(ItemTipsBtnConst.UseDeploy)
            {
                onClickInst = OnClickDeployUse,
            },
            new(ItemTipsBtnConst.UseCampingTent)
            {
                onClickConfig = OnClickCampTent,
            },
            new(ItemTipsBtnConst.Recharge)
            {
                onClickInst = OnClickRecharge,
                stateCond = CondRecharge,
            },
            new(ItemTipsBtnConst.UnloadAmmo)
            {
                onClickInst = OnClickUnloadAmmo,
            },
            new(ItemTipsBtnConst.UnloadPart)
            {
                onClickInst = OnClickUnloadPart,
            },
            new(ItemTipsBtnConst.TakeOff)
            {
                onClickInst = OnClickClothTakeOff,
            },
            new(ItemTipsBtnConst.Split)
            {
                onClickInst = OnClickItemSplit,
            },
            new(ItemTipsBtnConst.Drop)
            {
                onClickInst = OnClickItemDrop,
            },
            new(ItemTipsBtnConst.PartDrop)
            {
                onClickInst = OnClickItemPartDrop,
            },
            new(ItemTipsBtnConst.MoveToInventory)
            {
                onClickInst = OnClickMoveToBackpack,
                onClickConfig = OnClickItemMoveToInventory,
            },
            new(ItemTipsBtnConst.MoveToBox)
            {
                onClickInst = OnClickMoveToOtherside,
            },
            new(ItemTipsBtnConst.EditIC)
            {
                onClickInst = OnClickEditIC
            },
            new(ItemTipsBtnConst.Seed)
            {
                onClickInst = OnClickSeed
            },
            new(ItemTipsBtnConst.EquipWeapon)
            {
                onClickInst = OnClickEquipBelt,
            },
            new(ItemTipsBtnConst.EquipBelt)
            {
                onClickInst = OnClickEquipBelt,
            },
            new(ItemTipsBtnConst.UnloadWeapon)
            {
                onClickInst = OnClickUnloadBelt,
            },
            new(ItemTipsBtnConst.UnloadBelt)
            {
                onClickInst = OnClickUnloadBelt,
            },
            new(ItemTipsBtnConst.MoveToSafeBox)
            {
                onClickInst = OnClickMoveToSafeBox,
            },
            new (ItemTipsBtnConst.MoveToInventorySome)
            {
                onClickConfig = OnClickItemMoveToInventorySome,
            },
            new (ItemTipsBtnConst.MoveToBoxSome)
            {
                onClickConfig = OnClickConfigMoveToBoxSome,
            },
            new (ItemTipsBtnConst.MoveToCabinet)
            {
                onClickConfig=OnClickMovetoCabinet,
            },
            new (ItemTipsBtnConst.AllMoveToInventory)
            {
                onClickConfig=OnClickAllMoveToInventory,
            },
            new (ItemTipsBtnConst.AllInput)
            {
                onClickConfig=OnClickAllInPut,
            },
            new (ItemTipsBtnConst.Detail)
            {
                onClickConfig = OnClickDetail,
            },
            new (ItemTipsBtnConst.Skin)
            {
                onClickInst = OnClicSkin,
            },
        };

        public GComponent ComBoard => listBtns;

        public void Bind(UiItemTipsMain tipsMain, bool isSubTips)
        {
            this.isSubTips = isSubTips;
            listController = tipsMain.movableRoot.GetController("platform");
            BindItemTipsMain(tipsMain);
            SetRootController(tipsMain);
            listBtns.itemRenderer = BtnRenderer;
            btnMap.Clear();
            foreach (var info in allBtns)
            {
                btnMap[info.Cfg.Id] = info;
            }
        }

        public virtual void GetRightMenuList(BaseItemNode item, List<RightButtonItemData> btns)
        {
            if (btnMap.Count == 0)
            {
                foreach (var info in allBtns)
                {
                    btnMap[info.Cfg.Id] = info;
                }
            }

            curInstItem = item;
            curItemConfig = item?.ItemConfig;

            if (null == curBtns) curBtns = new();
            else curBtns.Clear();
            if (item != null) SetInstBtns(item);
            curBtns.Sort(CompareBtns);

            for (int i = 0; i < curBtns.Count; i++)
            {
                var info = curBtns[i];
                btns.Add(new RightButtonItemData(info.name, info.iconURL, () =>
                {
                    if (info.onClickInst != null && curInstItem != null) info.onClickInst.Invoke(curInstItem);
                    else if (info.onClickConfig != null) info.onClickConfig.Invoke(curItemConfig);
                }));
            }
        }

        private TipsBtnInfo AddBtn(int index)
        {
            if (null == curBtns) curBtns = new();
            if (!btnMap.ContainsKey(index)) return null;
            var info = btnMap[index];
            curBtns.Add(info);
            info.glistIndex = curBtns.Count - 1;
            info.allBtnIndex = index;
            return info;
        }

        public void SetItem(BaseItemNode item, ItemConfig tbItem)
        {
            curInstItem = item;
            curItemConfig = tbItem;

            if (null == curBtns) curBtns = new();
            else
            {
                ////重置数据
                //for (int i = 0; i < listBtns.numItems; i++)
                //{
                //    listBtns.GetChildAt(i).enabled = true;
                //}
                curBtns.Clear();
            }
            if (item != null)
            {
                SetInstBtns(item);
            }
            else
            {
                AddDetailBtn();
            } 
            if (tbItem != null) SetCfgBtns(tbItem);
            curBtns.Sort(CompareBtns);
            listBtns.numItems = curBtns.Count;
            listBtns.ResizeToFit(curBtns.Count);
            listBtns.visible = curBtns.Count > 0;
        }

        public void SetItem(OBJGenaral item)
        {
            listBtns.visible = false;
        }

        public void SetItem(TalentNode item)
        {
            listBtns.visible = false;
        }

        private int CompareBtns(TipsBtnInfo a, TipsBtnInfo b)
        {
            if (a.order == b.order) return (a.Cfg?.Id ?? 0) - (b.Cfg?.Id ?? 0);
            return a.order - b.order;
        }

        /// <summary>
        /// 设置动态物品图标
        /// </summary>
        /// <param name="item"></param>
        private void SetInstBtns(BaseItemNode item)
        {
            AddSpecialBtns(item.Config);
            // 在 研究台 ,修理台 背包容器里
            bool isInCustomInventory = Mc.Ui.IsWindowOpen("UiResearch") || Mc.Ui.IsWindowOpen("UiRepair");//研究台 ,修理台

            bool hasParentContainer = null != item.ParentNode;
            bool inMain = false, inWear = false, inBelt = false, inInventory = false, inMyInventory = false;
            bool isMyItem = item.ColId == Mc.MyPlayer.MyEntityServer.EntityId;
            if (hasParentContainer)
            {
                inMain = item.ParentContainer.NodeId == NodeConst.MainItemContainerNodeId;
                inWear = item.ParentContainer.NodeId == NodeConst.WearItemContainerNodeId;
                inBelt = item.ParentContainer.NodeId == NodeConst.BeltItemContainerNodeId;
                inInventory = inMain || inWear || inBelt;
                inMyInventory = isMyItem && inInventory;
            }
            var mainType = item.Config.Manufacturing;
            var subType = item.Config.SecondaryClassification;
            bool isBp = mainType == ItemType.Blueprint || mainType == ItemType.BlueprintItem;
            var isBanMove = item.BanMoveOP;

            // 研究台 ,修理台 背包容器里 只显示丢弃
            if (inMyInventory && isInCustomInventory)
            {
                if (!isSubTips && hasParentContainer && !isBanMove) AddBtn(ItemTipsBtnConst.Drop);
                if (!isSubTips && hasParentContainer && item.Amount > 1) AddBtn(ItemTipsBtnConst.PartDrop);
                return;
            }

            if (UiSubToolcupBoard.SubpageNow != null)
            {
                AddBtn(ItemTipsBtnConst.MoveToInventory);
                if (item.Amount > 0) { AddBtn(ItemTipsBtnConst.MoveToInventorySome); }
                return;
            }
            if (isBp) AddBtn(ItemTipsBtnConst.Learning);   // 蓝图
            //if ((inMain || inBelt) && curInstItem.BizId == ItemConst.ElectricBlueprintItemId) { AddBtn(ItemTipsBtnConst.EditIC); } // 电力集成电路
            if (!inWear && item.HasFlag(ItemFlags.CanEquipped) && !isBanMove) AddBtn(ItemTipsBtnConst.Equip);   // 服饰
            if (!isBp && item.HasFlag(ItemFlags.Consumable)) AddBtn(ItemTipsBtnConst.Use);
            // "可食用"标签下需要对某些类型作特殊处理
            bool isEatable = item.HasFlag(ItemFlags.Eatable);
            if (isEatable)
            {
                // 医疗物品, 策划指定为消耗品大类下的小类6
                if (mainType == ItemType.Expendable && subType == 6) AddBtn(ItemTipsBtnConst.Treat);
                // 其他都显示食用
                else AddBtn(ItemTipsBtnConst.Eat);
            }

            // 对于在主背包的手持物品与摆件物品, 显示使用
            if (inMyInventory)
            {
                // 手持物品的使用
                if (!isEatable && item.Config.OperationType == ItemOperationType.SwitchModel && mainType == ItemType.Expendable)
                {
                    // 水瓶
                    if (curInstItem is WaterBottleItemNode)
                    {
                        AddBtn(ItemTipsBtnConst.Drink);
                        AddBtn(ItemTipsBtnConst.SpillWater);
                    }
                    // 其他使用
                    else
                    {
                        AddBtn(ItemTipsBtnConst.UseHoldModel);
                    }
                }

                // 摆件物品的使用
                if (item.Config.OperationType == ItemOperationType.SwitchConstruction)
                {
                    AddBtn(ItemTipsBtnConst.UseDeploy);
                }
            }

            // 装备在快捷栏的物品
            if (isMyItem && inBelt && !isBanMove)
            {
                // 能装备到武器栏的物品，显示卸下武器
                if (Mc.CollectionItem.CanPutInfoWeaponSlot(item)) AddBtn(ItemTipsBtnConst.UnloadWeapon);
                // 其他，显示存入背包
                else AddBtn(ItemTipsBtnConst.UnloadBelt);
            }

            // 在背包内可装备到快捷栏的物品
            if (isMyItem && inMain && !isBanMove)
            {
                var containerBelt = Mc.CollectionItem.InventoryCom.ContainerBeltNode;
                // 如果这个物品能够装备到武器栏，且武器栏还有空位，则显示装备武器
                if (Mc.CollectionItem.CanPutInfoWeaponSlot(item))
                {
                    for (int i = (int)HoldItemIndex.Item7; i <= (int)HoldItemIndex.Item8; ++i)
                    {
                        if (null != containerBelt.GetChildNode(i)) continue;
                        AddBtn(ItemTipsBtnConst.EquipWeapon);
                        break;
                    }
                }
                // 如果这个物品能够装备到快捷栏, 且快捷栏还有空位, 则显示放入快捷栏
                else
                {
                    for (int i = (int)HoldItemIndex.Item1; i <= (int)HoldItemIndex.Item6; ++i)
                    {
                        if (null != containerBelt.GetChildNode(i)) continue;
                        if (containerBelt.Impl.CanBeltAcceptItem(item, i) != CanAcceptResult.CanAccept) continue;
                        AddBtn(ItemTipsBtnConst.EquipBelt);
                        break;
                    }
                }
            }

            bool isInOtherside = Mc.Ui.IsWindowOpen("UiInventoryOtherside");
            if (!isInOtherside && mainType == ItemType.Expendable && subType == 7)
            {
                AddBtn(ItemTipsBtnConst.Seed);
            }

            if (!isInOtherside && Mc.Ui.IsWindowOpen("UiTerritoryCenter"))
            {
                isInOtherside = true;
            }


            if (!isInOtherside && isInCustomInventory)
            {
                isInOtherside = true;
            }

            bool isInManger = Mc.Ui.IsWindowOpen("UiManger");// 拴马桩
            if (!isInOtherside && isInManger)
            {
                isInOtherside = true;
            }

            // 充能
            if (item.HasFlag(ItemFlags.Rechargeable)) AddBtn(ItemTipsBtnConst.Recharge);

            bool unloadBulletVisible = false;
            if (hasParentContainer)
            {
                Mc.CollectionItem.GetWeaponBulletInfo(item, false, out int bulletItemId, out int bulletAmount);
                unloadBulletVisible = bulletItemId > 0 && bulletAmount > 0;
            }
            #region 临时代码 炮台屏蔽卸子弹按钮
            var itemParentNode = item.ParentNode;
            if (null != itemParentNode && itemParentNode.Impl.ConfigId == 10020)
            {
                unloadBulletVisible = false;
            }
            #endregion
            if (unloadBulletVisible) AddBtn(ItemTipsBtnConst.UnloadAmmo);
            if (isSubTips && hasParentContainer)
            {
                if (curInstItem.ConfigId == ItemConst.PureWaterItemId || curInstItem.ConfigId == ItemConst.SaltWaterItemId) AddBtn(ItemTipsBtnConst.SpillWater);
                //else AddBtn(ItemTipsBtnConst.UnloadPart);
            }
            if (inWear && !isBanMove) AddBtn(ItemTipsBtnConst.TakeOff);

            if (!isSubTips && item.Config.Stack > 1 && item.Amount > 1 && !isInCustomInventory) AddBtn(ItemTipsBtnConst.Split);
            if (!isSubTips && hasParentContainer && !isBanMove) AddBtn(ItemTipsBtnConst.Drop);
            if (!isSubTips && hasParentContainer && item.Amount > 1) AddBtn(ItemTipsBtnConst.PartDrop);
            // 异端容器界面下的按钮
            if (isInOtherside && !isBanMove)
            {
                if (!inMyInventory) AddBtn(ItemTipsBtnConst.MoveToInventory);
                if (inInventory && Mc.LootCollection.IsCurLootCanPutItemsIn() && Mc.MyPlayer.MyRootNode?.CurShowLootingSysIndex == -1 && !isInCustomInventory)
                {
                    var info = AddBtn(ItemTipsBtnConst.MoveToBox);
                }
            }
            if (isInOtherside && item.Config.Manufacturing == ItemType.Expendable && item.Config.SecondaryClassification == 7)
            {
                curBtns.Clear();
            }
        }
        //检查是否是情报道具,并添加对应按钮
        private void CheckAddInfoItem()
        {
            if (curItemConfig != null && Mc.Reputation.isOpenCabinetWnd)
            {
                var inventoryComponent = Mc.MyPlayer.MyEntityServer.InventoryComponent;
                int amount = inventoryComponent.GetAmount(curItemConfig.Id);
                for (int i = 0; i < Mc.Tables.TbReputationConvertConfig.DataList.Count; i++)
                {
                    if (Mc.Tables.TbReputationConvertConfig.DataList[i].Id == curItemConfig.Id)
                    {
                        AddBtn(ItemTipsBtnConst.MoveToCabinet);
                        AddBtn(ItemTipsBtnConst.AllMoveToInventory);
                    }
                }
                if (Mc.Tables.TbReputationConvertConfig.DataList[0].ConverOutputId == curItemConfig.Id)
                {
                    AddBtn(ItemTipsBtnConst.AllInput);
                }
            }
        }
        /// <summary>
        /// 设置静态物品图标
        /// </summary>
        private void SetCfgBtns(ItemConfig item)
        {
            int mainType = item.Manufacturing;
            // 帐篷
            if (item.Id == ItemConst.CampingTent)
            {
                AddBtn(ItemTipsBtnConst.UseCampingTent);
            }
            CheckAddInfoItem();
            if (UiSubToolcupBoard.SubpageNow != null)
            {
                var data = UiSubToolcupBoard.SubpageNow.GetData(curItemConfig.Id);
                if (data != null && data.needAmount > 0 && data.playerAmount > 0 && data.GetMaxCanMove() > 0)
                {
                    AddBtn(ItemTipsBtnConst.MoveToBoxSome);
                }
            }
        }

        private void BtnRenderer(int index, GObject item)
        {
            if (null == curBtns || index >= curBtns.Count) return;
            var btn = item.asButton;
            if (null == btn) return;
            var btnInfo = curBtns[index];

            string btnName = (btnInfo.Cfg?.Id ?? 0).ToString();
            btn.name = btnName;
            btn.gameObjectName = btnName;
            btn.icon = btnInfo.iconURL;
            btn.title = btnInfo.nameDir ?? btnInfo.name ?? string.Empty;
            //if (btnInfo.allBtnIndex == ItemTipsBtnConst.MoveToCabinet || btnInfo.allBtnIndex == ItemTipsBtnConst.AllInput)
            //{
            //    var inventoryComponent = Mc.MyPlayer.MyEntityServer.InventoryComponent;
            //    int amount = inventoryComponent.GetAmount(curItemConfig.Id);
            //    //btn.enabled = amount > 0;
            //}
            //else
            //{
            //btn.enabled = true;
            //}



            if ((null != btnInfo.stateCond) && !btnInfo.stateCond(curInstItem)) btn.grayed = true;
            else btn.grayed = false;

            btn.onClick.Set(ctx =>
            {
                bool hideTips = false;
                if (btnInfo.onClickInst != null && curInstItem != null)
                {
                    hideTips = btnInfo.onClickInst.Invoke(curInstItem);
                }
                else if (btnInfo.onClickConfig != null)
                {
                    hideTips = btnInfo.onClickConfig.Invoke(curItemConfig);
                }
                if (hideTips)
                {
                    UiItemTips.HideTips();
                    Mc.Msg.FireMsg(EventDefine.UiHideAllInventoryIcons);
                }
                if (!string.IsNullOrEmpty(btnInfo.soundEvent))
                {
                    Mc.Audio.Add2DAudioByConfig(btnInfo.soundEvent);
                }
                Mc.QuickLoot.ClearQuickMoveItem();
            });
#if POCO
            btn.PocoRegister(ZString.Format("TipsMainUseItem{0}",btnInfo.Cfg?.Id));
#endif
        }

        private static bool OnClickItemDrop(BaseItemNode curItem)
        {
            if (null == curItem) return false;
            Mc.CollectionItem.DropItem(Mc.MyPlayer.RoleId, curItem.NodeId, curItem.Amount);
            return true;
        }

        private static bool OnClickItemPartDrop(BaseItemNode curItem)
        {
            if (null == curItem) return false;
            UiItemSplit.ShowItemSplit(curItem, true, LanguageManager.GetTextConst(LanguageConst.SplitAbandon));
            return true;
        }

        private static bool OnClickItemSplit(BaseItemNode curItem)
        {
            if (null == curItem) return false;
            UiItemSplit.ShowItemSplit(curItem, false, LanguageManager.GetTextConst(LanguageConst.ItemUseSplit));
            return true;
        }

        private static bool OnClickItemMoveToInventorySome(ItemConfig ic)
        {
            if (null == ic) return false;
            UiSubToolcupBoard.SubpageNow?.OnClickItemMoveToInventorySome(ic.Id);
            return true;
        }


        private static bool OnClickConfigMoveToBoxSome(ItemConfig ic)
        {
            if (null == ic) return false;
            UiSubToolcupBoard.SubpageNow?.OnClickConfigMoveToBoxSome(ic.Id);
            return true;
        }

        private static bool OnClickItemMoveToInventory(ItemConfig ic)
        {
            if (null == ic) return false;
            UiSubToolcupBoard.SubpageNow?.OnClickItemMoveToInventory(ic.Id);
            return true;
        }

        private static bool OnClickNormalUse(BaseItemNode curItem)
        {
            if (curItem != null && curItem.ItemConfig != null && curItem.ItemConfig.Manufacturing == ItemType.Bundle)
            {
                var giftCfg = Mc.Tables.TbItemPackages.GetOrDefault(curItem.ItemConfig.Id);
                if (giftCfg == null || giftCfg.DropIDList.Length <= 0)
                {
                    logger.ErrorFormat("[UiItemTipsBoardBtns] [OnClickNormalUse] Invalid params: giftCfg null={0}, DropIDList Length={1}", giftCfg == null, giftCfg == null ? 0 : giftCfg.DropIDList.Length);
                    return false;
                }

                UiSelectGift.emptyCell = curItem.Amount > 1 ? giftCfg.EmptyCell : giftCfg.EmptyCell - 1;
                if (giftCfg.Optional == 1)
                {
                    UiSelectGift.OpenWindow();
                    var win = UiSelectGift.GetWindow();
                    win?.Setup(curItem);
                    return true;
                }
                else
                {
                    if (Mc.CollectionItem.RemoteCallOpenPackage(curItem.NodeId, giftCfg.DropIDList[0]))
                    {
                        MgrAudio.PlayAudioEventAsync("UI_GiftBag_Open", null, null);
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
            }
            else
            {
                // 不隐藏tips，可连续使用
                if (Mc.CollectionItem.UseNormalItem(curItem))
                {
                    if (curItem.ItemConfig?.Manufacturing == ItemType.Expendable && curItem.ItemConfig?.SecondaryClassification == 8)
                    {
                        var dataList = Mc.Tables.TbBoxTask.DataList;
                        var boxData = dataList[0].Tasks;
                        long taskId = -1;
                        for (int i = 0; i < boxData.Count; i++)
                        {
                            var data = boxData[i];
                            if (data.CoefficientValue == curItem.ItemConfig.Id)
                            {
                                taskId = data.Id;
                                break;
                            }
                        }

                        if (!Mc.Mission.HasBoxTask(taskId))
                        {
                            Mc.Mission.boxTaskSelectId = curItem.ItemConfig.Id;
                            UiInventory.CloseInventory();
                            UiMissionView.OpenWindow((win) =>
                            {
                                win.JumpToPage(Common.Data.MissionTabType.Box);
                            });
                        }
                    }
                }
                return false;
            }
        }

        private static bool OnClickItemUseEquip(BaseItemNode curItem)
        {
            if (!Mc.CollectionItem.ClientEquipCloth(curItem)) return false;
            Mc.Msg.FireMsg<BaseItemNode>(EventDefine.UiClothUnChoose, null);
            return true;
        }

        /// <summary>
        /// 使用摆件
        /// </summary>
        private static bool OnClickDeployUse(BaseItemNode curItem)
        {
            return Mc.CollectionItem.UseDeployItem(curItem, true);
        }

        private static bool CondWaterBottleBtn(BaseItemNode curItem)
        {
            if (null == curItem) return false;
            if (curItem is WaterBottleItemNode itemBottle) return itemBottle.GetWaterAmount() > 0;
            else return curItem.Amount > 0;
        }

        private static bool OnClickHoldModelUse(BaseItemNode curItem)
        {
            bool res = Mc.CollectionItem.UseHoldModelItem(curItem);
            // 使用成功，隐藏Tips, 取消选中
            if (res)
            {
                UiItemTips.HideTips();
                Mc.Msg.FireMsg(EventDefine.UiHideAllInventoryIcons);
            }
            return false;
        }

        private static bool OnClickDropWater(BaseItemNode curItem)
        {
            BaseItemNode waterItem = curItem;
            if (curItem is WaterBottleItemNode itemBottle) waterItem = itemBottle.GetWaterItem();
            if (null != waterItem && waterItem.Amount > 0)
            {
                Mc.MyPlayer.DropWaterItem(waterItem.Id);
                Mc.Audio.AddAudio("Svl_WaterBottle_Slosh", Mc.MyPlayer.MyEntityId);
            }
            else
            {
                Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.NO_WATER);
            }
            return false;
        }

        private static bool OnClickItemLearnBlueprint(BaseItemNode curItem)
        {
            if (null == curItem) return false;
            // 如果蓝图已经解锁, 不能重复解锁, 需要弹一个提示
            if (UnityMainLoop.IsInGameLoop && Mc.Technology.IsBlueprintUnlockInTechTree(curItem.Config.BlueprintIds))
            {
                Mc.MsgTips.ShowRealtimeWeakTip(3014);
                return false;
            }
            if (Mc.Blueprint.IsBlueprintUnlocked(curItem.Config.BlueprintIds))
            {
                Mc.MsgTips.ShowRealtimeWeakTip(3014);
                return false;
            }

            var entityId = curItem.GetBelongComponent()?.ParentEntity.EntityId ?? 0;
            if (entityId <= 0) return false;
            Mc.CollectionItem.RemoteCallUseItem(entityId, curItem.NodeId, 1);
            return true;
        }

        private static bool OnClickUnloadAmmo(BaseItemNode curItem)
        {
            Mc.Msg.FireMsgAtOnce(EventDefine.InventoryMainSelectType, EInventoryType.Main, false, true);
            Mc.CollectionItem.TryUnloadBulletOfWeapon(curItem);
            return false;
        }

        /// <summary>
        /// 卸载配件
        /// </summary>
        private static bool OnClickUnloadPart(BaseItemNode curItem)
        {
            if (null == curItem || null == curItem.ParentNode) return false;
            var inventory = Mc.CollectionItem;
            // 背包实例数量已满, 需要做一个提示
            if (inventory.ContainerMain.IsFull)
            {
                UiMsgBox.ShowMsgBox(new()
                {
                    Msg = LanguageManager.GetTextConst(LanguageConst.BackpackFull),
                    BtnList = new()
                    {
                        new(LanguageManager.GetTextConst(LanguageConst.Cancel), null),
                        new(LanguageManager.GetTextConst(LanguageConst.Confirm), ()=>
                        {
                            Mc.CollectionItem.DropItem(0, curItem.NodeId, curItem.Amount);
                        }),
                    }
                });
                return false;
            }

            Mc.Msg.FireMsgAtOnce(EventDefine.InventoryMainSelectType, EInventoryType.Main, false, true);
            Mc.CollectionItem.MoveItem(curItem.NodeId, NodeSystemType.PlayerInventory, NodePathConst.Anywhere, -1);
            return false;
        }

        /// <summary>
        /// 脱下服饰
        /// </summary>
        public static bool OnClickClothTakeOff(BaseItemNode curItem)
        {
            if (null == curItem.ParentNode || curItem.ParentContainer.ContainerIndex != PlayerInventoryNodeIndex.Wear) return false;
            if (UiInventoryOtherside.IsOpen && Mc.LootCollection.HasLootContainer())
            {
                var itemBelongRole = (curItem.GetBelongComponent()?.ParentEntity as PlayerEntity)?.RoleId ?? 0;
                if (itemBelongRole != Mc.MyPlayer.RoleId)
                {
                    OnClickMoveToBackpack(curItem);
                    return true;
                }
            }
            if (!Mc.CollectionItem.CanMoveToInventory(curItem))
            {
                Mc.MsgTips.ShowRealtimeWeakTip((int)ErrorCode.BackpackFull);
                return false;
            }
            if (curItem is ExtraPackItemNode && Mc.CollectionItem.IsEquipParachuteBagUsing())
            {
                return false;
            }
            Mc.Msg.FireMsgAtOnce(EventDefine.InventoryMainSelectType, EInventoryType.Main, false, true);
            Mc.CollectionItem.InventoryCom.RemoteCallTakeOffCloth(ERpcTarget.World, curItem.NodeId);
            return true;
        }

        /// <summary>
        /// 移动到背包
        /// </summary>
        public static bool OnClickMoveToBackpack(BaseItemNode curItem)
        {
            if (!Mc.CollectionItem.CanMoveToInventory(curItem))
            {
                Mc.MsgTips.ShowRealtimeWeakTip((int)ErrorCode.BackpackFull);
                return false;
            }
            if (Mc.CollectionItem.NeedPickItem(curItem))
            {
                Mc.LootCollection.PickItem(curItem, StorageContainerConst.NoneContainer, -1);
            }
            else
            {
                Mc.CollectionItem.MoveItem(curItem.NodeId, NodeSystemType.PlayerInventory, NodePathConst.Anywhere, -1);
            }
            // 播放落位音效
            var tbItem = curItem.ItemConfig;
            if (null != tbItem && !string.IsNullOrEmpty(tbItem.SoundDrop)) Mc.Audio.Add2DAudio(tbItem.SoundDrop);
            return true;
        }

        /// <summary>
        /// 移动到异端容器
        /// </summary>
        public static bool OnClickMoveToOtherside(BaseItemNode curItem)
        {
            if (TerritoryCabinetComponent.TryMoveReputationItemToToolCupboardCheck(curItem))
            {
                // 声望模式下的声望物品
                return false;
            }

            // #20004 【摆件类优化】分解机界面的双击判定
            var root = Mc.CollectionItem.PlayerRoot;
            var systemIndex = root.LootSystemNode.Index;
            var itemContainer = Mc.MyPlayer.MyEntityServer?.InventoryComponent?.GetVehicleStorageContainer(); // todo: 载具特殊处理
            var containerIndex = itemContainer == null || itemContainer.IsFull ? NodePathConst.Anywhere : itemContainer.Impl.ContainerIndex;
            IDirectoryNode container = root.LootSystemNode;
            if (systemIndex == NodeSystemType.DecomposeMachineSystem)
            {
                containerIndex = StorageContainerConst.DecomposeInput;
                container = container.GetChildNode(StorageContainerConst.DecomposeInput) as IDirectoryNode;
            }

            bool canMove = Mc.CollectionItem.CanMoveTo(curItem, container, true,
                (loot, needToast) =>
                {
                    if (!needToast) return;
                    string strLootName = Mc.LootCollection.LootName;
                    Mc.MsgTips.ShowRealtimeWeakTip((int)ErrorCode.OthersideCanNotAccept, new string[] { curItem.Config.Name, strLootName ?? "" });
                },
                loot =>
                {
                    string strLootName = Mc.LootCollection.LootName;
                    Mc.MsgTips.ShowRealtimeWeakTip((int)ErrorCode.OthersideFull, new string[] { strLootName ?? "" });
                });
            if (!canMove) return false;
            Mc.CollectionItem.MoveItem(curItem.NodeId, systemIndex, containerIndex, -1);
            // 播放落位音效
            var tbItem = curItem.ItemConfig;
            if (null != tbItem && !string.IsNullOrEmpty(tbItem.SoundDrop)) Mc.Audio.Add2DAudio(tbItem.SoundDrop);
            return true;
        }

        /// <summary>
        /// 移动到保险柜
        /// </summary>
        public static bool OnClickMoveToSafeBox(BaseItemNode curItem)
        {
            // 已废弃
            return true;
        }

        public static bool OnClickEditIC(BaseItemNode curItem)
        {
            Mc.ElectricLocal.PrepareICEdit(curItem.Id);
            return false;
        }


        public static bool OnClickSeed(BaseItemNode curItem)
        {
            Mc.Plant.TryOpenWindow(curItem);
            return false;
        }

        /// <summary>
        /// 充能
        /// </summary>
        public static bool OnClickRecharge(BaseItemNode curItem)
        {
            if (null == curItem) return false;
            if ((Mc.MyPlayer?.MyEntityLocal?.NereastWorkbenchID ?? -1) < 0)
            {
                Mc.MsgTips.ShowRealtimeWeakTip(3028);
                return false;
            }
            IRechargeableEntity entity = Mc.Entity.GetEntity(curItem.HoldEntityId) as IRechargeableEntity;
            if (null == entity)
            {
                if (curItem.Condition >= curItem.MaxCondition)    //能量满不用充
                    return false;
                Mc.MyPlayer.MyEntityServer?.InventoryComponent.RemoteCallRechargeItem(ERpcTarget.World, curItem.NodeId);
            }
            else
            {
                if (!entity.CanRecharge)    //能量满不用充
                    return false;
                JackhammerChargeDetectSystem.RemoteCallJackHammerCharge(entity);
            }
            return false;
        }

        /// <summary>
        /// 充能条件
        /// </summary>
        private static bool CondRecharge(BaseItemNode curItem)
        {
            if (null == curItem) return false;
            return Mc.MyPlayer.MyEntityLocal.NereastWorkbenchID >= 0;
        }

        /// <summary>
        /// 装备快捷栏上的物品
        /// </summary>
        private static bool OnClickEquipBelt(BaseItemNode curItem)
        {
            int movePos = -1;
            var containerBelt = Mc.CollectionItem.InventoryCom.ContainerBeltNode;
            for (int i = (int)HoldItemIndex.Item1; i <= (int)HoldItemIndex.Item8; ++i)
            {
                if (null != containerBelt.GetChildNode(i)) continue;
                if (containerBelt.Impl.CanBeltAcceptItem(curItem, i) != CanAcceptResult.CanAccept) continue;
                movePos = i;
                break;
            }
            if (movePos < 0) return false;
            Mc.CollectionItem.MoveItem(curItem.Id, NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Belt, movePos);
            return true;
        }

        /// <summary>
        /// 卸载快捷栏上的物品
        /// </summary>
        private static bool OnClickUnloadBelt(BaseItemNode curItem)
        {
            Mc.Audio.Add2DAudioByConfig(Mc.Tables.TbUiAudio.UniversalButton);
            if (!Mc.CollectionItem.CanMoveToInventory(curItem))
            {
                Mc.MsgTips.ShowRealtimeWeakTip((int)ErrorCode.BackpackFull);
                return false;
            }
            Mc.Msg.FireMsgAtOnce(EventDefine.InventoryMainSelectType, EInventoryType.Main, false, true);
            Mc.CollectionItem.MoveItem(curItem.Id, NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Main);
            return true;
        }

        /// <summary>
        /// 点击帐篷使用
        /// </summary>
        private static bool OnClickCampTent(ItemConfig curItem)
        {
            if (!Mc.MyPlayer.CanCampingTentUse()) return false;
            if (null == curItem || !Mc.CollectionItem.DeployUseCheck()) return false;
            // 关闭背包, 进入摆件模式
            if (UiInventory.IsOpen) UiInventory.CloseInventory();
            else if (UiInventoryOtherside.IsOpen) UiInventoryOtherside.CloseInventory();
            Mc.Construction.EnterDeployCampingTent();
            return true;
        }

        //背包移动到情报柜
        private static bool OnClickMovetoCabinet(ItemConfig curItem)
        {
            BaseItemNode itemNode = null;
            Mc.CollectionItem.InventoryCom.ForeachItemInMain(node =>
            {
                if (node.BizId == curItem.Id)
                {
                    itemNode = node;
                    return false;
                }
                return true;
            });
            if (itemNode == null)
            {
                //Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.RaidIsInvalid);
                Mc.MsgTips.ShowDebugRealtimeWeakTip(LanguageManager.GetTextConst(LanguageConst.PackageNoItem));
            }
            else
            {
                var targetNode = Mc.Reputation.GetTerritoryCabinet().GetInputContainer();
                // targetNode.GetPath()的GC不好优化，里面有异步+引用逻辑
                UiItemSplit.ShowSplitItemToPath(itemNode, targetNode.GetPath(), UiItemSplit.ESplitItemType.InventoryToCabinet, LanguageManager.GetTextConst(LanguageConst.CabinetInPut), 1);
            }

            return true;
        }
        //情报柜移动到背包
        private static bool OnClickAllMoveToInventory(ItemConfig curItem)
        {
            var inventoryComponent = Mc.MyPlayer.MyEntityServer.InventoryComponent;
            var itemNode = Mc.Reputation.GetInputContainerNode(curItem.Id);
            var itemNodePath = Pool.GetList<long>();
            itemNode.GetPath(itemNodePath);
            inventoryComponent.RemoteCallMoveItemToPath(ERpcTarget.World, itemNodePath, NodePathConst.AnywhereInPlayerInventoryMain);
            Pool.ReleaseList(itemNodePath);
            return true;
        }
        //点击放入全部时，目前用于情报碟片功能
        private static bool OnClickAllInPut(ItemConfig curItem)
        {
            var inventoryComponent = Mc.MyPlayer.MyEntityServer.InventoryComponent;
            int amount = inventoryComponent.GetAmount(curItem.Id);
            if (amount == 0)
            {
                Mc.MsgTips.ShowRealtimeWeakTip(24069);
                return false;
            }
            var targetContainer = Mc.Reputation.GetTerritoryCabinet().GetOutputContainer();
            var targetItemNode = Mc.Reputation.GetOutputContainerNode();
            List<long> tempTargetPath = Pool.GetList<long>();
            if (targetItemNode != null)
            {
                targetItemNode.GetPath(tempTargetPath);
                //Mc.CollectionItem.InventoryCom.RemoteCallTransferItemBetweenContainers(ERpcTarget.World, curItem.Id, amount, tempSrcPath, targetContainer.GetPath());
            }
            else
            {
                MgrMyPlayer.GetNodePath(targetContainer, tempTargetPath);
            }

            Mc.CollectionItem.InventoryCom.RemoteCallTransferItemBetweenContainers(ERpcTarget.World, curItem.Id, amount, NodePathConst.PlayerInventoryMain, tempTargetPath);
            Pool.ReleaseList(tempTargetPath);
            return true;
        }
        //点击查看详情
        private static bool OnClickDetail(ItemConfig curItem)
        {
            Mc.Msg.FireMsgAtOnce(EventDefine.UiHideAllInventoryIcons);
            UiItemTips.HideTips();
            UiItemDetail.OpenItemDetailWin(curItem, "UiInventory");
            return true;
        }

        private static bool OnClicSkin(BaseItemNode curItem)
        {
            if (curItem.ParentContainer.NodeId == Mc.CollectionItem.ContainerWear.NodeId)
            {
                IAlpha3ItemContainerWrap container = Mc.CollectionItem.ContainerWearNode;
                int count = container.ContainerConfig.Manufacturing;
                //如果是从背包装备栏打开，装备栏任意一件是共享皮肤就提示
                List<BaseItemNode> resetItemList = null;
                for (int i = 0; i < count; ++i)
                {
                    BaseItemNode itemNode = container.GetSlot(i);
                    if (itemNode != null)
                    {
                        var skinData = Mc.LobbySkin.GetSkinData(itemNode.SkinId);
                        if (skinData == null && curItem.SkinId != 0)
                        {
                            if (resetItemList == null)
                            {
                                resetItemList = new List<BaseItemNode>();
                            }
                            resetItemList.Add(itemNode);
                        }
                    }
                }
                if (resetItemList != null && resetItemList.Count > 0)
                {
                    UiMsgBox.ShowMsgBox(new MsgBoxInfo(182, new List<System.Action> { null, () => ResetShareSkin(resetItemList, curItem) }));
                    return false;

                }
            }
            else
            {
                var skinData = Mc.LobbySkin.GetSkinData(curItem.SkinId);
                if (skinData == null && curItem.SkinId != 0)
                {
                    UiMsgBox.ShowMsgBox(new MsgBoxInfo(181, new List<System.Action> { null, () => ResetShareSkin(new List<BaseItemNode>() { curItem }, curItem) }));
                    return false;
                }
            }
            if (curItem.ParentContainer.NodeId == Mc.CollectionItem.ContainerWear.NodeId)
            {
                Mc.Ui.OpenWindow("UiGameSkinEquip");
            }
            else
            {
                UiInventoryItemSkin.OpenFromInventoryTips(curItem.Config.Id, curItem);
            }
            return true;
        }

        /// <summary>
        /// 还原共享皮肤为自己的预设皮肤
        /// </summary>
        /// <param name="resetItem">需要还原的共享皮肤</param>
        private static void ResetShareSkin(List<BaseItemNode> resetItemList, BaseItemNode curItem)
        {
            for (int i = 0; i < resetItemList.Count; i++)
            {
                long skinId = Mc.LobbySkin.GetItemEquippedSkin(resetItemList[i].ItemId);
                var itemNode = Mc.MyPlayer.MyEntityServer.SkinComp.GetItemNodeByItemId(resetItemList[i].ItemId);
                Action openAction = null;
                //还原到最后一个皮肤时打开界面
                if (i == resetItemList.Count - 1)
                {
                    openAction = () =>
                    {
                        if (curItem.ParentContainer.NodeId == Mc.CollectionItem.ContainerWear.NodeId)
                        {
                            Mc.Ui.OpenWindow("UiGameSkinEquip");
                        }
                        else
                        {
                            UiInventoryItemSkin.OpenFromInventoryTips(curItem.Config.Id, curItem);
                        }
                    };
                }
                if (skinId != 0)
                {
                    var skinItemNode = Mc.LobbySkin.GetSkinNodeBySkinId(resetItemList[i].SkinId);
                    Mc.LobbySkin.ChangeItemSkin(skinItemNode.Id, itemNode, openAction);
                }
                else
                {
                    Mc.LobbySkin.ChangeItemSkin(0, itemNode, openAction);
                }
            }

        }
    }
}