﻿using Character.Resource.AnimRule.Data;
using CommonUnity.Runtime.Animation;
using CommonUnity.Runtime.Character.Resource;
using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEditor.Animations;
using UnityEngine;
using WizardGames.Editor;
using WizardGames.Soc.Common.Unity.Character;

namespace Character.Resource.AnimRule.RuleTp
{
    public static class AnimEditorRule_TpCurveUpdate
    {
        struct ActionLocomotionWeightStruct
        {
            public AnimationCurve Dynamic_ActionWeightCurve;
            public AnimationCurve Spine_ActionWeightCurve;
            public AnimationCurve Spine1_ActionWeightCurve;
            public AnimationCurve Spine2_ActionWeightCurve;
            public AnimationCurve LClavicle_ActionWeightCurve;
            public AnimationCurve Head_ActionWeightCurve;
            public AnimationCurve RClavicle_ActionWeightCurve;
            public AnimationCurve Weapon_ActionWeightCurve;
            public AnimationCurve LeftArm_ActionWeightCurve;
            public AnimationCurve RightArm_ActionWeightCurve;
        }
        
        struct ActionAoWeightStruct
        {
            public AnimationCurve Dynamic_ActionWeightCurve;
            public AnimationCurve Spine_ActionWeightCurve;
            public AnimationCurve Spine1_ActionWeightCurve;
            public AnimationCurve Spine2_ActionWeightCurve;
            public AnimationCurve Head_ActionWeightCurve;
            public AnimationCurve Hand_ActionWeightCurve;
        }
        public static void AutoCreateAll()
        {
            //CreateLocomotion();
            //CreateActionToLoco();
            //CreateActionAo();
            //CreateHorseSpeacial();
            //GetAllCurveData();
        }

        private static void CreateHorseSpeacial()
        {
            CreateActionHorseSpeicalAdd();
            CreateActionHorseSpeicalOc();
        }
        
        public static void CreateActionHorseSpeicalAdd()
        {
            // Add
            //找当前武器，所有的片段，是否有导出actionloco的配置
            var allTpTemplate = AssetDatabase.FindAssets("t:TpClipSettingMeta",
                new string[] {"Assets/SocClientRes/Weapon"}); //
            foreach (var settingGuid in allTpTemplate)
            {
                var filepath = AssetDatabase.GUIDToAssetPath(settingGuid);
                var metaAsset = AssetDatabase.LoadAssetAtPath<TpClipSettingMeta>(filepath);
                //create asset
                var subPath = filepath.Substring(0, filepath.LastIndexOf('/'));
                var applyToAnimatorGuid = AssetDatabase.FindAssets("t:AnimEditorRule_ApplyToAnimator",
                    new string[] {subPath}); 
                if(applyToAnimatorGuid.Length != 1)
                    continue;
                //收集add层的所有的state的apply的片段
                var applyToAnimatorPath = AssetDatabase.GUIDToAssetPath(applyToAnimatorGuid[0]);
                var applyToAnimator = AssetDatabase.LoadAssetAtPath<AnimEditorRule_ApplyToAnimator>(applyToAnimatorPath);
                var controller = applyToAnimator.CurrentCtrl;
                var clipOverrides = new List<KeyValuePair<AnimationClip, AnimationClip>>();
                controller.GetOverrides(clipOverrides);
                var addStateTypeToClip = new SerializableDictionary<AnimParametersTp.EAdditiveLayer, System.Collections.Generic.List<AnimationClip>>();
                var addStateTypeToOcClip = new SerializableDictionary<AnimParametersTp.EAdditiveLayer, System.Collections.Generic.List<AnimationClip>>();
                var headName = "";
                var ac = controller.runtimeAnimatorController as AnimatorController;
                AnimEditorRuleUtils.ProcessLayerClipWithType<AnimParametersTp.EAdditiveLayer>(ac.layers[AnimParametersTp.AdditiveLayer_LayerIndex].stateMachine, addStateTypeToClip, ref headName);
                foreach (var item in addStateTypeToClip)
                {
                    var list = item.Value;
                    foreach (var baseClip in list)
                    {
                        foreach (var overridePair in clipOverrides)
                        {
                            if (overridePair.Key == baseClip)
                            {
                                if (overridePair.Value != null)
                                {
                                     if(addStateTypeToOcClip.TryGetValue(item.Key, out var ocClipList))
                                     {
                                         ocClipList.Add(overridePair.Value);
                                     }
                                     else
                                     {
                                         ocClipList = new List<AnimationClip>();
                                         ocClipList.Add(overridePair.Value);
                                         addStateTypeToOcClip.Add(item.Key, ocClipList);
                                     }
                                }
                                break;
                            }
                        }
                    }
                }
                
                //找到所有片段的所对应的AnimEditorRule_AddCurveEventData
                var actionLocomotionWeightDic = new Dictionary<AnimParametersTp.EAdditiveLayer,Anim_DataInfo>();
                foreach (var item in addStateTypeToOcClip)
                {
                    var list = item.Value;
                    var have = false;
                    foreach (var clip in list)
                    {
                        var clipPath = AssetDatabase.GetAssetPath(clip);
                        //父级
                        var clipSubPath = clipPath.Substring(0, clipPath.LastIndexOf("/"));
                        clipSubPath = clipSubPath.Substring(0, clipSubPath.LastIndexOf("/"));
                        //找addcurveeventdata
                        var addCurveGuid = AssetDatabase.FindAssets("t:AnimEditorRule_AddCurveEventData",
                            new string[] {clipSubPath}); 
                        if(addCurveGuid.Length != 1)
                            continue;
                        //收集oc层的所有的state的apply的片段
                        var addCurvePath = AssetDatabase.GUIDToAssetPath(addCurveGuid[0]);
                        var addCurve = AssetDatabase.LoadAssetAtPath<AnimEditorRule_AddCurveEventData>(addCurvePath);
                        foreach (var data in addCurve.DataDicts)
                        {
                            var keyName = clip.name.Substring(clip.name.IndexOf("_") + 1);
                            if (keyName.Equals(data.BaseKeyWord))
                            {
                                foreach (var curInfo in data.Curves)
                                {
                                    if (curInfo.CurveName == "HorseSpeacial_WeightCurve")
                                    {
                                        actionLocomotionWeightDic.TryAdd(item.Key, data);
                                        //存储相关的数据
                                        have = true;
                                        break;
                                    }
                                }
                                break;
                            }
                        }
                        if(have)
                            break;
                    }
                }
                //将当前的actionlocoweight生成在新的序列化文件里
                foreach (var add in metaAsset.AddLayerWeightCollection)
                {
                    var key = add.Key;
                    var item = add.Value;
                    var itemPath = AssetDatabase.GetAssetPath(item);
                    var addMeta = AssetDatabase.LoadAssetAtPath<TpMetaAdditiveWeight>(itemPath);
                    if (actionLocomotionWeightDic.TryGetValue(key, out var data))
                    {
                        //赋值
                        var tempAnimationCurve = new AnimationCurve();
                        foreach (var curInfo in data.Curves)
                        {
                            if (curInfo.CurveName == "HorseSpeacial_WeightCurve")
                            {
                                tempAnimationCurve = curInfo.Curve;
                                break;
                            }
                        }
                        CopyHorseSpecialWeightToMetaAdd(addMeta, ref addMeta.HorseIKWeightCurve, tempAnimationCurve);
                        actionLocomotionWeightDic.Remove(key);
                    }
                    else
                    {
                        addMeta.InitHorseIK();
                    }
                    EditorUtility.SetDirty(addMeta);
                    AssetDatabase.SaveAssetIfDirty(addMeta);
                }

                foreach (var left in actionLocomotionWeightDic)
                {
                    var key = left.Key;
                    var data = left.Value;
                    var locPath = subPath + "/TpMetaAdWeight_"+key.ToString()+".asset";
                    var addMeta = AssetDatabase.LoadAssetAtPath<TpMetaAdditiveWeight>(locPath);
                    if (addMeta == null)
                    {
                        addMeta = ScriptableObject.CreateInstance<TpMetaAdditiveWeight>();
                        AssetDatabase.CreateAsset(addMeta, locPath);
                        //赋值
                        var tempAnimationCurve = new AnimationCurve();
                        foreach (var curInfo in data.Curves)
                        {
                            if (curInfo.CurveName == "HorseSpeacial_WeightCurve")
                            {
                                tempAnimationCurve = curInfo.Curve;
                                break;
                            }
                        }
                        CopyHorseSpecialWeightToMetaAdd(addMeta, ref addMeta.HorseIKWeightCurve, tempAnimationCurve);
                        
                        addMeta.InitActionToLoco();
                        addMeta.InitAo();
                        
                        metaAsset.AddLayerWeightCollection.TryAdd(key, addMeta);
                    }
                    EditorUtility.SetDirty(addMeta);
                    AssetDatabase.SaveAssetIfDirty(addMeta);
                }
                EditorUtility.SetDirty(metaAsset);
                AssetDatabase.SaveAssetIfDirty(metaAsset);
            }
        }
        
        public static void CreateActionHorseSpeicalOc()
        {
            // Oc
            //找当前武器，所有的片段，是否有导出actionloco的配置
            var allTpTemplate = AssetDatabase.FindAssets("t:TpClipSettingMeta",
                new string[] {"Assets/SocClientRes/Weapon"}); //
            foreach (var settingGuid in allTpTemplate)
            {
                var filepath = AssetDatabase.GUIDToAssetPath(settingGuid);
                var metaAsset = AssetDatabase.LoadAssetAtPath<TpClipSettingMeta>(filepath);
                //create asset
                var subPath = filepath.Substring(0, filepath.LastIndexOf('/'));
                var applyToAnimatorGuid = AssetDatabase.FindAssets("t:AnimEditorRule_ApplyToAnimator",
                    new string[] {subPath}); 
                if(applyToAnimatorGuid.Length != 1)
                    continue;
                //收集add层的所有的state的apply的片段
                var applyToAnimatorPath = AssetDatabase.GUIDToAssetPath(applyToAnimatorGuid[0]);
                var applyToAnimator = AssetDatabase.LoadAssetAtPath<AnimEditorRule_ApplyToAnimator>(applyToAnimatorPath);
                var controller = applyToAnimator.CurrentCtrl;
                var clipOverrides = new List<KeyValuePair<AnimationClip, AnimationClip>>();
                controller.GetOverrides(clipOverrides);
                var ocStateTypeToClip = new SerializableDictionary<AnimParametersTp.EOverrideLayer, System.Collections.Generic.List<AnimationClip>>();
                var ocStateTypeToOcClip = new SerializableDictionary<AnimParametersTp.EOverrideLayer, System.Collections.Generic.List<AnimationClip>>();
                var headName = "";
                var ac = controller.runtimeAnimatorController as AnimatorController;
                AnimEditorRuleUtils.ProcessLayerClipWithType<AnimParametersTp.EOverrideLayer>(ac.layers[AnimParametersTp.OverrideLayer_LayerIndex].stateMachine, ocStateTypeToClip, ref headName);
                foreach (var item in ocStateTypeToClip)
                {
                    var list = item.Value;
                    foreach (var baseClip in list)
                    {
                        foreach (var overridePair in clipOverrides)
                        {
                            if (overridePair.Key == baseClip)
                            {
                                if (overridePair.Value != null)
                                {
                                     if(ocStateTypeToOcClip.TryGetValue(item.Key, out var ocClipList))
                                     {
                                         ocClipList.Add(overridePair.Value);
                                     }
                                     else
                                     {
                                         ocClipList = new List<AnimationClip>();
                                         ocClipList.Add(overridePair.Value);
                                         ocStateTypeToOcClip.Add(item.Key, ocClipList);
                                     }
                                }
                                break;
                            }
                        }
                    }
                }
                
                //找到所有片段的所对应的AnimEditorRule_AddCurveEventData
                var actionLocomotionWeightDic = new Dictionary<AnimParametersTp.EOverrideLayer,Anim_DataInfo>();
                foreach (var item in ocStateTypeToOcClip)
                {
                    var list = item.Value;
                    var have = false;
                    foreach (var clip in list)
                    {
                        var clipPath = AssetDatabase.GetAssetPath(clip);
                        //父级
                        var clipSubPath = clipPath.Substring(0, clipPath.LastIndexOf("/"));
                        clipSubPath = clipSubPath.Substring(0, clipSubPath.LastIndexOf("/"));
                        //找addcurveeventdata
                        var addCurveGuid = AssetDatabase.FindAssets("t:AnimEditorRule_AddCurveEventData",
                            new string[] {clipSubPath}); 
                        if(addCurveGuid.Length != 1)
                            continue;
                        //收集oc层的所有的state的apply的片段
                        var addCurvePath = AssetDatabase.GUIDToAssetPath(addCurveGuid[0]);
                        var addCurve = AssetDatabase.LoadAssetAtPath<AnimEditorRule_AddCurveEventData>(addCurvePath);
                        foreach (var data in addCurve.DataDicts)
                        {
                            var keyName = clip.name.Substring(clip.name.IndexOf("_") + 1);
                            if (keyName.Equals(data.BaseKeyWord))
                            {
                                foreach (var curInfo in data.Curves)
                                {
                                    if (curInfo.CurveName == "HorseSpeacial_WeightCurve")
                                    {
                                        actionLocomotionWeightDic.TryAdd(item.Key, data);
                                        //存储相关的数据
                                        have = true;
                                        break;
                                    }
                                }
                                break;
                            }
                        }
                        if(have)
                            break;
                    }
                }
                //将当前的actionlocoweight生成在新的序列化文件里
                foreach (var add in metaAsset.OcLayerWeightCollection)
                {
                    var key = add.Key;
                    var item = add.Value;
                    var itemPath = AssetDatabase.GetAssetPath(item);
                    var addMeta = AssetDatabase.LoadAssetAtPath<TpMetaOverrideWeight>(itemPath);
                    if (actionLocomotionWeightDic.TryGetValue(key, out var data))
                    {
                        //赋值
                        var tempAnimationCurve = new AnimationCurve();
                        foreach (var curInfo in data.Curves)
                        {
                            if (curInfo.CurveName == "HorseSpeacial_WeightCurve")
                            {
                                tempAnimationCurve = curInfo.Curve;
                                break;
                            }
                        }
                        CopyHorseSpecialWeightToMetaOc(addMeta, ref addMeta.HorseIKWeightCurve, tempAnimationCurve);
                        actionLocomotionWeightDic.Remove(key);
                    }
                    else
                    {
                        addMeta.InitHorseIK();
                    }
                    EditorUtility.SetDirty(addMeta);
                    AssetDatabase.SaveAssetIfDirty(addMeta);
                }

                foreach (var left in actionLocomotionWeightDic)
                {
                    var key = left.Key;
                    var data = left.Value;
                    var locPath = subPath + "/TpMetaOcWeight_"+key.ToString()+".asset";
                    var ocMeta = AssetDatabase.LoadAssetAtPath<TpMetaOverrideWeight>(locPath);
                    if (ocMeta == null)
                    {
                        ocMeta = ScriptableObject.CreateInstance<TpMetaOverrideWeight>();
                        AssetDatabase.CreateAsset(ocMeta, locPath);
                        //赋值
                        var tempAnimationCurve = new AnimationCurve();
                        foreach (var curInfo in data.Curves)
                        {
                            if (curInfo.CurveName == "HorseSpeacial_WeightCurve")
                            {
                                tempAnimationCurve = curInfo.Curve;
                                break;
                            }
                        }
                        CopyHorseSpecialWeightToMetaOc(ocMeta, ref ocMeta.HorseIKWeightCurve, tempAnimationCurve);
                        
                        ocMeta.Init();
                        ocMeta.InitActionToLoco();
                        ocMeta.InitAo();
                        
                        metaAsset.OcLayerWeightCollection.TryAdd(key, ocMeta);
                    }
                    EditorUtility.SetDirty(ocMeta);
                    AssetDatabase.SaveAssetIfDirty(ocMeta);
                }
                EditorUtility.SetDirty(metaAsset);
                AssetDatabase.SaveAssetIfDirty(metaAsset);
            }
        }
        
        private static void CopyHorseSpecialWeightToMetaAdd(TpMetaAdditiveWeight meta, ref AnimCurveKey curve, AnimationCurve sourceCurve)
        {
            var assetPath = AssetDatabase.GetAssetPath(meta);
            var folderPath = Path.GetDirectoryName(assetPath);
            //在目标目录下创建一个字符串文件夹
            var strFilePath = Path.Combine(folderPath, AnimStrKey.localStrFile);
            AnimStrGroupRef animStrGroupRef = null;
            if (!File.Exists(strFilePath))
            {
                animStrGroupRef = ScriptableObject.CreateInstance<AnimStrGroupRef>();
                AssetDatabase.CreateAsset(animStrGroupRef, strFilePath);
            }
            else
            {
                animStrGroupRef = AssetDatabase.LoadAssetAtPath<AnimStrGroupRef>(strFilePath);
            }
            
            var curveFilePath = Path.Combine(folderPath, AnimCurveKey.localStrFile);
            AnimCurveGroupRef animCurveGroup = null;
            if (!File.Exists(curveFilePath))
            {
                animCurveGroup = ScriptableObject.CreateInstance<AnimCurveGroupRef>();
                AssetDatabase.CreateAsset(animCurveGroup, curveFilePath);
            }
            else
            {
                animCurveGroup = AssetDatabase.LoadAssetAtPath<AnimCurveGroupRef>(curveFilePath);
            }
            
            AnimCurveKey curveKey = AnimCurveKey.CreateKey(animCurveGroup, sourceCurve);
            curve = curveKey;
            
            EditorUtility.SetDirty(animCurveGroup);
            EditorUtility.SetDirty(animStrGroupRef);
        }
         
        private static void CopyHorseSpecialWeightToMetaOc(TpMetaOverrideWeight meta, ref AnimCurveKey curve, AnimationCurve sourceCurve)
        {
            var assetPath = AssetDatabase.GetAssetPath(meta);
            var folderPath = Path.GetDirectoryName(assetPath);
            //在目标目录下创建一个字符串文件夹
            var strFilePath = Path.Combine(folderPath, AnimStrKey.localStrFile);
            AnimStrGroupRef animStrGroupRef = null;
            if (!File.Exists(strFilePath))
            {
                animStrGroupRef = ScriptableObject.CreateInstance<AnimStrGroupRef>();
                AssetDatabase.CreateAsset(animStrGroupRef, strFilePath);
            }
            else
            {
                animStrGroupRef = AssetDatabase.LoadAssetAtPath<AnimStrGroupRef>(strFilePath);
            }
            
            var curveFilePath = Path.Combine(folderPath, AnimCurveKey.localStrFile);
            AnimCurveGroupRef animCurveGroup = null;
            if (!File.Exists(curveFilePath))
            {
                animCurveGroup = ScriptableObject.CreateInstance<AnimCurveGroupRef>();
                AssetDatabase.CreateAsset(animCurveGroup, curveFilePath);
            }
            else
            {
                animCurveGroup = AssetDatabase.LoadAssetAtPath<AnimCurveGroupRef>(curveFilePath);
            }
            
            AnimCurveKey curveKey = AnimCurveKey.CreateKey(animCurveGroup, sourceCurve);
            curve = curveKey;
            
            EditorUtility.SetDirty(animCurveGroup);
            EditorUtility.SetDirty(animStrGroupRef);
        }

        /// <summary>
        /// step:4
        /// 获取tp的所有的addcurve的曲线名
        /// </summary>
        private static void GetAllCurveData()
        {
            var addCurveGuids = AssetDatabase.FindAssets("t:AnimEditorRule_AddCurveEventData",
                new string[] { "Assets/SocClientRes/Weapon", "Assets/SocClientRes/Anim/Char/Tp"});
            var curveNames = new HashSet<string>();
            foreach (var addCurveGuid in addCurveGuids)
            {
                var filepath = AssetDatabase.GUIDToAssetPath(addCurveGuid);
                //跳过fp
                if (filepath.Contains("SocClientRes/Weapon") && filepath.Contains("Animation/Fp"))
                    continue;
                var addCurveEventData = AssetDatabase.LoadAssetAtPath<AnimEditorRule_AddCurveEventData>(filepath);
                foreach (var data in addCurveEventData.DataDicts)
                {
                    foreach (var curveInfo in data.Curves)
                    {
                        Debug.Log("name:"+ curveInfo.CurveName+", path:"+ filepath + ", clipName:"+ data.BaseKeyWord);
                        if (!curveNames.TryGetValue(curveInfo.CurveName, out var curve))
                        {
                            curveNames.Add(curveInfo.CurveName);
                        }
                    }
                }
            }

            foreach (var name in curveNames)
            {
                Debug.Log("name:"+ name);
            }
        }
        
        /// <summary>
        /// step:3
        /// </summary>
        private static void CreateActionAo()
        {
            CreateActionAoAdd();
            CreateActionAoOc();
        }
        
        public static void CreateActionAoAdd()
        {
            // Add
            //找当前武器，所有的片段，是否有导出actionloco的配置
            var allTpTemplate = AssetDatabase.FindAssets("t:TpClipSettingMeta",
                new string[] {"Assets/SocClientRes/Weapon"}); //
            foreach (var settingGuid in allTpTemplate)
            {
                var filepath = AssetDatabase.GUIDToAssetPath(settingGuid);
                var metaAsset = AssetDatabase.LoadAssetAtPath<TpClipSettingMeta>(filepath);
                //create asset
                var subPath = filepath.Substring(0, filepath.LastIndexOf('/'));
                var applyToAnimatorGuid = AssetDatabase.FindAssets("t:AnimEditorRule_ApplyToAnimator",
                    new string[] {subPath}); 
                if(applyToAnimatorGuid.Length != 1)
                    continue;
                //收集add层的所有的state的apply的片段
                var applyToAnimatorPath = AssetDatabase.GUIDToAssetPath(applyToAnimatorGuid[0]);
                var applyToAnimator = AssetDatabase.LoadAssetAtPath<AnimEditorRule_ApplyToAnimator>(applyToAnimatorPath);
                var controller = applyToAnimator.CurrentCtrl;
                var clipOverrides = new List<KeyValuePair<AnimationClip, AnimationClip>>();
                controller.GetOverrides(clipOverrides);
                var addStateTypeToClip = new SerializableDictionary<AnimParametersTp.EAdditiveLayer, System.Collections.Generic.List<AnimationClip>>();
                var addStateTypeToOcClip = new SerializableDictionary<AnimParametersTp.EAdditiveLayer, System.Collections.Generic.List<AnimationClip>>();
                var headName = "";
                var ac = controller.runtimeAnimatorController as AnimatorController;
                AnimEditorRuleUtils.ProcessLayerClipWithType<AnimParametersTp.EAdditiveLayer>(ac.layers[AnimParametersTp.AdditiveLayer_LayerIndex].stateMachine, addStateTypeToClip, ref headName);
                foreach (var item in addStateTypeToClip)
                {
                    var list = item.Value;
                    foreach (var baseClip in list)
                    {
                        foreach (var overridePair in clipOverrides)
                        {
                            if (overridePair.Key == baseClip)
                            {
                                if (overridePair.Value != null)
                                {
                                     if(addStateTypeToOcClip.TryGetValue(item.Key, out var ocClipList))
                                     {
                                         ocClipList.Add(overridePair.Value);
                                     }
                                     else
                                     {
                                         ocClipList = new List<AnimationClip>();
                                         ocClipList.Add(overridePair.Value);
                                         addStateTypeToOcClip.Add(item.Key, ocClipList);
                                     }
                                }
                                break;
                            }
                        }
                    }
                }
                
                //找到所有片段的所对应的AnimEditorRule_AddCurveEventData
                var actionLocomotionWeightDic = new Dictionary<AnimParametersTp.EAdditiveLayer,Anim_DataInfo>();
                foreach (var item in addStateTypeToOcClip)
                {
                    var list = item.Value;
                    var have = false;
                    foreach (var clip in list)
                    {
                        var clipPath = AssetDatabase.GetAssetPath(clip);
                        //父级
                        var clipSubPath = clipPath.Substring(0, clipPath.LastIndexOf("/"));
                        clipSubPath = clipSubPath.Substring(0, clipSubPath.LastIndexOf("/"));
                        //找addcurveeventdata
                        var addCurveGuid = AssetDatabase.FindAssets("t:AnimEditorRule_AddCurveEventData",
                            new string[] {clipSubPath}); 
                        if(addCurveGuid.Length != 1)
                            continue;
                        //收集oc层的所有的state的apply的片段
                        var addCurvePath = AssetDatabase.GUIDToAssetPath(addCurveGuid[0]);
                        var addCurve = AssetDatabase.LoadAssetAtPath<AnimEditorRule_AddCurveEventData>(addCurvePath);
                        foreach (var data in addCurve.DataDicts)
                        {
                            var keyName = clip.name.Substring(clip.name.IndexOf("_") + 1);
                            if (keyName.Equals(data.BaseKeyWord))
                            {
                                if (data.NeedActionAoWeight)
                                {
                                    actionLocomotionWeightDic.TryAdd(item.Key, data);
                                    //存储相关的数据
                                    have = true;
                                }
                                break;
                            }
                        }
                        if(have)
                            break;
                    }
                }
                
                //将当前的actionlocoweight生成在新的序列化文件里
                foreach (var add in metaAsset.AddLayerWeightCollection)
                {
                    var key = add.Key;
                    var item = add.Value;
                    var itemPath = AssetDatabase.GetAssetPath(item);
                    var addMeta = AssetDatabase.LoadAssetAtPath<TpMetaAdditiveWeight>(itemPath);
                    if (actionLocomotionWeightDic.TryGetValue(key, out var data))
                    {
                        //赋值
                        var lj = new ActionAoWeightStruct();
                        lj.Dynamic_ActionWeightCurve = data.AoDynamic_ActionWeightCurve;
                        lj.Head_ActionWeightCurve = data.AoHead_ActionWeightCurve;
                        lj.Hand_ActionWeightCurve = data.AoHand_ActionWeightCurve;
                        lj.Spine1_ActionWeightCurve  = data.AoSpine1_ActionWeightCurve;
                        lj.Spine2_ActionWeightCurve  = data.AoSpine2_ActionWeightCurve;
                        lj.Spine_ActionWeightCurve   = data.AoSpine_ActionWeightCurve;
                        CopyActionAoWeightToMetaAdd(addMeta, ref addMeta.AoWeightCurve, lj);
                        actionLocomotionWeightDic.Remove(key);
                    }
                    else
                    {
                        addMeta.InitAo();
                    }
                    EditorUtility.SetDirty(addMeta);
                    AssetDatabase.SaveAssetIfDirty(addMeta);
                }

                foreach (var left in actionLocomotionWeightDic)
                {
                    var key = left.Key;
                    var data = left.Value;
                    var locPath = subPath + "/TpMetaAdWeight_"+key.ToString()+".asset";
                    var addMeta = AssetDatabase.LoadAssetAtPath<TpMetaAdditiveWeight>(locPath);
                    if (addMeta == null)
                    {
                        addMeta = ScriptableObject.CreateInstance<TpMetaAdditiveWeight>();
                        AssetDatabase.CreateAsset(addMeta, locPath);
                        //赋值
                        var lj = new ActionAoWeightStruct();
                        lj.Dynamic_ActionWeightCurve = data.AoDynamic_ActionWeightCurve;
                        lj.Head_ActionWeightCurve = data.AoHead_ActionWeightCurve;
                        lj.Hand_ActionWeightCurve = data.AoHand_ActionWeightCurve;
                        lj.Spine1_ActionWeightCurve  = data.AoSpine1_ActionWeightCurve;
                        lj.Spine2_ActionWeightCurve  = data.AoSpine2_ActionWeightCurve;
                        lj.Spine_ActionWeightCurve   = data.AoSpine_ActionWeightCurve;
                        CopyActionAoWeightToMetaAdd(addMeta, ref addMeta.AoWeightCurve, lj);
                        
                        addMeta.InitActionToLoco();
                        addMeta.InitHorseIK();
                        
                        metaAsset.AddLayerWeightCollection.TryAdd(key, addMeta);
                    }
                    EditorUtility.SetDirty(addMeta);
                    AssetDatabase.SaveAssetIfDirty(addMeta);
                }
                EditorUtility.SetDirty(metaAsset);
                AssetDatabase.SaveAssetIfDirty(metaAsset);
            }
        }
        
         public static void CreateActionAoOc()
        {
            // pc
            //找当前武器，所有的片段，是否有导出actionloco的配置
            var allTpTemplate = AssetDatabase.FindAssets("t:TpClipSettingMeta",
                new string[] {"Assets/SocClientRes/Weapon"}); //
            foreach (var settingGuid in allTpTemplate)
            {
                var filepath = AssetDatabase.GUIDToAssetPath(settingGuid);
                var metaAsset = AssetDatabase.LoadAssetAtPath<TpClipSettingMeta>(filepath);
                //create asset
                var subPath = filepath.Substring(0, filepath.LastIndexOf('/'));
                var applyToAnimatorGuid = AssetDatabase.FindAssets("t:AnimEditorRule_ApplyToAnimator",
                    new string[] {subPath}); 
                if(applyToAnimatorGuid.Length != 1)
                    continue;
                //收集oc层的所有的state的apply的片段
                var applyToAnimatorPath = AssetDatabase.GUIDToAssetPath(applyToAnimatorGuid[0]);
                var applyToAnimator = AssetDatabase.LoadAssetAtPath<AnimEditorRule_ApplyToAnimator>(applyToAnimatorPath);
                var controller = applyToAnimator.CurrentCtrl;
                var clipOverrides = new List<KeyValuePair<AnimationClip, AnimationClip>>();
                controller.GetOverrides(clipOverrides);
                var overrideStateTypeToClip = new SerializableDictionary<AnimParametersTp.EOverrideLayer, System.Collections.Generic.List<AnimationClip>>();
                var overrideStateTypeToOcClip = new SerializableDictionary<AnimParametersTp.EOverrideLayer, System.Collections.Generic.List<AnimationClip>>();
                var headName = "";
                var ac = controller.runtimeAnimatorController as AnimatorController;
                AnimEditorRuleUtils.ProcessLayerClipWithType<AnimParametersTp.EOverrideLayer>(ac.layers[AnimParametersTp.OverrideLayer_LayerIndex].stateMachine, overrideStateTypeToClip, ref headName);
                foreach (var item in overrideStateTypeToClip)
                {
                    var list = item.Value;
                    foreach (var baseClip in list)
                    {
                        foreach (var overridePair in clipOverrides)
                        {
                            if (overridePair.Key == baseClip)
                            {
                                if (overridePair.Value != null)
                                {
                                     if(overrideStateTypeToOcClip.TryGetValue(item.Key, out var ocClipList))
                                     {
                                         ocClipList.Add(overridePair.Value);
                                     }
                                     else
                                     {
                                         ocClipList = new List<AnimationClip>();
                                         ocClipList.Add(overridePair.Value);
                                         overrideStateTypeToOcClip.Add(item.Key, ocClipList);
                                     }
                                }
                                break;
                            }
                        }
                    }
                }
                
                //找到所有片段的所对应的AnimEditorRule_AddCurveEventData
                var actionLocomotionWeightDic = new Dictionary<AnimParametersTp.EOverrideLayer,Anim_DataInfo>();
                foreach (var item in overrideStateTypeToOcClip)
                {
                    var list = item.Value;
                    var have = false;
                    foreach (var clip in list)
                    {
                        var clipPath = AssetDatabase.GetAssetPath(clip);
                        //父级
                        var clipSubPath = clipPath.Substring(0, clipPath.LastIndexOf("/"));
                        clipSubPath = clipSubPath.Substring(0, clipSubPath.LastIndexOf("/"));
                        //找addcurveeventdata
                        var addCurveGuid = AssetDatabase.FindAssets("t:AnimEditorRule_AddCurveEventData",
                            new string[] {clipSubPath}); 
                        if(addCurveGuid.Length != 1)
                            continue;
                        //收集oc层的所有的state的apply的片段
                        var addCurvePath = AssetDatabase.GUIDToAssetPath(addCurveGuid[0]);
                        var addCurve = AssetDatabase.LoadAssetAtPath<AnimEditorRule_AddCurveEventData>(addCurvePath);
                        foreach (var data in addCurve.DataDicts)
                        {
                            var keyName = clip.name.Substring(clip.name.IndexOf("_") + 1);
                            if (keyName.Equals(data.BaseKeyWord))
                            {
                                if (data.NeedActionAoWeight)
                                {
                                    actionLocomotionWeightDic.TryAdd(item.Key, data);
                                    //存储相关的数据
                                    have = true;
                                }
                                break;
                            }
                        }
                        if(have)
                            break;
                    }
                }
                
                //将当前的actionlocoweight生成在新的序列化文件里
                foreach (var oc in metaAsset.OcLayerWeightCollection)
                {
                    var key = oc.Key;
                    var item = oc.Value;
                    var itemPath = AssetDatabase.GetAssetPath(item);
                    var ocMeta = AssetDatabase.LoadAssetAtPath<TpMetaOverrideWeight>(itemPath);
                    if (actionLocomotionWeightDic.TryGetValue(key, out var data))
                    {
                        var lj = new ActionAoWeightStruct();
                        lj.Dynamic_ActionWeightCurve = data.AoDynamic_ActionWeightCurve;
                        lj.Head_ActionWeightCurve = data.AoHead_ActionWeightCurve;
                        lj.Hand_ActionWeightCurve = data.AoHand_ActionWeightCurve;
                        lj.Spine1_ActionWeightCurve  = data.AoSpine1_ActionWeightCurve;
                        lj.Spine2_ActionWeightCurve  = data.AoSpine2_ActionWeightCurve;
                        lj.Spine_ActionWeightCurve   = data.AoSpine_ActionWeightCurve;
                        CopyActionAoWeightToMetaOc(ocMeta, ref ocMeta.AoWeightCurve, lj);
                        actionLocomotionWeightDic.Remove(key);
                    }
                    else
                    {
                        ocMeta.InitAo();
                    }
                    EditorUtility.SetDirty(ocMeta);
                    AssetDatabase.SaveAssetIfDirty(ocMeta);
                }

                foreach (var left in actionLocomotionWeightDic)
                {
                    var key = left.Key;
                    var data = left.Value;
                    var locPath = subPath + "/TpMetaOcWeight_"+key.ToString()+".asset";
                    var ocMeta = AssetDatabase.LoadAssetAtPath<TpMetaOverrideWeight>(locPath);
                    if (ocMeta == null)
                    {
                        ocMeta = ScriptableObject.CreateInstance<TpMetaOverrideWeight>();
                        AssetDatabase.CreateAsset(ocMeta, locPath);
                        ocMeta.Init();
                        ocMeta.InitActionToLoco();
                        ocMeta.InitHorseIK();
                       
                        var lj = new ActionAoWeightStruct();
                        lj.Dynamic_ActionWeightCurve = data.AoDynamic_ActionWeightCurve;
                        lj.Head_ActionWeightCurve = data.AoHead_ActionWeightCurve;
                        lj.Hand_ActionWeightCurve = data.AoHand_ActionWeightCurve;
                        lj.Spine1_ActionWeightCurve  = data.AoSpine1_ActionWeightCurve;
                        lj.Spine2_ActionWeightCurve  = data.AoSpine2_ActionWeightCurve;
                        lj.Spine_ActionWeightCurve   = data.AoSpine_ActionWeightCurve;
                        CopyActionAoWeightToMetaOc(ocMeta, ref ocMeta.AoWeightCurve, lj);
                        metaAsset.OcLayerWeightCollection.TryAdd(key, ocMeta);
                    }
                    EditorUtility.SetDirty(ocMeta);
                    AssetDatabase.SaveAssetIfDirty(ocMeta);
                }
                EditorUtility.SetDirty(metaAsset);
                AssetDatabase.SaveAssetIfDirty(metaAsset);
            }
        }
        
        
        private static void CopyActionAoWeightToMetaAdd(TpMetaAdditiveWeight meta, ref AnimCurveKey[] curves, ActionAoWeightStruct alw)
        {
            meta.Fix(ref curves);
            var assetPath = AssetDatabase.GetAssetPath(meta);
            var folderPath = Path.GetDirectoryName(assetPath);
            //在目标目录下创建一个字符串文件夹
            var strFilePath = Path.Combine(folderPath, AnimStrKey.localStrFile);
            AnimStrGroupRef animStrGroupRef = null;
            if (!File.Exists(strFilePath))
            {
                animStrGroupRef = ScriptableObject.CreateInstance<AnimStrGroupRef>();
                AssetDatabase.CreateAsset(animStrGroupRef, strFilePath);
            }
            else
            {
                animStrGroupRef = AssetDatabase.LoadAssetAtPath<AnimStrGroupRef>(strFilePath);
            }
            
            var curveFilePath = Path.Combine(folderPath, AnimCurveKey.localStrFile);
            AnimCurveGroupRef animCurveGroup = null;
            if (!File.Exists(curveFilePath))
            {
                animCurveGroup = ScriptableObject.CreateInstance<AnimCurveGroupRef>();
                AssetDatabase.CreateAsset(animCurveGroup, curveFilePath);
            }
            else
            {
                animCurveGroup = AssetDatabase.LoadAssetAtPath<AnimCurveGroupRef>(curveFilePath);
            }

            var bip01 = new AnimationCurve();
            bip01.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01, bip01,animCurveGroup);
            var Bip01Pelvis = new AnimationCurve();
            Bip01Pelvis.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Pelvis, Bip01Pelvis,animCurveGroup);
            var Bip01LThigh = new AnimationCurve();
            Bip01LThigh.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LThigh, Bip01LThigh,animCurveGroup);
            var Bip01LCalf = new AnimationCurve();
            Bip01LCalf.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LCalf, Bip01LCalf, animCurveGroup);
            var Bip01RThigh = new AnimationCurve();
            Bip01RThigh.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RThigh, Bip01RThigh,animCurveGroup);
            var Bip01RCalf = new AnimationCurve();
            Bip01RCalf.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RCalf, Bip01RCalf,animCurveGroup);
            var Bip01Spine = new AnimationCurve();
            Bip01Spine.CopyFrom(alw.Spine_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Spine, Bip01Spine,animCurveGroup);
            var Bip01Spine1 = new AnimationCurve();
            Bip01Spine1.CopyFrom(alw.Spine1_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Spine1,Bip01Spine1,animCurveGroup);
            var Bip01Spine2 = new AnimationCurve();
            Bip01Spine2.CopyFrom(alw.Spine2_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Spine2, Bip01Spine2,animCurveGroup);
            var Bip01LClavicle = new AnimationCurve();
            Bip01LClavicle.CopyFrom(alw.Hand_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LClavicle, Bip01LClavicle,animCurveGroup);
            var Bip01Neck = new AnimationCurve();
            Bip01Neck.CopyFrom(alw.Head_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Neck, Bip01Neck,animCurveGroup);
            var Bip01Head = new AnimationCurve();
            Bip01Head.CopyFrom(alw.Head_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Head, Bip01Head,animCurveGroup);
            var Bip01RClavicle = new AnimationCurve();
            Bip01RClavicle.CopyFrom(alw.Hand_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RClavicle, Bip01RClavicle,animCurveGroup);
            var BaseWeaponLocator = new AnimationCurve();
            BaseWeaponLocator.CopyFrom(alw.Hand_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.BaseWeaponLocator, BaseWeaponLocator,animCurveGroup);
            var Bip01LUpperArm = new AnimationCurve();
            Bip01LUpperArm.CopyFrom(alw.Hand_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LUpperArm, Bip01LUpperArm,animCurveGroup);
            var Bip01LForearm = new AnimationCurve();
            Bip01LForearm.CopyFrom(alw.Hand_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LForearm, Bip01LForearm,animCurveGroup);
            var Bip01LHand = new AnimationCurve();
            Bip01LHand.CopyFrom(alw.Hand_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LHand, Bip01LHand,animCurveGroup);
            var Bip01RUpperArm = new AnimationCurve();
            Bip01RUpperArm.CopyFrom(alw.Hand_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RUpperArm, Bip01RUpperArm,animCurveGroup);
            var Bip01RForearm = new AnimationCurve();
            Bip01RForearm.CopyFrom(alw.Hand_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RForearm, Bip01RForearm,animCurveGroup);
            var Bip01RHand = new AnimationCurve();
            Bip01RHand.CopyFrom(alw.Hand_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RHand, Bip01RHand,animCurveGroup);
            
            EditorUtility.SetDirty(animCurveGroup);
            EditorUtility.SetDirty(animStrGroupRef);
        }
        
         private static void CopyActionAoWeightToMetaOc(TpMetaOverrideWeight meta, ref AnimCurveKey[] curves, ActionAoWeightStruct alw)
        {
            meta.Fix(ref curves);
            var assetPath = AssetDatabase.GetAssetPath(meta);
            var folderPath = Path.GetDirectoryName(assetPath);
            //在目标目录下创建一个字符串文件夹
            var strFilePath = Path.Combine(folderPath, AnimStrKey.localStrFile);
            AnimStrGroupRef animStrGroupRef = null;
            if (!File.Exists(strFilePath))
            {
                animStrGroupRef = ScriptableObject.CreateInstance<AnimStrGroupRef>();
                AssetDatabase.CreateAsset(animStrGroupRef, strFilePath);
            }
            else
            {
                animStrGroupRef = AssetDatabase.LoadAssetAtPath<AnimStrGroupRef>(strFilePath);
            }
            
            var curveFilePath = Path.Combine(folderPath, AnimCurveKey.localStrFile);
            AnimCurveGroupRef animCurveGroup = null;
            if (!File.Exists(curveFilePath))
            {
                animCurveGroup = ScriptableObject.CreateInstance<AnimCurveGroupRef>();
                AssetDatabase.CreateAsset(animCurveGroup, curveFilePath);
            }
            else
            {
                animCurveGroup = AssetDatabase.LoadAssetAtPath<AnimCurveGroupRef>(curveFilePath);
            }

            var bip01 = new AnimationCurve();
            bip01.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01, bip01,animCurveGroup);
            var Bip01Pelvis = new AnimationCurve();
            Bip01Pelvis.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Pelvis, Bip01Pelvis,animCurveGroup);
            var Bip01LThigh = new AnimationCurve();
            Bip01LThigh.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LThigh, Bip01LThigh,animCurveGroup);
            var Bip01LCalf = new AnimationCurve();
            Bip01LCalf.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LCalf, Bip01LCalf, animCurveGroup);
            var Bip01RThigh = new AnimationCurve();
            Bip01RThigh.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RThigh, Bip01RThigh,animCurveGroup);
            var Bip01RCalf = new AnimationCurve();
            Bip01RCalf.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RCalf, Bip01RCalf,animCurveGroup);
            var Bip01Spine = new AnimationCurve();
            Bip01Spine.CopyFrom(alw.Spine_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Spine, Bip01Spine,animCurveGroup);
            var Bip01Spine1 = new AnimationCurve();
            Bip01Spine1.CopyFrom(alw.Spine1_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Spine1,Bip01Spine1,animCurveGroup);
            var Bip01Spine2 = new AnimationCurve();
            Bip01Spine2.CopyFrom(alw.Spine2_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Spine2, Bip01Spine2,animCurveGroup);
            var Bip01LClavicle = new AnimationCurve();
            Bip01LClavicle.CopyFrom(alw.Hand_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LClavicle, Bip01LClavicle,animCurveGroup);
            var Bip01Neck = new AnimationCurve();
            Bip01Neck.CopyFrom(alw.Head_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Neck, Bip01Neck,animCurveGroup);
            var Bip01Head = new AnimationCurve();
            Bip01Head.CopyFrom(alw.Head_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Head, Bip01Head,animCurveGroup);
            var Bip01RClavicle = new AnimationCurve();
            Bip01RClavicle.CopyFrom(alw.Hand_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RClavicle, Bip01RClavicle,animCurveGroup);
            var BaseWeaponLocator = new AnimationCurve();
            BaseWeaponLocator.CopyFrom(alw.Hand_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.BaseWeaponLocator, BaseWeaponLocator,animCurveGroup);
            var Bip01LUpperArm = new AnimationCurve();
            Bip01LUpperArm.CopyFrom(alw.Hand_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LUpperArm, Bip01LUpperArm,animCurveGroup);
            var Bip01LForearm = new AnimationCurve();
            Bip01LForearm.CopyFrom(alw.Hand_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LForearm, Bip01LForearm,animCurveGroup);
            var Bip01LHand = new AnimationCurve();
            Bip01LHand.CopyFrom(alw.Hand_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LHand, Bip01LHand,animCurveGroup);
            var Bip01RUpperArm = new AnimationCurve();
            Bip01RUpperArm.CopyFrom(alw.Hand_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RUpperArm, Bip01RUpperArm,animCurveGroup);
            var Bip01RForearm = new AnimationCurve();
            Bip01RForearm.CopyFrom(alw.Hand_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RForearm, Bip01RForearm,animCurveGroup);
            var Bip01RHand = new AnimationCurve();
            Bip01RHand.CopyFrom(alw.Hand_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RHand, Bip01RHand,animCurveGroup);
            
            EditorUtility.SetDirty(animCurveGroup);
            EditorUtility.SetDirty(animStrGroupRef);
        }
        

        /// <summary>
        /// step:2
        /// </summary>
        private static void CreateActionToLoco()
        {
            CreateActionToLocoAdd();
            CreateActionToLocoOc();
        }
        
        public static void CreateActionToLocoAdd()
        {
            // Add
            //找当前武器，所有的片段，是否有导出actionloco的配置
            var allTpTemplate = AssetDatabase.FindAssets("t:TpClipSettingMeta",
                new string[] {"Assets/SocClientRes/Weapon"}); //
            foreach (var settingGuid in allTpTemplate)
            {
                var filepath = AssetDatabase.GUIDToAssetPath(settingGuid);
                var metaAsset = AssetDatabase.LoadAssetAtPath<TpClipSettingMeta>(filepath);
                //create asset
                var subPath = filepath.Substring(0, filepath.LastIndexOf('/'));
                var applyToAnimatorGuid = AssetDatabase.FindAssets("t:AnimEditorRule_ApplyToAnimator",
                    new string[] {subPath}); 
                if(applyToAnimatorGuid.Length != 1)
                    continue;
                //收集add层的所有的state的apply的片段
                var applyToAnimatorPath = AssetDatabase.GUIDToAssetPath(applyToAnimatorGuid[0]);
                var applyToAnimator = AssetDatabase.LoadAssetAtPath<AnimEditorRule_ApplyToAnimator>(applyToAnimatorPath);
                var controller = applyToAnimator.CurrentCtrl;
                var clipOverrides = new List<KeyValuePair<AnimationClip, AnimationClip>>();
                controller.GetOverrides(clipOverrides);
                var addStateTypeToClip = new SerializableDictionary<AnimParametersTp.EAdditiveLayer, System.Collections.Generic.List<AnimationClip>>();
                var addStateTypeToOcClip = new SerializableDictionary<AnimParametersTp.EAdditiveLayer, System.Collections.Generic.List<AnimationClip>>();
                var headName = "";
                var ac = controller.runtimeAnimatorController as AnimatorController;
                AnimEditorRuleUtils.ProcessLayerClipWithType<AnimParametersTp.EAdditiveLayer>(ac.layers[AnimParametersTp.AdditiveLayer_LayerIndex].stateMachine, addStateTypeToClip, ref headName);
                foreach (var item in addStateTypeToClip)
                {
                    var list = item.Value;
                    foreach (var baseClip in list)
                    {
                        foreach (var overridePair in clipOverrides)
                        {
                            if (overridePair.Key == baseClip)
                            {
                                if (overridePair.Value != null)
                                {
                                     if(addStateTypeToOcClip.TryGetValue(item.Key, out var ocClipList))
                                     {
                                         ocClipList.Add(overridePair.Value);
                                     }
                                     else
                                     {
                                         ocClipList = new List<AnimationClip>();
                                         ocClipList.Add(overridePair.Value);
                                         addStateTypeToOcClip.Add(item.Key, ocClipList);
                                     }
                                }
                                break;
                            }
                        }
                    }
                }
                
                //找到所有片段的所对应的AnimEditorRule_AddCurveEventData
                var actionLocomotionWeightDic = new Dictionary<AnimParametersTp.EAdditiveLayer,Anim_DataInfo>();
                foreach (var item in addStateTypeToOcClip)
                {
                    var list = item.Value;
                    var have = false;
                    foreach (var clip in list)
                    {
                        var clipPath = AssetDatabase.GetAssetPath(clip);
                        //父级
                        var clipSubPath = clipPath.Substring(0, clipPath.LastIndexOf("/"));
                        clipSubPath = clipSubPath.Substring(0, clipSubPath.LastIndexOf("/"));
                        //找addcurveeventdata
                        var addCurveGuid = AssetDatabase.FindAssets("t:AnimEditorRule_AddCurveEventData",
                            new string[] {clipSubPath}); 
                        if(addCurveGuid.Length != 1)
                            continue;
                        //收集oc层的所有的state的apply的片段
                        var addCurvePath = AssetDatabase.GUIDToAssetPath(addCurveGuid[0]);
                        var addCurve = AssetDatabase.LoadAssetAtPath<AnimEditorRule_AddCurveEventData>(addCurvePath);
                        foreach (var data in addCurve.DataDicts)
                        {
                            var keyName = clip.name.Substring(clip.name.IndexOf("_") + 1);
                            if (keyName.Equals(data.BaseKeyWord))
                            {
                                if (data.NeedActionLocomotionWeight)
                                {
                                    actionLocomotionWeightDic.TryAdd(item.Key, data);
                                    //存储相关的数据
                                    have = true;
                                }
                                break;
                            }
                        }
                        if(have)
                            break;
                    }
                }
                
                //将当前的actionlocoweight生成在新的序列化文件里
                foreach (var add in metaAsset.AddLayerWeightCollection)
                {
                    var key = add.Key;
                    var item = add.Value;
                    var itemPath = AssetDatabase.GetAssetPath(item);
                    var addMeta = AssetDatabase.LoadAssetAtPath<TpMetaAdditiveWeight>(itemPath);
                    if (actionLocomotionWeightDic.TryGetValue(key, out var data))
                    {
                        //赋值
                        var lj = new ActionLocomotionWeightStruct();
                        lj.Dynamic_ActionWeightCurve = data.JogDynamic_ActionWeightCurve;
                        lj.Head_ActionWeightCurve = data.JogHead_ActionWeightCurve;
                        lj.LClavicle_ActionWeightCurve = data.JogLClavicle_ActionWeightCurve;
                        lj.LeftArm_ActionWeightCurve  = data.JogLeftArm_ActionWeightCurve;
                        lj.RClavicle_ActionWeightCurve = data.JogRClavicle_ActionWeightCurve;
                        lj.RightArm_ActionWeightCurve   = data.JogRightArm_ActionWeightCurve;
                        lj.Spine1_ActionWeightCurve  = data.JogSpine1_ActionWeightCurve;
                        lj.Spine2_ActionWeightCurve  = data.JogSpine2_ActionWeightCurve;
                        lj.Spine_ActionWeightCurve   = data.JogSpine_ActionWeightCurve;
                        lj.Weapon_ActionWeightCurve = data.JogWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMetaAdd(addMeta, ref addMeta.LJogWeightCurve, lj);
                        var lju = new ActionLocomotionWeightStruct();
                        lju.Dynamic_ActionWeightCurve = data.JumpDynamic_ActionWeightCurve;
                        lju.Head_ActionWeightCurve = data.JumpHead_ActionWeightCurve;
                        lju.LClavicle_ActionWeightCurve = data.JumpLClavicle_ActionWeightCurve;
                        lju.LeftArm_ActionWeightCurve  = data.JumpLeftArm_ActionWeightCurve;
                        lju.RClavicle_ActionWeightCurve = data.JumpRClavicle_ActionWeightCurve;
                        lju.RightArm_ActionWeightCurve   = data.JumpRightArm_ActionWeightCurve;
                        lju.Spine1_ActionWeightCurve  = data.JumpSpine1_ActionWeightCurve;
                        lju.Spine2_ActionWeightCurve  = data.JumpSpine2_ActionWeightCurve;
                        lju.Spine_ActionWeightCurve   = data.JumpSpine_ActionWeightCurve;
                        lju.Weapon_ActionWeightCurve = data.JumpWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMetaAdd(addMeta, ref addMeta.LJumpWeightCurve, lju);
                        var ls = new ActionLocomotionWeightStruct();
                        ls.Dynamic_ActionWeightCurve = data.SprintDynamic_ActionWeightCurve;
                        ls.Head_ActionWeightCurve = data.SprintHead_ActionWeightCurve;
                        ls.LClavicle_ActionWeightCurve = data.SprintLClavicle_ActionWeightCurve;
                        ls.LeftArm_ActionWeightCurve  = data.SprintLeftArm_ActionWeightCurve;
                        ls.RClavicle_ActionWeightCurve = data.SprintRClavicle_ActionWeightCurve;
                        ls.RightArm_ActionWeightCurve   = data.SprintRightArm_ActionWeightCurve;
                        ls.Spine1_ActionWeightCurve  = data.SprintSpine1_ActionWeightCurve;
                        ls.Spine2_ActionWeightCurve  = data.SprintSpine2_ActionWeightCurve;
                        ls.Spine_ActionWeightCurve   = data.SprintSpine_ActionWeightCurve;
                        ls.Weapon_ActionWeightCurve = data.SprintWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMetaAdd(addMeta, ref addMeta.LSprintWeightCurve, ls);
                        var lc = new ActionLocomotionWeightStruct();
                        lc.Dynamic_ActionWeightCurve = data.CrouchDynamic_ActionWeightCurve;
                        lc.Head_ActionWeightCurve = data.CrouchHead_ActionWeightCurve;
                        lc.LClavicle_ActionWeightCurve = data.CrouchLClavicle_ActionWeightCurve;
                        lc.LeftArm_ActionWeightCurve  = data.CrouchLeftArm_ActionWeightCurve;
                        lc.RClavicle_ActionWeightCurve = data.CrouchRClavicle_ActionWeightCurve;
                        lc.RightArm_ActionWeightCurve   = data.CrouchRightArm_ActionWeightCurve;
                        lc.Spine1_ActionWeightCurve  = data.CrouchSpine1_ActionWeightCurve;
                        lc.Spine2_ActionWeightCurve  = data.CrouchSpine2_ActionWeightCurve;
                        lc.Spine_ActionWeightCurve   = data.CrouchSpine_ActionWeightCurve;
                        lc.Weapon_ActionWeightCurve = data.CrouchWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMetaAdd(addMeta, ref addMeta.LIdleCrouchWeightCurve, lc);
                        var ll = new ActionLocomotionWeightStruct();
                        ll.Dynamic_ActionWeightCurve = data.LadderDynamic_ActionWeightCurve;
                        ll.Head_ActionWeightCurve = data.LadderHead_ActionWeightCurve;
                        ll.LClavicle_ActionWeightCurve = data.LadderLClavicle_ActionWeightCurve;
                        ll.LeftArm_ActionWeightCurve  = data.LadderLeftArm_ActionWeightCurve;
                        ll.RClavicle_ActionWeightCurve = data.LadderRClavicle_ActionWeightCurve;
                        ll.RightArm_ActionWeightCurve   = data.LadderRightArm_ActionWeightCurve;
                        ll.Spine1_ActionWeightCurve  = data.LadderSpine1_ActionWeightCurve;
                        ll.Spine2_ActionWeightCurve  = data.LadderSpine2_ActionWeightCurve;
                        ll.Spine_ActionWeightCurve   = data.LadderSpine_ActionWeightCurve;
                        ll.Weapon_ActionWeightCurve = data.LadderWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMetaAdd(addMeta, ref addMeta.LLadderWeightCurve, ll);
                        var lsi = new ActionLocomotionWeightStruct();
                        lsi.Dynamic_ActionWeightCurve = data.SwimIdleDynamic_ActionWeightCurve;
                        lsi.Head_ActionWeightCurve = data.SwimIdleHead_ActionWeightCurve;
                        lsi.LClavicle_ActionWeightCurve = data.SwimIdleLClavicle_ActionWeightCurve;
                        lsi.LeftArm_ActionWeightCurve  = data.SwimIdleLeftArm_ActionWeightCurve;
                        lsi.RClavicle_ActionWeightCurve = data.SwimIdleRClavicle_ActionWeightCurve;
                        lsi.RightArm_ActionWeightCurve   = data.SwimIdleRightArm_ActionWeightCurve;
                        lsi.Spine1_ActionWeightCurve  = data.SwimIdleSpine1_ActionWeightCurve;
                        lsi.Spine2_ActionWeightCurve  = data.SwimIdleSpine2_ActionWeightCurve;
                        lsi.Spine_ActionWeightCurve   = data.SwimIdleSpine_ActionWeightCurve;
                        lsi.Weapon_ActionWeightCurve = data.SwimIdleWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMetaAdd(addMeta, ref addMeta.LSwimIdleWeightCurve, lsi);
                        var lsw = new ActionLocomotionWeightStruct();
                        lsw.Dynamic_ActionWeightCurve = data.SwimDynamic_ActionWeightCurve;
                        lsw.Head_ActionWeightCurve = data.SwimHead_ActionWeightCurve;
                        lsw.LClavicle_ActionWeightCurve = data.SwimLClavicle_ActionWeightCurve;
                        lsw.LeftArm_ActionWeightCurve  = data.SwimLeftArm_ActionWeightCurve;
                        lsw.RClavicle_ActionWeightCurve = data.SwimRClavicle_ActionWeightCurve;
                        lsw.RightArm_ActionWeightCurve   = data.SwimRightArm_ActionWeightCurve;
                        lsw.Spine1_ActionWeightCurve  = data.SwimSpine1_ActionWeightCurve;
                        lsw.Spine2_ActionWeightCurve  = data.SwimSpine2_ActionWeightCurve;
                        lsw.Spine_ActionWeightCurve   = data.SwimSpine_ActionWeightCurve;
                        lsw.Weapon_ActionWeightCurve = data.SwimWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMetaAdd(addMeta, ref addMeta.LSwimMoveWeightCurve, lsw);
                        actionLocomotionWeightDic.Remove(key);
                    }
                    else
                    {
                        addMeta.InitActionToLoco();
                    }
                    EditorUtility.SetDirty(addMeta);
                    AssetDatabase.SaveAssetIfDirty(addMeta);
                }

                foreach (var left in actionLocomotionWeightDic)
                {
                    var key = left.Key;
                    var data = left.Value;
                    var locPath = subPath + "/TpMetaAdWeight_"+key.ToString()+".asset";
                    var addMeta = AssetDatabase.LoadAssetAtPath<TpMetaAdditiveWeight>(locPath);
                    if (addMeta == null)
                    {
                        addMeta = ScriptableObject.CreateInstance<TpMetaAdditiveWeight>();
                        AssetDatabase.CreateAsset(addMeta, locPath);
                        //赋值
                        var lj = new ActionLocomotionWeightStruct();
                        lj.Dynamic_ActionWeightCurve = data.JogDynamic_ActionWeightCurve;
                        lj.Head_ActionWeightCurve = data.JogHead_ActionWeightCurve;
                        lj.LClavicle_ActionWeightCurve = data.JogLClavicle_ActionWeightCurve;
                        lj.LeftArm_ActionWeightCurve  = data.JogLeftArm_ActionWeightCurve;
                        lj.RClavicle_ActionWeightCurve = data.JogRClavicle_ActionWeightCurve;
                        lj.RightArm_ActionWeightCurve   = data.JogRightArm_ActionWeightCurve;
                        lj.Spine1_ActionWeightCurve  = data.JogSpine1_ActionWeightCurve;
                        lj.Spine2_ActionWeightCurve  = data.JogSpine2_ActionWeightCurve;
                        lj.Spine_ActionWeightCurve   = data.JogSpine_ActionWeightCurve;
                        lj.Weapon_ActionWeightCurve = data.JogWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMetaAdd(addMeta, ref addMeta.LJogWeightCurve, lj);
                        var lju = new ActionLocomotionWeightStruct();
                        lju.Dynamic_ActionWeightCurve = data.JumpDynamic_ActionWeightCurve;
                        lju.Head_ActionWeightCurve = data.JumpHead_ActionWeightCurve;
                        lju.LClavicle_ActionWeightCurve = data.JumpLClavicle_ActionWeightCurve;
                        lju.LeftArm_ActionWeightCurve  = data.JumpLeftArm_ActionWeightCurve;
                        lju.RClavicle_ActionWeightCurve = data.JumpRClavicle_ActionWeightCurve;
                        lju.RightArm_ActionWeightCurve   = data.JumpRightArm_ActionWeightCurve;
                        lju.Spine1_ActionWeightCurve  = data.JumpSpine1_ActionWeightCurve;
                        lju.Spine2_ActionWeightCurve  = data.JumpSpine2_ActionWeightCurve;
                        lju.Spine_ActionWeightCurve   = data.JumpSpine_ActionWeightCurve;
                        lju.Weapon_ActionWeightCurve = data.JumpWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMetaAdd(addMeta, ref addMeta.LJumpWeightCurve, lju);
                        var ls = new ActionLocomotionWeightStruct();
                        ls.Dynamic_ActionWeightCurve = data.SprintDynamic_ActionWeightCurve;
                        ls.Head_ActionWeightCurve = data.SprintHead_ActionWeightCurve;
                        ls.LClavicle_ActionWeightCurve = data.SprintLClavicle_ActionWeightCurve;
                        ls.LeftArm_ActionWeightCurve  = data.SprintLeftArm_ActionWeightCurve;
                        ls.RClavicle_ActionWeightCurve = data.SprintRClavicle_ActionWeightCurve;
                        ls.RightArm_ActionWeightCurve   = data.SprintRightArm_ActionWeightCurve;
                        ls.Spine1_ActionWeightCurve  = data.SprintSpine1_ActionWeightCurve;
                        ls.Spine2_ActionWeightCurve  = data.SprintSpine2_ActionWeightCurve;
                        ls.Spine_ActionWeightCurve   = data.SprintSpine_ActionWeightCurve;
                        ls.Weapon_ActionWeightCurve = data.SprintWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMetaAdd(addMeta, ref addMeta.LSprintWeightCurve, ls);
                        var lc = new ActionLocomotionWeightStruct();
                        lc.Dynamic_ActionWeightCurve = data.CrouchDynamic_ActionWeightCurve;
                        lc.Head_ActionWeightCurve = data.CrouchHead_ActionWeightCurve;
                        lc.LClavicle_ActionWeightCurve = data.CrouchLClavicle_ActionWeightCurve;
                        lc.LeftArm_ActionWeightCurve  = data.CrouchLeftArm_ActionWeightCurve;
                        lc.RClavicle_ActionWeightCurve = data.CrouchRClavicle_ActionWeightCurve;
                        lc.RightArm_ActionWeightCurve   = data.CrouchRightArm_ActionWeightCurve;
                        lc.Spine1_ActionWeightCurve  = data.CrouchSpine1_ActionWeightCurve;
                        lc.Spine2_ActionWeightCurve  = data.CrouchSpine2_ActionWeightCurve;
                        lc.Spine_ActionWeightCurve   = data.CrouchSpine_ActionWeightCurve;
                        lc.Weapon_ActionWeightCurve = data.CrouchWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMetaAdd(addMeta, ref addMeta.LIdleCrouchWeightCurve, lc);
                        var ll = new ActionLocomotionWeightStruct();
                        ll.Dynamic_ActionWeightCurve = data.LadderDynamic_ActionWeightCurve;
                        ll.Head_ActionWeightCurve = data.LadderHead_ActionWeightCurve;
                        ll.LClavicle_ActionWeightCurve = data.LadderLClavicle_ActionWeightCurve;
                        ll.LeftArm_ActionWeightCurve  = data.LadderLeftArm_ActionWeightCurve;
                        ll.RClavicle_ActionWeightCurve = data.LadderRClavicle_ActionWeightCurve;
                        ll.RightArm_ActionWeightCurve   = data.LadderRightArm_ActionWeightCurve;
                        ll.Spine1_ActionWeightCurve  = data.LadderSpine1_ActionWeightCurve;
                        ll.Spine2_ActionWeightCurve  = data.LadderSpine2_ActionWeightCurve;
                        ll.Spine_ActionWeightCurve   = data.LadderSpine_ActionWeightCurve;
                        ll.Weapon_ActionWeightCurve = data.LadderWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMetaAdd(addMeta, ref addMeta.LLadderWeightCurve, ll);
                        var lsi = new ActionLocomotionWeightStruct();
                        lsi.Dynamic_ActionWeightCurve = data.SwimIdleDynamic_ActionWeightCurve;
                        lsi.Head_ActionWeightCurve = data.SwimIdleHead_ActionWeightCurve;
                        lsi.LClavicle_ActionWeightCurve = data.SwimIdleLClavicle_ActionWeightCurve;
                        lsi.LeftArm_ActionWeightCurve  = data.SwimIdleLeftArm_ActionWeightCurve;
                        lsi.RClavicle_ActionWeightCurve = data.SwimIdleRClavicle_ActionWeightCurve;
                        lsi.RightArm_ActionWeightCurve   = data.SwimIdleRightArm_ActionWeightCurve;
                        lsi.Spine1_ActionWeightCurve  = data.SwimIdleSpine1_ActionWeightCurve;
                        lsi.Spine2_ActionWeightCurve  = data.SwimIdleSpine2_ActionWeightCurve;
                        lsi.Spine_ActionWeightCurve   = data.SwimIdleSpine_ActionWeightCurve;
                        lsi.Weapon_ActionWeightCurve = data.SwimIdleWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMetaAdd(addMeta, ref addMeta.LSwimIdleWeightCurve, lsi);
                        var lsw = new ActionLocomotionWeightStruct();
                        lsw.Dynamic_ActionWeightCurve = data.SwimDynamic_ActionWeightCurve;
                        lsw.Head_ActionWeightCurve = data.SwimHead_ActionWeightCurve;
                        lsw.LClavicle_ActionWeightCurve = data.SwimLClavicle_ActionWeightCurve;
                        lsw.LeftArm_ActionWeightCurve  = data.SwimLeftArm_ActionWeightCurve;
                        lsw.RClavicle_ActionWeightCurve = data.SwimRClavicle_ActionWeightCurve;
                        lsw.RightArm_ActionWeightCurve   = data.SwimRightArm_ActionWeightCurve;
                        lsw.Spine1_ActionWeightCurve  = data.SwimSpine1_ActionWeightCurve;
                        lsw.Spine2_ActionWeightCurve  = data.SwimSpine2_ActionWeightCurve;
                        lsw.Spine_ActionWeightCurve   = data.SwimSpine_ActionWeightCurve;
                        lsw.Weapon_ActionWeightCurve = data.SwimWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMetaAdd(addMeta, ref addMeta.LSwimMoveWeightCurve, lsw);
                       
                        addMeta.InitAo();
                        addMeta.InitHorseIK();
                        
                        metaAsset.AddLayerWeightCollection.TryAdd(key, addMeta);
                    }
                    EditorUtility.SetDirty(addMeta);
                    AssetDatabase.SaveAssetIfDirty(addMeta);
                }
                EditorUtility.SetDirty(metaAsset);
                AssetDatabase.SaveAssetIfDirty(metaAsset);
            }
        }
      
        public static void CreateActionToLocoOc()
        {
            // OC
            //找当前武器，所有的片段，是否有导出actionloco的配置
            var allTpTemplate = AssetDatabase.FindAssets("t:TpClipSettingMeta",
                new string[] {"Assets/SocClientRes/Weapon"}); //
            foreach (var settingGuid in allTpTemplate)
            {
                var filepath = AssetDatabase.GUIDToAssetPath(settingGuid);
                var metaAsset = AssetDatabase.LoadAssetAtPath<TpClipSettingMeta>(filepath);
                //create asset
                var subPath = filepath.Substring(0, filepath.LastIndexOf('/'));
                var applyToAnimatorGuid = AssetDatabase.FindAssets("t:AnimEditorRule_ApplyToAnimator",
                    new string[] {subPath}); 
                if(applyToAnimatorGuid.Length != 1)
                    continue;
                //收集oc层的所有的state的apply的片段
                var applyToAnimatorPath = AssetDatabase.GUIDToAssetPath(applyToAnimatorGuid[0]);
                var applyToAnimator = AssetDatabase.LoadAssetAtPath<AnimEditorRule_ApplyToAnimator>(applyToAnimatorPath);
                var controller = applyToAnimator.CurrentCtrl;
                var clipOverrides = new List<KeyValuePair<AnimationClip, AnimationClip>>();
                controller.GetOverrides(clipOverrides);
                var overrideStateTypeToClip = new SerializableDictionary<AnimParametersTp.EOverrideLayer, System.Collections.Generic.List<AnimationClip>>();
                var overrideStateTypeToOcClip = new SerializableDictionary<AnimParametersTp.EOverrideLayer, System.Collections.Generic.List<AnimationClip>>();
                var headName = "";
                var ac = controller.runtimeAnimatorController as AnimatorController;
                AnimEditorRuleUtils.ProcessLayerClipWithType<AnimParametersTp.EOverrideLayer>(ac.layers[AnimParametersTp.OverrideLayer_LayerIndex].stateMachine, overrideStateTypeToClip, ref headName);
                foreach (var item in overrideStateTypeToClip)
                {
                    var list = item.Value;
                    foreach (var baseClip in list)
                    {
                        foreach (var overridePair in clipOverrides)
                        {
                            if (overridePair.Key == baseClip)
                            {
                                if (overridePair.Value != null)
                                {
                                     if(overrideStateTypeToOcClip.TryGetValue(item.Key, out var ocClipList))
                                     {
                                         ocClipList.Add(overridePair.Value);
                                     }
                                     else
                                     {
                                         ocClipList = new List<AnimationClip>();
                                         ocClipList.Add(overridePair.Value);
                                         overrideStateTypeToOcClip.Add(item.Key, ocClipList);
                                     }
                                }
                                break;
                            }
                        }
                    }
                }
                
                //找到所有片段的所对应的AnimEditorRule_AddCurveEventData
                var actionLocomotionWeightDic = new Dictionary<AnimParametersTp.EOverrideLayer,Anim_DataInfo>();
                foreach (var item in overrideStateTypeToOcClip)
                {
                    var list = item.Value;
                    var have = false;
                    foreach (var clip in list)
                    {
                        var clipPath = AssetDatabase.GetAssetPath(clip);
                        //父级
                        var clipSubPath = clipPath.Substring(0, clipPath.LastIndexOf("/"));
                        clipSubPath = clipSubPath.Substring(0, clipSubPath.LastIndexOf("/"));
                        //找addcurveeventdata
                        var addCurveGuid = AssetDatabase.FindAssets("t:AnimEditorRule_AddCurveEventData",
                            new string[] {clipSubPath}); 
                        if(addCurveGuid.Length != 1)
                            continue;
                        //收集oc层的所有的state的apply的片段
                        var addCurvePath = AssetDatabase.GUIDToAssetPath(addCurveGuid[0]);
                        var addCurve = AssetDatabase.LoadAssetAtPath<AnimEditorRule_AddCurveEventData>(addCurvePath);
                        foreach (var data in addCurve.DataDicts)
                        {
                            var keyName = clip.name.Substring(clip.name.IndexOf("_") + 1);
                            if (keyName.Equals(data.BaseKeyWord))
                            {
                                if (data.NeedActionLocomotionWeight)
                                {
                                    actionLocomotionWeightDic.TryAdd(item.Key, data);
                                    //存储相关的数据
                                    have = true;
                                }
                                break;
                            }
                        }
                        if(have)
                            break;
                    }
                }
                
                //将当前的actionlocoweight生成在新的序列化文件里
                foreach (var oc in metaAsset.OcLayerWeightCollection)
                {
                    var key = oc.Key;
                    var item = oc.Value;
                    var itemPath = AssetDatabase.GetAssetPath(item);
                    var ocMeta = AssetDatabase.LoadAssetAtPath<TpMetaOverrideWeight>(itemPath);
                    if (actionLocomotionWeightDic.TryGetValue(key, out var data))
                    {
                        //赋值
                        var lj = new ActionLocomotionWeightStruct();
                        lj.Dynamic_ActionWeightCurve = data.JogDynamic_ActionWeightCurve;
                        lj.Head_ActionWeightCurve = data.JogHead_ActionWeightCurve;
                        lj.LClavicle_ActionWeightCurve = data.JogLClavicle_ActionWeightCurve;
                        lj.LeftArm_ActionWeightCurve  = data.JogLeftArm_ActionWeightCurve;
                        lj.RClavicle_ActionWeightCurve = data.JogRClavicle_ActionWeightCurve;
                        lj.RightArm_ActionWeightCurve   = data.JogRightArm_ActionWeightCurve;
                        lj.Spine1_ActionWeightCurve  = data.JogSpine1_ActionWeightCurve;
                        lj.Spine2_ActionWeightCurve  = data.JogSpine2_ActionWeightCurve;
                        lj.Spine_ActionWeightCurve   = data.JogSpine_ActionWeightCurve;
                        lj.Weapon_ActionWeightCurve = data.JogWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMeta(ocMeta, ref ocMeta.LJogWeightCurve, lj);
                        var lju = new ActionLocomotionWeightStruct();
                        lju.Dynamic_ActionWeightCurve = data.JumpDynamic_ActionWeightCurve;
                        lju.Head_ActionWeightCurve = data.JumpHead_ActionWeightCurve;
                        lju.LClavicle_ActionWeightCurve = data.JumpLClavicle_ActionWeightCurve;
                        lju.LeftArm_ActionWeightCurve  = data.JumpLeftArm_ActionWeightCurve;
                        lju.RClavicle_ActionWeightCurve = data.JumpRClavicle_ActionWeightCurve;
                        lju.RightArm_ActionWeightCurve   = data.JumpRightArm_ActionWeightCurve;
                        lju.Spine1_ActionWeightCurve  = data.JumpSpine1_ActionWeightCurve;
                        lju.Spine2_ActionWeightCurve  = data.JumpSpine2_ActionWeightCurve;
                        lju.Spine_ActionWeightCurve   = data.JumpSpine_ActionWeightCurve;
                        lju.Weapon_ActionWeightCurve = data.JumpWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMeta(ocMeta, ref ocMeta.LJumpWeightCurve, lju);
                        var ls = new ActionLocomotionWeightStruct();
                        ls.Dynamic_ActionWeightCurve = data.SprintDynamic_ActionWeightCurve;
                        ls.Head_ActionWeightCurve = data.SprintHead_ActionWeightCurve;
                        ls.LClavicle_ActionWeightCurve = data.SprintLClavicle_ActionWeightCurve;
                        ls.LeftArm_ActionWeightCurve  = data.SprintLeftArm_ActionWeightCurve;
                        ls.RClavicle_ActionWeightCurve = data.SprintRClavicle_ActionWeightCurve;
                        ls.RightArm_ActionWeightCurve   = data.SprintRightArm_ActionWeightCurve;
                        ls.Spine1_ActionWeightCurve  = data.SprintSpine1_ActionWeightCurve;
                        ls.Spine2_ActionWeightCurve  = data.SprintSpine2_ActionWeightCurve;
                        ls.Spine_ActionWeightCurve   = data.SprintSpine_ActionWeightCurve;
                        ls.Weapon_ActionWeightCurve = data.SprintWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMeta(ocMeta, ref ocMeta.LSprintWeightCurve, ls);
                        var lc = new ActionLocomotionWeightStruct();
                        lc.Dynamic_ActionWeightCurve = data.CrouchDynamic_ActionWeightCurve;
                        lc.Head_ActionWeightCurve = data.CrouchHead_ActionWeightCurve;
                        lc.LClavicle_ActionWeightCurve = data.CrouchLClavicle_ActionWeightCurve;
                        lc.LeftArm_ActionWeightCurve  = data.CrouchLeftArm_ActionWeightCurve;
                        lc.RClavicle_ActionWeightCurve = data.CrouchRClavicle_ActionWeightCurve;
                        lc.RightArm_ActionWeightCurve   = data.CrouchRightArm_ActionWeightCurve;
                        lc.Spine1_ActionWeightCurve  = data.CrouchSpine1_ActionWeightCurve;
                        lc.Spine2_ActionWeightCurve  = data.CrouchSpine2_ActionWeightCurve;
                        lc.Spine_ActionWeightCurve   = data.CrouchSpine_ActionWeightCurve;
                        lc.Weapon_ActionWeightCurve = data.CrouchWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMeta(ocMeta, ref ocMeta.LIdleCrouchWeightCurve, lc);
                        var ll = new ActionLocomotionWeightStruct();
                        ll.Dynamic_ActionWeightCurve = data.LadderDynamic_ActionWeightCurve;
                        ll.Head_ActionWeightCurve = data.LadderHead_ActionWeightCurve;
                        ll.LClavicle_ActionWeightCurve = data.LadderLClavicle_ActionWeightCurve;
                        ll.LeftArm_ActionWeightCurve  = data.LadderLeftArm_ActionWeightCurve;
                        ll.RClavicle_ActionWeightCurve = data.LadderRClavicle_ActionWeightCurve;
                        ll.RightArm_ActionWeightCurve   = data.LadderRightArm_ActionWeightCurve;
                        ll.Spine1_ActionWeightCurve  = data.LadderSpine1_ActionWeightCurve;
                        ll.Spine2_ActionWeightCurve  = data.LadderSpine2_ActionWeightCurve;
                        ll.Spine_ActionWeightCurve   = data.LadderSpine_ActionWeightCurve;
                        ll.Weapon_ActionWeightCurve = data.LadderWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMeta(ocMeta, ref ocMeta.LLadderWeightCurve, ll);
                        var lsi = new ActionLocomotionWeightStruct();
                        lsi.Dynamic_ActionWeightCurve = data.SwimIdleDynamic_ActionWeightCurve;
                        lsi.Head_ActionWeightCurve = data.SwimIdleHead_ActionWeightCurve;
                        lsi.LClavicle_ActionWeightCurve = data.SwimIdleLClavicle_ActionWeightCurve;
                        lsi.LeftArm_ActionWeightCurve  = data.SwimIdleLeftArm_ActionWeightCurve;
                        lsi.RClavicle_ActionWeightCurve = data.SwimIdleRClavicle_ActionWeightCurve;
                        lsi.RightArm_ActionWeightCurve   = data.SwimIdleRightArm_ActionWeightCurve;
                        lsi.Spine1_ActionWeightCurve  = data.SwimIdleSpine1_ActionWeightCurve;
                        lsi.Spine2_ActionWeightCurve  = data.SwimIdleSpine2_ActionWeightCurve;
                        lsi.Spine_ActionWeightCurve   = data.SwimIdleSpine_ActionWeightCurve;
                        lsi.Weapon_ActionWeightCurve = data.SwimIdleWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMeta(ocMeta, ref ocMeta.LSwimIdleWeightCurve, lsi);
                        var lsw = new ActionLocomotionWeightStruct();
                        lsw.Dynamic_ActionWeightCurve = data.SwimDynamic_ActionWeightCurve;
                        lsw.Head_ActionWeightCurve = data.SwimHead_ActionWeightCurve;
                        lsw.LClavicle_ActionWeightCurve = data.SwimLClavicle_ActionWeightCurve;
                        lsw.LeftArm_ActionWeightCurve  = data.SwimLeftArm_ActionWeightCurve;
                        lsw.RClavicle_ActionWeightCurve = data.SwimRClavicle_ActionWeightCurve;
                        lsw.RightArm_ActionWeightCurve   = data.SwimRightArm_ActionWeightCurve;
                        lsw.Spine1_ActionWeightCurve  = data.SwimSpine1_ActionWeightCurve;
                        lsw.Spine2_ActionWeightCurve  = data.SwimSpine2_ActionWeightCurve;
                        lsw.Spine_ActionWeightCurve   = data.SwimSpine_ActionWeightCurve;
                        lsw.Weapon_ActionWeightCurve = data.SwimWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMeta(ocMeta, ref ocMeta.LSwimMoveWeightCurve, lsw);
                        actionLocomotionWeightDic.Remove(key);
                    }
                    else
                    {
                        ocMeta.InitActionToLoco();
                    }
                    EditorUtility.SetDirty(ocMeta);
                    AssetDatabase.SaveAssetIfDirty(ocMeta);
                }

                foreach (var left in actionLocomotionWeightDic)
                {
                    var key = left.Key;
                    var data = left.Value;
                    var locPath = subPath + "/TpMetaOcWeight_"+key.ToString()+".asset";
                    var ocMeta = AssetDatabase.LoadAssetAtPath<TpMetaOverrideWeight>(locPath);
                    if (ocMeta == null)
                    {
                        ocMeta = ScriptableObject.CreateInstance<TpMetaOverrideWeight>();
                        AssetDatabase.CreateAsset(ocMeta, locPath);
                        
                         //赋值
                        var lj = new ActionLocomotionWeightStruct();
                        lj.Dynamic_ActionWeightCurve = data.JogDynamic_ActionWeightCurve;
                        lj.Head_ActionWeightCurve = data.JogHead_ActionWeightCurve;
                        lj.LClavicle_ActionWeightCurve = data.JogLClavicle_ActionWeightCurve;
                        lj.LeftArm_ActionWeightCurve  = data.JogLeftArm_ActionWeightCurve;
                        lj.RClavicle_ActionWeightCurve = data.JogRClavicle_ActionWeightCurve;
                        lj.RightArm_ActionWeightCurve   = data.JogRightArm_ActionWeightCurve;
                        lj.Spine1_ActionWeightCurve  = data.JogSpine1_ActionWeightCurve;
                        lj.Spine2_ActionWeightCurve  = data.JogSpine2_ActionWeightCurve;
                        lj.Spine_ActionWeightCurve   = data.JogSpine_ActionWeightCurve;
                        lj.Weapon_ActionWeightCurve = data.JogWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMeta(ocMeta, ref ocMeta.LJogWeightCurve, lj);
                        var lju = new ActionLocomotionWeightStruct();
                        lju.Dynamic_ActionWeightCurve = data.JumpDynamic_ActionWeightCurve;
                        lju.Head_ActionWeightCurve = data.JumpHead_ActionWeightCurve;
                        lju.LClavicle_ActionWeightCurve = data.JumpLClavicle_ActionWeightCurve;
                        lju.LeftArm_ActionWeightCurve  = data.JumpLeftArm_ActionWeightCurve;
                        lju.RClavicle_ActionWeightCurve = data.JumpRClavicle_ActionWeightCurve;
                        lju.RightArm_ActionWeightCurve   = data.JumpRightArm_ActionWeightCurve;
                        lju.Spine1_ActionWeightCurve  = data.JumpSpine1_ActionWeightCurve;
                        lju.Spine2_ActionWeightCurve  = data.JumpSpine2_ActionWeightCurve;
                        lju.Spine_ActionWeightCurve   = data.JumpSpine_ActionWeightCurve;
                        lju.Weapon_ActionWeightCurve = data.JumpWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMeta(ocMeta, ref ocMeta.LJumpWeightCurve, lju);
                        var ls = new ActionLocomotionWeightStruct();
                        ls.Dynamic_ActionWeightCurve = data.SprintDynamic_ActionWeightCurve;
                        ls.Head_ActionWeightCurve = data.SprintHead_ActionWeightCurve;
                        ls.LClavicle_ActionWeightCurve = data.SprintLClavicle_ActionWeightCurve;
                        ls.LeftArm_ActionWeightCurve  = data.SprintLeftArm_ActionWeightCurve;
                        ls.RClavicle_ActionWeightCurve = data.SprintRClavicle_ActionWeightCurve;
                        ls.RightArm_ActionWeightCurve   = data.SprintRightArm_ActionWeightCurve;
                        ls.Spine1_ActionWeightCurve  = data.SprintSpine1_ActionWeightCurve;
                        ls.Spine2_ActionWeightCurve  = data.SprintSpine2_ActionWeightCurve;
                        ls.Spine_ActionWeightCurve   = data.SprintSpine_ActionWeightCurve;
                        ls.Weapon_ActionWeightCurve = data.SprintWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMeta(ocMeta, ref ocMeta.LSprintWeightCurve, ls);
                        var lc = new ActionLocomotionWeightStruct();
                        lc.Dynamic_ActionWeightCurve = data.CrouchDynamic_ActionWeightCurve;
                        lc.Head_ActionWeightCurve = data.CrouchHead_ActionWeightCurve;
                        lc.LClavicle_ActionWeightCurve = data.CrouchLClavicle_ActionWeightCurve;
                        lc.LeftArm_ActionWeightCurve  = data.CrouchLeftArm_ActionWeightCurve;
                        lc.RClavicle_ActionWeightCurve = data.CrouchRClavicle_ActionWeightCurve;
                        lc.RightArm_ActionWeightCurve   = data.CrouchRightArm_ActionWeightCurve;
                        lc.Spine1_ActionWeightCurve  = data.CrouchSpine1_ActionWeightCurve;
                        lc.Spine2_ActionWeightCurve  = data.CrouchSpine2_ActionWeightCurve;
                        lc.Spine_ActionWeightCurve   = data.CrouchSpine_ActionWeightCurve;
                        lc.Weapon_ActionWeightCurve = data.CrouchWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMeta(ocMeta, ref ocMeta.LIdleCrouchWeightCurve, lc);
                        var ll = new ActionLocomotionWeightStruct();
                        ll.Dynamic_ActionWeightCurve = data.LadderDynamic_ActionWeightCurve;
                        ll.Head_ActionWeightCurve = data.LadderHead_ActionWeightCurve;
                        ll.LClavicle_ActionWeightCurve = data.LadderLClavicle_ActionWeightCurve;
                        ll.LeftArm_ActionWeightCurve  = data.LadderLeftArm_ActionWeightCurve;
                        ll.RClavicle_ActionWeightCurve = data.LadderRClavicle_ActionWeightCurve;
                        ll.RightArm_ActionWeightCurve   = data.LadderRightArm_ActionWeightCurve;
                        ll.Spine1_ActionWeightCurve  = data.LadderSpine1_ActionWeightCurve;
                        ll.Spine2_ActionWeightCurve  = data.LadderSpine2_ActionWeightCurve;
                        ll.Spine_ActionWeightCurve   = data.LadderSpine_ActionWeightCurve;
                        ll.Weapon_ActionWeightCurve = data.LadderWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMeta(ocMeta, ref ocMeta.LLadderWeightCurve, ll);
                        var lsi = new ActionLocomotionWeightStruct();
                        lsi.Dynamic_ActionWeightCurve = data.SwimIdleDynamic_ActionWeightCurve;
                        lsi.Head_ActionWeightCurve = data.SwimIdleHead_ActionWeightCurve;
                        lsi.LClavicle_ActionWeightCurve = data.SwimIdleLClavicle_ActionWeightCurve;
                        lsi.LeftArm_ActionWeightCurve  = data.SwimIdleLeftArm_ActionWeightCurve;
                        lsi.RClavicle_ActionWeightCurve = data.SwimIdleRClavicle_ActionWeightCurve;
                        lsi.RightArm_ActionWeightCurve   = data.SwimIdleRightArm_ActionWeightCurve;
                        lsi.Spine1_ActionWeightCurve  = data.SwimIdleSpine1_ActionWeightCurve;
                        lsi.Spine2_ActionWeightCurve  = data.SwimIdleSpine2_ActionWeightCurve;
                        lsi.Spine_ActionWeightCurve   = data.SwimIdleSpine_ActionWeightCurve;
                        lsi.Weapon_ActionWeightCurve = data.SwimIdleWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMeta(ocMeta, ref ocMeta.LSwimIdleWeightCurve, lsi);
                        var lsw = new ActionLocomotionWeightStruct();
                        lsw.Dynamic_ActionWeightCurve = data.SwimDynamic_ActionWeightCurve;
                        lsw.Head_ActionWeightCurve = data.SwimHead_ActionWeightCurve;
                        lsw.LClavicle_ActionWeightCurve = data.SwimLClavicle_ActionWeightCurve;
                        lsw.LeftArm_ActionWeightCurve  = data.SwimLeftArm_ActionWeightCurve;
                        lsw.RClavicle_ActionWeightCurve = data.SwimRClavicle_ActionWeightCurve;
                        lsw.RightArm_ActionWeightCurve   = data.SwimRightArm_ActionWeightCurve;
                        lsw.Spine1_ActionWeightCurve  = data.SwimSpine1_ActionWeightCurve;
                        lsw.Spine2_ActionWeightCurve  = data.SwimSpine2_ActionWeightCurve;
                        lsw.Spine_ActionWeightCurve   = data.SwimSpine_ActionWeightCurve;
                        lsw.Weapon_ActionWeightCurve = data.SwimWeapon_ActionWeightCurve;
                        CopyActionLocoWeightToMeta(ocMeta, ref ocMeta.LSwimMoveWeightCurve, lsw);
                        
                        ocMeta.Init();
                        ocMeta.InitAo();
                        ocMeta.InitHorseIK();
                        
                        metaAsset.OcLayerWeightCollection.TryAdd(key, ocMeta);
                    }
                    EditorUtility.SetDirty(ocMeta);
                    AssetDatabase.SaveAssetIfDirty(ocMeta);
                }
                EditorUtility.SetDirty(metaAsset);
                AssetDatabase.SaveAssetIfDirty(metaAsset);
            }
        }

        private static void CopyActionLocoWeightToMeta(TpMetaOverrideWeight meta, ref AnimCurveKey[] curves, ActionLocomotionWeightStruct alw)
        {
            meta.Fix(ref curves);
            var assetPath = AssetDatabase.GetAssetPath(meta);
            var folderPath = Path.GetDirectoryName(assetPath);
            //在目标目录下创建一个字符串文件夹
            var strFilePath = Path.Combine(folderPath, AnimStrKey.localStrFile);
            AnimStrGroupRef animStrGroupRef = null;
            if (!File.Exists(strFilePath))
            {
                animStrGroupRef = ScriptableObject.CreateInstance<AnimStrGroupRef>();
                AssetDatabase.CreateAsset(animStrGroupRef, strFilePath);
            }
            else
            {
                animStrGroupRef = AssetDatabase.LoadAssetAtPath<AnimStrGroupRef>(strFilePath);
            }
            
            var curveFilePath = Path.Combine(folderPath, AnimCurveKey.localStrFile);
            AnimCurveGroupRef animCurveGroup = null;
            if (!File.Exists(curveFilePath))
            {
                animCurveGroup = ScriptableObject.CreateInstance<AnimCurveGroupRef>();
                AssetDatabase.CreateAsset(animCurveGroup, curveFilePath);
            }
            else
            {
                animCurveGroup = AssetDatabase.LoadAssetAtPath<AnimCurveGroupRef>(curveFilePath);
            }

            var bip01 = new AnimationCurve();
            bip01.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01, bip01,animCurveGroup);
            var Bip01Pelvis = new AnimationCurve();
            Bip01Pelvis.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Pelvis, Bip01Pelvis,animCurveGroup);
            var Bip01LThigh = new AnimationCurve();
            Bip01LThigh.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LThigh, Bip01LThigh,animCurveGroup);
            var Bip01LCalf = new AnimationCurve();
            Bip01LCalf.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LCalf, Bip01LCalf, animCurveGroup);
            var Bip01RThigh = new AnimationCurve();
            Bip01RThigh.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RThigh, Bip01RThigh,animCurveGroup);
            var Bip01RCalf = new AnimationCurve();
            Bip01RCalf.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RCalf, Bip01RCalf,animCurveGroup);
            var Bip01Spine = new AnimationCurve();
            Bip01Spine.CopyFrom(alw.Spine_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Spine, Bip01Spine,animCurveGroup);
            var Bip01Spine1 = new AnimationCurve();
            Bip01Spine1.CopyFrom(alw.Spine1_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Spine1,Bip01Spine1,animCurveGroup);
            var Bip01Spine2 = new AnimationCurve();
            Bip01Spine2.CopyFrom(alw.Spine2_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Spine2, Bip01Spine2,animCurveGroup);
            var Bip01LClavicle = new AnimationCurve();
            Bip01LClavicle.CopyFrom(alw.LClavicle_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LClavicle, Bip01LClavicle,animCurveGroup);
            var Bip01Neck = new AnimationCurve();
            Bip01Neck.CopyFrom(alw.Head_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Neck, Bip01Neck,animCurveGroup);
            var Bip01Head = new AnimationCurve();
            Bip01Head.CopyFrom(alw.Head_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Head, Bip01Head,animCurveGroup);
            var Bip01RClavicle = new AnimationCurve();
            Bip01RClavicle.CopyFrom(alw.RClavicle_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RClavicle, Bip01RClavicle,animCurveGroup);
            var BaseWeaponLocator = new AnimationCurve();
            BaseWeaponLocator.CopyFrom(alw.Weapon_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.BaseWeaponLocator, BaseWeaponLocator,animCurveGroup);
            var Bip01LUpperArm = new AnimationCurve();
            Bip01LUpperArm.CopyFrom(alw.LeftArm_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LUpperArm, Bip01LUpperArm,animCurveGroup);
            var Bip01LForearm = new AnimationCurve();
            Bip01LForearm.CopyFrom(alw.LeftArm_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LForearm, Bip01LForearm,animCurveGroup);
            var Bip01LHand = new AnimationCurve();
            Bip01LHand.CopyFrom(alw.LeftArm_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LHand, Bip01LHand,animCurveGroup);
            var Bip01RUpperArm = new AnimationCurve();
            Bip01RUpperArm.CopyFrom(alw.RightArm_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RUpperArm, Bip01RUpperArm,animCurveGroup);
            var Bip01RForearm = new AnimationCurve();
            Bip01RForearm.CopyFrom(alw.RightArm_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RForearm, Bip01RForearm,animCurveGroup);
            var Bip01RHand = new AnimationCurve();
            Bip01RHand.CopyFrom(alw.RightArm_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RHand, Bip01RHand,animCurveGroup);
            
            EditorUtility.SetDirty(animCurveGroup);
            EditorUtility.SetDirty(animStrGroupRef);
        }

         private static void CopyActionLocoWeightToMetaAdd(TpMetaAdditiveWeight meta, ref AnimCurveKey[] curves, ActionLocomotionWeightStruct alw)
        {
            meta.Fix(ref curves);
            var assetPath = AssetDatabase.GetAssetPath(meta);
            var folderPath = Path.GetDirectoryName(assetPath);
            //在目标目录下创建一个字符串文件夹
            var strFilePath = Path.Combine(folderPath, AnimStrKey.localStrFile);
            AnimStrGroupRef animStrGroupRef = null;
            if (!File.Exists(strFilePath))
            {
                animStrGroupRef = ScriptableObject.CreateInstance<AnimStrGroupRef>();
                AssetDatabase.CreateAsset(animStrGroupRef, strFilePath);
            }
            else
            {
                animStrGroupRef = AssetDatabase.LoadAssetAtPath<AnimStrGroupRef>(strFilePath);
            }
            
            var curveFilePath = Path.Combine(folderPath, AnimCurveKey.localStrFile);
            AnimCurveGroupRef animCurveGroup = null;
            if (!File.Exists(curveFilePath))
            {
                animCurveGroup = ScriptableObject.CreateInstance<AnimCurveGroupRef>();
                AssetDatabase.CreateAsset(animCurveGroup, curveFilePath);
            }
            else
            {
                animCurveGroup = AssetDatabase.LoadAssetAtPath<AnimCurveGroupRef>(curveFilePath);
            }

            var bip01 = new AnimationCurve();
            bip01.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01, bip01,animCurveGroup);
            var Bip01Pelvis = new AnimationCurve();
            Bip01Pelvis.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Pelvis, Bip01Pelvis,animCurveGroup);
            var Bip01LThigh = new AnimationCurve();
            Bip01LThigh.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LThigh, Bip01LThigh,animCurveGroup);
            var Bip01LCalf = new AnimationCurve();
            Bip01LCalf.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LCalf, Bip01LCalf, animCurveGroup);
            var Bip01RThigh = new AnimationCurve();
            Bip01RThigh.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RThigh, Bip01RThigh,animCurveGroup);
            var Bip01RCalf = new AnimationCurve();
            Bip01RCalf.CopyFrom(alw.Dynamic_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RCalf, Bip01RCalf,animCurveGroup);
            var Bip01Spine = new AnimationCurve();
            Bip01Spine.CopyFrom(alw.Spine_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Spine, Bip01Spine,animCurveGroup);
            var Bip01Spine1 = new AnimationCurve();
            Bip01Spine1.CopyFrom(alw.Spine1_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Spine1,Bip01Spine1,animCurveGroup);
            var Bip01Spine2 = new AnimationCurve();
            Bip01Spine2.CopyFrom(alw.Spine2_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Spine2, Bip01Spine2,animCurveGroup);
            var Bip01LClavicle = new AnimationCurve();
            Bip01LClavicle.CopyFrom(alw.LClavicle_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LClavicle, Bip01LClavicle,animCurveGroup);
            var Bip01Neck = new AnimationCurve();
            Bip01Neck.CopyFrom(alw.Head_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Neck, Bip01Neck,animCurveGroup);
            var Bip01Head = new AnimationCurve();
            Bip01Head.CopyFrom(alw.Head_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01Head, Bip01Head,animCurveGroup);
            var Bip01RClavicle = new AnimationCurve();
            Bip01RClavicle.CopyFrom(alw.RClavicle_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RClavicle, Bip01RClavicle,animCurveGroup);
            var BaseWeaponLocator = new AnimationCurve();
            BaseWeaponLocator.CopyFrom(alw.Weapon_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.BaseWeaponLocator, BaseWeaponLocator,animCurveGroup);
            var Bip01LUpperArm = new AnimationCurve();
            Bip01LUpperArm.CopyFrom(alw.LeftArm_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LUpperArm, Bip01LUpperArm,animCurveGroup);
            var Bip01LForearm = new AnimationCurve();
            Bip01LForearm.CopyFrom(alw.LeftArm_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LForearm, Bip01LForearm,animCurveGroup);
            var Bip01LHand = new AnimationCurve();
            Bip01LHand.CopyFrom(alw.LeftArm_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01LHand, Bip01LHand,animCurveGroup);
            var Bip01RUpperArm = new AnimationCurve();
            Bip01RUpperArm.CopyFrom(alw.RightArm_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RUpperArm, Bip01RUpperArm,animCurveGroup);
            var Bip01RForearm = new AnimationCurve();
            Bip01RForearm.CopyFrom(alw.RightArm_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RForearm, Bip01RForearm,animCurveGroup);
            var Bip01RHand = new AnimationCurve();
            Bip01RHand.CopyFrom(alw.RightArm_ActionWeightCurve);
            meta.AddCurve(ref curves,TpBoneNameConf.Bip01RHand, Bip01RHand,animCurveGroup);
            
            EditorUtility.SetDirty(animCurveGroup);
            EditorUtility.SetDirty(animStrGroupRef);
        }


        /// <summary>
        /// step:1
        /// </summary>
        public static void CreateLocomotion()
        {
            //生成所有meta下的LocomotionLayerWeightCollection;
            var allTpTemplate = AssetDatabase.FindAssets("t:TpClipSettingMeta",
                new string[] {"Assets/SocClientRes/Weapon"}); //
            foreach (var settingGuid in allTpTemplate)
            {
                var filepath = AssetDatabase.GUIDToAssetPath(settingGuid);
                var tpSetting = AssetDatabase.LoadAssetAtPath<TpClipSettingMeta>(filepath);
                tpSetting.LocomotionLayerWeightCollection = new SerializableDictionary<AnimParametersTp.ELocomotionLayer, TpMetaLocomotionWeight>();
                //idle
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipStance_HipStandIdle, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipStance_HipCrouchIdle, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLCrouch, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRCrouch, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRStand, null);
                //s2c
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipStance_Stand2Crouch, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipStance_Stand2CrouchReady, null);
                //c2s
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipStance_Crouch2Stand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipStance_Crouch2StandReady, null);
                //jog
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogBLStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogBStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogBRStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogFLStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogFRStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogFStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogRStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogLStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkBLStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkBRStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkBStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkFLStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkFRStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkFStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkLStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkRStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchBLStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchBRStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchBStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchFLStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchFRStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchFStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchLStand, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchRStand, null);
                //sprint
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_SprintF, null);
                //jump
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Jump_JumpStart, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Jump_JumpLoop, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Jump_JumpEnd, null);
                //swimidle
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Swim_SwimIdle, null);
                //swimjog
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Swim_Swim_JogF, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Swim_Swim_JogB, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Swim_Swim_JogL, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Swim_Swim_JogR, null);
                //swimsprint
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Swim_Swim_SprintF, null);
                //mantle
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Mantle_MantleOnFar, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Mantle_MantleOnNear, null);
                //ladder
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Ladder_DownEnter, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Ladder_DownLeave, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Ladder_LadderFastDown, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Ladder_LadderFastUp, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Ladder_LadderMove, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Ladder_LadderProcedureCrouch, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Ladder_LadderProcedureCrouchWait, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Ladder_LadderProcedureJog, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Ladder_LadderProcedureWait, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Ladder_UpEnter, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Ladder_UpLeave, null);
                //horse
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.RiderJump_JumpCanter_JumpCanterEnd, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.RiderJump_JumpCanter_JumpCanterLoop, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.RiderJump_JumpCanter_JumpCanterStart, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.RiderJump_JumpForward_JumpForwardEnd, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.RiderJump_JumpForward_JumpForwardLoop, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.RiderJump_JumpForward_JumpForwardStart, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.RiderJump_JumpGallop_JumpGallopEnd, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.RiderJump_JumpGallop_JumpGallopLoop, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.RiderJump_JumpGallop_JumpGallopStart, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.RiderJump_JumpSprint_JumpSprintEnd, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.RiderJump_JumpSprint_JumpSprintLoop, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.RiderJump_JumpSprint_JumpSprintStart, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.RiderJump_Neigh, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.RiderLocomotion_RiderLocomotionNode, null);
                //parachute
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Parachute_Cut, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Parachute_ParachuteIdle, null);
                tpSetting.LocomotionLayerWeightCollection.TryAdd(AnimParametersTp.ELocomotionLayer.Parachute_Start, null);
                //create asset
                var subPath = filepath.Substring(0, filepath.LastIndexOf('/'));
                //idle
                var idle = CreateLoAsset(subPath, "TpMetaLoWeight_Idle", tpSetting.idleWeightList);
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipStance_HipStandIdle] = idle;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipStance_HipCrouchIdle] = idle;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLCrouch] = idle;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRCrouch] = idle;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLStand] = idle;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRStand] = idle;
                //s2c
                var s2c = CreateLoAsset(subPath, "TpMetaLoWeight_Stand2Crouch", tpSetting.stand2CrouchWeightList);
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipStance_Stand2Crouch] = s2c;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipStance_Stand2CrouchReady] = s2c;
                //c2s
                var c2s = CreateLoAsset(subPath, "TpMetaLoWeight_Crouch2Stand", tpSetting.crouch2StandWeightList);
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipStance_Crouch2Stand] = c2s;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipStance_Crouch2StandReady] = c2s;
                //jog
                var jog = CreateLoAsset(subPath, "TpMetaLoWeight_Jog", tpSetting.jogWeightList);
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogBLStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogBStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogBRStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogFLStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogFRStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogFStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogRStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogLStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkBLStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkBRStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkBStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkFLStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkFRStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkFStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkLStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkRStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchBLStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchBRStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchBStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchFLStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchFRStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchFStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchLStand] = jog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchRStand] = jog;
                //sprint
                var sprint = CreateLoAsset(subPath, "TpMetaLoWeight_Sprint", tpSetting.sprintWeightList);
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_SprintF] = sprint;
                //jump
                var jump = CreateLoAsset(subPath, "TpMetaLoWeight_Jump", tpSetting.jumpWeightList);
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Jump_JumpStart] = jump;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Jump_JumpLoop] = jump;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Jump_JumpEnd] = jump;
                //swimidle
                var swimIdle = CreateLoAsset(subPath, "TpMetaLoWeight_SwimIdle", tpSetting.swimIdleWeightList);
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Swim_SwimIdle] = swimIdle;
                //swimjog
                var swimJog = CreateLoAsset(subPath, "TpMetaLoWeight_SwimJog", tpSetting.swimJogWeightList);
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Swim_Swim_JogB] = swimJog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Swim_Swim_JogF] = swimJog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Swim_Swim_JogL] = swimJog;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Swim_Swim_JogR] = swimJog;
                //swimsprint
                var swimSprint = CreateLoAsset(subPath, "TpMetaLoWeight_SwimSprint", tpSetting.swimSprintWeightList);
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Swim_Swim_SprintF] = swimSprint;
                //mantle
                var mantle = CreateLoAsset(subPath, "TpMetaLoWeight_Mantle", tpSetting.mantleWeightList);
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Mantle_MantleOnFar] = mantle;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Mantle_MantleOnNear] = mantle;
                //ladder
                var ladder = CreateLoAsset(subPath, "TpMetaLoWeight_Ladder", tpSetting.ladderWeightList);
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Ladder_DownEnter] = ladder;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Ladder_DownLeave] = ladder;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Ladder_LadderFastDown] = ladder;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Ladder_LadderFastUp] = ladder;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Ladder_LadderMove] = ladder;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Ladder_LadderProcedureCrouch] = ladder;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Ladder_LadderProcedureCrouchWait] = ladder;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Ladder_LadderProcedureJog] = ladder;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Ladder_LadderProcedureWait] = ladder;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Ladder_UpEnter] = ladder;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Ladder_UpLeave] = ladder;
                //horse
                var horse = CreateLoAsset(subPath, "TpMetaLoWeight_Horse", tpSetting.horseLocomotionWeightList);
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.RiderJump_JumpCanter_JumpCanterEnd] = horse;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.RiderJump_JumpCanter_JumpCanterLoop] = horse;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.RiderJump_JumpCanter_JumpCanterStart] = horse;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.RiderJump_JumpForward_JumpForwardEnd] = horse;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.RiderJump_JumpForward_JumpForwardLoop] = horse;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.RiderJump_JumpForward_JumpForwardStart] = horse;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.RiderJump_JumpGallop_JumpGallopEnd] = horse;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.RiderJump_JumpGallop_JumpGallopLoop] = horse;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.RiderJump_JumpGallop_JumpGallopStart] = horse;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.RiderJump_JumpSprint_JumpSprintEnd] = horse;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.RiderJump_JumpSprint_JumpSprintLoop] = horse;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.RiderJump_JumpSprint_JumpSprintStart] = horse;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.RiderJump_Neigh] = horse;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.RiderLocomotion_RiderLocomotionNode] = horse;
                //parachute
                var parachute = CreateLoAsset(subPath, "TpMetaLoWeight_Parachute", tpSetting.parachuteLocomotionWeightList);
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Parachute_Cut] = parachute;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Parachute_ParachuteIdle] = parachute;
                tpSetting.LocomotionLayerWeightCollection[AnimParametersTp.ELocomotionLayer.Parachute_Start] = parachute;
                
                EditorUtility.SetDirty(tpSetting);
                AssetDatabase.SaveAssetIfDirty(tpSetting);
            }
        }

        private static TpMetaLocomotionWeight CreateLoAsset(string subPath, string assetName, SkeletonMaskWeightValue[] WeightList)
        {
            //创建序列化文件
           
            var locPath = subPath + "/"+assetName+".asset";
            var asset = AssetDatabase.LoadAssetAtPath<TpMetaLocomotionWeight>(locPath);
            if (asset == null)
            {
                asset = ScriptableObject.CreateInstance<TpMetaLocomotionWeight>();
                AssetDatabase.CreateAsset(asset, locPath);
            }
            //拷贝老的数据
            Array.Copy(WeightList, asset.WeightList, WeightList.Length);
            EditorUtility.SetDirty(asset);
            AssetDatabase.SaveAssetIfDirty(asset);
            return asset;
        }
    }
}