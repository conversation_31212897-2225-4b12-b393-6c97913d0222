using FairyGUI;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Pandora;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SDK;
using WizardGames.Soc.SocClient.Control;

namespace WizardGames.Soc.SocClient.Ui
{
    /// <summary>
    /// 注意, 这个elem暂时不能改为动态加载, 因为依赖了elem的管理, 如果改为动态加载, 逻辑可能要做对应修改
    /// </summary>
    public class UiHudElemEntryList : UiHudElem
    {
        private List<UiHudElem> entries = new();
        private List<string> entriesNames = new();
        private GButton btnFolder;
        private Transition folderAnim;
        private Transition unfolderAnim;
        private GGroup mailRedTips;
        /// <summary>
        /// 这两步引导需要配置成起始步，支持反复触发
        /// </summary>
        private readonly int guideStepId1 = 1119;
        private readonly int guideStepId2 = 1120;
        private readonly int guideId = 112;
        protected override void OnBoardInit()
        {
            base.OnBoardInit();
            // 改为动态加载的话, 这里获取node就会有问题
            var nodeRoot = TryGetNode();
            if (null == nodeRoot || !IsNodeValid) return;
            entriesNames.Add(NodeName);//把自己也添加进去
            btnFolder = nodeRoot.GetChild("btnFolder").asButton;
            btnFolder.onClick.Set(OnClickFolder);
            this.ExpandClickRange(btnFolder, this.tbData.ExEffectRadius);
            folderAnim = nodeRoot.GetTransition("fold_anim");
            unfolderAnim = nodeRoot.GetTransition("unfold_anim");
            mailRedTips = nodeRoot.GetChild("mail_red_tips").asGroup;
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            RefreshRedDot();
            UpdateMailRedTips(0);
            TryTriggerGuide();
            Mc.Msg.AddListener(EventDefine.UpdateSingleMission, TryTriggerGuide);
            Mc.Msg.AddListener(EventDefine.UpdateTrackMissionId, TryTriggerGuide);
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            Mc.Msg.RemoveListener(EventDefine.UpdateSingleMission, TryTriggerGuide);
            Mc.Msg.RemoveListener(EventDefine.UpdateTrackMissionId, TryTriggerGuide);
        }

        public void OnHudBlockElemChange(UiHudElem curElem)
        {
            var name = curElem?.NodeName;
            if (!entriesNames.Contains(name))
                HideEntryGroup();
        }

        private void HideEntryGroup()
        {
            if (IsCenterConsoleShow())
            {
                this.btnFolder.GetController("openState").SetSelectedPage("close");
                folderAnim.Play(() => {
                    UpdateMailRedTips(0);
                    TryTriggerGuide();
                });
            }
        }


        private bool IsCenterConsoleShow()
        {
            var centerConsole = Mc.Ui.GetWindow("UiCenterConsole");
            if (centerConsole != null && centerConsole.IsActive && centerConsole.IsVisible)
            {
                return true;
            }
            return false;
        }

        protected override void OnCreate(GComponent node)
        {
            base.OnCreate(node);
            if (IsCenterConsoleShow())
            {
                UiHud.SetHudBgBlockState(HideEntryGroup);
            }
            Mc.Msg.AddListener(EventDefine.OpenUIDyingNotice, OnRefreshByOpenUIDying);
            Mc.Msg.AddListener(EventDefine.CloseUIDyingNotice, OnRefreshByCloseUIDying);
            Mc.RedDot?.RegisterRedDotUpdate(RedDotType.GameHudEntryList, UpdateLock);
            Mc.RedDot?.RegisterRedDotUpdate(RedDotType.GameMail, UpdateMailRedTips);
            Mc.Msg.AddListener<UiHudElem>(EventDefine.HudBlockElemChange, OnHudBlockElemChange);
            Mc.Msg.AddListener(EventDefine.HideEntryList, HideEntryGroup);
            Mc.Msg.AddListener(EventDefine.PlaySurvivalEffect, PlaySurvivalEffect);
            Mc.Msg.AddListener(EventDefine.OpenEntryList, OnOpenEntryList);

            // 只显示ESC，不走正常HotKey流程
            TryGetNode().InitKeyTips("key", ActionName.Escape);
        }

        public void TryTriggerGuide()
        {
            if (Mc.Mission == null || !Mc.Mission.HasMission) return;
            var missionCfg = Mc.Tables.TbQuestPhase.GetOrDefault(Mc.Mission.NewbieRemoteData.Id);
            if (missionCfg != null && missionCfg.GuideId == guideId)
            {
                var guideData = Mc.Guide.GetGuideData(missionCfg.GuideId);
                if (guideData != null)
                {
                    if (IsCenterConsoleShow())
                    {
                        Mc.Msg.FireMsg(EventDefine.TryGuideInterface, guideId, guideStepId2);
                    }
                    else
                    {
                        Mc.Msg.FireMsg(EventDefine.TryGuideInterface, guideId, guideStepId1);
                    }
                }
            }
        }

        private void UpdateMailRedTips(int _)
        {
            if (mailRedTips == null) return;//大厅的编辑模式下，不会执行boardInit，所以mailRedTips会为空
            mailRedTips.visible = !this.IsInEdit && (Mc.RedDot?.GetRedDotBool(RedDotType.GameMail) ?? false) && !IsCenterConsoleShow()
                && !PlayHelper.IsNewbie;
        }

        //红点更新的时候需要更新一下锁帧
        private void UpdateLock(int _)
        {
            SetUpdateModeIfLock(FGUIUpdateType.LockAfterShow);
        }

        public void OnRefreshByOpenUIDying()
        {
            RefreshRedDot();
        }
        public void OnRefreshByCloseUIDying()
        {
            RefreshRedDot();
        }

        public void RefreshRedDot()
        {
            Mc.RedDot?.RefreshRedDot(RedDotType.GameHudEntryList);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            Mc.Msg.RemoveListener(EventDefine.OpenUIDyingNotice, OnRefreshByOpenUIDying);
            Mc.Msg.RemoveListener(EventDefine.CloseUIDyingNotice, OnRefreshByCloseUIDying);
            Mc.RedDot?.UnregisterRedDotUpdate(RedDotType.GameHudEntryList, UpdateLock);
            Mc.RedDot?.UnregisterRedDotUpdate(RedDotType.GameMail, UpdateMailRedTips);
            Mc.Msg.RemoveListener<UiHudElem>(EventDefine.HudBlockElemChange, OnHudBlockElemChange);
            Mc.Msg.RemoveListener(EventDefine.HideEntryList, HideEntryGroup);
            Mc.Msg.RemoveListener(EventDefine.PlaySurvivalEffect, PlaySurvivalEffect);
            Mc.Msg.RemoveListener(EventDefine.OpenEntryList, OnOpenEntryList);
        }

        public void OnClickFolder()
        {
            Mc.Ui.OpenWindow("UiCenterConsole");
            //
            // if (!Mc.ObserverMode)
            // {
            //     Mc.Ui.OpenWindow("UiCenterConsole");
            // }
            // else
            // {
            //     Mc.SettingClient.OpenUiSetting();
            // }
        }

        private void OnOpenEntryList()
        {
            //OnClickFolder();
            if (btnFolder != null) btnFolder.onClick.Call();
        }


        public override void StartEdit()
        {
            base.StartEdit();
            var nodeRoot = TryGetNode();
            if (null == nodeRoot || !IsNodeValid) return;
            this.btnFolder = nodeRoot.GetChild("btnFolder").asButton;
            this.btnFolder.GetController("openState").SetSelectedPage("close");
            //隐藏邮件红点
            mailRedTips = nodeRoot.GetChild("mail_red_tips").asGroup;
            mailRedTips.visible = false;
        }

        public override void EndEdit(bool needToFalseIsInEditor = true)
        {
            base.EndEdit(needToFalseIsInEditor);
            var nodeRoot = TryGetNode();
            if (null == nodeRoot || !IsNodeValid) return;
        }
        /// <summary>
        /// 默认点击节点
        /// </summary>
        /// <returns></returns>
        protected override GComponent HudDefaultTouchNode()
        {
            var nodeRoot = TryGetNode();
            if (null == nodeRoot || !IsNodeValid) return null;
            return nodeRoot.GetChild("btnFolder")?.asCom;
        }
        /// <summary>
        /// 重写选择效果
        /// </summary>
        /// <param name="isOn"></param>
        public override void SelectEffect(bool isOn)
        {
            base.SelectEffect(isOn);
            var nodeRoot = TryGetNode();
            if (null != nodeRoot && IsNodeValid){
                
                }
            }

        private void PlaySurvivalEffect()
        {
            SetUpdateModeIfLock(FGUIUpdateType.Free);
            btnFolder.GetTransition("appear").Play(() =>
            {
                SetUpdateModeIfLock(FGUIUpdateType.LockAfterShow);
            });
        }
    }
}