using FairyGUI;
using System;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Plant;
using WizardGames.Soc.SocClient.Ui.Utils;

namespace WizardGames.Soc.SocClient.Ui
{
    public class UiPlantManurePage : UiPlantBasePage
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(UiPlantManurePage));
        public override EPageType PageType => EPageType.Manure;

        public GButton closeBtn;
        public GButton manureBtn;
        private GTextField manureBtnNum;
        private Controller manureBtnCtrl;
        private UiPlantBox2D uiPlantBox2D;

        private PlantBoxData curPlantBox;

        private bool hasSetSelected;

        private int totalManure;

        public static UiPlantBasePage Create(GComponent com,UiPlantOperationSubPanel uiPlantOperationSubPanel)
        {
            UiPlantBasePage rt = new UiPlantManurePage();
            rt.Init(com,uiPlantOperationSubPanel);
            return rt;
        }

        protected override void OnInit(GComponent com, UiPlantOperationSubPanel uiPlantOperationSubPanel)
        {
            base.OnInit(com, uiPlantOperationSubPanel);
            var plantBox2D = com.GetChild("plant_box").asCom;
            uiPlantBox2D = UiPlantBox2D.Create(plantBox2D, uiPlantOperationSubPanel,EPageType.Manure);

            closeBtn = com.GetChild("close_btn").asButton;
            closeBtn.onClick.Set(OnClickClose);
            manureBtn = com.GetChild("manure_btn").asButton;
            manureBtn.onClick.Set(OnClickManure);
            manureBtnNum = manureBtn.GetChild("num").asTextField;
            manureBtnCtrl = manureBtn.GetController("enable");

            totalManure = Mc.Plant.TotalManure;
        }

        protected override void OnEnable(object data)
        {
            base.OnEnable(data);
            curPlantBox = data as PlantBoxData;
            totalManure = Mc.Plant.TotalManure;
            hasSetSelected = false;
            Mc.Msg.AddListener<long>(EventDefine.ManureSuccess, OnManureSuccess);
            Mc.Msg.AddListener<int>(EventDefine.ManurePageSelectedSlot, OnManurePageSelectedSlot);
            Mc.Msg.AddListener<int, int>(EventDefine.PlantBoxModelSelectedSlot, OnPlantBoxModelSelectedSlot);
        }

        protected override void OnRefresh()
        {
            base.OnRefresh();
            RefreshPlants();

            OnManurePageSelectedSlot();
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            Mc.Msg.RemoveListener<long>(EventDefine.ManureSuccess, OnManureSuccess);
            Mc.Msg.RemoveListener<int>(EventDefine.ManurePageSelectedSlot, OnManurePageSelectedSlot);
            Mc.Msg.RemoveListener<int, int>(EventDefine.PlantBoxModelSelectedSlot, OnPlantBoxModelSelectedSlot);
        }

        protected override void OnDispose()
        {
            base.OnDispose();
            uiPlantBox2D?.Release();
        }

        private void RefreshPlants()
        {
            uiPlantBox2D?.Refresh(curPlantBox, !hasSetSelected);
            hasSetSelected = true;
        }

        private void OnClickClose()
        {
            uiPlantOperationSubPanel.ChangePage(EPageType.Default);
            // Mc.Msg.FireMsg(EventDefine.OnChangePlantMainPage, EPageType.Default);
        }

        private void OnClickManure()
        {
            if (curPlantBox == null || uiPlantBox2D == null) return;
            var selectedIdxed = uiPlantBox2D.GetSelected();
            if (selectedIdxed.Count <= 0)
            {
                //请点击需要施肥的区域
                Mc.MsgTips.ShowRealtimeWeakTip(23111);
                return;
            }

            if (totalManure - selectedIdxed.Count < 0)
            {
                //当前肥料不足
                Mc.MsgTips.ShowRealtimeWeakTip(23112);
                return;
            }

            curPlantBox.RequestManure(selectedIdxed);
        }

        private void OnManureSuccess(long collectionId)
        {
            if (curPlantBox != null && curPlantBox.EntityId == collectionId)
            {
                OnClickClose();
            }
        }

        private void OnPlantBoxModelSelectedSlot(int a, int b)
        {
            OnManurePageSelectedSlot();
        }

        private void OnManurePageSelectedSlot(int index = 0)
        {
            if (curPlantBox == null || uiPlantBox2D == null) return;
            var selectedIdxed = uiPlantBox2D.GetSelected();
            if (manureBtn != null)
            {
                var enable = totalManure > 0 && totalManure - selectedIdxed.Count >= 0;
                manureBtnCtrl.SetSelectedIndex(enable ? 1 : 0);
            }

            manureBtnNum.text = string.Format("X{0}", selectedIdxed.Count);
        }
    }
}
