using Contexts;
using Cysharp.Text;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Weapon;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocClient.Manager;

namespace WizardGames.Soc.SocClient.Player.HeldItem
{
    public abstract class ALocalCustomWrapper
    {
        public static SocLogger logger = LogHelper.GetLogger(typeof(ALocalCustomWrapper));

        public IItemEntity S;
        public IItemEntity L;

        public virtual void Release()
        {
            if (L != null)
            {
                if (Mc.System.Context is ClientContext c)
                {
                    c.MgrGunkickFormula?.RemoveGunkickFormula(L.EntityId);
                    c.MgrShootFormula?.RemoveFormula(L.EntityId);
                }

                Mc.Entity.EntitySetRemoveHandleLocalOnly(L);
            }

            S = L = null;
        }

        public abstract bool CompareHistory(long cmd,HistoryWeaponEntity archiveHistory, IItemEntity server,ref string rst);
        public abstract void RollBack(IItemEntity server);
    }

    public class LocalWeaponCustomWrapper :ALocalCustomWrapper
    {
        public WeaponCustom Source { get;private set; }
        public WeaponCustom Local{ get;private set; }
        protected HashSet<long> HeldItemSubscribeIds = new HashSet<long>();

        protected Dictionary<long, ALocalCustomWrapper> accessories = new Dictionary<long, ALocalCustomWrapper>();
        public LocalWeaponCustomWrapper(WeaponCustom source,WeaponCustom local)
        {
            S = Source = source;
            L = Local = local;
            Local.SetIsCreateLocal(true);
            logger.InfoFormat("LocalHeldItemEntityController Add WeaponCustom SourceInst:{0},LocalInst:{1}",Source.GetHashCode(),Local.GetHashCode());

            if(source.Accessory0 != null) accessories.Add(Local.Accessory0.EntityId, new LocalWeaponAccessoryItemCustomWrapper(Source.Accessory0,Local.Accessory0));
            if(source.Accessory1 != null) accessories.Add(Local.Accessory1.EntityId, new LocalWeaponAccessoryItemCustomWrapper(Source.Accessory1,Local.Accessory1));
            if(source.Accessory2 != null) accessories.Add(Local.Accessory2.EntityId, new LocalWeaponAccessoryItemCustomWrapper(Source.Accessory2,Local.Accessory2));
            if(source.Accessory3 != null) accessories.Add(Local.Accessory3.EntityId, new LocalWeaponAccessoryItemCustomWrapper(Source.Accessory3,Local.Accessory3));
            HeldItemSubscribeIds.Add(Source.SubscribePropertyChange<int>(WeaponCustom.PropertyIds.CLIP_CAPACITY, OnClipCapcityChange));
            HeldItemSubscribeIds.Add(Source.SubscribePropertyChange<long>(WeaponCustom.PropertyIds.SKIN_ID, OnSkinChange));
            HeldItemSubscribeIds.Add(Source.SubscribePropertyChange<int>(WeaponCustom.PropertyIds.CLIPS, OnClipChange));
            HeldItemSubscribeIds.Add(this.Source.SubscribePropertyChange<float>(WeaponCustom.PropertyIds.MAX_CONDITION, OnMaxConditionChange));
            HeldItemSubscribeIds.Add(this.Source.SubscribePropertyChange(WeaponCustom.PropertyIds.ACCESSORY0, OnAccessoryChange0));
            HeldItemSubscribeIds.Add(this.Source.SubscribePropertyChange(WeaponCustom.PropertyIds.ACCESSORY1, OnAccessoryChange1));
            HeldItemSubscribeIds.Add(this.Source.SubscribePropertyChange(WeaponCustom.PropertyIds.ACCESSORY2, OnAccessoryChange2));
            HeldItemSubscribeIds.Add(this.Source.SubscribePropertyChange(WeaponCustom.PropertyIds.ACCESSORY3, OnAccessoryChange3));
           
        }

        public void OnMaxConditionChange(CustomTypeBase entity, float o, float n)
        {
            Local.MaxCondition = n;
        }

        public void OnClipCapcityChange(CustomTypeBase entity, int oldCap, int newCap)
        {
            Local.ClipCapacity = newCap ;
        }
        
        public void OnSkinChange(CustomTypeBase entity, long oldSkin, long newSkin)
        {
            Local.SkinId = newSkin;
        }
        public void OnClipChange(CustomTypeBase entity, int oldClip, int newClip)
        {
            if (Mc.MyPlayer.MyEntityLocal.CurrentWeaponId != Local.EntityId)
            {
                Local.Clips = newClip;
            }
        }

        protected void OnAccessoryChange0(CustomTypeBase entity, CustomTypeBase oldAcc, CustomTypeBase newAcc)
        {
            if (oldAcc is WeaponAccessoryItemCustom old)
            {
                if (accessories.Remove(old.EntityId, out var wrapper))
                {
                    wrapper.Release();
                }
            }

            var clone = LocalHeldItemEntityController.Clone<WeaponAccessoryItemCustom>(newAcc as IPredictType);// newAcc != null ? (newAcc as IPredictType).Clone() as WeaponAccessoryItemCustom : null;
            Local.Accessory0 = clone;
            if (Local.Accessory0 != null)
            {
                accessories.Add(Local.Accessory0.EntityId, new LocalWeaponAccessoryItemCustomWrapper(Source.Accessory0,Local.Accessory0));
            }
        }

        protected void OnAccessoryChange1(CustomTypeBase entity, CustomTypeBase oldAcc, CustomTypeBase newAcc)
        {
            if (oldAcc is WeaponAccessoryItemCustom old)
            {
                if (accessories.Remove(old.EntityId, out var wrapper))
                {
                    wrapper.Release();
                }
            }
            var clone = LocalHeldItemEntityController.Clone<WeaponAccessoryItemCustom>(newAcc as IPredictType);
            Local.Accessory1 = clone;
            if (Local.Accessory1 != null)
            {
                accessories.Add(Local.Accessory1.EntityId, new LocalWeaponAccessoryItemCustomWrapper(Source.Accessory1,Local.Accessory1));
            }
        }

        protected void OnAccessoryChange2(CustomTypeBase entity, CustomTypeBase oldAcc, CustomTypeBase newAcc)
        {
            if (oldAcc is WeaponAccessoryItemCustom old)
            {
                if (accessories.Remove(old.EntityId, out var wrapper))
                {
                    wrapper.Release();
                }
            }
            var clone = LocalHeldItemEntityController.Clone<WeaponAccessoryItemCustom>(newAcc as IPredictType);
            Local.Accessory2 = clone;
            if (Local.Accessory2 != null)
            {
                accessories.Add(Local.Accessory2.EntityId, new LocalWeaponAccessoryItemCustomWrapper(Source.Accessory2,Local.Accessory2));
            }
        }

        protected void OnAccessoryChange3(CustomTypeBase entity, CustomTypeBase oldAcc, CustomTypeBase newAcc)
        {
            if (oldAcc is WeaponAccessoryItemCustom old)
            {
                if (accessories.Remove(old.EntityId, out var wrapper))
                {
                    wrapper.Release();
                }
            }
            var clone = LocalHeldItemEntityController.Clone<WeaponAccessoryItemCustom>(newAcc as IPredictType);
            Local.Accessory3 = clone;
            if (Local.Accessory3 != null)
            {
                accessories.Add(Local.Accessory3.EntityId, new LocalWeaponAccessoryItemCustomWrapper(Source.Accessory3,Local.Accessory3));
            }
        }

        public override void Release()
        {
            base.Release();
            foreach (var kvp in accessories)
            {
                kvp.Value.Release();
            }
            accessories.Clear();
            foreach (long id in HeldItemSubscribeIds)
            {
                Source.UnSubscribePropertyChange(id);
            }

            logger.InfoFormat("LocalHeldItemEntityController Release WeaponCustom SourceInst:{0},LocalInst:{1}",Source.GetHashCode(),Local.GetHashCode());
            Source = null;
            Local = null;
        }
#region 回滚
        public override bool CompareHistory(long cmd,HistoryWeaponEntity archiveHistory, IItemEntity server,ref string rst)
        {
            if (server is WeaponCustom weapon)
            {
                if (archiveHistory.Clips != weapon.Clips)
                {
                    rst = ZString.Format("cmd:{0} 子弹数不一致 c:{1} s:{2},weapon:{3},table:{4}", cmd,archiveHistory.Clips,weapon.Clips,weapon.EntityId,weapon.TableId);
                    return false;
                }
                if (archiveHistory.UsingAmmoId != weapon.UsingAmmoId)
                {
                    rst = ZString.Format("cmd:{0} 当前弹药不一致 c:{1} s:{2},weapon:{3},table:{4}", cmd,archiveHistory.UsingAmmoId,weapon.UsingAmmoId,weapon.EntityId,weapon.TableId);
                    return false;
                }

                long acc0 = weapon.Accessory0?.EntityId ?? 0;
                if (archiveHistory.Acc0Id != acc0)
                {
                    rst = ZString.Format("cmd:{0} acc0不一致 c:{1} s:{2},weapon:{3},table:{4}", cmd,archiveHistory.Acc0Id,acc0,weapon.EntityId,weapon.TableId);
                    return false;
                }
                long acc1 = weapon.Accessory1?.EntityId ?? 0;
                if (archiveHistory.Acc1Id != acc1)
                {
                    rst = ZString.Format("cmd:{0} acc0不一致 c:{1} s:{2},weapon:{3},table:{4}", cmd,archiveHistory.Acc1Id,acc1,weapon.EntityId,weapon.TableId);
                    return false;
                }
                long acc2 = weapon.Accessory2?.EntityId ?? 0;
                if (archiveHistory.Acc2Id != acc2)
                {
                    rst = ZString.Format("cmd:{0} acc0不一致 c:{1} s:{2},weapon:{3},table:{4}", cmd,archiveHistory.Acc2Id,acc2,weapon.EntityId,weapon.TableId);
                    return false;
                }
                long acc3 = weapon.Accessory3?.EntityId ?? 0;
                if (archiveHistory.Acc3Id != acc3)
                {
                    rst = ZString.Format("cmd:{0} acc0不一致 c:{1} s:{2},weapon:{3},table:{4}", cmd,archiveHistory.Acc3Id,acc3,weapon.EntityId,weapon.TableId);
                    return false;
                }

                if (archiveHistory.SkinId != server.SkinId)
                {
                    Local.SkinId = server.SkinId;
                }
                if (archiveHistory.FireInterval != weapon.FireInterval)
                {
                    Local.FireInterval = weapon.FireInterval;
                }
                if (!Mathf.Approximately(archiveHistory.Condition, server.Condition))
                {
                    Local.Condition = server.Condition;
                }
            }

            return true;
        }

        public override void RollBack(IItemEntity server)
        {
            if (server is WeaponCustom weapon)
            {
                Local.Clips = weapon.Clips;
                Local.SkinId = weapon.SkinId;
                Local.NextFireTime = weapon.NextFireTime;
                Local.NextClipConsumeTime = weapon.NextClipConsumeTime;
                Local.NextBurstFireTime = weapon.NextBurstFireTime;
                Local.BurstFireCount = weapon.BurstFireCount;
                Local.IsReloading = weapon.IsReloading;
                Local.UsingAmmoId = weapon.UsingAmmoId;
                Local.LastAmmoId = weapon.LastAmmoId;
                Local.LastAmmoId = weapon.LastAmmoId;
                Local.ContinueReloadingAmmoID = weapon.ContinueReloadingAmmoID;
                Local.Condition = server.Condition;
                Local.StepReloadingAmmoAmount = weapon.StepReloadingAmmoAmount;
                Local.WarmupProgress = weapon.WarmupProgress;
                if (AccChange(Local.Accessory0, weapon.Accessory0))
                {
                    Local.Accessory0 = weapon.Accessory0;
                }
                if (AccChange(Local.Accessory1, weapon.Accessory1))
                {
                    Local.Accessory1 = weapon.Accessory1;
                }
                if (AccChange(Local.Accessory2, weapon.Accessory2))
                {
                    Local.Accessory2 = weapon.Accessory2;
                }
                if (AccChange(Local.Accessory3, weapon.Accessory3))
                {
                    Local.Accessory3 = weapon.Accessory3;
                }
                Local.UpdateWeaponAccessoryCorrection(); //重新刷一遍本地entity的配件影响数值
            }
        }

        private bool AccChange(WeaponAccessoryItemCustom local,WeaponAccessoryItemCustom server)
        {
            var localid = local ?.EntityId ?? 0;
            var serverid = server ?.EntityId ?? 0;
            return localid != serverid;
        }

        #endregion
    }

    public class LocalMeleeCustomWrapper :ALocalCustomWrapper
    {
        public MeleeCustom Source { get;private set; }
        public MeleeCustom Local{ get;private set; }
        protected HashSet<long> HeldItemSubscribeIds = new HashSet<long>();
        public LocalMeleeCustomWrapper(MeleeCustom source,MeleeCustom local)
        {
            S = Source = source;
            L = Local = local;
            logger.InfoFormat("LocalHeldItemEntityController Add MeleeCustom SourceInst:{0},LocalInst:{1}",Source.GetHashCode(),Local.GetHashCode());

            HeldItemSubscribeIds.Add(Source.SubscribePropertyChange<long>(MeleeCustom.PropertyIds.SKIN_ID, OnSkinChange));
            HeldItemSubscribeIds.Add(Source.SubscribePropertyChange<int>(MeleeCustom.PropertyIds.CLIPS, OnClipChange));
            HeldItemSubscribeIds.Add(this.Source.SubscribePropertyChange<float>(WeaponCustom.PropertyIds.MAX_CONDITION, OnMaxConditionChange));
        }

        public void OnMaxConditionChange(CustomTypeBase entity, float o, float n)
        {
            Local.MaxCondition = n;
        }
        public void OnSkinChange(CustomTypeBase entity, long oldSkin, long newSkin)
        {
            Local.SkinId = newSkin;
        }
        public void OnClipChange(CustomTypeBase entity, int oldClip, int newClip)
        {
            if (Mc.MyPlayer.MyEntityLocal.CurrentWeaponId != Local.EntityId)
            {
                Local.Clips = newClip;
            }
        }
        public override void Release()
        {
            base.Release();
            foreach (long id in HeldItemSubscribeIds)
            {
                Source.UnSubscribePropertyChange(id);
            }
            logger.InfoFormat("LocalHeldItemEntityController Release MeleeCustom SourceInst:{0},LocalInst:{1}",Source.GetHashCode(),Local.GetHashCode());

            Source = null;
            Local = null;
        }

        public override bool CompareHistory(long cmd, HistoryWeaponEntity archiveHistory, IItemEntity server, ref string rst)
        {
            if (server is MeleeCustom melee)
            {
                if (archiveHistory.Clips != melee.Clips)
                {
                    rst = ZString.Format("cmd:{0} 子弹数不一致 c:{1} s:{2},weapon:{3},table:{4}", cmd, archiveHistory.Clips,
                        melee.Clips, melee.EntityId, melee.TableId);
                    return false;
                }
                if (archiveHistory.SkinId != server.SkinId)
                {
                    Local.SkinId = server.SkinId;
                }
                if (!Mathf.Approximately(archiveHistory.Condition, server.Condition))
                {
                    Local.Condition = server.Condition;
                }
            }

            return true;
        }

        public override void RollBack(IItemEntity server)
        {
            if (server is MeleeCustom melee)
            {
                Local.Clips = melee.Clips;
                Local.SkinId = melee.SkinId;
                Local.NextFireTime = melee.NextFireTime;
                Local.IsReloading = melee.IsReloading;
                Local.ContinueReloadingAmmoID = melee.ContinueReloadingAmmoID;
                Local.Condition = melee.Condition;
                Local.StepReloadingAmmoAmount = melee.StepReloadingAmmoAmount;
            }
        }
    }

    public class LocalUseItemCustomWrapper :ALocalCustomWrapper
    {
        public UseItemCustom Source { get;private set; }
        public UseItemCustom Local{ get;private set; }
        protected HashSet<long> HeldItemSubscribeIds = new HashSet<long>();
        public LocalUseItemCustomWrapper(UseItemCustom source,UseItemCustom local)
        {
            S = Source = source;
            L = Local = local;
            logger.InfoFormat("LocalHeldItemEntityController Add UseItemCustom SourceInst:{0},LocalInst:{1}",Source.GetHashCode(),Local.GetHashCode());

            HeldItemSubscribeIds.Add(Source.SubscribePropertyChange<long>(UseItemCustom.PropertyIds.SKIN_ID, OnSkinChange));
            HeldItemSubscribeIds.Add(this.Source.SubscribePropertyChange<float>(WeaponCustom.PropertyIds.MAX_CONDITION, OnMaxConditionChange));
        }
        public void OnMaxConditionChange(CustomTypeBase entity, float o, float n)
        {
            Local.MaxCondition = n;
        }
        public void OnSkinChange(CustomTypeBase entity, long oldSkin, long newSkin)
        {
            Local.SkinId = newSkin;
        }
        public override void Release()
        {
            base.Release();
            foreach (long id in HeldItemSubscribeIds)
            {
                Source.UnSubscribePropertyChange(id);
            }
            logger.InfoFormat("LocalHeldItemEntityController Release UseItemCustom SourceInst:{0},LocalInst:{1}",Source.GetHashCode(),Local.GetHashCode());
            Source = null;
            Local = null;
        }

        public override bool CompareHistory(long cmd, HistoryWeaponEntity archiveHistory, IItemEntity server, ref string rst)
        {
            if (archiveHistory.SkinId != server.SkinId)
            {
                Local.SkinId = server.SkinId;
            }
            if (!Mathf.Approximately(archiveHistory.Condition, server.Condition))
            {
                Local.Condition = server.Condition;
            }
            return true;
        }

        public override void RollBack(IItemEntity server)
        {
            Local.SkinId = server.SkinId;
            Local.Condition = server.Condition;
        }
    }

    public class LocalThrowWeaponCustomWrapper :ALocalCustomWrapper
    {
        public ThrowWeaponCustom Source { get;private set; }
        public ThrowWeaponCustom Local{ get;private set; }
        protected HashSet<long> HeldItemSubscribeIds = new HashSet<long>();
        public LocalThrowWeaponCustomWrapper(ThrowWeaponCustom source,ThrowWeaponCustom local)
        {
            S = Source = source;
            L = Local = local;
            Local.LoadTableParam();
            logger.InfoFormat("LocalHeldItemEntityController Add MeleeCustom SourceInst:{0},LocalInst:{1}",Source.GetHashCode(),Local.GetHashCode());
            Local.IsRFDetonation = source.IsRFDetonation;
            HeldItemSubscribeIds.Add(Source.SubscribePropertyChange<long>(ThrowWeaponCustom.PropertyIds.SKIN_ID, OnSkinChange));
            HeldItemSubscribeIds.Add(Source.SubscribePropertyChange<bool>(ThrowWeaponCustom.PropertyIds.IS_RF_DETONATION, OnIsRFDetonationChange));
            HeldItemSubscribeIds.Add(this.Source.SubscribePropertyChange<float>(WeaponCustom.PropertyIds.MAX_CONDITION, OnMaxConditionChange));
        }
        public void OnMaxConditionChange(CustomTypeBase entity, float o, float n)
        {
            Local.MaxCondition = n;
        }
        private void OnIsRFDetonationChange(CustomTypeBase entity, bool oldValue, bool newValue)
        {
            Local.IsRFDetonation = newValue;
        }

        public void OnSkinChange(CustomTypeBase entity, long oldSkin, long newSkin)
        {
            Local.SkinId = newSkin;
        }
        public override void Release()
        {
            base.Release();
            foreach (long id in HeldItemSubscribeIds)
            {
                Source.UnSubscribePropertyChange(id);
            }
            logger.InfoFormat("LocalHeldItemEntityController Release ThrowWeaponCustom SourceInst:{0},LocalInst:{1}",Source.GetHashCode(),Local.GetHashCode());

            Source = null;
            Local = null;
        }

        public override bool CompareHistory(long cmd, HistoryWeaponEntity archiveHistory, IItemEntity server, ref string rst)
        {
            if (archiveHistory.SkinId != server.SkinId)
            {
                Local.SkinId = server.SkinId;
            }
            if (!Mathf.Approximately(archiveHistory.Condition, server.Condition))
            {
                Local.Condition = server.Condition;
            }
            return true;
        }

        public override void RollBack(IItemEntity server)
        {
            Local.SkinId = server.SkinId;
            Local.Condition = server.Condition;
        }
    }

    public class LocalHeldItemCustomWrapper :ALocalCustomWrapper
    {
        public HeldItemCustom Source { get;private set; }
        public HeldItemCustom Local{ get;private set; }
        protected HashSet<long> HeldItemSubscribeIds = new HashSet<long>();
        public LocalHeldItemCustomWrapper(HeldItemCustom source,HeldItemCustom local)
        {
            S = Source = source;
            L = Local = local;
            logger.InfoFormat("LocalHeldItemEntityController Add HeldItemCustom SourceInst:{0},LocalInst:{1}",Source.GetHashCode(),Local.GetHashCode());

            HeldItemSubscribeIds.Add(Source.SubscribePropertyChange<long>(HeldItemCustom.PropertyIds.SKIN_ID, OnSkinChange));
            HeldItemSubscribeIds.Add(Source.SubscribePropertyChange<int>(HeldItemCustom.PropertyIds.COLLECTED_CONTENT, OnCollectChange));
            HeldItemSubscribeIds.Add(this.Source.SubscribePropertyChange<float>(WeaponCustom.PropertyIds.MAX_CONDITION, OnMaxConditionChange));
        }
        public void OnMaxConditionChange(CustomTypeBase entity, float o, float n)
        {
            Local.MaxCondition = n;
        }
        public void OnSkinChange(CustomTypeBase entity, long oldSkin, long newSkin)
        {
            Local.SkinId = newSkin;
        }
        public void OnCollectChange(CustomTypeBase entity, int oldV, int newV)
        {
            Local.CollectedContent = newV;
        }
        public override void Release()
        {
            base.Release();
            foreach (long id in HeldItemSubscribeIds)
            {
                Source.UnSubscribePropertyChange(id);
            }
            logger.InfoFormat("LocalHeldItemEntityController Release UseItemCustom SourceInst:{0},LocalInst:{1}",Source.GetHashCode(),Local.GetHashCode());
            Source = null;
            Local = null;
        }

        public override bool CompareHistory(long cmd, HistoryWeaponEntity archiveHistory, IItemEntity server, ref string rst)
        {
            if (archiveHistory.SkinId != server.SkinId)
            {
                Local.SkinId = server.SkinId;
            }
            if (!Mathf.Approximately(archiveHistory.Condition, server.Condition))
            {
                Local.Condition = server.Condition;
            }
            return true;
        }

        public override void RollBack(IItemEntity server)
        {
            Local.SkinId = server.SkinId;
            Local.Condition = server.Condition;
        }
    }

    public class LocalWeaponAccessoryItemCustomWrapper :ALocalCustomWrapper
    {
        public WeaponAccessoryItemCustom Source { get;private set; }
        public WeaponAccessoryItemCustom Local{ get;private set; }
        protected HashSet<long> HeldItemSubscribeIds = new HashSet<long>();
        public LocalWeaponAccessoryItemCustomWrapper(WeaponAccessoryItemCustom source,WeaponAccessoryItemCustom local)
        {
            S = Source = source;
            L = Local = local;
            logger.InfoFormat("LocalHeldItemEntityController Add WeaponAccessoryItemCustom SourceInst:{0},LocalInst:{1}",Source.GetHashCode(),Local.GetHashCode());

            HeldItemSubscribeIds.Add(Source.SubscribePropertyChange<int>(WeaponAccessoryItemCustom.PropertyIds.STATUS, OnStatusChange));
            HeldItemSubscribeIds.Add(Source.SubscribePropertyChange<float>(WeaponAccessoryItemCustom.PropertyIds.CONDITION, OnConditionChange));
            HeldItemSubscribeIds.Add(Source.SubscribePropertyChange<long>(WeaponAccessoryItemCustom.PropertyIds.SKIN_ID, OnSkinChange));
            HeldItemSubscribeIds.Add(this.Source.SubscribePropertyChange<float>(WeaponCustom.PropertyIds.MAX_CONDITION, OnMaxConditionChange));
        }
        public void OnMaxConditionChange(CustomTypeBase entity, float o, float n)
        {
            Local.MaxCondition = n;
        }
        public void OnStatusChange(CustomTypeBase entity, int oldStatus, int newStatus)
        {
            Local.Status = newStatus;
        }

        public void OnConditionChange(CustomTypeBase entity, float oldCondition, float newCondition)
        {
            Local.Condition = newCondition;
        }

        public void OnSkinChange(CustomTypeBase entity, long oldSkin, long newSkin)
        {
            Local.SkinId = newSkin;
        }
        public override void Release()
        {
            base.Release();
            foreach (long id in HeldItemSubscribeIds)
            {
                Source.UnSubscribePropertyChange(id);
            }
            logger.InfoFormat("LocalHeldItemEntityController Release WeaponAccessoryItemCustom SourceInst:{0},LocalInst:{1}",Source.GetHashCode(),Local.GetHashCode());

            Source = null;
            Local = null;
        }

        public override bool CompareHistory(long cmd, HistoryWeaponEntity archiveHistory, IItemEntity server, ref string rst)
        {
            //暂不实现配件
            return true;
        }

        public override void RollBack(IItemEntity server)
        {
            //暂不实现配件
        }
    }
}
