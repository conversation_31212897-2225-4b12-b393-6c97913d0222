#参数1：时间 例如（20250212170105）即 2025-2-12 17：01：05
#参数2：设备名
#参数3：casename
#参数4：数据目录
from datetime import datetime
import sys
import os
import requests
import json
from io import StringIO
from collections import defaultdict

profilers_tag_frameindesdic = {}

class Metadata:
    def __init__(self, file_name, file_device, file_data, file_time):
        self.FileName = file_name
        self.FileDevice = file_device
        self.FileData = file_data
        self.FileTime = file_time

class Metadata_:
    def __init__(self, file_data, file_time):
        self.FileData = file_data
        self.FileTime = file_time

def split_data_async(timepathfirst, device_name, timepathsecond):
    url = "http://192.168.8.188:9003"
    file_data = os.path.join(timepathfirst, device_name)  # 拼接文件路径
    metadata = Metadata_(file_data, timepathsecond)
    
    # 将 metadata 转换为 JSON
    metadata_json = json.dumps(metadata.__dict__)

    # 设置请求头
    headers = {'Content-Type': 'application/json'}

    # 发送 POST 请求
    try:
        response = requests.post(url, data=metadata_json, headers=headers, timeout=600)  # 超时设置为 10 分钟
        response.raise_for_status()  # 检查请求是否成功
        print("Response:", response.text)  # 输出响应内容
    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")

keys = ["fps", "mem", "cpu", "gpu", "gputime" ,"frameindex"]
def split_csv_data(filedata):
    if filedata:
        lines = filedata.split('\n')
    else:
        return ""
    key_value_pairs = defaultdict(list)

    # 从第一行开始遍历 (skip header)
    for i in range(1, len(lines)):
        line = lines[i].strip()  # 去除前后空白字符
        if not line:  # 跳过空行
            continue
        
        items = line.split(',')
        if len(items) < len(keys):  # 确保行中列的数量满足要求
            continue 
        for j in range(len(keys)):
            key = keys[j]
            key_value_pairs[key].append(items[j].strip())  # 去除每个 item 的前后空格

    return deal_file_data(key_value_pairs)

def split_csv_data_mem(filedata):
    if filedata:
        lines = filedata.split('\n')
    else:
        return ""
    key_value_pairs = defaultdict(list)  # 使用 defaultdict 以便简化添加元素的操作

    for line in lines:
        if line.strip() == "":
            continue  # 跳过空行
        items = line.split(';')
        for item in items:
            subitems = item.split(':')
            if len(subitems) < 2:
                continue
            key, value = subitems[0].strip(), subitems[1].strip()  # 去除多余的空格
            key_value_pairs[key].append(value)

    return deal_file_data(key_value_pairs)

def deal_file_data(data):
    return json.dumps({key: value for key, value in data.items()}, ensure_ascii=False)

def get_data_from_file(path):
    try:
        # 发送 GET 请求
        response = requests.get(path)
        response.raise_for_status()  # 如果响应状态码不是 200，将抛出异常
        # 设置正确的编码
        response.encoding = 'utf-8'
        return response.text  # 返回响应内容
    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")
        return None
    
def upload_data_async(file_path, device_name,time_path_first, time_path_second, upload_url):
    try:
        # 获取文件信息与数据
        file_info = os.path.splitext(os.path.basename(file_path))
        file_data = open(file_path, 'r', encoding='utf-8').read()
        time = time_path_first
        timespan = time_path_second
        file_name = f"{timespan}_{file_info[0]}.txt"  # 格式化文件名

        # 构建元数据
        metadata = Metadata(file_name, device_name, file_data, time)
        metadata_json = json.dumps(metadata.__dict__)

        headers = {'Content-Type': 'application/json'}
        timeout = 300  # Timeout set to 5 minutes

        # 尝试依次上传数据
        response = requests.post(upload_url, data=metadata_json, headers=headers, timeout=timeout)
        if response.status_code == requests.codes.ok:  # If successful
            return f"http://192.168.8.188:9200/{time}/{device_name}/{file_name}"
        else:
            print(f"Attempt to upload to {upload_url} failed with status code {response.status_code}")

        # 如果所有上传 URL 都失败，打印错误信息
        print(f"{file_name} 上传数据失败")
        return None

    except Exception as e:
        print(f"An error occurred: {str(e)}")
        return None
    
def upload_data_async_by_data(file_data, name, device_name, time_path_first, time_path_second, upload_url):
    # 定义文件名
    time = time_path_first  # 根据需要获取当前时间
    file_name = f"{time_path_second}_{name}.txt"

    # 构建元数据对象
    metadata = Metadata(file_name, device_name, file_data, time)
    metadata_json = json.dumps(metadata.__dict__)

    if not metadata_json:
        return f"{name}_数据为空"

    headers = {'Content-Type': 'application/json'}
    timeout = 300  # Timeout set to 5 minutes

    # 尝试依次上传数据
    try:
        # 尝试进行请求
        response = requests.post(upload_url, data=metadata_json, headers=headers, timeout=timeout)
        response.raise_for_status()  # 如果返回状态码不是 200，抛出异常
        if response.status_code == requests.codes.ok:
            return f"http://192.168.8.188:9200/{time}/{device_name}/{file_name}"
    except requests.exceptions.Timeout:
        print("请求超时")
    except requests.exceptions.ConnectionError:
        print("连接错误，无法连接到远程服务器")
    except requests.exceptions.HTTPError as e:
        print(f"HTTP 错误: {e.response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
    
    # 如果所有上传 URL 都失败，记录错误
    print(f"{file_name} 上传数据失败")
    return None

def set_sql_data(sb: StringIO, title: str, url: str):
    sb.write(f"title: {title}, origin_url: {url}\n")

def set_sql_data_with_data(sb: StringIO, title: str, url: str, data: str, is_int_type: bool = False, is_end: bool = False):
    if is_int_type:
        sb.write('{"type": "int", "start_time": ')
    else:
        sb.write('{"type": "json", "start_time": ')

    start_time = profilers_tag_frameindesdic.get(title, 0)
    sb.write(f"{start_time}, \"end_time\": 0, \"tags\": [], \"title\": \"{title}\", \"origin_url\": \"{url}\", \"data\": {data}")

    if is_end:
        sb.write("}")
    else:
        sb.write("},")

def split_csv_data_profiler(filedata):
    if filedata:
        lines = filedata.split('\n')
    else:
        return ""
    key_value_pairs = {}
    first_keys = [
        "Anim", 
        "UI", 
        "Net", 
        "Effect", 
        "Render", 
        "Audio", 
        "Physics", 
        "Battle team", 
        "System team",
        "Pve team",
        "Arch team",
        "PocoMgr",
        "AnimBegin",
        "AnimEnd",
        "AnimSpecial",
        "PhysicsDetails",
        "gcdetailslist",
        "custom"
    ]
    sb = []
    appdstr = ""
    allstr = "{"

    for i in range(1, len(lines)):
        line = lines[i].strip()
        if line == '':
            continue
        
        items = line.split(',')
        if len(items) < 2:
            continue

        stipvalue = items[2].strip() if len(items) >= 3 else "0"
        stipvalue = stipvalue if stipvalue else "0"
        stipvalue = stipvalue if items[1].strip() else "0"
        
        title = items[0].strip()
        
        # 处理标题
        if title == "PocoMgr":
            title = "poco_mgr"
        elif title == "GpuTime":
            title = "GPUTime"
        elif title == "其他":
            title = "others"

        if title not in first_keys:
            if title == "Unity_WaitForLastPresent":  # 特殊处理
                item = f'"{title}": {{"ave_cost": {stipvalue}, "cost_per_frame": {items[1].strip()}}}'
                allstr += f'"wait_ldp": {{"ave_cost_total_time": {stipvalue}, "cost_per_frame_total_time": {items[1].strip()}, {item}}},'
            else:
                if not sb:
                    sb.append(f'"{title}": {{"ave_cost": {stipvalue}, "cost_per_frame": {items[1].strip()}}}')
                else:
                    sb.append(f', "{title}": {{"ave_cost": {stipvalue}, "cost_per_frame": {items[1].strip()}}}')
        else:
            scendstr = ''.join(sb)
            if title == "custom":  # 特殊处理
                if not scendstr:
                    appdstr = f'"{title}": {{"AveValue": {stipvalue}, "ValuePerFrame": {items[1].strip()}}},'
                else:
                    scendstr = scendstr.replace("ave_cost", "AveValue").replace("cost_per_frame", "ValuePerFrame")
                    appdstr = f'"{title}": {{"AveValue_total": {stipvalue}, "ValuePerFrame_total": {items[1].strip()}, {scendstr}}},'
            else:
                if not scendstr:
                    appdstr = f'"{title}": {{"ave_cost_total_time": {stipvalue}, "cost_per_frame_total_time": {items[1].strip()}}},'
                else:
                    appdstr = f'"{title}": {{"ave_cost_total_time": {stipvalue}, "cost_per_frame_total_time": {items[1].strip()}, {scendstr}}},'
            
            allstr += appdstr
            sb.clear()
            appdstr = ""

    allstr += ''.join(sb)
    allstr += "}"
    return allstr

def UploadPerformanceDataAsync():
    sb = StringIO()
    sb_sql = StringIO()
    sb_sql.write("{\"case_data\": [")

    parsed_time = datetime.strptime(sys.argv[1], '%Y%m%d%H%M%S')
    timepathfirst = f"{parsed_time.year}/{parsed_time.month:02}/{parsed_time.strftime('%Y%m%d')}/{ sys.argv[3]}"
    timepathsecond = parsed_time.strftime('%H%M%S')
    deviceName = f"{sys.argv[2].replace(' ', '_')}/{timepathsecond}"
    root_folder = sys.argv[4]

    log_files_profiler = []
    log_files_log = []
    for root, dirs, files in os.walk(root_folder):
        for file in files:
            filestr = str(file)
            file_path = os.path.join(root, file)
            if filestr == f"{timepathsecond}_Other":
                with open(file_path, 'r',encoding='utf-8') as file:
                    lines = file.readlines()
                if len(lines) > 1:
                    filedata_error = lines[1]
                    filedata_averagefps = lines[0]
            if filestr == f"{timepathsecond}_DC":
                with open(file_path, 'r',encoding='utf-8') as file:
                    filedata_dc = file.read()
            if filestr.startswith("Memorydata"):
                log_filepath_memory = file_path
            if filestr.startswith("WinOpenCostRec"):
                with open(file_path, 'r',encoding='utf-8') as file:
                    filedata_UI = file.read()
            if filestr.startswith("performancedata"):
                log_filepath_common = file_path
            if filestr.startswith("profile_"):
                log_files_profiler.append(file_path)
            if filestr == "config_profiler.txt":
                with open(file_path, 'r',encoding='utf-8') as file:
                    config_profiler = file.read()
                    ProfilerStepNames = config_profiler.split(';')
            if filestr.startswith("ClentLog"):
                log_files_log.append(file_path)
    #DC
    send_msg = []
    try:
        # 将 JSON 字符串解析为列表的字典
        temp = json.loads(filedata_dc)
        send_msg = list(temp)  # 复制到 send_msg
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON: {e}")  # 类似于 Debug.Log
    dc = defaultdict(list)  # 使用 defaultdict 方便追加元素
    for item in send_msg:
        for key in item.keys():
            dc[key].append(item[key])
    dcpath = upload_data_async_by_data(filedata_dc,"DC",deviceName,timepathfirst,timepathsecond,"http://192.168.8.188:9001")
    set_sql_data(sb, "DC", dcpath)
    set_sql_data_with_data(sb_sql, "DC", dcpath, deal_file_data(dc)) 
    print("dc upload...")
    
    #profiler
    path = []
    frameindexlists = []
    for log_file in log_files_profiler:
        if os.path.exists(log_file):  # 检查文件是否存在
            with open(log_file, 'r', encoding='utf-8') as file:  # 读取文件
                lines = file.readlines()  # 读取所有行
                for i in range(1, len(lines)):
                    if lines[i].startswith("基本信息") and "当前帧索引" in lines[i]:  # 检查条件
                        frameindex = lines[i].split(' ')[4].split('[')[1].replace("]", "")
                        frameindexlists.append(int(frameindex))  # 将帧索引添加到列表中
                        break
        path.append(upload_data_async(log_file,deviceName,timepathfirst,timepathsecond,"http://192.168.8.188:9001"))
    print("profiler upload...")
    split_data_async(timepathfirst,deviceName,timepathsecond)
    print("profiler split...")

    profilers_tag_frameindesdic[f"profiler_{ProfilerStepNames[0]}"] = 0
    for i in range(1, len(ProfilerStepNames)):
        profilers_tag_frameindesdic[f"profiler_{ProfilerStepNames[i]}"] = frameindexlists[i - 1]

    for i in range(0,len(log_files_profiler)):
        url_profiler = path[i].replace(".txt", "_analyze.csv")
        data = get_data_from_file(url_profiler)
        set_sql_data(sb, ProfilerStepNames[i], url_profiler)
        set_sql_data_with_data(sb_sql, f"profiler_{ProfilerStepNames[i]}", url_profiler, split_csv_data_profiler(data))
    
    if len(log_files_profiler) >1:
        sum_analyze = f"http://192.168.8.188:9200/{timepathfirst}/{deviceName}/{timepathsecond}_Sum_analyze.csv"
        set_sql_data(sb, "Sum_profiler_analyze", sum_analyze)
        filedata_sum_analyze = get_data_from_file(sum_analyze)
        set_sql_data_with_data(sb_sql, "Sum_profiler_analyze", sum_analyze, split_csv_data_profiler(filedata_sum_analyze))
    elif len(log_files_profiler) ==1:
        url_profiler = path[0].replace(".txt", "_analyze.csv")
        data = get_data_from_file(url_profiler)
        set_sql_data_with_data(sb_sql, "Sum_profiler_analyze", url_profiler, split_csv_data_profiler(data))

    #common
    urlcommon = upload_data_async(log_filepath_common,deviceName,timepathfirst,timepathsecond,"http://192.168.8.188:9001")
    filedata_common = get_data_from_file(urlcommon)
    set_sql_data(sb, "common", urlcommon)
    set_sql_data_with_data(sb_sql, "common", urlcommon, split_csv_data(filedata_common))
    print("common upload...")

    #memopry
    # urlmemopry = upload_data_async(log_filepath_memory,deviceName,timepathfirst,timepathsecond,"http://192.168.8.188:9001")
    # filedata_memory = get_data_from_file(urlmemopry)
    # set_sql_data(sb, "memory", urlmemopry)
    # set_sql_data_with_data(sb_sql, "memory", urlmemopry, split_csv_data_mem(filedata_memory))
    # print("memory upload...")

    #errorcount
    url_error = upload_data_async_by_data(filedata_error,"errorcount",deviceName,timepathfirst,timepathsecond,"http://192.168.8.188:9001")
    set_sql_data(sb, "errorcount", url_error)
    set_sql_data_with_data(sb_sql, "errorcount", url_error, filedata_error,True)
    print("errorcount upload...")

    #fps
    url_fps = upload_data_async_by_data(filedata_averagefps,"averagefps",deviceName,timepathfirst,timepathsecond,"http://192.168.8.188:9001")
    set_sql_data(sb, "averagefps", url_fps)
    set_sql_data_with_data(sb_sql, "averagefps", url_fps, filedata_averagefps,False,True)
    print("fps upload...")

    #入库数据上传
    sb_sql.write("]}")
    url = upload_data_async_by_data(sb_sql.getvalue(), "sqldata",deviceName,timepathfirst,timepathsecond,"http://192.168.8.188:9001")
    set_sql_data(sb, "sqldata", url)
    print("sql upload...")

    resulturl = upload_data_async_by_data(sb.getvalue(), "Result",deviceName,timepathfirst,timepathsecond,"http://192.168.8.188:9001")
    print("result upload...")

    #ui
    url_ui = upload_data_async_by_data(filedata_UI, "WinOpenCostRec",deviceName,timepathfirst,timepathsecond,"http://192.168.8.188:9001")
    print("ui upload...")

    #log
    for log in log_files_log:
        url_log = upload_data_async(log,deviceName,timepathfirst,timepathsecond,"http://192.168.8.188:9001")
    print("log upload...")

UploadPerformanceDataAsync()