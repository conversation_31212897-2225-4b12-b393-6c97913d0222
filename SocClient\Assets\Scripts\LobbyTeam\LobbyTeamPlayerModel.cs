using Cinemachine;
using Cysharp.Text;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Client.Lobby;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.Combat;
using WizardGames.Soc.Common.Unity.Config;
using WizardGames.Soc.Common.Unity.Extend;
using WizardGames.Soc.SocClient.GoLoader;

namespace WizardGames.Soc.SocClient.Manager
{
    /// <summary>
    /// 创建模型的类型
    /// </summary>
    public enum ELobbyModelType
    {
        LobbyTeamModel,//大厅组队
        HistoryModel,//历史战局
        MainModel,//主界面
        AppointmentTeamModel,//预约组队
    }
    
    /// <summary>
    /// 大厅模型创建关联数据（目前使用场景：历史战局、组队场景）
    /// </summary>
    public class LobbyModelData
    {
        public Dictionary<int, LobbDisplayModel> playersDic;
        public Dictionary<int, PlayerDisplayData> displayDataDic;

        public LobbyModelData()
        {
            playersDic = new();
            displayDataDic = new();
        }
    }

    /// <summary>
    /// 单个模型相关数据
    /// </summary>
    public class LobbDisplayModel
    {
        public DisplayModel displayModel;//用与展示的人物外观
        public float createTime;//创建时间
    }
    /// <summary>
    /// 组队场景和历史战局场景模型显示处理
    /// </summary>
    public class LobbyTeamPlayerModel
    {
        private static SocLogger Log = LogHelper.GetLogger(typeof(LobbyTeamPlayerModel));

        /// <summary>
        /// 把大厅模型整合起来
        /// </summary>
        public Dictionary<ELobbyModelType, LobbyModelData> playerModelsDic = new();
        
        private readonly float[] hideTime = { 0.15f, 1.1f, 0.8f, 1.15f };
        private readonly float animTime = 1f;

        public bool hasMaterials = true;
        
        private List<int> keysToRemove = new List<int>();
        
        private LobbySceneModelPoint lobbyModelPoint;

        /// <summary>
        /// 队长timeline位置
        /// </summary>
        private readonly string TEAM_TRACK = "TeamTrack4";

        public void InitLobbyModelPoint()
        {
            lobbyModelPoint = Object.FindObjectOfType<LobbySceneModelPoint>();
            if (lobbyModelPoint == null)
            {
                Log.Error("LobbySceneModelPoint is null");
            }
        }

        public GameObject GetLobbyMainModelRoot()
        {
            if (lobbyModelPoint == null || lobbyModelPoint.playerLoaderMain == null) return null;
            return lobbyModelPoint.playerLoaderMain;
        }

        public CinemachineVirtualCamera GetLobbyMainVirtualCamera()
        {
            if (lobbyModelPoint == null) return null;
            return lobbyModelPoint?.transform.Find("Steps/VirtualCamHome").GetComponent<CinemachineVirtualCamera>();
        }

        public CinemachineVirtualCamera GetLobbyTeamVirtualCamera()
        {
            if (lobbyModelPoint == null) return null;
            return lobbyModelPoint?.transform.Find("Steps/VirtualCamTeam").GetComponent<CinemachineVirtualCamera>();
        }

        /// <summary>
        /// 绑定组队每个模型的timeline
        /// </summary>
        public void PlayerBindTimeline()
        {
            var steps = LobbyStepController.Instance.GetStepsOfType<LobbyStepTimeline>();
            if (null == steps || 0 == steps.Count) return;
            var stepTimeLine = steps[0];
            var playersDic = playerModelsDic[ELobbyModelType.LobbyTeamModel].playersDic;
            foreach (var player in playersDic)
            {
                var playerTrackName = ZString.Concat("TeamTrack", player.Key);
                var animtor = player.Value.displayModel.ModelAnimator;
                stepTimeLine.ChangeBinding(playerTrackName, animtor);
            }

        }
        
        /// <summary>
        /// 删除指定类型players
        /// </summary>
        public void ReleaseDisplayPlayers(ELobbyModelType type = ELobbyModelType.LobbyTeamModel)
        {
            if(!playerModelsDic.ContainsKey(type))
                return;
            var playersDic = playerModelsDic[type].playersDic;
            hasMaterials = false; //销毁的时候假如正在更新材质，关掉更新
            //防一手销毁的时候，材质还在更新
            if(playersDic == null || playersDic.Count == 0)
                return;
            keysToRemove.Clear();
            foreach (var player in playersDic)
            {
                keysToRemove.Add(player.Key);
            }
            foreach (var key in keysToRemove)
            {
                DeletePlayer(type,key);
            }
            playersDic.Clear();
            var displayDataDic = playerModelsDic[type].displayDataDic;
            displayDataDic.Clear();
        }

        /// <summary>
        /// 删除所有model
        /// </summary>
        /// <param name="type"></param>
        public void ReleaseAllDisplayPlayers()
        {
            foreach (var (type, modelData) in playerModelsDic)
            {
                ReleaseDisplayPlayers(type);
            }
            playerModelsDic.Clear();
            
        }
        
        public void UpdateMaterials(ELobbyModelType type = ELobbyModelType.LobbyTeamModel)
        {
            if (!hasMaterials || !playerModelsDic.ContainsKey(type))
            {
                return; 
            }
            var playersDic = playerModelsDic[type].playersDic;
            var now = Time.time;
            foreach (var (key, pair) in playersDic)
            {
                var creationTime = pair.createTime + hideTime[key];
                var endTime = creationTime + animTime;
                
                if( now >= endTime) continue;
                var lerp = Mathf.InverseLerp(creationTime, endTime, now);
                var materials = GetMaterials(pair.displayModel);//异步可能拿不到最新的材质，所以不做缓存，这里now >= endTime 会拦截，无需担心一直遍历的问题
                if(materials == null) continue;
                foreach (var material in materials)
                {
                    material.SetFloat(PropertyToID.DitherJoinTeamHash, lerp);
                    //TA建议开始的时候“_DITHER_ON”开启，结束的时候关闭,有优化效果.这里0.9f是一个经验值，目前是4个角色进场动画结束时都是大于0.9f
                    if (material.GetFloat(PropertyToID.DitherJoinTeamHash) >= 0.9f)
                    {
                        material.SetFloat(PropertyToID.DitherJoinTeamHash, 1);
                        OnPartModelLoaded(pair.displayModel.ModelAnimator);
                        material.DisableKeyword("_DITHER_ON");
                    }
                    else
                    {
                        material.EnableKeyword("_DITHER_ON");
        
                    }
                }
            }
            
        }
        

        public Vector3 GetPlayerParentPos(int index)
        {
            if (lobbyModelPoint == null) return Vector3.zero;
            if (lobbyModelPoint.playerLoaderTeams == null) return Vector3.zero;
            var playerLoaderTeam = lobbyModelPoint.playerLoaderTeams[index];
            if(playerLoaderTeam == null) return Vector3.zero;    
            var loader = playerLoaderTeam.transform.Find("playerUIPosRoot");
            if (loader == null) return Vector3.zero;
            
            return loader.position;
        }
        
        public bool IsPlayerModelsDicNull(ELobbyModelType type)
        {
            if(Mc.LobbyTeam == null || Mc.LobbyTeam.lobbyTeamPlayerModel == null) return true;
            return playerModelsDic == null || !playerModelsDic.ContainsKey(type) || playerModelsDic[type].playersDic == null || playerModelsDic[type].playersDic.Count == 0;
        }
        /// <summary>
        /// 设置模型是否显示，可以用来控制是否显示进场动画
        /// </summary>
        public void SetModelActive(ELobbyModelType type, bool show)
        {
            if(!playerModelsDic.ContainsKey(type))
                return;
            var playersDic = playerModelsDic[type].playersDic;
            foreach (var (index,player) in playersDic)
            {
                player.displayModel.SetActive(show);
            }
            
        }
        /// <summary>
        /// 根据类型获取某个类型模型总数据
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public LobbyModelData GetPlayerModelByType(ELobbyModelType type)
        {
            if (playerModelsDic.ContainsKey(type))
            {
                return playerModelsDic[type];
            }

            return null;
        }
        /// <summary>
        /// 删除index的模型
        /// </summary>
        /// <param name="index"></param>
        public void DeletePlayer(ELobbyModelType type,int index)
        {
            var playerModel = GetPlayerModelByType(type);
            Log.InfoFormat("DeletePlayer--index:{0}", index);
            if (playerModel != null && playerModel.playersDic != null && playerModel.playersDic.ContainsKey(index))
            {
                Log.InfoFormat("DeletePlayer--success index:{0} type:{1}", index, type);
                var playersDic = playerModel.playersDic;
                playersDic[index].displayModel.Release();
                playersDic.Remove(index);
                var displayDataDic = playerModel.displayDataDic;
                displayDataDic.Remove(index);
            }
        }
        /// <summary>
        /// 创建一种类型模型数据
        /// </summary>
        /// <param name="type"></param>
        /// <param name="index"></param>
        /// <param name="displayData"></param>
        public void CreatePlayModel(ELobbyModelType type, int index, PlayerDisplayData displayData, bool isPlayAnim = true)
        {
            Log.InfoFormat("CreatePlayer  ELobbyModelType：{0}--index:{1}", type, index);
            if (lobbyModelPoint == null ) return ;
            if (lobbyModelPoint.playerLoaderTeams == null) return;
            var playerLoaderTeam = lobbyModelPoint.playerLoaderTeams[index];
            var objPlayerModel = PlayerLoader.GetSkeletonWithHead();
            objPlayerModel.transform.SetParent(playerLoaderTeam.transform);
            objPlayerModel.transform.localPosition = Vector3.zero;
            objPlayerModel.transform.localScale = Vector3.one;
            objPlayerModel.transform.localEulerAngles = Vector3.zero;
            var objPlayerModelPoint = objPlayerModel.AddComponent<ObjectPointComponent>();
            objPlayerModelPoint.InitData();
            
            var displayModel = new DisplayModel(new PlayerData(displayData), objPlayerModelPoint, ModelType.Lb, MgrConfigPhysicsLayer.LayerDefault, true);
            var animatorStr = Mc.Tables.TbChracterParameter.UiAnimatorTp;
            if (isPlayAnim)
            {
                animatorStr = Mc.Tables.TbGlobalConfig.LobbyTeamAnimatorOver1;
                switch (index)
                {
                    case 1:
                        animatorStr = Mc.Tables.TbGlobalConfig.LobbyTeamAnimatorOver2;
                        break;
                    case 2:
                        animatorStr = Mc.Tables.TbGlobalConfig.LobbyTeamAnimatorOver3;
                        break;
                    case 3:
                        animatorStr = Mc.Tables.TbGlobalConfig.LobbyTeamAnimatorOver4;
                        break;
                }
                displayModel.NeedReloadAnimClip = false;
            }
            displayModel.CreateAnimator(animatorStr);
            
            displayModel.Show(true);
            displayModel.CheckBackWeapon();
            displayModel.UpdateAllPart();//这个会重置自身动画
            if (isPlayAnim)
            {
                displayModel.ModelAnimator.Play("start", 0, 0);
                displayModel.ModelAnimator.Update(0);
                OnPartModelLoaded(displayModel.ModelAnimator, AnimatorCullingMode.AlwaysAnimate);
            }
            else
            {
                displayModel.ModelAnimator.cullingMode = AnimatorCullingMode.AlwaysAnimate;
            }
            
            LobbDisplayModel playModel = new LobbDisplayModel();
            playModel.displayModel = displayModel;
            playModel.createTime = Time.time;
            
            if(!playerModelsDic.ContainsKey(type))
            {
                var lobbyModelData = new LobbyModelData();
                if (!lobbyModelData.playersDic.ContainsKey(index))
                {
                    lobbyModelData.playersDic.Add(index, playModel);
                    lobbyModelData.displayDataDic.Add(index, displayData);
                }
                else
                {
                    lobbyModelData.playersDic[index] = playModel;
                    lobbyModelData.displayDataDic[index] = displayData;
                }
                playerModelsDic.Add(type, lobbyModelData);
            }
            else
            {
                var lobbyModelData = playerModelsDic[type];
                if (!lobbyModelData.playersDic.ContainsKey(index))
                {
                    lobbyModelData.playersDic.Add(index, playModel);
                    lobbyModelData.displayDataDic.Add(index, displayData);
                }
                else
                {
                    lobbyModelData.playersDic[index] = playModel;
                    lobbyModelData.displayDataDic[index] = displayData;
                }
            }
        }

        public void SetGoLayer(int layer)
        {
            var playerModel = GetPlayerModelByType(ELobbyModelType.LobbyTeamModel);
            if (playerModel != null && playerModel.playersDic != null)
            {
                foreach (var model in playerModel.playersDic)
                {
                    model.Value.displayModel.SetGoLayer(layer);
                }
            }
        }
        
        public void PutOnPlan(PlayerDisplayData displayData, DisplayModel displayModel, Dictionary<int, int> useDic, Dictionary<int, int> useCostume)
        {
            displayData.EquipmentDisplayDatas.Clear();
          
            foreach (var item in useDic)
            {
                if (item.Value != 0)
                {
                    var skinCfg = Mc.Tables.TBSkin.GetOrDefault(item.Value);
                    displayData.EquipmentDisplayDatas.Add(new EquipmentDisplayData { TableId = skinCfg.OwnerID, SkinId = skinCfg.ID });
                }
            }
          
            foreach (var item in useCostume)
            {
                if (item.Value != 0)
                {
                    var skinCfg = Mc.Tables.TBSkin.GetOrDefault(item.Value);
                    displayData.EquipmentDisplayDatas.Add(new EquipmentDisplayData { TableId = skinCfg.ID });
                }
            }
            displayModel.UpdateAllPart();
        }
        
        public void GetPlayerDisplayData(ELobbyModelType type, int index, out LobbDisplayModel displayModel, out PlayerDisplayData displayData)
        {
            displayModel = null;
            displayData = null;
            var playerModel = GetPlayerModelByType(type);
            if (playerModel != null && playerModel.playersDic.ContainsKey(index))
            {
                displayModel = playerModel.playersDic[index];
                displayData = playerModel.displayDataDic[index];
            }
        }
        
        public void UpdateLobbyModelData(ELobbyModelType type, int index, PlayerDisplayData displayData)
        {
            var playerModel = GetPlayerModelByType(type);
            if (playerModel != null && playerModel.displayDataDic.ContainsKey(index))
            {
                playerModel.displayDataDic[index].CopyFrom(displayData);
            }
        }

        public List<Material> GetMaterials(DisplayModel displayModel)
        {
            List<Material> materials = new();//这里只能new，不然多个displayModel引用的是同一个
            if (hasMaterials)
            {
                var objPlayerModel = displayModel.GetModelPoint();
                var renderers = objPlayerModel.GetComponentListInChildrenByPool<MeshRenderer>();//头发是MeshRenderer
                var skinRenderers = objPlayerModel.GetComponentListInChildrenByPool<SkinnedMeshRenderer>();//身体是SkinnedMeshRenderer

                for (int i = 0; i < renderers.Count; i++)
                {
                    MeshRenderer renderer = renderers[i];
                    for (int j = 0; j < renderer.materials.Length; j++)
                    {
                        Material material = renderer.materials[j];
                        materials.Add(material);
                        material.SetFloat(PropertyToID.DitherJoinTeamHash, 0);
                    }
                }
                for (int i = 0; i < skinRenderers.Count; i++)
                {
                    SkinnedMeshRenderer renderer = skinRenderers[i];
                    for (int j = 0; j < renderer.materials.Length; j++)
                    {
                        Material material = renderer.materials[j];
                        materials.Add(material);
                        material.SetFloat(PropertyToID.DitherJoinTeamHash, 0);
                    }
                }
                Pool.ReleaseList(renderers);
                Pool.ReleaseList(skinRenderers);
            }
            else
            {
                displayModel.SetAnimIdle();
            }

            return materials;
        }

        public void OnPartModelLoaded(Animator animator, AnimatorCullingMode mode = AnimatorCullingMode.CullUpdateTransforms)
        {
            if (animator != null)
            {
                animator.cullingMode = mode;
            }
        }

        /// <summary>
        /// 组队进新服设置最后上船动画绑定
        /// </summary>
        /// <param name="parent"></param>
        public void SetCaptainParent(Transform parent)
        {
            var steps = LobbyStepController.Instance.GetStepsOfType<LobbyStepTimeline>();
            if (null == steps || 0 == steps.Count) return;
            var stepTimeLine = steps[0];
            var playersDic = playerModelsDic[ELobbyModelType.LobbyTeamModel].playersDic;
            foreach (var player in playersDic)
            {
                var playerTrackName = ZString.Concat("BoatingTrack", player.Key);
                var animtor = player.Value.displayModel.ModelAnimator;
                stepTimeLine.ChangeBinding(playerTrackName, animtor);
                player.Value.displayModel.SetParent(parent);
            }
        }
    }
}