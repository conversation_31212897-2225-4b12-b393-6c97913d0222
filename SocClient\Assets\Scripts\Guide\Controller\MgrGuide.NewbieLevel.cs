using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AzureSky;
using WizardGames.Soc.Common.Data.guide;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.PersistentData;
using WizardGames.Soc.SocClient.Ui;
using WizardGames.Soc.Common.Unity.ObjPool;
using Combat;
using FairyGUI;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Profile;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.SocClient.Timeline;

namespace WizardGames.Soc.SocClient.Guide
{
    public partial class MgrGuide
    {
        protected static SocLogger logger = LogHelper.GetLogger(typeof(MgrGuide));
        //屏蔽登录开关
        private bool disableNewbieLevel = false;
        private long curMissionId = -1;

        private const long BUILD_DOOR_TASK_ID = 2040110;


        private bool isNewbieLevelFinished = false;


        private List<long> manualViewGuideTrigger = new List<long> { 6604 };

        /// <summary>
        /// 在这个列表中的任务对应的引导id不会触发引导，需要在各自业务代码里监听事件（UpdateSingleMission、UpdateTrackMissionId）触发引导
        /// </summary>
        private readonly List<int> newbieLevelGuideIdList = new()
        {
            2074
        };

        private List<Vector3> guideLineTarget = new List<Vector3>();

        private GameObject effectObj;

        public bool cartoonVideoFinished = false;

        public bool subtitleFinished = false;
        public long subtitleTaskId = 0;

        private string bgAudioName = string.Empty;

        public bool IsNewbieLevelFinished
        {
            get
            {
#if !PUBLISH
                var commonPersistData = Mc.PersistentData.GetAccountSaveData<CommonAccountPersistentData>(PersistentDataType.Common);
                return (commonPersistData != null && commonPersistData.isNewbieFinished) || disableNewbieLevel || isNewbieLevelFinished;
#else
                return disableNewbieLevel || isNewbieLevelFinished;
#endif
            }
        }

        private void OnNewbieLevelMissionChanged()
        {
            if (Mc.Mission.HasMission) 
            {
                var curId = Mc.Mission.NewbieRemoteData.Id;
            }

            if (!PlayHelper.IsNewbie) return;
            if (!isAwake) return;

            if (Mc.Mission != null && Mc.Mission.HasMission)
            {
                if (Mc.Mission.NewbieRemoteData.Id == BUILD_DOOR_TASK_ID)
                {
                    Mc.Construction.SetTargetSelectedBuildData(new SelectedBuildData()
                    {
                        FirstTagId = 2,
                        SecondTagId = 0,
                        DataTemplateId = 2,
                        IsCombo = true,
                        ChildGroupId = 4
                    }, clearAfterUse: false);
                }
                else
                {
                    Mc.Construction.ClearTargetSelectedBuildData();
                }

                if (curMissionId == Mc.Mission.NewbieRemoteData.Id) return;
                
                var missionCfg = Mc.Tables.TbQuestPhase.GetOrDefault(Mc.Mission.NewbieRemoteData.Id);
                if (missionCfg != null)
                {
                    TaskBattleUtil.TaskForceMove = missionCfg.EventMove;
                }

                if (curMissionId == Mc.Tables.TbNewbieParams.TelescopeMissionId1)
                {
                    Mc.Msg.FireMsg(EventDefine.CloseTelescope);
                    TaskBattleUtil.SetPlayerInputable(true);
                }

                if (curMissionId == BUILD_DOOR_TASK_ID || curMissionId == 2040142)
                {
                    Mc.Construction.ClearTargetSelectedBuildData();
                }

                if (effectObj != null)
                {
                    GameObject.Destroy(effectObj);
                    effectObj = null;
                }

                curMissionId = Mc.Mission.NewbieRemoteData.Id;

                if (!string.IsNullOrEmpty(bgAudioName))
                {
                    Mc.Audio.Stop2DAudio(bgAudioName, 1.5f);
                    bgAudioName = string.Empty;
                }
                if (!string.IsNullOrEmpty(missionCfg.BgAudio))
                {
                    Mc.Audio.Add2DAudio(missionCfg.BgAudio);
                    bgAudioName = missionCfg.BgAudio;
                }

                RecordFinishInfo(curMissionId);

                if (missionCfg.GuideId > 0)
                {
                    if (missionCfg.EventDelayDic.TryGetValue(Common.Data.TaskGuideType.ViewGuide, out var time) && time > 0)
                    {
                        var timer = Mc.TimerWheel.AddTimerOnce(time, (id, data, delete) =>
                        {
                            if (Mc.Mission.NewbieRemoteData.Id == Mc.Tables.TbNewbieParams.HitEnemyMissionId)
                            {
                                if (Mc.MyPlayer.MyEntityLocal.IsNullHand)
                                {
                                    Mc.Msg.FireMsg(EventDefine.TryGuideInterface, missionCfg.GuideId, 664001);
                                }
                                else
                                {
                                    Mc.Msg.FireMsg(EventDefine.TryGuideInterface, missionCfg.GuideId, 664003);
                                }
                            }
                            else if (Mc.Mission.NewbieRemoteData.Id == 2040040)
                            {
                                var IHeldItemEntity = Mc.MyPlayer?.MyEntityLocal.GetHeldItemByEntityId(Mc.MyPlayer.MyEntityLocal.CurrentWeaponId);
                                if (IHeldItemEntity == null || IHeldItemEntity.TableId != 23060006)
                                {
                                    Mc.Msg.FireMsg(EventDefine.TryGuideInterface, missionCfg.GuideId, 6614001);
                                }
                                else
                                {
                                    Mc.Msg.FireMsg(EventDefine.TryGuideInterface, missionCfg.GuideId, 6614002);
                                }
                            }
                            else
                            {
                                if (!newbieLevelGuideIdList.Contains(missionCfg.GuideId))
                                {
                                    Mc.Msg.FireMsg(EventDefine.TryGuideInterface, missionCfg.GuideId, -1);
                                }
                            }
                            newbieLevelTimer.Remove(id);
                        });
                        if (!newbieLevelTimer.Contains(timer))
                        {
                            newbieLevelTimer.Add(timer);
                        }
                    }
                    else
                    {
                        if (Mc.Mission.NewbieRemoteData.Id == Mc.Tables.TbNewbieParams.HitEnemyMissionId)
                        {
                            if (Mc.MyPlayer.MyEntityLocal.IsNullHand)
                            {
                                Mc.Msg.FireMsg(EventDefine.TryGuideInterface, missionCfg.GuideId, 664001);
                            }
                            else
                            {
                                Mc.Msg.FireMsg(EventDefine.TryGuideInterface, missionCfg.GuideId, 664003);
                            }
                        }
                        else if (Mc.Mission.NewbieRemoteData.Id == 2040040)
                        {
                            var IHeldItemEntity = Mc.MyPlayer?.MyEntityLocal.GetHeldItemByEntityId(Mc.MyPlayer.MyEntityLocal.CurrentWeaponId);
                            if (IHeldItemEntity == null || IHeldItemEntity.TableId != 23060006)
                            {
                                Mc.Msg.FireMsg(EventDefine.TryGuideInterface, missionCfg.GuideId, 6614001);
                            }
                            else
                            {
                                Mc.Msg.FireMsg(EventDefine.TryGuideInterface, missionCfg.GuideId, 6614002);
                            }
                        }
                        else
                        {
                            if (!newbieLevelGuideIdList.Contains(missionCfg.GuideId))
                            {
                                Mc.Msg.FireMsg(EventDefine.TryGuideInterface, missionCfg.GuideId, -1);
                            }
                        }
                    }
                }

                if (Mc.Mission.NewbieRemoteData.Id == Mc.Tables.TbNewbieParams.TelescopeMissionId1)
                {
                    TaskBattleUtil.LookAtTargetPos(new Vector3(Mc.Tables.TbNewbieParams.TelescopeLookatPos2.x, Mc.Tables.TbNewbieParams.TelescopeLookatPos2.y, Mc.Tables.TbNewbieParams.TelescopeLookatPos2.z));
                    TaskBattleUtil.SetPlayerInputable(false);
                    if (missionCfg != null)
                    {
                        ShowSubtitle(missionCfg.SubtitleId);
                    }
                }


                if (curMissionId == Mc.Tables.TbNewbieParams.SafeAreaMissionId)
                {
                    TaskBattleUtil.EnableForbiddenSwitchWeapon();
                }

                if (curMissionId == BUILD_DOOR_TASK_ID)
                {
                    Mc.Construction.SetTargetSelectedBuildData(new SelectedBuildData()
                    {
                        FirstTagId = 2,
                        SecondTagId = 0,
                        DataTemplateId = 2,
                        IsCombo = true,
                        ChildGroupId = 4
                    }, clearAfterUse: false);
                }

                if (curMissionId == 2040142)
                {
                    Mc.Construction.SetTargetSelectedBuildData(new SelectedBuildData()
                    {
                        FirstTagId = 1,
                        SecondTagId = 0,
                        DataTemplateId = 20,
                        IsCombo = false,
                        ChildGroupId = 0
                    }, clearAfterUse: false);
                }

                if (curMissionId == 2060180)
                {
                    Mc.Construction.SetTargetSelectedBuildData(new SelectedBuildData()
                    {
                        FirstTagId = 1,
                        SecondTagId = 19,
                        DataTemplateId = 10,
                        IsCombo = true,
                        ChildGroupId = 1
                    }, clearAfterUse: false);
                }

                if (curMissionId == 2060185)
                {
                    Mc.Construction.SetTargetSelectedBuildData(new SelectedBuildData()
                    {
                        FirstTagId = 8,
                        SecondTagId = 4,
                        DataTemplateId = 10,
                        IsCombo = true,
                        ChildGroupId = 2
                    }, clearAfterUse: false, false);
                }


                if (curMissionId == 2060215)
                {
                    Mc.TimerWheel.AddTimerOnce(100, (_, _, _) =>
                    {
                        long curWeaponId = Mc.MyPlayer.MyEntityLocal.CurrentWeaponId;
                        if (curWeaponId > 0)
                        {
                            Utility.InteractionUtil.TryInteract(PlayerInteractiveId.Inspection);
                        }
                    });
                }

                if (missionCfg.EffectID > 0)
                {
                    var effectCfg = Mc.Tables.TbNewbieGuideEffect.GetOrDefault(missionCfg.EffectID);
                    if (effectCfg != null)
                    {
                        effectObj = GoPool.Get(effectCfg.Path);
                        if (effectObj != null)
                        {
                            var pos = effectCfg.Pos;
                            effectObj.transform.localPosition = new Vector3(pos.x, pos.y, pos.z);
                            var rot = effectCfg.Rot;
                            effectObj.transform.localRotation = Quaternion.Euler(rot.x, rot.y, rot.z);
                            effectObj.transform.localScale = Vector3.one * effectCfg.Scale;
                            effectObj.name = "NewbieLevel_Effect";
                        }
                    }
                }

                if (Mc.Mission.NewbieRemoteData.Id == Mc.Tables.TbNewbieParams.TelescopeMissionId)
                {
                    if (!CombatConfig.TelescopeUsing)
                    {
                        return;
                    }
                    else
                    {
                        TaskBattleUtil.LookAtTargetPos(new Vector3(Mc.Tables.TbNewbieParams.TelescopeLookatPos1.x, Mc.Tables.TbNewbieParams.TelescopeLookatPos1.y, Mc.Tables.TbNewbieParams.TelescopeLookatPos1.z));
                        TaskBattleUtil.SetPlayerInputable(false);
                        if (missionCfg != null)
                        {
                            ShowSubtitle(missionCfg.SubtitleId);
                        }
                    }
                }

                if (missionCfg == null) return;
                var guideCfg = Mc.Tables.TbGuide.GetOrDefault(missionCfg.GuideId);
                if (guideCfg != null)
                {
                    if (guideCfg.GuideType == 3)
                    {
                        UiSlideGuide.OpenWindow();
                    }
                    else if (guideCfg.GuideType == 4)
                    {
#if !STANDALONE_REAL_PC
                        UiShinGuide.OpenWindow();
#endif
                    }
                }

                foreach (var id in newbieLevelTimer)
                {
                    Mc.TimerWheel.CancelTimer(id);
                }

                newbieLevelTimer.Clear();

                //任务ID更新时播放字幕
                if (missionCfg.SubtitleId > 0)
                {
                    if (missionCfg.EventDelayDic.TryGetValue(Common.Data.TaskGuideType.Subtitle, out var time) && time > 0)
                    {
                        var timer = Mc.TimerWheel.AddTimerOnce(time, (id, data, delete) =>
                        {
                            ShowSubtitle(missionCfg.SubtitleId);
                            newbieLevelTimer.Remove(id);
                        });
                        if (!newbieLevelTimer.Contains(timer))
                        {
                            newbieLevelTimer.Add(timer);
                        }
                    }
                    else
                    {
                        ShowSubtitle(missionCfg.SubtitleId);
                    }
                }

                if (missionCfg.GuideLineID > 0)
                {
                    ProfilerApi.BeginSample(EProfileFunc.Client_MgrGuideNewbieLevel_GuideLine);
                    guideLineTarget.Clear();

                    if (missionCfg.EventDelayDic.TryGetValue(TaskGuideType.GuideLine, out int time) && time > 0)
                    {
                        var timer = Mc.TimerWheel.AddTimerOnce(time, (id, data, delete) =>
                        {
                            Mc.GuideLine.ShowGuideLinesById(missionCfg.GuideLineID);
                        });
                    }
                    else
                    {
                        Mc.GuideLine.ShowGuideLinesById(missionCfg.GuideLineID);
                    }

                    ProfilerApi.EndSample(EProfileFunc.Client_MgrGuideNewbieLevel_GuideLine);
                }
                else
                {
                    Mc.GuideLine.HideGuideLine();
                }

                if (!string.IsNullOrEmpty(missionCfg.CartoonVideoUrl) || !string.IsNullOrEmpty(missionCfg.UiName))
                {
                    if (missionCfg.EventDelayDic.TryGetValue(Common.Data.TaskGuideType.CartoonVideo, out var time) && time > 0)
                    {
                        var timer = Mc.TimerWheel.AddTimerOnce(time, (id, data, delete) =>
                        {
                            Mc.MyPlayer.MyEntityLocal.DrawComponent.Equip(HoldItemIndex.ItemGugujiModel); //切出咕咕鸡
                            newbieLevelTimer.Remove(id);
                        });
                    }
                    else
                    {
                        Mc.MyPlayer.MyEntityLocal.DrawComponent.Equip(HoldItemIndex.ItemGugujiModel); //切出咕咕鸡                    
                    }
                }

                if (missionCfg.TimelineId > 0)
                {
                    if (missionCfg.EventDelayDic.TryGetValue(Common.Data.TaskGuideType.Timeline, out var time) && time > 0)
                    {
                        var timer = Mc.TimerWheel.AddTimerOnce(time, (id, data, delete) =>
                        {
                            Mc.Timeline?.TryPlayTimeline(missionCfg.TimelineId);
                            newbieLevelTimer.Remove(id);
                        });
                    }
                    else
                    {
                        Mc.Timeline?.TryPlayTimeline(missionCfg.TimelineId);
                    }
                }

                HandleFakeNpcHUD();
            }
            else
            {
                TaskBattleUtil.TaskForceMove = false;
                TaskBattleUtil.DisableForbiddenSwitchWeapon();
                Mc.GuideLine.HideGuideLine();
            }
        }

        private void OnTelescopeStateChange(bool isOpen)
        {
            if (!PlayHelper.IsNewbie) return;
            if (!isAwake) return;

            if (isOpen && Mc.Mission.HasMission)
            {
                if (Mc.Mission.NewbieRemoteData.Id == Mc.Tables.TbNewbieParams.TelescopeMissionId)
                {
                    TaskBattleUtil.LookAtTargetPos(new Vector3(Mc.Tables.TbNewbieParams.TelescopeLookatPos1.x, Mc.Tables.TbNewbieParams.TelescopeLookatPos1.y, Mc.Tables.TbNewbieParams.TelescopeLookatPos1.z));
                    TaskBattleUtil.SetPlayerInputable(false);
                    var missionCfg = Mc.Tables.TbQuestPhase.GetOrDefault(Mc.Mission.NewbieRemoteData.Id);
                    if (missionCfg != null)
                    {
                        ShowSubtitle(missionCfg.SubtitleId);
                    }
                }
            }
        }

        private void OnThrowStateChange(long playerId, bool throwState)
        {
            if (Mc.MyPlayer != null && Mc.MyPlayer.MyEntityId != playerId) return;

            if (Mc.Mission.HasMission)
            {
                if (Mc.Mission.NewbieRemoteData.Id != Mc.Tables.TbNewbieParams.HitEnemyMissionId) return;
                if (!throwState)
                {
                    //两秒后切任务，要看到石头扔出去的轨迹
                    Mc.TimerWheel.AddTimerOnce(500, (id, data, delete) =>
                    {
                        Mc.Msg.FireMsg(EventDefine.TutorialFinishViewGuide, 6604);
                    });
                }
            }
        }

        private void RecordFinishInfo(long taskId)
        {
            var questPhase = Mc.Tables.TbQuestPhase.GetOrDefault(taskId);
            if (questPhase == null)
                return;
            int taskPhaseEndCondition = questPhase.TaskPhaseEndCondition;
            var questCondition = Mc.Tables.TbQuestCondition.GetOrDefault(taskPhaseEndCondition);
            if (questCondition == null)
                return;
            if (questCondition.TargetData != WizardGames.Soc.Common.Data.task.TargetData.ClientTrigger)
                return;
            long[] endConditionParameters = questPhase.EndConditionParameter;
            if (endConditionParameters.Length < 2)
                return;
            int triggerId = (int)endConditionParameters[1];

            var questClientTrigger = Mc.Tables.TbQuestClientTrigger.GetOrDefault(triggerId);
            if (questClientTrigger == null)
                return;

            if (questClientTrigger.TaskTriggerType != ETaskTriggerType.FinishCartoonVideo)
            {
                cartoonVideoFinished = false;
            }

            if (questClientTrigger.TaskTriggerType != ETaskTriggerType.FinishSubtitle)
            {
                subtitleFinished = false;
            }
        }

        public void OnPlayerAwake()
        {
            isAwake = true;
            OnNewbieLevelMissionChanged();
            OnNewbieMissionChanged();
            OnMainQuestMissionChanged();
            TryGuideMicrophone();
        }

        public bool BelongManualViewGuideTrigger(long id)
        {
            return manualViewGuideTrigger.Contains(id);
        }


        private void OnReconnectFailed()
        {
            TaskBattleUtil.SetPlayerInputable(true);
        }

        private void OnTimelineUseTelescope(bool use)
        {
            if (use)
            {
                if (PlayHelper.IsNewbie)
                {
                    Mc.FullScreenEffect.ShowFullScreenEffect("TelescopeNewBie");
                }
                else
                {
                    Mc.FullScreenEffect.ShowFullScreenEffect("Telescope");
                }
                Mc.FullScreenEffect.ShowTeleScopeMaskEffect();
            }
            else
            {
                if (PlayHelper.IsNewbie)
                {
                    Mc.FullScreenEffect.HideFullScreenEffect("TelescopeNewBie");
                }
                else
                {
                    Mc.FullScreenEffect.HideFullScreenEffect("Telescope");
                }
            }
        }

        private Dictionary<long, GameObject> fakeNpcPosGoDic = new Dictionary<long, GameObject>();
        private Dictionary<long, long> hudId2fakeNpcId = new Dictionary<long, long>();
        private List<long> rmIndex = new List<long>();
        private void HandleFakeNpcHUD()
        {
            if (Mc.NewbieLevel == null) return;
            var cfg = Mc.Tables.TbQuestPhase.GetOrDefault(Mc.Mission.NewbieRemoteData.Id);
            if (cfg == null) return;
            var curTaskNpcId = cfg.ModelId;
            if (hudId2fakeNpcId.Count > 0)
            {
                foreach (var data in hudId2fakeNpcId)
                {
                    if (data.Key != curTaskNpcId)
                        rmIndex.Add(data.Key);

                }
            }

            if (rmIndex.Count > 0)
            {
                for (int i = 0; i < rmIndex.Count; i++)
                {
                    var id = rmIndex[i];
                    RemoveNpcHud(id);
                }
                rmIndex.Clear();
            }

            if (curTaskNpcId == 0)
                return;

            if (hudId2fakeNpcId.ContainsKey(curTaskNpcId))
            {
                Mc.Msg.FireMsg(EventDefine.SetTeachingNpcMarkerDesc, cfg.NpcHUDGuide);
            }
            else
            {
                GameObject npcHudPosGo;
                if (fakeNpcPosGoDic.ContainsKey(curTaskNpcId))
                {
                    npcHudPosGo = fakeNpcPosGoDic[curTaskNpcId];
                }
                else
                {
                    npcHudPosGo = Mc.NewbieLevel.GetNpcHudPos(curTaskNpcId);
                }
                if (npcHudPosGo != null)
                {
                    var hudId = Mc.Marker.CreateTeachingNpcHeadMarker(npcHudPosGo, cfg.NpcHUDGuide);
                    hudId2fakeNpcId.Add(hudId, curTaskNpcId);
                }
            }
        }

        private void RemoveNpcHud(long hudId)
        {
            if (hudId2fakeNpcId.Count == 0)
                return;
            if (hudId2fakeNpcId.ContainsKey(hudId))
            {
                Mc.Marker.RemoveTeachingNpcHeadMarker(hudId);
                long npcId = hudId2fakeNpcId[hudId];
                hudId2fakeNpcId.Remove(hudId);
                if (fakeNpcPosGoDic.ContainsKey(npcId))
                {
                    fakeNpcPosGoDic.Remove(npcId);
                }
            }
        }
    }
}
