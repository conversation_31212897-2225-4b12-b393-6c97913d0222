using System;
using System.Threading;
using WizardGames.Soc.Common.Cache;
using WizardGames.Soc.Common.Framework.Network.Impl;
using WizardGames.Soc.Common.TimerWheel;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Network;

namespace WizardGames.Soc.SocClient.Framework
{
    public abstract class PostToKcpTask : AsyncTaskBase
    { }

    public class KcpThread : ThreadBase<PostToKcpTask>
    {
        private KcpClient kcpClient;
        private string ipAddress;
        private int port;

        public KcpThread(string ipAddress, int port, KcpClient client)
        {
            this.ipAddress = ipAddress;
            this.port = port;
            kcpClient = client;
        }

        public override bool Boot()
        {
            // 覆盖基类
            thread = new Thread(Loop)
            {
                Name = "KcpThread",
                IsBackground = true,
            };
            thread.Start();
            return true;
        }

        protected override void Loop()
        {
            logger.Info("Start");
            kcpClient.Connect(ipAddress, (ushort)port);

            if (Interlocked.CompareExchange(ref State, ThreadState.RUNNING, ThreadState.NOT_READY) == ThreadState.NOT_READY)
            {
                logger.Info("BeginUpdate");
                while (State == ThreadState.RUNNING && kcpClient.Active)
                {
                    Queue.ProcessAsyncTask();
                    kcpClient.Tick();
                    // 这里按照kk意思 sleep 5ms
                    Thread.Sleep(5);
                }
                logger.Info("LeaveUpdate");
            }

            //Queue.ProcessAsyncTask(true);
        }

        public void Cleanup()
        {
            logger.Info("kcp logic thread exit");
        }
    }

    public class OnDataReceivedTask : PostToKcpTask, IPooledObject
    {
        private static PoolWithLock<OnDataReceivedTask> pool = new(256);
        private ArraySegment<byte> data;
        private UdpBuffer socketInfo;
        private KcpClient host;
        public bool IsReturn { get; set; }

        public static OnDataReceivedTask GetFromPool(ref ArraySegment<byte> data, UdpBuffer info, KcpClient host)
        {
            var task = pool.Get();
            task.data = data;
            task.socketInfo = info;
            task.host = host;
            return task;
        }

        public override void ExcuteFunc()
        {
            host.RawInput(ref data);
            host.TryPeekPackets();
        }

        public void ReturnToPool()
        {
            host = null;
            socketInfo.ReturnToPool();
            socketInfo = null;
            data = null;
            pool.Recycle(this);
        }
    }

    public class SimpleTask : PostToKcpTask
    {
        private static readonly SocLogger logger = LogHelper.GetLogger(typeof(SimpleTask));
        private Action action;
        public SimpleTask(Action action)
        {
            this.action = action;
        }
        public override void ExcuteFunc()
        {
            try
            {
                action.Invoke();
            }
            catch (Exception e)
            {
                logger.ErrorFormat("Execute SimpleTask with exception: {0}", e);
            }
        }
    }
}
