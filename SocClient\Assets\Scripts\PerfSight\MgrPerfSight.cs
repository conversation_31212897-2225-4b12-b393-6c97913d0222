#if ENABLE_PERF_SIGHT
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.Networking;
using Utilities;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Common.Indicator;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.Syncronization;
using WizardGames.Soc.Common.Unity;
using WizardGames.Soc.Common.Unity.Manager;
using WizardGames.Soc.Common.Unity.SocAssetBundle.SocBundleRuntime;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Manager; 
using Newtonsoft.Json.Linq;
using WizardGames.Soc.Common.Profile;
using WizardGames.Soc.Common.Unity.Loader;
using WizardGames.Soc.Common.Unity.SocAssetBundle;
using ApplicationConfig = WizardGames.Soc.Common.Unity.ApplicationConfig;
using UnityEngine.Rendering.Universal;
using WizardGames.Soc.SettingsFramework.Framework;
using WizardGames.Soc.Common.Unity.Scene;


namespace WizardGames.Soc.SocClient
{
    public class MgrPerfSight : MgrBase
    {
        public const string PERF_SIGHT_DOMAIN_NAME = "default";
        public const string PERF_SIGHT_GPU_NAME = "gpu";
        public const string PERF_SIGHT_LEVEL0_NAME = "level0";
        public const string PERF_SIGHT_GPUCHECK_MIN_NAME = "gpucheckmin";
        public const string PERF_SIGHT_GPUCHECK_MAX_NAME = "gpucheckmax";
        public const string PERF_SIGHT_SEGMENTATION_NAME = "segmentation";

        public struct AppEvents
        {
            public const string Catgory = "app_event";
            public const string Quit = "app_quit";
            public const string Crash = "app_crash";
            public const string Focus = "app_focus";
            public const string Pause = "app_pause";
            public const string Exit = "world_exit";
        }

        public struct PerfSightTag
        {
            public string fpsQualityTag;
            public string stepTag;
        }
        public struct PerfTimeInfo
        {
            public DateTime? startTime;
            public DateTime? endTime;
            public bool isStartTimeValid;
            public bool isEndTimeValid;
        }
        public enum ETagType
        {
            FpsQualityTag, stepTag
        }

        public enum EDeviceConfigStatus
        {
            None = 0, Local = 1, Remote = 2, RemoteCache = 3
        }

        public enum ETApmGameObjectStatus
        {
            Normal = 0, Destroied = 1, Disable = 2, FrameComponentDestroied = 3, FrameComponentDisable = 4
        }

        private class DeviceLevelInfo 
        {
            public float defaultLevel = 0;
            public float gpuLevel = 0;
            public float level0 = 0;
            public float gpuCheckMin = 0;
            public float gpuCheckMax = 0;
            public float segmentation = 0;
        }

        private static SocLogger logger = LogHelper.GetLogger(typeof(MgrPerfSight));
        public bool NeedAutoFin = true;
        private bool _isInit = false;
        private bool _isLevelBegan = false;
        private bool _isFocus = true;
        private int _sendIntervalSeconds = int.MaxValue;
        private int _sendWaitingSeconds = 0;
        private int _currentLevelId = 0;
        private int _levelId = 0;
        private string _levelName = string.Empty;
        private string _sceneName = string.Empty;
        private int _rtt = int.MaxValue;
        private int _fps = 0;
        private bool _isTagBegan = false;
        private bool _hasPostedServerInfo = false;
        private PerfSightTag _tag = new PerfSightTag();
        private EDeviceConfigStatus _deviceConfigStatus = EDeviceConfigStatus.None;
        private int _deviceConfigVersion = 0;
        private int _hasChangedQualityFps = 0;
        private ETApmGameObjectStatus _TApmGameObjectStatus = ETApmGameObjectStatus.Normal;
        private bool _isFirstCheck = true;
        private GameObject _TApmGameObject = null;
        private Component _ApmFrameProcessor = null;
        public RMQualityLevel QualityLevel = RMQualityLevel.None;
        private PerfTimeInfo _timeInfo = new PerfTimeInfo() { startTime = DateTime.UtcNow.AddHours(8), endTime = DateTime.UtcNow.AddHours(8),isEndTimeValid=false,isStartTimeValid=false };
        private string AppId;
        private DeviceLevelInfo _deviceLevelInfo = new DeviceLevelInfo();
        private long _beginTime = 0;

        /// <summary>
        /// 初始化PerfSight
        /// </summary>
        public override void Init()
        {
            if (_isInit)
                return;

            _isInit = true;
            Application.focusChanged += OnApplicationFocusChanged;

            Action<string> enterWorld = new Action<string>(BeginLevel);
            McCommonUnity.Scene.BeforeEnterGameSceneFunc -= enterWorld;
            McCommonUnity.Scene.BeforeEnterGameSceneFunc += enterWorld;

            //Action exitWorld = new Action(BeforeExitWorld);
            //McCommonUnity.Scene.BeforeExitGameSceneFunc -= exitWorld;
            //McCommonUnity.Scene.BeforeExitGameSceneFunc += exitWorld;

            if (Application.isEditor)
                return;

            TextAsset settingsText = Resources.Load<TextAsset>("PerfSightPlayerSettings");
            if (settingsText == null)
            {
                logger.Info("PerfSightAgent PerfSightSettings is null");
                return;
            }


            PerfSightPlayerSettings settings = JsonUtility.FromJson<PerfSightPlayerSettings>(settingsText.text);
            AppId = settings.AppId;
            Resources.UnloadAsset(settingsText);
            
            // 每隔一段时间就发送，防止数据过大
            _sendIntervalSeconds = settings.SendIntervalMinutes >= 1 ? settings.SendIntervalMinutes * 60 : int.MaxValue;

#if UNITY_STANDALONE || UNITY_EDITOR
            //PC配置方法：通过调用SetPCServerURL函数设置，该函数需要在InitContext前调用
            //PerfSightAgent.SetPCServerURL(settings.ReportUrl);
            PerfSightAgent.SetPCAppVersion(VersionCodeUtils.GetClientVersion());
#endif
            //在 Android 或 iOS 平台上，PerfSight SDK 会自动收集应用程序的版本号，可以通过SetVersionIden接口进行游戏资源版本号的自主设定

            var codeVersion = VersionCodeUtils.GetCodeVersion();
            if (codeVersion != null)
                PerfSightAgent.SetVersionIden(VersionCodeUtils.GetCodeVersion());

            //#if !PUBLISH || DEVELOPMENT_BUILD
            //            PerfSightAgent.EnableDebugMode(); // 可选，调试情况下打开日志
            //#endif
            //            PerfSightAgent.InitContext(settings.AppId); // 必选，AppId请联系PerfSight获取

            logger.InfoFormat("PerfSightAgent Init Success AppId:{0} ReportUrl:{1}", settings.AppId, settings.ReportUrl);
        }

        public override void OnAccountLogined()
        {
            base.OnAccountLogined();

            var accountName = Mc.Config.accountName;
            accountName = accountName.Trim();

            // PerfSight用户标识
            PerfSightAgent.SetUserId(accountName);

            logger.InfoFormat("PerfSightAgent.SetUserId({0});", accountName);
        }

        /*public override Task AfterEnterWorld()
        {
            OnLoadLevelCompleted();
            return base.AfterEnterWorld();
        }

        public override Task OnExitWorld()
        {
            EndLevel();
            return base.OnExitWorld();
        }*/

        public void OnLoadLevelCompleted()
        {
            // PerfSight 场景加载结束
            if (_isLevelBegan)
            {
                PerfSightAgent.MarkLoadlevelCompleted();
                if (_beginTime != 0) 
                {
                    long now = System.DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                    float loadTime = (float)(now - _beginTime)/1000;
                    PerfSightAgent.PostValueF("level_info", "level_loadtime", loadTime);
                    logger.Info($"level_loadtimeMs:{loadTime}s");
                    _beginTime = 0;
                }
            }
            logger.Info("PerfSightAgent.MarkLoadLevelCompleted");
        }

        public void OnWorldExit()
        {
            // 发送程序崩溃事件
            PerfSightAgent.PostValueI(AppEvents.Catgory, AppEvents.Exit, 1);

            EndLevel();
        }

        private void OnApplicationFocusChanged(bool focus)
        {
            _isFocus = focus;
            if (focus)
            {
                MgrProfiler.EndPerfExclude("LossFocus");
                PerfSightAgent.PostValueI(AppEvents.Catgory, AppEvents.Focus, 1);
            }
            else
            {
                MgrProfiler.BeginPerfExclude("LossFocus");
                PerfSightAgent.PostValueI(AppEvents.Catgory, AppEvents.Pause, 1);
            }
        }

        public bool OnApplicationQuit()
        {
            if (!_isFocus)
            {
                // 结束剔除
                MgrProfiler.EndPerfExclude("LossFocus");
            }

            // 发送程序退出事件
            PerfSightAgent.PostValueI(AppEvents.Catgory, AppEvents.Quit, 1);

            EndLevel();
            return true;
        }

        public void OnApplicationCrash()
        {
            // 发送程序崩溃事件
            PerfSightAgent.PostValueI(AppEvents.Catgory, AppEvents.Crash, 1);

            EndLevel();
        }

        public void BeginLevel(string name)
        {
            if (_isLevelBegan)
            {
                _timeInfo.isEndTimeValid = false;
                _timeInfo.isStartTimeValid = false;
                EndLevel();
            }

            // PerfSight 场景开始标记
            _isLevelBegan = true;
            _sendWaitingSeconds = 0;
            var nowTime = DateTime.Now;
            
            _levelId = nowTime.Month * 100000000 + nowTime.Day * 1000000 + nowTime.Hour * 10000 + nowTime.Minute * 100 + nowTime.Second;
            _currentLevelId = _levelId;
            _levelName = name;

            if (McCommonUnity.Scene.IsChangingScenen)
                _sceneName = name;
            else
                _sceneName = McCommonUnity.Scene.GetSceneConfig(McCommonUnity.Scene.CurScene).SceneName;

            _timeInfo.startTime = DateTime.UtcNow.AddHours(8);
            _timeInfo.isStartTimeValid = true;
            PerfSightAgent.MarkLoadlevel(name);
            PostLevelInfo();
            PostDeviceInfo();
            PostQualitySettings();
            SetQuality(_fps, QualityLevel);
            _beginTime = System.DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

            _hasChangedQualityFps = 0;
            _TApmGameObjectStatus = ETApmGameObjectStatus.Normal;
            logger.InfoFormat("PerfSightAgent.MarkLoadLevel({0}) levelId:{1}", name, _levelId);
        }
        /// <summary>
        /// 专门指的是DPI和分辨率
        /// </summary>
        public void PostQualitySettings()
        {
            if (!_isLevelBegan)
                return;
            Vector2 resolution = RMQualityManager.GetRenderResolution();

            int detailMatrial = 1;
            var keyword = "_TMP_DEBUG_DISABLE_DETAIL_GLOBAL";
            if (UnityEngine.Shader.IsKeywordEnabled(keyword))
                detailMatrial = 0;
            PerfSightAgent.PostValueF("quality_info", "upscaling_resolution", Screen.height * UpScalingManager.instance.TargetRatio);
            PerfSightAgent.PostValueF("quality_info", "upscaling_mode", (float)UpScalingManager.instance.GetUpScalingMode());
            PerfSightAgent.PostValueF("quality_info", "resolution", resolution.y);
            PerfSightAgent.PostValueF("quality_info", "screen_DPI", Screen.dpi);
            PerfSightAgent.PostValueF("quality_info", "scaled_DPI", Mathf.Min(Screen.dpi, 400 * QualitySettings.resolutionScalingFixedDPIFactor));
            PerfSightAgent.PostValueF("quality_info", "super_resolution_scal", SettingManager.superResolutionScale);
            PerfSightAgent.PostValueI("quality_info", "detail_matrial", detailMatrial);
            PerfSightAgent.PostValueF("quality_info", "max_LOD_Level", QualitySettings.maximumLODLevel);
        }
        public void EndLevel()
        {
            if (!_isLevelBegan)
            {
                return;
            }

            logger.InfoFormat("PerfSightAgent.MarkLevelFin, levelName:{0} scene_name:{1} levelId:{2} waitingSeconds:{3}", _levelName, _sceneName, _levelId, _sendWaitingSeconds);
            _isLevelBegan = false;
            _isTagBegan = false;
            _sendWaitingSeconds = 0;
            _levelId = 0;
            _currentLevelId = 0;
            // PerfSight 场景结束标记
            _timeInfo.endTime=DateTime.UtcNow.AddHours(8);
            _timeInfo.isEndTimeValid = true;
            PerfSightAgent.MarkLevelFin();
            _hasPostedServerInfo = false;
            _beginTime = 0;
        }

        /// <summary>
        /// 设置 PerfSight 自定义画质信息
        /// </summary>
        /// <param name="fps">帧率</param>
        /// <param name="qualityLevel">画质</param>
        public void SetQuality(int fps, RMQualityLevel qualityLevel, bool needPostInfo = true)
        {
            /** PerfSight 自定义画质信息，若使用此接口请提前联系PerfSight提供对应规则
             * 此接口用于将目标帧率，机型档位等其它维度融合为一个值，通过SetQuality进行上传，后台可以根据各个维度的取值
             * 以及整体的取值进行计算
             * 比如帧率有未知、25、30、 40、50、60、90七档，使用个位代表帧率（0，1，2，3，4，5，6）
             * 画质分档为未知、流畅、均衡、精致、高清、极致，使用十位代表画质分档（0，1，2，3，4，5）
             * 机型分档为0-9，使用百位代表机型分档
             * 上报时调用SetQuality(312)即代表目标帧率：30，画质分档：流畅，机型分档：3
             */
            int fpsLevel;
            // 功能暂时关闭
            //// 局内切换帧率时，做数据切分
            //if (_fps != fps && _isLevelBegan)
            //    DataCut();

            if (needPostInfo && _isLevelBegan && (_hasChangedQualityFps == 0) && (_fps != fps || QualityLevel != qualityLevel))
            {
                _hasChangedQualityFps = 1;
                PostQualityInfo();
            }

            if (qualityLevel != RMQualityLevel.None)
            {
                var qualityOptionsKeys = Mc.SettingClient.GetConfigOptionsKeys("gameQualitySwitch");
                var languageId = qualityOptionsKeys[(int)qualityLevel];
                var qualityTag = LanguageManager.GetTextConst(languageId);
                ChangeTag($"画质:{qualityTag},帧率:{fps}", ETagType.FpsQualityTag);
            }
            else 
            {
                ChangeTag($"画质:未知,帧率:{fps}", ETagType.FpsQualityTag);
            }
            _fps = fps;
            QualityLevel = qualityLevel;
            switch (fps)
            {
                case 25: fpsLevel = 1; break;
                case 30: fpsLevel = 2; break;
                case 40: fpsLevel = 3; break;
                case 50: fpsLevel = 4; break;
                case 60: fpsLevel = 5; break;
                case 90: fpsLevel = 6; break;
                default: fpsLevel = 0; break; // 未知帧率
            }
            int deviceLevel = Mc.DeviceLevel.DeviceLevel/100;
            int quality = fpsLevel + ((int)qualityLevel + 1) * 10 + deviceLevel*100;
            if (!_isLevelBegan) return;
            PerfSightAgent.SetQuality(quality);
            logger.InfoFormat("PerfSightAgent.SetQuality({0})", quality);

        }

        public void ChangeTag(string tag, ETagType tagType)
        {
            if (!_isLevelBegan) return;
            switch (tagType)
            {
                case ETagType.FpsQualityTag:
                    {
                        if (!string.IsNullOrEmpty(this._tag.fpsQualityTag) && this._tag.fpsQualityTag.Equals(tag)) return;
                        this._tag.fpsQualityTag = tag;
                    }
                    break;
                case ETagType.stepTag:
                    {
                        if (!string.IsNullOrEmpty(this._tag.stepTag) && this._tag.stepTag.Equals(tag)) return;
                        this._tag.stepTag = tag;
                    }
                    break;
            }

            if (_isTagBegan)
                PerfSightAgent.EndTag();
            var tagStr = this._tag.fpsQualityTag + "," + this._tag.stepTag;
            PerfSightAgent.BeginTag(tagStr);
            this._isTagBegan = true;
        }

        public void DataCut()
        {
            _sendWaitingSeconds = 0;
            _levelId = _currentLevelId;
            PerfSightAgent.MarkLevelFin();
            PerfSightAgent.MarkLoadlevel(_levelName);
            PostLevelInfo();
        }

        public void CheckTApmGameObject()
        {
            if (!_isLevelBegan) return;

            ETApmGameObjectStatus TApmGameObjectStatus = ETApmGameObjectStatus.Normal;

            if (_TApmGameObject == null) 
            {
                _TApmGameObject = GameObject.Find("/TApmGameObject");
                if (_TApmGameObject != null)
                    _ApmFrameProcessor = _TApmGameObject.GetComponent("ApmFrameProcessor");
            }
            var ApmFrameProcessorEnable = true;
            if (_ApmFrameProcessor is Behaviour behaviourComponent)
                ApmFrameProcessorEnable = behaviourComponent.enabled;

            if (_TApmGameObject == null) 
                TApmGameObjectStatus = ETApmGameObjectStatus.Destroied;
            else if (_ApmFrameProcessor == null)
                TApmGameObjectStatus = ETApmGameObjectStatus.FrameComponentDestroied;
            else if (!_TApmGameObject.activeSelf)
                TApmGameObjectStatus = ETApmGameObjectStatus.Disable;
            else if (ApmFrameProcessorEnable)
                TApmGameObjectStatus = ETApmGameObjectStatus.FrameComponentDisable;
            else
                TApmGameObjectStatus = ETApmGameObjectStatus.Normal;

            if (TApmGameObjectStatus != _TApmGameObjectStatus)
            {
                _TApmGameObjectStatus = TApmGameObjectStatus;
                string catgory = "client_gameobject_status";
                PerfSightAgent.PostValueI(catgory, "TApmGameObject_Status", (int)_TApmGameObjectStatus);
            }
        }

        public void Update()
        {
            if (!_isLevelBegan)
                return;

            // 上报网络延迟时间（RTT ）
            var rtt = Mc.MyPlayer.MyEntityServer?.NetworkRtt ?? 0;
            if (rtt != 0)
                PerfSightAgent.UpdateNetLatency(rtt, "Full-link");
            var ping = Mc.LocalService.LastPingNum;
            if (ping != int.MaxValue)
                PerfSightAgent.UpdateNetLatency(ping, "Ping");

            // 功能暂时关闭
            //// 每隔一段时间就强制发送数据
            //++_sendWaitingSeconds;
            //if (_sendIntervalSeconds <= _sendWaitingSeconds && !string.IsNullOrEmpty(_levelName) && NeedAutoFin)
            //{
            //    DataCut();
            //}

            // 每秒监控TApmGameObject是否被销毁
            CheckTApmGameObject();
        }

        public void PostQualityInfo() 
        {
            if (!_isLevelBegan) return;

            string catgory = "quality_info";
            PerfSightAgent.PostValueI(catgory, "quality_change_flag", _hasChangedQualityFps);
        }

        void PostLevelInfo()
        {
            // 上报场景的自定义数据，只上传一次
            if (_levelId == 0)
                return;

            string catgory = "level_info";
            PerfSightAgent.BeginTupleWrap(catgory);
            PerfSightAgent.PostValueI(catgory, "level_id", _levelId);
            PerfSightAgent.PostValueI(catgory, "send_interval_seconds", _sendIntervalSeconds);
            PerfSightAgent.PostValueI(catgory, "device_config_status", (int)_deviceConfigStatus);
            PerfSightAgent.PostValueI(catgory, "device_config_version", (int)_deviceConfigVersion);
            if(!String.IsNullOrEmpty(_sceneName))
                PerfSightAgent.PostValueS(catgory, "scene_name", _sceneName);
            PerfSightAgent.EndTupleWrap();
            _levelId = 0;

        }

        /// <summary>
        /// 上报一列自定义数据
        /// </summary>
        /// <param name="dataList"></param>
        public void PostValue(List<IndicatorData> dataList)
        {
            if (!_isLevelBegan)
            {
                return;
            }

            // 上报自定义数据
            for (int i = 0; i < dataList.Count; ++i)
            {
                PostValue(dataList[i]);
            }
        }

        private float GetAspectRatio() 
        {
            float aspectRatio = 0f;
#if UNITY_ANDROID && !UNITY_EDITOR
            if (Screen.height>0)
                aspectRatio = 1.0f * Screen.width / Screen.height;
            logger.Info($"Screen Width:{Screen.width},Height:{Screen.height}");
#endif
            return aspectRatio;

        }

        private String GetScreenLayout()
        {
            String screenSize = "unknown";
#if UNITY_ANDROID && !UNITY_EDITOR
            try 
            {
                if (Application.platform != RuntimePlatform.Android)
                {
                    logger.Error("This is not an Android platform.");
                    return screenSize;
                }

                // 获取 UnityPlayer 的 Activity
                AndroidJavaClass unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
                AndroidJavaObject currentActivity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");

                // 调用 DeviceUtils 的 GetScreenLayout 方法
                AndroidJavaClass deviceUtilsClass = new AndroidJavaClass("com.wizardgames.deviceutils.DeviceUtils");
                screenSize = deviceUtilsClass.CallStatic<String>("GetScreenLayout", currentActivity);
            }
            catch(Exception e)
            {
                logger.Error($"Failed to access Android API,DeviceUtils.GetScreenLayout(): {e}");
            }
#endif
            return screenSize;
        }

        private float GetDeviceSize()
        {
            float screenInches = 0;

#if UNITY_ANDROID && !UNITY_EDITOR
            try
            {
                if (Application.platform != RuntimePlatform.Android)
                {
                    logger.Error("This is not an Android platform.");
                }

                // 获取 UnityPlayer 的 Activity
                AndroidJavaClass unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
                AndroidJavaObject currentActivity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");

                // 调用 DeviceUtils 的 GetScreenLayout 方法
                AndroidJavaClass deviceUtilsClass = new AndroidJavaClass("com.wizardgames.deviceutils.DeviceUtils");
                screenInches = (float)deviceUtilsClass.CallStatic<double>("GetPhysicalScreenSize", currentActivity);
            }
            catch (Exception e)
            {
                logger.Error($"Failed to access Android API,DeviceUtils.GetScreenInches(): {e}");
            }
#endif

            return screenInches;
        }

        public void PostDeviceInfo() 
        {
            if(!_isLevelBegan)
                return;
            var aspectRatio = GetAspectRatio();
            var screenLayout = GetScreenLayout();
            var deviceSize = GetDeviceSize();

            var catgory = "device_info";
            PerfSightAgent.BeginTupleWrap(catgory);

            PerfSightAgent.PostValueF(catgory, "aspect_ratio", aspectRatio);
            PerfSightAgent.PostValueS(catgory, "screen_layout", screenLayout);
            PerfSightAgent.PostValueF(catgory, "device_size", deviceSize);

            PerfSightAgent.PostValueF(catgory, "defalut_level", _deviceLevelInfo.defaultLevel);
            PerfSightAgent.PostValueF(catgory, "gpu_level", _deviceLevelInfo.gpuLevel);
            PerfSightAgent.PostValueF(catgory, "level_0", _deviceLevelInfo.level0);
            PerfSightAgent.PostValueF(catgory, "gpu_check_min", _deviceLevelInfo.gpuCheckMin);
            PerfSightAgent.PostValueF(catgory, "gpu_check_max", _deviceLevelInfo.gpuCheckMax);
            PerfSightAgent.PostValueF(catgory, "segmentation", _deviceLevelInfo.segmentation);

            PerfSightAgent.EndTupleWrap();
            logger.Info($"device_size_info, aspectRatio:{aspectRatio}, screenLayout:{screenLayout}, deviceSize:{deviceSize}");
            logger.Info($"device_level_info ,defaultLevel:{_deviceLevelInfo.defaultLevel}, gpuLevel:{_deviceLevelInfo.gpuLevel}, level0:{_deviceLevelInfo.level0}, " +
                $"gpuCheckMin:{_deviceLevelInfo.gpuCheckMin}, gpuCheckMax:{_deviceLevelInfo.gpuCheckMax}, segmentation:{_deviceLevelInfo.segmentation}");
        }

        /// <summary>
        /// 上报server_info,一局只上报一次
        /// </summary>
        public void PostServerInfo()
        {
            if (_hasPostedServerInfo || !_isLevelBegan)
                return;
            if (GlobalInfoSyncEntity.Instance == null || ProcessEntity.Instance == null)
                return;
            float server_uptime = (float)(Mc.Time.ServerWorldTime - GlobalInfoSyncEntity.Instance.LogicStartTime) /(3600 * 1000);
            var roleId = Mc.Config.roleId;
            var openId = Mc.Config.openId;
            var serverId = Mc.Config.serverId;

            var catgory = "server_info";
            PerfSightAgent.BeginTupleWrap(catgory);
            PerfSightAgent.PostValueF(catgory, "server_uptime", server_uptime);
            PerfSightAgent.PostValueS(catgory, "roleId", roleId);
            PerfSightAgent.PostValueS(catgory, "openId", openId);
            PerfSightAgent.PostValueS(catgory, "serverId", serverId);
            PerfSightAgent.EndTupleWrap();
            _hasPostedServerInfo = true;
            logger.InfoFormat("server_uptime:{0}, roleId:{1}, openId:{2}, serverId:{3} ", server_uptime, roleId, openId, serverId);
        }

        /// <summary>
        /// 上报自定义数据
        /// </summary>
        /// <param name="data"></param>
        public void PostValue(IndicatorData data)
        {
            if (data == null || data.Values.Count == 0)
            {
                return;
            }

            string catgory = data.Measurement;
            PerfSightAgent.BeginTupleWrap(catgory);
            foreach (var tag in data.Tags)
            {
                PerfSightAgent.PostValueS(catgory, tag.Key, tag.Value);
            }

            foreach (var val in data.Values)
            {
                val.Value.PostValue(catgory, val.Key);
            }
            PerfSightAgent.EndTupleWrap();
            //Log.InfoFormat("PerfSightAgent PostIndicatorData:{0}", catgory);
        }

        public static int CpuTemperature => PerfSightAgent.GetCpuTemperature();

        public static int IOSThermalState => PerfSightAgent.GetIosThermalState();

        /// <summary>
        /// 获取机型分档信息
        /// 1-9档，0为未命中或获取错误
        /// </summary>
        /// <returns></returns>
        public int GetDeviceLevel()
        {
            _deviceConfigStatus = EDeviceConfigStatus.Local; 
            string configName = "perfsight_level";
            bool isIos = false;
            if (Application.platform == RuntimePlatform.IPhonePlayer)   //Android和IOS的配置文件格式不同
            {
                configName += "_ios";
                isIos = true;
            }
            int deviceLevel = -1;
            try
            {
                string configPath = Path.Combine(Application.persistentDataPath, "config", configName + ".json"); //下载的配置
                string manualConfigPath = Path.Combine(Application.persistentDataPath, "config", "QCC_level.json"); //手动放的配置

                string configUrl = ApplicationConfig.Instance.PerfSightLevelUrl() + (configName + ".json");
                string configMd5Url = ApplicationConfig.Instance.PerfSightLevelUrl() + (configName + ".md5");
#if !PUBLISH
                //内网测试走文件服cdn
                configUrl = ApplicationConfig.Instance.PerfSightLevelTestUrl() + (configName + ".json");
                configMd5Url = ApplicationConfig.Instance.PerfSightLevelTestUrl() + (configName + ".md5");
#endif
                string configMd5Path = configPath.Replace(".json", ".md5");

                bool ret = UpdateServerDeviceConfig(configPath, configMd5Url, configUrl); //服务器上的配置文件是否可用
                string content = GetDefaultConfigContent(configName + ".json");
                if (ret)
                {
                    content = File.ReadAllText(configPath);
                }
                else if (File.Exists(configMd5Path) && File.Exists(configPath)) //连不上服务器时，校验本地缓存是否可用
                {
                    var md5 = File.ReadAllText(configMd5Path);
                    UtilsMd5.TryCalcFileMd5(configPath, out string md5Calc);
                    if (md5Calc != null && md5Calc == md5)
                        content = File.ReadAllText(configPath);
                }
                
                string defaultContent = GetDefaultConfigContent(configName + ".json");

                int contentVersion = GetConfigVersion(content);
                int defaultContentVersion = GetConfigVersion(defaultContent);
                content = contentVersion >= defaultContentVersion ? content : defaultContent;

                if (File.Exists(manualConfigPath))
                {
                    string manualContent = File.ReadAllText(manualConfigPath);
                    int manualVersion = GetConfigVersion(manualContent);
                    int version = GetConfigVersion(content);

                    if (manualVersion > version)    //只要手动放置的配置文件版本号更高，就使用它。
                    {
                        content = manualContent;
                    }
                }

                if (string.IsNullOrEmpty(content))
                {
                    logger.ErrorFormat($"GetDeviceLevel failed: No available config file!");
                    deviceLevel =  - 1;
                }
                else
                {
                    logger.InfoFormat("call check device class by string: {0}", content);
                    deviceLevel =  PerfSightAgent.CheckDeviceClassByString(PERF_SIGHT_DOMAIN_NAME, content);
                    var configureList = GetConfigureList(content);
                    _deviceLevelInfo.defaultLevel = deviceLevel;
                    if (!isIos)
                    {
                        //0档机型再分档
                        if (deviceLevel <= 1)
                        {
                            //gpu分档
                            if (configureList.Contains(PERF_SIGHT_GPU_NAME))
                            {
                                deviceLevel = PerfSightAgent.CheckDeviceClassByString(PERF_SIGHT_GPU_NAME, content);
                                _deviceLevelInfo.gpuLevel = deviceLevel;
                            }
                            //level0性能指标分档
                            if (configureList.Contains(PERF_SIGHT_LEVEL0_NAME) && deviceLevel <= 1)
                            {
                                deviceLevel = PerfSightAgent.CheckDeviceClassByString(PERF_SIGHT_LEVEL0_NAME, content);
                                _deviceLevelInfo.level0 = deviceLevel;
                            }
                        }

                        //GPU档位校验
                        if (configureList.Contains(PERF_SIGHT_GPUCHECK_MIN_NAME) && configureList.Contains(PERF_SIGHT_GPUCHECK_MAX_NAME))
                        {
                            var gpuLevelMin = PerfSightAgent.CheckDeviceClassByString(PERF_SIGHT_GPUCHECK_MIN_NAME, content);
                            _deviceLevelInfo.gpuCheckMin = gpuLevelMin;
                            var gpuLevelMax = PerfSightAgent.CheckDeviceClassByString(PERF_SIGHT_GPUCHECK_MAX_NAME, content);
                            _deviceLevelInfo.gpuCheckMax = gpuLevelMax;
                            if (gpuLevelMin > 1 && deviceLevel < gpuLevelMin)
                                deviceLevel = gpuLevelMin;
                            if (gpuLevelMax > 1 && deviceLevel > gpuLevelMax)
                                deviceLevel = gpuLevelMax;
                        }

                        //档位细分
                        int segementationLevel = 1;
                        if (configureList.Contains(PERF_SIGHT_SEGMENTATION_NAME))
                        {
                            segementationLevel = PerfSightAgent.CheckDeviceClassByString(PERF_SIGHT_SEGMENTATION_NAME, content);
                            _deviceLevelInfo.segmentation = segementationLevel;
                        }
                        if (deviceLevel >= 0 && segementationLevel > 0)
                            deviceLevel = (deviceLevel - 1) * 100 + segementationLevel - 1;
                        else if (deviceLevel >= 0)
                            deviceLevel = (deviceLevel - 1) * 100;
                    }
                    else 
                    {
                        //档位细分
                        int segementationLevel = 1;
                        if (configureList.Contains(PERF_SIGHT_SEGMENTATION_NAME))
                        {
                            segementationLevel = PerfSightAgent.CheckDeviceClassByString(PERF_SIGHT_SEGMENTATION_NAME, content);
                            _deviceLevelInfo.segmentation = segementationLevel;
                        }
                        if (deviceLevel >= 0 && segementationLevel > 0)
                            deviceLevel = (deviceLevel - 1) * 100 + segementationLevel - 1;
                        else if (deviceLevel >= 0)
                            deviceLevel = (deviceLevel - 1) * 100;
                    }

                    _deviceConfigVersion = GetConfigVersion(content);
                }
            }
            catch (Exception ex)
            {
                logger.ErrorFormat($"GetDeviceLevel failed: {ex.Message}");
                logger.Error(ex);
                deviceLevel = -1;
            }
            return deviceLevel ;
        }

        /// <summary>
        /// 更新配置文件
        /// </summary>
        /// <param name="configPath">存放路径</param>
        /// <param name="md5Url">校验url</param>
        /// <param name="configUrl">配置文件url</param>
        /// <returns>新配置文件是否可用</returns>
        private bool UpdateServerDeviceConfig(string configPath, string md5Url, string configUrl)
        {
            bool useServerConfig = false;

            bool ret = HttpUtils.HttpGet(md5Url, 3000, out var md5Server); //获取服务器上的md5值

            string md5Path = configPath.Replace(".json", ".md5");

            if (!ret)
            {
                logger.Error("get level config md5 failed!");
            }
            else
            {
                if (File.Exists(configPath))
                {
                    UtilsMd5.TryCalcFileMd5(configPath, out string md5Calc);

                    if (md5Calc!= null && md5Calc != md5Server) //本地的配置已旧，删除
                    {
                        logger.Info("local level config is expired, remove it!");
                        File.Delete(configPath);
                    }
                }

                if (!File.Exists(configPath)) //文件不存在时，尝试下载
                {
                    ret = HttpUtils.HttpDownload(configUrl, configPath, 3000);
                    if (ret)
                    {
                        UtilsMd5.TryCalcFileMd5(configPath, out var md5);
                        if (md5 == null || md5 != md5Server)   //md5不一致
                        {
                            logger.ErrorFormat("down level config failed, md5 error. expect:{0}, get:{1}", md5Server, md5);
                        }
                        else //下载完成
                        {
                            logger.Info("down device level success!");
                            useServerConfig = true;
                            File.WriteAllText(md5Path, md5Server);
                            _deviceConfigStatus = EDeviceConfigStatus.Remote;
                        }
                    }
                    else
                    {
                        logger.Warn("down device level failed!");
                    }
                }
                else //文件存在并且校验完成
                {
                    useServerConfig = true;
                    _deviceConfigStatus = EDeviceConfigStatus.RemoteCache;
                }
            }

            return useServerConfig;
        }
        
        /// <summary>
        /// 读取StreamingAssets下的配置文件
        /// </summary>
        /// <returns></returns>
        private string GetDefaultConfigContent(string configName)
        {
            string filePath = Path.Combine(Application.streamingAssetsPath, "Config", "perfsight", configName);
            if (Application.platform == RuntimePlatform.Android)
            {
                using (UnityWebRequest request = UnityWebRequest.Get(filePath))
                {
                    var operation = request.SendWebRequest();
                    var lastTime = DateTime.UtcNow.AddSeconds(30);

                    while (!operation.isDone)
                    {
                        if (DateTime.UtcNow >= lastTime)
                        {
                            logger.Error("Timeout while reading app_config from StreamingAssets.");
                            return null;
                        }
                    }

                    if (request.result == UnityWebRequest.Result.Success)
                    {
                        return request.downloadHandler.text;
                    }

                    logger.ErrorFormat("Failed to read file from StreamingAssets: {0}", request.error);
                    return null;
                }
            }
            else
            {
                if (File.Exists(filePath))
                {
                    return File.ReadAllText(filePath);
                }

                return null;
            }
        }

        private List<string> GetConfigureList(string content) 
        {
            List<string> configureList = new List<string>();
            try
            {
                JObject obj = JObject.Parse(content);
                JToken token = obj["configureList"];
                if (token is { Type: JTokenType.Array })
                {
                    configureList = token.ToObject<List<string>>();
                }
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("Get config configureList failed: {0}", ex.Message);
                logger.Error(ex);
            }

            return configureList;
        }

        private int GetConfigVersion(string content)
        {
            try
            {
                JObject obj = JObject.Parse(content);
                JToken token = obj["version"];
                if (token is { Type: JTokenType.Integer })
                {
                    return token.ToObject<int>();
                }
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("Get config version failed: {0}", ex.Message);
                logger.Error(ex);
            }
            return -1;
        }
        public PerfTimeInfo GetTimeInfo()
        {
            return _timeInfo;
        }
    }
}
#endif
