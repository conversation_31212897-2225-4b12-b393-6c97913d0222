using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.ObjPool;

namespace WizardGames.Soc.SocSimulator.OfflinePreserver
{
    public class OfflinePreserverGroup : IRecyclable
    {
        public static OfflinePreserverGroup CreateOfflinePreserverGroup(long playerId)
        {
            OfflinePreserverGroup g = Pool.Get<OfflinePreserverGroup>();
            g.PlayerID = playerId;
            return g;
        }

        public long PlayerID { get; private set; }
        protected Dictionary<string, BaseOfflinePreserver> preservers = new Dictionary<string, BaseOfflinePreserver>();
        public bool Finish => preservers.Count == 0;

        public void AddPreserver(BaseOfflinePreserver preserver)
        {
            if (preservers.ContainsKey(preserver.TypeName))
            {//同一种类型有新的直接用新的替换老的
                preservers[preserver.TypeName].Release();
                preservers.Remove(preserver.TypeName);
            }

            preservers.Add(preserver.TypeName, preserver);
        }


        public void Update()
        {
            //判断preservers中每个元素是否Finished 如果是移除
            List<string> toRemove = Pool.GetList<string>();
            foreach (var kvp in preservers)
            {
                if (kvp.Value.Finished)
                {
                    toRemove.Add(kvp.Key);
                }
            }
            foreach (var key in toRemove)
            {
                preservers[key].Release();
                preservers.Remove(key);
            }
            Pool.ReleaseList(toRemove);
        }

        public void Release()
        {
            foreach (var kvp in preservers)
            {
                kvp.Value.Release();
            }
            preservers.Clear();
            Pool.Release(this);
        }

        public void OnGet()
        {
            //throw new System.NotImplementedException();
        }

        public void OnRelease()
        {
            
        }

        public void OnDestroy()
        {
            //throw new System.NotImplementedException();
        }
    }
}
