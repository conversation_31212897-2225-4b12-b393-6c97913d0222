using FairyGUI;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Collection;
using WizardGames.Soc.Common.Const;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.SocClient.ClientItem;
using WizardGames.Soc.SocClient.Collection;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.State.Character;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.Character.State.Event;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.SocConst.Soc.Const;
using WizardGames.Soc.Common.Data.DataItem;

namespace WizardGames.Soc.SocClient.Ui
{
    /// <summary>
    /// HUD武器栏快捷选单
    /// </summary>
    public partial class UiHudWeaponChoose : WindowComBase, IUiFps30Update
    {
        private GComponent comRoot;
        private GComponent comBoard;
        private GLabel labelTitle;
        private Controller ctrlBoardStyle;
        private GComponent comDragTip;
        private ComHudDropBar comDropbar;
        private Controller ctrlDropBarStyle;
        private GObject objAutoScrollTop;
        private GObject objAutoScrollBottom;
        private GList listIcons;

        private List<BaseItemNode> curItems = new();
        private List<BaseItemNode> itemListForResort = new();
        private HashSet<long> oldItemRec = new();
        private Queue<BaseItemNode> newItemQueue = new();
        private UiHudElemWeapons elemWeapon;

        private bool isFirstRefresh = false;
        private int minItemCount = 0;
        private bool canShowAutoScrollTrigger = false;
        private bool isAutoScrollTriggerVisible = false;
        protected float autoScrollStep = 0;
        protected float autoScrollFactor = 0;
        protected float autoScrollTimer = 0;
        protected float autoScrollTime = 0;
        private float triggerAutoChooseX;

        private static bool openWithDragState = false;
        public static int CurChooseIndex { get; private set; } = -1;
        public static bool IsChooseVisible { get; private set; } = false;
        public static bool IsInDragging { get; private set; } = false;

        private bool IsInDragState
        {
            get => ctrlBoardStyle.selectedPage.Equals("drag");
            set
            {
                IsInDragging = value;
                ctrlBoardStyle.SetSelectedPage(value ? "drag" : "normal");
            }
        }

        protected override void OnInit()
        {
            base.OnInit();
            autoScrollTime = Mc.Tables.TbGlobalConfig.GetWeaponChooseAutoScrollTriggerTime();
            autoScrollStep = Mc.Tables.TbGlobalConfig.GetWeaponChooseAutoScrollStep();

            comRoot = ContentPane.GetChild("root").asCom;
            comBoard = comRoot.GetChild("content").asCom;
            ctrlBoardStyle = comBoard.GetController("style");
            ctrlDropBarStyle = comBoard.GetController("dropBarPos");

            labelTitle = comBoard.GetChild("title").asLabel;
            comDragTip = comBoard.GetChild("dragTips").asCom;
            comDropbar = comBoard.GetChild("dropbar") as ComHudDropBar;
            comDropbar.visible = false;
            objAutoScrollTop = comBoard.GetChild("autoScrollTop");
            objAutoScrollBottom = comBoard.GetChild("autoScrollBottom");
            RegisterAutoScrollTigger(objAutoScrollTop.asCom);
            RegisterAutoScrollTigger(objAutoScrollBottom.asCom);

            listIcons = comBoard.GetChild("iconList").asList;
            listIcons.SetVirtual();
            listIcons.itemRenderer = IconRenderer;
            listIcons.scrollPane.onScroll.Add(CheckAutoScrollTriggerState);
            listIcons.BindArrowVisibleExtCondition(() => !canShowAutoScrollTrigger);

            minItemCount = Mathf.FloorToInt(listIcons.scrollPane.viewHeight / listIcons.defaultItemSize.y);

            RegisterEvent<UiHudElem>(EventDefine.HudBlockElemChange, elem=>
            {
                if (null != elemWeapon && elemWeapon == elem) return;
                HideSelf();
            });
            RegisterEvent<WindowComBase>(EventDefine.UiOpenEvent, win=>
            {
                if (null == win || win.uiName == name) return;
                int layerId = win?.WinInfo.Layer ?? 0;
                if (layerId <= LayerIdConst.GamePlayOverlay || layerId >= LayerIdConst.Popup) return;
                HideSelf();
            });
            RegisterEvent<ItemDragInfo>(EventDefine.HudWeaponIconDragStart, OnHudWeaponIconDragStart);
            RegisterEvent(EventDefine.HudWeaponIconDragEnd, OnHudWeaponIconDragEnd);
            RegisterEvent<BaseItemNode>(EventDefine.UpdateItemNode, OnUpdateItemNode);
        }

        private bool CheckAndInsertItem(BaseItemNode itemNode)
        {
            if (!Mc.CollectionItem.CanPutInfoWeaponSlot(itemNode)) return true;
            var itemCfg = itemNode.Config;
            if (itemCfg.Manufacturing != ItemType.Weapon) return true;
            if (isFirstRefresh)
            {
                curItems.Add(itemNode);
            }
            else
            {
                if (oldItemRec.Contains(itemNode.Id)) oldItemRec.Remove(itemNode.Id);
                else newItemQueue.Enqueue(itemNode);
            }
            return true;
        }

        /// <summary>
        /// 刷新物品列表
        /// 如果是第一次刷新，则获取背包与快捷栏上所有武器并排序显示
        /// 如果是后续刷新，则需要保持第一次刷新的物品顺序，优先将新物品插入到被移除物品的位置
        /// </summary>
        private void RefreshList()
        {
            if (UiHud.IsInEdit)
            {
                curItems.Clear();
                // 编辑模式下空节点的展示要撑满，多一个
                for (int i = curItems.Count; i <= minItemCount; i++)
                {
                    curItems.Add(null);
                }
            }
            else
            {
                isFirstRefresh = curItems.Count == 0;
                if (isFirstRefresh)
                {
                    curItems.Clear();
                }
                else
                {
                    foreach (var item in curItems)
                    {
                        if (null == item) continue;
                        oldItemRec.Add(item.NodeId);
                    }
                }
                // 收集背包与快捷栏（武器栏除外）上的所有武器以显示
                IAlpha3ItemContainerWrap containerMain = Mc.CollectionItem.InventoryCom.ContainerMainNode;
                MgrCollectionItem.IterOneContainer(containerMain, CheckAndInsertItem);
                if (isFirstRefresh)
                {
                    curItems.Sort(MgrCollectionItem.CommonItemNodeBagSort);
                }
                else if (oldItemRec.Count > 0 || newItemQueue.Count > 0)
                {
                    itemListForResort.AddRange(curItems);
                    curItems.Clear();
                    foreach (var item in itemListForResort)
                    {
                        if (null == item) continue;
                        // oldItemRec中还存在说明是已经被移除的物品，使用一个新物品填入它的位置
                        if (oldItemRec.Contains(item.Id))
                        {
                            if (newItemQueue.Count > 0)
                            {
                                curItems.Add(newItemQueue.Dequeue());
                            }
                            continue;
                        }
                        curItems.Add(item);
                    }
                    itemListForResort.Clear();
                    oldItemRec.Clear();
                    // 如果还有没填完的新物品，追加到列表末尾
                    while (newItemQueue.Count > 0)
                    {
                        curItems.Add(newItemQueue.Dequeue());
                    }
                    newItemQueue.Clear();
                }
                // 物品数量小于预定数量时，使用空节点进行补足
                for (int i = curItems.Count; i < minItemCount; i++)
                {
                    curItems.Add(null);
                }
            }

            ItemsReSort();

            if (listIcons.numItems == curItems.Count) listIcons.RefreshVirtualList();
            else listIcons.numItems = curItems.Count;
            // 内容高度未超过视口高度，不允许滚动
            var pane = listIcons.scrollPane;
            pane.touchEffect = pane.contentHeight > pane.viewHeight;
        }

        private void OnUpdateItemNode(BaseItemNode itemNode)
        {
            RefreshList();
        }

        public override void OnDestroy()
        {
            base.OnDestroy();
            elemWeapon = null;
        }
        /// <summary>
        /// 屏幕尺寸变动
        /// 折叠屏
        /// </summary>
        private void OnMakeFullScreen()
        {
            RefreshBoardState();
        }

        private void IconRenderer(int index, GObject item)
        {
            var comRoot = item.asCom;
            if (null == comRoot) return;
            var icon = comRoot.GetChild("icon") as ComItemIcon;
            var curItems = GetRenderData();
            if (null == icon || index >= curItems.Count) return;
            var tarItem = curItems[index];
            var comChoose = comRoot.GetChild("choose").asCom;
            comChoose.visible = CurChooseIndex == index; ;
            icon.CanDrag = false;
            icon.SetInstData(tarItem);
            icon.SetValue((tarItem as WeaponItemNode)?.BulletAmount.ToString() ?? null);
            var globalCfg = Mc.Tables.TbGlobalConfig;
            icon.SetVerticalDragSlow(globalCfg.GetWeaponChooseSlowDragTriggerTime(), globalCfg.GetWeaponChooseSlowDragTriggerAngle());
            icon.onClick.Set(() => OnClickIcon(icon, tarItem, index));
            icon.OnIconDragStart = OnIconDragStart;
            icon.OnIconDragEnd = OnIconDragEnd;
            icon.OnIconAcceptDrag = OnIconAcceptDrag;
            icon.OnMouseOrTouchMoveIn = ctx =>
            {
                if (UiItemIconDragDrop.IsDragging && UiItemIconDragDrop.CurDragIcon != icon)
                {
                    // 检查是否是武器
                    var weapon = UiItemIconDragDrop.CurDragIcon as ComItemIcon;
                    if (weapon != null && weapon.InstItem.TypeId == ItemEntityType.Weapon)
                    {
                        icon.SetHighlight(true);
                    } 
                }
            };
            icon.OnMouseOrTouchMoveOut = ctx => icon.SetHighlight(false);
        }

        private void OnClickIcon(ComItemIcon icon, BaseItemNode itemNode, int index)
        {
            if (null == itemNode || null == elemWeapon) return;
            if(icon == null || icon.IsIconEnable == false)  return;
            if (elemWeapon.MenuChooseIndex < 0)
            {
                CurChooseIndex = CurChooseIndex == index? -1: index;
                listIcons.RefreshVirtualList();
                return;
            }
            int weaponIndex = elemWeapon.TransShowIndexWithIconIndex(elemWeapon.MenuChooseIndex);
            elemWeapon.MenuChooseIndex = -1;
            int movePosIndex = (int)HoldItemIndex.Item7 + weaponIndex;
            Mc.CollectionItem.MoveItem(itemNode.NodeId, NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Belt, movePosIndex);
        }

        public void ResetIconChoose()
        {
            CurChooseIndex = -1;
            listIcons.RefreshVirtualList();
        }

        private bool OnIconDragStart(ItemDragInfo info)
        {
            comDropbar.visible = true;
            return true;
        }

        private void OnIconDragEnd()
        {
            comDropbar.visible = false;
        }

        private bool OnIconAcceptDrag(ComBaseIcon icon, ItemDragInfo info)
        {
            OnIconAcceptDrag_Palform(icon, info);
            if (icon is not ComHUDWeaponItemIcon myIcon || null == myIcon.InstItem) return false;
            var itemNode = myIcon.InstItem;
            var tarIcon = UiItemIconDragDrop.CurDragIcon as ComItemIcon;
            if (null == tarIcon) return false;
            int movePosIndex = tarIcon.Position;
            if (movePosIndex >= 0)
            {
                Mc.CollectionItem.MoveItem(itemNode.NodeId, NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Belt, movePosIndex);
            }
            elemWeapon.MenuChooseIndex = -1;
            ResetIconChoose();
            return true;
        }

        private void OnHudWeaponIconDragStart(ItemDragInfo info)
        {
            comDropbar.visible = true;
            SetAutoScrollTriggerEnable(true);
            UiItemIconDragDrop.UseFromIconSize = true;
            UiItemIconDragDrop.OnDraggingIcon = OnIconDragging;
            OnHudWeaponIconDragStart_Plaform();
        }

        private void OnHudWeaponIconDragEnd()
        {
            comDropbar.visible = false;
            SetAutoScrollTriggerEnable(false);
            UiItemIconDragDrop.OnDraggingIcon = null;
            UiItemIconDragDrop.UseFromIconSize = false;
            OnHudWeaponIconDragEnd_Plaform();
        }

        private void OnIconDragging(float x, float y)
        {
            if (!IsInDragging) return;
            // 当图标在横向越过拖拽提示范围时，显示选单
            bool areaAtLeft = true;
            if (null != elemWeapon) areaAtLeft = elemWeapon.IsTabLeft;
            if ((areaAtLeft && x <= triggerAutoChooseX) || (!areaAtLeft && x >= triggerAutoChooseX))
            {
                if (UiItemIconDragDrop.IsDragging)
                {
                    UiItemIconDragDrop.SetDragIconDir(EIconDragDir.None);
                }
                IsInDragState = false;
            }
        }

        private void RegisterAutoScrollTigger(GComponent trigger)
        {
            if (null == trigger) return;
            var posStyleCtrl = trigger.GetController("posStyle");
            bool isDirectionUp = posStyleCtrl.selectedPage.Equals("bottom");
            var triggerStyleCtrl = trigger.GetController("triggerStyle");
            var triggerArea = trigger.GetChild("triggerArea");
            triggerArea.onRollOver.Add(() =>
            {
                autoScrollFactor = isDirectionUp ? autoScrollStep : -autoScrollStep;
                triggerStyleCtrl.SetSelectedPage("on");
            });
            triggerArea.onRollOut.Add(() =>
            {
                ResetAutoScroll();
                triggerStyleCtrl.SetSelectedPage("off");
            });
            trigger.visible = false;
        }

        private void CheckAutoScrollTriggerState()
        {
            if (!canShowAutoScrollTrigger)
            {
                if (isAutoScrollTriggerVisible)
                {
                    isAutoScrollTriggerVisible = false;
                    objAutoScrollTop.visible = false;
                    objAutoScrollBottom.visible = false;
                }
                return;
            }
            isAutoScrollTriggerVisible = true;
            objAutoScrollTop.visible = listIcons.scrollPane.percY > 0;
            objAutoScrollBottom.visible = !listIcons.scrollPane.isBottomMost;
        }

        /// <summary>
        /// 重置自动滚动状态
        /// </summary>
        protected virtual void ResetAutoScroll()
        {
            autoScrollFactor = 0;
            autoScrollTimer = 0;
        }

        private void SetAutoScrollTriggerEnable(bool enable)
        {
            canShowAutoScrollTrigger = enable;
            listIcons.SetArrowVisible(!enable);
            CheckAutoScrollTriggerState();
            if (!enable) ResetAutoScroll();
        }

        public virtual void CheckAutoScroll(float dt)
        {
            if (0 == autoScrollFactor) return;
            if (autoScrollTimer < autoScrollTime)
            {
                autoScrollTimer += dt;
                return;
            }
            listIcons.scrollPane.SetPosY(listIcons.scrollPane.scrollingPosY + dt * autoScrollFactor, false);
        }

        public void OnFps30Update(float dt)
        {
            CheckAutoScroll(dt);
        }

        public static bool TrySwipeWeaponWithBeltIcon(ComItemIcon icon)
        {
            if (null == icon) return false;
            var win = Mc.Ui.GetWindow("UiHudWeaponChoose") as UiHudWeaponChoose;
            if (null == win || null == win.curItems) return false;
            if (CurChooseIndex < 0 || CurChooseIndex >= win.curItems.Count) return false;
            var movePosIndex = icon.Position;
            var itemNode = win.curItems[CurChooseIndex];
            Mc.CollectionItem.MoveItem(itemNode.NodeId, NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Belt, movePosIndex);
            win.ResetIconChoose();
            return true;
        }

        public static bool TryResetIconChoose()
        {
            var win = Mc.Ui.GetWindow("UiHudWeaponChoose") as UiHudWeaponChoose;
            if (null == win) return false;
            if (CurChooseIndex == -1) return false;
            win.ResetIconChoose();
            return true;
        }
    }
}