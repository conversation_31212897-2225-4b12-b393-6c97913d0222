using System.IO;
using UnityEngine;
using UnityEngine.Networking;
using WizardGames.Soc.Common.Unity.SocAssetBundle;
using WizardGames.Soc.Common.Unity.SocAssetBundle.SocBundleRuntime;

namespace WizardGames.Soc.Common.Unity.Loader
{
    public static class VersionCodeUtils
    {
        static SocLogger logger = LogHelper.GetLogger(typeof(VersionCodeUtils));

        public static readonly string RemoteClientVersionPath = Path.Combine(Application.persistentDataPath, SocAssetBundleConst.ClientVersionFileName);
        
#if UNITY_EDITOR
        public static void CreateClientVersionFile(string clientVersion)
        {
            if (string.IsNullOrEmpty(clientVersion))
            {
                throw new UnityEditor.Build.BuildFailedException($"Client version is null or empty! {clientVersion}");
            }
            
            var versionFilePath = Path.Combine(Application.streamingAssetsPath, SocAssetBundleConst.ClientVersionFileName);
            File.WriteAllText(versionFilePath, clientVersion);
            Debug.Log($"Save client version {clientVersion} to {versionFilePath}");
            UnityEditor.AssetDatabase.Refresh();
        }
        
        public static void CreateAppVersionFile(string appVersion)
        {
            if (string.IsNullOrEmpty(appVersion))
            {
                throw new UnityEditor.Build.BuildFailedException($"App version is null or empty! {appVersion}");
            }
            
            var versionFilePath = Path.Combine(Application.streamingAssetsPath, SocAssetBundleConst.AppVersionFileName);
            File.WriteAllText(versionFilePath, appVersion);
            Debug.Log($"Save app version {appVersion} to {versionFilePath}");
            UnityEditor.AssetDatabase.Refresh();
        }

        public static void CreateDolphinVersionFile(string clientVersion)
        {
            // 截取clientVersion后四段
            var versionParts = clientVersion.Split('.');
            if (versionParts.Length < 3)
            {
                throw new UnityEditor.Build.BuildFailedException($"Client version format is invalid! {clientVersion}");
            }
            var dolphinVersion = string.Join(".", versionParts[^4], versionParts[^3], versionParts[^2], versionParts[^1]);
            var versionFilePath = Path.Combine(Application.streamingAssetsPath, SocAssetBundleConst.DolphinVersionFileName);
            File.WriteAllText(versionFilePath, dolphinVersion);
            Debug.Log($"Save dolphin version {dolphinVersion} to {versionFilePath}");
            UnityEditor.AssetDatabase.Refresh();
        }
#endif
        
        public static string GetClientVersion()
        {
            if (File.Exists(RemoteClientVersionPath))
            {
                string clientVersion = File.ReadAllText(RemoteClientVersionPath);
                return clientVersion;
            }
            logger.WarnFormat("File {0} not found!", RemoteClientVersionPath);
            
            var clientVersionRes = ReadFileFromStreamingAssets(SocAssetBundleConst.ClientVersionFileName);
            if(clientVersionRes != null)
            {
                return clientVersionRes;
            }
            logger.WarnFormat("File {0} not found in StreamingAssets!", SocAssetBundleConst.ClientVersionFileName);
            
            return Application.version;
        }
        
        public static string GetAppVersion()
        {
            var appVersion = ReadFileFromStreamingAssets(SocAssetBundleConst.AppVersionFileName);
            if (appVersion == null)
            {
                logger.Warn("File app_version.txt not found!");
                return null;
            }
            
            return appVersion;
        }

        public static string GetResVersion()
        {
            var filePath = Path.Combine(BundleRuntimeContext.AssetBundleAdapterRes.PersistentPath, SocAssetBundleConst.AssetBundleVersionFileName);
            if (!File.Exists(filePath))
            {
                logger.WarnFormat("File {0} not found!", filePath);
                return null;
            }
            
            var resVersion = File.ReadAllText(filePath);
            return resVersion;
        }
        
        public static string GetCodeVersion()
        {
            var filePath = Path.Combine(BundleRuntimeContext.AssetBundleAdapterRes.PersistentPath, SocAssetBundleConst.CodeVersionFileName);
            if (!File.Exists(filePath))
            {
                logger.WarnFormat("File {0} not found!", filePath);
                return null;
            }
            
            var resVersion = File.ReadAllText(filePath);
            return resVersion;
        }

        public static string GetDolphinVersion()
        {
            var dolphinVersion = ReadFileFromStreamingAssets(SocAssetBundleConst.DolphinVersionFileName);
            if(dolphinVersion != null)
            {
                return dolphinVersion;
            }
            logger.WarnFormat("File {0} not found in StreamingAssets!", SocAssetBundleConst.DolphinVersionFileName);
            
            return GetClientVersion();
        }

        public static string ReadFileFromStreamingAssets(string fileName)
        {
            string filePath = Path.Combine(Application.streamingAssetsPath, fileName);
#if UNITY_EDITOR || UNITY_IOS
            if (File.Exists(filePath))
                return File.ReadAllText(filePath);

            logger.WarnFormat("File {0} not found!", filePath);
            return null;
#endif
            UnityWebRequest request = UnityWebRequest.Get(filePath);
            request.SendWebRequest();
            while (!request.isDone) {}

            if (request.result == UnityWebRequest.Result.Success) 
                return request.downloadHandler.text;
                
            logger.ErrorFormat("ReadFileFromStreamingAssets File: {0} Error: {1}", filePath, request.error);
            return null;
        }
        
        public static void PrintAppVersion()
        {
            var clientVersion = GetClientVersion();
            var appVersion = GetAppVersion();
            logger.ReleaseCriticalFormat("Client version: {0}", clientVersion);
            logger.ReleaseCriticalFormat("App version: {0}", appVersion);
        }
        
        public static void PrintVersionCode()
        {
            var clientVersion = GetClientVersion();
            var appVersion = GetAppVersion();
            var resVersion = GetResVersion();
            var codeVersion = GetCodeVersion();
            
            logger.InfoFormat("Client version: {0}", clientVersion);
            logger.InfoFormat("App version: {0}", appVersion);
            logger.InfoFormat("Res version: {0}", resVersion);
            logger.InfoFormat("Code version: {0}", codeVersion);
        }
    }
}