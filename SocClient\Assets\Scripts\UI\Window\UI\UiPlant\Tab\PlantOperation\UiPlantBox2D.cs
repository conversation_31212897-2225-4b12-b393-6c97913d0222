using Cysharp.Text;
using FairyGUI;
using System;
using System.Collections.Generic;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Plant;
using WizardGames.Soc.SocClient.Ui.Utils;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.Common.CustomType;
using UnityEngine;

namespace WizardGames.Soc.SocClient.Ui
{
    public class UiPlantBox2D
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(UiPlantBox2D));
        public GList plantLst;
        private Dictionary<GObject, Dictionary<string, GObject>> plantGODic = new Dictionary<GObject, Dictionary<string, GObject>>();
        private PlantBoxData curPlantBox;
        private EPageType pageType;

        private UiHybridSelectPlant uiHybridSelectPlant;
        
        private List<int> selectedIdxes = new List<int>();

        private Action itemSelectChangeAction;

        public int lastSelectedIndex = -1;
        private UiPlantOperationSubPanel uiPlantOperationSubPanel;
        
        public static UiPlantBox2D Create(GComponent com,UiPlantOperationSubPanel plantOperationSubPanel, EPageType pageType = EPageType.Default, Action onChangeAction = null, UiHybridSelectPlant uiSelectPlant = null)
        {
            var rt = new UiPlantBox2D();
            rt.Init(com,plantOperationSubPanel, pageType, onChangeAction, uiSelectPlant);
            return rt;
        }
        
        private void Init(GComponent com,UiPlantOperationSubPanel plantOperationSubPanel, EPageType pageType, Action onChangeAction = null, UiHybridSelectPlant uiSelectPlant = null)
        {
            uiHybridSelectPlant = uiSelectPlant;
            this.uiPlantOperationSubPanel = plantOperationSubPanel;
            itemSelectChangeAction = onChangeAction;
            if (com == null)
            {
                logger.Info("[Init] com is null");
                return;
            }
            this.pageType = pageType;
            try
            {
                plantLst = com.GetChild("list").asList;
                plantLst.itemRenderer = OnRenderPlant;
                plantLst.onClickItem.Set(OnClickPlant);

                switch (pageType)
                {
                    case EPageType.Seed:
                        plantLst.selectionMode = ListSelectionMode.Single;
                        break;
                    case EPageType.Manure:
                        plantLst.selectionMode = ListSelectionMode.Multiple_SingleClick;
                        break;
                    case EPageType.Hybrid:
                        plantLst.selectionMode = ListSelectionMode.Multiple_SingleClick;
                        break;
                    default:
                        plantLst.selectionMode = ListSelectionMode.None;
                        break;
                }

                Mc.Msg.AddListener<int, int>(EventDefine.PlantBoxModelSelectedSlot, OnPlantBoxModelSelectedSlot);
                Mc.Msg.AddListener<int, long, long>(EventDefine.PrePlant, OnPrePlant);
                Mc.Msg.AddListener<int, long>(EventDefine.CancelPrePlant, OnCancelPrePlant);
            }
            catch (Exception e)
            {
                logger.InfoFormat("[Init] error {0}", e.Message);
            }
        }

        public void Refresh(PlantBoxData plantBox, bool first = false)
        {
            curPlantBox = plantBox;
            if (curPlantBox != null && curPlantBox.plantBoxCfg != null && plantLst != null)
            {
                plantLst.numItems = curPlantBox.plantBoxCfg.Capacity;

                //初次打开，或重新打开种植界面时的操作
                if (first) 
                {
                    switch (pageType)
                    {
                        case EPageType.Seed:
                            for (var i = 0; i < curPlantBox.plantBoxCfg.Capacity; ++i)
                            {
                                var plant = curPlantBox.GetPlantData(i);
                                if (plant == null)
                                {
                                    //选中第一个没有植物的index并记录下种植
                                    plantLst.selectedIndex = i;
                                    lastSelectedIndex = i;
                                    Mc.Msg.FireMsg(EventDefine.PlantPageSelectedSlot, plantLst.selectedIndex);
                                    break;
                                }
                            }
                            break;
                        case EPageType.Manure:
                            {
                                plantLst.ClearSelection();
                                //批量选择
                                var totalManure = Mc.Plant.TotalManure;
                                int added = 0;
                                for (var i = 0; i < curPlantBox.plantBoxCfg.Capacity; ++i)
                                {
                                    var plant = curPlantBox.GetPlantData(i);
                                    if (plant != null && plant.CanManure())
                                    {
                                        if (added < totalManure)
                                        {
                                            ++added;
                                            plantLst.AddSelection(i, false);
                                        }
                                        else
                                        {
                                            break;
                                        }
                                    }
                                }
                                Mc.Msg.FireMsg<List<int>>(EventDefine.SelectMultipleSlotWhenOpenManurePage, plantLst.GetSelection());
                            }
                            break;
                        case EPageType.Hybrid:
                            break;
                        default:
                            plantLst.selectionMode = ListSelectionMode.None;
                            break;
                    }
                }
            }
        }

        private void OnRenderPlant(int index, GObject obj)
        {
            if (curPlantBox == null || obj == null) return;
            var comObj = obj.asCom;
            if (comObj == null) return;

            //添加item里的组件到dict中进行管理
            if (!plantGODic.ContainsKey(obj))
            {
                plantGODic.Add(obj, new Dictionary<string, GObject>());
                plantGODic[obj]["plant"] = comObj.GetChild("plant");
                plantGODic[obj]["disable"] = comObj.GetChild("disable");
                plantGODic[obj]["target"] = comObj.GetChild("target");
                plantGODic[obj]["groupMaterial"] = comObj.GetChild("groupMaterial");
                plantGODic[obj]["text_materialIndex"] = comObj.GetChild("text_materialIndex");
                plantGODic[obj]["plantedBg"] = comObj.GetChild("plantedBg");
            }

            var objDic = plantGODic[obj];
            if (objDic == null) return;

            //获取plantData对组件进行渲染
            var plantData = curPlantBox.GetPlantData(index);
            if (objDic.TryGetValue("plant", out var plantLoader) && plantLoader != null)
            {
                var plantDrag = plantLoader as ComItemIcon;
                if (plantDrag != null)
                {
                    if (plantData != null && plantDrag != null)
                    {
                        var itemCfg = Mc.Tables.TbItemConfig.GetOrDefault(plantData.SeedId);
                        plantDrag.SetTepmlateData(itemCfg);
                        plantDrag.SetIconBgVisible(false);
                        SafeUtil.SafeSetVisible(plantDrag, true);
                        plantDrag.CanDrag = false;

                        plantGODic[obj]["plantedBg"].visible = true;
                    }
                    else
                    {
                        var bizId = pageType == EPageType.Seed ? Mc.Plant.GetPrePlantBizId(index) : 0;
                        if (bizId != 0)
                        {
                            var itemCfg = Mc.Tables.TbItemConfig.GetOrDefault(bizId);
                            if (itemCfg != null)
                            {
                                plantDrag.SetTepmlateData(itemCfg);
                                plantDrag.SetIconBgVisible(false);
                            }
                            else
                            {
                                plantDrag.SetEmpty();
                                plantDrag.SetIconBgVisible(false);
                            }
                        }
                        else
                        {
                            plantDrag.SetEmpty();
                            plantDrag.SetIconBgVisible(false);
                        }

                        plantGODic[obj]["plantedBg"].visible = false;
                    }
                }

                Controller canHarvestControl = comObj.GetController("CanHarvest");
                if (plantData != null && plantData.Stage == PlantStage.Harvest)
                {
                    canHarvestControl.selectedPage = "True";
                }
                else
                {
                    canHarvestControl.selectedPage = "False";
                }
            }
#if POCO
            obj.PocoRegister(ZString.Format("PlantBoxIndex{0}", index));
#endif

            //根据不同功能显示额外的状态
            switch (pageType)
            {
                case EPageType.Seed:
                    {
                        objDic.TryGetValue("plant", out var plantCom);
                        if (plantCom is ComItemIcon plantDrag)
                        {
                            Controller selectCtrl = comObj.GetController("button");
                            plantDrag.OnMouseOrTouchMoveIn = (ctx) =>
                            {
                                if (ctx == null || !UiItemIconDragDrop.IsDragging)
                                {
                                    return;
                                }

                                plantLst.selectedIndex = -1;
                                plantLst.selectedIndex = index;
                                Mc.Msg.FireMsg(EventDefine.PlantPageSelectedSlot, plantLst.selectedIndex);
                            };

                            plantDrag.OnIconAcceptDrag = (item1, item2) =>
                            {
                                UiItemIconDragDrop.CancelItemDrag();
                                if (curPlantBox == null) return false;
                                if (plantData != null) return false;
                                var dragInfo = item2;
                                if (dragInfo == null) return false;
                                if (dragInfo.customData is BaseItemNode seedData)
                                {
                                    Mc.Plant.PrePlant(index, seedData.BizId, seedData.Id);
                                }

                                return true;
                            };
                            comObj.touchable = plantData == null;
                        }
                    }
                    break;
                case EPageType.Hybrid:
                    {
                        //能否操作
                        bool canSelect = false;
                        // 选择阶段不能操作已锁定的植株
                        bool islock = false;
                        //当前item在已选植物中的下标位置
                        int selectSortIndex = SelectedIndexSort.Contains(index) ? SelectedIndexSort.IndexOf(index) : -1;
                        //目标植株位置
                        int firstSelectedPlantIndex = SelectedIndexSort.Count > 0 ? SelectedIndexSort[0] : -1;
                        //当前植株是否是目标植株
                        var isFirstSelect = IsFirstSelect(index);

                        if (plantData != null)
                        {
                            if (uiHybridSelectPlant?.CurSelectStep == SelectStep.Target)
                            {
                                //选择目标阶段锁住材料植株的操作
                                islock = !isFirstSelect && selectSortIndex >= 0;
                                //目标植株选择阶段只能选择杂交期的植株
                                canSelect = SelectedIndexSort.Count == 0 ? plantData.CanHybrid() : isFirstSelect || selectSortIndex >= 0;
                            }
                            else
                            {
                                //材料选择阶段锁住目标植株
                                islock = isFirstSelect;

                                //材料植株选择 1、种子类型相同 2、符合能杂交的阶段 ，不能操作目标植株
                                var targetPlantData = curPlantBox.GetPlantData(firstSelectedPlantIndex);
                                if (targetPlantData != null)
                                {
                                    var canHybridStageList = McCommon.Tables.TbPlantConstConfig.CanHybridizationStage;
                                    canSelect = plantData.SeedId == targetPlantData.SeedId
                                                && canHybridStageList.ContainsWithoutLinq((int)plantData.Stage);
                                }
                            }
                        }

                        if (!canSelect && SelectedIndexSort.Contains(index)) RemoveIndex(index);

                        comObj.touchable = canSelect && !islock;
                        comObj.asButton.selected = SelectedIndexSort.Contains(index);

                        if (plantGODic[obj].TryGetValue("disable", out var disable) && disable != null)
                        {
                            disable.visible = !canSelect && !islock;
                        }

                        if (objDic.TryGetValue("target", out var target) && target != null)
                        {
                            target.visible = isFirstSelect;
                        }

                        if (objDic.TryGetValue("groupMaterial", out var groupMaterial) && groupMaterial != null)
                        {
                            var isMaterial = selectSortIndex > 0;
                            groupMaterial.visible = isMaterial;
                            if (isMaterial && objDic.TryGetValue("text_materialIndex", out var textMaterialIndex) && textMaterialIndex != null)
                            {
                                SafeUtil.SafeSetText(textMaterialIndex, selectSortIndex.ToString());
                            }
                        }
                        break;
                    }
            }

        }

        public List<int> SelectedIndexSort = new();

        private void OnClickPlant(EventContext context)
        {
            if (plantLst == null) return;
            if (curPlantBox == null || curPlantBox.plantBoxCfg == null) return;

            var index = plantLst.GetChildIndex((GObject)context.data);
            var itemIndex = plantLst.ChildIndexToItemIndex(index);
            if (itemIndex < 0 || itemIndex >= curPlantBox.plantBoxCfg.Capacity) return;

            switch (pageType)
            {
                case EPageType.Manure:
                    {
                        var btn = context.data as GButton;
                        if (btn != null)
                        {
                            if (btn.selected)
                            {
                                var plant = curPlantBox.GetPlantData(index);
                                if (plant == null)
                                {
                                    //此区域无农作物，无法施肥
                                    Mc.MsgTips.ShowRealtimeWeakTip(23109);
                                    plantLst.RemoveSelection(index);
                                    break;
                                }
                                else if (!plant.CanManure())
                                {
                                    //此区域的农作物受肥量已达上限，无法施肥
                                    Mc.MsgTips.ShowRealtimeWeakTip(23110);
                                    plantLst.RemoveSelection(index);
                                    break;
                                }
                            }

                            //此处由于对于同一个id，同时注册了两个函数，这两个函数会按照不确定的顺序（可能是注册顺序的倒序进行调用）。需要保证函数与函数间没有先后序依赖
                            Mc.Msg.FireMsg(EventDefine.ManurePageSelectedSlot, itemIndex);
                        }
                    }
                    break;
                case EPageType.Default:
                    {
                        if (curPlantBox.GetPlantData(index) != null)
                        {
                            uiPlantOperationSubPanel.ChangePage(EPageType.PlantDetail);
                            // Mc.Msg.FireMsg(EventDefine.OnChangePlantMainPage, EPageType.PlantDetail);
                            Mc.Msg.FireMsg(EventDefine.DetailPageSelectedSlot, index);
                            uiPlantOperationSubPanel.PlantModelPreview.UpdateHighLight(index);
                        }
                        else
                        {
                            uiPlantOperationSubPanel.ChangePage(EPageType.Seed);
                            // Mc.Msg.FireMsg(EventDefine.OnChangePlantMainPage, EPageType.Seed);
                        }
                    }
                    break;
                case EPageType.Seed: 
                    {
                        Mc.Msg.FireMsg(EventDefine.PlantPageSelectedSlot, itemIndex);//发消息更新模型
                    }
                    break;
            }

            lastSelectedIndex = itemIndex;
            itemSelectChangeAction?.Invoke();
        }

        /// <summary>
        /// 当前item是否是首选项
        /// </summary>
        /// <param name="index"></param>
        /// <returns></returns>
        public bool IsFirstSelect(int index)
        {
            return SelectedIndexSort.Count > 0 && SelectedIndexSort[0] == index;
        }

        private void OnPrePlant(int selectedId, long bizId, long seedId)
        {
            if (pageType == EPageType.Seed)
            {
                if (curPlantBox != null && curPlantBox.GetPlantData(selectedId) != null) return;

                var slotObj = plantLst.GetChildAt(selectedId);

                var itemIcon = plantGODic[slotObj]["plant"] as ComItemIcon;
                if (bizId != 0)
                {
                    var itemCfg = Mc.Tables.TbItemConfig.GetOrDefault(bizId);
                    if (itemCfg != null)
                    {
                        itemIcon.SetTepmlateData(itemCfg);
                        itemIcon.SetIconBgVisible(false);
                    }
                    else
                    {
                        itemIcon.SetEmpty();
                        itemIcon.SetIconBgVisible(false);
                    }
                }
                else
                {
                    itemIcon.SetEmpty();
                    itemIcon.SetIconBgVisible(false);
                }
            }
        }

        private void OnCancelPrePlant(int slot, long instanceId)
        {
            if (pageType == EPageType.Seed)
            {
                if (curPlantBox != null && curPlantBox.GetPlantData(slot) != null) return;

                var slotObj = plantLst.GetChildAt(slot);

                var itemIcon = plantGODic[slotObj]["plant"] as ComItemIcon;
                itemIcon.SetEmpty();
                itemIcon.SetIconBgVisible(false);
            }
        }

        /// <summary>
        /// 模型被点中时的处理 处理选中
        /// </summary>
        /// <param name="slot"></param>
        /// <param name="pageType"></param>
        private void OnPlantBoxModelSelectedSlot(int slot, int pageType)
        {
            if (curPlantBox == null || plantLst == null) return;
            var plant = curPlantBox.GetPlantData(slot);
            if (plant == null)
            {
                if (pageType == (int)this.pageType)
                {
                    switch (this.pageType)
                    {
                        case EPageType.Manure:
                            //此区域无农作物，无法施肥
                            Mc.MsgTips.ShowRealtimeWeakTip(23109);
                            break;
                        case EPageType.Seed:
                            {
                                var btn = plantLst.GetChildAt(slot).asButton;
                                if (!btn.selected)
                                {
                                    plantLst.selectedIndex = slot;
                                    Mc.Msg.FireMsg(EventDefine.PlantPageSelectedSlot, plantLst.selectedIndex);
                                }
                            }
                            break;
                    }
                }
            }
            else
            {
                if (pageType == (int)this.pageType)
                {
                    switch (this.pageType)
                    {
                        case EPageType.Manure:
                            {
                                var btn = plantLst.GetChildAt(slot).asButton;
                                if (!plant.CanManure())
                                {
                                    //此区域的农作物受肥量已达上限，无法施肥
                                    Mc.MsgTips.ShowRealtimeWeakTip(23110);
                                    break;
                                }

                                if (!btn.selected)
                                {
                                    plantLst.AddSelection(slot, false);
                                }
                                else
                                {
                                    plantLst.RemoveSelection(slot);
                                }
                                Mc.Msg.FireMsg(EventDefine.ManurePageSelectedSlot, slot);
                            }
                            break;
                    }
                }
            }
        }

        public void SetLastSelectedIndex(int curIndex)
        {
            lastSelectedIndex = curIndex;
        }

        public void Release()
        {
            Mc.Msg.RemoveListener<int, int>(EventDefine.PlantBoxModelSelectedSlot, OnPlantBoxModelSelectedSlot);
            Mc.Msg.RemoveListener<int, long, long>(EventDefine.PrePlant, OnPrePlant);
            Mc.Msg.RemoveListener<int, long>(EventDefine.CancelPrePlant, OnCancelPrePlant);
        }

        public List<int> GetSelected()
        {
            selectedIdxes.Clear();
            if (plantLst == null) return selectedIdxes;
            return plantLst.GetSelection(selectedIdxes);
        }

        public void RemoveAllSelected()
        {
            plantLst.ClearSelection();
            SelectedIndexSort.Clear();
        }
        
        public void RemoveIndex(int index)
        {
            if (SelectedIndexSort[0] == index)
            {
                RemoveAllSelected();
                return;
            }
            plantLst.RemoveSelection(index);
            GetSelected();
            SelectedIndexSort.Remove(index);
        }
    }
}
