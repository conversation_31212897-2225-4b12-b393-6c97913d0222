using FairyGUI;
using System;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Plant;
using WizardGames.Soc.SocClient.Ui.Utils;

namespace WizardGames.Soc.SocClient.Ui
{
    public class UiPlantDefaultPage : UiPlantBasePage
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(UiPlantDefaultPage));
        public override EPageType PageType => EPageType.Default;
        
        private GButton seedBtn;
        private GButton manureBtn;
        private ComCustomButton waterBtn;
        private GButton harvestBtn;

        private UiPlantBox2D uiPlantBox2D;
        private PlantBoxData curPlantBox;
    
        public static UiPlantBasePage Create(GComponent com,UiPlantOperationSubPanel uiPlantOperationSubPanel)
        {
            UiPlantDefaultPage rt = new UiPlantDefaultPage();
            rt.Init(com,uiPlantOperationSubPanel);
            return rt;
        }
        protected override void OnInit(GComponent com,UiPlantOperationSubPanel uiPlantOperationSubPanel)
        {
            base.OnInit(com,uiPlantOperationSubPanel);
            seedBtn = com.GetChild("seed_btn").asButton;
            seedBtn.onClick.Set(OnClickSeed);
            manureBtn = com.GetChild("manure_btn").asButton;
            manureBtn.onClick.Set(OnClickManure);
            waterBtn = com.GetChild("water_btn") as ComCustomButton;
            waterBtn.clickCD = 2f;
            waterBtn.onClick.Set(OnClickWater);
            harvestBtn = com.GetChild("harvest_btn").asButton;
            harvestBtn.onClick.Set(OnClickHarvest);
            
            GComponent plantBoxCom = com.GetChild("plant_box_com").asCom;
            uiPlantBox2D = UiPlantBox2D.Create(plantBoxCom,uiPlantOperationSubPanel);
        }

        protected override void OnEnable(object data)
        {
            base.OnEnable(data);
            curPlantBox = data as PlantBoxData;
        }

        protected override void OnDispose()
        {
            base.OnDispose();
            uiPlantBox2D?.Release();
        }

        protected override void OnRefresh()
        {
            base.OnRefresh();
            CheckBtnCanUse(seedBtn, curPlantBox != null && curPlantBox.CanSeed());
            CheckBtnCanUse(manureBtn, curPlantBox != null && curPlantBox.CanManure());
            CheckBtnCanUse(waterBtn.Btn, curPlantBox != null && curPlantBox.CanWater());
            SafeUtil.SafeSetVisible(harvestBtn, curPlantBox != null && curPlantBox.CanHarvest());
            uiPlantBox2D?.Refresh(curPlantBox);
        }
        
        private void CheckBtnCanUse(GButton btn, bool canUse)
        {
            Controller controller = btn.GetController("btnState");
            controller.selectedPage = canUse ? "canUse" : "canNotUse";
        }
        
        private void OnClickSeed()
        {
            if (curPlantBox.CanSeed(true))
            {
                uiPlantOperationSubPanel.ChangePage(EPageType.Seed);
            }
            Mc.Audio.PlayAudioEvent(null, "UI_Click_01");
        }

        private void OnClickManure()
        {        
            if (curPlantBox.CanManure(true))
            {
                uiPlantOperationSubPanel.ChangePage(EPageType.Manure);
            }
            Mc.Audio.PlayAudioEvent(null, "UI_Click_01");
        }

        private void OnClickWater()
        {
            if (curPlantBox.CanWater(true))
            {
                curPlantBox.RequestWater();
            }
            Mc.Audio.PlayAudioEvent(null, "UI_Click_01");
        }
        
        private void OnClickHarvest()
        {
            if (curPlantBox != null)
            {
                curPlantBox.RequestHarvest(-1);
            }
            Mc.Audio.PlayAudioEvent(null, "UI_Click_01");
        }
    }
}