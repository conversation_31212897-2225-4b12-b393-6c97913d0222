#if STANDALONE_REAL_PC
using System;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.SocClient.Manager;

namespace WizardGames.Soc.SocClient.Ui
{
    public partial class UiHudWeaponChoose
    {
        /// <summary>
        /// PC 重新排序用到的临时列表
        /// </summary>
        private List<BaseItemNode> padded = new (8);
        private List<BaseItemNode> reordered = new(8);

        private void RefreshBoardState()
        {
            if (null == elemWeapon || null == elemWeapon.statusTarget) return;
            Vector2 globalPos = elemWeapon.GetWeaponChooseBoardGlobalPos();
            comBoard.position = comRoot.GlobalToLocal(globalPos);
            ctrlDropBarStyle.SetSelectedIndex(elemWeapon.IsTabLeft? 0: 1);
            if (elemWeapon.IsTabLeft)
            {
                triggerAutoChooseX = comDragTip.LocalToStage(Vector2.right * comDragTip.width).x;
            }
            else
            {
                triggerAutoChooseX = comDragTip.LocalToStage(Vector2.zero).x;
            }
        }

        private List<BaseItemNode> GetRenderData()
        {
            return reordered;
        }

        /// <summary>
        /// PC对渲染数据重新排序
        /// 从下往上，从左往右
        /// </summary>
        private void ItemsReSort()
        {
            minItemCount = Mathf.Max(minItemCount, 6);
            var columnCount = 2;
            int itemCount = Mathf.Max(curItems.Count, minItemCount);
            int rowCount = (itemCount + columnCount - 1) / columnCount;

            // 填充空数据
            padded.Clear();
            for (int i = 0; i < curItems.Count; i++)
            {
                padded.Add(curItems[i]);
            }
       
            while (padded.Count < itemCount)
            {
                padded.Add(null);  // null 表示空项，占位
            }
           

            // 重新排布
            reordered.Clear();
            for (int row = rowCount - 1; row >= 0; row--) // 从底部行开始
            {
                for (int col = 0; col < columnCount; col++)
                {
                    int index = row * columnCount + col;
                    if (index < padded.Count)
                    {
                        reordered.Add(padded[index]);
                    }
                }
            }
        }

        private void OnHudWeaponIconDragStart_Plaform()
        {
            var elemBlet = UiHudElemShortcuts.GetElemInst();
            if (null != elemBlet && elemBlet.IsElemEnable)
            {
                elemBlet.Belt.SetShowDragTitle(false);
            }
        }

        private void OnHudWeaponIconDragEnd_Plaform()
        {
            var elemBlet = UiHudElemShortcuts.GetElemInst();
            if (null != elemBlet && elemBlet.IsElemEnable)
            {
                elemBlet.Belt.SetShowDragTitle(true);
            }
        }

        public static UiHudWeaponChoose Open(bool isDrag = false)
        {
            openWithDragState = isDrag;
            var win = Mc.Ui.OpenWindowT<UiHudWeaponChoose>("UiHudWeaponChoose");
            if (null != win)
            {
                win.IsInDragState = isDrag;
            }
            return win;
        }

        public static void Hide()
        {
            var win = Mc.Ui.GetWindow("UiHudWeaponChoose");
            if (null != win)
            {
                win.HideSelf();
            }
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            if (!UiHud.IsInEdit)
            {
                elemWeapon = UiHudElemWeapons.GetElem();
                if (null != elemWeapon)
                {
                    elemWeapon.SetChooseStyle(true);
                    elemWeapon.SetWeaponChooseState(true, openWithDragState ? -1 : 0);
                }

                // 快捷栏需要一并打开
                var elemBlet = UiHudElemShortcuts.GetElemInst();
                if (null != elemBlet && elemBlet.IsElemEnable)
                {
                    elemBlet.Belt.SetChooseMenuState(true);
                }
            }
            RefreshBoardState();
            RefreshList();
            IsChooseVisible = true;
            Mc.Msg.AddListener(EventDefine.OnHudMakeFullScreen, OnMakeFullScreen);
        }

        protected override void OnDisable()
        {
            openWithDragState = false;
            IsChooseVisible = false;
            CurChooseIndex = -1;
            base.OnDisable();
            curItems.Clear();
            if (!UiHud.IsInEdit && null != elemWeapon)
            {
                elemWeapon.SetWeaponChooseState(false);
                elemWeapon.SetChooseStyle(false);
                elemWeapon.Refresh();

                // 快捷栏需要一并关闭
                var elemBlet = UiHudElemShortcuts.GetElemInst();
                if (null != elemBlet && elemBlet.IsElemEnable)
                {
                    elemBlet.Belt.SetChooseMenuState(false);
                }
            }
            for (int i = 0; i < listIcons.numChildren; ++i)
            {
                var comChild = listIcons.GetChildAt(i)?.asCom;
                if (null == comChild) continue;
                var icon = comChild.GetChild("icon") as ComItemIcon;
                if (null == icon) continue;
                icon.SetHighlight(false);
            }
            comDropbar.visible = false;
            listIcons.numItems = 0;
            Mc.Msg.RemoveListener(EventDefine.OnHudMakeFullScreen, OnMakeFullScreen);
        }

        private void OnIconAcceptDrag_Palform(ComBaseIcon icon, ItemDragInfo info)
        {
            if (null == info || null == info.item || icon == UiItemIconDragDrop.CurDragIcon)
            {
                return;
            }

            var dropItem = info.item;
            bool isWeaponItemNode = dropItem is WeaponItemNode;
            // 如果不是远程武器, 不处理
            if (!isWeaponItemNode)
            {
                Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.ItemCannotPutInWeapon);
            }
        }
    }
}
#endif