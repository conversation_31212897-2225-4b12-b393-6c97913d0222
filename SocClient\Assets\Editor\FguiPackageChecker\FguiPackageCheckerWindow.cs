using System;
using UnityEditor;
using UnityEngine;
using WizardGames.Soc.SocClient.Manager;

namespace FguiPackageChecker
{
    public class FguiPackageCheckerWindow : EditorWindow
    {
        /// <summary>
        /// ui初始化
        /// </summary>
        private bool uiInit = false;

        /// <summary>
        /// 新增一个变量按钮
        /// </summary>
        private GUIStyle btnStyle;

        /// <summary>
        /// 标题风格
        /// </summary>
        private GUIStyle midLabelGuiStyle;

        /// <summary>
        /// 每个控件的Label
        /// </summary>
        private GUIStyle packageNameLabelStyle;

        /// <summary>
        /// 每个控件的Label
        /// </summary>
        private GUIStyle packageDependenciesLabelStyle;

        /// <summary>
        /// 无错误引用 √ 的GUISyle
        /// </summary>
        private GUIStyle trueFlagGuiStyle;

        /// <summary>
        /// 有错误引用 × 的GUISyle;
        /// </summary>
        private GUIStyle falseFlagGuiStyle;

        /// <summary>
        /// 每个控件的Label
        /// </summary>
        private GUIStyle resultLabelStyle;

        /// <summary>
        /// 上一帧布局
        /// </summary>
        private Rect lastPosition = new Rect();

        /// <summary>
        /// 编辑器区宽度
        /// </summary>
        private int areaWidth = 400;

        /// <summary>
        /// 滑动条位置
        /// </summary>
        private Vector2 uiPackageTableScrollVec;

        /// <summary>
        /// 滑动条位置
        /// </summary>
        private Vector2 uiPackageResultScrollVec;

        /// <summary>
        /// 包体检测
        /// </summary>
        private FguiPackageChecker fguiPackageChecker;

        /// <summary>
        /// SocUi的路径
        /// </summary>
        private string socUiPath;


        [MenuItem("程序工具/UI/FGUI运行时用具")]
        public static void ShowFGUIRuntimeTool()
        {
            if (null == Mc.Ui)
            {
                EditorUtility.DisplayDialog("提示", "请运行Editor后使用", "确定");
                return;
            }
            Mc.Ui.OpenWindow("UiRuntimeTools");
        }


        [MenuItem("程序工具/UI/FGui包体循环引用检测工具")]
        public static void ShowWindow()
        {
            var window = GetWindow<FguiPackageCheckerWindow>();
            window.titleContent = new GUIContent("包体循环引用检测工具");
        }


        private void OnGUI()
        {
            if (!uiInit)
            {
                uiInit = true;

                UiInit();

                socUiPath = EditorPrefs.GetString("socUiPath");
                fguiPackageChecker = new FguiPackageChecker();
                if (socUiPath != String.Empty)
                {
                    fguiPackageChecker.LoadAllPackage(socUiPath);
                }
            }

            // 检查布局是否发生变化
            if (position != lastPosition)
            {
                lastPosition = position;
                areaWidth = (int) position.width;
            }

            OnSelectPathGUI("SocUi地址：", socUiPath, "设置SocUi地址");

            GUILayout.Space(3);

            using (new GUILayout.VerticalScope(GUI.skin.box))
            {
                if (GUILayout.Button("检查", GUILayout.ExpandWidth(true), GUILayout.Height(50)))
                {
                    if (socUiPath != String.Empty && fguiPackageChecker != null)
                    {
                        fguiPackageChecker.LoadAllPackage(socUiPath);
                    }
                    else
                    {
                        if (EditorUtility.DisplayDialog("提示", "SocUi路径不能为空，请填写正确路径", "确定"))
                        {
                            AssetDatabase.Refresh();
                        }
                    }

                    AssetDatabase.Refresh();
                }
            }

            if (socUiPath == string.Empty || fguiPackageChecker == null)
            {
                // 未初始化
                return;
            }

            GUILayout.Space(10);

            using (new GUILayout.HorizontalScope())
            {
                using (new GUILayout.VerticalScope(GUILayout.Width(areaWidth / 2 - 10)))
                {
                    GUILayout.Label("包体依赖", midLabelGuiStyle, GUILayout.ExpandWidth(true));
                    using (new GUILayout.VerticalScope(GUI.skin.box))
                    {
                        using (var uiPackageScroll = new GUILayout.ScrollViewScope(uiPackageTableScrollVec, GUILayout.ExpandWidth(true), GUILayout.ExpandHeight(true)))
                        {
                            uiPackageTableScrollVec = uiPackageScroll.scrollPosition;

                            foreach (var uiPackageDependenciesPair in fguiPackageChecker.UiPackageTableDependenciesDic)
                            {
                                OnePackageDependenciesGui(uiPackageDependenciesPair.Key, uiPackageDependenciesPair.Value);
                            }
                        }
                    }
                }

                GUILayout.Space(3);

                using (new GUILayout.VerticalScope(GUILayout.Width(areaWidth / 2 - 10)))
                {
                    GUILayout.Label("通用包", midLabelGuiStyle, GUILayout.ExpandWidth(true));
                    using (new GUILayout.VerticalScope(GUI.skin.box))
                    {
                        foreach (var uiPackageDependenciesPair in fguiPackageChecker.UiPackageCommonDependenciesDic)
                        {
                            OnePackageDependenciesGui(uiPackageDependenciesPair.Key, uiPackageDependenciesPair.Value);
                        }
                    }

                    GUILayout.Space(10);

                    GUILayout.Label("依赖结果", midLabelGuiStyle, GUILayout.ExpandWidth(true));
                    using (new GUILayout.VerticalScope(GUI.skin.box, GUILayout.ExpandHeight(true)))
                    {
                        using (var uiPackageScroll = new GUILayout.ScrollViewScope(uiPackageResultScrollVec, GUILayout.ExpandWidth(false), GUILayout.ExpandHeight(true)))
                        {
                            uiPackageResultScrollVec = uiPackageScroll.scrollPosition;
                            var dependencyResultDic = fguiPackageChecker.DependencyResultDic;
                            if (dependencyResultDic != null && dependencyResultDic.Count > 0)
                            {
                                foreach (var dependencyResultPair in dependencyResultDic)
                                {
                                    var resultText = string.Empty;
                                    var resultList = dependencyResultPair.Value;
                                    if (resultList != null && resultList.Count > 0)
                                    {
                                        for (var i = 0; i < resultList.Count; i++)
                                        {
                                            resultText += resultList[i].dependencyResultStr + Environment.NewLine;
                                        }
                                    }

                                    EditorGUILayout.TextArea(resultText, resultLabelStyle);
                                    GUILayout.Space(15);
                                }
                            }
                            else
                            {
                                GUILayout.Label("不存在引用异常的情况");
                            }
                        }
                    }
                }
            }
        }


        /// <summary>
        /// 初始化
        /// </summary>
        private void UiInit()
        {
            btnStyle = new GUIStyle(EditorStyles.miniButtonMid);
            btnStyle.fontSize = 20;

            midLabelGuiStyle = new GUIStyle(EditorStyles.boldLabel);
            midLabelGuiStyle.normal.textColor = Color.yellow;
            midLabelGuiStyle.alignment = TextAnchor.MiddleCenter;
            midLabelGuiStyle.fontSize = 25;

            packageNameLabelStyle = new GUIStyle(EditorStyles.boldLabel);
            packageNameLabelStyle.fontSize = 15;
            packageNameLabelStyle.alignment = TextAnchor.MiddleRight;
            packageNameLabelStyle.fontStyle = FontStyle.Bold;

            packageDependenciesLabelStyle = new GUIStyle(EditorStyles.boldLabel);
            packageDependenciesLabelStyle.fontSize = 13;
            packageDependenciesLabelStyle.alignment = TextAnchor.MiddleLeft;
            packageDependenciesLabelStyle.richText = true;

            resultLabelStyle = new GUIStyle(EditorStyles.boldLabel);
            resultLabelStyle.fontSize = 15;
            resultLabelStyle.alignment = TextAnchor.MiddleLeft;

            trueFlagGuiStyle = new GUIStyle(EditorStyles.miniLabel);
            trueFlagGuiStyle.fontSize = 15;
            trueFlagGuiStyle.normal.textColor = Color.green;
            trueFlagGuiStyle.alignment = TextAnchor.MiddleLeft;
            trueFlagGuiStyle.fontStyle = FontStyle.Bold;

            falseFlagGuiStyle = new GUIStyle(EditorStyles.miniLabel);
            falseFlagGuiStyle.fontSize = 15;
            falseFlagGuiStyle.normal.textColor = Color.red;
            falseFlagGuiStyle.alignment = TextAnchor.MiddleLeft;
            falseFlagGuiStyle.fontStyle = FontStyle.Bold;
        }


        /// <summary>
        /// 地址选择框
        /// </summary>
        /// <param name="lab"></param>
        /// <param name="path"></param>
        /// <param name="title"></param>
        private void OnSelectPathGUI(string lab, string path, string title)
        {
            GUILayout.BeginHorizontal();
            {
                GUILayout.Label(lab, GUILayout.Width(100));

                if (path != "")
                {
                    GUILayout.Label(path);
                }

                if (GUILayout.Button("浏览", GUILayout.Width(60)))
                {
                    var dirPath = EditorUtility.OpenFolderPanel(title, path, "");

                    if (dirPath.Length != 0)
                    {
                        if (string.IsNullOrEmpty(dirPath))
                        {
                            Debug.Log("目标地址为空");
                            return;
                        }

                        EditorPrefs.SetString("socUiPath", dirPath);
                        socUiPath = EditorPrefs.GetString("socUiPath");
                    }
                }
            }
            GUILayout.EndHorizontal();
        }


        /// <summary>
        /// 绘制一条依赖关系
        /// </summary>
        /// <param name="nameStr"></param>
        /// <param name="dependenciesStr"></param>
        private void OnePackageDependenciesGui(string nameStr, string dependenciesStr)
        {
            using (new GUILayout.VerticalScope(GUI.skin.box))
            {
                GUILayout.BeginHorizontal(GUILayout.Height(30));
                {
                    GUILayout.Width(50);
                    GUILayout.Label(nameStr + " 依赖 ：", packageNameLabelStyle, GUILayout.Width(300));
                    GUILayout.Space(5);
                    GUILayout.Label(dependenciesStr, packageDependenciesLabelStyle, GUILayout.ExpandWidth(true));
                    GUILayout.Width(50);

                    if (fguiPackageChecker.UiPackageResultDic.ContainsKey(nameStr))
                    {
                        FalseFlag();
                    }
                    else
                    {
                        TrueFlag();
                    }
                }
                GUILayout.EndHorizontal();
            }
        }


        /// <summary>
        /// 正确标记
        /// </summary>
        private void TrueFlag()
        {
            EditorGUILayout.LabelField(new GUIContent("√"), trueFlagGuiStyle, GUILayout.Width(55), GUILayout.ExpandWidth(false), GUILayout.Height(30));
        }


        /// <summary>
        /// 错误标记
        /// </summary>
        private void FalseFlag()
        {
            EditorGUILayout.LabelField(new GUIContent("X"), falseFlagGuiStyle, GUILayout.Width(55), GUILayout.ExpandWidth(false), GUILayout.Height(30));
        }


        private void OnDestroy()
        {
            fguiPackageChecker.Clear();
        }
    }
}
