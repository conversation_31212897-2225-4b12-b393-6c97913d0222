using UnityEngine;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Ui;

namespace WizardGames.Soc.SocClient.Control
{
    /// <summary>
    /// 移动控制逻辑类
    /// </summary>
    public class MoveControl : IActionExecutor
    {
        public static SocLogger logger = LogHelper.GetLogger(typeof(MoveControl));
        /// <summary>
        /// 上一次传入的cmd序列的记录。
        /// 由于每次传入的cmd可能是同一个，需要检查下seq。记录一下
        /// </summary>
        private int lastRecordCmdSequence = 0;
        /// <summary>
        /// 上一次记录的cmd是否前进
        /// </summary>
        private bool lastRecordCmdMoveForward = false;
        /// <summary>
        /// 上一次记录的cmd是否后退
        /// </summary>
        private bool lastRecordCmdMoveBackward = false;
        /// <summary>
        /// 上一次记录的cmd是否左移
        /// </summary>
        private bool lastRecordCmdMoveLeft = false;
        /// <summary>
        /// 上一次记录的cmd是否右移
        /// </summary>
        private bool lastRecordCmdMoveRight = false;
        
        /// <summary>
        /// 上一次记录的cmd是否上移
        /// </summary>
        private bool lastRecordCmdUp = false;
        
        /// <summary>
        /// 上一次记录的cmd是否下移
        /// </summary>
        private bool lastRecordCmdDown = false;

        /// <summary>
        /// Joystick输入值
        /// </summary>
        private Vector2 joystickInput = Vector2.zero;
        /// <summary>
        /// Joystick输入角度
        /// </summary>
        private float inputAngle;
        
        /// <summary>
        /// Joystick输入百分比
        /// </summary>
        private float joystickPercent;

        /// <summary>
        /// 输入垂直方向 按键W或S 摇杆前或后
        /// </summary>
        private ActionName inputVerticalDirect;
        
        /// <summary>
        /// 输入水平方向 按键A或D 摇杆前或后
        /// </summary>
        private ActionName inputHorizontalDirect;
        
        /// <summary>
        /// 移动状态
        /// </summary>
        private MoveState moveState;

        /// <summary>
        /// touch是否拖动到原点
        /// </summary>
        private bool isOrigin;

        public float JoyStickYawOffset { get; set; } = 0;//移动朝向偏移。比如：tp有个功能，砍树时玩家朝A方向，此时会让玩家转向朝着树方向B，通过设置此变量，玩家还是可以朝A行走而不是朝B

        public bool PreExecute(ActionName actionName, UserCmd userCmd, bool isKeyResponse)
        {
            return false;
        }
        public void Execute(ActionName actionName, UserCmd userCmd, bool isKeyResponse)
        {
            if (isKeyResponse)
            {
                //按键响应逻辑
                //pc，是run为按键并且不是同向同时按，MoveForward\Back\Left\Right一定有
                //idle,是没有run，但可能有按键（同向同时按）也就是MoveFor\back\left\right有数值，也可能没有
                switch (actionName)
                {
                    case ActionName.MoveForward:
                        userCmd.MoveForward = true;
                        break;
                    case ActionName.MoveBackward:
                        userCmd.MoveBackward = true;
                        break;
                    case ActionName.MoveLeft:
                        userCmd.MoveLeft = true;
                        break;
                    case ActionName.MoveRight:
                        userCmd.MoveRight = true;
                        break;
                    case ActionName.Up:
                        userCmd.Up = true;
                        break;
                    case ActionName.Down:
                        userCmd.Down = true;
                        break;
                }
            }

            userCmd.Run = (userCmd.MoveForward || userCmd.MoveBackward || userCmd.MoveLeft || userCmd.MoveRight) &&
                          !(userCmd.MoveForward && userCmd.MoveBackward && !userCmd.MoveLeft && !userCmd.MoveRight) &&
                          !(!userCmd.MoveForward && !userCmd.MoveBackward && userCmd.MoveLeft && userCmd.MoveRight) &&
                          !(userCmd.MoveForward && userCmd.MoveBackward && userCmd.MoveLeft && userCmd.MoveRight);
            userCmd.Idle = !userCmd.Run;
            userCmd.JoystickYaw = CalcMoveYaw(userCmd);
            userCmd.JoystickPercent = userCmd.Run ? 1 : 0;

            //Debug.LogError(userCmd.Run+","+userCmd.Idle);

            //移动轮盘有数值时才
            //run是在内区，MoveForward\Back\Left\Right一定有
            //idle是在镂空区或者在原点或者手离开遥感，同时MoveForward\Back\Left\Right一定没有
            if (joystickInput != Vector2.zero)
            {
                switch (inputVerticalDirect)
                {
                    case ActionName.MoveForward:
                        userCmd.MoveForward = true;
                        break;
                    case ActionName.MoveBackward:
                        userCmd.MoveBackward = true;
                        break;
                    case ActionName.MoveLeft:
                        userCmd.MoveLeft = true;
                        break;
                    case ActionName.MoveRight:
                        userCmd.MoveRight = true;
                        break;
                    case ActionName.Up:
                        userCmd.Up = true;
                        break;
                    case ActionName.Down:
                        userCmd.Down = true;
                        break;
                }
                
                switch (inputHorizontalDirect)
                {
                    case ActionName.MoveForward:
                        userCmd.MoveForward = true;
                        break;
                    case ActionName.MoveBackward:
                        userCmd.MoveBackward = true;
                        break;
                    case ActionName.MoveLeft:
                        userCmd.MoveLeft = true;
                        break;
                    case ActionName.MoveRight:
                        userCmd.MoveRight = true;
                        break;
                    case ActionName.Up:
                        userCmd.Up = true;
                        break;
                    case ActionName.Down:
                        userCmd.Down = true;
                        break;
                }

                userCmd.Idle = moveState == MoveState.Idle;
                userCmd.Run = moveState is MoveState.Run or MoveState.Sprint;
                userCmd.Sprint = moveState == MoveState.Sprint;
                userCmd.JoystickYaw = inputAngle;
                userCmd.JoystickPercent = joystickPercent;
            }

            userCmd.JoystickYaw += this.JoyStickYawOffset;
            //检查是否连续移动
            if (userCmd.Sequence - lastRecordCmdSequence == 1)
            {
                userCmd.MoveForwardContinuous = lastRecordCmdMoveForward && userCmd.MoveForward;
                userCmd.MoveBackwardContinuous = lastRecordCmdMoveBackward && userCmd.MoveBackward;
                userCmd.MoveLeftContinuous = lastRecordCmdMoveLeft && userCmd.MoveLeft;
                userCmd.MoveRightContinuous = lastRecordCmdMoveRight && userCmd.MoveRight;
                userCmd.UpContinue = lastRecordCmdUp && userCmd.Up;
                userCmd.DownContinue = lastRecordCmdDown && userCmd.Down;
            }

            // Debug.LogError(userCmd.Sequence+ " " + userCmd.MoveForward + " "+ userCmd.Sprint);
            //更新本次动作执行
            lastRecordCmdSequence = userCmd.Sequence;
            lastRecordCmdMoveForward = userCmd.MoveForward;
            lastRecordCmdMoveBackward = userCmd.MoveBackward;
            lastRecordCmdMoveLeft = userCmd.MoveLeft;
            lastRecordCmdMoveRight = userCmd.MoveRight;
            lastRecordCmdUp = userCmd.Up;
            lastRecordCmdDown = userCmd.Down;
        }


        public void OnActionActiveChanged(ActionName actionName, bool active)
        {
        }
#if STANDALONE_REAL_PC || UNITY_EDITOR
        public bool UiJoystickInput = false;
#endif
        public void UpdateJoystickData(Vector2 input, float angle, ActionName direct,  ActionName hDirect, MoveState state, bool origin, float percent)
        {
            // logger.ErrorFormat("UpdateJoystickData: frame: {0} input: {1}, angle: {2}, direct: {3}, hDirect: {4}, state: {5}, origin: {6}, percent: {7}",
                // Time.frameCount, input, angle, direct, hDirect, state, origin, percent);
            // 自由相机模式，拦截MoveControl的输入，并传送给 FreeCamera
            if (Mc.Photo != null && Mc.Photo.IsFreeMode())
            {
                Mc.Photo.CameraTranslate(input, state);
                
                joystickInput = Vector2.zero;
                inputAngle = 0;
                inputVerticalDirect = ActionName.MoveForward;
                inputHorizontalDirect = ActionName.MoveLeft;
                moveState = MoveState.Idle;
                isOrigin = true;
                joystickPercent = 0;
                
                return;
            }
            
            joystickInput = input;
            inputAngle = angle;
            inputVerticalDirect = direct;
            inputHorizontalDirect = hDirect;
            moveState = state;
            isOrigin = origin;
            joystickPercent = percent;
        }

        private float CalcMoveYaw(UserCmd cmd)
        {
            var wantedDirection = new Vector2();
            if (cmd.MoveForward)
                wantedDirection.y++;
            if (cmd.MoveBackward)
                wantedDirection.y--;
            if (cmd.MoveRight)
                wantedDirection.x++;
            if (cmd.MoveLeft)
                wantedDirection.x--;

            var angle = Vector2.Angle(wantedDirection, Vector2.up);
            if (wantedDirection.x < 0)
            {
                angle = 360 - angle;
            }
            return angle;
        }
    }
}