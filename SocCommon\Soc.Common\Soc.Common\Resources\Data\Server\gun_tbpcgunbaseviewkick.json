{"hash": 1287817900, "data": [{"baseviewkickid": 1, "baseviewkicktype": 1, "rotation": "pitch", "vkvalues": [0.76, 1.18, 0.76, 0.76, 0.76, 0.67, 0.84, 1.18, 0.84, 1.09, 1.01, 0.76, 1.09, 1.09, 1.18, 1.18, 1.34, 1.43, 1.09, 1.09, 1.34, 1.01, 1.01, 1.09, 1.26, 1.18, 1.18, 0.92, 1.01, 0.92, 0.5, 1.01, 1.09, 0.59, 0.76, 1.18, 0.84, 0.67, 1.26, 1.01], "uptime": 0.1, "toppausetime": 0, "downtime": 0.3, "curvePath": "Weapon/Curve/ViewKickCurve_AR.asset"}, {"baseviewkickid": 2, "baseviewkicktype": 1, "rotation": "yaw", "vkvalues": [0.069, 0.138, -0.092, 0.092, -0.046, -0.23, -0.092, -0.23, -0.138, -0.644, -0.046, -0.506, -0.092, 0.276, -0.046, 0.23, -0.184, -0.046, -0.092, 0.092, -0.23, -0.276, -0.092, 0.138, 0.138, -0.276, -0.276, 0.184, 0, -0.184, -0.368, 0.046, 0.322, 0.322, 0, 0.092, -0.046, 0.322, 0.184, -0.184], "uptime": 0.1, "toppausetime": 0, "downtime": 0.3, "curvePath": "Weapon/Curve/ViewKickCurve_AR.asset"}, {"baseviewkickid": 3, "baseviewkicktype": 3, "rotation": "pitch", "vkvalues": [0.864, 1.08, 0.792, 0.792, 0.864, 0.864, 0.576, 0.648, 0.576, 0.648, 0.36, 0.792, 0.72, 0.576, 0.648, 0.576, 0.648, 0.432, 0.72, 0.576, 0.576, 0.576, 0.432, 0.864, 0.72, 0.72, 0.864, 0.864, 0.864, 0.792, 0.576, 0.864, 0.648, 0.72, 0.72, 0.648, 0.864, 0.576, 0.648, 0.72], "uptime": 0.03, "toppausetime": 0, "downtime": 0.15, "curvePath": "Weapon/Curve/ViewKickCurve_SMG.asset"}, {"baseviewkickid": 4, "baseviewkicktype": 3, "rotation": "yaw", "vkvalues": [-0.132, 0.088, 0.088, 0.264, 0.22, -0.22, -0.22, -0.308, -0.176, -0.264, -0.132, 0.132, 0.264, 0.176, 0.22, 0.22, 0.22, 0.22, -0.044, -0.044, -0.088, -0.572, -0.088, -0.176, -0.22, 0, -0.264, 0.22, 0.308, 0.22, 0.088, 0.22, 0.132, 0.22, 0.176, 0.22, 0.132, 0.176, 0.132, 0.22], "uptime": 0.03, "toppausetime": 0, "downtime": 0.15, "curvePath": "Weapon/Curve/ViewKickCurve_SMG.asset"}, {"baseviewkickid": 5, "baseviewkicktype": 4, "rotation": "pitch", "vkvalues": [1.03, 1.29, 1.16, 0.9, 0.13, 0, 0.65, 0.9, 0.77, 1.16, 0.65, 0.77, 1.03, 0.77, 0.77, 0.9, 0.77, 1.68, 0.52, 1.03, 1.16, 1.16, 0.9, 0.9, 0.77, 1.03, 0.52, 0.77, 0.77, 1.29, 0.9, 1.03, 1.16, 1.68, 1.42, 0.77, 0.77, 1.16, 1.16, 0.77, 1.16, 1.16, 0.65, 1.03, 0.77, 1.16, 1.16, 1.03, 1.16, 1.29, 1.16, 0.52, 0.65, 0, 0.39, 1.03, 0.65, 0.52, -0.13, 0.13, 0.64, 1.42, 1.03, 0.77, 0.9, 1.42, 1.03, 1.29, 0.9, 1.16, 0, 0.13, 0.64, 1.16, 1.68, 0.9, 1.03, 1.29, 1.03, 1.16], "uptime": 0.1, "toppausetime": 0, "downtime": 0.3, "curvePath": "Weapon/Curve/ViewKickCurve_AR.asset"}, {"baseviewkickid": 6, "baseviewkicktype": 4, "rotation": "yaw", "vkvalues": [0.52, 0.39, -0.39, -0.52, -1.03, -0.9, -0.52, -0.77, -0.39, -0.13, -0.77, -0.13, 0.13, -0.26, -0.39, 0.26, 0, 0, -0.52, 0, 0.52, -0.13, -0.39, -0.39, -0.77, -0.65, -1.16, -1.29, -1.03, 0, 0.52, 0.26, 0.9, 0.64, 0.39, 0.65, 1.16, 0, -0.52, -0.9, -0.52, 0, -1.03, -0.52, -1.29, 0.13, -1.16, -0.9, 0.26, 0.13, -0.26, 1.16, 1.03, 1.03, 1.03, 0.13, -0.77, -1.42, -1.29, -1.16, -0.65, 0, -1.16, -1.29, -1.03, 0, -0.52, -0.13, -0.9, -0.26, -1.29, -0.9, -1.16, -1.81, 0.52, -0.39, -0.77, -0.26, -0.77, 0.39], "uptime": 0.1, "toppausetime": 0, "downtime": 0.3, "curvePath": "Weapon/Curve/ViewKickCurve_AR.asset"}, {"baseviewkickid": 7, "baseviewkicktype": 5, "rotation": "pitch", "vkvalues": [4.5], "uptime": 0.01, "toppausetime": 0.1, "downtime": 0.4, "curvePath": "Weapon/Curve/ViewKickCurve_SG.asset"}, {"baseviewkickid": 8, "baseviewkicktype": 5, "rotation": "yaw", "vkvalues": [1.5], "uptime": 0.01, "toppausetime": 0.1, "downtime": 0.4, "curvePath": "Weapon/Curve/ViewKickCurve_SG.asset"}, {"baseviewkickid": 9, "baseviewkicktype": 7, "rotation": "pitch", "vkvalues": [5], "uptime": 0.01, "toppausetime": 0.15, "downtime": 0.8, "curvePath": "Weapon/Curve/TestViewKickCurve1.asset"}, {"baseviewkickid": 10, "baseviewkicktype": 7, "rotation": "yaw", "vkvalues": [1.5], "uptime": 0.01, "toppausetime": 0.15, "downtime": 0.8, "curvePath": "Weapon/Curve/TestViewKickCurve1.asset"}, {"baseviewkickid": 11, "baseviewkicktype": 9, "rotation": "pitch", "vkvalues": [1.2], "uptime": 0.03, "toppausetime": 0.05, "downtime": 0.4, "curvePath": "Weapon/Curve/ViewKickCurve_SR.asset"}, {"baseviewkickid": 12, "baseviewkicktype": 9, "rotation": "yaw", "vkvalues": [0.2], "uptime": 0.03, "toppausetime": 0.05, "downtime": 0.4, "curvePath": "Weapon/Curve/ViewKickCurve_SR.asset"}, {"baseviewkickid": 13, "baseviewkicktype": 10, "rotation": "pitch", "vkvalues": [1.28, 1.08, 1.24, 1.08, 1.3, 1.38, 1, 1.14, 1.2, 1.36, 1.2, 0.94, 1.32, 0.88, 1.38], "uptime": 0.1, "toppausetime": 0.05, "downtime": 0.25, "curvePath": "Weapon/Curve/ViewKickCurve_PIS.asset"}, {"baseviewkickid": 14, "baseviewkicktype": 10, "rotation": "yaw", "vkvalues": [0.4, 0.3, 0.12, 0.06, 0.3, 0.18, 0, 0.3, -0.18, 0.6, -0.58, 0.64, 0.04, 0.54, 0.02], "uptime": 0.1, "toppausetime": 0.05, "downtime": 0.25, "curvePath": "Weapon/Curve/ViewKickCurve_PIS.asset"}, {"baseviewkickid": 15, "baseviewkicktype": 11, "rotation": "pitch", "vkvalues": [2], "uptime": 0.01, "toppausetime": 0.05, "downtime": 0.4, "curvePath": "Weapon/Curve/ViewKickCurve_SR.asset"}, {"baseviewkickid": 16, "baseviewkicktype": 11, "rotation": "yaw", "vkvalues": [0.2], "uptime": 0.01, "toppausetime": 0.05, "downtime": 0.4, "curvePath": "Weapon/Curve/ViewKickCurve_SR.asset"}, {"baseviewkickid": 17, "baseviewkicktype": 12, "rotation": "pitch", "vkvalues": [1.16, 0.72, 0.84, 1.28, 0.5, 0.9, 0.8, 1.04, 1.12, 0.84, 0.8, 0.8, 0.72, 1.02, 0.76, 0.6, 0.78, 0.84, 0.88, 0.96, 0.64, 1.02], "uptime": 0.1, "toppausetime": 0.03, "downtime": 0.22, "curvePath": "Weapon/Curve/ViewKickCurve_SAR.asset"}, {"baseviewkickid": 18, "baseviewkicktype": 12, "rotation": "yaw", "vkvalues": [0.2, -0.04, 0.12, -0.28, 0.22, 0.42, 0.36, 0.36, 0.3, 0, 0.18, 0.3, -0.3, -0.18, -0.48, -0.42, 0.18, 0.42, 0.48, 0, 0.34, 0.36], "uptime": 0.1, "toppausetime": 0.03, "downtime": 0.22, "curvePath": "Weapon/Curve/ViewKickCurve_SAR.asset"}, {"baseviewkickid": 19, "baseviewkicktype": 14, "rotation": "pitch", "vkvalues": [0.408, 0.51, 0.918, 1.122, 1.224, 0.816, 1.326, 0.816, 1.122, 1.02, 0.918, 0.816, 0.918, 0.612, 0.714, 0.918, 1.02, 0.918, 0.51, 0.612, 0.816, 0.714, 0.714, 1.122, 0.816, 1.122, 0.612, 1.02, 0.918, 0.714, 1.122, 0.816, 0.816, 0.714, 0.612, 0.918, 1.53, 1.224, 1.02, 1.122], "uptime": 0.1, "toppausetime": 0, "downtime": 0.25, "curvePath": "Weapon/Curve/ViewKickCurve_AR.asset"}, {"baseviewkickid": 20, "baseviewkicktype": 14, "rotation": "yaw", "vkvalues": [0, -0.192, -0.288, -0.192, -0.096, -0.576, -0.288, 0.192, 0.096, 0, -0.192, -0.192, -0.288, -0.096, -0.288, 0.384, 0.384, -0.384, -0.576, 0, 0.576, -0.096, -0.192, -0.672, -0.288, -0.192, -0.288, 0, 0.384, -0.48, -0.384, -0.384, -0.384, -0.288, -0.192, -0.096, 0.288, 0.288, -0.384, -0.576], "uptime": 0.1, "toppausetime": 0, "downtime": 0.25, "curvePath": "Weapon/Curve/ViewKickCurve_AR.asset"}, {"baseviewkickid": 21, "baseviewkicktype": 15, "rotation": "pitch", "vkvalues": [0.48, 0.36, 0.6, 0.54, 1.02, 0.9, 0.72, 0.54, 0.84, 1.08, 0.72, 0.72, 0.48, 0.6, 0.72, 1.08, 0.6, 0.24, 0.84, 0.24, 0.12, 0.6, 0.48, 0.84, 0.24, 0.6, 0.72, 0.24, 0.48, 0.36, 0.48, 0.48, 0.84, 0.72, 0.72, 0.72, 1.08, 0.48, 0.6, 0.72], "uptime": 0.1, "toppausetime": 0, "downtime": 0.2, "curvePath": "Weapon/Curve/ViewKickCurve_SMG.asset"}, {"baseviewkickid": 22, "baseviewkicktype": 15, "rotation": "yaw", "vkvalues": [0, 0.12, 0.12, 0, -0.24, -0.3, -0.12, -0.3, -0.24, -0.3, -0.18, -0.18, -0.48, 0, -0.12, 0.18, 0.18, 0.42, -0.12, -0.36, -0.42, -0.36, -0.3, -0.24, -0.3, -0.12, -0.3, -0.18, -0.06, -0.12, -0.18, -0.18, -0.18, -0.24, 0.12, -0.06, 0.18, -0.3, -0.24, -0.18], "uptime": 0.1, "toppausetime": 0, "downtime": 0.2, "curvePath": "Weapon/Curve/ViewKickCurve_SMG.asset"}, {"baseviewkickid": 23, "baseviewkicktype": 16, "rotation": "pitch", "vkvalues": [0.9, 0.63, 0.45, 0.81, 0.72, 0.63, 0.63, 0.54, 0.63, 0.27, 0.81, 0.81, 0.81, 1.08, 0.81, 0.9, 0.72, 0.63, 0.72, 0.72, 0.9, 0.81, 0.81, 0.81, 0.45, 1.17, 0.9, 0.99, 0.72, 1.44, 0.99, 0.99, 0, 1.08, 1.08, 1.08, 0.9, 0.72, 0.54, 1.08], "uptime": 0.1, "toppausetime": 0, "downtime": 0.25, "curvePath": "Weapon/Curve/ViewKickCurve_SMG.asset"}, {"baseviewkickid": 24, "baseviewkicktype": 16, "rotation": "yaw", "vkvalues": [0.048, 0, -0.144, -0.24, -0.24, -0.24, -0.144, -0.192, 0.192, 0.336, 0.24, -0.336, -0.336, -0.336, -0.384, -0.336, -0.384, 0.144, -0.048, 0.432, -0.288, -0.288, -0.336, -0.288, 0.384, -0.144, -0.144, -0.24, -0.336, -0.288, -0.24, -0.24, -0.576, -0.384, -0.048, -0.096, -0.336, -0.24, -0.48, -0.096], "uptime": 0.1, "toppausetime": 0, "downtime": 0.25, "curvePath": "Weapon/Curve/ViewKickCurve_SMG.asset"}, {"baseviewkickid": 25, "baseviewkicktype": 23, "rotation": "pitch", "vkvalues": [5], "uptime": 0.01, "toppausetime": 0.1, "downtime": 0.4, "curvePath": "Weapon/Curve/ViewKickCurve_SG.asset"}, {"baseviewkickid": 26, "baseviewkicktype": 23, "rotation": "yaw", "vkvalues": [1], "uptime": 0.01, "toppausetime": 0.1, "downtime": 0.4, "curvePath": "Weapon/Curve/ViewKickCurve_SG.asset"}, {"baseviewkickid": 27, "baseviewkicktype": 26, "rotation": "pitch", "vkvalues": [0.81, 1.17, 1.08, 0.81, 0.81, 1.17, 0.9, 0.99, 0.72, 0.81, 1.08, 0.99, 1.26, 0.9, 1.17, 0.81, 0.99, 0.72, 0.81, 0.99, 1.17, 0.99, 0.99, 0.99, 1.17, 0.81, 1.26, 0.81, 0.72, 0.9, 0.81, 0.81, 1.08, 0.9, 0.81, 1.17, 1.08, 0.54, 0.81, 1.08, 0.27, 0.99, 0.99, 0.72, 0.72, 0.9, 1.17, 1.35, 0.63, 0.9, 0.9, 1.17, 0.81, 0.9, 1.89, 1.08, 1.17, 0.9, 0.99, 0.72, 0.45, 0.63, 0.54, 0.36, 0.27, 0.54, 1.08, 0.45, 0.9, 0.72, 0.45, 0.81, 0.72, 0.72, 0.99, 0.81, 0.09, -0.63, -0.27, 0.81, 1.17, 0.63, 0.81, 1.08, 0.54, 0.81, 0.72, 0.63, 0.63, 0.09, 1.17, 0.45, 0.81, 0.81, 0.72, 0.315, 0.675, 0.315, 0.765, 0.36, 0.45, -0.45, 0, -0.45, 0.99, 0.18, -1.17, 0.45, 1.035, 0.495, 0.585, 0.9, 0.9, 1.035, 1.35, 1.485, 1.215, 1.125, 0.99, 1.125, 1.17, 1.08, 0.09, 1.305, 0.9, 1.17, 0.495, 1.08, 0.9, 1.08, 0.99, 0.81, 0.81, 0.855, 0.81, 1.08, 1.08, 0.81, 0.9, 0.27, 0.72, 0.09, 0.315, 0.09, 0.36, 0, 1.125, 0.27, -0.36, 1.17, 1.08, 0.315, 1.395, 0.99], "uptime": 0.1, "toppausetime": 0, "downtime": 0.25, "curvePath": "Weapon/Curve/ViewKickCurve_AR.asset"}, {"baseviewkickid": 28, "baseviewkicktype": 26, "rotation": "yaw", "vkvalues": [0.24, 0.06, -0.3, -0.36, -0.24, -0.48, -0.3, -0.3, -0.3, -0.24, -0.36, -0.3, 0.18, -0.3, -0.36, -0.48, -0.12, -0.6, -0.48, -0.18, -0.48, -0.12, 0.36, 0.3, 0.36, 0.54, 0.24, -0.3, -0.54, -0.06, -0.24, -0.24, -0.6, 0.12, -0.12, -0.84, 0.18, 0.66, 0.42, 0.24, 0.66, 0.36, 0.06, 0.54, -0.48, -0.24, -0.72, -0.42, -0.36, -0.36, -0.12, -0.54, -0.36, 0.12, 0.06, -0.06, 0.24, -0.06, 0.06, 0.42, 0.9, 0.84, 0.6, 0.6, 0.66, 0.6, 0.06, -0.54, -0.48, -0.48, -0.78, -0.36, -0.72, -0.84, 0.06, 0.42, 0.66, 0.6, 0.84, 0.9, -0.18, -0.54, -0.3, -0.18, -0.48, -0.42, 0.6, 0.42, 0.6, 0.78, 0.12, 0.48, 0.42, 0.6, 0.48, 0.51, 0.75, 0.6, 0.06, -1.32, -0.81, -0.81, -0.78, -0.6, -0.66, -0.78, -0.72, -0.84, -0.75, -0.6, 0.66, 0.27, 0.42, 0.36, 0.27, 0.33, -0.06, -0.06, -0.48, 0.33, 0.33, 0.27, 0.78, 0.09, 0.45, 0.06, 0.54, -0.27, -0.12, 0.18, 0.54, -0.21, -0.27, -0.39, -0.45, -0.21, 0, -0.39, -0.21, 0.66, 0.54, 0.99, 0.87, 0.9, 0.84, 0.87, 0.72, 0.84, 0.75, 0.18, 0.48, 0.84, 0.06, 0.33], "uptime": 0.1, "toppausetime": 0, "downtime": 0.25, "curvePath": "Weapon/Curve/ViewKickCurve_AR.asset"}, {"baseviewkickid": 29, "baseviewkicktype": 27, "rotation": "pitch", "vkvalues": [1.32, 1.26, 0.96, 1.02, 1.62, 1.62, 1.5, 1.38, 1.32, 1.32], "uptime": 0.01, "toppausetime": 0.05, "downtime": 0.3, "curvePath": "Weapon/Curve/ViewKickCurve_PIS.asset"}, {"baseviewkickid": 30, "baseviewkicktype": 27, "rotation": "yaw", "vkvalues": [0.3, 0, -0.66, -0.66, -0.6, 0.18, 0.18, -0.06, -0.6, 0.66], "uptime": 0.01, "toppausetime": 0.05, "downtime": 0.3, "curvePath": "Weapon/Curve/ViewKickCurve_PIS.asset"}, {"baseviewkickid": 31, "baseviewkicktype": 30, "rotation": "pitch", "vkvalues": [2.07, 2.16, 1.89, 1.89, 1.71, 1.8], "uptime": 0.03, "toppausetime": 0.03, "downtime": 0.4, "curvePath": "Weapon/Curve/ViewKickCurve_PIS.asset"}, {"baseviewkickid": 32, "baseviewkicktype": 30, "rotation": "yaw", "vkvalues": [0, 0.72, 0.36, 1.35, -1.26, 1.8], "uptime": 0.03, "toppausetime": 0.03, "downtime": 0.4, "curvePath": "Weapon/Curve/ViewKickCurve_PIS.asset"}, {"baseviewkickid": 33, "baseviewkicktype": 32, "rotation": "pitch", "vkvalues": [0.96, 1.08, 1.14, 1.2, 1.08, 1.08, 1.26, 0.6, 0.72, 1.2, 0.66, 0.84, 0.96, 0.9, 1.32, 0.96, 1.02, 0.36, 0.48, 0.66, 0.6, 0.54, 0.84, 0.54, 1.02, 0.96], "uptime": 0.1, "toppausetime": 0.03, "downtime": 0.22, "curvePath": "Weapon/Curve/ViewKickCurve_SAR.asset"}, {"baseviewkickid": 34, "baseviewkicktype": 32, "rotation": "yaw", "vkvalues": [0.24, 0.08, 0.32, 0.2, 0.32, 0.36, 0.24, -0.44, -0.4, -0.36, -0.76, -0.28, 0.2, 0.56, 0.2, -0.36, -0.12, -0.56, -0.52, -0.48, -0.68, -0.64, -0.4, -0.72, -0.16, -0.28], "uptime": 0.1, "toppausetime": 0.03, "downtime": 0.22, "curvePath": "Weapon/Curve/ViewKickCurve_SAR.asset"}, {"baseviewkickid": 35, "baseviewkicktype": 33, "rotation": "pitch", "vkvalues": [0.44, 0.84, 0.8, 1.04, 1.04, 1.12, 1.04, 1.08, 1.04, 1.08, 1.08, 0.96, 0.96, 1.2, 1.04, 1, 1.04, 0.96, 0.86, 1.02, 0.96, 0.8, 1.12, 1.06, 0.98], "uptime": 0.1, "toppausetime": 0, "downtime": 0.25, "curvePath": "Weapon/Curve/ViewKickCurve_SMG.asset"}, {"baseviewkickid": 36, "baseviewkicktype": 33, "rotation": "yaw", "vkvalues": [0.25, 0.04, 0.11, 0.25, 0.14, 0.22, 0.07, 0.32, 0, -0.25, 0.29, 0.14, 0.5, 0.25, 0.29, 0.14, 0.04, 0.32, 0.18, 0.05, -0.13, -0.09, -0.11, -0.16, -0.09], "uptime": 0.1, "toppausetime": 0, "downtime": 0.25, "curvePath": "Weapon/Curve/ViewKickCurve_SMG.asset"}]}