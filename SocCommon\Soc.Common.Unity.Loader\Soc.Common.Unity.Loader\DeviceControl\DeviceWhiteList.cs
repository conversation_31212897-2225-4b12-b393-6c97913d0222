//静态类，设备白名单
using System;
using System.Collections;
using System.Runtime.InteropServices;
using UnityEngine;
using WizardGames.Soc.Common.Unity;
using WizardGames.Soc.Common.Unity.Loader;
using WizardGames.Soc.SocClient.Data;

namespace WizardGames.Soc.SocClient.Device
{
    public class WhitelistCheckResult
    {
        public bool IsWhitelisted { get; set; }
        public string DialogTitle { get; set; }
        public string DialogContent { get; set; }
        
        public WhitelistCheckResult(bool isWhitelisted, string dialogTitle = "", string dialogContent = "")
        {
            IsWhitelisted = isWhitelisted;
            DialogTitle = dialogTitle;
            DialogContent = dialogContent;
        }
    }
    
    public static class DeviceWhitelist
    {
        private static readonly SocLogger Log = LogHelper.GetLogger(typeof(DeviceWhitelist));
        
#if UNITY_IOS
        #region Objective-C Native Methods
        [DllImport("__Internal")]
        private static extern bool IsDeviceUnsupportedNative();
        #endregion
#endif
        
        // ReSharper disable Unity.PerformanceAnalysis
        public static IEnumerator CheckWhitelistAsync(Action<WhitelistCheckResult> callback = null, bool showDialogIfNotSupported = true)
        {
            Log.Info("[DeviceWhitelist] CheckWhitelistAsync");
            WhitelistCheckResult result = CheckWhitelist();
            
            callback?.Invoke(result);
            
            //上报Tlog
            string cpuStr = $"{PlatformDeviceInfo.GetCPUName()} {SystemInfo.processorType} {SystemInfo.processorFrequency} {SystemInfo.processorCount}";
            string tlogData = $"{PlatformDeviceInfo.GetDeviceModel()}|{cpuStr}|{PlatformDeviceInfo.GetDeviceName()}|{result.IsWhitelisted}";
            TLogHelper.UploadClientTLog("LoginBlocked", tlogData);
            
            if (!result.IsWhitelisted && showDialogIfNotSupported)
            {
                // 设备不在白名单中，显示阻塞对话框
                if (UiUpdateMono.Instance != null)
                {
                    Log.Info("[DeviceWhitelist] Show dialog");
                    // 使用协程版本的PopMsgInBlock，确保正确阻塞
                    yield return UiUpdateMono.Instance.PopMsgInBlockCoroutine(result.DialogContent, 
                        LanguageManager.GetTextConst(LanguageConstAOT.Quit));

                    Log.Info("[DeviceWhitelist] Exit game");
                    Application.Quit();
                    yield break; // 确保协程结束
                }
                else
                {
                    Log.Error("UiUpdateMono instance not found!");
                }
            }
            
            yield return null;
        }
        
        public static WhitelistCheckResult CheckWhitelist()
        {
#if !PUBLISH
            // 非外放版本，利用分支做开关
            string branch = ApplicationConfig.Instance.Branch();
            if (branch != "weekly" && !branch.StartsWith("publish"))
                return new WhitelistCheckResult(true);
#endif
            
#if UNITY_EDITOR
            // 在编辑器中，直接返回白名单通过
            Debug.Log("[DeviceWhitelist] CheckWhitelist in editor. DeviceModel: " + PlatformDeviceInfo.GetDeviceModel());
            return new WhitelistCheckResult(true);
#elif UNITY_ANDROID
            // 借用了Java层的云控兼容性配置，直接调用Java层的接口
            Debug.Log("[DeviceWhitelist] CheckWhitelist in android.");
            AndroidJavaClass unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
            AndroidJavaObject currentActivity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");
            if(currentActivity == null){
                Debug.LogError("[DeviceWhitelist] CheckWhitelist failed with null main activity");
                return new WhitelistCheckResult(false);
            }
            Debug.Log("[DeviceWhitelist] CheckWhitelist with main activity.");
            bool bUnsupportedDevice = currentActivity.Call<bool>("isDeviceUnsupported");
            if(bUnsupportedDevice){
                string strDialogTitle = currentActivity.Call<string>("getUnsupportedDeviceDialogLocalizedTitle");
                string strDialogContent = currentActivity.Call<string>("getUnsupportedDeviceDialogLocalizedMessage");
                return new WhitelistCheckResult(!bUnsupportedDevice, strDialogTitle, strDialogContent);
            }
#elif UNITY_IOS 
            Debug.Log("[DeviceWhitelist] CheckWhitelist in ios.");
            bool bUnsupportedDevice = IsDeviceUnsupportedNative();
            if(bUnsupportedDevice){
                string strDialogTitle = DeviceCompatibilityMgr.GetUnsupportedDeviceDialogLocalizedTitle();
                string strDialogContent = DeviceCompatibilityMgr.GetUnsupportedDeviceDialogLocalizedMessage();
                Debug.Log($"[DeviceWhitelist] not support: {bUnsupportedDevice}, title: {strDialogTitle}, content: {strDialogContent}");
                return new WhitelistCheckResult(!bUnsupportedDevice, strDialogTitle, strDialogContent);
            }
#elif UNITY_STANDALONE
            // PC设备比较简单，用C#实现的DeviceCompatibilityMgr来实现
            // 读取的配置文件和安卓版本使用的云控兼容性配置文件一样，只是减少了PlatformConfigParser的输入Variable
            // PC设备的输入Variable 只考虑 DeviceModel 和 DeviceName
            Debug.Log("[DeviceWhitelist] Check device config...");
            var compatMgr = new DeviceCompatibilityMgr();
            bool isDeviceSupported = compatMgr.IsDeviceSupported();
            if(!isDeviceSupported){
                string strDialogTitle = DeviceCompatibilityMgr.GetUnsupportedDeviceDialogLocalizedTitle();
                string strDialogContent = DeviceCompatibilityMgr.GetUnsupportedDeviceDialogLocalizedMessage();
                return new WhitelistCheckResult(isDeviceSupported, strDialogTitle, strDialogContent);
            }
#endif
            return new WhitelistCheckResult(true);
        }
    }
}
