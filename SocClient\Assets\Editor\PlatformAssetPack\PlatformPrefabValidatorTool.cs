﻿using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace PackTool
{
    public class PrefabModificationValidator
    {
        private const string CLIENT_ROOT = "Assets/SocClientRes";
        private const string PLATFORM_ROOT = "Assets/SocPlatformRes";

        public enum PlatformType { Mobile, PC }

        public void ValidateAllPrefabs(List<string> allClientPrefabs)
        {
            var reportData = new List<string> { };

            try
            {
                for (int i = 0; i < allClientPrefabs.Count; i++)
                {
                    var progress = (float)i / allClientPrefabs.Count;
                    if (EditorUtility.DisplayCancelableProgressBar("Validating Prefabs",
                            $"Processing {Path.GetFileName(allClientPrefabs[i])}", progress))
                        break;

                    var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(allClientPrefabs[i]);
                    if (prefab == null)
                    {
                        Debug.LogWarning($"Prefab at {allClientPrefabs[i]} could not be loaded.");
                        continue;
                    }
                    reportData.AddRange(ValidatePrefabModifications(prefab));
                }
            }
            finally
            {
                EditorUtility.ClearProgressBar();
                GenerateCsvReport(reportData);
            }
        }

        private List<string> ValidatePrefabModifications(GameObject clientPrefab)
        {
            var results = new List<string>();

            var sourcePath = AssetDatabase.GetAssetPath(clientPrefab);
            
            Debug.Log("开始检查 prefab: " +sourcePath);
            if (sourcePath == null)
            {
                Debug.LogWarning($"Prefab {clientPrefab.name} has no valid asset path.");
                return results;
            }
            
            var nestedReferences = CollectNestedPrefabs(clientPrefab);

            foreach (var (nestedPath, instance) in nestedReferences)
            {
                foreach (PlatformType platform in System.Enum.GetValues(typeof(PlatformType)))
                {
                    var platformPath = GetPlatformPrefabPath(nestedPath, platform);
                    if (!File.Exists(platformPath))
                    {
                        continue;
                    }

                    CheckModificationLoss(sourcePath, results, instance, platform, platformPath, nestedPath);
                }
            }

            Debug.Log($"检查完成 prefab: {sourcePath}, 找到 {nestedReferences.Count} 个嵌套引用");
            return results;
        }

        private List<(string path, GameObject instance)> CollectNestedPrefabs(GameObject prefab)
        {
            var result = new List<(string, GameObject)>();
            var alltrans = prefab.GetComponentsInChildren<Transform>(true);
            var prefabTrans = alltrans.Where(t => PrefabUtility.IsAnyPrefabInstanceRoot(t.gameObject)).ToArray();

            foreach (var inst in prefabTrans)
            {
                var go = inst.gameObject;
                var assetPath = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(go);
                if (assetPath.StartsWith(CLIENT_ROOT))
                    result.Add((assetPath, go));
            }

            return result;
        }

        private string GetPlatformPrefabPath(string clientPath, PlatformType platform)
        {
            var relativePath = clientPath.Substring(CLIENT_ROOT.Length + 1);
            return $"{PLATFORM_ROOT}/{platform}/{relativePath}";
        }

        private (GameObject originalPrefab, PropertyModification[] modifications) CaptureModifications(
            GameObject instance)
        {
            var original = PrefabUtility.GetCorrespondingObjectFromSource(instance);
            var modifications = PrefabUtility.GetPropertyModifications(instance)
                .Where(m => m != null && m.target != null && m.objectReference != null)
                .ToArray();

            return (original, modifications);
        }

        private void CheckModificationLoss(string sourcePath, List<string> results, GameObject instance,
            PlatformType platform, string platformPath, string nestedPath)
        {
            var instSource = PrefabUtility.GetCorrespondingObjectFromSource(instance);

            PropertyModification[] curmodifications = PrefabUtility.GetPropertyModifications(instSource);
            if (curmodifications == null)
            {
                curmodifications = PrefabUtility.GetPropertyModifications(instance);
            }

            PropertyModification[] curModify = curmodifications
                .Where(m => m.target != null && m.objectReference != null)
                .ToArray();

            if (curModify.Length == 0)
            {
                return;
            }

            //先把老资源拷贝带外部目录
            var tmpDir = "Asset/../Tmp";
            if (!Directory.Exists(tmpDir))
            {
                Directory.CreateDirectory(tmpDir);
            }


            var newlostMods = new List<string>();
            for (int i = 0; i < curModify.Length; i++)
            {
                var mod = curModify[i];
                if (mod.target is Component  c)
                {
                    newlostMods.Add("Hierarchy: "+PlatformPackUtils.GetTransformPathSkipRoot(c.transform));
                }
                else if (mod.target is GameObject go)
                {
                    newlostMods.Add("Hierarchy: "+PlatformPackUtils.GetTransformPathSkipRoot(go.transform));
                }
                else
                {
                    newlostMods.Add("Object: "+mod.target.name);
                }
            }
            

            //先把资产拷贝出去
            var tmpPath = Path.Combine(tmpDir, Path.GetFileName(nestedPath));

            File.Copy(nestedPath, tmpPath, true);

            PlatformPackUtils.CopyAsset(platformPath, nestedPath);
            AssetDatabase.Refresh();

            PropertyModification[] newModifications = PrefabUtility.GetPropertyModifications(instSource);
            if (newModifications == null)
            {
                newModifications = PrefabUtility.GetPropertyModifications(instance);
            }

            PropertyModification[] newModify =
                newModifications.Where(m => m.target != null && m.objectReference != null).ToArray();


            for(int i = 0; i < curModify.Length; i++)
            {
                var mod = curModify[i];
                bool found = false;
                foreach (var next in newModify)
                {
                    if (mod.target == next.target && mod.propertyPath == next.propertyPath)
                    {
                        found = true;
                        break;
                    }
                }

                if (!found)
                {
                    results.Add($"{sourcePath}," +
                                $"{platform}," +
                                $"{nestedPath}," +
                                $"{mod.propertyPath +" missing "}," +
                                $"{newlostMods[i]}");
                }
            }

            //再还原
            File.Copy(tmpPath, nestedPath, true);
            AssetDatabase.Refresh();
        }

        private void GenerateCsvReport(List<string> lines)
        {
            var reportPath = $"Assets/_Preview/PrefabValidationReport_{System.DateTime.Now:yyyyMMdd_HHmmss}.csv";
            File.WriteAllLines(reportPath, lines);
            EditorUtility.RevealInFinder(reportPath);
        }
    }
}