﻿using WizardGames.SocConst.Soc.Const;
using System.Collections.Generic;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocClient.Collection;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.SocClient.GoLoader;
using WizardGames.Soc.SocClient.Ui;
using UnityEngine.Rendering.Universal;

namespace WizardGames.Soc.SocClient
{
    public enum EDailyMissionState 
    {
        Invalid,
        InProgress,
        NotGetReward,
        Complete
    }

    public partial class MgrMission : MgrBase
    {
        public List<long> dailyMissionIdsLst = new List<long>();

        private List<long> inProgressDailyMissionLst = new List<long>();
        private List<long> notGetRewardDailyMissionLst = new List<long>();
        private List<long> preNotGetRewardDailyMissionLst = new List<long>();
        private List<long> completedDailyMissionLst = new List<long>();

        private List<long> inProgressDailyMissionRewardLst = new List<long>();
        private List<long> notGetRewardDailyMissionRewardLst = new List<long>();
        private List<long> completedDailyMissionRewardLst = new List<long>();


        public void InitDailyTask(SystemRootNode systemRoot) 
        {
            if (systemRoot == null)
                return;

            var inProgressNode = systemRoot.GetChildNode(PlayerTaskContainerIndex.Daily)?.GetChildNode(TaskNodeIndex.InProgress) as DirectoryNode;
            inProgressNode.SubscribeAnyUpdateCallback(RefreshInProgressDailyMission);

            inProgressDailyMissionLst.Clear();
            inProgressDailyMissionRewardLst.Clear();
            foreach (var (id, node) in inProgressNode)
            {
                if (node is not TaskNode taskNode) continue;
                var cfg = Mc.Tables.TbDailyTask.GetOrDefault(taskNode.BizId);
                if (cfg == null) continue;
                if (cfg.TaskType == DailyTaskType.Score)
                {
                    inProgressDailyMissionRewardLst.Add(taskNode.BizId);
                }
                else
                {
                    inProgressDailyMissionLst.Add(taskNode.BizId);
                }
            }

            var notGetRewardNode = systemRoot.GetChildNode(PlayerTaskContainerIndex.Daily)?.GetChildNode(TaskNodeIndex.CompletedAndNotGetReward) as DirectoryNode;
            notGetRewardNode.SubscribeAnyUpdateCallback(RefreshNotGetRewardDailyMission);
            notGetRewardDailyMissionLst.Clear();
            notGetRewardDailyMissionRewardLst.Clear();
            preNotGetRewardDailyMissionLst.Clear();
            foreach (var (id, node) in notGetRewardNode)
            {
                if (node is not TaskNode taskNode) continue;
                var cfg = Mc.Tables.TbDailyTask.GetOrDefault(taskNode.BizId);
                if (cfg == null) continue;
                if (cfg.TaskType == DailyTaskType.Score)
                {
                    notGetRewardDailyMissionRewardLst.Add(taskNode.BizId);
                }
                else
                {
                    notGetRewardDailyMissionLst.Add(taskNode.BizId);
                }
            }

            preNotGetRewardDailyMissionLst.AddRange(notGetRewardDailyMissionLst);

            var taskContainer = systemRoot.GetChildNode(PlayerTaskContainerIndex.Daily) as TaskContainer;
            taskContainer.CompletedTaskIds.SubscribeAnyUpdateCallback(RefreshCompletedDailyMission);
            completedDailyMissionLst.Clear();
            completedDailyMissionRewardLst.Clear();
            foreach (var taskId in taskContainer.CompletedTaskIds)
            {
                var cfg = Mc.Tables.TbDailyTask.GetOrDefault(taskId);
                if (cfg == null) continue;
                if (cfg.TaskType == DailyTaskType.Score)
                {
                    completedDailyMissionRewardLst.Add(taskId);
                }
                else
                {
                    completedDailyMissionLst.Add(taskId);
                }
            }

            Mc.Msg.FireMsg(EventDefine.UpdateDailyMission);
        }

        private void RefreshInProgressDailyMission()
        {
            inProgressDailyMissionLst.Clear();
            inProgressDailyMissionRewardLst.Clear();
            if (Mc.MyPlayer.MyRootNode == null)
                return;
            var inProgressNode = Mc.MyPlayer.MyRootNode?.GetNodeByPath(NodeSystemType.TaskSystem, PlayerTaskContainerIndex.Daily, TaskNodeIndex.InProgress) as DirectoryNode;
            foreach (var (id, node) in inProgressNode)
            {
                if (node is not TaskNode taskNode) continue;
                var cfg = Mc.Tables.TbDailyTask.GetOrDefault(taskNode.BizId);
                if (cfg == null) continue;
                if (cfg.TaskType == DailyTaskType.Score)
                {
                    inProgressDailyMissionRewardLst.Add(taskNode.BizId);
                }
                else
                {
                    inProgressDailyMissionLst.Add(taskNode.BizId);
                }
            }
            Mc.Msg.FireMsg(EventDefine.UpdateDailyMission);
        }

        private void RefreshNotGetRewardDailyMission() 
        {
            notGetRewardDailyMissionLst.Clear();
            notGetRewardDailyMissionRewardLst.Clear();
            if (Mc.MyPlayer.MyRootNode == null)
                return;
            var notGetRewardNode = Mc.MyPlayer.MyRootNode?.GetNodeByPath(NodeSystemType.TaskSystem, PlayerTaskContainerIndex.Daily, TaskNodeIndex.CompletedAndNotGetReward) as DirectoryNode;

            foreach (var (id, node) in notGetRewardNode)
            {
                if (node is not TaskNode taskNode) continue;
                var cfg = Mc.Tables.TbDailyTask.GetOrDefault(taskNode.BizId);
                if (cfg == null) continue;
                if (cfg.TaskType == DailyTaskType.Score)
                {
                    notGetRewardDailyMissionRewardLst.Add(taskNode.BizId);
                }
                else
                {
                    notGetRewardDailyMissionLst.Add(taskNode.BizId);
                }
            }

            foreach(var id in notGetRewardDailyMissionLst) 
            {
                if (!preNotGetRewardDailyMissionLst.Contains(id)) 
                {
                    UiTaskTipPop.ShowTips(new UiTaskTipPop.ShowParams(MissionTabType.Daily, id));
                    break;
                }
            }
            preNotGetRewardDailyMissionLst.Clear();
            preNotGetRewardDailyMissionLst.AddRange(notGetRewardDailyMissionLst);

            Mc.RedDot.RefreshRedDot(RedDotType.Task_DailyTab);

            Mc.Msg.FireMsg(EventDefine.UpdateDailyMission);
        }

        private void RefreshCompletedDailyMission()
        {
            completedDailyMissionLst.Clear();
            completedDailyMissionRewardLst.Clear();
            if (Mc.MyPlayer.MyRootNode == null)
                return;
            var taskContainer = Mc.MyPlayer.MyRootNode?.GetNodeByPath(NodeSystemType.TaskSystem, PlayerTaskContainerIndex.Daily) as TaskContainer;
            foreach (var taskId in taskContainer.CompletedTaskIds)
            {
                var cfg = Mc.Tables.TbDailyTask.GetOrDefault(taskId);
                if (cfg == null) continue;
                if (cfg.TaskType == DailyTaskType.Score)
                {
                    completedDailyMissionRewardLst.Add(taskId);
                }
                else
                {
                    completedDailyMissionLst.Add(taskId);
                }
            }
            Mc.Msg.FireMsg(EventDefine.UpdateDailyMission);
        }

        public int HasDailyMissionReward()
        {
            if (completedDailyMissionRewardLst.Count >= 4) return 0;
            return notGetRewardDailyMissionLst.Count + notGetRewardDailyMissionRewardLst.Count;
        }

        public void GetDailyMissionLstByType(DailyTaskType taskType, ref List<long> lst)
        {
            lst.Clear();
            if (taskType == DailyTaskType.Score) 
            {
                lst.AddRange(inProgressDailyMissionRewardLst);
                lst.AddRange(notGetRewardDailyMissionRewardLst);
                lst.AddRange(completedDailyMissionRewardLst);
                lst.Sort(CompareRewardMission);
            }
            else 
            {
                foreach(var id in inProgressDailyMissionLst) 
                {
                    var cfg = Mc.Tables.TbDailyTask.GetOrDefault(id);
                    if (cfg == null) continue;
                    if (taskType == DailyTaskType.All || cfg.TaskType == taskType) 
                    {
                        lst.Add(id);
                    }
                }
                foreach (var id in notGetRewardDailyMissionLst)
                {
                    var cfg = Mc.Tables.TbDailyTask.GetOrDefault(id);
                    if (cfg == null) continue;
                    if (taskType == DailyTaskType.All || cfg.TaskType == taskType)
                    {
                        lst.Add(id);
                    }
                }
                foreach (var id in completedDailyMissionLst)
                {
                    var cfg = Mc.Tables.TbDailyTask.GetOrDefault(id);
                    if (cfg == null) continue;
                    if (taskType == DailyTaskType.All || cfg.TaskType == taskType)
                    {
                        lst.Add(id);
                    }
                }
                lst.Sort(CompareNormalMission);
            }
        }

        private List<long> tempScoreLst = new List<long>();
        public int GetTotalDailyMissionScore() 
        {
            tempScoreLst.Clear();
            GetDailyMissionLstByType(DailyTaskType.Score, ref tempScoreLst);
            int rt = 0;

            int subNum = 0;

            foreach(var id in tempScoreLst) 
            {
                var state = Mc.Mission.GetMedalDailyMissionState(id);
                if (state == EDailyMissionState.InProgress)
                {
                    rt += GetTaskCount(id);
                    rt -= subNum;
                    break;
                }
                var taskConfig = Mc.Tables.TbQuestPhase.GetOrDefault(id);
                rt += (int)taskConfig.EndConditionParameter[0];
                subNum += (int)taskConfig.EndConditionParameter[0];
            }
            return rt;
        }


        private int CompareRewardMission(long a, long b)
        {
            var aCfg = Mc.Tables.TbDailyTask.GetOrDefault(a);
            var bCfg = Mc.Tables.TbDailyTask.GetOrDefault(b);
            return aCfg.SortWeight.CompareTo(bCfg.SortWeight);
        }

        private int CompareNormalMission(long a, long b) 
        {
            var aWeight = Mc.Tables.TbDailyTask.GetOrDefault(a).SortWeight;
            var bWeight = Mc.Tables.TbDailyTask.GetOrDefault(b).SortWeight;

            if (notGetRewardDailyMissionLst.Contains(a)) 
            {
                aWeight -= 100;
            }
            else if (completedDailyMissionLst.Contains(a)) 
            {
                aWeight += 200;
            }

            if (notGetRewardDailyMissionLst.Contains(b))
            {
                bWeight -= 100;
            }
            else if (completedDailyMissionLst.Contains(b))
            {
                bWeight += 200;
            }

            return aWeight.CompareTo(bWeight);
        }


        public EDailyMissionState GetNormalDailyMissionState(long id) 
        {
            if (inProgressDailyMissionLst.Contains(id)) 
            {
                return EDailyMissionState.InProgress;
            }
            else if (notGetRewardDailyMissionLst.Contains(id)) 
            {
                return EDailyMissionState.NotGetReward;
            }
            else if (completedDailyMissionLst.Contains(id)) 
            {
                return EDailyMissionState.Complete;
            }

            return EDailyMissionState.Invalid;
        }

        public EDailyMissionState GetMedalDailyMissionState(long id)
        {
            if (inProgressDailyMissionRewardLst.Contains(id))
            {
                return EDailyMissionState.InProgress;
            }
            else if (notGetRewardDailyMissionRewardLst.Contains(id))
            {
                return EDailyMissionState.NotGetReward;
            }
            else if (completedDailyMissionRewardLst.Contains(id))
            {
                return EDailyMissionState.Complete;
            }

            return EDailyMissionState.Invalid;
        }

        public bool IsDailyMissionTracked(long id)
        {
            if (Mc.MyPlayer.MyEntityServer.TaskComponent != null)
            {
                return id == Mc.MyPlayer.MyEntityServer.TaskComponent.TrackTaskId;
            }
            return false;
        }

        public bool IsDailyMissionTracked()
        {
            if (Mc.MyPlayer.MyEntityServer.TaskComponent != null)
            {
                var trackId = Mc.MyPlayer.MyEntityServer.TaskComponent.TrackTaskId;
                return inProgressDailyMissionLst.Contains(trackId);
            }
            return false;
        }
    }
}
