
using System;
using System.Text;
using UnityEngine;
using AssetBundleAOT.SimpleJSON;
using UnityEngine.Networking;
using WizardGames.Soc.SocClient.Device;

namespace WizardGames.Soc.Common.Unity.Loader
{
    public static class TLogHelper
    {
        private static readonly string TLogUrl = ApplicationConfig.Instance.AppConfig.Hall.HallApiUrl + "/tlog/sendclientlog";
        private static string IndicatorFormat(string str)
        {
            if (string.IsNullOrEmpty(str))
            {
                return "Null";
            }

            return str.Replace(" ", "\\ ").Replace(",", "\\,").Replace("=", "\\=");
        }
        
        //上传客户端埋点日志，由于和MgrUploadLog.cs的程序集不同，无法用他的静态方法，所以这里单独写一个方法，二者功能一样
        public static void UploadClientTLog(string logName, string data = null)
        {
            string timeStr = DateTime.UtcNow.AddHours(8).ToString("yyyy-MM-dd HH:mm:ss");//时区+8
            string deviceIdStr = IndicatorFormat(PlatformDeviceInfo.GetDeviceID());
            string msg = $"{timeStr}|{deviceIdStr}";
            if (data != null)
            {
                msg += $"|{data}";
            }
            JSONObject param = new();
            param.Add("logName", logName);
            param.Add("msg", msg);
            UnityWebRequest unityWebRequest = new(TLogUrl, "POST");
            byte[] bodyRaw = Encoding.UTF8.GetBytes(param.ToString());
            unityWebRequest.uploadHandler = new UploadHandlerRaw(bodyRaw);
            unityWebRequest.downloadHandler = new DownloadHandlerBuffer();
            unityWebRequest.SetRequestHeader("Content-Type", "application/json");
            unityWebRequest.SendWebRequest();
            Debug.Log($"logName:{logName},msg:{msg}");
        }
    }
}