﻿using System;
using System.Threading;
using WizardGames.Soc.Common.Cache;
using WizardGames.Soc.Common.Framework.Network.Impl;
using WizardGames.Soc.Common.TimerWheel;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.SocBotServer.Threads;
using WizardGames.Soc.SocClient.Network;

namespace WizardGames.Soc.SocClient.Framework
{
    public abstract class AsyncTaskBase : IAsyncTask
    {
        public abstract void ExcuteFunc();
    }
    public abstract class PostToKcpTask : AsyncTaskBase
    { }

    public class KcpThread : ThreadBase<PostToKcpTask>
    {
        public TimerWheel TimerWheel;
        private long lastTimeStamp;
        private KcpClient kcpClient;
        private string ipAddress;
        private int port;

        public KcpThread(string ipAddress, int port, KcpClient client)
        {
            this.ipAddress = ipAddress;
            this.port = port;
            kcpClient = client;
        }

        public override bool Boot()
        {
            // 覆盖基类
            thread = new Thread(Loop)
            {
                Name = "KcpThread",
                IsBackground = true,
            };
#if SOC_BOT_SERVER
            thread.Name = $"KcpThread{kcpClient.RoleId}";
#endif
            thread.Start();
            return true;
        }

        protected override void Loop()
        {
            kcpClient.Connect(ipAddress, (ushort)port);
            TimerWheel = new TimerWheel("Kcp");
            TimerWheel.Start();
            lastTimeStamp = TimeStampUtil.GetRawTimestampMsec();
            TimerWheel.AddTimerRepeat(16, 16, (_, _, _) =>
            {
                kcpClient.Tick();
            });

            if (Interlocked.CompareExchange(ref State, ThreadState.RUNNING, ThreadState.NOT_READY) == ThreadState.NOT_READY)
            {
                logger.Info("BeginUpdate");
                while (State == ThreadState.RUNNING && kcpClient.Active)
                {
                    Queue.ProcessAsyncTask(logger);
                    var now = TimeStampUtil.GetRawTimestampMsec();
                    var elapsed = now - lastTimeStamp;
                    if (elapsed > 0)
                    {
                        TimerWheel.Update((uint)elapsed);
                        lastTimeStamp = now;
                    }
                    Queue.Wait(16);
                }
                logger.Info("LeaveUpdate");
            }

            //Queue.ProcessAsyncTask(true);
        }

        public void Cleanup()
        {
            logger.Info("kcp logic thread exit");
        }
    }

    public class OnDataReceivedTask : PostToKcpTask, IPooledObject
    {
        private static ConcurrentPool<OnDataReceivedTask> pool = new(256);
        private ArraySegment<byte> data;
        private UdpBuffer socketInfo;
        private KcpClient client;
        public bool IsReturn { get; set; }

        public static OnDataReceivedTask GetFromPool(ref ArraySegment<byte> data, UdpBuffer info, KcpClient client)
        {
            var task = pool.Get();
            task.data = data;
            task.socketInfo = info;
            task.client = client;
            return task;
        }

        public override void ExcuteFunc()
        {
            client.RawInput(ref data);
            client.TryPeekPackets();
        }

        public void ReturnToPool()
        {
            socketInfo.ReturnToPool();
            socketInfo = null;
            data = null;
            client = null;
        }
    }

    public class SimpleTask : PostToKcpTask
    {
        private static readonly SocLogger logger = LogHelper.GetLogger(typeof(SimpleTask));
        private Action action;
        public SimpleTask(Action action)
        {
            this.action = action;
        }
        public override void ExcuteFunc()
        {
            try
            {
                action.Invoke();
            }
            catch (Exception e)
            {
                logger.ErrorFormat("Execute SimpleTask with exception: {0}", e);
            }
        }
    }
}
