using FairyGUI;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Unity.Construction;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Plant;

namespace WizardGames.Soc.SocClient.Ui
{
    public class PlantModelPreview
    {
        private UiPlantOperationSubPanel uiPlantOperationSubPanel;
        public ComShowItemModel comShowItemModel;
        public PlantBoxCtrl plantBoxCtrl;
        
        private Vector2 touchStartPos = Vector2.zero;
        public readonly Vector3 modelFixedRot = new Vector3(270, 0, 0);
        public Vector3 modelOriRot;

        public static PlantModelPreview Create(GLoader loader3D,UiPlantOperationSubPanel uiPlantOperationSubPanel)
        {
            PlantModelPreview plantModelPreview = new PlantModelPreview();
            plantModelPreview.OnInit(loader3D,uiPlantOperationSubPanel);
            return plantModelPreview;
        }

        private void OnInit(GLoader loader3D,UiPlantOperationSubPanel uiPlantOperationSubPanel)
        {
            this.uiPlantOperationSubPanel = uiPlantOperationSubPanel;
            comShowItemModel = new ComShowItemModel(loader3D);
            comShowItemModel.TryInitScene(modelType: MgrUiModel.ModelType.Plant);
            comShowItemModel.AdjustCamera();
        }

        public void Show()
        {            
            comShowItemModel?.Show();
            InitPlantBoxModel();
            UnityEngine.Shader.SetGlobalFloat(PropertyToID.UberAHash, 1);
        }

        public void Hide()
        {
            comShowItemModel?.Hide();
            if (!Mc.Ui.IsWindowOpen("UiInventory"))
            {
                UnityEngine.Shader.SetGlobalFloat(PropertyToID.UberAHash, 0);
            }
        }
        
        public void OnDestroy()
        {
            comShowItemModel?.Clear();
            comShowItemModel = null;
        }
        
        public void RegisterEvent()
        {
            Mc.Msg.AddListener<int, long, long>(EventDefine.PrePlant, OnPrePlant);
            Mc.Msg.AddListener<int, long>(EventDefine.CancelPrePlant, OnCancelPrePlant);
            Mc.Msg.AddListener<int>(EventDefine.PlantPageSelectedSlot, OnPlantPageSelectedSlot);
            Mc.Msg.AddListener<List<int>>(EventDefine.SelectMultipleSlotWhenOpenManurePage, OnSelectMultipleSlotWhenOpenManurePage);
            Mc.Msg.AddListener<int>(EventDefine.ManurePageSelectedSlot, OnManurePageSelectedSlot);

        }
        
        public void UnRegisterEvent()
        {
            Mc.Msg.RemoveListener<int, long, long>(EventDefine.PrePlant, OnPrePlant);
            Mc.Msg.RemoveListener<int, long>(EventDefine.CancelPrePlant, OnCancelPrePlant);
            Mc.Msg.RemoveListener<int>(EventDefine.PlantPageSelectedSlot, OnPlantPageSelectedSlot);
            Mc.Msg.RemoveListener<List<int>>(EventDefine.SelectMultipleSlotWhenOpenManurePage, OnSelectMultipleSlotWhenOpenManurePage);
            Mc.Msg.RemoveListener<int>(EventDefine.ManurePageSelectedSlot, OnManurePageSelectedSlot);
        }

        public void OnFps10Update()
        {
            if (UiItemIconDragDrop.IsDragging)
            {
                var pos = Stage.inst.touchPosition;
                if (uiPlantOperationSubPanel.LastPageType == EPageType.Seed)
                {
                    comShowItemModel.RayCastPlant(pos, out var hit);
                    if (hit.transform != null)
                    {
                        var index = hit.transform.name.LastIndexOf("_");
                        if (int.TryParse(hit.transform.name.Substring(index + 1), out var slot))
                        {
                            plantBoxCtrl.RefreshHighLight(slot);
                            Mc.Msg.FireMsg(EventDefine.PlantBoxModelSelectedSlot, slot, uiPlantOperationSubPanel.RightWindownCtrl.selectedIndex);
                        }
                    }
                }
            }
            comShowItemModel.AdjustMaterial();
        }

        public void OnChangePage(EPageType pageType)
        {
            switch (pageType)
            {
                case EPageType.Seed:
                case EPageType.Manure:
                    comShowItemModel?.SetTouchable(true);
                    modelOriRot = comShowItemModel != null ? comShowItemModel.GetRotation() : Vector3.zero;
                    comShowItemModel?.SetRotation(modelFixedRot);
                    break;
                case EPageType.PlantDetail:
                    modelOriRot = comShowItemModel != null ? comShowItemModel.GetRotation() : Vector3.zero;
                    break;
                default:
                    comShowItemModel?.SetRotation(modelOriRot);
                    comShowItemModel?.SetTouchable(true);

                    plantBoxCtrl.ClearAllPrePlantContext();//不去重复刷新PlantBox，刷新通过UpdatePlant事件进行刷新
                    plantBoxCtrl.RefreshPlants();//需要重新刷新下模型，存在UpdatePlant事件和OnPlant事件的时序问题

                    plantBoxCtrl.DisableAllOutline();
                    break;
            }
        }

        public void RefreshPlants(PlantBoxData curPlantBox = null)
        {
            plantBoxCtrl.RefreshPlants(curPlantBox);
        }

        private void InitPlantBoxModel()
        {
            if (uiPlantOperationSubPanel.CurPlantBox != null && uiPlantOperationSubPanel.CurPlantBox.plantBoxCfg != null)
            {
                string modelPath = CommonConstructionUtils.GetSeparatePrefabPathByType(uiPlantOperationSubPanel.CurPlantBox.plantBoxCfg.Id, EConstructionPrefabType.SourceMesh, 0);
                if (!string.IsNullOrEmpty(modelPath))
                {
                    if (comShowItemModel != null)
                    {
                        comShowItemModel.RefreshModel(modelPath, (info) =>
                        {
                            if (info == null || info.objModelHolder == null) return;
                            Quaternion rotation = Quaternion.Euler(uiPlantOperationSubPanel.CurPlantBox.plantBoxCfg.ModelOriAngle[0], uiPlantOperationSubPanel.CurPlantBox.plantBoxCfg.ModelOriAngle[1], uiPlantOperationSubPanel.CurPlantBox.plantBoxCfg.ModelOriAngle[2]);
                            info.objModelHolder.transform.rotation = rotation;
                            info.objModelHolder.transform.localScale = Vector3.one * uiPlantOperationSubPanel.CurPlantBox.plantBoxCfg.ModelZoom;
                            info.objModelHolder.transform.localPosition = new Vector3(uiPlantOperationSubPanel.CurPlantBox.plantBoxCfg.ModelOffset[0], uiPlantOperationSubPanel.CurPlantBox.plantBoxCfg.ModelOffset[1], uiPlantOperationSubPanel.CurPlantBox.plantBoxCfg.ModelOffset[2]);
                            if (comShowItemModel != null)
                            {
                                comShowItemModel.EnableTouch(OnItemTouchBegin, OnItemTouchMove);
                                comShowItemModel.EnableClick(OnClickPlantBoxModel);
                                comShowItemModel.EnableItemDrop(OnItemDrop);

                                plantBoxCtrl = comShowItemModel.modelGO.GetComponent<PlantBoxCtrl>();
                                plantBoxCtrl.SetLayer("UI");
                                plantBoxCtrl.RefreshPlants(uiPlantOperationSubPanel.CurPlantBox);
                            }
                        });
                    }
                }
            }
        }
        
        public void OnItemTouchBegin(EventContext context)
        {
            if (uiPlantOperationSubPanel.RightWindownCtrl != null && uiPlantOperationSubPanel.RightWindownCtrl.selectedIndex != (int)EPageType.Default
                                                                && uiPlantOperationSubPanel.RightWindownCtrl.selectedIndex != (int)EPageType.PlantDetail) return;
            touchStartPos = context.inputEvent.position;
            context.CaptureTouch();
        }

        public void OnItemTouchMove(EventContext context)
        {
            if (uiPlantOperationSubPanel.RightWindownCtrl != null && uiPlantOperationSubPanel.RightWindownCtrl.selectedIndex != (int)EPageType.Default
                                                     && uiPlantOperationSubPanel.RightWindownCtrl.selectedIndex != (int)EPageType.PlantDetail) return;
            var deltaX = context.inputEvent.x - touchStartPos.x;
            var deltaY = context.inputEvent.y - touchStartPos.y;
            comShowItemModel?.Rotate(new Vector3(-deltaY, -deltaX, 0), Space.World);
            touchStartPos = context.inputEvent.position;
        }
        
        public void OnClickPlantBoxModel(EventContext context)
        {
            if (context == null || comShowItemModel == null) return;

            if (uiPlantOperationSubPanel.LastPageType != EPageType.PlantDetail)
            {
                comShowItemModel.RayCastPlant(context.inputEvent.position, out var hit);
                if (hit.transform != null)
                {
                    var index = hit.transform.name.LastIndexOf("_");
                    if (int.TryParse(hit.transform.name.Substring(index + 1), out var slot))
                    {
                        var plant = uiPlantOperationSubPanel.CurPlantBox.GetPlantData(slot);
                        if (uiPlantOperationSubPanel.LastPageType == EPageType.Default && uiPlantOperationSubPanel.CurPlantBox != null && uiPlantOperationSubPanel.RightWindownCtrl != null)
                        {
                            if (plant != null)
                            {
                                uiPlantOperationSubPanel.ChangePage(EPageType.PlantDetail);
                                // Mc.Msg.FireMsg(EventDefine.OnChangePlantMainPage, EPageType.PlantDetail);
                                Mc.Msg.FireMsg(EventDefine.DetailPageSelectedSlot, slot);
                                plantBoxCtrl.ToggleHighLight(slot);
                                plantBoxCtrl.EnableOutline(slot);
                            }
                            else
                            {
                                uiPlantOperationSubPanel.ChangePage(EPageType.Seed);
                                // Mc.Msg.FireMsg(EventDefine.OnChangePlantMainPage, EPageType.Seed);
                            }
                        }

                        if (uiPlantOperationSubPanel.LastPageType != EPageType.Default)
                        {
                            Mc.Msg.FireMsg(EventDefine.PlantBoxModelSelectedSlot, slot, uiPlantOperationSubPanel.RightWindownCtrl.selectedIndex);
                        }

                        if (uiPlantOperationSubPanel.LastPageType == EPageType.Seed && plant == null)//如果当前在种植界面时，并且无植物时刷新高亮
                        {
                            //todo wst: 违反迪米特原则，之后用耦合更少的方式实现
                            UiPlantSeedPage seedPagePage = uiPlantOperationSubPanel.PlantPageDic[EPageType.Seed] as UiPlantSeedPage;
                            int prevSelectedSlot = seedPagePage.GetPrevSelectedIndex();

                            if (prevSelectedSlot != slot)
                            {
                                OnPlantPageSelectedSlot(slot);
                                seedPagePage.SetLastSelectedIndex(slot);//做完所有表现后，更新上一个选中
                            }
                            Mc.Audio.PlayAudioEvent(null, "UI_Click_02");
                        }
                    }
                }
                else
                {
                    if (uiPlantOperationSubPanel.LastPageType == EPageType.Seed || uiPlantOperationSubPanel.LastPageType == EPageType.Manure)
                    {
                        uiPlantOperationSubPanel.ChangePage(EPageType.Default);
                        // Mc.Msg.FireMsg(EventDefine.OnChangePlantMainPage, EPageType.Default);
                    }
                }
            }
            else
            {
                comShowItemModel.RayCastPlant(context.inputEvent.position, out var hit);
                if (hit.transform != null)
                {
                    var index = hit.transform.name.LastIndexOf("_");
                    if (int.TryParse(hit.transform.name.Substring(index + 1), out var slot))
                    {
                        var plant = uiPlantOperationSubPanel.CurPlantBox.GetPlantData(slot);
                        if (plant != null)
                        {
                            Mc.Msg.FireMsg(EventDefine.DetailPageSelectedSlot, slot);
                            plantBoxCtrl.UnHighLightAllSlots();
                            plantBoxCtrl.ToggleHighLight(slot);

                            plantBoxCtrl.DisableAllOutline();
                            plantBoxCtrl.EnableOutline(slot);
                            return;
                        }
                    }
                }
                uiPlantOperationSubPanel.ChangePage(EPageType.Default);
                // Mc.Msg.FireMsg(EventDefine.OnChangePlantMainPage, EPageType.Default);
            }
        }
        
        public void OnItemDrop(EventContext context)
        {
            UiItemIconDragDrop.CancelItemDrag();
            if (uiPlantOperationSubPanel.RightWindownCtrl != null && uiPlantOperationSubPanel.RightWindownCtrl.selectedIndex == (int)EPageType.Seed)
            {
                if (context == null || comShowItemModel == null) return;

                comShowItemModel.RayCastPlant(context.inputEvent.position, out var hit);

                if (hit.transform == null) return;

                var index = hit.transform.name.LastIndexOf("_");
                if (!int.TryParse(hit.transform.name.Substring(index + 1), out var slot))
                {
                    return;
                }

                var dragInfo = context.data as ItemDragInfo;
                if (dragInfo == null) return;
                if (dragInfo.customData is BaseItemNode seedData && uiPlantOperationSubPanel.CurPlantBox != null && uiPlantOperationSubPanel.CurPlantBox.GetPlantData(slot) == null)
                {
                    Mc.Plant.PrePlant(slot, seedData.BizId, seedData.Id);
                }
            }
        }

        public void UpdateHighLight(int index)
        {
            plantBoxCtrl.ToggleHighLight(index);
            plantBoxCtrl.EnableOutline(index);
        }

        private void OnPrePlant(int slot, long seedId, long instanceId)
        {
            if (plantBoxCtrl != null)
            {
                plantBoxCtrl.PrePlant(slot, seedId);
            }
        }

        private void OnCancelPrePlant(int slot, long seedId)
        {
            if (plantBoxCtrl != null)
            {
                plantBoxCtrl.CancelPrePlant(slot, seedId);
            }
        }
        
        private void OnPlantPageSelectedSlot(int selectedIndex)
        {
            plantBoxCtrl.RefreshHighLight(selectedIndex);
        }

        private void OnSelectMultipleSlotWhenOpenManurePage(List<int> selectedIndex)
        {
            if (plantBoxCtrl != null)
            {
                plantBoxCtrl.UnHighLightAllSlots();
                foreach (var index in selectedIndex)
                {
                    plantBoxCtrl.ToggleHighLight(index);
                }
            }
        }

        private void OnManurePageSelectedSlot(int slotIndex)
        {
            plantBoxCtrl.ToggleHighLight(slotIndex);
        }
    }
}
