{"hash": 932355391, "data": [{"id": 1, "constName": "DisconnectConnect", "text": {"index": 2079349080, "text": "断开当前连接..."}}, {"id": 2, "constName": "ExitGameScene", "text": {"index": 1772341855, "text": "退出游戏场景..."}}, {"id": 3, "constName": "ConnectSvr", "text": {"index": 773518353, "text": "连接{0}......"}}, {"id": 4, "constName": "EnterOffline", "text": {"index": 1291970644, "text": "进入离线地图{0}..."}}, {"id": 5, "constName": "UnitMeter", "text": {"index": 469619012, "text": "米"}}, {"id": 6, "constName": "UnitHour", "text": {"index": 239284321, "text": "时"}}, {"id": 7, "constName": "UnitMin", "text": {"index": 1731041262, "text": "分"}}, {"id": 8, "constName": "UnitSec", "text": {"index": 722324195, "text": "秒"}}, {"id": 9, "constName": "UnitWater", "text": {"index": 59166383, "text": "{0}毫升"}}, {"id": 10, "constName": "UnitPower", "text": {"index": 1133737055, "text": "rWm"}}, {"id": 12, "constName": "ChatPrivate", "text": {"index": 18217164, "text": "私聊"}}, {"id": 13, "constName": "ChatChannel", "text": {"index": 1214334740, "text": "频道"}}, {"id": 14, "constName": "ChatTeam", "text": {"index": 1500327936, "text": "队伍"}}, {"id": 15, "constName": "ChatWorld", "text": {"index": 800317670, "text": "世界"}}, {"id": 16, "constName": "ChatTeamLabel", "text": {"index": 1854228139, "text": "Hud_Chat_Title01"}}, {"id": 17, "constName": "ChatWorldLabel", "text": {"index": 93499275, "text": "Hud_Chat_Title02"}}, {"id": 18, "constName": "ChatPrivateLabel", "text": {"index": 1611967366, "text": "Hud_Chat_Title03"}}, {"id": 19, "constName": "KillSelf", "text": {"index": 194669483, "text": "自我解脱"}}, {"id": 20, "constName": "Aid", "text": {"index": 236718688, "text": "救助"}}, {"id": 21, "constName": "DangerLifting", "text": {"index": 312184840, "text": "解除威胁"}}, {"id": 22, "constName": "ChatAppointment", "text": {"index": 1712122609, "text": "预约"}}, {"id": 23, "constName": "DetailTrace", "text": {"index": 181526304, "text": "追踪"}}, {"id": 24, "constName": "DetailCanntTrace", "text": {"index": 1238250579, "text": "无法追踪"}}, {"id": 25, "constName": "DetailNoDrop", "text": {"index": 1019435516, "text": "暂无掉落。"}}, {"id": 26, "constName": "DetailCanntGetByTrade", "text": {"index": 1438227932, "text": "无法通过交易获得。"}}, {"id": 27, "constName": "GoToCraft", "text": {"index": 263055538, "text": "前往制作"}}, {"id": 28, "constName": "NotUnlockItem", "text": {"index": 894964112, "text": "道具未解锁"}}, {"id": 29, "constName": "ChatAppointmentLabel", "text": {"index": 1739898229, "text": "Hud_Chat_Title01"}}, {"id": 30, "constName": "DetailMixingTable", "text": {"index": 1606658277, "text": "调制台"}}, {"id": 31, "constName": "OpenMixingTable", "text": {"index": 520547381, "text": "打开调制台"}}, {"id": 32, "constName": "OpenWorkbench", "text": {"index": 938662027, "text": "打开工作台"}}, {"id": 33, "constName": "HasUnlocked", "text": {"index": 587262695, "text": "道具已解锁。"}}, {"id": 34, "constName": "CantUnlockViaWorkbench", "text": {"index": 1501076611, "text": "该道具无法通过工作台解锁。"}}, {"id": 35, "constName": "ResearchWorckbench", "text": {"index": 329703585, "text": "研究工作台"}}, {"id": 36, "constName": "OpenResearchWorckbench", "text": {"index": 1020017083, "text": "打开研究台"}}, {"id": 37, "constName": "NameRecycler", "text": {"index": 119152975, "text": "分解机"}}, {"id": 38, "constName": "OpenRecycler", "text": {"index": 1860298748, "text": "打开分解机"}}, {"id": 39, "constName": "ItemCantRecycle", "text": {"index": 1043939078, "text": "该道具无法被分解。"}}, {"id": 40, "constName": "CantGetByRecycle", "text": {"index": 142684066, "text": "该道具无法通过分解获得。"}}, {"id": 41, "constName": "Confirm", "text": {"index": 1381094159, "text": "确定"}}, {"id": 42, "constName": "Cancel", "text": {"index": 1421253321, "text": "取消"}}, {"id": 43, "constName": "Yes", "text": {"index": 1991366089, "text": "确定"}}, {"id": 44, "constName": "No", "text": {"index": 1560502734, "text": "取消"}}, {"id": 45, "constName": "OK", "text": {"index": 21370788, "text": "确定"}}, {"id": 46, "constName": "ClearAndReplace", "text": {"index": 1015322185, "text": "清空并替换"}}, {"id": 47, "constName": "WorkBenchNoNeed", "text": {"index": 267176636, "text": "手工制作"}}, {"id": 48, "constName": "WorkBenchLv1Need", "text": {"index": 424608332, "text": "一级工作台"}}, {"id": 49, "constName": "WorkBenchLv2Need", "text": {"index": 1467859520, "text": "二级工作台"}}, {"id": 50, "constName": "WorkBenchLv3Need", "text": {"index": 547433182, "text": "三级工作台"}}, {"id": 51, "constName": "SelectWaterContainer", "text": {"index": 2099344852, "text": "放入一个盛水容器"}}, {"id": 52, "constName": "BuildStable", "text": {"index": 1036144535, "text": "{0}% 支撑力"}}, {"id": 53, "constName": "Unlock", "text": {"index": 1085540240, "text": "解锁"}}, {"id": 54, "constName": "UnlockAll", "text": {"index": 1677188108, "text": "解锁全部"}}, {"id": 60, "constName": "NoRightToUpdate", "text": {"index": 665354001, "text": "你没有工具柜权限，无法进行升级。需要工具柜权限。"}}, {"id": 61, "constName": "NoRightToRemove", "text": {"index": 2082855907, "text": "你没有工具柜权限,无法进行拆除。需要工具柜权限。"}}, {"id": 62, "constName": "NoRightToFix", "text": {"index": 591646161, "text": "你没有工具柜权限，无法进行维修。需要工具柜权限。"}}, {"id": 63, "constName": "NoRightToRecycle", "text": {"index": 855021187, "text": "你没有工具柜权限，无法进行回收。需要工具柜权限。"}}, {"id": 64, "constName": "NoRight", "text": {"index": 2130432412, "text": "你没有权限, 需要工具柜权限。"}}, {"id": 65, "constName": "BuildCantOpCDTip1", "text": {"index": 1389226307, "text": "无法回收:你只能在建造后{0:N0}秒内进行回收。"}}, {"id": 66, "constName": "BuildCantOpCDTip2", "text": {"index": 1951803005, "text": "无法升级:你只能在建造后{0:N0}秒内进行升级。"}}, {"id": 67, "constName": "BuildCantOpCDTip3", "text": {"index": 2105000206, "text": "无法拆除:你只能在建造后{0:N0}秒内进行拆除。"}}, {"id": 69, "constName": "BuildCantOpCDTip5", "text": {"index": 106884982, "text": "无法修理:你只能在建造后{0:N0}秒内进行修理。"}}, {"id": 70, "constName": "BuildDamageCantOpTip1", "text": {"index": 114425233, "text": "无法修理:最近受损，你只能在{0:N0}秒后进行修理。"}}, {"id": 71, "constName": "BuildDamageCantOpTip2", "text": {"index": 223169856, "text": "无法回收:最近受损，你只能在{0:N0}秒后进行回收。"}}, {"id": 73, "constName": "BuildDamageCantOpTip4", "text": {"index": 1159036085, "text": "无法拆除:最近受损，你只能在{0:N0}秒后进行拆除。"}}, {"id": 74, "constName": "BuildDamageCantOpTip5", "text": {"index": 697316144, "text": "无法旋转:最近受损，你只能在{0:N0}秒后进行旋转。"}}, {"id": 75, "constName": "LoadingScene", "text": {"index": 2068850943, "text": "正在绘制地形地貌…"}}, {"id": 76, "constName": "WaitForBuidRecover", "text": {"index": 212172717, "text": "正在搭建领地建筑…"}}, {"id": 77, "constName": "WaitForStreamLoadFinish", "text": {"index": 978073293, "text": "正在部署树木矿石…"}}, {"id": 78, "constName": "WaitForStreamLoad", "text": {"index": 614879921, "text": "开始部署树木矿石…"}}, {"id": 79, "constName": "ServerNotUsable", "text": {"index": 1347183886, "text": "此战局正在维护中，暂不可用，请尝试其他战局。"}}, {"id": 80, "constName": "WaitForSnapshot", "text": {"index": 842973435, "text": "正在同步动态信息…"}}, {"id": 81, "constName": "UserInLogin", "text": {"index": 115553351, "text": "用户{0}登录中"}}, {"id": 82, "constName": "WaitGameSync", "text": {"index": 451909534, "text": "等待战局数据同步"}}, {"id": 83, "constName": "DisconnectExitTips", "text": {"index": 2073155871, "text": "战局连接断开，请重新进入游戏"}}, {"id": 84, "constName": "ServerDisconnect", "text": {"index": 1096251895, "text": "与战局断开连接，是否重试?"}}, {"id": 85, "constName": "ReconnectGame", "text": {"index": 549490061, "text": "尝试重连服务器...{0}/{1}"}}, {"id": 86, "constName": "SvrGroupOfficial", "text": {"index": 735754653, "text": "官方"}}, {"id": 87, "constName": "SvrGroupPublic", "text": {"index": 1623215723, "text": "trunk"}}, {"id": 88, "constName": "SvrGroupOutnet", "text": {"index": 1927012983, "text": "performance"}}, {"id": 89, "constName": "SvrGroupDesign", "text": {"index": 792852214, "text": "策划"}}, {"id": 90, "constName": "SvrGroupProgrammer", "text": {"index": 650464349, "text": "程序"}}, {"id": 91, "constName": "SvrGroupLocal", "text": {"index": 328587553, "text": "本地"}}, {"id": 92, "constName": "SvrGroupMap", "text": {"index": 1219197451, "text": "地图验证"}}, {"id": 93, "constName": "SvrGroupBomb", "text": {"index": 1066607797, "text": "炸家"}}, {"id": 94, "constName": "TeamLeave", "text": {"index": 482475198, "text": "是否离开当前队伍"}}, {"id": 95, "constName": "TeamKickout", "text": {"index": 407566930, "text": "是否将{0}踢出队伍"}}, {"id": 96, "constName": "TeamLeaderChange", "text": {"index": 1864793932, "text": "你确定要把队长权限让给队员吗？\n（一旦转移，无法撤回！）"}}, {"id": 97, "constName": "TeamRightChange", "text": {"index": 458090632, "text": "给与权限后，该玩家可向其他玩家发送组队邀请，同时可以请离普通队员，是否给与权限？"}}, {"id": 98, "constName": "TeamRightRemove", "text": {"index": 1014477421, "text": "是否移除该队友的权限?"}}, {"id": 99, "constName": "IfReconnect", "text": {"index": 1122184351, "text": "与服务器断开连接, 点击重试或返回登录界面"}}, {"id": 100, "constName": "TryToReconnect", "text": {"index": 10321205, "text": "尝试重连"}}, {"id": 101, "constName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "text": {"index": 1034618756, "text": "返回大厅"}}, {"id": 102, "constName": "BedSuccessGive", "text": {"index": 1503610359, "text": "赠送成功"}}, {"id": 103, "constName": "BedSuccessGet", "text": {"index": 1560589016, "text": "{0}赠送了你新的床位"}}, {"id": 104, "constName": "Bed<PERSON>ena<PERSON>", "text": {"index": 1165410858, "text": "床铺命名"}}, {"id": 105, "constName": "Give", "text": {"index": 1939977617, "text": "赠送"}}, {"id": 106, "constName": "BedGiveConfirm", "text": {"index": 1833542167, "text": "是否确认赠送？"}}, {"id": 107, "constName": "StartCleanGameData", "text": {"index": 328141494, "text": "开始清理游戏世界"}}, {"id": 108, "constName": "SvrNotMatchReason", "text": {"index": 1991816901, "text": "客户端的{0}与服务器不匹配，此客户端被禁止登录"}}, {"id": 109, "constName": "SvrNotMatchTips", "text": {"index": 976665568, "text": "服务器版本不匹配"}}, {"id": 110, "constName": "SvrFailLogin", "text": {"index": 691953480, "text": "服务器登录失败: {0}"}}, {"id": 111, "constName": "DanceWave", "text": {"index": 2028050233, "text": "组队"}}, {"id": 112, "constName": "DanceCheck", "text": {"index": 2031525485, "text": "检视"}}, {"id": 113, "constName": "ResearchCost", "text": {"index": 1611624415, "text": "研究花费"}}, {"id": 114, "constName": "HUDBloodNoDamage", "text": {"index": 60363542, "text": "无敌"}}, {"id": 115, "constName": "InReconnect", "text": {"index": 633781420, "text": "重连大厅中......{0}"}}, {"id": 116, "constName": "StartToMake", "text": {"index": 997355689, "text": "立即制作"}}, {"id": 117, "constName": "ExclusivePart", "text": {"index": 866697118, "text": "互斥部位："}}, {"id": 118, "constName": "ExclusiveFull", "text": {"index": 1279210360, "text": "全身"}}, {"id": 119, "constName": "ExclusiveHelmet", "text": {"index": 1363282401, "text": "头盔"}}, {"id": 120, "constName": "ExclusiveGlasses", "text": {"index": 1741901296, "text": "眼镜"}}, {"id": 121, "constName": "ExclusiveChin", "text": {"index": 877561999, "text": "下巴"}}, {"id": 122, "constName": "ExclusiveUpArmor", "text": {"index": 1318344096, "text": "上身护甲"}}, {"id": 123, "constName": "ExclusiveUpLining", "text": {"index": 1953481193, "text": "上身内衬"}}, {"id": 124, "constName": "ExclusiveGlove", "text": {"index": 96065882, "text": "手套"}}, {"id": 125, "constName": "ExclusiveTrousers", "text": {"index": 1673200380, "text": "裤子"}}, {"id": 126, "constName": "ExclusiveLowerArmor", "text": {"index": 1857444189, "text": "下身护甲"}}, {"id": 127, "constName": "ExclusiveShoe", "text": {"index": 1697064310, "text": "鞋子"}}, {"id": 128, "constName": "EquipProtect", "text": {"index": 1404748656, "text": "保护"}}, {"id": 129, "constName": "EquipFull", "text": {"index": 1483041933, "text": "全身"}}, {"id": 130, "constName": "EquipHead", "text": {"index": 270748985, "text": "头部"}}, {"id": 131, "constName": "EquipUpper", "text": {"index": 1189137339, "text": "上半身"}}, {"id": 132, "constName": "EquipLower", "text": {"index": 2080521043, "text": "下半身"}}, {"id": 133, "constName": "BackpackFull", "text": {"index": 791728147, "text": "背包已满, 卸下的配件将会丢弃, 是否继续？"}}, {"id": 134, "constName": "FixTips1", "text": {"index": 1648523207, "text": "把物品拖动到这里进行修复。"}}, {"id": 135, "constName": "FixTips2", "text": {"index": 1961755139, "text": "此物品无法修理。"}}, {"id": 136, "constName": "FixTips3", "text": {"index": 2002237588, "text": "学会制作该物品后才能进行修理。"}}, {"id": 137, "constName": "FixTips5", "text": {"index": 1523554122, "text": "此物品无需修理。"}}, {"id": 139, "constName": "ChatNoMsg", "text": {"index": 1445472251, "text": "暂无消息"}}, {"id": 140, "constName": "ChatClickToInput", "text": {"index": 405995391, "text": "点击输入"}}, {"id": 141, "constName": "ChatSend", "text": {"index": 1268562713, "text": "发送"}}, {"id": 142, "constName": "ChatNoChannel", "text": {"index": 1387319046, "text": "未选择频道"}}, {"id": 143, "constName": "ChatNoMoreMsg", "text": {"index": 2026182531, "text": "没有更多消息了"}}, {"id": 144, "constName": "ChatUnreadMsg", "text": {"index": 825237511, "text": "{0}条未读消息"}}, {"id": 145, "constName": "ChatNewMsg", "text": {"index": 1384259493, "text": "{0}条新消息"}}, {"id": 146, "constName": "ChatYesterdayTime", "text": {"index": 9869673, "text": "昨天"}}, {"id": 147, "constName": "ChatDayTime1", "text": {"index": 1759270027, "text": "星期一"}}, {"id": 148, "constName": "ChatDayTime2", "text": {"index": 563619385, "text": "星期二"}}, {"id": 149, "constName": "ChatDayTime3", "text": {"index": 1966335972, "text": "星期三"}}, {"id": 150, "constName": "ChatDayTime4", "text": {"index": 1725624151, "text": "星期四"}}, {"id": 151, "constName": "ChatDayTime5", "text": {"index": 648175726, "text": "星期五"}}, {"id": 152, "constName": "ChatDayTime6", "text": {"index": 606487871, "text": "星期六"}}, {"id": 153, "constName": "ChatDayTime7", "text": {"index": 1056189893, "text": "星期日"}}, {"id": 154, "constName": "BpTypeCommon", "text": {"index": 1402790459, "text": "常用"}}, {"id": 155, "constName": "BpTypeFavor", "text": {"index": 664866907, "text": "收藏"}}, {"id": 156, "constName": "NeedWorkBenchLevel", "text": {"index": 458884972, "text": "需要{0}级工作台"}}, {"id": 157, "constName": "SettingSyncPop", "text": {"index": 1859343892, "text": "将按钮方案同步到该玩法，核心按钮包括："}}, {"id": 158, "constName": "SettingSyncTitle", "text": {"index": 1408470667, "text": "自定义同步"}}, {"id": 159, "constName": "QuickJoinConfirm", "text": {"index": 959526206, "text": "加入上一次游玩的服务器{0}？"}}, {"id": 160, "constName": "SettingBtnRAttack", "text": {"index": 315309534, "text": "右侧攻击按钮"}}, {"id": 161, "constName": "SettingBtnLAttack", "text": {"index": 944149131, "text": "左侧攻击按钮"}}, {"id": 162, "constName": "SettingBtnMove", "text": {"index": 805761050, "text": "移动控制按钮"}}, {"id": 163, "constName": "SettingBtnJump", "text": {"index": 1119115416, "text": "跳跃按钮"}}, {"id": 164, "constName": "SettingBtnMirror", "text": {"index": 860083762, "text": "开镜按钮"}}, {"id": 165, "constName": "SettingBtnCrouch", "text": {"index": 288239305, "text": "下蹲按钮"}}, {"id": 166, "constName": "SettingBtnReload", "text": {"index": 1232944981, "text": "换弹按钮"}}, {"id": 167, "constName": "SettingBtnShortcuts", "text": {"index": 33260401, "text": "快捷栏按钮"}}, {"id": 168, "constName": "SettingBtnBackpack", "text": {"index": 1788357987, "text": "背包按钮"}}, {"id": 169, "constName": "HUDSwitchTips", "text": {"index": 1035042266, "text": "当前配置未保存，是否切换并保存？"}}, {"id": 170, "constName": "HudDirectlySwitch", "text": {"index": 1446970876, "text": "直接切换"}}, {"id": 171, "constName": "HudSaveSwitch", "text": {"index": 118447964, "text": "保存并切换"}}, {"id": 172, "constName": "HudSaveTips", "text": {"index": 504102451, "text": "当前配置未保存，是否保存后退出？"}}, {"id": 173, "constName": "HudDirectlyExit", "text": {"index": 619218634, "text": "直接退出"}}, {"id": 174, "constName": "HudSaveExit", "text": {"index": 943108606, "text": "保存并退出"}}, {"id": 175, "constName": "SeriouslyInjured", "text": {"index": 1830496673, "text": "身负重伤"}}, {"id": 176, "constName": "SeriouslyInjuredTips", "text": {"index": 1444629651, "text": "如果您没有及时救助，您将被淘汰或有{0}的机会原地重生"}}, {"id": 177, "constName": "RescueSelf", "text": {"index": 73797882, "text": "自救"}}, {"id": 178, "constName": "NoMedicineToRescue", "text": {"index": 1530559594, "text": "没有医疗包用于自救"}}, {"id": 179, "constName": "FallDamageCantRescue", "text": {"index": 952176386, "text": "垂落伤无法自救"}}, {"id": 180, "constName": "NoMedicineToRescueTips", "text": {"index": 1508155663, "text": "您的背包中没有足够的医疗包用于自救"}}, {"id": 181, "constName": "FallDamageCantRescueTips", "text": {"index": 1029053930, "text": "垂落受伤时无法使用医疗包进行自救"}}, {"id": 182, "constName": "NoAidBox", "text": {"index": 2020785090, "text": "没有医疗包用于治疗"}}, {"id": 183, "constName": "InEmergency", "text": {"index": 1777811643, "text": "治疗中"}}, {"id": 184, "constName": "InRescue", "text": {"index": 1794432833, "text": "救援中"}}, {"id": 185, "constName": "RescueOthers", "text": {"index": 1656598115, "text": "救援"}}, {"id": 186, "constName": "HudElect", "text": {"index": 811247533, "text": "电力"}}, {"id": 187, "constName": "HudConstruction", "text": {"index": 1282802725, "text": "建造"}}, {"id": 188, "constName": "ElecInterface", "text": {"index": 1221359110, "text": "功率      电力端口"}}, {"id": 190, "constName": "BuildUpdateLevelComplete", "text": {"index": 304014840, "text": "已经升到当前等级"}}, {"id": 191, "constName": "ProtectPropHead", "text": {"index": 234135434, "text": "头部防御属性"}}, {"id": 192, "constName": "ProtectPropUpper", "text": {"index": 2036802711, "text": "上身防御属性"}}, {"id": 193, "constName": "ProtectPropLower", "text": {"index": 1184802917, "text": "下身防御属性"}}, {"id": 194, "constName": "AttrProtectFromGun", "text": {"index": 1844113282, "text": "弹药防御"}}, {"id": 195, "constName": "AttrProtectFromMelee", "text": {"index": 593881921, "text": "近战防御"}}, {"id": 196, "constName": "AttrProtectFromExplosion", "text": {"index": 1906128470, "text": "抵御爆炸"}}, {"id": 197, "constName": "AttrProtectFromCold", "text": {"index": 601369171, "text": "抵御寒冷"}}, {"id": 198, "constName": "AttrProtectFromRadiotion", "text": {"index": 441325921, "text": "抵御辐射"}}, {"id": 199, "constName": "AttrProtectFromAnimal", "text": {"index": 650676316, "text": "啮咬防御"}}, {"id": 200, "constName": "AttrWholeAttr", "text": {"index": 1041117326, "text": "全身防御属性"}}, {"id": 201, "constName": "ItemUseSplit", "text": {"index": 1544221321, "text": "拆分"}}, {"id": 202, "constName": "ItemUseSplitToBackpack", "text": {"index": 764333407, "text": "拆入背包"}}, {"id": 203, "constName": "ItemUseSplitToOther", "text": {"index": 31759517, "text": "拆入其他容器"}}, {"id": 204, "constName": "ItemUsePartDrop", "text": {"index": 259319878, "text": "部分丢弃"}}, {"id": 205, "constName": "ItemUseMove", "text": {"index": 1123211208, "text": "移至{0}"}}, {"id": 206, "constName": "ItemUseDropWater", "text": {"index": 1604778312, "text": "倒水"}}, {"id": 207, "constName": "ItemUseEat", "text": {"index": 1913835067, "text": "食用"}}, {"id": 208, "constName": "ItemUseDrink", "text": {"index": 1136962348, "text": "饮用"}}, {"id": 209, "constName": "ItemUseLearnBp", "text": {"index": 1523550083, "text": "学习蓝图"}}, {"id": 210, "constName": "ItemUseTreat", "text": {"index": 660883059, "text": "医疗"}}, {"id": 211, "constName": "ItemUseEquip", "text": {"index": 46056450, "text": "装备"}}, {"id": 212, "constName": "ItemUse", "text": {"index": 1379841750, "text": "使用"}}, {"id": 213, "constName": "ItemUseUnloadAmmo", "text": {"index": 1668275154, "text": "取出子弹"}}, {"id": 214, "constName": "ItemUseUnloadPart", "text": {"index": 591468972, "text": "卸下配件"}}, {"id": 215, "constName": "ItemUseTakeOff", "text": {"index": 1992349891, "text": "卸下装备"}}, {"id": 216, "constName": "ItemUseDrop", "text": {"index": 2034077118, "text": "丢弃"}}, {"id": 217, "constName": "ItemUseMoveToInventory", "text": {"index": 2018017595, "text": "移至背包"}}, {"id": 218, "constName": "ItemUseMoveToBox", "text": {"index": 1177101474, "text": "移至其他容器"}}, {"id": 219, "constName": "ItemUseRecharge", "text": {"index": 1353301433, "text": "充能"}}, {"id": 220, "constName": "SvrDescTest", "text": {"index": 965907683, "text": "CE测试专用服务器，请遵循现场人员指引进行游戏"}}, {"id": 221, "constName": "SvrParseError", "text": {"index": 283786387, "text": "Fail to deserialize the server info, serverId = {0}, error = {1}"}}, {"id": 223, "constName": "NameCardNoModify", "text": {"index": 1631255518, "text": "与原昵称相同"}}, {"id": 224, "constName": "NameCardEmpty", "text": {"index": 608754413, "text": "名称不能为空。"}}, {"id": 225, "constName": "NameCardRuler", "text": {"index": 1780629205, "text": "输入的昵称过短"}}, {"id": 227, "constName": "SettingBackToLobby", "text": {"index": 1176378315, "text": "返回大厅"}}, {"id": 228, "constName": "SettingLogout", "text": {"index": 40111937, "text": "退出登录"}}, {"id": 229, "constName": "SettingAdjustTips", "text": {"index": 247190489, "text": "请根据个人习惯调整"}}, {"id": 230, "constName": "SettingGameView", "text": {"index": 1936848927, "text": "人称功能设置"}}, {"id": 231, "constName": "SettingBackToLobbyAsk", "text": {"index": 2113231245, "text": "是否暂离战局，返回大厅？\n（请在领地建筑内或使用野营帐篷安全下线）"}}, {"id": 232, "constName": "SettingLogoutAsk", "text": {"index": 499341893, "text": "是否退出登录？"}}, {"id": 233, "constName": "SettingTabCommon", "text": {"index": 394696335, "text": "通用"}}, {"id": 234, "constName": "SettingTabTool", "text": {"index": 642979253, "text": "道具"}}, {"id": 235, "constName": "SettingTabBattle", "text": {"index": 1714426743, "text": "战斗"}}, {"id": 236, "constName": "SettingTabLevel", "text": {"index": 520174463, "text": "关卡"}}, {"id": 237, "constName": "SettingTabBuild", "text": {"index": 469836271, "text": "建造"}}, {"id": 238, "constName": "SettingRole", "text": {"index": 1575632911, "text": "角色"}}, {"id": 239, "constName": "SettingScene", "text": {"index": 369291255, "text": "场景"}}, {"id": 240, "constName": "SettingChangeProp", "text": {"index": 477759347, "text": "更改属性"}}, {"id": 241, "constName": "SettingSvr", "text": {"index": 1532503655, "text": "服务器"}}, {"id": 242, "constName": "SettingTask", "text": {"index": 1178041514, "text": "任务"}}, {"id": 243, "constName": "SettingContainer", "text": {"index": 1597772197, "text": "容器修改"}}, {"id": 244, "constName": "SettingItemConfig", "text": {"index": 551160813, "text": "配置道具"}}, {"id": 245, "constName": "SettingItemReborn", "text": {"index": 1715752929, "text": "出生道具"}}, {"id": 246, "constName": "SettingSensitive", "text": {"index": 128598865, "text": "灵敏度"}}, {"id": 247, "constName": "SettingCamera", "text": {"index": 1483827943, "text": "镜头"}}, {"id": 248, "constName": "SettingWeapon", "text": {"index": 1156513149, "text": "枪械"}}, {"id": 249, "constName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text": {"index": 1569268141, "text": "渲染"}}, {"id": 250, "constName": "SettingOp", "text": {"index": 1246183336, "text": "操作"}}, {"id": 251, "constName": "SettingAimAssist", "text": {"index": 594669862, "text": "辅助瞄准"}}, {"id": 252, "constName": "SettingLog", "text": {"index": 1035737673, "text": "日志打印"}}, {"id": 253, "constName": "SettingAirDrop", "text": {"index": 42046323, "text": "空投"}}, {"id": 254, "constName": "SettingMonster", "text": {"index": 1308730599, "text": "怪物"}}, {"id": 255, "constName": "SettingLight", "text": {"index": 15492831, "text": "光照"}}, {"id": 256, "constName": "SettingBasic", "text": {"index": 1169409311, "text": "基础设定"}}, {"id": 257, "constName": "TeamInAiding", "text": {"index": 1783169605, "text": "救援中"}}, {"id": 258, "constName": "TeamIsDied", "text": {"index": 1144852822, "text": "被淘汰"}}, {"id": 259, "constName": "TeamIsWounded", "text": {"index": 90545531, "text": "倒地"}}, {"id": 260, "constName": "TeamIsLive", "text": {"index": 2071276723, "text": "在线"}}, {"id": 261, "constName": "TeamIsOffline", "text": {"index": 1861556653, "text": "离线"}}, {"id": 262, "constName": "TeamSendMsg", "text": {"index": 1194857668, "text": "发送消息"}}, {"id": 263, "constName": "Team<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "text": {"index": 1512294654, "text": "转移队长"}}, {"id": 264, "constName": "TeamKickoutMate", "text": {"index": 287669689, "text": "踢出队友"}}, {"id": 265, "constName": "TeamFeachBackRight", "text": {"index": 315357108, "text": "收回权限"}}, {"id": 266, "constName": "TeamGiveOutRight", "text": {"index": 1634253723, "text": "给与权限"}}, {"id": 267, "constName": "SettingFreeCameraSenstive", "text": {"index": 189576841, "text": "自由镜头灵敏度"}}, {"id": 268, "constName": "SettingCameraSenstive", "text": {"index": 1419255586, "text": "镜头灵敏度（用于不开火情况下，滑动屏幕时镜头转动的灵敏度）"}}, {"id": 269, "constName": "SettingFireCameraSenstive", "text": {"index": 763437900, "text": "开火镜头灵敏度（用于开火情况下，划动屏幕时镜头转动的灵敏度，可用于压枪操作）"}}, {"id": 270, "constName": "SettingGyroSensitive", "text": {"index": 231919598, "text": "陀螺仪灵敏度（用于陀螺仪开启时，通过调整手机姿态来转动镜头的灵敏度）"}}, {"id": 271, "constName": "SettingFireGyroSensitive", "text": {"index": 154641608, "text": "开火陀螺仪灵敏度（用于陀螺仪开启时，通过调整手机姿态来转动开火时镜头的灵敏度）"}}, {"id": 272, "constName": "TeamInputSearch", "text": {"index": 1832909283, "text": "请输入玩家昵称或编号"}}, {"id": 274, "constName": "TeamCurSvr", "text": {"index": 257080430, "text": "本服"}}, {"id": 275, "constName": "TeamCDTime", "text": {"index": 1776777369, "text": "{0} 秒"}}, {"id": 276, "constName": "TeamRefresh", "text": {"index": 850812936, "text": "刷新"}}, {"id": 277, "constName": "GameModeReadyTitle", "text": {"index": 750393854, "text": "离线抄家 - 【{0}】 \\n 【{1}】"}}, {"id": 278, "constName": "GameModeExitConfirm", "text": {"index": 1944301756, "text": "是否要退出服务器？"}}, {"id": 279, "constName": "GameModeLeaderLabel", "text": {"index": 907983783, "text": "队长"}}, {"id": 280, "constName": "GameModeSelectConfirm", "text": {"index": 1136888170, "text": "确认"}}, {"id": 281, "constName": "GameModeSelectCancel", "text": {"index": 1618854267, "text": "取消"}}, {"id": 282, "constName": "ElecCableColor", "text": {"index": 1131561660, "text": "电线颜色"}}, {"id": 283, "constName": "ElecInUse", "text": {"index": 491431245, "text": "使用中"}}, {"id": 284, "constName": "ElecUse", "text": {"index": 36286231, "text": "使用"}}, {"id": 285, "constName": "ChatPlayerAlreadyInTeam", "text": {"index": 1577453926, "text": "玩家已经是队友"}}, {"id": 286, "constName": "ChatSendMsg", "text": {"index": 1805240599, "text": "发送消息"}}, {"id": 287, "constName": "CharInvite", "text": {"index": 756806397, "text": "邀请入队"}}, {"id": 288, "constName": "GameModeCampExit", "text": {"index": 557764141, "text": "退出"}}, {"id": 289, "constName": "GameModeCampWatch", "text": {"index": 1581456616, "text": "观战"}}, {"id": 290, "constName": "GameModeCampWatchConfirm", "text": {"index": 1043569995, "text": "观战确认"}}, {"id": 291, "constName": "GameModeCampExitConfirm", "text": {"index": 22585065, "text": "退出确认"}}, {"id": 292, "constName": "GameModeCampDefend", "text": {"index": 386829696, "text": "防守"}}, {"id": 293, "constName": "GameModeCampAttack", "text": {"index": 1647774285, "text": "进攻"}}, {"id": 294, "constName": "GameModeCampSelect", "text": {"index": 1858667778, "text": "选择阵营"}}, {"id": 295, "constName": "GameModeCampFull", "text": {"index": 1981155530, "text": "加入失败，当前选择阵营人数已达上限"}}, {"id": 296, "constName": "GameModeCampSame", "text": {"index": 578477748, "text": "加入失败，所选阵营与当前阵营一致"}}, {"id": 297, "constName": "GameModeCampError", "text": {"index": 1752550987, "text": "加入失败，此阵营不存在"}}, {"id": 298, "constName": "GameModeNotInGame", "text": {"index": 1806080102, "text": "加入失败，与玩法服务器已断开链接"}}, {"id": 299, "constName": "GameModeStageBan", "text": {"index": 1145245861, "text": "加入失败，当前阶段不允许切换阵营"}}, {"id": 300, "constName": "GameModeWait1", "text": {"index": 14942777, "text": "正在搭建防御体系，请稍候"}}, {"id": 301, "constName": "GameModeWait2", "text": {"index": 361179280, "text": "宝箱内物资清单更新中"}}, {"id": 302, "constName": "GameModeWait3", "text": {"index": 507643819, "text": "正在更新领地权限设置"}}, {"id": 303, "constName": "NameBackpack", "text": {"index": 529095024, "text": "仓库"}}, {"id": 304, "constName": "CollectorNotHave", "text": {"index": 995130244, "text": "未拥有"}}, {"id": 305, "constName": "CollectorCantTransWater", "text": {"index": 1070004283, "text": "不可转移水"}}, {"id": 306, "constName": "ElecFull", "text": {"index": 2099792464, "text": "已充满"}}, {"id": 307, "constName": "ElecExitCable", "text": {"index": 2041395560, "text": "确认退出布线吗？ 会清除此条电缆所有的已部署的电路走线"}}, {"id": 308, "constName": "NameFixItem", "text": {"index": 346273737, "text": "修理物"}}, {"id": 309, "constName": "FixMaterialCheck", "text": {"index": 1488090946, "text": "修理所需材料不足。"}}, {"id": 310, "constName": "CollectorNoBottle", "text": {"index": 737848214, "text": "选择一个水瓶或塑料水桶，然后用它来倒入或灌取露水收集器中的水"}}, {"id": 311, "constName": "CollectorNotSameWaterType", "text": {"index": 542014612, "text": "装有盐水的容器无法倒入或灌取水。请先倒空盐水或选择盛有淡水的容器。"}}, {"id": 312, "constName": "CollectorTakeFullBottle", "text": {"index": 629173996, "text": "当前容器里已装满水，无法取水。"}}, {"id": 313, "constName": "CollectorTakeEmptyCatcher", "text": {"index": 562360396, "text": "露水收集器里没有水，无法灌取水。"}}, {"id": 314, "constName": "CollectorGiveFullCatcher", "text": {"index": 1864589572, "text": "露水收集器里已装满水，无法注入水。"}}, {"id": 315, "constName": "CollectorGiveEmptyBottle", "text": {"index": 706605596, "text": "当前容器里没有水，无法注入水。"}}, {"id": 316, "constName": "SettingOpConfig", "text": {"index": 167236329, "text": "操作设置"}}, {"id": 317, "constName": "SettingFillParams", "text": {"index": 2124848559, "text": "填写参数"}}, {"id": 318, "constName": "BackpackItemNum", "text": {"index": 1356042319, "text": "背包中数量 {0}"}}, {"id": 319, "constName": "SleepTips", "text": {"index": 2143165034, "text": "你正在睡觉"}}, {"id": 320, "constName": "PCSleepTips", "text": {"index": 1218560666, "text": "(按任意键唤醒)"}}, {"id": 321, "constName": "MobileSleepTips", "text": {"index": 1613537129, "text": "(点击任意位置唤醒)"}}, {"id": 322, "constName": "IsRefuseAll", "text": {"index": 328882, "text": "是否拒绝全部组队邀请？"}}, {"id": 323, "constName": "HudInvitePanel", "text": {"index": 206255212, "text": "{0}\n邀请您加入他的队伍"}}, {"id": 324, "constName": "BuildLevelAssetPreLoad", "text": {"index": 16992443, "text": "正在刷新领地建筑等级…"}}, {"id": 325, "constName": "BuildAssetPreLoad", "text": {"index": 877217470, "text": "正在恢复领地设施状态…"}}, {"id": 326, "constName": "LoadAlwaysInMemAB", "text": {"index": 450346999, "text": "正在加载角色基础信息…"}}, {"id": 327, "constName": "UnloadAlwaysInMemAB", "text": {"index": 943687398, "text": "正在卸载角色基础信息…"}}, {"id": 328, "constName": "AssetDownloadScene", "text": {"index": 1962179533, "text": "正在从云端获取新地图…"}}, {"id": 329, "constName": "AssetLoadLocalScene", "text": {"index": 1285843915, "text": "正在从本地载入地形图…"}}, {"id": 330, "constName": "AssetLoadAB", "text": {"index": 1573223052, "text": "正在获取补充内容清单…"}}, {"id": 331, "constName": "AssetLoadSceneFromAB", "text": {"index": 1635812185, "text": "正在从清单中载入地图…"}}, {"id": 332, "constName": "AssetLoadDesignScene", "text": {"index": 58827366, "text": "正在构建岛屿遗迹信息…"}}, {"id": 333, "constName": "AssetStartUnloadScene", "text": {"index": 845377137, "text": "正在抹除地形信息…"}}, {"id": 334, "constName": "AssetLoadPreloadAssets", "text": {"index": 385334005, "text": "正在启动…"}}, {"id": 335, "constName": "AssetLoadScene", "text": {"index": 741900064, "text": "正在加载地图：{0}"}}, {"id": 336, "constName": "AssetFailToLoadSceneAB", "text": {"index": 27306930, "text": "加载补充地图内容失败："}}, {"id": 337, "constName": "AssetFailToLoadScene", "text": {"index": 812998782, "text": "加载地图失败"}}, {"id": 338, "constName": "AssetDownloadSceneAssets", "text": {"index": 1491784236, "text": "正在从云端获取地图信息，速度：{0}"}}, {"id": 339, "constName": "AssetFailToDownloadAssets", "text": {"index": 393929591, "text": "下载资源包失败。"}}, {"id": 340, "constName": "AssetBrokenAssetPreload", "text": {"index": 1537044883, "text": "正在拼装摆件设施…"}}, {"id": 341, "constName": "AssetShortcutsAssetPreload", "text": {"index": 1606555708, "text": "正在准备快捷栏位…"}}, {"id": 342, "constName": "AssetMonsterAssetPreload", "text": {"index": 1249391724, "text": "正在唤醒遗迹守卫…"}}, {"id": 343, "constName": "AssetCommonAssetPreload", "text": {"index": 1114004001, "text": "正在装填背包道具…"}}, {"id": 344, "constName": "AssetMapAssetPreload", "text": {"index": 1579745543, "text": "正在绘制完整地图…"}}, {"id": 345, "constName": "AssetGoAssetPreload", "text": {"index": 1749016425, "text": "正在建立岛外通讯…"}}, {"id": 346, "constName": "AssetAssetPreload", "text": {"index": 1941025600, "text": "正在提前载入资源…"}}, {"id": 347, "constName": "DetailFacility", "text": {"index": 1879830965, "text": "设施"}}, {"id": 348, "constName": "DetailMakeBp", "text": {"index": 1091465357, "text": "制作方式"}}, {"id": 349, "constName": "DetailSee", "text": {"index": 1967264899, "text": "查看"}}, {"id": 350, "constName": "DetailBpUnlockCost", "text": {"index": 375303449, "text": "蓝图解锁消耗"}}, {"id": 351, "constName": "DetailResearchReq", "text": {"index": 701696997, "text": "逆向研究解锁"}}, {"id": 352, "constName": "DetailRecycledFor", "text": {"index": 1189326091, "text": "分解可得"}}, {"id": 353, "constName": "DetailRecycledFrom", "text": {"index": 1919978088, "text": "可通过分解以下物品获得"}}, {"id": 354, "constName": "DetailDropSource", "text": {"index": 511800572, "text": "信息"}}, {"id": 355, "constName": "DetailTradePos", "text": {"index": 129698950, "text": "地点"}}, {"id": 356, "constName": "DetailTradePrice", "text": {"index": 126129210, "text": "交易价格"}}, {"id": 357, "constName": "DetailCanUseToCraft", "text": {"index": 261013988, "text": "可用于制作"}}, {"id": 358, "constName": "SettingMainBasic", "text": {"index": 566632951, "text": "基础设置"}}, {"id": 359, "constName": "SettingMainOp", "text": {"index": 1171526772, "text": "操作设置"}}, {"id": 360, "constName": "SettingMainSensitive", "text": {"index": 95883849, "text": "灵敏度设置"}}, {"id": 361, "constName": "SettingMainSound", "text": {"index": 778913746, "text": "声音设置"}}, {"id": 362, "constName": "SettingMainGM", "text": {"index": 1943618913, "text": "Gm指令"}}, {"id": 363, "constName": "SettingMainGMItem", "text": {"index": 1716725912, "text": "Gm道具"}}, {"id": 364, "constName": "SettingMainGMMonster", "text": {"index": 1909477826, "text": "Gm刷怪"}}, {"id": 365, "constName": "SettingMainGMCode", "text": {"index": 2065700360, "text": "程序指令"}}, {"id": 366, "constName": "TeamTeam", "text": {"index": 1699034403, "text": "队伍"}}, {"id": 367, "constName": "TeamFriends", "text": {"index": 420012578, "text": "好友"}}, {"id": 368, "constName": "TeamArmy", "text": {"index": 1613521068, "text": "军团"}}, {"id": 369, "constName": "SettingSecondHeadGyro", "text": {"index": 1655982892, "text": "陀螺仪"}}, {"id": 370, "constName": "SettingSecondAimAssist", "text": {"index": 1872441929, "text": "辅助瞄准"}}, {"id": 371, "constName": "SettingSecondItemHighLight", "text": {"index": 57940879, "text": "物品高亮"}}, {"id": 372, "constName": "SettingSecondElecAutoSet", "text": {"index": 1794201811, "text": "电力自动布线"}}, {"id": 373, "constName": "SettingSecondGM", "text": {"index": 1300905583, "text": "蓝牙耳机效果增强"}}, {"id": 374, "constName": "SettingSecondBuildSize", "text": {"index": 431297031, "text": "建筑物尺寸"}}, {"id": 375, "constName": "SettingSecondGameQuality", "text": {"index": 1116677618, "text": "游戏质量"}}, {"id": 376, "constName": "SettingSecondGraphQuality", "text": {"index": 438836276, "text": "画面品质"}}, {"id": 377, "constName": "SettingSecondFrame", "text": {"index": 677113240, "text": "帧率设置"}}, {"id": 378, "constName": "SettingSecondAntiAlias", "text": {"index": 813383081, "text": "抗锯齿（立即生效，提高画面平滑，但会增加发热和耗电）"}}, {"id": 379, "constName": "SettingSecondShadow", "text": {"index": 778176518, "text": "阴影（关闭阴影可以降低性能损耗、发热和耗电）"}}, {"id": 380, "constName": "SettingSecondPlayerGod", "text": {"index": 1824866739, "text": "角色无敌"}}, {"id": 381, "constName": "SettingSecondSwitchView", "text": {"index": 433208087, "text": "切换视角"}}, {"id": 382, "constName": "SettingSecondSmooth", "text": {"index": 240716848, "text": "平滑移动"}}, {"id": 383, "constName": "SettingSecondAcceleration", "text": {"index": 636805110, "text": "开启加速"}}, {"id": 384, "constName": "SettingSecondHitSound", "text": {"index": 1104409711, "text": "受击音效测试"}}, {"id": 385, "constName": "SettingSecondDelay", "text": {"index": 1072389331, "text": "延迟补偿"}}, {"id": 386, "constName": "SettingSecondPointTrans", "text": {"index": 615360850, "text": "打点传送"}}, {"id": 387, "constName": "SettingSecondViewkick", "text": {"index": 551963283, "text": "镜头抖动"}}, {"id": 388, "constName": "SettingSecondGunkick", "text": {"index": 1335359671, "text": "枪械抖动"}}, {"id": 389, "constName": "SettingSecondSway", "text": {"index": 575157929, "text": "Sway"}}, {"id": 390, "constName": "SettingSecondJoystick", "text": {"index": 1789454880, "text": "移动轮盘"}}, {"id": 391, "constName": "SettingSecondFireAssistBox", "text": {"index": 864781738, "text": "开火辅助框"}}, {"id": 392, "constName": "SettingSecondTarReductionFrame", "text": {"index": 807326643, "text": "目标周围减速框"}}, {"id": 393, "constName": "SettingSecondAmiAssistTool", "text": {"index": 703628502, "text": "辅助瞄准工具"}}, {"id": 394, "constName": "SettingSecondUseAimAssistToolData", "text": {"index": 124947113, "text": "使用辅瞄工具数据"}}, {"id": 395, "constName": "SettingSecond3CTest", "text": {"index": 31479254, "text": "3cTestDebug"}}, {"id": 396, "constName": "SettingSecondDamageLog", "text": {"index": 234211384, "text": "伤害Log"}}, {"id": 397, "constName": "SettingSecondAI", "text": {"index": 263561376, "text": "AI开关"}}, {"id": 398, "constName": "SettingSecondMonsterInvincible", "text": {"index": 2101390056, "text": "怪物无敌"}}, {"id": 399, "constName": "SettingSecondShowPointInfo", "text": {"index": 1063941895, "text": "显示点位信息"}}, {"id": 400, "constName": "SettingSecondDayNight", "text": {"index": 366778251, "text": "昼夜变化"}}, {"id": 401, "constName": "SettingSecondDeadSheepRefresh", "text": {"index": 1391612147, "text": "死羊刷新"}}, {"id": 402, "constName": "SettingSecondShowOtherName", "text": {"index": 89463206, "text": "血条显示非队友角色名"}}, {"id": 403, "constName": "SettingSecondFlyMode", "text": {"index": 313614033, "text": "飞行模式"}}, {"id": 404, "constName": "SettingSecondUnlimitedBuild", "text": {"index": 190785523, "text": "无限建造"}}, {"id": 405, "constName": "SettingSecondObserverMode", "text": {"index": 121559825, "text": "观察者模式"}}, {"id": 406, "constName": "SettingSecondAbDownload", "text": {"index": 1949929461, "text": "ResAb下载"}}, {"id": 407, "constName": "SettingSecondCrushingSwitch", "text": {"index": 769703216, "text": "破碎性能切换"}}, {"id": 408, "constName": "SettingSecondPreBuildAdsorb", "text": {"index": 1684390400, "text": "拟建体碰撞吸附优化"}}, {"id": 409, "constName": "SettingSecondSelectBirthProp", "text": {"index": 1004265742, "text": "选择出生道具"}}, {"id": 410, "constName": "BtnOn", "text": {"index": 966623665, "text": "开"}}, {"id": 411, "constName": "BtnOff", "text": {"index": 269171963, "text": "关"}}, {"id": 412, "constName": "BtnAlwaysOn", "text": {"index": 1282288092, "text": "开"}}, {"id": 413, "constName": "BtnRoutine", "text": {"index": 1285574099, "text": "常规"}}, {"id": 414, "constName": "BtnMedium", "text": {"index": 1684624475, "text": "中等"}}, {"id": 415, "constName": "BtnLarger", "text": {"index": 1819303866, "text": "较大"}}, {"id": 416, "constName": "BtnFireLMFloatRFFix", "text": {"index": 457332977, "text": "左手移动，右手固定开火"}}, {"id": 417, "constName": "BtnFireLMFloatRFFloat", "text": {"index": 936550288, "text": "左手移动，右手跟随开火"}}, {"id": 418, "constName": "BtnFireLMFixRFFix", "text": {"index": 1307642355, "text": "左手固定移动，右手固定开火"}}, {"id": 419, "constName": "BtnVeryLowQuality", "text": {"index": 1767336793, "text": "极低"}}, {"id": 420, "constName": "BtnLowQuality", "text": {"index": 1027205722, "text": "低质量"}}, {"id": 421, "constName": "BtnStandardQuality", "text": {"index": 1481442134, "text": "标准"}}, {"id": 422, "constName": "BtnHDQuality", "text": {"index": 1088472549, "text": "高清"}}, {"id": 428, "constName": "BtnMoveFixed", "text": {"index": 1664281569, "text": "固定"}}, {"id": 429, "constName": "BtnMoveFloating", "text": {"index": 325690961, "text": "浮动"}}, {"id": 430, "constName": "BtnMoveDynamic", "text": {"index": 503173469, "text": "动态"}}, {"id": 431, "constName": "BtnMax", "text": {"index": 1969462325, "text": "高"}}, {"id": 432, "constName": "BtnMed", "text": {"index": 831238992, "text": "中"}}, {"id": 433, "constName": "BtnMin", "text": {"index": 1887042867, "text": "低"}}, {"id": 434, "constName": "BtnSpawnATree", "text": {"index": 2101331820, "text": "生成一颗树木"}}, {"id": 435, "constName": "BtnSpawnAOre", "text": {"index": 1028015995, "text": "生成一块矿石"}}, {"id": 436, "constName": "BtnSpawnCollectables", "text": {"index": 141718559, "text": "生成可采集物"}}, {"id": 437, "constName": "BtnAddFireInPlace", "text": {"index": 146021336, "text": "原地添加火堆"}}, {"id": 438, "constName": "BtnChangeSvrTime", "text": {"index": 1336643527, "text": "更改服务器时间"}}, {"id": 439, "constName": "BtnSoundTest", "text": {"index": 2142962303, "text": "音效测试"}}, {"id": 440, "constName": "BtnAdjustComfort", "text": {"index": 1573783254, "text": "调整舒适度"}}, {"id": 441, "constName": "BtnModifyAttr", "text": {"index": 430019983, "text": "修改生存属性"}}, {"id": 442, "constName": "BtnCauseDamage", "text": {"index": 289609633, "text": "造成伤害"}}, {"id": 443, "constName": "BtnAddDummyPlayer", "text": {"index": 1583639846, "text": "添加假玩家(只能1次)"}}, {"id": 444, "constName": "BtnDeleteDummyPlayer", "text": {"index": 2064235066, "text": "删除所有假玩家"}}, {"id": 445, "constName": "BtnAngularConstS", "text": {"index": 1229189860, "text": "角度常数S"}}, {"id": 446, "constName": "BtnSenstiveFactorAX", "text": {"index": 1909294489, "text": "灵敏度系数AX"}}, {"id": 447, "constName": "BtnSenstiveFactorAY", "text": {"index": 93299817, "text": "灵敏度系数AY"}}, {"id": 448, "constName": "BtnGyrosCoefficientLX", "text": {"index": 1575068479, "text": "陀螺仪系数LX"}}, {"id": 449, "constName": "BtnGyrosCoefficientLY", "text": {"index": 1721884517, "text": "陀螺仪系数LY"}}, {"id": 450, "constName": "BtnWeight1", "text": {"index": 1616580612, "text": "权重-W1"}}, {"id": 451, "constName": "BtnWeight2", "text": {"index": 1256587248, "text": "权重-W2"}}, {"id": 452, "constName": "BtnWeight3", "text": {"index": **********, "text": "权重-W3"}}, {"id": 453, "constName": "BtnModifyLerpRate", "text": {"index": 359299655, "text": "修改LerpRate"}}, {"id": 454, "constName": "BtnMinMoveDis", "text": {"index": **********, "text": "最小移动距离"}}, {"id": 455, "constName": "BtnHorizIgnoreTrans", "text": {"index": 216842256, "text": "水平忽略位移"}}, {"id": 456, "constName": "BtnVertIgnoreTrans", "text": {"index": **********, "text": "竖直忽略位移"}}, {"id": 457, "constName": "BtnPitchLimit", "text": {"index": 227964471, "text": "俯仰限制角度"}}, {"id": 458, "constName": "BtnFOVNormal", "text": {"index": 315887869, "text": "FOV-正常"}}, {"id": 459, "constName": "BtnFOVADS", "text": {"index": **********, "text": "FOV-开镜"}}, {"id": 460, "constName": "BtnRenderDelay", "text": {"index": *********, "text": "渲染延迟"}}, {"id": 461, "constName": "BtnSpawnACorpse", "text": {"index": *********, "text": "生成一个尸体"}}, {"id": 462, "constName": "BtnSpawnABox", "text": {"index": **********, "text": "生成一个宝箱"}}, {"id": 463, "constName": "BtnCreateDeadSheep", "text": {"index": *********, "text": "召唤死羊建筑"}}, {"id": 464, "constName": "BtnSummonMonster", "text": {"index": *********, "text": "召唤怪物"}}, {"id": 465, "constName": "BtnTravelToSpawnPoint", "text": {"index": *********, "text": "传送到出生点"}}, {"id": 466, "constName": "BtnSizeMult", "text": {"index": *********, "text": "尺寸倍率"}}, {"id": 467, "constName": "SettingSecondNightBright", "text": {"index": 1108252846, "text": "夜间亮度调整"}}, {"id": 468, "constName": "SettingSecondFPView", "text": {"index": *********, "text": "第一人称镜头视野"}}, {"id": 469, "constName": "SettingSecondMasterVolume", "text": {"index": 1620868219, "text": "主音量"}}, {"id": 470, "constName": "SettingSecondMicVolume", "text": {"index": 1860358988, "text": "麦克风音量"}}, {"id": 471, "constName": "SettingSecondSpeakerVolume", "text": {"index": 1247716266, "text": "扬声器音量"}}, {"id": 472, "constName": "SettingSecondBattleSoundEffect", "text": {"index": 1745041483, "text": "音效音量"}}, {"id": 473, "constName": "SettingSecondAmbientSound", "text": {"index": *********, "text": "环境音量"}}, {"id": 474, "constName": "SettingSecondFPFreeShot", "text": {"index": 197988942, "text": "第一人称人物自由镜头（小眼睛）"}}, {"id": 475, "constName": "SettingSecondFPDontOpenCamera", "text": {"index": 1279758303, "text": "第一人称不开镜"}}, {"id": 476, "constName": "SettingSecondIronSightsHolograms", "text": {"index": 453642563, "text": "机瞄、全息"}}, {"id": 477, "constName": "SettingSecond8xScope", "text": {"index": 2013909251, "text": "8倍镜"}}, {"id": 478, "constName": "SettingSecondTPVehFreeCamera", "text": {"index": 1533871135, "text": "第三人称人物、载具状态自由镜头（小眼睛）"}}, {"id": 479, "constName": "SettingSecondTPDontOpenCamera", "text": {"index": 677858794, "text": "第三人称不开镜"}}, {"id": 480, "constName": "SettingSecondIronSight", "text": {"index": 2012175952, "text": "金属瞄准镜"}}, {"id": 481, "constName": "SettingSecond16xScope", "text": {"index": 1358635749, "text": "16倍镜"}}, {"id": 482, "constName": "SettingSecondFPS", "text": {"index": 1966238847, "text": "FPS"}}, {"id": 483, "constName": "SettingSecondFillBackpack", "text": {"index": 432457784, "text": "填满背包"}}, {"id": 484, "constName": "SettingSecondUnstuck", "text": {"index": 1030190480, "text": "脱离卡死"}}, {"id": 485, "constName": "SettingSecondAddWeapons", "text": {"index": 394595348, "text": "添加武器"}}, {"id": 486, "constName": "SettingSecondExpandBackpack", "text": {"index": 17253800, "text": "扩充背包"}}, {"id": 487, "constName": "SettingSecondAddAmmo", "text": {"index": 311592051, "text": "增加300子弹"}}, {"id": 488, "constName": "SettingSecondSummonAirDrop", "text": {"index": 1213359305, "text": "召唤空投"}}, {"id": 489, "constName": "SettingSecondTopologyDebug", "text": {"index": 604780866, "text": "拓扑debug/warning"}}, {"id": 490, "constName": "SettingSecondTopologyCaculates", "text": {"index": 1075402039, "text": "拓扑计算密度数据"}}, {"id": 491, "constName": "SettingSecondClearDeadSheep", "text": {"index": 2147124273, "text": "清空死羊建筑"}}, {"id": 492, "constName": "SettingSecondSelfWanted", "text": {"index": 162410541, "text": "自身红名"}}, {"id": 493, "constName": "SettingSecondCancelWanted", "text": {"index": 1004991160, "text": "取消自身红名"}}, {"id": 494, "constName": "SettingSecondDeleteAllBuilding", "text": {"index": 424054513, "text": "删除所有建筑"}}, {"id": 495, "constName": "SettingSecondDeleteMyBuilding", "text": {"index": 1738357831, "text": "删除自己建筑"}}, {"id": 496, "constName": "SettingBtnLanSwitch", "text": {"index": 102677874, "text": "语言设置"}}, {"id": 497, "constName": "SettingBtnLanDev", "text": {"index": 463662173, "text": "开发"}}, {"id": 498, "constName": "SettingBtnLanCh", "text": {"index": 1042037532, "text": "中文"}}, {"id": 499, "constName": "SettingBtnLanEn", "text": {"index": 1494452123, "text": "英语"}}, {"id": 500, "constName": "SettingLanSwitchTips", "text": {"index": 808301495, "text": "确认切换当前显示语言吗? 这将会返回到登录界面"}}, {"id": 501, "constName": "ActTipsSwitchNotEnable", "text": {"index": 673268725, "text": "开关未激活"}}, {"id": 502, "constName": "ActTipsSwitchNotOpen", "text": {"index": 161316962, "text": "开关已开启"}}, {"id": 503, "constName": "InteractiveOp", "text": {"index": 501568990, "text": "操作"}}, {"id": 504, "constName": "InteractiveCallElevator", "text": {"index": 870510686, "text": "呼叫电梯"}}, {"id": 505, "constName": "NearbyRespawn", "text": {"index": 1378128308, "text": "淘汰点附近重生"}}, {"id": 506, "constName": "SettingAutoPickup", "text": {"index": 766869113, "text": "自动拾取"}}, {"id": 507, "constName": "SettingClosePickup", "text": {"index": 906830341, "text": "关闭拾取列表时关闭自动拾取"}}, {"id": 508, "constName": "SettingDieBagAtTop", "text": {"index": 1321774932, "text": "新的淘汰背包是否显示在最上方"}}, {"id": 509, "constName": "SettingAutoPickupType", "text": {"index": 125547467, "text": "自动拾取道具类型"}}, {"id": 510, "constName": "SettingSwitchHud", "text": {"index": 70747157, "text": "HUD方案"}}, {"id": 511, "constName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text": {"index": 787013776, "text": "拾取设置"}}, {"id": 512, "constName": "KatyushaCoolDown", "text": {"index": 1563902484, "text": "冷却({0})"}}, {"id": 513, "constName": "KatyushaCannotFire", "text": {"index": 1145731430, "text": "无法发射"}}, {"id": 514, "constName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "text": {"index": 1479441088, "text": "准备就绪"}}, {"id": 515, "constName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text": {"index": 1974815829, "text": "发射"}}, {"id": 516, "constName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "text": {"index": 1463467232, "text": "校准"}}, {"id": 517, "constName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text": {"index": 873222217, "text": "发射中"}}, {"id": 518, "constName": "KatyushaLoadMissiles", "text": {"index": 1632097769, "text": "装载导弹"}}, {"id": 519, "constName": "KatyushaInsertModule", "text": {"index": 391537682, "text": "安装模块"}}, {"id": 520, "constName": "KatyushaRemoveModule", "text": {"index": 621947331, "text": "卸载模块"}}, {"id": 521, "constName": "KatyushaNoModule", "text": {"index": 1576962343, "text": "瞄准模块"}}, {"id": 522, "constName": "ItemTypeGenerate", "text": {"index": 1854077991, "text": "综合"}}, {"id": 523, "constName": "KatyushaMissileLoad", "text": {"index": 1013295272, "text": "安装"}}, {"id": 524, "constName": "KatyushaMissileUnLoad", "text": {"index": 964055211, "text": "卸载"}}, {"id": 525, "constName": "KatyushaMissileOwned", "text": {"index": 1649143684, "text": "拥有:{0}"}}, {"id": 526, "constName": "Remain<PERSON>es", "text": {"index": 2086360312, "text": "剩余资源:{0}"}}, {"id": 527, "constName": "RoomReseting", "text": {"index": 1885270051, "text": "房间重置中"}}, {"id": 528, "constName": "Waiting", "text": {"index": 972620852, "text": "等待中"}}, {"id": 529, "constName": "LootRes", "text": {"index": 941460391, "text": "抢夺物资"}}, {"id": 530, "constName": "ConfirmGiveUpOrNot", "text": {"index": 2141189235, "text": "请确认是否放弃"}}, {"id": 531, "constName": "ConfirmGiveUp", "text": {"index": 1182209712, "text": "确认放弃"}}, {"id": 532, "constName": "LeaveSettle", "text": {"index": 1657261872, "text": "中途撤离，即将结算"}}, {"id": 533, "constName": "WeakDefense ", "text": {"index": 1095685493, "text": "弱防御力 "}}, {"id": 534, "constName": "StrongDefense ", "text": {"index": 1783138396, "text": "强防御力 "}}, {"id": 535, "constName": "BuildWaitDestroy", "text": {"index": 1424763061, "text": "点击按钮拆除这个建筑"}}, {"id": 536, "constName": "BuildWaitRepair", "text": {"index": 217708731, "text": "点击按钮维修这个建筑"}}, {"id": 537, "constName": "BuildWaitClearWire", "text": {"index": 1252462982, "text": "点击按钮拆除电线"}}, {"id": 538, "constName": "BuildWaitPickup", "text": {"index": 789455016, "text": "点击按钮回收这个建筑"}}, {"id": 544, "constName": "UpgradeFull", "text": {"index": 31626205, "text": "该建筑已升级到指定材质"}}, {"id": 545, "constName": "WeaponEquipTips", "text": {"index": 391146998, "text": "成功装备{0}"}}, {"id": 546, "constName": "WeaponUnloadTips", "text": {"index": 1245129699, "text": "卸下{0}"}}, {"id": 548, "constName": "PermExeSuc", "text": {"index": 1515749278, "text": "申请记录处理成功。"}}, {"id": 549, "constName": "PermAdminAddConfirm", "text": {"index": 1737965717, "text": "拥有管理权限的玩家可以分配重要权限，请慎重考虑。"}}, {"id": 551, "constName": "PermAdminAddSuc2", "text": {"index": 1839283729, "text": "您在领地{0}已被添加为领地管理员。"}}, {"id": 552, "constName": "PermAdminRemConfirm", "text": {"index": 680627376, "text": "是否确定将{0}移除管理员一职？"}}, {"id": 554, "constName": "PermRemSuc2", "text": {"index": 942051987, "text": "您在领地{0}的管理员一职已被移除。"}}, {"id": 556, "constName": "PermAccessed", "text": {"index": 1229324744, "text": "你已获得领地{0}下的{1}权限，可在工具柜-领地中枢-查看权限中查看详细内容。"}}, {"id": 557, "constName": "PermTeamQuit", "text": {"index": 1440337826, "text": "你已失去队伍相关领地下的所有权限。"}}, {"id": 559, "constName": "PermNoWorkbench", "text": {"index": 1078114388, "text": "领地范围未检测到工作台，是否跳转制作界面？"}}, {"id": 560, "constName": "PermRequestConfirm", "text": {"index": 1014383290, "text": "是否确认申请权限{0}？申请成功后{1}内不可再申请权限。"}}, {"id": 561, "constName": "PermRequestSuc", "text": {"index": 2072850049, "text": "权限申请成功。"}}, {"id": 562, "constName": "PermRequestCoolDown", "text": {"index": 1794163137, "text": "权限申请冷却中，剩余{0}。"}}, {"id": 563, "constName": "PermRequestApproved", "text": {"index": 162256934, "text": "您在领地{0}申请的{1}权限已被通过。"}}, {"id": 564, "constName": "PermRequestDeclined", "text": {"index": 871268470, "text": "您在领地{0}申请的{1}权限已被拒绝。"}}, {"id": 565, "constName": "PermWelcome", "text": {"index": 1207738813, "text": "我已通过您对{0}权限的请求，欢迎加入领地{1}！"}}, {"id": 566, "constName": "PermSorry", "text": {"index": 491122582, "text": "非常抱歉，你在领地{0}申请的{1}权限请求已被拒绝。"}}, {"id": 567, "constName": "PermRefreshSuc", "text": {"index": 222083555, "text": "刷新成功。"}}, {"id": 568, "constName": "PermNeedID", "text": {"index": 731811977, "text": "[color=#D8D8D8]请输入玩家昵称[/color]"}}, {"id": 569, "constName": "PermRenamedSuc", "text": {"index": 2119215619, "text": "更名成功。"}}, {"id": 570, "constName": "PermAddGroupSuc", "text": {"index": 1206156552, "text": "关系组{0}已添加成功。"}}, {"id": 572, "constName": "PermGroupNumFull", "text": {"index": 109063406, "text": "自定义关系组已满，无法添加新关系。"}}, {"id": 574, "constName": "PermGroupChanged", "text": {"index": 148484966, "text": "您的关系组已被修改或移除，点击退出当前界面。"}}, {"id": 575, "constName": "PermUntitledLand", "text": {"index": 453910560, "text": "未命名领地"}}, {"id": 576, "constName": "PermPlayerHasPermission", "text": {"index": 749671656, "text": "该玩家已经被赋予关系。"}}, {"id": 577, "constName": "ReputationRateTip", "text": {"index": 999927908, "text": "转化率=100% * 数量上限 / 当前总数\n转化率越高，T0转化为T1的耗时越短\n当前小队摆件总数：{0}\n本服小队摆件上限：{1}"}}, {"id": 578, "constName": "ReputationSettleTimeTip", "text": {"index": 421631361, "text": "每日固定时间进行基于当前情报段位结算局外奖励，奖励将通过邮件发送到局外账号"}}, {"id": 579, "constName": "ReputationSubmitTip", "text": {"index": 1041793844, "text": "每次提交都只会消耗固定数量（段位越高，消耗越大）\n数量不足则无法提交"}}, {"id": 580, "constName": "PermDelSuc", "text": {"index": 685136185, "text": "移除玩家权限成功。"}}, {"id": 584, "constName": "ExtendPackItemListPrefix", "text": {"index": 852110971, "text": "以及道具:{0}"}}, {"id": 585, "constName": "ExtendPackReplaceTips1", "text": {"index": 2087433397, "text": "背包已满,替换扩容背包后将丢弃旧的扩容背包{0},是否继续?"}}, {"id": 586, "constName": "ExtendPackReplaceTips2", "text": {"index": 1229489003, "text": "背包已满,替换扩容背包后部分道具将一并转移到{0}, 包含:{1}"}}, {"id": 587, "constName": "ExtendPackReplaceTips3", "text": {"index": 1154662440, "text": "背包已满,替换后背包和{0}均无法存放剩余道具"}}, {"id": 588, "constName": "ExtendPackMoveTips1", "text": {"index": 59763511, "text": "背包空间不足无法存放"}}, {"id": 589, "constName": "ExtendPackMoveTips2", "text": {"index": 1986039876, "text": "背包空间不足, 请清空扩容背包后尝试"}}, {"id": 590, "constName": "ExtendPackUnloadTips1", "text": {"index": 669627571, "text": "背包已满,卸下扩容背包后部分道具将被转移到{0},包含:{1}"}}, {"id": 591, "constName": "ExtendPackUnloadTips2", "text": {"index": 200863387, "text": "背包已满,卸下后背包和{0}均无法存放剩余道具"}}, {"id": 592, "constName": "ExtendPackUnloadTips3", "text": {"index": 1322543653, "text": "背包已满,卸下扩容背包后部分道具将被丢弃,包含: {0}"}}, {"id": 593, "constName": "ExtendPackPickTips1", "text": {"index": 347635693, "text": "背包已满,替换扩容背包后将丢弃旧的扩容背包{0},是否继续?"}}, {"id": 594, "constName": "UiMailSystemSender", "text": {"index": 797565173, "text": "系统"}}, {"id": 595, "constName": "UiMailNoticeSender", "text": {"index": 2073007413, "text": "公告"}}, {"id": 596, "constName": "UiMailDeleteAllTip", "text": {"index": 931780310, "text": "您需要删除所有已读邮件吗？"}}, {"id": 602, "constName": "UiMailDateFormat", "text": {"index": 661370402, "text": "{0}年{1}月{2}日"}}, {"id": 603, "constName": "UiMailValidFormat", "text": {"index": 222118230, "text": "{0}天"}}, {"id": 604, "constName": "UiMailValidForever", "text": {"index": 151881908, "text": "永久有效"}}, {"id": 605, "constName": "UiMailDelete", "text": {"index": 978842823, "text": "删除"}}, {"id": 606, "constName": "UiMailReceive", "text": {"index": 1920011462, "text": "领取"}}, {"id": 607, "constName": "ItemUseEditIC", "text": {"index": 1336971961, "text": "电路编辑"}}, {"id": 610, "constName": "DropItem", "text": {"index": 1751837031, "text": "掉落物"}}, {"id": 611, "constName": "UiBigPlantBox", "text": {"index": 1072230056, "text": "大种植箱"}}, {"id": 612, "constName": "UiSmallPlantBox", "text": {"index": 27644126, "text": "小种植箱"}}, {"id": 613, "constName": "PickDrop", "text": {"index": 1238866909, "text": "拾取掉落物"}}, {"id": 617, "constName": "Admin", "text": {"index": 2074789161, "text": "管理员"}}, {"id": 618, "constName": "PermApplyRecord", "text": {"index": 91495303, "text": "申请记录"}}, {"id": 619, "constName": "PlantSeed", "text": {"index": 541208660, "text": "播种"}}, {"id": 620, "constName": "PlantStageSeed", "text": {"index": 612723371, "text": "幼苗期"}}, {"id": 621, "constName": "PlantStageHybrid", "text": {"index": 1287063909, "text": "杂交期"}}, {"id": 622, "constName": "PlantStageManure", "text": {"index": 1627679387, "text": "成熟期"}}, {"id": 623, "constName": "PlantStageHarvest", "text": {"index": 1698837566, "text": "收获期"}}, {"id": 624, "constName": "PlantCreateBox", "text": {"index": 1838315351, "text": "制作种植箱"}}, {"id": 625, "constName": "AutoOpenDoorSetting", "text": {"index": 2033724922, "text": "门自动化设置"}}, {"id": 626, "constName": "PermLookOver", "text": {"index": 1145710422, "text": "查看权限列表"}}, {"id": 627, "constName": "PermManage", "text": {"index": 1631488107, "text": "权限管理"}}, {"id": 628, "constName": "PermAddNewGroup", "text": {"index": 1510548376, "text": "新增组"}}, {"id": 629, "constName": "PermDeleteGroup", "text": {"index": 590046434, "text": "删除组"}}, {"id": 630, "constName": "PermAgreeAll", "text": {"index": 1706701773, "text": "一键同意"}}, {"id": 632, "constName": "PermRoleChangeCloseWinTip", "text": {"index": 1286391834, "text": "你的权限已经被更改，需要关闭界面"}}, {"id": 633, "constName": "PermDeleteGroupConfirmTip", "text": {"index": 274020429, "text": "你确定要删除这个权限组？"}}, {"id": 636, "constName": "PermNextApplyTime", "text": {"index": 1473112582, "text": "距离下次可申请时间"}}, {"id": 637, "constName": "PermApplyTitleTime", "text": {"index": 715327982, "text": "申请时间"}}, {"id": 638, "constName": "PermApplyGroupName", "text": {"index": 1404544011, "text": "申请组名称"}}, {"id": 639, "constName": "PermApplyResult", "text": {"index": 1607966620, "text": "申请结果"}}, {"id": 640, "constName": "PermApplySuccessful", "text": {"index": 1202036736, "text": "成功"}}, {"id": 641, "constName": "PermApplyFail", "text": {"index": 732690165, "text": "失败"}}, {"id": 642, "constName": "PermAddNewMemberOnGroupChanged", "text": {"index": 1713849121, "text": "选中添加成员的组已经不存在，请重新选择"}}, {"id": 643, "constName": "PermAddGroupFullTip", "text": {"index": 240996239, "text": "自定义组已经满，无法新增"}}, {"id": 644, "constName": "PermAddGroupDefaultName", "text": {"index": 44038852, "text": "新权限组"}}, {"id": 645, "constName": "PermInvalidGroupRename", "text": {"index": 1497367080, "text": "无效的命名"}}, {"id": 646, "constName": "PermAddGroupNoSelectAnyItem", "text": {"index": 290682309, "text": "你未选择任何权限"}}, {"id": 647, "constName": "PermSearchMemberBtn", "text": {"index": 275100292, "text": "搜索"}}, {"id": 648, "constName": "PermSearchMemberLabel", "text": {"index": 682632136, "text": "查找"}}, {"id": 649, "constName": "PermTeamMemberLabel", "text": {"index": 129781475, "text": "队伍"}}, {"id": 650, "constName": "PermMemberItemDelete", "text": {"index": 333492118, "text": "删除"}}, {"id": 651, "constName": "PermMemberItemChange", "text": {"index": 114216891, "text": "修改"}}, {"id": 652, "constName": "PermLord", "text": {"index": 2133295282, "text": "领主"}}, {"id": 653, "constName": "PermMemberItemAdd", "text": {"index": 1262585741, "text": "添加"}}, {"id": 654, "constName": "PermMemberFull", "text": {"index": 575654164, "text": "成员已满"}}, {"id": 655, "constName": "PermAddMember", "text": {"index": 1167090282, "text": "添加成员"}}, {"id": 656, "constName": "PermNoApplyRecord", "text": {"index": 1695747351, "text": "没有申请记录"}}, {"id": 657, "constName": "PermNoPlayerInThisGroup", "text": {"index": 983593723, "text": "该组内没有组员"}}, {"id": 658, "constName": "PermSearchPlayerNotFound", "text": {"index": 394256046, "text": "未找到玩家"}}, {"id": 660, "constName": "PlantNoSeed", "text": {"index": 467363640, "text": "不播种"}}, {"id": 661, "constName": "PermMemberItemAgree", "text": {"index": 1970690524, "text": "同意"}}, {"id": 662, "constName": "PermMemberItemReject", "text": {"index": 667929741, "text": "拒绝"}}, {"id": 663, "constName": "PermApply", "text": {"index": 1845566199, "text": "申请权限"}}, {"id": 664, "constName": "PermChange", "text": {"index": 729803078, "text": "修改权限"}}, {"id": 665, "constName": "PermApplyOp", "text": {"index": 832769111, "text": "申请"}}, {"id": 666, "constName": "PermCanNotApplyOp", "text": {"index": 35249390, "text": "无法申请"}}, {"id": 667, "constName": "PermChooseOp", "text": {"index": 1753443754, "text": "选择"}}, {"id": 668, "constName": "BatchRepairSuc", "text": {"index": 324231720, "text": "批量修复成功"}}, {"id": 669, "constName": "BatchRepairNoPermission", "text": {"index": 1454514991, "text": "你没有权限进行批量修复"}}, {"id": 670, "constName": "BatchRepairTerritoryNotFound", "text": {"index": 897817493, "text": "未找到领地，你不能进行批量修复"}}, {"id": 671, "constName": "BatchRepairInAttackCd", "text": {"index": 416907370, "text": "正处于攻击冷却中，你不能进行批量修复"}}, {"id": 672, "constName": "BatchRepairInBatchCd", "text": {"index": 252105837, "text": "正处于修复冷却中，你不能进行批量修复"}}, {"id": 673, "constName": "BatchRepairPartRecordNotFound", "text": {"index": 1931755542, "text": "建筑记录未找到，无法进行批量修复"}}, {"id": 674, "constName": "BatchRepairHpFull", "text": {"index": 1265825442, "text": "批量修复失败，生命值满"}}, {"id": 675, "constName": "BatchRepairRecordError", "text": {"index": 2078341850, "text": "批量修复失败，建筑记录错误"}}, {"id": 676, "constName": "BatchRepairPlayerNearby", "text": {"index": 782110601, "text": "批量修复失败，附近有玩家"}}, {"id": 677, "constName": "BatchRepairConsumeItemFail", "text": {"index": 462562537, "text": "批量修复失败，材料不足或扣除异常"}}, {"id": 678, "constName": "BatchRepairCreatePartEntityFail", "text": {"index": 1940297095, "text": "批量修复失败，创建建筑出错"}}, {"id": 679, "constName": "BatchRepairNoPart", "text": {"index": 1165037687, "text": "没有需要批量修复的建筑"}}, {"id": 683, "constName": "RepairFailNotDamage", "text": {"index": 664953334, "text": "无法维修，未收到伤害"}}, {"id": 684, "constName": "RepairFailNotFindBag", "text": {"index": 1483241567, "text": "无法维修，未找到玩家背包"}}, {"id": 685, "constName": "HybridPlayTips", "text": {"index": 1886166853, "text": "游戏说明\n1）选择1个杂交期植株，2个材料植株后，可以进入游戏\n2）可使用点数调整单个基因在单个顺位上的概率\n3）进入小游戏后，点击屏幕即可增加已选中的基因的概率\n4）每个植株只能作为目标植株杂交一次"}}, {"id": 691, "constName": "PlantNoPlanter", "text": {"index": 1103032715, "text": "附近未检测到种植箱，是否前往制作？"}}, {"id": 692, "constName": "PlantDelConfirm", "text": {"index": 1610614726, "text": "是否确认移除植株？"}}, {"id": 693, "constName": "TagFloor", "text": {"index": 369002985, "text": "天花板"}}, {"id": 694, "constName": "TagWall", "text": {"index": 967058358, "text": "墙壁"}}, {"id": 695, "constName": "TagWindow", "text": {"index": 510871066, "text": "门窗"}}, {"id": 696, "constName": "TagStairs", "text": {"index": 159065611, "text": "楼梯"}}, {"id": 697, "constName": "TagFoundation", "text": {"index": 508583454, "text": "地基"}}, {"id": 698, "constName": "TagPowerEquip", "text": {"index": 1477951350, "text": "供电设备"}}, {"id": 699, "constName": "TagElecLinkDevice", "text": {"index": 109407837, "text": "链接设备"}}, {"id": 700, "constName": "TagPowerComponents", "text": {"index": 1364761582, "text": "电力组件"}}, {"id": 701, "constName": "TagElecDevice", "text": {"index": 474359189, "text": "用电设备"}}, {"id": 702, "constName": "OthersideTitleNearBy", "text": {"index": 853157748, "text": "附近物品"}}, {"id": 703, "constName": "OthersideTitleOfflinePlayer", "text": {"index": 447786984, "text": "{0}的背包"}}, {"id": 704, "constName": "OthersideTitleDefault", "text": {"index": 1308747984, "text": "战利品"}}, {"id": 705, "constName": "BuildNoAdsorption", "text": {"index": 1377694080, "text": "未找到吸附点。"}}, {"id": 706, "constName": "BuildCoreTips1", "text": {"index": 833436369, "text": "该造物需要被放置在墙壁、墙壁框架或者半墙等建筑上。"}}, {"id": 707, "constName": "BuildCoreTips2", "text": {"index": 186947115, "text": "该造物需要被摆放在地面上。"}}, {"id": 708, "constName": "BuildCoreTips3", "text": {"index": 473233647, "text": "该造物需要被放置在三角地基上。"}}, {"id": 709, "constName": "BuildCoreTips4", "text": {"index": 1603233406, "text": "该造物需要被放置在地基、天花板等建筑上。"}}, {"id": 710, "constName": "BuildCoreTips5", "text": {"index": 2077630409, "text": "该造物需要被放置在地基、天花板、墙壁等建筑上。"}}, {"id": 711, "constName": "BuildCoreTips6", "text": {"index": 1914323791, "text": "该造物需要被放置在矩形地基、天花板上。"}}, {"id": 712, "constName": "BuildCoreTips7", "text": {"index": 1338323886, "text": "该造物需要被放置在地面或地基、天花板等建筑上。"}}, {"id": 713, "constName": "BuildAngleFailed1", "text": {"index": 500530862, "text": "请尝试摆放在更平缓的区域。"}}, {"id": 714, "constName": "BuildAngleFailed2", "text": {"index": 700692992, "text": "需要向下吸附在天花板上。"}}, {"id": 715, "constName": "BuildAngleFailed3", "text": {"index": 231711796, "text": "该造物需要被放置在墙上。"}}, {"id": 716, "constName": "BuildAngleFailed4", "text": {"index": 1124123336, "text": "该造物需要被放置在地基与墙相接处。"}}, {"id": 717, "constName": "BuildLayerFailed1", "text": {"index": 1669923435, "text": "该造物底部需要被完全摆放在地基上。"}}, {"id": 718, "constName": "BuildLayerFailed2", "text": {"index": 1874687054, "text": "该造物底部需要被完全摆放在地面上。"}}, {"id": 719, "constName": "BuildCantInWater", "text": {"index": 416420909, "text": "该造物不可在水中建造。"}}, {"id": 720, "constName": "BuildAirflowFailed", "text": {"index": 1080846778, "text": "下方造物上空无法建造天花板。"}}, {"id": 721, "constName": "BuildTerrainFailed", "text": {"index": 292636558, "text": "当前造物不可在地形中建造。"}}, {"id": 722, "constName": "BuildBotFailed", "text": {"index": 2051123408, "text": "当前位置无法建造,底部未处于地面以下。"}}, {"id": 723, "constName": "BuildBlocked", "text": {"index": 955060465, "text": "有物体阻挡，不可建造。"}}, {"id": 724, "constName": "Build2Close2PVE", "text": {"index": 218510395, "text": "离PVE区域太近，无法建造。"}}, {"id": 725, "constName": "Build2Close3Road", "text": {"index": 1015895274, "text": "离道路太近，无法建造。"}}, {"id": 726, "constName": "BuildPreventFailed", "text": {"index": 973071174, "text": "离其他建造太近，无法建造。"}}, {"id": 727, "constName": "BuildDebrisBlocked", "text": {"index": 638468446, "text": "请等小木盒消失后再尝试建造。"}}, {"id": 728, "constName": "BuildSuppFailed", "text": {"index": 249005581, "text": "当前位置支撑力不足，无法建造。"}}, {"id": 729, "constName": "BuildSuppFailedWarn", "text": {"index": 1449881241, "text": "存在支撑力不足风险,是否确认删除？"}}, {"id": 730, "constName": "BuildNoMaterial", "text": {"index": 1441847953, "text": "材料不足，无法建造。"}}, {"id": 731, "constName": "BuildOutRange", "text": {"index": 626787845, "text": "超出可建造距离，无法建造。"}}, {"id": 732, "constName": "BuildSightBlocked", "text": {"index": 391917612, "text": "视线被阻挡，无法建造。"}}, {"id": 733, "constName": "btnComGuide", "text": {"index": 2060143300, "text": "控件引导开关"}}, {"id": 734, "constName": "btnInfoBoard", "text": {"index": 1328374212, "text": "实体信息面板开关"}}, {"id": 735, "constName": "btnResetBoard", "text": {"index": 2126817263, "text": "重置实体面板显示次数"}}, {"id": 736, "constName": "btnResetGuide", "text": {"index": 2018444218, "text": "重置引导显示次数"}}, {"id": 737, "constName": "btnShowGuideId", "text": {"index": 802026946, "text": "显示引导ID"}}, {"id": 738, "constName": "titleGuide", "text": {"index": 1537664738, "text": "新手引导"}}, {"id": 739, "constName": "btnPerformanceDataCollect", "text": {"index": 1629582933, "text": "性能数据采集"}}, {"id": 740, "constName": "ContainerModify", "text": {"index": 114934467, "text": "容器修改"}}, {"id": 741, "constName": "OneClickClear", "text": {"index": 1665055645, "text": "一键清空"}}, {"id": 742, "constName": "btnShowSightDetection", "text": {"index": 2145612904, "text": "视线检测范围显示"}}, {"id": 743, "constName": "WallSnapFix", "text": {"index": 815228692, "text": "墙壁动态吸附"}}, {"id": 744, "constName": "BtnTabAll", "text": {"index": 1380586880, "text": "全部"}}, {"id": 745, "constName": "SelectToRepair", "text": {"index": 748261667, "text": "选择建筑进行修复"}}, {"id": 746, "constName": "SelectToUpgrade", "text": {"index": 1166566954, "text": "选择建筑进行升级"}}, {"id": 747, "constName": "SelectToDestroy", "text": {"index": 190013032, "text": "选择建筑进行删除"}}, {"id": 748, "constName": "SelectToPickup", "text": {"index": 1826078454, "text": "选择建筑进行回收"}}, {"id": 749, "constName": "CraftGoToTechTree", "text": {"index": 469451076, "text": "前往研发界面"}}, {"id": 750, "constName": "CraftNotLearn", "text": {"index": 750204014, "text": "蓝图未学习"}}, {"id": 751, "constName": "ElectricTimerSettingTitle", "text": {"index": 368977286, "text": "设置计时器持续时间"}}, {"id": 752, "constName": "ElectricTimerSettingDescription", "text": {"index": 1204443958, "text": "秒数"}}, {"id": 753, "constName": "ElectricTimerSettingConfirm", "text": {"index": 1999119552, "text": "设定持续时间"}}, {"id": 756, "constName": "KillSelfTips", "text": {"index": 1269114591, "text": "是否放弃?"}}, {"id": 757, "constName": "SettingSecondFpsLimit", "text": {"index": 1529286302, "text": "帧率上限"}}, {"id": 758, "constName": "EscapeFromStuckTips", "text": {"index": 345694501, "text": "是否脱离卡死?"}}, {"id": 760, "constName": "FpsLimitTip", "text": {"index": 1620830776, "text": "提高帧率，会较大程度加快设备耗电、发热"}}, {"id": 762, "constName": "AdaptiveDecreaseRMQuality", "text": {"index": 1875020768, "text": "帧率太低，是否降低画质为"}}, {"id": 763, "constName": "Member", "text": {"index": 547183996, "text": "成员"}}, {"id": 764, "constName": "Upgrade", "text": {"index": 404676101, "text": "升级"}}, {"id": 765, "constName": "Downgrade", "text": {"index": 1174503567, "text": "降级"}}, {"id": 767, "constName": "PermAssignWarning", "text": {"index": 7262363, "text": "该权限移除后，该成员将被移除本领地。"}}, {"id": 768, "constName": "TooHotToGather", "text": {"index": 601218346, "text": "不可采集，请等待火焰熄灭"}}, {"id": 800, "constName": "LobbyTeamTip1", "text": {"index": 81422342, "text": "邀请您加入 大厅准备中 {0}模式的队伍，是否现在跳转界面？"}}, {"id": 801, "constName": "LobbyTeamTip2", "text": {"index": 1965831641, "text": "邀请您加入 大厅准备中 {0}模式的队伍"}}, {"id": 802, "constName": "LobbyTeamTip3", "text": {"index": 75582408, "text": "邀请您加入 {0}模式 的 {1}服务器的队伍，是否同意并进入服务器？"}}, {"id": 804, "constName": "VendingMachineChangeConfirm", "text": {"index": 189480533, "text": "当前已对商店做出了修改，保存之后才会正式生效，确定要保存吗？"}}, {"id": 805, "constName": "VendingMachineSave", "text": {"index": 221128355, "text": "保存"}}, {"id": 806, "constName": "VendingMachineNotSave", "text": {"index": 1934492169, "text": "不要保存"}}, {"id": 807, "constName": "ItemTipsEquipWeapon", "text": {"index": 251521352, "text": "装备武器"}}, {"id": 808, "constName": "ItemTipsEquipBelt", "text": {"index": 1838462920, "text": "放入快捷栏"}}, {"id": 809, "constName": "ItemTipsUnloadWeapon", "text": {"index": 1588069928, "text": "卸下武器"}}, {"id": 810, "constName": "ItemTipsUnloadBelt", "text": {"index": 267042735, "text": "存入背包"}}, {"id": 811, "constName": "SettingLobbyGM", "text": {"index": 47213853, "text": "大厅GM"}}, {"id": 812, "constName": "AddCurrency", "text": {"index": 1221686534, "text": "增加指定货币"}}, {"id": 813, "constName": "OilPutIN", "text": {"index": 1963233291, "text": "放入燃油"}}, {"id": 814, "constName": "OilPutOut", "text": {"index": 1321575723, "text": "取出燃油"}}, {"id": 815, "constName": "VendingMachineInputSearch", "text": {"index": 737777455, "text": "请输入道具名称"}}, {"id": 816, "constName": "DoubleClickToMove", "text": {"index": 1603054956, "text": "[color=#c7f279]双击[/color]转移"}}, {"id": 817, "constName": "DoubleClickToEquip", "text": {"index": 1834330945, "text": "[color=#c7f279]双击[/color]装备"}}, {"id": 818, "constName": "ItemDetailDropAndTrade", "text": {"index": 360490687, "text": "掉落和交易"}}, {"id": 819, "constName": "ItemDetailCraft", "text": {"index": 1420865367, "text": "制作"}}, {"id": 820, "constName": "ItemDetailBlueprint", "text": {"index": 461211810, "text": "蓝图"}}, {"id": 821, "constName": "ItemDetailDecompose", "text": {"index": 1692515519, "text": "分解"}}, {"id": 822, "constName": "ItemDetailBeCrafting", "text": {"index": 1608640, "text": "可用于制作"}}, {"id": 823, "constName": "DoubleClickToUnEquip", "text": {"index": 720646139, "text": "[color=#c7f279]双击[/color]卸下"}}, {"id": 824, "constName": "PlayerInfo", "text": {"index": 1697083919, "text": "玩家信息"}}, {"id": 825, "constName": "PrivateChat", "text": {"index": 2095135375, "text": "私聊玩家"}}, {"id": 826, "constName": "AddFriend", "text": {"index": 1403975356, "text": "加为好友"}}, {"id": 827, "constName": "KickTeam", "text": {"index": 1265346370, "text": "移出队伍"}}, {"id": 828, "constName": "OpenOven", "text": {"index": 782361927, "text": "打开"}}, {"id": 829, "constName": "PermAssignWarningAdmin", "text": {"index": 856877580, "text": "该成员身份是管理员，请先移除其管理员身份再取消其最后的权限。"}}, {"id": 830, "constName": "ItemDetailNoTarget", "text": {"index": 1949239223, "text": "4米范围内无{0}"}}, {"id": 831, "constName": "LeaveTeam", "text": {"index": 2078619015, "text": "离开队伍"}}, {"id": 832, "constName": "LeaveTeamTips", "text": {"index": 1234367368, "text": "是否退出当前队伍"}}, {"id": 833, "constName": "KickTeamTips", "text": {"index": 703575705, "text": "是否将{0}踢出队伍"}}, {"id": 834, "constName": "TeammateNotOccupyPermPosition", "text": {"index": 686477873, "text": "队友不占用权限位置"}}, {"id": 835, "constName": "LevelTextFormat", "text": {"index": 1942394031, "text": "等级{0}"}}, {"id": 836, "constName": "ReputationUnlockAmountFormat", "text": {"index": 765346755, "text": "{0}点积分解锁"}}, {"id": 837, "constName": "ReputationGetBluePrintFormat", "text": {"index": 66976623, "text": "获得蓝图{0}"}}, {"id": 839, "constName": "ItemDetailGoToOven", "text": {"index": 2015804810, "text": "前往{0}"}}, {"id": 841, "constName": "LobbySkinAppleCacheArmorSkin", "text": {"index": 1881806419, "text": "是否装备缓存的护甲皮肤"}}, {"id": 842, "constName": "ItemDetailUnlockingMethod", "text": {"index": 1912112375, "text": "解锁方式"}}, {"id": 843, "constName": "SearchManual", "text": {"index": 256337543, "text": "查找手册"}}, {"id": 844, "constName": "VendingMachineName", "text": {"index": 670188466, "text": "{0}的商店"}}, {"id": 846, "constName": "ItemUseSeeding", "text": {"index": 2058340554, "text": "前往种植"}}, {"id": 847, "constName": "NoSearchResult", "text": {"index": 66813632, "text": "无查询结果"}}, {"id": 848, "constName": "NoMember", "text": {"index": 1397195465, "text": "无成员"}}, {"id": 849, "constName": "ComboUpgrade", "text": {"index": 804369943, "text": "组合建筑升级"}}, {"id": 851, "constName": "Filter", "text": {"index": 266378930, "text": "筛选"}}, {"id": 852, "constName": "Monument", "text": {"index": 1280848439, "text": "据点"}}, {"id": 853, "constName": "Respawn", "text": {"index": 1905877388, "text": "重生点位"}}, {"id": 854, "constName": "Shop", "text": {"index": 96269016, "text": "商店"}}, {"id": 855, "constName": "HorseDesc", "text": {"index": 1425274301, "text": "生命值     {0}\n速度上限    {1}km/h\n体力值     {2}"}}, {"id": 856, "constName": "LoadingLobbyScene", "text": {"index": 290011402, "text": "正在加载大厅场景..."}}, {"id": 857, "constName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text": {"index": 987969622, "text": "登录中..."}}, {"id": 858, "constName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text": {"index": 246166341, "text": "大厅"}}, {"id": 859, "constName": "ChatLobbyWel<PERSON>", "text": {"index": 1577581236, "text": "您已加入大厅服务器聊天频道"}}, {"id": 860, "constName": "ChatWorldWelcome", "text": {"index": 1230330084, "text": "您已加入世界频道聊天服务器"}}, {"id": 861, "constName": "ChatSystemMessage", "text": {"index": 763695783, "text": "系统消息"}}, {"id": 862, "constName": "ChatIsFriend", "text": {"index": 29401346, "text": "【好友】"}}, {"id": 863, "constName": "FindNewBattleFailed", "text": {"index": 38048186, "text": "匹配新战局失败"}}, {"id": 864, "constName": "FindNewBattleFailedTip", "text": {"index": 604804983, "text": "因本次测试规模所限，同时开启的服务器数量较少，您或您的小队成员已在当前每一个服务器中创建了角色；您可通过“历史战局”界面继续之前的战局"}}, {"id": 865, "constName": "WaterMillilitre", "text": {"index": 949290164, "text": "毫升"}}, {"id": 866, "constName": "SettingMainGMSkin", "text": {"index": 1248291456, "text": "Gm皮肤"}}, {"id": 867, "constName": "InventoryTitleBackpack", "text": {"index": 204742950, "text": "背包"}}, {"id": 868, "constName": "InventoryTitleCraft", "text": {"index": 2056586143, "text": "制作"}}, {"id": 869, "constName": "BuildModifyTo", "text": {"index": 1750417864, "text": "编辑为"}}, {"id": 870, "constName": "OthersideTitleDeadPlayer", "text": {"index": 700070705, "text": "{0}的战利品"}}, {"id": 871, "constName": "MonumentUIUnlockTime", "text": {"index": 1603615240, "text": "解锁倒计时"}}, {"id": 872, "constName": "btnResetHandbook", "text": {"index": 411438016, "text": "重置生存手册解锁状态"}}, {"id": 873, "constName": "btnUnlockAllHandBook", "text": {"index": 1125413810, "text": "解锁所有生存手册"}}, {"id": 874, "constName": "btnUnlockOneHandbook", "text": {"index": 702768517, "text": "解锁指定手册"}}, {"id": 875, "constName": "btnAnimationBucket", "text": {"index": 2000304826, "text": "开启动画桶"}}, {"id": 876, "constName": "btnGpuInstance", "text": {"index": 234683527, "text": "远景树的gpu instance"}}, {"id": 877, "constName": "btnCharacterBucket", "text": {"index": 121891924, "text": "开启角色桶"}}, {"id": 878, "constName": "btnResBucket", "text": {"index": 839660890, "text": "开启资源桶"}}, {"id": 879, "constName": "TerritorySafe", "text": {"index": 539484950, "text": "领地保险柜"}}, {"id": 880, "constName": "SkinFilterAll", "text": {"index": 70865785, "text": "全部"}}, {"id": 881, "constName": "SkinSortDefault", "text": {"index": 2022832750, "text": "默认"}}, {"id": 882, "constName": "SkinSortQuality", "text": {"index": 1002378322, "text": "品质"}}, {"id": 883, "constName": "EnterNumber", "text": {"index": 1723698530, "text": "请输入数字"}}, {"id": 884, "constName": "CombatMarker_Cancel", "text": {"index": 1138129952, "text": "取消"}}, {"id": 885, "constName": "CombatMarker_OtherConfirm", "text": {"index": 1266750983, "text": "他人确认"}}, {"id": 886, "constName": "CombatMarker_Confirm", "text": {"index": 2010801010, "text": "确认"}}, {"id": 887, "constName": "btnCharacterScore", "text": {"index": 1378216178, "text": "开启角色性能得分显示"}}, {"id": 888, "constName": "btnResScore", "text": {"index": 959549742, "text": "开启资源性能得分显示"}}, {"id": 889, "constName": "btnCloseAiReview", "text": {"index": 1041301603, "text": "智能助登录手账号验证"}}, {"id": 890, "constName": "confirmCanUnlock", "text": {"index": 619923662, "text": "是否确认解绑床位{0}"}}, {"id": 891, "constName": "SafeBox", "text": {"index": 114584537, "text": "保险柜"}}, {"id": 892, "constName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text": {"index": 1726053023, "text": "暂存保险柜"}}, {"id": 893, "constName": "Track", "text": {"index": 142394740, "text": "追踪"}}, {"id": 894, "constName": "StopTrack", "text": {"index": 1405544455, "text": "取消追踪"}}, {"id": 895, "constName": "StartUsingDivingTank", "text": {"index": 951201299, "text": "氧气瓶开始使用"}}, {"id": 896, "constName": "StopUsingDivingTank", "text": {"index": 15849060, "text": "氧气瓶停止使用"}}, {"id": 897, "constName": "DivingTankIsBroken", "text": {"index": 440659173, "text": "氧气瓶氧气耗尽"}}, {"id": 898, "constName": "UnderRepair", "text": {"index": 1193111981, "text": "正在修复中"}}, {"id": 899, "constName": "PartRecoverRemainTime", "text": {"index": 269378111, "text": "剩余修复时间{0}"}}, {"id": 900, "constName": "BackToL<PERSON>in", "text": {"index": 1073628376, "text": "退出游戏"}}, {"id": 901, "constName": "AcceptJump", "text": {"index": 1691901711, "text": "接受跳转"}}, {"id": 902, "constName": "AcceptNoJump", "text": {"index": 1337572140, "text": "接受不跳转"}}, {"id": 903, "constName": "TeamTips", "text": {"index": 165259141, "text": "{0}在{1}-{2}中向您发起组队邀请"}}, {"id": 904, "constName": "OldBattleServer", "text": {"index": 835644117, "text": "战局"}}, {"id": 905, "constName": "SameBattleServer", "text": {"index": 1484759063, "text": "当前战局"}}, {"id": 906, "constName": "OtherBattleServer", "text": {"index": 1142119747, "text": "其他战局"}}, {"id": 907, "constName": "GivePrivilege", "text": {"index": 517682177, "text": "睡袋接受权限"}}, {"id": 908, "constName": "Anybody", "text": {"index": 433046460, "text": "任何人"}}, {"id": 909, "constName": "Teammate", "text": {"index": 1873709453, "text": "队友"}}, {"id": 910, "constName": "Forbid", "text": {"index": 1998089971, "text": "禁止"}}, {"id": 911, "constName": "PickableOnTheGround", "text": {"index": 501553, "text": "地上的物品"}}, {"id": 912, "constName": "BasicStructures", "text": {"index": 852351671, "text": "结构组件"}}, {"id": 913, "constName": "FunctionalOrnaments", "text": {"index": 934327485, "text": "摆件"}}, {"id": 914, "constName": "PowerOrnaments", "text": {"index": 1683823409, "text": "电力/水利摆件"}}, {"id": 915, "constName": "RandomPointRebon", "text": {"index": 397598962, "text": "随机复活"}}, {"id": 916, "constName": "SafeArea", "text": {"index": 1382865269, "text": "安全区"}}, {"id": 917, "constName": "WarningTitle", "text": {"index": 1629420988, "text": "[color=#d7482c]威胁概览[/color]"}}, {"id": 918, "constName": "EnemyNPC", "text": {"index": 945801677, "text": "敌人×"}}, {"id": 919, "constName": "RadiationLow", "text": {"index": 1417642266, "text": "低辐射"}}, {"id": 920, "constName": "RadiationMid", "text": {"index": 2029625020, "text": "中辐射"}}, {"id": 921, "constName": "RadiationHigh", "text": {"index": 808960340, "text": "高辐射"}}, {"id": 922, "constName": "LowTempurature", "text": {"index": 2083518874, "text": "低温"}}, {"id": 923, "constName": "UnderWaterEnv", "text": {"index": 1462945778, "text": "存在水下环境"}}, {"id": 924, "constName": "DarkArea", "text": {"index": 2020141342, "text": "存在黑暗区域"}}, {"id": 925, "constName": "BasicResourceTitle", "text": {"index": 49248644, "text": "据点基础资源"}}, {"id": 926, "constName": "GreenCardRoomResource", "text": {"index": 229316531, "text": "绿卡房资源"}}, {"id": 927, "constName": "BlueCardRoomResource", "text": {"index": 95791562, "text": "蓝卡房资源"}}, {"id": 928, "constName": "RedCardRoomResource", "text": {"index": 1616084520, "text": "红卡房资源"}}, {"id": 929, "constName": "SiteDone", "text": {"index": 81997318, "text": "完成"}}, {"id": 930, "constName": "ChoseRebonWay", "text": {"index": 1385216412, "text": "请选择重生方式"}}, {"id": 931, "constName": "WildBeast", "text": {"index": 211546049, "text": "野兽×"}}, {"id": 932, "constName": "DeviceTitle", "text": {"index": 1532429301, "text": "据点设备"}}, {"id": 933, "constName": "ElectricalDataNoConnect", "text": {"index": 514099689, "text": "无连接"}}, {"id": 934, "constName": "ElectricalDataNoPower", "text": {"index": 1712761227, "text": "无电力输入"}}, {"id": 935, "constName": "ElectricalDataLowPower", "text": {"index": 166009352, "text": "电力不足"}}, {"id": 936, "constName": "ElectricalDataWork", "text": {"index": 293213521, "text": "正常工作"}}, {"id": 937, "constName": "TerritoryCenter", "text": {"index": 1089692573, "text": "领地"}}, {"id": 938, "constName": "TaskDifficulty", "text": {"index": 1839669248, "text": "难度:{0}"}}, {"id": 939, "constName": "TaskProgress", "text": {"index": 534622686, "text": "进度:{0}/{1}"}}, {"id": 940, "constName": "PointTaskDailyReward", "text": {"index": 555412870, "text": "每日任务奖励:{0}/{1}"}}, {"id": 941, "constName": "PointTaskReset", "text": {"index": 1788221651, "text": "每日任务奖励重置：{0}"}}, {"id": 942, "constName": "Repair", "text": {"index": 1726972305, "text": "开始修理"}}, {"id": 943, "constName": "RepairRemainSeconds", "text": {"index": 1527989089, "text": "{0}秒"}}, {"id": 944, "constName": "RepairRemainMinute", "text": {"index": 1764609936, "text": "{0}分{1}秒"}}, {"id": 945, "constName": "RepairRemainHours", "text": {"index": 1843725237, "text": "{0}小时{1}分{2}秒"}}, {"id": 946, "constName": "RebonPointInCd", "text": {"index": 1949831526, "text": "{0}秒后"}}, {"id": 947, "constName": "Collectible", "text": {"index": 1159898769, "text": "采集物"}}, {"id": 948, "constName": "<PERSON><PERSON>", "text": {"index": 1869214033, "text": "重生"}}, {"id": 949, "constName": "CoolDownTime", "text": {"index": 1782568609, "text": "冷却时间"}}, {"id": 950, "constName": "MaxBedDistance", "text": {"index": 1120095447, "text": "{0}m以上"}}, {"id": 951, "constName": "LockAttached", "text": {"index": 636720586, "text": "有锁"}}, {"id": 952, "constName": "PlayingInMode", "text": {"index": 1745468766, "text": "在{0}模式中"}}, {"id": 953, "constName": "Drop", "text": {"index": 64966973, "text": "掉落"}}, {"id": 954, "constName": "RecruitDefaultName", "text": {"index": 880310512, "text": "{0}的队伍"}}, {"id": 955, "constName": "RecruitDefaultCondition", "text": {"index": 431155347, "text": "欢迎加入我们！"}}, {"id": 956, "constName": "TerritoryCenterBelonging", "text": {"index": 1958987071, "text": "{0}的领地"}}, {"id": 957, "constName": "NormalToolCarbinet", "text": {"index": 1128980073, "text": "工具柜"}}, {"id": 958, "constName": "IntelligenceToolCarbinet", "text": {"index": 2039417744, "text": "情报工具柜"}}, {"id": 959, "constName": "TerritoryTechLevel", "text": {"index": 1552203976, "text": "可解锁或制作{0}物品"}}, {"id": 960, "constName": "TerritoryPrivilege", "text": {"index": 1171058598, "text": "已有{0}权限"}}, {"id": 961, "constName": "TerritoryProtectHour", "text": {"index": 432629489, "text": "领地维护时间>{0}小时"}}, {"id": 962, "constName": "TerritoryProtectMinute", "text": {"index": 323742267, "text": "领地维护时间>{0}分钟"}}, {"id": 963, "constName": "TerritoryProtectTime", "text": {"index": 974025495, "text": "领地维护时间>{0}小时{1}分钟"}}, {"id": 964, "constName": "TerritoryTechLevel0", "text": {"index": 467285986, "text": "手工科技"}}, {"id": 965, "constName": "TerritoryTechLevel1", "text": {"index": 105889962, "text": "一级科技"}}, {"id": 966, "constName": "TerritoryTechLevel2", "text": {"index": 158250431, "text": "二级科技"}}, {"id": 967, "constName": "TerritoryTechLevel3", "text": {"index": 1716604546, "text": "三级科技"}}, {"id": 968, "constName": "ChatIsStranger", "text": {"index": 1356267798, "text": "【陌生人】"}}, {"id": 969, "constName": "NearByPVPCdHint", "text": {"index": 5218906, "text": "被玩家淘汰冷却{0}秒"}}, {"id": 970, "constName": "NearByCdHint", "text": {"index": 1597414754, "text": "使用后将在淘汰点附近随机重生，冷却{0}秒"}}, {"id": 971, "constName": "ItemTipsSwitchClip", "text": {"index": 211011093, "text": "切换弹药"}}, {"id": 972, "constName": "ItemTipsUnloadClip", "text": {"index": 950745550, "text": "卸下弹药"}}, {"id": 973, "constName": "GameServerQueueTip", "text": {"index": 857885998, "text": "您当前排队的位置：{0}"}}, {"id": 974, "constName": "LowEnginePart", "text": {"index": 129973505, "text": "低级零件"}}, {"id": 975, "constName": "MidEnginePart", "text": {"index": 247331872, "text": "中级零件"}}, {"id": 976, "constName": "HighEnginePart", "text": {"index": 1127844619, "text": "高级零件"}}, {"id": 977, "constName": "DyingTipText", "text": {"index": 1652249751, "text": "倒计时结束有 {0}% 概率可自动自救，自动自救概率随水分和饱腹值增长，最高为{1}%"}}, {"id": 978, "constName": "MedicineTipText", "text": {"index": 1384335348, "text": "消耗所携带的医疗包可进行自救"}}, {"id": 979, "constName": "SettingSecondCenterDamageUi", "text": {"index": 271166299, "text": "中心受击UI"}}, {"id": 980, "constName": "SettingSecondMinimapSoudUi", "text": {"index": 1065306578, "text": "雷达图UI"}}, {"id": 981, "constName": "SettingSecondSoundPrintUi", "text": {"index": 1842426716, "text": "声纹UI"}}, {"id": 982, "constName": "SettingSecondDamageBloodUi", "text": {"index": 1187102079, "text": "边缘血雾UI"}}, {"id": 983, "constName": "PartSlotScope", "text": {"index": 821713115, "text": "瞄准镜"}}, {"id": 984, "constName": "PartSlotMuzzle", "text": {"index": 1814485168, "text": "枪口"}}, {"id": 985, "constName": "PartSlotUnderBarrel", "text": {"index": 969120207, "text": "下挂"}}, {"id": 986, "constName": "PartSlotMagazine", "text": {"index": 191592780, "text": "弹夹"}}, {"id": 987, "constName": "GeneUnknown", "text": {"index": 1242472140, "text": "基因未知"}}, {"id": 988, "constName": "BatchUpgrading", "text": {"index": 1483606805, "text": "基地升级中"}}, {"id": 989, "constName": "BatchUpgrade", "text": {"index": 1874998039, "text": "基地升级"}}, {"id": 990, "constName": "StandardMode", "text": {"index": 654943928, "text": "标准模式"}}, {"id": 991, "constName": "BomberMode", "text": {"index": 1528962954, "text": "炸家模式"}}, {"id": 992, "constName": "OtherMode", "text": {"index": 1549998368, "text": "其他模式"}}, {"id": 993, "constName": "TeachBook", "text": {"index": 1276949920, "text": "教学手册"}}, {"id": 994, "constName": "ReputationShutdownNormalTitle", "text": {"index": 447108531, "text": "关闭情报站？"}}, {"id": 995, "constName": "ReputationShutdownNormalTips", "text": {"index": 1689821923, "text": "关闭后，您的情报等级不会受到影响，您和您的团队成员都可以将自己的任何工具柜升级为情报站。"}}, {"id": 996, "constName": "ReputationShutdownFaildTitle", "text": {"index": 1779359065, "text": "关闭情报站失败"}}, {"id": 997, "constName": "ReputationShutdownFaildTips", "text": {"index": 511101770, "text": "在关闭之前，您必须确保所有情报文件已从此情报站完全移除。"}}, {"id": 998, "constName": "HideTeamCombatMarker", "text": {"index": 1216905780, "text": "屏蔽队友标记"}}, {"id": 999, "constName": "ShowSelectedTeamCombatMarker", "text": {"index": 12617720, "text": "屏蔽后是否展示确认标记"}}, {"id": 1000, "constName": "PlacedPartNum", "text": {"index": 749537783, "text": "已放置数{0}"}}, {"id": 1001, "constName": "GameInside", "text": {"index": 597256683, "text": "游戏中"}}, {"id": 1002, "constName": "PleaseAddRes", "text": {"index": 29682585, "text": "请添加资源"}}, {"id": 1003, "constName": "NameInputHint", "text": {"index": 2080991447, "text": "点击输入您的昵称"}}, {"id": 1004, "constName": "OpenSafeBox", "text": {"index": 1400628452, "text": "打开保险柜"}}, {"id": 1005, "constName": "ModifySafeBox", "text": {"index": 605796514, "text": "改造为保险工具柜"}}, {"id": 1006, "constName": "NonFriend", "text": {"index": 794384215, "text": "非好友"}}, {"id": 1007, "constName": "DeployRepair", "text": {"index": 1529377492, "text": "点击按钮维修这个摆件"}}, {"id": 1008, "constName": "VehicleRepair", "text": {"index": 1507986558, "text": "点击按钮维修这个载具"}}, {"id": 1009, "constName": "Use", "text": {"index": 866930763, "text": "使用"}}, {"id": 1010, "constName": "InUse", "text": {"index": 1910988054, "text": "使用中"}}, {"id": 1011, "constName": "NotOwned", "text": {"index": 418782805, "text": "未拥有"}}, {"id": 1012, "constName": "SelfAidProb", "text": {"index": 1076162108, "text": "自动自救概率"}}, {"id": 1013, "constName": "ConfirmDeleteFriend", "text": {"index": 2111628376, "text": "是否确认删除{0}"}}, {"id": 1014, "constName": "SuccessUnlockBlueprint", "text": {"index": 1239759618, "text": "你学会了一个新蓝图！"}}, {"id": 1015, "constName": "PowerConsumption", "text": {"index": 1618236743, "text": "耗电量"}}, {"id": 1016, "constName": "GenerationCapacity", "text": {"index": 2048019251, "text": "输出功率"}}, {"id": 1017, "constName": "DeleteFriend", "text": {"index": 1500432861, "text": "删除好友"}}, {"id": 1018, "constName": "ReportPlayer", "text": {"index": 1130921411, "text": "举报玩家"}}, {"id": 1019, "constName": "BuildingComponents", "text": {"index": 1891708918, "text": "建造组件"}}, {"id": 1020, "constName": "Placeables", "text": {"index": 148106178, "text": "可放置"}}, {"id": 1021, "constName": "OfficialBase", "text": {"index": 453623527, "text": "官方小屋"}}, {"id": 1022, "constName": "Current<PERSON>ower", "text": {"index": 1484095808, "text": "当前功率:{0}/{1}"}}, {"id": 1023, "constName": "CurrentFlow", "text": {"index": 1222722812, "text": "当前流速:{0}/{1}"}}, {"id": 1047, "constName": "NetworkAbnormal", "text": {"index": 207264516, "text": "网络异常，是否尝试重连？"}}, {"id": 1048, "constName": "Owned", "text": {"index": 1575855486, "text": "拥有"}}, {"id": 1049, "constName": "ReportPlayerName", "text": {"index": 958099078, "text": "是否举报该玩家昵称违规"}}, {"id": 1050, "constName": "ElectricInfoChargeLeft", "text": {"index": 1725293482, "text": "可供电时长"}}, {"id": 1051, "constName": "ElectricInfoChargeTime", "text": {"index": 736868234, "text": "剩余充电时间"}}, {"id": 1052, "constName": "ElectricInfoUsage", "text": {"index": 1388711285, "text": "输出功率"}}, {"id": 1053, "constName": "ElectricInfoPower", "text": {"index": 231490874, "text": "储电量"}}, {"id": 1054, "constName": "ElectricInfoPlugPart", "text": {"index": 1812959350, "text": "待连接元件"}}, {"id": 1055, "constName": "ElectricInfoPlugSlot", "text": {"index": 2106537687, "text": "待连接端口"}}, {"id": 1056, "constName": "ConfrimBlockFriend", "text": {"index": 1502779403, "text": "该玩家为你的好友，是否确定屏蔽？"}}, {"id": 1057, "constName": "ConfirmUnBlock", "text": {"index": 108724877, "text": "是否解除对该玩家的屏蔽？"}}, {"id": 1058, "constName": "BlockPlayer", "text": {"index": 1289867449, "text": "屏蔽玩家"}}, {"id": 1059, "constName": "UnBlock", "text": {"index": 507887334, "text": "解除屏蔽"}}, {"id": 1060, "constName": "ElectrictCancelLastPoint", "text": {"index": 1915169554, "text": "取消上次打点"}}, {"id": 1061, "constName": "ElectrictClearAllPoints", "text": {"index": 1504580900, "text": "清除所有打点"}}, {"id": 1062, "constName": "ProhibitMountHorse", "text": {"index": 2077194582, "text": "穿戴重型套装，无法进行骑乘"}}, {"id": 1063, "constName": "SwipeCardGameBlackout", "text": {"index": 248365868, "text": "断电将会关闭电路盒通电状态，相连的开关将会关闭，读卡器将无法刷卡，是否确认断电？"}}, {"id": 1064, "constName": "btnEffectScore", "text": {"index": 1452138624, "text": "开启特效性能得分显示"}}, {"id": 1065, "constName": "SafetyAreaDistance", "text": {"index": 2017592381, "text": "[color=#faf3ec][size=24]距离安全区还有[/size][/color][color=#ff6266][size=24]{0}[/size][/color][color=#faf3ec][size=24]米[/size][/color]"}}, {"id": 1066, "constName": "btnEffectBucket", "text": {"index": 1596746529, "text": "开启特效桶"}}, {"id": 1067, "constName": "RepairMaterials", "text": {"index": 936019671, "text": "维修材料"}}, {"id": 1068, "constName": "DoubleClickToGet", "text": {"index": 499962522, "text": "[color=#c7f279]双击[/color]取回"}}, {"id": 1069, "constName": "FpSwitchText", "text": {"index": **********, "text": "第一人称"}}, {"id": 1070, "constName": "TpSwitchText", "text": {"index": 452992921, "text": "第三人称"}}, {"id": 1071, "constName": "btnTpsSwitch", "text": {"index": 646335209, "text": "第三人称切换功能"}}, {"id": 1072, "constName": "Stock", "text": {"index": 977642580, "text": "库存："}}, {"id": 1073, "constName": "ShowWaterMark", "text": {"index": 320135923, "text": "显示水印"}}, {"id": 1074, "constName": "Health", "text": {"index": **********, "text": "生命值"}}, {"id": 1075, "constName": "Enginetotalpower", "text": {"index": **********, "text": "引擎总功率"}}, {"id": 1076, "constName": "Stamina", "text": {"index": **********, "text": "体力上限"}}, {"id": 1077, "constName": "Maxspeed", "text": {"index": 843074029, "text": "最大速度"}}, {"id": 1078, "constName": "Fuelconsumption", "text": {"index": **********, "text": "每分钟油耗"}}, {"id": 1079, "constName": "Storagecapacity", "text": {"index": **********, "text": "存储容量"}}, {"id": 1080, "constName": "Hungerlevel", "text": {"index": 414220333, "text": "饥饿程度"}}, {"id": 1081, "constName": "Corrosionconditions", "text": {"index": **********, "text": "腐蚀程度"}}, {"id": 1082, "constName": "Mildcorrosion", "text": {"index": **********, "text": "轻度腐蚀"}}, {"id": 1083, "constName": "Moderatecorrosion", "text": {"index": **********, "text": "中度腐蚀"}}, {"id": 1084, "constName": "Severecorrosion", "text": {"index": 596749602, "text": "重度腐蚀"}}, {"id": 1085, "constName": "Nocorrosion", "text": {"index": 1641586094, "text": "无"}}, {"id": 1086, "constName": "Hungercorrodes", "text": {"index": 88224157, "text": "饥饿"}}, {"id": 1087, "constName": "Satietyerosion", "text": {"index": 1894111989, "text": "饱腹"}}, {"id": 1088, "constName": "Barrel", "text": {"index": 1898429779, "text": ""}}, {"id": 1089, "constName": "MusicSound", "text": {"index": 1461040836, "text": "音乐音量"}}, {"id": 1090, "constName": "WinDowSound", "text": {"index": 958600075, "text": "界面音量"}}, {"id": 1091, "constName": "LowThreat", "text": {"index": 463851355, "text": "低威胁"}}, {"id": 1092, "constName": "MidThreat", "text": {"index": 137683161, "text": "中威胁"}}, {"id": 1093, "constName": "HighThreat", "text": {"index": 1566092849, "text": "高威胁"}}, {"id": 1094, "constName": "ExtremeThreat", "text": {"index": 2140212036, "text": "极度威胁"}}, {"id": 1095, "constName": "NoModuleCode", "text": {"index": 1992403623, "text": "错误 541"}}, {"id": 1096, "constName": "NoModuleResult", "text": {"index": 1772388314, "text": "请先安装模块，再安装导弹"}}, {"id": 1097, "constName": "InCdCode", "text": {"index": 552316680, "text": "错误 412"}}, {"id": 1098, "constName": "InCdResult", "text": {"index": 1251716026, "text": "多管火箭系统处于冷却时间中，请稍后尝试"}}, {"id": 1099, "constName": "MildcorrosionText", "text": {"index": 1722816188, "text": "载具放置在安全区，受到轻微的腐蚀伤害"}}, {"id": 1100, "constName": "ModeratecorrosionText", "text": {"index": 988973401, "text": "载具放置在室内，受到较少的腐蚀伤害"}}, {"id": 1101, "constName": "SeverecorrosionText", "text": {"index": 1834589806, "text": "载具放置在野外，受到较大的腐蚀伤害"}}, {"id": 1102, "constName": "NocorrosionText", "text": {"index": 1156420723, "text": "载具使用中，不会进入腐蚀状态"}}, {"id": 1103, "constName": "HungercorrodesText", "text": {"index": 1346314650, "text": "马匹未进食进入腐蚀状态，生命值持续流失"}}, {"id": 1104, "constName": "SatietyerosionText", "text": {"index": 1124588889, "text": "马匹饱腹状态"}}, {"id": 1105, "constName": "ConnectWire", "text": {"index": 2006490934, "text": "连接电线"}}, {"id": 1106, "constName": "Placement", "text": {"index": 1239135810, "text": "放置摆件"}}, {"id": 1107, "constName": "Modify", "text": {"index": 1108830227, "text": "改造"}}, {"id": 1108, "constName": "ConnectPipe", "text": {"index": 1881676513, "text": "连接水管"}}, {"id": 1109, "constName": "Build", "text": {"index": 985845760, "text": "建造"}}, {"id": 1110, "constName": "ClearWire", "text": {"index": 1603417605, "text": "拆线"}}, {"id": 1111, "constName": "Destory", "text": {"index": 2032930030, "text": "删除"}}, {"id": 1112, "constName": "PickUp", "text": {"index": 58401053, "text": "拾取"}}, {"id": 1113, "constName": "OthersideInventoryWearText", "text": {"index": 1729405059, "text": "{0}的武器/装备"}}, {"id": 1114, "constName": "OthersideInventoryBeltText", "text": {"index": 1837784435, "text": "{0}的快捷栏"}}, {"id": 1115, "constName": "OthersideInventoryMainText", "text": {"index": 209025305, "text": "{0}的背包"}}, {"id": 1116, "constName": "WaterInterface", "text": {"index": 938164642, "text": "水利接口"}}, {"id": 1117, "constName": "ReputationContainerTitle", "text": {"index": 1635475306, "text": "情报补给箱 - {0}专属"}}, {"id": 1118, "constName": "LobbyGaming", "text": {"index": 135200268, "text": "游戏中"}}, {"id": 1119, "constName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text": {"index": 961965343, "text": "组队中"}}, {"id": 1120, "constName": "MildcorrosionIcon", "text": {"index": 999466435, "text": "载具受到轻度腐蚀，生命值持续流失"}}, {"id": 1121, "constName": "ModeratecorrosionIcon", "text": {"index": 1351677529, "text": "载具受到中度腐蚀，生命值持续流失"}}, {"id": 1122, "constName": "SeverecorrosionIcon", "text": {"index": 2079963019, "text": "载具受到重度腐蚀，生命值持续流失"}}, {"id": 1123, "constName": "btnFootstepBucket", "text": {"index": 1674290943, "text": "脚步检测频率桶"}}, {"id": 1124, "constName": "Accept", "text": {"index": 1459767048, "text": "接受"}}, {"id": 1125, "constName": "Reject", "text": {"index": 1396261668, "text": "拒绝"}}, {"id": 1126, "constName": "TeamInvite", "text": {"index": 563003098, "text": "组队邀请"}}, {"id": 1127, "constName": "WaterVelocity", "text": {"index": 596548645, "text": "流速"}}, {"id": 1128, "constName": "ElectricPower", "text": {"index": 2025636761, "text": "功率"}}, {"id": 1129, "constName": "ElectricPort", "text": {"index": 2007824961, "text": "电力端口"}}, {"id": 1130, "constName": "ResHandleConfirm", "text": {"index": 1267475172, "text": "确认"}}, {"id": 1131, "constName": "ResHandleConfirmAdd", "text": {"index": 2040752975, "text": "确认添加"}}, {"id": 1132, "constName": "LeftTransformTime", "text": {"index": 1818018154, "text": "剩余改造次数"}}, {"id": 1134, "constName": "ExtendPackReplaceTips4", "text": {"index": 1678203800, "text": "背包已满,替换扩容背包后将丢弃部分道具,包含道具:{0}"}}, {"id": 1135, "constName": "TargetSeatIsNotEmpty", "text": {"index": 186537482, "text": "无法乘坐，已有其他玩家"}}, {"id": 1136, "constName": "StayInPlace", "text": {"index": 1100209837, "text": "留在原地"}}, {"id": 1137, "constName": "ToTheGround", "text": {"index": 1281767949, "text": "丢在地上"}}, {"id": 1138, "constName": "SurprisePlayRankTitle1", "text": {"index": 1434977183, "text": "破译大师"}}, {"id": 1139, "constName": "SurprisePlayRankTitle2", "text": {"index": 1413033172, "text": "探索先驱"}}, {"id": 1140, "constName": "SurprisePlayRankTitle3", "text": {"index": 593065519, "text": "通灵者"}}, {"id": 1141, "constName": "SurprisePlayRankTitle4", "text": {"index": 1313763890, "text": "失意者"}}, {"id": 1142, "constName": "NameCardRuler2", "text": {"index": 672791942, "text": "输入的昵称过长"}}, {"id": 1143, "constName": "Level", "text": {"index": 1582017476, "text": "{0}级"}}, {"id": 1144, "constName": "ProtectTimeLeft1", "text": {"index": 789704236, "text": "腐蚀保护时间剩余{0}天{1}小时{2}分钟。"}}, {"id": 1145, "constName": "ProtectTimeLeft2", "text": {"index": 1575792378, "text": "腐蚀保护时间剩余{0}小时{1}分钟。"}}, {"id": 1146, "constName": "ProtectTimeLeft3", "text": {"index": 1577333906, "text": "腐蚀保护时间剩余{0}分钟。"}}, {"id": 1147, "constName": "UsingItem_Reloading", "text": {"index": 1244536214, "text": "换弹中..."}}, {"id": 1148, "constName": "UsingItem_Engaging", "text": {"index": 33687199, "text": "使用中..."}}, {"id": 1149, "constName": "HorseStorage", "text": {"index": 1409092471, "text": "马背包"}}, {"id": 1150, "constName": "WoodenWaistcoat", "text": {"index": 1509149273, "text": "木制马甲"}}, {"id": 1151, "constName": "RightSaddleBag", "text": {"index": 1318388484, "text": "右马鞍包"}}, {"id": 1152, "constName": "LeftSaddleBag", "text": {"index": 1385632615, "text": "左马鞍包"}}, {"id": 1153, "constName": "HorseEquipment", "text": {"index": 1065316145, "text": "装备栏"}}, {"id": 1154, "constName": "EngineAccessories", "text": {"index": 755670425, "text": "引擎"}}, {"id": 1155, "constName": "StorageModules", "text": {"index": 308169820, "text": "储物模块"}}, {"id": 1156, "constName": "StorageOtherVehicles", "text": {"index": 381136043, "text": "储物"}}, {"id": 1157, "constName": "DragWeaponItemHere", "text": {"index": 1196005117, "text": "拖拽枪械道具到该位置"}}, {"id": 1158, "constName": "HudGenericMessage", "text": {"index": 1828831687, "text": "[color={0}]\\[{1}][/color] {2}: {3}"}}, {"id": 1159, "constName": "RaidIsInvalid", "text": {"index": 2084517797, "text": "当前禁止领地突袭，无法对领地内建筑造成伤害"}}, {"id": 1160, "constName": "SETTINGS", "text": {"index": 1245066397, "text": "设置"}}, {"id": 1161, "constName": "ItemSplit_AllFull", "text": {"index": 934708303, "text": "目标容器已满，继续拆分将会自动丢弃，是否继续？"}}, {"id": 1162, "constName": "ServerStartTime", "text": {"index": 1176452607, "text": "战局启动时间"}}, {"id": 1163, "constName": "Survived", "text": {"index": 798441309, "text": "已生存"}}, {"id": 1164, "constName": "Day", "text": {"index": 1846680964, "text": "天"}}, {"id": 1165, "constName": "RaidCurStageTitle1", "text": {"index": 951857865, "text": "领地突袭[color=#d7482c]【允许】[/color]"}}, {"id": 1166, "constName": "RaidCurStageTitle2", "text": {"index": 1937612593, "text": "领地突袭[color=#93bf71]【禁止】[/color]"}}, {"id": 1167, "constName": "RaidCountDown", "text": {"index": 228590703, "text": "{0}倒计时:"}}, {"id": 1168, "constName": "RaidStage1", "text": {"index": 1218879715, "text": "[color=#d7482c]允许领地突袭[/color]"}}, {"id": 1169, "constName": "RaidStage2", "text": {"index": 500440874, "text": "[color=#93bf71]禁止领地突袭[/color]"}}, {"id": 1170, "constName": "RaidTimeRange", "text": {"index": 1236795236, "text": "领地突袭时间段：{0}"}}, {"id": 1171, "constName": "<PERSON><PERSON>", "text": {"index": 499364958, "text": "拾取"}}, {"id": 1172, "constName": "NearbyInteractables", "text": {"index": 540037592, "text": "附近可交互"}}, {"id": 1173, "constName": "HudChatChannelAppointmentColor", "text": {"index": 1923653691, "text": "#9ad849"}}, {"id": 1174, "constName": "HudChatChannelWorldColor", "text": {"index": 754901760, "text": "#ff9234"}}, {"id": 1175, "constName": "HudChatChannelTeamColor", "text": {"index": 981608610, "text": "#9ad849"}}, {"id": 1176, "constName": "HudChatChannelPrivateColor", "text": {"index": 450173743, "text": "#5ecbd5"}}, {"id": 1177, "constName": "LayoutPoints", "text": {"index": 583594754, "text": "打点"}}, {"id": 1178, "constName": "KickedByServer", "text": {"index": 307402181, "text": "踢出服务器"}}, {"id": 1179, "constName": "KickReason", "text": {"index": 431862212, "text": "踢出服务器的原因是:{0}"}}, {"id": 1180, "constName": "SuicideTip1", "text": {"index": 1829646547, "text": "被淘汰会掉落物资，队友救援或倒计时结束有{0}%概率可自动自救，是否确认放弃"}}, {"id": 1181, "constName": "SuicideTip2", "text": {"index": 1352220123, "text": "被淘汰会掉落物资，使用医疗包可进行自救，是否确认放弃"}}, {"id": 1182, "constName": "SettingDisplayPick", "text": {"index": 651940909, "text": "展示简略信息"}}, {"id": 1183, "constName": "DeleteConfirm", "text": {"index": 1521478594, "text": "如果你确定删除此战局，请在下方输入[color=#ff8002]【确认删除】[/color]4个字"}}, {"id": 1184, "constName": "Delete1", "text": {"index": 1409726284, "text": "确认删除"}}, {"id": 1185, "constName": "TotalBattleTime", "text": {"index": 1743573915, "text": "{0:0.#}小时"}}, {"id": 1186, "constName": "DeleteTimeCD", "text": {"index": 1307430071, "text": "{0}后可用"}}, {"id": 1187, "constName": "SvrDisconnectTimeOut", "text": {"index": 1164082127, "text": "与服务器断开连接, 点击尝试重连或返回大厅"}}, {"id": 1188, "constName": "KickedByLogin", "text": {"index": 696841173, "text": "您的账号在其他设备登录，请点击确认重新登录。"}}, {"id": 1189, "constName": "HorseMildcorrosionIcon", "text": {"index": 935782030, "text": "马匹处于轻度饥饿状态，生命值持续流失"}}, {"id": 1190, "constName": "HorseModeratecorrosionIcon", "text": {"index": 1799410550, "text": "马匹处于中度饥饿状态，生命值持续流失"}}, {"id": 1191, "constName": "HorseSeverecorrosionIcon", "text": {"index": 1309928917, "text": "马匹处于重度饥饿状态，生命值持续流失"}}, {"id": 1192, "constName": "MailReceiveAttachmentTip1", "text": {"index": 1866538495, "text": "背包已满，附件无法领取！"}}, {"id": 1193, "constName": "NoCurrentLevel", "text": {"index": 1936619634, "text": "当前建筑不存在该等级"}}, {"id": 1194, "constName": "ChangeNameCDTime", "text": {"index": 1538283141, "text": "改名冷却中{0}"}}, {"id": 1195, "constName": "DirectionNorth", "text": {"index": 101457540, "text": "北"}}, {"id": 1196, "constName": "DirectionNorthEast", "text": {"index": 107048438, "text": "东北"}}, {"id": 1197, "constName": "DirectionEast", "text": {"index": 626213189, "text": "东"}}, {"id": 1198, "constName": "DirectionSouthEast", "text": {"index": 1364308822, "text": "东南"}}, {"id": 1199, "constName": "DirectionSouth", "text": {"index": 611207638, "text": "南"}}, {"id": 1200, "constName": "DirectionSouthWest", "text": {"index": 339206971, "text": "西南"}}, {"id": 1201, "constName": "DirectionWest", "text": {"index": 965710373, "text": "西"}}, {"id": 1202, "constName": "DirectionNorthWest", "text": {"index": 580945681, "text": "西北"}}, {"id": 1204, "constName": "TrainHUDNeutral", "text": {"index": 301409765, "text": "空挡"}}, {"id": 1205, "constName": "TrainHUDForward", "text": {"index": 1697268458, "text": "前进"}}, {"id": 1206, "constName": "TrainHUDReverse", "text": {"index": 138269221, "text": "后退"}}, {"id": 1207, "constName": "TrainHUDLow", "text": {"index": 116864590, "text": "1档"}}, {"id": 1208, "constName": "TrainHUDMedium", "text": {"index": 1034363509, "text": "2档"}}, {"id": 1209, "constName": "TrainHUDHigh", "text": {"index": 1611691135, "text": "3档"}}, {"id": 1210, "constName": "IllegalNaming", "text": {"index": 810577167, "text": "昵称含有不可用字符"}}, {"id": 1212, "constName": "ReMainTimer", "text": {"index": 1295340814, "text": "[color=#FF8F2B][size=36]{0}[/size][/color]秒"}}, {"id": 1213, "constName": "NearbyRespawnNotice", "text": {"index": 852127723, "text": "淘汰点附近重生便于拾取掉落物资，确认不使用淘汰点附近重生吗？"}}, {"id": 1214, "constName": "<PERSON><PERSON><PERSON>", "text": {"index": 794879034, "text": "饱腹值:{0}"}}, {"id": 1215, "constName": "HungerThirst", "text": {"index": 261155896, "text": "水分:{0}"}}, {"id": 1216, "constName": "Far", "text": {"index": 126193476, "text": "远"}}, {"id": 1217, "constName": "Near", "text": {"index": 873358743, "text": "近"}}, {"id": 1218, "constName": "ItemDetailDrop", "text": {"index": 283153872, "text": "掉落"}}, {"id": 1219, "constName": "ItemDetailTrade", "text": {"index": 853104012, "text": "交易"}}, {"id": 1222, "constName": "CraftQueue", "text": {"index": 1244096034, "text": "制作队列"}}, {"id": 1223, "constName": "CraftItemDesc", "text": {"index": 226270535, "text": "道具描述"}}, {"id": 1224, "constName": "CraftCollect", "text": {"index": 784975839, "text": "收藏"}}, {"id": 1225, "constName": "CraftWorkbenchLevelNotEnough", "text": {"index": 818878828, "text": "附近没有{0}级工作台"}}, {"id": 1226, "constName": "CraftNeedMat", "text": {"index": 899485059, "text": "所需材料"}}, {"id": 1227, "constName": "CraftCurrentTechLevel", "text": {"index": 793984624, "text": "当前科技等级"}}, {"id": 1228, "constName": "CraftNoTechLevel", "text": {"index": 1364218130, "text": "无科技等级"}}, {"id": 1229, "constName": "CraftTechLevel1", "text": {"index": 1641707676, "text": "一级科技等级"}}, {"id": 1230, "constName": "CraftTechLevel2", "text": {"index": 251597142, "text": "二级科技等级"}}, {"id": 1231, "constName": "CraftTechLevel3", "text": {"index": 819473626, "text": "三级科技等级"}}, {"id": 1232, "constName": "CraftMatch", "text": {"index": 334061166, "text": "满足"}}, {"id": 1233, "constName": "CraftNotMatch", "text": {"index": 1886981355, "text": "不满足"}}, {"id": 1234, "constName": "ItemDetailTask", "text": {"index": 1412425794, "text": "任务"}}, {"id": 1235, "constName": "ItemDetailTaskReward", "text": {"index": 2116666447, "text": "任务奖励"}}, {"id": 1236, "constName": "ItemDetailJumpTo", "text": {"index": 1464608775, "text": "跳转"}}, {"id": 1211, "constName": "SignUpPopup", "text": {"index": 151240009, "text": "请输入由数字，英文组成的账号名，字符数范围5~16。"}}, {"id": 1237, "constName": "CraftMatNotEnough", "text": {"index": 695700513, "text": "材料不足"}}, {"id": 1239, "constName": "BpTypeRecommend", "text": {"index": 1417554318, "text": "推荐"}}, {"id": 1240, "constName": "QuickTalkSound", "text": {"index": 425572615, "text": "快捷发言音量"}}, {"id": 1241, "constName": "CraftCollectEmptyTip", "text": {"index": 1239613425, "text": "点击 ☆ 可将道具加入收藏"}}, {"id": 1242, "constName": "CraftFilterEmptyTip", "text": {"index": 1202081660, "text": "暂无符合筛选条件的道具"}}, {"id": 1245, "constName": "CloseMsgTip", "text": {"index": 765883538, "text": "关闭"}}, {"id": 1246, "constName": "CloseMsgTip2", "text": {"index": 1125805244, "text": "关闭{0}"}}, {"id": 1247, "constName": "IDText", "text": {"index": 1376365189, "text": "编号:{0}"}}, {"id": 1248, "constName": "LobbyTeamTip5", "text": {"index": 1241269506, "text": "已向{0}发送来自{1}-{2}的组队邀请"}}, {"id": 1249, "constName": "CraftGoToCraft", "text": {"index": 208686109, "text": "制作"}}, {"id": 1250, "constName": "CraftGoToUnlock", "text": {"index": 1095518482, "text": "去解锁"}}, {"id": 1251, "constName": "ReputationLevelShow", "text": {"index": 112166936, "text": "情报等级显示"}}, {"id": 1252, "constName": "TowardForward", "text": {"index": 199236394, "text": "向后"}}, {"id": 1253, "constName": "TowardBack", "text": {"index": 1939515943, "text": "向前"}}, {"id": 1254, "constName": "TowardLeft", "text": {"index": 1713236268, "text": "向左"}}, {"id": 1255, "constName": "TowardRight", "text": {"index": 737591807, "text": "向右"}}, {"id": 1256, "constName": "UnitDay", "text": {"index": 512944580, "text": "天"}}, {"id": 1257, "constName": "BanTimeForever", "text": {"index": 1097542937, "text": "已被永久封禁"}}, {"id": 1258, "constName": "BanTimeRemain", "text": {"index": 1612415936, "text": "解封剩余时间：{0}"}}, {"id": 1259, "constName": "RecruitLeaveTeam", "text": {"index": 1141244912, "text": "点击确认将离开队伍，是否继续？"}}, {"id": 1260, "constName": "MapDownloadFailed", "text": {"index": 1829338420, "text": "地图包下载失败。"}}, {"id": 1261, "constName": "NotConnected", "text": {"index": 596864788, "text": "未接通"}}, {"id": 1262, "constName": "Retry", "text": {"index": 1400838440, "text": "重试"}}, {"id": 1263, "constName": "Place", "text": {"index": 67834699, "text": "放置"}}, {"id": 1264, "constName": "TechtreePrimaryTab0", "text": {"index": 780567741, "text": "手工科技"}}, {"id": 1265, "constName": "TechtreePrimaryTab1", "text": {"index": 111243672, "text": "一级科技"}}, {"id": 1266, "constName": "TechtreePrimaryTab2", "text": {"index": 1406619361, "text": "二级科技"}}, {"id": 1267, "constName": "TechtreePrimaryTab3", "text": {"index": 68322878, "text": "三级科技"}}, {"id": 1268, "constName": "ElectricSlotNotConnectedGary", "text": {"index": 1964911930, "text": "#BDBEBE"}}, {"id": 1269, "constName": "ElectricSlotSelected", "text": {"index": 472705608, "text": "#FF7900"}}, {"id": 1270, "constName": "MapShopDetailLeftNum", "text": {"index": 238046311, "text": "剩余:{0}"}}, {"id": 1271, "constName": "HandBook", "text": {"index": 1158863962, "text": "生存手册"}}, {"id": 1273, "constName": "ToolCupBoard", "text": {"index": 2068136052, "text": "工具柜"}}, {"id": 1274, "constName": "PickListDropItem", "text": {"index": 409839482, "text": "{0}掉落"}}, {"id": 1275, "constName": "CustomPanelCategoriesBase", "text": {"index": 2112782269, "text": "基础"}}, {"id": 1276, "constName": "CustomPanelCategoriesVehicle", "text": {"index": 1452616559, "text": "载具"}}, {"id": 1277, "constName": "CustomPanelCategoriesBuilding", "text": {"index": 215278028, "text": "建造"}}, {"id": 1278, "constName": "CustomPanelCategoriesAll", "text": {"index": 27065841, "text": "全部"}}, {"id": 2000, "constName": "Input1", "text": {"index": 2083496496, "text": "输入1"}}, {"id": 2001, "constName": "Input2", "text": {"index": 730738278, "text": "输入2"}}, {"id": 2002, "constName": "Output", "text": {"index": 529233888, "text": "输出"}}, {"id": 2003, "constName": "Input", "text": {"index": 1571023947, "text": "输入"}}, {"id": 2004, "constName": "Targeting", "text": {"index": 970977759, "text": "目标锁定"}}, {"id": 2005, "constName": "LowAmmo", "text": {"index": 705154348, "text": "弹量低"}}, {"id": 2006, "constName": "NoAmmo", "text": {"index": 103574450, "text": "无弹药"}}, {"id": 2007, "constName": "Block", "text": {"index": 298262213, "text": "阻断"}}, {"id": 2008, "constName": "PassThrough", "text": {"index": 725646066, "text": "通电"}}, {"id": 2009, "constName": "Increment", "text": {"index": 1714688993, "text": "增量"}}, {"id": 2010, "constName": "Decrement", "text": {"index": 100338454, "text": "递减"}}, {"id": 2011, "constName": "Clear", "text": {"index": 597155370, "text": "清除"}}, {"id": 2012, "constName": "BranchOutput", "text": {"index": 1981445377, "text": "分支输出"}}, {"id": 2013, "constName": "Toggle", "text": {"index": 206939184, "text": "切换"}}, {"id": 2014, "constName": "CallEle1", "text": {"index": 2103921023, "text": "呼叫电梯1"}}, {"id": 2015, "constName": "CallEle2", "text": {"index": 1940179428, "text": "呼叫电梯2"}}, {"id": 2016, "constName": "Input3", "text": {"index": 739796323, "text": "输入3"}}, {"id": 2017, "constName": "Output1", "text": {"index": 1711632555, "text": "输出1"}}, {"id": 2018, "constName": "Output2", "text": {"index": 1493764809, "text": "输出2"}}, {"id": 2019, "constName": "Output3", "text": {"index": 649546630, "text": "输出3"}}, {"id": 2020, "constName": "PowerIn", "text": {"index": 780334136, "text": "输入端口"}}, {"id": 2021, "constName": "WaterIn", "text": {"index": 1498132754, "text": "入水口"}}, {"id": 2022, "constName": "WaterOut", "text": {"index": 148811241, "text": "出水口"}}, {"id": 2023, "constName": "Set", "text": {"index": 87758059, "text": "设置"}}, {"id": 2024, "constName": "Reset", "text": {"index": 1537464082, "text": "重置"}}, {"id": 2025, "constName": "InvertedOutput", "text": {"index": 2014567445, "text": "反相输出"}}, {"id": 2026, "constName": "Output4", "text": {"index": 789750207, "text": "输出4"}}, {"id": 2027, "constName": "Output5", "text": {"index": 175695212, "text": "输出5"}}, {"id": 2028, "constName": "Output6", "text": {"index": 2030442676, "text": "输出6"}}, {"id": 2029, "constName": "Output7", "text": {"index": 192675327, "text": "输出7"}}, {"id": 2030, "constName": "Output8", "text": {"index": 1531198981, "text": "输出8"}}, {"id": 2031, "constName": "Input4", "text": {"index": 1529241297, "text": "输入4"}}, {"id": 2032, "constName": "Input5", "text": {"index": 1433716843, "text": "输入5"}}, {"id": 2033, "constName": "Input6", "text": {"index": 1994349937, "text": "输入6"}}, {"id": 2034, "constName": "Input7", "text": {"index": 732215040, "text": "输入7"}}, {"id": 2035, "constName": "Input8", "text": {"index": 1478624813, "text": "输入8"}}, {"id": 2036, "constName": "Start", "text": {"index": 147090879, "text": "开始"}}, {"id": 2037, "constName": "Stop", "text": {"index": 694102509, "text": "停止"}}, {"id": 2038, "constName": "SwitchOn", "text": {"index": 1446239718, "text": "开启"}}, {"id": 2039, "constName": "SwitchOff", "text": {"index": 236481191, "text": "关闭"}}, {"id": 2040, "constName": "DefenceStrong", "text": {"index": 1794665934, "text": "强防御"}}, {"id": 2041, "constName": "DefenceWeak", "text": {"index": 1131825346, "text": "弱防御"}}, {"id": 2042, "constName": "CloudArchive", "text": {"index": 1917683913, "text": "方案"}}, {"id": 2043, "constName": "ArchiveSaveSuccess", "text": {"index": 1136988613, "text": "成功保存至{0}！"}}, {"id": 2045, "constName": "WaterPipeColor", "text": {"index": 1962839533, "text": "水管颜色"}}, {"id": 2046, "constName": "PermMgrMember", "text": {"index": 1082702180, "text": "管理成员"}}, {"id": 2047, "constName": "Res<PERSON>ersonError", "text": {"index": 2001985534, "text": "客户端\\服务器资源包版本不一致，请重启游戏更新"}}, {"id": 2048, "constName": "OtherWise", "text": {"index": 716468457, "text": "否则"}}, {"id": 2049, "constName": "HostileAttackTips", "text": {"index": 671620528, "text": "[color=#faf3ec][size=24]否则[/size][/color][color=#ff6266][size=24]{0}[/size][/color][color=#faf3ec][size=24]秒后发起攻击[/size][/color]"}}, {"id": 2050, "constName": "ShopBuyNow", "text": {"index": 938988119, "text": "立即购买"}}, {"id": 2051, "constName": "ShopSoldOut", "text": {"index": 55742757, "text": "商品售罄"}}, {"id": 2052, "constName": "ShopBuying", "text": {"index": 467458977, "text": "购买中"}}, {"id": 2053, "constName": "ShopCannotBuy", "text": {"index": 1485372369, "text": "无法购买"}}, {"id": 2054, "constName": "RequestNetworkRetry", "text": {"index": 361963763, "text": "网络连接失败，请重试"}}, {"id": 2055, "constName": "ReputationRewardBtn", "text": {"index": 117962188, "text": "奖励"}}, {"id": 2057, "constName": "ConnectNewServerTimeOutTip", "text": {"index": 1750785433, "text": "匹配中断，正在恢复中，请稍候"}}, {"id": 2060, "constName": "TemperatureSymbol", "text": {"index": 1864283457, "text": "℃"}}, {"id": 2061, "constName": "BigMapMyShops", "text": {"index": 899874450, "text": "我的商店"}}, {"id": 2062, "constName": "BigMapOtherPlayerShops", "text": {"index": 1469346102, "text": "其他玩家商店"}}, {"id": 2063, "constName": "PointTaskRewardNum", "text": {"index": 1170994094, "text": "每日可领取奖励次数{0}/{1}"}}, {"id": 2064, "constName": "BoxTaskProgress", "text": {"index": 319559291, "text": "任务进度:{0}"}}, {"id": 2065, "constName": "Finished", "text": {"index": 1615379603, "text": "已完成"}}, {"id": 2066, "constName": "ComHandlePanelEmptyTips1", "text": {"index": 1924495538, "text": "你的库存中没有这些物品。"}}, {"id": 2067, "constName": "ComHandlePanelEmptyTips2", "text": {"index": 122185987, "text": "你的输入中没有这些物品。"}}, {"id": 2068, "constName": "InitResourceManifestFailed", "text": {"index": 997365314, "text": "获取patch列表失败"}}, {"id": 2069, "constName": "Quit", "text": {"index": 1577257897, "text": "退出"}}, {"id": 2070, "constName": "RetryDownload", "text": {"index": 753271969, "text": "重试"}}, {"id": 2071, "constName": "DownloadResourceFailed", "text": {"index": 1942312074, "text": "下载资源失败"}}, {"id": 2072, "constName": "NoInternetConnection", "text": {"index": 418475119, "text": "无网络连接"}}, {"id": 2073, "constName": "ClientNeedUpdate", "text": {"index": 1747070536, "text": "检测到有新的客户端版本，请更新"}}, {"id": 2074, "constName": "CurrentNonWiFiEnvironment", "text": {"index": 1438175027, "text": "当前非Wifi网络，是否继续下载？"}}, {"id": 2075, "constName": "InitDependencyFailed", "text": {"index": 493178676, "text": "初始化依赖关系失败，重试中"}}, {"id": 2076, "constName": "StartDownloading", "text": {"index": 1092572784, "text": "开始下载"}}, {"id": 2077, "constName": "DownloadingResPackNow", "text": {"index": 1830794359, "text": "正在下载"}}, {"id": 2078, "constName": "DownloadResPackFailed", "text": {"index": 1233022834, "text": "下载失败，正在重试  "}}, {"id": 2079, "constName": "Download", "text": {"index": 280138313, "text": "下载"}}, {"id": 2080, "constName": "PleaseInstallApk", "text": {"index": 663199912, "text": "请安装新版本客户端"}}, {"id": 2081, "constName": "DownloadResFailedWithErrorCode", "text": {"index": 1467387310, "text": "下载失败，请检查网络后重试。错误码："}}, {"id": 2082, "constName": "PreloadingResPacks", "text": {"index": 1085473000, "text": "正在加载资源(不消耗流量)"}}, {"id": 2083, "constName": "SkipUpdate", "text": {"index": 1948451909, "text": "跳过更新"}}, {"id": 2084, "constName": "InitResPackList", "text": {"index": 1163966170, "text": "初始化资源列表"}}, {"id": 2085, "constName": "InsufficientDiskSpace", "text": {"index": 637638323, "text": "存储空间不足"}}, {"id": 2086, "constName": "CheckVersion", "text": {"index": 1595045676, "text": "检查版本中"}}, {"id": 2087, "constName": "SourceAnalyseDiff", "text": {"index": 1744959617, "text": "资源对比中"}}, {"id": 2088, "constName": "SourceExtract", "text": {"index": 1908961347, "text": "正在解压(不消耗流量)"}}, {"id": 2089, "constName": "FixSource", "text": {"index": 2078385254, "text": "正在修复损坏的资源文件"}}, {"id": 2090, "constName": "PrepareSource", "text": {"index": 587324650, "text": "正在准备资源"}}, {"id": 2091, "constName": "MinOffsetDesc", "text": {"index": 563101056, "text": "{0}分钟前"}}, {"id": 2092, "constName": "HourOffsetDesc", "text": {"index": 1188400749, "text": "{0}小时前"}}, {"id": 2093, "constName": "DayOffsetDesc", "text": {"index": 1202441610, "text": "{0}天前"}}, {"id": 2094, "constName": "MonthOffsetDesc", "text": {"index": 1722596403, "text": "30天前"}}, {"id": 2095, "constName": "SettingOpenMirrorTouchThrough", "text": {"index": 399833646, "text": "开镜按钮穿透滑屏"}}, {"id": 2096, "constName": "SettingJumpTouchThrough", "text": {"index": 531662795, "text": "跳跃按钮穿透滑屏"}}, {"id": 2097, "constName": "SettingCrouchTouchThrough", "text": {"index": 205080044, "text": "下蹲按钮穿透滑屏"}}, {"id": 2098, "constName": "FriendNumsDesc", "text": {"index": 761417140, "text": "游戏好友：{0}/{1}"}}, {"id": 2099, "constName": "SettingDynamicBattle", "text": {"index": 1078427915, "text": "新手关"}}, {"id": 2100, "constName": "SettingStartDynamicBattle", "text": {"index": 413666194, "text": "进入"}}, {"id": 2101, "constName": "TechTreeTips1", "text": {"index": 1873277045, "text": "<img src='ui://CommonGlobal/Techtree_img_qgzt' width='30' height='30'/>附近需要一级工作台"}}, {"id": 2102, "constName": "TechTreeTips2", "text": {"index": 807588477, "text": "<img src='ui://CommonGlobal/Techtree_img_qgzt' width='30' height='30'/>附近需要二级工作台"}}, {"id": 2103, "constName": "TechTreeTips3", "text": {"index": 1909437657, "text": "<img src='ui://CommonGlobal/Techtree_img_qgzt' width='30' height='30'/>附近需要三级工作台"}}, {"id": 2104, "constName": "AutoCloseDoorHint", "text": {"index": 1904052748, "text": "{0}秒后自动关闭"}}, {"id": 2105, "constName": "SettingAutoCloseDoor", "text": {"index": 1516146728, "text": "仅自动关门"}}, {"id": 2106, "constName": "SettingAutoCloseDoorTime", "text": {"index": 1558408793, "text": "自动关门时间设置"}}, {"id": 2107, "constName": "HorseWeakTitle", "text": {"index": 128391570, "text": "需要进食"}}, {"id": 2108, "constName": "HorseWeakContent", "text": {"index": 272406306, "text": "马匹生命值较低或处于饥饿状态，需要尽快投喂食物"}}, {"id": 2109, "constName": "RouletteTextAttack", "text": {"index": 452347577, "text": "攻击"}}, {"id": 2110, "constName": "RouletteTextDefense", "text": {"index": 635602472, "text": "防御"}}, {"id": 2111, "constName": "RouletteTextFoundEnemy", "text": {"index": 1520506312, "text": "发现敌人"}}, {"id": 2112, "constName": "RouletteTextEnemyPassed", "text": {"index": 633933377, "text": "敌人经过"}}, {"id": 2113, "constName": "BigMapEmptyBed", "text": {"index": 85901578, "text": "暂无睡袋"}}, {"id": 2114, "constName": "BigMapEmptyTerritory", "text": {"index": 1115456316, "text": "暂无领地"}}, {"id": 2115, "constName": "SettingOpenDoorAutoDir", "text": {"index": 1468518481, "text": "顺方向开门"}}, {"id": 2116, "constName": "IntimacyDesc", "text": {"index": 1841882565, "text": "友好度代表您与好友互动的多寡，当您与好友合作游戏时，友好度会不断增加。\n在未来友好度可解锁更多的玩法。"}}, {"id": 2117, "constName": "IntimacyGet", "text": {"index": 665921795, "text": "与好友在对局内组队，随共同在线时长增加(每次离开对局结算)"}}, {"id": 2118, "constName": "IntimacyTitle", "text": {"index": 1357683320, "text": "友好度"}}, {"id": 2119, "constName": "TempFriendTitle", "text": {"index": 907749580, "text": "临时好友"}}, {"id": 2120, "constName": "RemainCount1", "text": {"index": 451037701, "text": "野外剩余摆放数量：[color=#FD5252]{0}/[/color]{1}"}}, {"id": 2121, "constName": "RemainCount2", "text": {"index": 1448543927, "text": "野外剩余摆放数量：[color=#FFFFFF]{0}/[/color]{1}"}}, {"id": 2122, "constName": "HaveCount", "text": {"index": 210566797, "text": "拥有：[color=#D8D8D8]{0}[/color]"}}, {"id": 2123, "constName": "CombineDesc", "text": {"index": 1647906650, "text": "组合建筑"}}, {"id": 2124, "constName": "CoreDesc", "text": {"index": 1257862488, "text": "核心建筑"}}, {"id": 2125, "constName": "BlueprintTipDes", "text": {"index": 296592593, "text": "蓝图由声望系统获取，声望界面在领地柜打开"}}, {"id": 2126, "constName": "ShowHudEffectArea", "text": {"index": 323870723, "text": "显示Hud按钮热区"}}, {"id": 2127, "constName": "Num1", "text": {"index": 38759662, "text": "一"}}, {"id": 2128, "constName": "Num2", "text": {"index": 20757721, "text": "二"}}, {"id": 2129, "constName": "Num3", "text": {"index": 1261837976, "text": "三"}}, {"id": 2130, "constName": "Num4", "text": {"index": 654846324, "text": "四"}}, {"id": 2131, "constName": "Num5", "text": {"index": 114872939, "text": "五"}}, {"id": 2132, "constName": "Num6", "text": {"index": 510782063, "text": "六"}}, {"id": 2133, "constName": "Num7", "text": {"index": 650274705, "text": "七"}}, {"id": 2134, "constName": "Num8", "text": {"index": 2021277909, "text": "八"}}, {"id": 2135, "constName": "Num9", "text": {"index": 973751857, "text": "九"}}, {"id": 2136, "constName": "TechTreeNeedWorkBench", "text": {"index": 1716088040, "text": "附近需要{0}级工作台"}}, {"id": 2137, "constName": "LiftingPlatformDurability", "text": {"index": 2140751062, "text": "耐久"}}, {"id": 2138, "constName": "LiftingPlatformSpeed", "text": {"index": 1076630992, "text": "速度"}}, {"id": 2139, "constName": "LiftingPlatformDefense", "text": {"index": 1619269132, "text": "防护"}}, {"id": 2140, "constName": "LiftingPlatformFunction", "text": {"index": 1752386177, "text": "功能"}}, {"id": 2141, "constName": "LiftingPlatformSoltOccupy", "text": {"index": 161611020, "text": "槽位占用 [color=#D3D3D3]{0}[/color]"}}, {"id": 2142, "constName": "LiftingPlatformSoltDurability", "text": {"index": 1546662605, "text": "耐久度 [color=#D3D3D3]{0}[/color]"}}, {"id": 2143, "constName": "LiftingPlatformInstall", "text": {"index": 90379450, "text": "安装"}}, {"id": 2144, "constName": "LiftingPlatformUninstall", "text": {"index": 1032602522, "text": "卸下"}}, {"id": 2145, "constName": "LiftingPlatformReplace", "text": {"index": 826554372, "text": "替换"}}, {"id": 2146, "constName": "LiftingPlatformCraftAndInstall", "text": {"index": 2084506492, "text": "制造并安装"}}, {"id": 2147, "constName": "LiftingPlatformUnlock", "text": {"index": 2058117766, "text": "去解锁"}}, {"id": 2148, "constName": "LiftingPlatformCannotCraft", "text": {"index": 1465579851, "text": "材料不足"}}, {"id": 2149, "constName": "LiftingPlatformSlotNotEnough", "text": {"index": 187070203, "text": "槽位不足"}}, {"id": 2150, "constName": "ConfirmResetCustomHudLayout", "text": {"index": 970977607, "text": "重置当前布局?"}}, {"id": 2151, "constName": "HorseFeed", "text": {"index": 820156077, "text": "[color=#98d14e]投喂[/color] {0}"}}, {"id": 2152, "constName": "HorseFoodEmpty", "text": {"index": 1364742344, "text": "缺少投喂食物"}}, {"id": 2153, "constName": "TechtreeInfoTip", "text": {"index": 537187917, "text": "(包含{0}个前置科技)"}}, {"id": 2154, "constName": "Move", "text": {"index": 860329841, "text": "移动"}}, {"id": 2155, "constName": "Deploy", "text": {"index": 1912314148, "text": "摆放"}}, {"id": 2156, "constName": "TeamTitle", "text": {"index": 464954592, "text": "组队"}}, {"id": 2157, "constName": "TwigGrade", "text": {"index": 99705803, "text": "茅草"}}, {"id": 2158, "constName": "Wood<PERSON><PERSON>", "text": {"index": 2118090720, "text": "木头"}}, {"id": 2159, "constName": "StoneGrade", "text": {"index": 209620086, "text": "石头"}}, {"id": 2160, "constName": "MetalGrade", "text": {"index": 1383992498, "text": "金属"}}, {"id": 2161, "constName": "ToptierGrade", "text": {"index": 947253104, "text": "钢"}}, {"id": 2162, "constName": "ConstructionDefaultGrade", "text": {"index": 287696338, "text": "默认建造材质"}}, {"id": 2163, "constName": "CampingTentCountdownTip", "text": {"index": 1520827297, "text": "摆放中"}}, {"id": 2164, "constName": "LatelyUnLockSurvival", "text": {"index": 1259733863, "text": "最近解锁"}}, {"id": 2165, "constName": "SwipeCardGameGiveUp", "text": {"index": 1565064490, "text": "[color=#FD6F32]退出视为失败，受到生命值扣除惩罚[/color]"}}, {"id": 2166, "constName": "CampingTentSleepTips", "text": {"index": 192697052, "text": "正在帐篷中睡觉"}}, {"id": 2167, "constName": "CampingTentProtectTips", "text": {"index": 1426104936, "text": "保护时长剩余：[color=#ff7900]{0}[/color]"}}, {"id": 2168, "constName": "TechTreeJumpBuildTip", "text": {"index": 750495123, "text": "已解锁，前往建造"}}, {"id": 2169, "constName": "LiftingPlatformUninstallParts", "text": {"index": 1554640339, "text": "卸下配件"}}, {"id": 2170, "constName": "LiftingPlatformClearItems", "text": {"index": 680460704, "text": "清空物品"}}, {"id": 2171, "constName": "LiftingPlatformRepairModule", "text": {"index": 1598084524, "text": "维修以下模块:"}}, {"id": 2172, "constName": "LiftingPlatformRepairMat", "text": {"index": 1848717040, "text": "维修所需材料:"}}, {"id": 2173, "constName": "LiftingPlatformRepairMatNotEnough", "text": {"index": 2022067460, "text": "维修材料不足"}}, {"id": 2184, "constName": "PlayTipDefaultText", "text": {"index": 142126791, "text": "保护时长：[color=#ff7900]{0}[/color]"}}, {"id": 2185, "constName": "BlueprintTipDes1", "text": {"index": 1220388103, "text": "带工具柜"}}, {"id": 2186, "constName": "BlueprintTipDes2", "text": {"index": 1743841786, "text": "不带工具柜"}}, {"id": 2187, "constName": "UiTeamTabQuickInvite", "text": {"index": 357237309, "text": "快捷邀请"}}, {"id": 2188, "constName": "UiTeamTabTeamHall", "text": {"index": 1760145455, "text": "大厅招募"}}, {"id": 2189, "constName": "UiTeamTabRecommendFriend", "text": {"index": 1514394794, "text": "推荐队友"}}, {"id": 2190, "constName": "UiTeamTabInviteInfo", "text": {"index": 1942851069, "text": "邀请信息"}}, {"id": 2191, "constName": "UiTeamTabMyTeam", "text": {"index": 1947326971, "text": "我的小队"}}, {"id": 2192, "constName": "SettingTreasureTask", "text": {"index": 492944848, "text": "夺宝行动"}}, {"id": 2193, "constName": "EnginePartSlotCarburator", "text": {"index": 1657486070, "text": "化油器"}}, {"id": 2194, "constName": "EnginePartSlotCrankshafts", "text": {"index": 1217237265, "text": "曲轴"}}, {"id": 2195, "constName": "EnginePartSlotPistons", "text": {"index": 547783901, "text": "活塞"}}, {"id": 2196, "constName": "EnginePartSlotSpark", "text": {"index": 715219363, "text": "火花塞"}}, {"id": 2197, "constName": "EnginePartSlotValves", "text": {"index": 1247362119, "text": "气阀"}}, {"id": 2198, "constName": "CancelCraft", "text": {"index": 1589336293, "text": "取消制作"}}, {"id": 2199, "constName": "CraftInQueue", "text": {"index": 1675985187, "text": "当前正在制作队列中等待制作"}}, {"id": 2200, "constName": "Crafting", "text": {"index": 694345054, "text": "当前正在制作中"}}, {"id": 2201, "constName": "WaitCraft", "text": {"index": 326682626, "text": "等待制作中"}}, {"id": 2202, "constName": "CraftCompleteWaitGet", "text": {"index": 385760979, "text": "制作完成待领取"}}, {"id": 2203, "constName": "GoToGet", "text": {"index": 1211257792, "text": "前往领取"}}, {"id": 2204, "constName": "CraftCompleteInQueueWaitGet", "text": {"index": 223461311, "text": "当前一直在完成，在制作队列中等待领取"}}, {"id": 2205, "constName": "UiTeamInviteItemGameModel", "text": {"index": 618446194, "text": "模式：{0}"}}, {"id": 2206, "constName": "UiTeamInviteItemSource", "text": {"index": 1799301110, "text": "来源：{0}"}}, {"id": 2207, "constName": "LiftingPlatformContainerParts", "text": {"index": 1132389031, "text": "内含配件"}}, {"id": 2208, "constName": "LiftingPlatformContainerItems", "text": {"index": 940738548, "text": "内含物品"}}, {"id": 2209, "constName": "DeployLock", "text": {"index": 425346426, "text": "该摆件尚未解锁"}}, {"id": 2210, "constName": "CraftingCd", "text": {"index": 2069070913, "text": "制作中\n{0}"}}, {"id": 2211, "constName": "UiTeamFriendStateOnline", "text": {"index": 1502632448, "text": "大厅"}}, {"id": 2212, "constName": "UiTeamFriendStateLobbyTeam", "text": {"index": 942873628, "text": "大厅队伍"}}, {"id": 2213, "constName": "UiTeamFriendStateModeGame", "text": {"index": 1376718437, "text": "游戏中"}}, {"id": 2214, "constName": "UiTeamOpenMicPopTitle", "text": {"index": 2042928270, "text": "开启语音"}}, {"id": 2215, "constName": "UiTeamOpenMicPopMsg", "text": {"index": 1462857535, "text": "加入队伍需要先开启语音，是否开启？"}}, {"id": 2216, "constName": "UiTeamGivePermission", "text": {"index": 416263615, "text": "赋予大厅队友邀请其他人的权限，当您通过【开始游戏】进入战局后队友邀请其他人的权限将会被收回"}}, {"id": 2217, "constName": "CheckWireHint1", "text": {"index": 1977692852, "text": "不是当前连接的目标设备"}}, {"id": 2218, "constName": "CheckWireHint2", "text": {"index": 320461029, "text": "请将视线对准场景中需要查看的水利设备"}}, {"id": 2219, "constName": "CheckWireHint3", "text": {"index": 1647745709, "text": "请将视线对准场景中需要查看的电利设备"}}, {"id": 2220, "constName": "SortTitleDefault", "text": {"index": 1042771490, "text": "默认排序"}}, {"id": 2221, "constName": "SortTitleGainTime", "text": {"index": 1177665150, "text": "获得时间"}}, {"id": 2222, "constName": "WaterConsumption", "text": {"index": 1971304928, "text": "耗水量"}}, {"id": 2223, "constName": "LobbyLoginFail", "text": {"index": 1418950677, "text": "大厅登录失败"}}, {"id": 2224, "constName": "OldBattleServerNum", "text": {"index": 1982009464, "text": "历史战局数量:{0}"}}, {"id": 2225, "constName": "NoConnected", "text": {"index": 1912057833, "text": "未通电"}}, {"id": 2226, "constName": "InOperation", "text": {"index": 449230312, "text": "运行中"}}, {"id": 2227, "constName": "Abnormal<PERSON>ower", "text": {"index": 731962748, "text": "电流不足"}}, {"id": 2228, "constName": "NoPower", "text": {"index": 525852872, "text": "无电流"}}, {"id": 2229, "constName": "StoryStageChangeCD", "text": {"index": 1818457350, "text": "阶段持续时间:{0}"}}, {"id": 2230, "constName": "UiTeamCreateTitle", "text": {"index": 416526184, "text": "创建队伍"}}, {"id": 2231, "constName": "VehicleCorpse", "text": {"index": 536247723, "text": "载具残骸"}}, {"id": 2232, "constName": "RewardPreview", "text": {"index": 1915519912, "text": "奖励预览"}}, {"id": 2233, "constName": "UnlockAndGet", "text": {"index": 1694638390, "text": "解锁并领取"}}, {"id": 2234, "constName": "InGameReward", "text": {"index": 39961773, "text": "战局奖励"}}, {"id": 2235, "constName": "OutGameReward", "text": {"index": 858422194, "text": "账号奖励"}}, {"id": 2236, "constName": "CanGetLevel", "text": {"index": 213949891, "text": "{0}级可获得"}}, {"id": 2237, "constName": "WireTipStateTooFar", "text": {"index": 447055826, "text": "当前准心目标已超出最大线缆长度，请缩减移动范围"}}, {"id": 2238, "constName": "WireTipStateNoSurface", "text": {"index": 479863806, "text": "请将视线移动到可以布线的区域"}}, {"id": 2239, "constName": "WireTipStateNoAvailableSlot", "text": {"index": 1877578966, "text": "该设备无可连接接口"}}, {"id": 2240, "constName": "UiTeamPublishRecruit", "text": {"index": 1907903423, "text": "发布招募"}}, {"id": 2241, "constName": "UiTeamModifyRecruit", "text": {"index": 2027506611, "text": "修改招募"}}, {"id": 2242, "constName": "UiTeamCreateTeamAndModifyRecruit", "text": {"index": 171675527, "text": "创建队伍并发布招募"}}, {"id": 2243, "constName": "RemainCount3", "text": {"index": 1696331880, "text": "领地剩余摆放数量：[color=#FD5252]{0}/[/color]{1}"}}, {"id": 2244, "constName": "RemainCount4", "text": {"index": 1952125537, "text": "领地剩余摆放数量：[color=#FFFFFF]{0}/[/color]{1}"}}, {"id": 2245, "constName": "UiTeamApplyListTitle", "text": {"index": 1429153023, "text": "大厅队伍申请列表"}}, {"id": 2246, "constName": "BuildEditNoTarget", "text": {"index": 2083069453, "text": "请将视线对准场景中需要编辑的对象"}}, {"id": 2247, "constName": "BuildWireNoElectricTarget1", "text": {"index": 947450951, "text": "请将视线对准场景中的电力设备"}}, {"id": 2248, "constName": "BuildWireNoElectricTarget2", "text": {"index": 1861829739, "text": "请将视线对准场景中需要连接的目标电力设备"}}, {"id": 2249, "constName": "BuildWireNoWaterTarget1", "text": {"index": 2036634984, "text": "请将视线对准场景中的水利设备"}}, {"id": 2250, "constName": "BuildWireNoWaterTarget2", "text": {"index": 1497604794, "text": "请将视线对准场景中需要连接的目标水利设备"}}, {"id": 2251, "constName": "IntelligentWireLockTarget", "text": {"index": 635625497, "text": "保持视线对准在该设备上，即将进入自动连线..."}}, {"id": 2252, "constName": "IntelligentWireCancelConnect", "text": {"index": 815711850, "text": "移开准心可取消自动连接"}}, {"id": 2253, "constName": "IntelligentWireReplace", "text": {"index": 100977018, "text": "目标接口已被占用，是否替换已有连接？"}}, {"id": 2254, "constName": "IntelligentWireConnecting", "text": {"index": 1125750964, "text": "自动连接中..."}}, {"id": 2255, "constName": "IntelligentWireWaitConfirm", "text": {"index": 1397707837, "text": "等待确认操作中..."}}, {"id": 2256, "constName": "IntelligentWireSwitchHandMode", "text": {"index": 270226448, "text": "自动接线模式下无法手动切换接口，是否切换为手动接线？"}}, {"id": 2257, "constName": "SwitchHandWireMode", "text": {"index": 1979897644, "text": "切换手动接线"}}, {"id": 2258, "constName": "NewbieLevelSkip", "text": {"index": 49571851, "text": "跳过"}}, {"id": 2259, "constName": "NewbieLevelSkipDesc", "text": {"index": 520245376, "text": "是否跳过新手关(仅开发模式)"}}, {"id": 2260, "constName": "SettingAdjustFPS", "text": {"index": 1240867524, "text": "为了更好游戏体验，我们已经将帧率降为合适的帧率。"}}, {"id": 2261, "constName": "SettingAdjustQuality", "text": {"index": 221530307, "text": "为了更好游戏体验，我们已经将画质降为合适的画质。"}}, {"id": 2262, "constName": "ShareInCD", "text": {"index": 1887311557, "text": "共享冷却中"}}, {"id": 2263, "constName": "InCD", "text": {"index": 1466738005, "text": "冷却中"}}, {"id": 2264, "constName": "DeadSheepAttackRemainTime", "text": {"index": 709446685, "text": "领地摧毁倒计时:{0}"}}, {"id": 2265, "constName": "DeadSheepCaptureRemainTime", "text": {"index": 1127025717, "text": "{0}后可占领"}}, {"id": 2266, "constName": "DeadSheepInnerSpoils", "text": {"index": 1311497393, "text": "掠夺核心层战利品箱{0}/{1}"}}, {"id": 2267, "constName": "DeadSheepOutterSpoils", "text": {"index": 1090161416, "text": "掠夺外围战利品箱{0}/{1}"}}, {"id": 2268, "constName": "DeadSheepTaskTitle", "text": {"index": 790141615, "text": "死羊任务目标"}}, {"id": 2269, "constName": "CanCaptured", "text": {"index": 1988482432, "text": "可占领"}}, {"id": 2270, "constName": "CannotCaptured", "text": {"index": 2075632567, "text": "不可占领"}}, {"id": 2271, "constName": "DestroyTerritoryConstruction", "text": {"index": 1330984225, "text": "摧毁领地柜"}}, {"id": 2272, "constName": "WireReplace", "text": {"index": 1679025530, "text": "目标接口已被占用，你可以替换连接，或者切换为其他接口"}}, {"id": 2273, "constName": "BtnWireReplaceDesc", "text": {"index": 1882072128, "text": "替换连接"}}, {"id": 2274, "constName": "Matching", "text": {"index": 1784238465, "text": "正在寻找战局\n请稍等"}}, {"id": 2275, "constName": "FriendRequest", "text": {"index": 778378329, "text": "好友请求"}}, {"id": 2276, "constName": "UnlockLevel", "text": {"index": 1589034729, "text": "{0}级解锁"}}, {"id": 2277, "constName": "BadgeTypeStr", "text": {"index": 1853951893, "text": "徽章"}}, {"id": 2278, "constName": "ExitTeamToHistoryBattle", "text": {"index": 688439721, "text": "进入历史战局您将退出当前大厅的组队，是否进入？"}}, {"id": 2279, "constName": "TargetIsTopGradeCantChange", "text": {"index": 2048288788, "text": "当前对象已升至最高等级且无法再进行编辑改造"}}, {"id": 2280, "constName": "InviteMatchTip", "text": {"index": 1041214018, "text": "接受组队您将直接跳转到大厅队伍界面，请确保战局安全再加入队伍"}}, {"id": 2281, "constName": "IntelligenceToolCarbinetSafety", "text": {"index": 1109467272, "text": "情报工具柜（保险柜）"}}, {"id": 2282, "constName": "IntelligenceToolSafety", "text": {"index": 1694655987, "text": "工具柜（保险柜）"}}, {"id": 2283, "constName": "LiftingPlatformSlot", "text": {"index": 2011991463, "text": "槽位"}}, {"id": 2284, "constName": "RaidRule", "text": {"index": 1346638161, "text": "每天{0}点允许领地突袭，其余时间领地内建筑无敌"}}, {"id": 2285, "constName": "CabinetClose", "text": {"index": 1096711782, "text": "(已关闭)"}}, {"id": 2286, "constName": "CraftSkin", "text": {"index": 638149425, "text": "皮肤"}}, {"id": 2287, "constName": "Received", "text": {"index": 1967339992, "text": "已领取"}}, {"id": 2288, "constName": "GoTo", "text": {"index": 1944217366, "text": "前往"}}, {"id": 2289, "constName": "NotStart", "text": {"index": 1457828192, "text": "未开始"}}, {"id": 2290, "constName": "Doing", "text": {"index": 1278080384, "text": "进行中"}}, {"id": 2291, "constName": "Receive", "text": {"index": 1850036728, "text": "领取"}}, {"id": 2292, "constName": "OpenByForever", "text": {"index": 2001389707, "text": "全天开放"}}, {"id": 2293, "constName": "OpenByStable", "text": {"index": 91470505, "text": "{0}后开放"}}, {"id": 2294, "constName": "CloseByStable", "text": {"index": 2073878905, "text": "{0}后关闭"}}, {"id": 2295, "constName": "MapEvent", "text": {"index": 1398133049, "text": "事件"}}, {"id": 2296, "constName": "Hour", "text": {"index": 2062092001, "text": "小时"}}, {"id": 2297, "constName": "Minute", "text": {"index": 1303299173, "text": "分钟"}}, {"id": 2298, "constName": "InfoDisk", "text": {"index": 2070161600, "text": "情报碟片"}}, {"id": 2299, "constName": "InfoDiskReceive", "text": {"index": 267788411, "text": "情报碟片接收中。。。"}}, {"id": 2300, "constName": "GlobalRemainCount1", "text": {"index": 60204881, "text": "全局剩余摆放数量[color=#FD5252]{0}/[/color]{1}"}}, {"id": 2301, "constName": "GlobalRemainCount2", "text": {"index": 40211967, "text": "全局剩余摆放数量[color=#FFFFFF]{0}/[/color]{1}"}}, {"id": 2302, "constName": "StoryStage", "text": {"index": 393888519, "text": "阶段"}}, {"id": 2303, "constName": "StoryInformation", "text": {"index": 755357411, "text": "情报"}}, {"id": 2304, "constName": "NearBy", "text": {"index": 1981213636, "text": "附近可拾取"}}, {"id": 2305, "constName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text": {"index": 217031076, "text": "埋枪中..."}}, {"id": 2306, "constName": "InstallTransmitter", "text": {"index": 75460684, "text": "安装发信器中..."}}, {"id": 2307, "constName": "Month", "text": {"index": 550418599, "text": "月"}}, {"id": 2308, "constName": "<PERSON>on", "text": {"index": 21075631, "text": "日"}}, {"id": 2309, "constName": "CabinetLevelUp", "text": {"index": 301484450, "text": "情报柜升级\n情报转化率+{0}%\n情报存储上限+{1}"}}, {"id": 2310, "constName": "CabinetLevelDesc", "text": {"index": 1487285909, "text": "此等级等于：小队中最高的情报等级；\n此等级影响：转化效率 和 存储上限；"}}, {"id": 2311, "constName": "CabinetEfficientDesc", "text": {"index": 972400269, "text": "转化效率将影响情报文件破译时长：\n###转化效率越高，每个情报文件破译所用时长越短；\n###标准情况下，初级6秒钟/中级9分钟/高级90分钟；\n###转化效率由情报柜等级决定，逐步提升；\n###情报碟片达到存储上限时，转化效率减半；\n[color=#FF6600] 50%[/color]：破译耗时为标准情况的一倍（初级=12秒）。\n[color=#969696]100%[/color]：标准情况\n[color=#848D5A]200%[/color]：破译耗时为标准情况的一半（初级=3秒）。"}}, {"id": 2312, "constName": "CabinetMemberInfo", "text": {"index": 2118770228, "text": "情报队员信息"}}, {"id": 2313, "constName": "CabinetLevel", "text": {"index": 474731161, "text": "情报柜等级{0}级"}}, {"id": 2314, "constName": "PackageNoItem", "text": {"index": 544939934, "text": "背包中没有这个物品"}}, {"id": 2315, "constName": "CabinetInPut", "text": {"index": 503234754, "text": "放入情报柜"}}, {"id": 2316, "constName": "CabinetEfficient", "text": {"index": 911192054, "text": "转化效率{0}%"}}, {"id": 2317, "constName": "NoInfoItem", "text": {"index": 1932437191, "text": "暂无情报物品"}}, {"id": 2318, "constName": "InfoCommitConfirm", "text": {"index": 2145523518, "text": "情报提交确认"}}, {"id": 2319, "constName": "ConversionTime", "text": {"index": 1569957894, "text": "转化时间{0}"}}, {"id": 2320, "constName": "CarckGet", "text": {"index": 810559554, "text": "破译获得"}}, {"id": 2321, "constName": "MaintenanceTimeDetail", "text": {"index": 1948159328, "text": "{numD=1}日{numH=2}时{numM=3}分"}}, {"id": 2322, "constName": "MaintenanceTime", "text": {"index": 2075437417, "text": "维护时间"}}, {"id": 2323, "constName": "Lock", "text": {"index": 444957765, "text": "锁定"}}, {"id": 2324, "constName": "AppointmentLock", "text": {"index": 850505482, "text": "预约锁定"}}, {"id": 2325, "constName": "PrepareLaunchPreview", "text": {"index": 526785871, "text": "开服预览"}}, {"id": 2326, "constName": "ReserveServer", "text": {"index": 216010195, "text": "预约服务器"}}, {"id": 2327, "constName": "ReserveServerTips", "text": {"index": 2066624569, "text": "您确认要预约【{0}】的服务器吗？距离服务器开始还有{1}"}}, {"id": 2328, "constName": "CancelMatching ", "text": {"index": 1693682324, "text": "取消匹配"}}, {"id": 2329, "constName": "SettingReportBuilding", "text": {"index": 1185772669, "text": "建筑举报"}}, {"id": 2330, "constName": "StartMatching", "text": {"index": 1058413305, "text": "{0}模式已开启!"}}, {"id": 2331, "constName": "StartMatchingTime", "text": {"index": 497009241, "text": "{0}可进入"}}, {"id": 2332, "constName": "InUpkeepWooden", "text": {"index": 2002529852, "text": "木质建筑维护中"}}, {"id": 2333, "constName": "InCorruptWooden", "text": {"index": 1246383960, "text": "木质建筑腐蚀中"}}, {"id": 2334, "constName": "InUpkeepStone", "text": {"index": 1508888908, "text": "石质建筑维护中"}}, {"id": 2335, "constName": "InCorruptStone", "text": {"index": 2129776776, "text": "石质建筑腐蚀中"}}, {"id": 2336, "constName": "InUpkeepMetal", "text": {"index": 933658225, "text": "铁质建筑维护中"}}, {"id": 2337, "constName": "InCorruptMetal", "text": {"index": 31068092, "text": "铁质建筑腐蚀中"}}, {"id": 2338, "constName": "InUpkeepSteel", "text": {"index": 1696025769, "text": "钢质建筑维护中"}}, {"id": 2339, "constName": "InCorruptSteel", "text": {"index": 923568327, "text": "钢质建筑腐蚀中"}}, {"id": 2340, "constName": "IsCancelAppointment", "text": {"index": 1874277164, "text": "是否退出预约？"}}, {"id": 2341, "constName": "AppointmentTimeoutTip", "text": {"index": 1026824167, "text": "预约活动已过期，点击确认以返回"}}, {"id": 2342, "constName": "PrepareForBattle", "text": {"index": 188627297, "text": "备战中"}}, {"id": 2343, "constName": "GameStart", "text": {"index": 2031978967, "text": "开始游戏"}}, {"id": 2344, "constName": "BagItemType", "text": {"index": 633016248, "text": "物资"}}, {"id": 2345, "constName": "BagSeedType", "text": {"index": 1273491910, "text": "种子"}}, {"id": 2346, "constName": "EventEnding", "text": {"index": 899265918, "text": "即将结束"}}, {"id": 2347, "constName": "HaveInfoItem", "text": {"index": 991377545, "text": "存在剩余情报"}}, {"id": 2348, "constName": "AppointmentFailedTip", "text": {"index": 32232589, "text": "有队友短时间内多次进行组队匹配，暂时无法进行组队活动，剩余时间【{0}】"}}, {"id": 2349, "constName": "EventComing", "text": {"index": 1970193381, "text": "即将开始"}}, {"id": 2350, "constName": "EventEndingCountDown", "text": {"index": 536436283, "text": "{0}后结束"}}, {"id": 2351, "constName": "EventComingCountDown", "text": {"index": 216766497, "text": "{0}后开始"}}, {"id": 2352, "constName": "ServerEndTime", "text": {"index": 621365895, "text": "战局结束时间"}}, {"id": 2353, "constName": "SettingMainObserver", "text": {"index": 1487808706, "text": "观察者指令"}}, {"id": 2354, "constName": "ObserverSetTransform", "text": {"index": 1785430322, "text": "设置坐标"}}, {"id": 2355, "constName": "ObserverTransport", "text": {"index": 840675171, "text": "传送到玩家旁"}}, {"id": 2356, "constName": "Grade1", "text": {"index": 1230431568, "text": "（茅草）"}}, {"id": 2357, "constName": "Grade2", "text": {"index": 1743520840, "text": "（木头）"}}, {"id": 2358, "constName": "Grade3", "text": {"index": 1476531871, "text": "（石头）"}}, {"id": 2359, "constName": "Grade4", "text": {"index": 835158791, "text": "（铁质）"}}, {"id": 2360, "constName": "Grade5", "text": {"index": 1468043682, "text": "（钢质）"}}, {"id": 2361, "constName": "TreasureTaskQuit", "text": {"index": 1585468442, "text": "确认"}}, {"id": 2362, "constName": "TreasureTaskBack", "text": {"index": 1024676784, "text": "我再想想"}}, {"id": 2363, "constName": "TerritoryToolcupBoard", "text": {"index": 1750533150, "text": "工具柜"}}, {"id": 2364, "constName": "TerritorySafeBox", "text": {"index": 471370770, "text": "保险柜"}}, {"id": 2365, "constName": "TerritoryBatchUpgrade", "text": {"index": 2065965206, "text": "批量升级"}}, {"id": 2366, "constName": "TerritoryBatchRecover", "text": {"index": 1564813984, "text": "批量修复"}}, {"id": 2367, "constName": "InvalidReceived", "text": {"index": 2057522260, "text": "无效数据，断开连接"}}, {"id": 2368, "constName": "InvalidOperation", "text": {"index": 1831432122, "text": "无法升级或改造为低于当前材质或与当前目标相同的对象"}}, {"id": 2369, "constName": "ClientNeedReboot", "text": {"index": 525747364, "text": "更新完成，请点击按钮重启程序。"}}, {"id": 2370, "constName": "TreasureMissionRefreshExplanation", "text": {"index": 1048158528, "text": "刷新次数，每天会补充至上限\n刷新任务时消耗1"}}, {"id": 2371, "constName": "TreasureMissionPropExplanation", "text": {"index": 1836483095, "text": "线索数量，每天会补充1个直到上限\n接取任务时消耗1"}}, {"id": 2372, "constName": "WirePointCount", "text": {"index": 1314077655, "text": "(打点总数:{0})"}}, {"id": 2373, "constName": "DestroySafeBox", "text": {"index": 1107992269, "text": "拆除保险柜"}}, {"id": 2374, "constName": "CantDestroySafeBox", "text": {"index": 444966847, "text": "无法拆除"}}, {"id": 2375, "constName": "CabinetLv", "text": {"index": 346722147, "text": "情报柜等级{0}级"}}, {"id": 2376, "constName": "NextWorldLevel", "text": {"index": 1201766800, "text": "{0}后提升至{1}级"}}, {"id": 2377, "constName": "InfoBadgeNum", "text": {"index": 956955018, "text": "情报徽章({0}/{1})"}}, {"id": 2378, "constName": "InfoCarck", "text": {"index": 1358011452, "text": "情报破译"}}, {"id": 2379, "constName": "DiskSaveLimit", "text": {"index": 1596800833, "text": "存储上限"}}, {"id": 2380, "constName": "MemberInfo", "text": {"index": 431660034, "text": "队友信息"}}, {"id": 2381, "constName": "ViewInfo", "text": {"index": 213848223, "text": "查看信息"}}, {"id": 2382, "constName": "Exchange", "text": {"index": 545220369, "text": "兑换"}}, {"id": 2383, "constName": "OnlyCaptainCanExchange", "text": {"index": 1059395073, "text": "仅限队长兑换"}}, {"id": 2384, "constName": "RiskRecord", "text": {"index": 1502683705, "text": "风险记录"}}, {"id": 2385, "constName": "EmulatorCheckTitle", "text": {"index": 1312682859, "text": "模拟器检测标题"}}, {"id": 2386, "constName": "EmulatorCheckMsg", "text": {"index": 815920107, "text": "模拟器检测提示内容"}}, {"id": 2387, "constName": "EmulatorCheckConfirm", "text": {"index": 1482982419, "text": "确定"}}, {"id": 2388, "constName": "UiTeamAppointment", "text": {"index": 891431699, "text": "[color=#FF7132]【预约】[/color]"}}, {"id": 2389, "constName": "NoConsume", "text": {"index": 820560593, "text": "不消耗任何材料"}}, {"id": 2390, "constName": "QuitReservationMode", "text": {"index": 1037321756, "text": "退出预约"}}, {"id": 2391, "constName": "UiTeamAppointmentTime", "text": {"index": 377506044, "text": "预约时间："}}, {"id": 2392, "constName": "ExitGame", "text": {"index": 1211129722, "text": "退出游戏"}}, {"id": 2393, "constName": "UpdateGame", "text": {"index": 548424652, "text": "更新"}}, {"id": 2394, "constName": "ToolcupBoradDecaying", "text": {"index": 2097763721, "text": "建筑腐蚀中"}}, {"id": 2395, "constName": "ToolcupBoradUpkeep", "text": {"index": 1170692297, "text": "维护时间"}}, {"id": 2396, "constName": "QuitTeam", "text": {"index": 623958904, "text": "退出队伍"}}, {"id": 2397, "constName": "KickOut", "text": {"index": 1992720133, "text": "踢出队伍"}}, {"id": 2398, "constName": "InvitePermission", "text": {"index": 120046438, "text": "邀请权限"}}, {"id": 2399, "constName": "ToolcupBoardHourPut", "text": {"index": 883453148, "text": "放入{0}小时"}}, {"id": 2400, "constName": "BatchUpgradeAllDesc", "text": {"index": 947766003, "text": "将基地内的所有建筑升级到选中材质"}}, {"id": 2401, "constName": "BatchUpgradeMaxDesc", "text": {"index": 1998468839, "text": "根据背包和领地柜内的材料智能计算可升级的建筑"}}, {"id": 2402, "constName": "BatchUpgradeCustomDesc", "text": {"index": 500533208, "text": "可以自定义选择你想要升级的建筑体"}}, {"id": 2403, "constName": "BatchRecoverAllDesc", "text": {"index": 263001166, "text": "修复领地下所有受损的子建筑、结构造物和摆件残骸"}}, {"id": 2404, "constName": "BatchRecoverBaseDesc", "text": {"index": 834563812, "text": "可部分选择子建筑或者摆件残骸进行单独修复"}}, {"id": 2405, "constName": "CreateRoleHintNameInput", "text": {"index": 733339518, "text": "[color=#ba5427]请输入名字，限制4-16字符[/color]"}}, {"id": 2406, "constName": "SexMan", "text": {"index": 1975295857, "text": "男"}}, {"id": 2407, "constName": "SexWoman", "text": {"index": 1801990054, "text": "女"}}, {"id": 2408, "constName": "ClientNotAvailable", "text": {"index": 319869114, "text": "此版本已不可用"}}, {"id": 2409, "constName": "Blueprint", "text": {"index": 440333856, "text": "研究台"}}, {"id": 2410, "constName": "RepairBench", "text": {"index": 1263459701, "text": "修理台"}}, {"id": 2411, "constName": "HaveItemAmount", "text": {"index": 1652370180, "text": "已拥有:{0}"}}, {"id": 2412, "constName": "GlobalRepairMonster", "text": {"index": 519417413, "text": "怪物"}}, {"id": 2413, "constName": "GlobalRepairVehicle", "text": {"index": 494107265, "text": "载具"}}, {"id": 2414, "constName": "GlobalRepairPlayer", "text": {"index": 1469370107, "text": "敌对玩家"}}, {"id": 2415, "constName": "NoRiskRecord", "text": {"index": 1000668244, "text": "队伍暂无贡献记录"}}, {"id": 2416, "constName": "ItemSplitDefaultTxt", "text": {"index": 138206539, "text": "{name=多管火箭弹} [color=#FFFFFF]{changeSign=-}{changeNum= 123}[/color]"}}, {"id": 2417, "constName": "CreateRoleCancel", "text": {"index": 1282446323, "text": "我再想想"}}, {"id": 2418, "constName": "EnterMonumentTips", "text": {"index": 89106284, "text": "已进入据点中，开始追踪据点任务"}}, {"id": 2419, "constName": "NewTaskTips", "text": {"index": 684956533, "text": "新任务"}}, {"id": 2420, "constName": "AddictionTips1", "text": {"index": 83020688, "text": "呵护双眼 请您休息一下"}}, {"id": 2421, "constName": "AddictionTips2", "text": {"index": 277407656, "text": "依据国家新闻出版署《关于防止未成年人沉迷网络游戏工作的通知》与《关于进一步严格管理切实防止未成年人沉迷网络游戏的通知》的相关规定，未成年人用户除周五、周六、周日及法定节假日的20时至21时外其他时间均不可登录游戏。"}}, {"id": 2422, "constName": "AddictionTips3", "text": {"index": 1970812242, "text": "依据国家新闻出版署《关于防止未成年人沉迷网络游戏工作的通知》与《关于进一步严格管理 切实防止未成年人沉迷网络游戏的通知》的相关规定，今日在线时间已超过时限，当前无法进入游戏。请合理安排时间，享受健康生活。"}}, {"id": 2423, "constName": "AddictionTips4", "text": {"index": 1079135280, "text": "依据国家新闻出版署《关于防止未成年人沉迷网络游戏工作的通知》与《关于进一步严格管理切实防止未成年人沉迷网络游戏的通知》的相关规定，今日在线时间已超过时限，当前无法进入游戏。请合理安排时间，享受健康生活。"}}, {"id": 2424, "constName": "AddictionTips5", "text": {"index": 1034787956, "text": "您今日已累计在线{0}小时，请休息15分钟，合理安排时间，享受健康生活。"}}, {"id": 2425, "constName": "AddictionTips6", "text": {"index": 2082741413, "text": "您已被纳入防沉迷系统，依据相关规定，该账号不能付费充值。"}}, {"id": 2426, "constName": "AddictionTips7", "text": {"index": 445660720, "text": "您已被纳入防沉迷系统，依据相关规定，该账号单次充值金额不得超过50元人民币，每月充值金额累计不得超过200元人民币。"}}, {"id": 2427, "constName": "AddictionTips8", "text": {"index": 1855433496, "text": "您已被纳入防沉迷系统，依据相关规定，该账号单次充值金额不得超过100元人民币，每月充值金额累计不得超过400元人民币。"}}, {"id": 2428, "constName": "ReservationModeTimeOut", "text": {"index": 1914795237, "text": "预约时间已失效，已无法通过预约队伍进入战局。点击【确定】会自动退出当前队伍。"}}, {"id": 2429, "constName": "ChangeContainerName", "text": {"index": 1560652390, "text": "更改容器名字"}}, {"id": 2430, "constName": "DailyTaskResetTime", "text": {"index": 1921368940, "text": "每日任务重置:{0}时{1}分{2}秒"}}, {"id": 2431, "constName": "CabinetMaxLevel", "text": {"index": 918188708, "text": "已满级"}}, {"id": 2432, "constName": "CreateRoleDialogue01", "text": {"index": 1668685342, "text": "[color=#53e1ff]德拉科[/color]：您的[color=#ff9f4d]新档案[/color]已创建完毕，请使用此身份立即执行潜入任务。"}}, {"id": 2433, "constName": "BeeBuzzTaskNotOpen", "text": {"index": 460572401, "text": "事件未开放"}}, {"id": 2434, "constName": "DanceClapping", "text": {"index": 137773401, "text": "鼓掌"}}, {"id": 2435, "constName": "DanceLike", "text": {"index": 688882097, "text": "点赞"}}, {"id": 2436, "constName": "DanceShrugginghands", "text": {"index": 1300493745, "text": "摊手"}}, {"id": 2437, "constName": "BagEquipType", "text": {"index": 1006514910, "text": "装备"}}, {"id": 2438, "constName": "BagSurvivalItemType", "text": {"index": 156853285, "text": "生存道具"}}, {"id": 2439, "constName": "BagShortcutBarType", "text": {"index": 1770200669, "text": "快捷栏"}}, {"id": 2440, "constName": "BagExtendInventoryType", "text": {"index": 613216820, "text": "扩容背包"}}, {"id": 2441, "constName": "BagRangedWeaponType", "text": {"index": 676710890, "text": "远程武器"}}, {"id": 2442, "constName": "MainMapRaidDefenseTips", "text": {"index": 563949861, "text": "全天保护：全天领地内建筑无敌"}}, {"id": 2443, "constName": "MainMapRaidAttackTips", "text": {"index": 1811590361, "text": "全天抄家：全天允许领地突袭"}}, {"id": 2444, "constName": "MainMapRaidDayTips", "text": {"index": 113389518, "text": "全天"}}, {"id": 2445, "constName": "ReputationLevel", "text": {"index": 1199864338, "text": "情报等级{0}级"}}, {"id": 2446, "constName": "NewbieLevelSkipVideoDesc", "text": {"index": 923377884, "text": "您将跳过精彩剧情，是否继续？"}}, {"id": 2447, "constName": "CommonTrue", "text": {"index": 475165862, "text": "是"}}, {"id": 2448, "constName": "CommonFalse", "text": {"index": 1032295179, "text": "否"}}, {"id": 2449, "constName": "BeeBuzzHasExchanged", "text": {"index": 727136894, "text": "已兑换"}}, {"id": 2450, "constName": "OpenProxima", "text": {"index": 1165020508, "text": "打开Proxima"}}, {"id": 2451, "constName": "ElectricInfoHMS", "text": {"index": 1327076346, "text": "{0:N0}时 {1:N0}分 {2:N0}秒"}}, {"id": 2452, "constName": "ElectricInfoMS", "text": {"index": 927717070, "text": "{0:N0}分 {1:N0}秒"}}, {"id": 2453, "constName": "ElectricInfoS", "text": {"index": 1765264923, "text": "{0:N0}秒"}}, {"id": 2454, "constName": "ElectricInforWm", "text": {"index": 1229391626, "text": "{0}/{1} 瓦分钟"}}, {"id": 2455, "constName": "GoToView", "text": {"index": 1989936803, "text": "前往查看"}}, {"id": 2456, "constName": "DolphinUpdateFailed", "text": {"index": 436681846, "text": "更新失败({0})"}}, {"id": 2457, "constName": "VendingMachine", "text": {"index": 1599508233, "text": "自动售货机"}}, {"id": 2458, "constName": "LobbyStashTabProp", "text": {"index": 301351189, "text": "道具"}}, {"id": 2459, "constName": "LobbyStashTabNewly", "text": {"index": 1996642545, "text": "最近获得"}}, {"id": 2460, "constName": "LobbyStashTabLimitProp", "text": {"index": 1629381589, "text": "限时道具"}}, {"id": 2461, "constName": "LobbyStashTabGift", "text": {"index": 264583629, "text": "礼包"}}, {"id": 2462, "constName": "LobbyStashTabSkin", "text": {"index": 1624901576, "text": "皮肤"}}, {"id": 2463, "constName": "LobbyStashTabBattle", "text": {"index": 1467784067, "text": "局内"}}, {"id": 2464, "constName": "LobbyStashUsePropTip1", "text": {"index": 1940754736, "text": "使用后获得下列资源"}}, {"id": 2465, "constName": "LobbyStashUsePropTip2", "text": {"index": 1526885736, "text": "使用后从下列资源中随机获得"}}, {"id": 2466, "constName": "ItemDetail", "text": {"index": 2080106500, "text": "道具详情"}}, {"id": 2467, "constName": "GameMode", "text": {"index": 503976671, "text": "游戏模式"}}, {"id": 2468, "constName": "TechTree", "text": {"index": 1614140463, "text": "科技树"}}, {"id": 2469, "constName": "LobbyStashRandomTip", "text": {"index": 1201211380, "text": "？？"}}, {"id": 2470, "constName": "<PERSON><PERSON><PERSON><PERSON>", "text": {"index": 1043149693, "text": "状态"}}, {"id": 2471, "constName": "MailTabTitle1", "text": {"index": 1217761369, "text": "大厅"}}, {"id": 2472, "constName": "MailTabTitle2", "text": {"index": 198501868, "text": "战局"}}, {"id": 2473, "constName": "MailNums", "text": {"index": 1856859533, "text": "邮件数：{0}/{1}"}}, {"id": 2474, "constName": "StoreManager", "text": {"index": 607272922, "text": "商店管理"}}, {"id": 2475, "constName": "GiveBed", "text": {"index": 56612774, "text": "睡袋赠予"}}, {"id": 2476, "constName": "ReputationBadge", "text": {"index": 804861029, "text": "情报徽章"}}, {"id": 2477, "constName": "LiftingPlatform", "text": {"index": 88438917, "text": "载具改装"}}, {"id": 2478, "constName": "NameCard", "text": {"index": 507397995, "text": "个人信息"}}, {"id": 2479, "constName": "Building", "text": {"index": 1628515266, "text": "建筑&摆件"}}, {"id": 2480, "constName": "Market", "text": {"index": 796883416, "text": "商城"}}, {"id": 2481, "constName": "ReputationRecord", "text": {"index": 734861860, "text": "提交奖励"}}, {"id": 2482, "constName": "<PERSON><PERSON><PERSON>", "text": {"index": 1309575681, "text": "多管火箭系统"}}, {"id": 2483, "constName": "MailBox", "text": {"index": 1252655140, "text": "邮箱"}}, {"id": 2484, "constName": "HistoryBattle", "text": {"index": 300174856, "text": "历史战局"}}, {"id": 2485, "constName": "SwipeCardGame", "text": {"index": 27096280, "text": "电路盒"}}, {"id": 2486, "constName": "SurprisePlay", "text": {"index": 934050352, "text": "惊喜玩法"}}, {"id": 2487, "constName": "LobbyStashFold", "text": {"index": 1994859703, "text": "收起"}}, {"id": 2488, "constName": "LobbyStashMore", "text": {"index": 37639085, "text": "更多"}}, {"id": 2489, "constName": "ManualDischarge", "text": {"index": 1637562037, "text": "手动卸货"}}, {"id": 2490, "constName": "IgnoreAllUILockSwitch", "text": {"index": 1447700511, "text": "忽略所有UI锁定"}}, {"id": 2491, "constName": "ManualDischargeDes", "text": {"index": 130455610, "text": "车厢货物折损50%"}}, {"id": 2492, "constName": "PermissionCardDes", "text": {"index": 1282420323, "text": "完整卸货"}}, {"id": 2493, "constName": "HighPermissionCardDes", "text": {"index": 1328542246, "text": "可额外获得车厢内物品"}}, {"id": 2494, "constName": "LockMissileDistance", "text": {"index": 541388909, "text": "目标距离：{0}m"}}, {"id": 2495, "constName": "SocTextUpdateTest", "text": {"index": 4538537, "text": "多语言更新测试202503201758"}}, {"id": 2496, "constName": "GameInFix", "text": {"index": 1032052298, "text": "修复中......"}}, {"id": 2497, "constName": "LobbyStashTimeOut", "text": {"index": 461446969, "text": "已过期"}}, {"id": 2498, "constName": "InsufficientCoin", "text": {"index": 1790647984, "text": "{0}不足"}}, {"id": 2499, "constName": "CurrencyExchange", "text": {"index": 1633793743, "text": "使用{0}兑换{1}"}}, {"id": 2500, "constName": "CostumeName", "text": {"index": 7623294, "text": "时装"}}, {"id": 2501, "constName": "StartScreenCap", "text": {"index": 1224276681, "text": "开启录屏模式"}}, {"id": 2502, "constName": "CompleteCurrency", "text": {"index": 1535096137, "text": "补齐{0}"}}, {"id": 2503, "constName": "GoToRecharge", "text": {"index": 722814870, "text": "前往充值"}}, {"id": 2504, "constName": "UiCommonRewardTips1", "text": {"index": 1755697089, "text": "以下奖励自动发放至账户"}}, {"id": 2505, "constName": "GoToOfficialWeb", "text": {"index": 1995607006, "text": "前往官网"}}, {"id": 2506, "constName": "TechTreeUnLockTitle", "text": {"index": 572344038, "text": "自行解锁科技"}}, {"id": 2507, "constName": "TechTreeUnLockTip", "text": {"index": 492587931, "text": "队友：{0}"}}, {"id": 2508, "constName": "SignalAtHeightsChanneling", "text": {"index": 74692718, "text": "信号传输中..."}}, {"id": 2509, "constName": "SalvageChanneling", "text": {"index": 1551276850, "text": "打捞中..."}}, {"id": 2510, "constName": "ExploreTheCaveChanneling", "text": {"index": 970662898, "text": "挖掘中..."}}, {"id": 2511, "constName": "Plant", "text": {"index": 1145667942, "text": "种植"}}, {"id": 2512, "constName": "Hybrid", "text": {"index": 1573506269, "text": "杂交"}}, {"id": 2513, "constName": "LobbyStashBatchUsage", "text": {"index": 2090563667, "text": "是否批量使用"}}, {"id": 2514, "constName": "AddAmmo", "text": {"index": 723068048, "text": "添加弹药"}}, {"id": 2515, "constName": "AddFuel", "text": {"index": 618749260, "text": "添加燃料"}}, {"id": 2516, "constName": "TrainCargoIndex", "text": {"index": 2038226307, "text": "第{0}节车厢"}}, {"id": 2517, "constName": "Locked", "text": {"index": 1674026128, "text": "(已上锁)"}}, {"id": 2518, "constName": "LobbyLevelLimit", "text": {"index": 1651520108, "text": "等级达到{0}级开启"}}, {"id": 2519, "constName": "TribeTitle", "text": {"index": 160395354, "text": "社群"}}, {"id": 2520, "constName": "TribeMine", "text": {"index": 1160221320, "text": "我的社群"}}, {"id": 2521, "constName": "TribeSearch", "text": {"index": 716292987, "text": "寻找社群"}}, {"id": 2522, "constName": "CreateTribe", "text": {"index": 1214288173, "text": "创建社群"}}, {"id": 2523, "constName": "TribeTagNum", "text": {"index": 1959715730, "text": "社群标签({0}/{1})"}}, {"id": 2524, "constName": "TribeInviteMembersNum", "text": {"index": 1381598204, "text": "邀请成员({0}/{1})"}}, {"id": 2525, "constName": "RecycleTime", "text": {"index": 961411631, "text": "分解时间:"}}, {"id": 2526, "constName": "ProbabilityGet", "text": {"index": 1039937454, "text": "概率获得"}}, {"id": 2527, "constName": "AvailableRefreshCount", "text": {"index": 569068617, "text": "剩余刷新次数：{0}"}}, {"id": 2528, "constName": "TimeAfterNextClue", "text": {"index": 981620363, "text": "{0:D2}:{1:D2}:{2:D2}后恢复{3}条线索"}}, {"id": 2529, "constName": "TribeSimple", "text": {"index": 1355479667, "text": "普通社群"}}, {"id": 2530, "constName": "TribeSuper", "text": {"index": 392611014, "text": "超级社群"}}, {"id": 2531, "constName": "TribeNameLimit", "text": {"index": 320626577, "text": "社群名称({0}-{1}个字符)"}}, {"id": 2532, "constName": "TribeNameInputTips", "text": {"index": 1706422875, "text": "请输入社群名称"}}, {"id": 2533, "constName": "SaveToPlan", "text": {"index": 921621626, "text": "保存穿搭"}}, {"id": 2534, "constName": "NameComposter", "text": {"index": 635800825, "text": "堆肥机"}}, {"id": 2535, "constName": "AddFuelDesc", "text": {"index": 788495072, "text": "添加燃料x{0}"}}, {"id": 2536, "constName": "AddAmmoDesc", "text": {"index": 1651271112, "text": "添加弹药x{0}"}}, {"id": 2537, "constName": "ChangeNameCost", "text": {"index": 1398639396, "text": "本次改名需要消耗{0}张<img src='{1}' width='39' height='39'/>[color=#ff7132]{2}[/color]"}}, {"id": 2538, "constName": "ChangeNameCDDesc", "text": {"index": 948458282, "text": "无法修改，下次修改日期{0}"}}, {"id": 2539, "constName": "PersonalHomepageMain", "text": {"index": 2055371776, "text": "主页"}}, {"id": 2540, "constName": "PersonalHomepageHistoryRecord", "text": {"index": 1015167320, "text": "历史战绩"}}, {"id": 2541, "constName": "KatyushaUnloadMissiles", "text": {"index": 723913978, "text": "卸载导弹"}}, {"id": 2542, "constName": "FuelDesc", "text": {"index": 1901227101, "text": "燃料"}}, {"id": 2543, "constName": "AmmoDesc", "text": {"index": 1779669363, "text": "弹药"}}, {"id": 2544, "constName": "TribeChooseAvatarTitle", "text": {"index": 1107369645, "text": "更换社群头像"}}, {"id": 2545, "constName": "TribeChooseTagTitle", "text": {"index": 561591711, "text": "社群标签"}}, {"id": 2546, "constName": "TribeCustomTagTitle", "text": {"index": 1313236589, "text": "自定义标签"}}, {"id": 2547, "constName": "Save", "text": {"index": 592905495, "text": "保存"}}, {"id": 2548, "constName": "TribeCustomTagLengthLimit", "text": {"index": 976914749, "text": "请输入标签名（长度2-8）"}}, {"id": 2549, "constName": "TribeChooseTagNum", "text": {"index": 1015982891, "text": "选择你喜欢的标签(已选{0}/{1})"}}, {"id": 2550, "constName": "TribeInviteMemberTips", "text": {"index": 1458285136, "text": "社群创建成功后自动向所选目标人员发出邀请，对方同意后即可加入；对方是否同意不影响社群创建。"}}, {"id": 2551, "constName": "LobbyTeamTitle", "text": {"index": 2068880492, "text": "我的队伍"}}, {"id": 2552, "constName": "LobbyTeamAppointTitle", "text": {"index": 920516218, "text": "我的预约"}}, {"id": 2553, "constName": "TribeMainHelpTips", "text": {"index": 384643486, "text": "[size=32]社群系统[/size] \n[size=10] \n [/size]\n    玩家可以创建属于自己的社群并邀请朋友们加入，也可以加入其他人或是系统创建的社群。每个群有自己专属的频道，您可以更方便地与朋友们聊天；还能通过队伍分享等功能更便捷地招募队友。\n[size=10] \n [/size]\n    以下是各类群的介绍：\n1.系统群：账号等级升到[color=#FF7132]2级[/color]时自动加入。\n2.自建群：账号等级[color=#FF7132]5级[/color]以上，消耗一定的资源就可以创建属于自己的社群。\n3.超级群：在[color=#FF7132]运营活动中可获取创建资格[/color]，超级群能容纳更多的成员，未来还会开放灯牌等更丰富功能。"}}, {"id": 2554, "constName": "LobbyTeamAppointmentTip", "text": {"index": 1089319306, "text": "{0}预约开启"}}, {"id": 2555, "constName": "MonumentAlreadyHaveMonsterTips", "text": {"index": 562800108, "text": "当前据点活动的小队过多，尝试清除后再进行召唤"}}, {"id": 2556, "constName": "<PERSON><PERSON><PERSON><PERSON>", "text": {"index": 294816787, "text": "头像"}}, {"id": 2557, "constName": "PlayerAvatar<PERSON>rame", "text": {"index": 99646786, "text": "头像框"}}, {"id": 2558, "constName": "PlayerNameCard", "text": {"index": 258231616, "text": "名片"}}, {"id": 2559, "constName": "PlayerChatBubble", "text": {"index": 1993109387, "text": "聊天气泡"}}, {"id": 2560, "constName": "LobbyTeamAppointmentTip1", "text": {"index": 792635363, "text": "选择合适的时间段进行游戏，大厅队伍仅可有一个，同时即将开服的时段可以选择一个进行预约"}}, {"id": 2561, "constName": "ResearchTime", "text": {"index": 1070142752, "text": "研究时间:"}}, {"id": 2562, "constName": "MallCountDownFinished", "text": {"index": 320478003, "text": "活动已结束"}}, {"id": 2563, "constName": "ContactWater", "text": {"index": 796852966, "text": "接水中..."}}, {"id": 2564, "constName": "IntegrationPage", "text": {"index": 933121810, "text": "悬赏"}}, {"id": 2565, "constName": "RepairOutPut", "text": {"index": 430985242, "text": "取出"}}, {"id": 2566, "constName": "RepairTip1", "text": {"index": 1276055137, "text": "无法修理"}}, {"id": 2567, "constName": "RepairTip2", "text": {"index": 195560174, "text": "材料不足"}}, {"id": 2568, "constName": "TribeKickoutMate", "text": {"index": 1002298327, "text": "踢出社群"}}, {"id": 2569, "constName": "TribeKickoutConfirmTip", "text": {"index": 1130831626, "text": "确认将{0}移出社群"}}, {"id": 2570, "constName": "TribeAllAgree", "text": {"index": 1541141221, "text": "全部同意"}}, {"id": 2571, "constName": "TribeAllRefuse", "text": {"index": 832009572, "text": "全部拒绝"}}, {"id": 2572, "constName": "TribeInviteTitle", "text": {"index": 1286967741, "text": "待处理邀请"}}, {"id": 2573, "constName": "TribeAuditTitle", "text": {"index": 1615395389, "text": "{0}的加入申请"}}, {"id": 2574, "constName": "ContainerInfoValue1", "text": {"index": 1917789183, "text": "{0}毫升/{1}毫升"}}, {"id": 2575, "constName": "ContainerInfoValue2", "text": {"index": 491064991, "text": "{0}毫升/分"}}, {"id": 2576, "constName": "ContainerInfoValue3", "text": {"index": 1570727338, "text": "{0}秒/次"}}, {"id": 2577, "constName": "TransmitterTip", "text": {"index": 69171204, "text": "当射频发射器与射频接收器频率一致时，可触发接收器的功能"}}, {"id": 2578, "constName": "CurFrequency", "text": {"index": 694276466, "text": "当前频率:"}}, {"id": 2579, "constName": "PutInDieselOil", "text": {"index": 616855291, "text": "放入柴油"}}, {"id": 2580, "constName": "NoHaveAmmo", "text": {"index": 1249650361, "text": "未添加弹药"}}, {"id": 2581, "constName": "NoEquipWeapon", "text": {"index": 1836294308, "text": "未装配武器"}}, {"id": 2582, "constName": "NoEquipWeaponTips", "text": {"index": 2056624043, "text": "请先装配武器"}}, {"id": 2583, "constName": "PhotoTemplate", "text": {"index": 674905715, "text": "模板"}}, {"id": 2584, "constName": "PhotoSetting", "text": {"index": 1349926654, "text": "设置"}}, {"id": 2585, "constName": "PhotoGesture", "text": {"index": 665721136, "text": "动作"}}, {"id": 2586, "constName": "PhotoFilter", "text": {"index": 134963968, "text": "滤镜"}}, {"id": 2587, "constName": "PhotoRoute", "text": {"index": 1004182833, "text": "路径"}}, {"id": 2588, "constName": "RandomRefresh", "text": {"index": 41265402, "text": "概率刷出"}}, {"id": 2589, "constName": "DisCharge", "text": {"index": 576211618, "text": "卸货"}}, {"id": 2590, "constName": "MallBundleSoldOut", "text": {"index": 1313708045, "text": "已拥有"}}, {"id": 2591, "constName": "MallBundleNotStart", "text": {"index": 30476117, "text": "即将上架"}}, {"id": 2592, "constName": "EquipWeapon", "text": {"index": 605468728, "text": "装配武器"}}, {"id": 2593, "constName": "ChangeWeapon", "text": {"index": 454622215, "text": "替换武器"}}, {"id": 2594, "constName": "GameStyleMain", "text": {"index": 15146907, "text": "主要"}}, {"id": 2595, "constName": "GameStyleSecondary", "text": {"index": 1488206023, "text": "次要"}}, {"id": 2596, "constName": "PsersonalTag", "text": {"index": 1201329707, "text": "个性标签"}}, {"id": 2597, "constName": "GameStyle", "text": {"index": 398634252, "text": "游戏风格"}}, {"id": 2598, "constName": "ReportVoice", "text": {"index": 1139100741, "text": "举报语音"}}, {"id": 2599, "constName": "ReportBuildings", "text": {"index": 28860967, "text": "举报建筑"}}, {"id": 2600, "constName": "RefreshClueTime", "text": {"index": 1589505180, "text": "每日{0:D2}:{1:D2}新增{2}条线索"}}, {"id": 2601, "constName": "MallPayConfirmTitle", "text": {"index": 402512614, "text": "购买确认"}}, {"id": 2602, "constName": "NameHitchPost", "text": {"index": 2069170999, "text": "拴马桩"}}, {"id": 2603, "constName": "DailyMissionTab", "text": {"index": 560735906, "text": "日常"}}, {"id": 2604, "constName": "OutpostRebornPoint", "text": {"index": 1118821696, "text": "前哨站重生"}}, {"id": 2605, "constName": "OutpostCdHint", "text": {"index": 806932048, "text": "使用冷却{0}秒"}}, {"id": 2606, "constName": "TimerModeDesc", "text": {"index": 550086814, "text": "倒计时模式"}}, {"id": 2607, "constName": "RFModeDesc", "text": {"index": 768159674, "text": "遥控模式"}}, {"id": 2608, "constName": "MallBuyTitleBundle", "text": {"index": 970951479, "text": "捆绑包"}}, {"id": 2609, "constName": "MallBuyTitleMallItem", "text": {"index": 318261240, "text": "商品"}}, {"id": 2610, "constName": "TeamApplyBtnText", "text": {"index": 1355046957, "text": "发送"}}, {"id": 2611, "constName": "TeamApplyInputText", "text": {"index": 796438936, "text": "我想和你一起组队，请看我的个人信息"}}, {"id": 2612, "constName": "AmmoNoMatchMsg", "text": {"index": 465462028, "text": "当前弹药与该武器不匹配，自动炮塔将无法正常运行。是否更换弹药？"}}, {"id": 2613, "constName": "AmmoNoMatchTitle", "text": {"index": 403587428, "text": "弹药不匹配"}}, {"id": 2614, "constName": "UiTeamTabAppointment", "text": {"index": 1925966274, "text": "预约招募"}}, {"id": 2615, "constName": "CreateLobbyTeam", "text": {"index": 1195741648, "text": "创建大厅队伍"}}, {"id": 2616, "constName": "CreateAppointmentTeam", "text": {"index": 1248236792, "text": "创建预约队伍"}}, {"id": 2617, "constName": "NoLobbyTeam", "text": {"index": 1935271077, "text": "暂无大厅队伍"}}, {"id": 2618, "constName": "NoAppointmentTeam", "text": {"index": 693117220, "text": "暂无预约队伍"}}, {"id": 2619, "constName": "UnPublishLobbyTeam", "text": {"index": 900921301, "text": "暂未发布到组队大厅"}}, {"id": 2620, "constName": "UnPublishAppointmentTeam", "text": {"index": 35342175, "text": "暂未发布到预约大厅"}}, {"id": 2621, "constName": "MallBuyItemCountTips", "text": {"index": 226266750, "text": "包含{0}件商品"}}, {"id": 2622, "constName": "MallBuyTitleFixPack", "text": {"index": 1063119257, "text": "礼包"}}, {"id": 2623, "constName": "MallBuyMallItemSoldOut", "text": {"index": 1996304954, "text": "已达购买上限"}}, {"id": 2624, "constName": "UiTeamCreateAppointmentTeamAndModifyRecruit", "text": {"index": 1059671219, "text": "创建预约队伍并发布招募"}}, {"id": 2625, "constName": "UiTeamNoLimit", "text": {"index": 2020842210, "text": "不限"}}, {"id": 2626, "constName": "MoreOperation", "text": {"index": 993738722, "text": "更多操作"}}, {"id": 2627, "constName": "ExpandList", "text": {"index": 965350534, "text": "收起"}}, {"id": 2628, "constName": "PublishLobbyTeam", "text": {"index": 1216516124, "text": "发布大厅队伍"}}, {"id": 2629, "constName": "PublishAppointmentTeam", "text": {"index": 847879524, "text": "发布预约队伍"}}, {"id": 2630, "constName": "RecruitPlayerTagTitle1", "text": {"index": 1839279506, "text": "队友倾向"}}, {"id": 2631, "constName": "RecruitPlayerTagTitle2", "text": {"index": 2077440495, "text": "擅长职业"}}, {"id": 2632, "constName": "UiAppointmentTeamApplyListTitle", "text": {"index": 1801532299, "text": "预约申请列表"}}, {"id": 2633, "constName": "PhotoShareTitle", "text": {"index": 156931495, "text": "分享"}}, {"id": 2634, "constName": "PersonalTagType1", "text": {"index": 1965479469, "text": "风格"}}, {"id": 2635, "constName": "PersonalTagType2", "text": {"index": 1799074243, "text": "个性"}}, {"id": 2636, "constName": "PersonalTagType3", "text": {"index": 1386141581, "text": "称号"}}, {"id": 2637, "constName": "PersonalTagType4", "text": {"index": 1065955776, "text": "分类4"}}, {"id": 2638, "constName": "PersonalTagSelectNum", "text": {"index": 841195862, "text": "选择你喜欢的标签（已选[color=#ff7132]{0}[/color]/{1}）"}}, {"id": 2639, "constName": "UiTeamTabGameHall", "text": {"index": 1892634481, "text": "战局招募"}}, {"id": 2640, "constName": "UiTeamChatSource", "text": {"index": 1210839798, "text": "群聊"}}, {"id": 2641, "constName": "UiRecruitSource", "text": {"index": 1484085263, "text": "来源：{0}"}}, {"id": 2642, "constName": "C4TimerModeTips", "text": {"index": 415052393, "text": "切换为计时引爆模式"}}, {"id": 2643, "constName": "C4RemoteControlModeTips", "text": {"index": 403153207, "text": "切换为遥控引爆模式"}}, {"id": 2644, "constName": "DigName", "text": {"index": 932557932, "text": "挖掘机"}}, {"id": 2645, "constName": "OtherTribe", "text": {"index": 171094437, "text": "其它社群"}}, {"id": 2646, "constName": "MallBuySellEnd", "text": {"index": 44129210, "text": "已下架"}}, {"id": 2647, "constName": "SettingKeyMouse", "text": {"index": 1489398939, "text": "键鼠设置"}}, {"id": 2648, "constName": "RePublishRecruit", "text": {"index": 312865597, "text": "由于更换队长，是否重新发布当前队伍到{0}？"}}, {"id": 2649, "constName": "UiTeamModifyAppointmentRecruit", "text": {"index": 139877994, "text": "修改预约招募"}}, {"id": 2650, "constName": "UiTeamPublishAppointmentRecruit", "text": {"index": 605663734, "text": "发布预约招募"}}, {"id": 2651, "constName": "QuitTeamTitle", "text": {"index": 760719422, "text": "退出大厅队伍"}}, {"id": 2652, "constName": "QuitAppointmentTeamTitle", "text": {"index": 240925959, "text": "退出预约队伍"}}, {"id": 2653, "constName": "EnterAppointmentTeamTitle", "text": {"index": 1096565736, "text": "进入预约战局"}}, {"id": 2654, "constName": "EnterAppointmentTeamContent", "text": {"index": 529848950, "text": "是否直接进入该战局，或您可以通过历史战局进入{0}游戏"}}, {"id": 2655, "constName": "PlayerChangeShowAvatar", "text": {"index": 1870074824, "text": "修改形象"}}, {"id": 2656, "constName": "CreateRole", "text": {"index": 186315467, "text": "创建角色"}}, {"id": 2657, "constName": "CollectProgress", "text": {"index": 1983293043, "text": "收集进度{0}/{1}"}}, {"id": 2658, "constName": "DefaultPortrait", "text": {"index": 397969853, "text": "默认头像"}}, {"id": 2659, "constName": "Seconds", "text": {"index": 1207506339, "text": "{0}秒"}}, {"id": 2660, "constName": "AppointmentTeamOpenWarnTip", "text": {"index": 1205104711, "text": "你的预约情报模式还有{0}分钟开启，请尽快返回队伍！"}}, {"id": 2661, "constName": "HaveSignal", "text": {"index": 195529361, "text": "有"}}, {"id": 2662, "constName": "NoSignal", "text": {"index": 247570728, "text": "无"}}, {"id": 2663, "constName": "Zero", "text": {"index": 275261001, "text": "0"}}, {"id": 2664, "constName": "TempCofferSafeBox", "text": {"index": 1709281669, "text": "归还中枢"}}, {"id": 2665, "constName": "SafetyBoxWeapon", "text": {"index": 978947962, "text": "武器装备"}}, {"id": 2666, "constName": "SafetyBoxBelt", "text": {"index": 548569110, "text": "防御装备"}}, {"id": 2667, "constName": "SafetyBoxUniverse", "text": {"index": 1640054236, "text": "通用"}}, {"id": 2668, "constName": "MaxCanPutInDesc", "text": {"index": 1553570077, "text": "最多可放入：{0}"}}, {"id": 2669, "constName": "MaxCanPutOutDesc", "text": {"index": 1313288827, "text": "最多可取出：{0}"}}, {"id": 2670, "constName": "AutoConnectWireTips", "text": {"index": 947027667, "text": "自动连线说明"}}, {"id": 2671, "constName": "QuitTribeConfirmTips", "text": {"index": 1459602480, "text": "确定要退出社群吗？\n[color=#FF7132]退出后在群内的活跃度等数据将被清除，无法恢复[/color]"}}, {"id": 2672, "constName": "ReplaceTribeConfirmTips", "text": {"index": 496912680, "text": "确认后会为您重新分配一个系统社群。\n[color=#FF7132]每24小时只能更换1次，操作完成后将无法撤销[/color]"}}, {"id": 2673, "constName": "CreateSuperTribeConfirmTips", "text": {"index": 379800981, "text": "是否确认消耗材料创建超级群？"}}, {"id": 2674, "constName": "SelectSameFreTitle", "text": {"index": 282062202, "text": "选择同频道具"}}, {"id": 2675, "constName": "DismissInputTips", "text": {"index": 1458456088, "text": "输入文字"}}, {"id": 2676, "constName": "DismissConfirm", "text": {"index": 75150225, "text": "确认解散"}}, {"id": 2677, "constName": "DismissConfirm2", "text": {"index": 443953106, "text": "确认解散{0}吗？"}}, {"id": 2678, "constName": "LobbyTeamAppointmentBookingTitle", "text": {"index": 538458846, "text": "时段选择"}}, {"id": 2679, "constName": "ArmorTitle", "text": {"index": 507943805, "text": "护甲"}}, {"id": 2680, "constName": "WeaponTitle", "text": {"index": 1320674065, "text": "武器"}}, {"id": 2681, "constName": "CharLimit", "text": {"index": 1490471119, "text": "{0}/{1}字符"}}, {"id": 2682, "constName": "Dig<PERSON>o<PERSON>uel", "text": {"index": 1126808832, "text": "暂无{0}可转换"}}, {"id": 2683, "constName": "PreViewTitle", "text": {"index": 414368101, "text": "预览"}}, {"id": 2684, "constName": "PleaseSelectWeapon", "text": {"index": 1541758496, "text": "请选择装配武器"}}, {"id": 2685, "constName": "BuryWeaponTip", "text": {"index": 325284872, "text": "请放入埋入的武器"}}, {"id": 2686, "constName": "NoWeaponTip", "text": {"index": 1568518134, "text": "没有可装配的武器"}}, {"id": 2687, "constName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "text": {"index": 1390777132, "text": "机型检测"}}, {"id": 2688, "constName": "UnSupportDevice", "text": {"index": 716654226, "text": "很抱歉，您的设备目前还未能覆盖，敬请期待。设备型号：{0}  GPU型号：{1}"}}, {"id": 2689, "constName": "UiTeamApplyDescTitle", "text": {"index": 1954484768, "text": "申请备注"}}, {"id": 2690, "constName": "GiftPackage", "text": {"index": 1774863577, "text": "礼包"}}, {"id": 2691, "constName": "SelectReward", "text": {"index": 2067606848, "text": "请选择奖励"}}, {"id": 2692, "constName": "PlantMainTitle", "text": {"index": 1277043813, "text": "种植管理"}}, {"id": 2693, "constName": "UiChangeNameTimeTip", "text": {"index": 1277222576, "text": "每{0}天可修改一次"}}, {"id": 2694, "constName": "TerritoryHaveItem", "text": {"index": 1510892377, "text": "有道具待领取"}}, {"id": 2695, "constName": "FactionLevelDetail", "text": {"index": 1655054482, "text": "等级达到{0}级开放"}}, {"id": 2696, "constName": "PublishGameRecruitTitle", "text": {"index": 1109047920, "text": "招募局内玩家"}}, {"id": 2697, "constName": "Detail", "text": {"index": 215866344, "text": "详情"}}, {"id": 2698, "constName": "SecondsToUse", "text": {"index": 416470919, "text": "[color=#FF6600]{0}[/color]秒后可用"}}, {"id": 2699, "constName": "UseSleepBag2RespawnHint", "text": {"index": 407113852, "text": "床位使用后，附近的床位会一同进入冷却时间"}}, {"id": 2700, "constName": "UseNearBy2RespawnHint", "text": {"index": 961858645, "text": "使用后会在淘汰点附近重生"}}, {"id": 2701, "constName": "UseRandom2RespawnHint", "text": {"index": 2142751312, "text": "使用后会在随机位置重生，离淘汰点较远"}}, {"id": 2702, "constName": "UseOutpost2RespawnHint", "text": {"index": 1854281680, "text": "使用后会在前哨站安全区内重生"}}, {"id": 2703, "constName": "NearbyRespawnCdHint", "text": {"index": 119728374, "text": "由于交战状态被淘汰，暂时无法使用附近复活"}}, {"id": 2704, "constName": "StorySettlementCD", "text": {"index": 918501241, "text": "战局结算倒计时:{0}"}}, {"id": 2705, "constName": "ApplyEnterGameTeamResultTitle", "text": {"index": 304264550, "text": "成功加入队伍"}}, {"id": 2706, "constName": "ApplyEnterGameTeamResultTips", "text": {"index": 1770631566, "text": "您的局内招募申请已通过可以通过历史战局加入游戏，现在是否直接跳转进入战局？"}}, {"id": 2707, "constName": "StoryActionPlanCD", "text": {"index": 1263251134, "text": "行动倒计时:{0}天{1}时{2}分{3}秒"}}, {"id": 2708, "constName": "ChatRecruitType0", "text": {"index": 1888008134, "text": "大厅队伍"}}, {"id": 2709, "constName": "ChatRecruitType1", "text": {"index": 1792634116, "text": "预约队伍"}}, {"id": 2710, "constName": "ChatRecruitType2", "text": {"index": 517503490, "text": "局内队伍"}}, {"id": 2711, "constName": "MainQuestMissionTab", "text": {"index": 2123651704, "text": "任务"}}, {"id": 2712, "constName": "TeamSelectorAutoSelect", "text": {"index": 1656112280, "text": "{0}秒后自动选择"}}, {"id": 2713, "constName": "SelectPlayModeTipNoOpenTip", "text": {"index": 1803853087, "text": "尚未开放"}}, {"id": 2714, "constName": "ServerVersionInfoMatch", "text": {"index": 586545021, "text": "匹配"}}, {"id": 2715, "constName": "ServerVersionInfoPartMatch", "text": {"index": 676012624, "text": "部分匹配"}}, {"id": 2716, "constName": "ServerVersionInfoNotLogin", "text": {"index": 458475934, "text": "不可登录"}}, {"id": 2717, "constName": "ServerVersionInfoDesc", "text": {"index": 190794943, "text": "当前服务器信息：NetworkVersion:{0}  RpcVersion:{1}  RpcCustomTypeVersion:{2}  EntityVersionVerifyCode:{3}  ResourceVersion:{4}  CompatibilityLoginLevel:{5}  DisableClientResourceVersionCheck:{6}"}}, {"id": 2718, "constName": "ServerCheckVersionInfoDesc", "text": {"index": 362272700, "text": "校验服务器信息：NetworkVersion:{0}  RpcVersion:{1}  RpcCustomTypeVersion:{2}  EntityVersionVerifyCode:{3}  ResourceVersion:{4}"}}, {"id": 2719, "constName": "ServerVersionInfoUnKnown", "text": {"index": 1129826049, "text": "未知"}}, {"id": 2720, "constName": "ChiefIntelligenceOfficer", "text": {"index": 506486157, "text": "首席情报官"}}, {"id": 2721, "constName": "ElitePenetrator", "text": {"index": 1402712085, "text": "精英渗透者"}}, {"id": 2722, "constName": "OutstandingExecutor", "text": {"index": 1896471121, "text": "优异执行人"}}, {"id": 2723, "constName": "ActiveObserver", "text": {"index": 1387656167, "text": "活跃观察员"}}, {"id": 2724, "constName": "NoLimitTime", "text": {"index": 170152456, "text": "不限时"}}, {"id": 2725, "constName": "TeamRecruitPublishCondition1", "text": {"index": 2039092254, "text": "发布"}}, {"id": 2726, "constName": "TeamRecruitPublishCondition2", "text": {"index": 1670950823, "text": "暂不发布"}}, {"id": 2727, "constName": "TeamRecruitMicCondition1", "text": {"index": 574591675, "text": "建议开启"}}, {"id": 2728, "constName": "TeamRecruitMicCondition2", "text": {"index": 1866583110, "text": "可关闭"}}, {"id": 2729, "constName": "TeamRecruitApplyCondition1", "text": {"index": 503676020, "text": "需要申请"}}, {"id": 2730, "constName": "TeamRecruitApplyCondition2", "text": {"index": 1839572038, "text": "直接加入"}}, {"id": 2731, "constName": "TeamCancelMatching", "text": {"index": 1324720870, "text": "确定要停止匹配吗？"}}, {"id": 2732, "constName": "TalentSkill", "text": {"index": 1156040162, "text": "技巧"}}, {"id": 2733, "constName": "Looting", "text": {"index": 1978532998, "text": "掠夺"}}, {"id": 2734, "constName": "UiGameTeamApplyListTitle", "text": {"index": 577033525, "text": "申请列表"}}, {"id": 2735, "constName": "LobbyTeamPlayTip1", "text": {"index": 869337291, "text": "等待开服"}}, {"id": 2736, "constName": "LobbyTeamPlayTip2", "text": {"index": 1282735792, "text": "进入游戏"}}, {"id": 2737, "constName": "LobbyTeamPlayTip3", "text": {"index": 1058818068, "text": "等待队长开始"}}, {"id": 2738, "constName": "LobbyTeamPlayTip4", "text": {"index": 1538116387, "text": "准备"}}, {"id": 2739, "constName": "LobbyTeamPlayTip5", "text": {"index": 1412270022, "text": "取消准备"}}, {"id": 2740, "constName": "LobbyTeamPlayTip6", "text": {"index": 2103441280, "text": "停止匹配"}}, {"id": 2741, "constName": "TeamCancelReady", "text": {"index": 1153679411, "text": "是否取消准备？"}}, {"id": 2742, "constName": "OwnNum", "text": {"index": 81519610, "text": "拥有数量:{0}"}}, {"id": 2743, "constName": "SplitAbandon", "text": {"index": 1790030292, "text": "拆分丢弃"}}, {"id": 2744, "constName": "ReturnLogin", "text": {"index": 106942848, "text": "返回登录"}}, {"id": 2745, "constName": "OthersideTabTitleNearBy", "text": {"index": 1300426769, "text": "附近"}}, {"id": 2746, "constName": "BigMapCarShops", "text": {"index": 870607224, "text": "巡逻车队"}}, {"id": 2747, "constName": "EditLimitTimeDesc", "text": {"index": 1864223021, "text": "后不可编辑"}}, {"id": 2748, "constName": "TalentMainHelpTips", "text": {"index": 947238367, "text": "[size=32]天赋系统帮助文本占位[/size]"}}, {"id": 2749, "constName": "TalentMainHelpTipsTitle", "text": {"index": 756870865, "text": "天赋系统大标题占位"}}, {"id": 2750, "constName": "TalentMainHelpTipsSubTitle", "text": {"index": 570928441, "text": "天赋系统副标题占位"}}, {"id": 2751, "constName": "WeedOut", "text": {"index": 936460605, "text": "淘汰了"}}, {"id": 2752, "constName": "KnockDown", "text": {"index": 1381337419, "text": "击倒了"}}, {"id": 2753, "constName": "BattleReportLog2", "text": {"index": 191006375, "text": "{0}导致{1}{2}"}}, {"id": 2754, "constName": "BattleReportLog1", "text": {"index": 1273767235, "text": "{0}使用{1}{2}{3}"}}, {"id": 2755, "constName": "BattleReportLog4", "text": {"index": 922264456, "text": "{0}使用{1}摧毁了{2}"}}, {"id": 2756, "constName": "BattleData", "text": {"index": 1634763766, "text": "战斗数据"}}, {"id": 2757, "constName": "BuildDestory", "text": {"index": 2106506967, "text": "建筑摧毁"}}, {"id": 2758, "constName": "ListOfAllMaterialLosses", "text": {"index": 513605962, "text": "物资损失一览"}}, {"id": 2759, "constName": "ListOfAllDestroyedBuildings", "text": {"index": 735055562, "text": "建筑摧毁一览"}}, {"id": 2760, "constName": "ListOfExplosive", "text": {"index": 39762174, "text": "爆炸物使用清单"}}, {"id": 2761, "constName": "ListOfServicingMaterial", "text": {"index": 382539330, "text": "维修材料清单"}}, {"id": 2762, "constName": "ListOfDestroyedBuildings", "text": {"index": 369144755, "text": "建筑摧毁清单"}}, {"id": 2763, "constName": "ListOfDestroyedDecoration", "text": {"index": 1432262475, "text": "摆件摧毁清单"}}, {"id": 2764, "constName": "BattleReportAttack", "text": {"index": 1097965570, "text": "突袭"}}, {"id": 2765, "constName": "BattleReportDefend", "text": {"index": 1136240105, "text": "防守"}}, {"id": 2766, "constName": "BattleReportSuccess", "text": {"index": 1129625766, "text": "成功"}}, {"id": 2767, "constName": "BattleReportFail", "text": {"index": 801184184, "text": "失败"}}, {"id": 2768, "constName": "TotalDuration", "text": {"index": 1479665003, "text": "总耗时"}}, {"id": 2769, "constName": "KillTimes", "text": {"index": 1828182465, "text": "淘汰敌人"}}, {"id": 2770, "constName": "DeathTimes", "text": {"index": 975391994, "text": "被淘汰"}}, {"id": 2771, "constName": "OutputDamage", "text": {"index": 1409796838, "text": "输出伤害"}}, {"id": 2772, "constName": "DestroyedBuilding", "text": {"index": 1345697387, "text": "摧毁建筑"}}, {"id": 2773, "constName": "ExplosiveCost", "text": {"index": 555129092, "text": "爆炸物材料消耗"}}, {"id": 2774, "constName": "DefendResult", "text": {"index": 1884698550, "text": "防守{0}"}}, {"id": 2775, "constName": "AttackResult", "text": {"index": 1974822177, "text": "占领{0}"}}, {"id": 2776, "constName": "AttackTime", "text": {"index": 1406191089, "text": "突袭时间"}}, {"id": 2777, "constName": "AttackTerritory", "text": {"index": 524327053, "text": "突袭领地"}}, {"id": 2778, "constName": "DefendTime", "text": {"index": 91742573, "text": "遇袭时间"}}, {"id": 2779, "constName": "DefendTerritory", "text": {"index": 1676891800, "text": "遇袭领地"}}, {"id": 2780, "constName": "AttackRecord", "text": {"index": 1294382707, "text": "突袭记录"}}, {"id": 2781, "constName": "DefendRecord", "text": {"index": 2114362186, "text": "遇袭记录"}}, {"id": 2782, "constName": "FactionMissionCompletedTips", "text": {"index": 759196237, "text": "已完成{0}势力任务"}}, {"id": 2783, "constName": "DailyMissionCompletedTips", "text": {"index": 20323461, "text": "已完成每日简报"}}, {"id": 2784, "constName": "TalentRewardTask", "text": {"index": 2088693199, "text": "激活[{0}]的[{1}]个天赋({2}/{3})"}}, {"id": 2785, "constName": "AttackBattleReport", "text": {"index": 770400111, "text": "突袭战报"}}, {"id": 2786, "constName": "DefendBattleReport", "text": {"index": 913823771, "text": "遇袭战报"}}, {"id": 2787, "constName": "FlowDetail", "text": {"index": 36666904, "text": "流水详情"}}, {"id": 2788, "constName": "BuySomeThing", "text": {"index": 164001549, "text": "购买{0}级{1}"}}, {"id": 2789, "constName": "BuyTalentLevel", "text": {"index": 1456017890, "text": "是否需要花费<img src='{0}' width='44' height='44'/>[color=#ff7132]{1}[/color]学习"}}, {"id": 2790, "constName": "ForceSwitch", "text": {"index": 1596732030, "text": "切换势力"}}, {"id": 2791, "constName": "BattleReportLog5", "text": {"index": 1380384216, "text": "{0}通过支撑破碎毁坏了{1}"}}, {"id": 2792, "constName": "RequireForceLevel", "text": {"index": 1359782888, "text": "势力等级{0}级"}}, {"id": 2793, "constName": "AdminBasic", "text": {"index": 390074566, "text": "基础"}}, {"id": 2794, "constName": "AdminItem", "text": {"index": 503662132, "text": "道具"}}, {"id": 2795, "constName": "AdminCreate", "text": {"index": 1772700205, "text": "创建"}}, {"id": 2796, "constName": "Learn", "text": {"index": 296449525, "text": "学习"}}, {"id": 2797, "constName": "MonumentMissionCompletedTips", "text": {"index": 871900150, "text": "已完成遗迹任务"}}, {"id": 2798, "constName": "DeadSheepMissionCompletedTips", "text": {"index": 2127401040, "text": "已完成死羊任务"}}, {"id": 2799, "constName": "TreasureMissionCompletedTips", "text": {"index": 1321155751, "text": "已完成夺宝任务"}}, {"id": 2800, "constName": "FactionMissionRoundTips", "text": {"index": 1249812091, "text": "剩余完成次数:{0}"}}, {"id": 2801, "constName": "ArchiveRespawn", "text": {"index": 1324013470, "text": "存档点重生"}}, {"id": 2802, "constName": "UseArchive2RespawnHint", "text": {"index": 1472727710, "text": "使用后会在存档点附近重生"}}, {"id": 2803, "constName": "ArchiveRespawnCdHint", "text": {"index": 610471473, "text": "存档点cd中"}}, {"id": 2804, "constName": "NewbieBlockPopTip1", "text": {"index": 888448897, "text": "预计部署完成时间"}}, {"id": 2805, "constName": "NewbieBlockPopTip2", "text": {"index": 656266438, "text": "前方依旧拥堵，准备再次尝试"}}, {"id": 2806, "constName": "NewbieBlockPopTip3", "text": {"index": 528110428, "text": "部署完成,准备登机"}}, {"id": 2807, "constName": "EquipSkin", "text": {"index": 195188093, "text": "应用外观"}}, {"id": 2808, "constName": "EquipSkined", "text": {"index": 1198178341, "text": "已应用外观"}}, {"id": 2809, "constName": "ListOfAllMaterialHarvest", "text": {"index": 664910730, "text": " 物资收获一览"}}, {"id": 2810, "constName": "DetailedDataTip", "text": {"index": 2083422600, "text": "1份初级情报文件计为1<br/>1份中级情报文件计为100<br/>1份高级情报文件计为1000"}}, {"id": 2811, "constName": "StorageDebrisDescTip", "text": {"index": 1959438494, "text": "容器被摧毁时，会根据剧本规则保护物资，需要在残骸消失时间内取回"}}, {"id": 2812, "constName": "StorageDebrisDestroyTime", "text": {"index": 267207296, "text": "消失时间"}}, {"id": 2813, "constName": "StorageDebrisDestroyTime2", "text": {"index": 190272349, "text": "[color=#ff7132]{0}[/color]销毁"}}, {"id": 2814, "constName": "GuideNewbieLevelJoystick", "text": {"index": 245879915, "text": "拖动摇杆"}}, {"id": 2815, "constName": "GuideNewbieLevelJump", "text": {"index": 1154264327, "text": "点击跳跃"}}, {"id": 2816, "constName": "PenetratingShowContext", "text": {"index": 1228780300, "text": "武器穿透等级大于目标护甲等级为增伤；小于目标等级为减伤。数值差异越大伤害变化越剧烈"}}, {"id": 2817, "constName": "LobbyLevelLimitUnlock", "text": {"index": 433967093, "text": "局外等级[color=#cb4821]{0}[/color]解锁"}}, {"id": 2818, "constName": "TerritoryBlueprint", "text": {"index": 713033480, "text": "领地蓝图"}}, {"id": 2819, "constName": "CurrentTerritory", "text": {"index": 1676706013, "text": "当前领地"}}, {"id": 2820, "constName": "DayTimeFormat", "text": {"index": 164241961, "text": "{0}天{1}时"}}, {"id": 2821, "constName": "BattlePassUnlockPremium", "text": {"index": 1139155337, "text": "解锁进阶通行证"}}, {"id": 2822, "constName": "BattlePassUnlockCollector", "text": {"index": 1661693582, "text": "升级典藏通行证"}}, {"id": 2823, "constName": "BattlePassPremium", "text": {"index": 747446482, "text": "高级"}}, {"id": 2824, "constName": "BattlePassFree", "text": {"index": 1584323502, "text": "免费"}}, {"id": 2825, "constName": "MinutesTimeFormat", "text": {"index": 1925510462, "text": "{0}分钟"}}, {"id": 2826, "constName": "UnitWaterFormat", "text": {"index": 771899703, "text": "{0}/{1}毫升"}}, {"id": 2827, "constName": "BattlePassTimeRemain", "text": {"index": 1240003764, "text": "活动时间剩余{0}"}}, {"id": 2828, "constName": "BattlePassExpAddDesc", "text": {"index": 591657557, "text": "{0}%经验加成"}}, {"id": 2829, "constName": "BattlePassExpNoAddDesc", "text": {"index": 986611645, "text": "无额外经验加成"}}, {"id": 2830, "constName": "BattlePassUpgradeToPremium", "text": {"index": 974877419, "text": "解锁进阶通行证"}}, {"id": 2831, "constName": "BattlePassUpgradeToCollector", "text": {"index": 598615719, "text": "升级典藏通行证"}}, {"id": 2832, "constName": "BattlePassDescTitle", "text": {"index": 256780917, "text": "通行证说明"}}, {"id": 2833, "constName": "CurrentTerritoryNoSave", "text": {"index": 1075310940, "text": "当前领地未保存"}}, {"id": 2834, "constName": "CustomBlueprintSaving", "text": {"index": 576085653, "text": "蓝图保存中..."}}, {"id": 2835, "constName": "ModelLoading", "text": {"index": 1152711508, "text": "模型加载中..."}}, {"id": 2836, "constName": "InsufficientLevel", "text": {"index": 666622335, "text": "等级不足"}}, {"id": 2837, "constName": "WorldInfoLevelBuffDesc", "text": {"index": 67576080, "text": "世界情报等级修正:x{0}%"}}, {"id": 2838, "constName": "BatchRecoverTechLocked", "text": {"index": 1640013344, "text": "科技未解锁"}}, {"id": 2839, "constName": "BatchRecoverDebrisConflict", "text": {"index": 1802621820, "text": "存在无法修复"}}, {"id": 2840, "constName": "LobbyRecruitApply", "text": {"index": 2011662484, "text": "大厅招募申请"}}, {"id": 2841, "constName": "CaptainApply", "text": {"index": 1732482996, "text": "队长申请"}}, {"id": 2842, "constName": "PleaseBlueprintName", "text": {"index": 1815863674, "text": "请输入蓝图名称"}}, {"id": 2843, "constName": "InputTextLimitTip", "text": {"index": 222318716, "text": "限{0}字符({1}/{2})"}}, {"id": 2844, "constName": "TeamBattleHallTopTitle", "text": {"index": 445308302, "text": "组队大厅"}}, {"id": 2845, "constName": "GoToSaveIngame", "text": {"index": 1329358347, "text": "前往战局保存"}}, {"id": 2846, "constName": "ReportCountDesc1", "text": {"index": 955737234, "text": "{0}/{1}"}}, {"id": 2847, "constName": "ReportCountDesc2", "text": {"index": 446118897, "text": "[color=#D6622F]{0}[/color]/{1}"}}, {"id": 2848, "constName": "PublishRecruitToBattleGame", "text": {"index": 1955359089, "text": "发布招募到当前战局"}}, {"id": 2849, "constName": "NoPublishRecruitToBattleGame", "text": {"index": 597683215, "text": "暂未发布当前战局招募"}}, {"id": 2850, "constName": "PublishGameToLobbyRecruitTips", "text": {"index": 2024749293, "text": "该招募仅大厅玩家可查看，且无法撤销"}}, {"id": 2851, "constName": "PublishGameRecruitTips", "text": {"index": 151106161, "text": "该招募仅当前战局玩家可查看（发布{0}小时后失效）"}}, {"id": 2852, "constName": "CallBackToBattle", "text": {"index": 788971553, "text": "呼叫上线"}}, {"id": 2853, "constName": "PublishGameToLobbyRecruitTitle", "text": {"index": 1556411987, "text": "招募大厅玩家"}}, {"id": 2854, "constName": "DismountFailHint", "text": {"index": 775808527, "text": "当前已无合法下车点此时下车会立即重生，是否选择重生？"}}, {"id": 2855, "constName": "Possession", "text": {"index": 1770923902, "text": "附身"}}, {"id": 2856, "constName": "InGameRecruitApply", "text": {"index": 1800097383, "text": "战局招募申请"}}, {"id": 2857, "constName": "AppointmentRecruitApply", "text": {"index": 1174563844, "text": "预约招募申请"}}, {"id": 2858, "constName": "BattleTeamGListNone", "text": {"index": 500052515, "text": "暂无队友"}}, {"id": 2859, "constName": "ObserverModePlayer", "text": {"index": 1441494887, "text": "玩家列表"}}, {"id": 2860, "constName": "ObserverModeTerritoryCenter", "text": {"index": 1495182450, "text": "领地列表"}}, {"id": 2861, "constName": "SwitchToAppointmentTeam", "text": {"index": 1657624972, "text": "切换到预约"}}, {"id": 2862, "constName": "SwitchToNormalTeam", "text": {"index": 1807210369, "text": "切换到组队"}}, {"id": 2863, "constName": "SpraySet", "text": {"index": 1205414628, "text": "喷漆设置"}}, {"id": 2864, "constName": "S<PERSON>y<PERSON>iew", "text": {"index": 1458891401, "text": "喷漆展示"}}, {"id": 2865, "constName": "GestureSet", "text": {"index": 543436282, "text": "手势设置"}}, {"id": 2866, "constName": "GestureView", "text": {"index": 494917499, "text": "手势展示"}}, {"id": 2867, "constName": "EnterMsgTip", "text": {"index": 621531814, "text": "确认进入"}}, {"id": 2868, "constName": "RecruitPlatformPc", "text": {"index": 546220876, "text": "混服"}}, {"id": 2869, "constName": "RecruitPlatformMobile", "text": {"index": 924758186, "text": "手游"}}, {"id": 2870, "constName": "CloseMsgTip3", "text": {"index": 979609096, "text": "关闭({0})"}}, {"id": 2871, "constName": "LeaveTeamTip1", "text": {"index": 1440170862, "text": "退队后将会失去队友给予的领地权限，是否确认退出"}}, {"id": 2872, "constName": "LeaveTeamTip2", "text": {"index": 1488788109, "text": "退出队伍后，当前队友将失去您给予的领地相关权限，是否确认操作"}}, {"id": 2873, "constName": "KickOutTeamTip1", "text": {"index": 1195298601, "text": "踢出该玩家将会失去该玩家领地内的相关权限，是否确认操作"}}, {"id": 2874, "constName": "KickOutTeamTip2", "text": {"index": 1274543118, "text": "该玩家领地内有队伍声望柜，踢出该玩家将会失去该玩家领地内的相关权限，是否确认操作"}}, {"id": 2875, "constName": "RemoveMember", "text": {"index": 1720838317, "text": "删除成员"}}, {"id": 2876, "constName": "UpgradeAdmin", "text": {"index": 1115559878, "text": "升为管理员"}}, {"id": 2877, "constName": "DowngradeAdmin", "text": {"index": 211699167, "text": "降为成员"}}, {"id": 2878, "constName": "PermissionDesc", "text": {"index": 651282504, "text": "宣誓友好\n勾选此项以添加豁免炮塔类摆件攻击的权限。\n\n领地操作权\n勾选此项以添加操作门、容器、工具柜、炮塔以及其他摆件的权限。\n\n领地建造权\n勾选此项以添加摆放建筑的权限。\n\n领地编辑权\n勾选此项以添加编辑造物的权限，包括升级、维修、回收、删除、改造与旋转。"}}, {"id": 2879, "constName": "Building<PERSON><PERSON><PERSON>", "text": {"index": 1984838835, "text": "建筑管理"}}, {"id": 2880, "constName": "BattlePassUnLocked", "text": {"index": 1198989864, "text": "已解锁"}}, {"id": 2881, "constName": "BattlePassPremiumName", "text": {"index": 627354199, "text": "进阶版通行证"}}, {"id": 2882, "constName": "BattlePassCollectorName", "text": {"index": 1998363108, "text": "典藏版通行证"}}, {"id": 2883, "constName": "BattlePassUnLock", "text": {"index": 1916089481, "text": "解锁{0}"}}, {"id": 2884, "constName": "Teaching", "text": {"index": 1334448942, "text": "教学"}}, {"id": 2885, "constName": "TakeOffAll", "text": {"index": 1820559622, "text": "全部取出"}}, {"id": 2886, "constName": "TalentUnlockTip", "text": {"index": 2042015167, "text": "{0}技巧[color=#cb4821]{1}[/color]解锁"}}, {"id": 2887, "constName": "ChangeNickname", "text": {"index": 1106979664, "text": "更改名称"}}, {"id": 2888, "constName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "text": {"index": 425622319, "text": "常用"}}, {"id": 2889, "constName": "RouletteGesture", "text": {"index": 1925369164, "text": "手势"}}, {"id": 2890, "constName": "Rou<PERSON><PERSON><PERSON><PERSON>", "text": {"index": 751914756, "text": "喷漆"}}, {"id": 2891, "constName": "ToGameRecruitHall", "text": {"index": 1521632649, "text": "查看招募"}}, {"id": 2892, "constName": "LobbyRecruitDesc", "text": {"index": 1406623284, "text": "您可以向其他在大厅的玩家发布大厅招募，同时其他玩家也可以通过该招募加入您的大厅队伍"}}, {"id": 2893, "constName": "LobbyAppointmentRecruitDesc", "text": {"index": 744389977, "text": "您可以向其他在大厅的玩家发布预约招募，同时其他玩家也可以通过该招募加入您的预约队伍"}}, {"id": 2894, "constName": "GameRecruitDesc", "text": {"index": 1409607541, "text": "您可以向其他在当前战局的玩家发布招募，同时其他玩家也可以通过该招募加入您在当前战局的队伍"}}, {"id": 2895, "constName": "GameToLobbyRecruitDesc", "text": {"index": 97637513, "text": "您可以向其他在大厅的玩家发布招募，当前在大厅的玩家可以通过该招募加入您在当前战局的队伍"}}, {"id": 2896, "constName": "TalentTags1", "text": {"index": 626421888, "text": "生存"}}, {"id": 2897, "constName": "TalentTags2", "text": {"index": 66734121, "text": "探索"}}, {"id": 2898, "constName": "TalentTags3", "text": {"index": 1850682043, "text": "战斗"}}, {"id": 2899, "constName": "TalentTags4", "text": {"index": 1912457721, "text": "抄家"}}, {"id": 2900, "constName": "TalentTags5", "text": {"index": 1986237675, "text": "采集"}}, {"id": 2901, "constName": "TalentTags6", "text": {"index": 1125652354, "text": "经营"}}, {"id": 2902, "constName": "TalentTitles1", "text": {"index": 17559773, "text": "渗透者新人"}}, {"id": 2903, "constName": "TalentTitles2", "text": {"index": 1286501532, "text": "渗透者老兵"}}, {"id": 2904, "constName": "TalentTitles3", "text": {"index": 515332061, "text": "渗透者教官"}}, {"id": 2905, "constName": "TalentTitles4", "text": {"index": 59221948, "text": "科伯特新人"}}, {"id": 2906, "constName": "TalentTitles5", "text": {"index": 60307128, "text": "科伯特老兵"}}, {"id": 2907, "constName": "TalentTitles6", "text": {"index": 1021260144, "text": "科伯特教官"}}, {"id": 2908, "constName": "TalentTitles7", "text": {"index": 1290783573, "text": "前哨站新人"}}, {"id": 2909, "constName": "TalentTitles8", "text": {"index": 713739404, "text": "前哨站老兵"}}, {"id": 2910, "constName": "TalentTitles9", "text": {"index": 1818868026, "text": "科伯特教官"}}, {"id": 2911, "constName": "BattleTeamImpeachTitle", "text": {"index": 256604828, "text": "由于队长{0}小时未上线，队员可申请为队长"}}, {"id": 2912, "constName": "BattleTeamVoteTitle", "text": {"index": 1421278654, "text": "玩家{0}申请成为队长"}}, {"id": 2913, "constName": "UiTeamModifyGameRecruit", "text": {"index": 855005443, "text": "修改局内招募"}}, {"id": 2914, "constName": "ShareSkinCountDesc", "text": {"index": 176717171, "text": "一共获得了[color=#ff7132]{0}[/color]个武器外观，快来并肩作战吧！"}}, {"id": 2915, "constName": "MainQuestMissionTime", "text": {"index": 1476179233, "text": "战局持续时间：{0}"}}, {"id": 2916, "constName": "MainQuestMissionTrainingDesc", "text": {"index": 605403495, "text": "想要成为一名合格的渗透者，需要在生存与探索，战斗与突袭，建造与工程各方面拥有熟练的技能水平才能在实验岛上生存下来。\n1、选择与切换：你可以选择任意一条势力特训，在特训过程中也可以随时切换任务线。\n2、战局时间：[color=#ff9234]战局持续期间，被淘汰，基地被摧毁都不会强制离开战局，直到战局持续时间结束或主动从实验岛撤离[/color]。\n   战局结束后将从实验岛撤离，撤离后将无法再返回该实验岛。\n3、跳过特训：跳过特训依然可以领取所有的势力特训任务，并在后续的行动中完成特训任务。"}}, {"id": 2917, "constName": "MainQuestMissionConfirmSelect ", "text": {"index": 258075737, "text": "您当前正处于{0}中，是否确定切换到{1}中？"}}, {"id": 2918, "constName": "UiRecruitTimeDesc", "text": {"index": 1909496409, "text": "{0:D2}/{1:D2} {2:D2}:{3:D2}"}}, {"id": 2919, "constName": "UiRecruitTimeOpened", "text": {"index": 53301631, "text": "已开服"}}, {"id": 2920, "constName": "UiRecruitTimeOpen", "text": {"index": 326111809, "text": "开服"}}, {"id": 2921, "constName": "AutoOpenDoorBtns", "text": {"index": 833046990, "text": "批量设置自动开关门"}}, {"id": 2922, "constName": "AutoOpenDoorBtnsTips", "text": {"index": 1178306606, "text": "您可通过此设置将领地下所有门类全部设置为开启或者关闭。"}}, {"id": 2923, "constName": "AutoOpenDoorAllOpen", "text": {"index": 1110416243, "text": "全部开启"}}, {"id": 2924, "constName": "AutoOpenDoorAllClose", "text": {"index": 1660732220, "text": "全部关闭"}}, {"id": 2925, "constName": "SettingOpenDoorAutoDirTips", "text": {"index": 371628860, "text": "开启设置后，在您打开领地下的门时，它们将朝你所处的反方向打开。"}}, {"id": 2926, "constName": "AutoOpenDoorTips", "text": {"index": 643713516, "text": "支持您根据个人喜好在自动开关门和仅自动关门之间切换。切换为仅自动关门后，门将不会自动打开，而是在您手动打开后自动关闭。"}}, {"id": 2927, "constName": "ShareDressSkinCountDesc", "text": {"index": 387572935, "text": "一共获得了[color=#ff7132]{0}[/color]个外观，快来并肩作战吧！"}}, {"id": 2928, "constName": "ShareBuildingSkinCountDesc", "text": {"index": 1611172202, "text": "一共获得了[color=#ff7132]{0}[/color]个建筑外观，快来并肩作战吧！"}}, {"id": 2929, "constName": "ShareItemSkinCountDesc", "text": {"index": 1628867260, "text": "一共获得了[color=#ff7132]{0}[/color]个摆件外观，快来并肩作战吧！"}}, {"id": 2930, "constName": "Commit", "text": {"index": 2062174980, "text": "提交"}}, {"id": 2931, "constName": "DetailPage", "text": {"index": 1664093932, "text": "详细信息"}}, {"id": 2932, "constName": "MedalPage", "text": {"index": 94388995, "text": "勋章"}}, {"id": 2933, "constName": "MainQuestTrainingConfirmTitle", "text": {"index": 711612043, "text": "势力特训"}}, {"id": 2934, "constName": "BuyLimitForever", "text": {"index": 1800256969, "text": "永久限购"}}, {"id": 2935, "constName": "BuyLimitSeason", "text": {"index": 920483167, "text": "赛季限购"}}, {"id": 2936, "constName": "BuyLimitMonth", "text": {"index": 272458743, "text": "本月限购"}}, {"id": 2937, "constName": "BuyLimitWeekly", "text": {"index": 1886651100, "text": "本周限购"}}, {"id": 2938, "constName": "BuyLimitDay", "text": {"index": 585100751, "text": "本日限购"}}, {"id": 2939, "constName": "InvalidDamageNumbers", "text": {"index": 1018407326, "text": "无效"}}, {"id": 2940, "constName": "TribeInviteEmptyTitle", "text": {"index": 11062681, "text": "暂时没有邀请"}}, {"id": 2941, "constName": "TribeActiveDesc", "text": {"index": 109094045, "text": "活跃度规则\n1.成员在社群频道发言即可增加活跃度，每人每天可提供的活跃度有限制。\n2.社群活跃度等于最近7天内所有成员的活跃度增量之和，每天0点更新。\n3.活跃度度达到[color=#FF7132]10000[/color] 即可被加入推荐列表。\n4.超级群如果连续多日活跃度低于[color=#FF7132]3000[/color] 则会被限制推荐。"}}, {"id": 2942, "constName": "TribeActiveFrozenDesc", "text": {"index": 703676307, "text": "低活跃度提醒\n1.因活跃度持续低迷，群已被[color=#FF7132]限制推荐[/color]（无法被推荐，无法通过标签搜索到）；其余群功能不受影响；\n2.活跃度增加到[color=#FF7132]5000[/color]后即可解除限制，群活跃度每天0点更新。\n3.成员在社群频道发言即可增加活跃度，每人每天可提供的活跃度有限制。"}}, {"id": 2943, "constName": "TribeMyTribeEmptyTitle", "text": {"index": 1153896159, "text": "未加入任何社群"}}, {"id": 2944, "constName": "CreateTribeCountLimit", "text": {"index": 1208578772, "text": "创建数量已达上限"}}, {"id": 2945, "constName": "CreateTribeLevelLimit", "text": {"index": 1447467163, "text": "{0}级可创建社群"}}, {"id": 2946, "constName": "CreateSuperTribeLevelLimit", "text": {"index": 382505120, "text": "{0}级可创建超级社群"}}, {"id": 2947, "constName": "NotSaveBlueprint", "text": {"index": 65961298, "text": "暂未保存蓝图"}}, {"id": 2948, "constName": "MainQuestMissionConfirmFirstSelectTitle", "text": {"index": 2113982077, "text": "开始特训"}}, {"id": 2949, "constName": "MainQuestMissionConfirmLeaveTitle", "text": {"index": 632536010, "text": "离开实验岛"}}, {"id": 2950, "constName": "MainQuestMissionConfirmNotFirstTitle", "text": {"index": 1127007564, "text": "突袭遗弃领地"}}, {"id": 2951, "constName": "MainQuestMissionConfirmNotice", "text": {"index": 548463592, "text": "注意:"}}, {"id": 2952, "constName": "MainQuestMissionConfirmReserve", "text": {"index": 1287024915, "text": "保留:"}}, {"id": 2953, "constName": "MainQuestMissionConfirmLoss", "text": {"index": 440491976, "text": "遗失:"}}, {"id": 2954, "constName": "MainQuestMissionConfirmNoticeDesc", "text": {"index": 1368098205, "text": "开始特训后，[color=#ce5b46]将解除新手保护，遭遇其他幸存者的攻击[/color]，务必做好准备。"}}, {"id": 2955, "constName": "MainQuestMissionConfirmReserveDesc", "text": {"index": 1771317629, "text": "撤离后，战局内获得的账号奖励（账号经验，皮肤，情报奖章等）将永久保留。"}}, {"id": 2956, "constName": "MainQuestMissionConfirmLossDesc", "text": {"index": 771963134, "text": "在实验岛上获得的装备，道具，材料，科技，情报等级，以及基地都不会被带走。"}}, {"id": 2957, "constName": "BatchUpgradeCustom", "text": {"index": 946939874, "text": "自定义升级"}}, {"id": 2958, "constName": "MedalLevelAdvance", "text": {"index": 1991819147, "text": "高级"}}, {"id": 2959, "constName": "MedalLevelMiddle", "text": {"index": 830451200, "text": "中级"}}, {"id": 2960, "constName": "MedalLevelNormal", "text": {"index": 508990073, "text": "普通"}}, {"id": 2961, "constName": "BatchRecoverCustom", "text": {"index": 856822448, "text": "自定义修复"}}, {"id": 2962, "constName": "MainQuestMissionConfirmLeaveConfirm", "text": {"index": 475817671, "text": "离开后，将直接前往[color=#ce5b46]标准战局[/color]，无法再返回[color=#ce5b46]新手实验岛[/color]，确定离开？"}}, {"id": 2963, "constName": "MainQuestMissionConfirmLeaveConfirm2", "text": {"index": 1075632561, "text": "跳过特训可以领取所有势力任务线并在后续的行动中完成"}}, {"id": 2964, "constName": "VehicleAddFuel", "text": {"index": 712366445, "text": "添加燃料"}}, {"id": 2965, "constName": "VehicleOwnNum", "text": {"index": 325781733, "text": "拥有数量：{0}"}}, {"id": 2966, "constName": "VehicleInputMax", "text": {"index": 2026561285, "text": "最多可放入：{0}"}}, {"id": 2967, "constName": "VehicleTakeFuel", "text": {"index": 77195699, "text": "取出燃料"}}, {"id": 2968, "constName": "VehicleLoadedNum", "text": {"index": 1769958857, "text": "装载数量：{0}"}}, {"id": 2969, "constName": "VehicleTakeMax", "text": {"index": 1516699135, "text": "最多可取出：{0}"}}, {"id": 2970, "constName": "VehicleFuelNum", "text": {"index": 1031493547, "text": "低品质燃油 +{0}"}}, {"id": 2971, "constName": "LoginExpired", "text": {"index": 1315030333, "text": "登录授权已过期，是否重新授权登录？"}}, {"id": 2972, "constName": "TribeInvitePermissionDesc", "text": {"index": 1068771991, "text": "开启后赋予全部成员邀请他人、审核入群申请的权限"}}, {"id": 2973, "constName": "TribeNameShortTip", "text": {"index": 584726343, "text": "群名过短"}}, {"id": 2974, "constName": "TribeNameIllegalTip", "text": {"index": 1891924740, "text": "群名含有不可用字符"}}, {"id": 2975, "constName": "ReputationFatigueDesc", "text": {"index": 1040102095, "text": "【用途】：领取情报账号奖励时，需消耗对应[color=#969696]疲劳值[/color]；\n【限制】：[color=#969696]疲劳值[/color]为0时将无法领取奖励；\n【重置】：每日凌晨5点将自动将[color=#969696]疲劳值[/color]重置到上限；\n【升级】：提升账号等级，可以提升[color=#969696]疲劳值[/color]上限；"}}, {"id": 2976, "constName": "TribeDefaultName", "text": {"index": 1434784268, "text": "{0}的群"}}, {"id": 2977, "constName": "TalentUnlockDesc", "text": {"index": 1235441730, "text": "{0}{1}解锁"}}, {"id": 2978, "constName": "TeamChooseLimitNum", "text": {"index": 601724793, "text": "人数"}}, {"id": 2979, "constName": "TeamChoosePlatform", "text": {"index": 948136392, "text": "平台"}}, {"id": 2980, "constName": "TeamChooseArea", "text": {"index": 564377228, "text": "区域"}}, {"id": 2981, "constName": "TeamChoosePlatformMobile", "text": {"index": 2145446200, "text": "手游服"}}, {"id": 2982, "constName": "TeamChoosePlatformPC", "text": {"index": 714218787, "text": "混合服"}}, {"id": 2983, "constName": "LockerS<PERSON>", "text": {"index": 1813564629, "text": "柜子{0}"}}, {"id": 2984, "constName": "HasMailToRead", "text": {"index": 1152466366, "text": "有待阅读邮件"}}, {"id": 2985, "constName": "TribeGraduate", "text": {"index": 519068121, "text": "进阶群可加入"}}, {"id": 2986, "constName": "RankName100101", "text": {"index": 1481893306, "text": "生存学徒Ⅰ"}}, {"id": 2987, "constName": "RankName100102", "text": {"index": 866112613, "text": "生存学徒Ⅱ"}}, {"id": 2988, "constName": "RankName100103", "text": {"index": 841827025, "text": "生存学徒Ⅲ"}}, {"id": 2989, "constName": "RankName100104", "text": {"index": 1825654152, "text": "荒野行者Ⅰ"}}, {"id": 2990, "constName": "RankName100105", "text": {"index": 1913688257, "text": "荒野行者Ⅱ"}}, {"id": 2991, "constName": "RankName100106", "text": {"index": 726984021, "text": "荒野行者Ⅲ"}}, {"id": 2992, "constName": "RankName100107", "text": {"index": 678863295, "text": "求生专家Ⅰ"}}, {"id": 2993, "constName": "RankName100108", "text": {"index": 1216500201, "text": "求生专家Ⅱ"}}, {"id": 2994, "constName": "RankName100109", "text": {"index": 465861014, "text": "求生专家Ⅲ"}}, {"id": 2995, "constName": "RankName100110", "text": {"index": 1385757221, "text": "自然之子Ⅰ"}}, {"id": 2996, "constName": "RankName100111", "text": {"index": 335865639, "text": "自然之子Ⅱ"}}, {"id": 2997, "constName": "RankName100112", "text": {"index": 1966513075, "text": "自然之子Ⅲ"}}, {"id": 2998, "constName": "RankName100113", "text": {"index": 943992708, "text": "万物共生Ⅰ"}}, {"id": 2999, "constName": "RankName100114", "text": {"index": 1384565924, "text": "万物共生Ⅱ"}}, {"id": 3000, "constName": "RankName100115", "text": {"index": 267990233, "text": "万物共生Ⅲ"}}, {"id": 3001, "constName": "Locker", "text": {"index": 1798954620, "text": "衣柜"}}, {"id": 3002, "constName": "StayNewbieTribe", "text": {"index": 1070441640, "text": "留在系统群"}}, {"id": 3003, "constName": "LeaveNewbieTribe", "text": {"index": 1356637568, "text": "加入进阶群"}}, {"id": 3004, "constName": "CheckAndPromptStartMatch", "text": {"index": 862438669, "text": "当前队伍人数未达到小队上限，请确认是否开始匹配？"}}, {"id": 3005, "constName": "ChoosePlanformFailed", "text": {"index": 1198747070, "text": "切换失败，{0}使用的非移动端设备，无法切换到手游模式"}}, {"id": 3006, "constName": "TribeApplyListEmpty", "text": {"index": 2002655009, "text": "暂无人申请"}}, {"id": 3007, "constName": "ChoosePlanformFailedTitle", "text": {"index": 1591713842, "text": "切换失败"}}, {"id": 3008, "constName": "LobbyStashEmpty", "text": {"index": 2139444450, "text": "暂无物品"}}, {"id": 3009, "constName": "EncounteredFriendTitle", "text": {"index": 207683938, "text": "最近遭遇"}}, {"id": 3010, "constName": "TribeInviteSomebody", "text": {"index": 1816115924, "text": "邀请入群"}}, {"id": 3011, "constName": "TribeHasInvitedSomebody", "text": {"index": 580137523, "text": "已邀请"}}, {"id": 3012, "constName": "ChangeTerritoryName", "text": {"index": 1607162596, "text": "编辑您的领地名称"}}, {"id": 3013, "constName": "TribeRecommend", "text": {"index": 442228792, "text": "推荐社群"}}, {"id": 3014, "constName": "TribeSearchResult", "text": {"index": 657120541, "text": "搜索结果"}}, {"id": 3015, "constName": "RankModeTwo", "text": {"index": 1683754460, "text": "双排模式"}}, {"id": 3016, "constName": "RankModeFour", "text": {"index": 1893027569, "text": "四排模式"}}, {"id": 3017, "constName": "UnlockCondition", "text": {"index": 1458659812, "text": "解锁条件"}}, {"id": 3018, "constName": "MedalUnlockTips1", "text": {"index": 1565479676, "text": "完成下列[color=#da622f]任意一项[/color]任务完成解锁勋章"}}, {"id": 3019, "constName": "MedalUnlockTips2", "text": {"index": 1764815216, "text": "积分差异"}}, {"id": 3020, "constName": "MedalHistory", "text": {"index": 1537073864, "text": "勋章记录"}}, {"id": 3021, "constName": "RaidHalfTimeTip", "text": {"index": 1450268488, "text": "下半场开始"}}, {"id": 3022, "constName": "RaidHalf1MinTip", "text": {"index": 1391153825, "text": "还剩一分钟"}}, {"id": 3023, "constName": "RaidTeamChooseTitle", "text": {"index": 245625900, "text": "阵营选择"}}, {"id": 3024, "constName": "RaidTeamNameAtk", "text": {"index": 1977396126, "text": "进攻"}}, {"id": 3025, "constName": "RaidTeamNameDef", "text": {"index": 1342526034, "text": "防守"}}, {"id": 3026, "constName": "RaidTooManyAtk", "text": {"index": 764571350, "text": "进攻方人数过多"}}, {"id": 3027, "constName": "RaidTooManyDef", "text": {"index": 1999371627, "text": "防守方人数过多"}}, {"id": 3028, "constName": "RaidTeamFull", "text": {"index": 1727167322, "text": "队伍满了"}}, {"id": 3029, "constName": "RaidTeamLocked", "text": {"index": 2141306405, "text": "队伍已锁定"}}, {"id": 3030, "constName": "RaidTargetChangeAtkInYard1", "text": {"index": 1254784749, "text": "游戏开始"}}, {"id": 3031, "constName": "RaidTargetChangeAtkInYard2", "text": {"index": 626860173, "text": "进攻敌方领地"}}, {"id": 3032, "constName": "RaidTargetChangeDefInYard1", "text": {"index": 1331514009, "text": "游戏开始"}}, {"id": 3033, "constName": "RaidTargetChangeDefInYard2", "text": {"index": 1668954314, "text": "保护我方领地"}}, {"id": 3034, "constName": "RaidBattleInYard", "text": {"index": 1844862421, "text": "正在争夺庭院"}}, {"id": 3035, "constName": "RaidBattleInCore", "text": {"index": 938986338, "text": "正在争夺核心层"}}, {"id": 3036, "constName": "RaidMainTargetAtk", "text": {"index": 1977417588, "text": "拆除工具柜"}}, {"id": 3037, "constName": "RaidMainTargetDef", "text": {"index": 1915856120, "text": "保护工具柜"}}, {"id": 3038, "constName": "RaidTcOnHitAtk", "text": {"index": 1585564361, "text": "正在拆除工具柜"}}, {"id": 3039, "constName": "RaidTcOnHitDef", "text": {"index": 1797033964, "text": "工具柜受到攻击"}}, {"id": 3040, "constName": "RaidTargetChangeAtkInCore1", "text": {"index": 1543541385, "text": "敌人已进入核心层"}}, {"id": 3041, "constName": "RaidTargetChangeAtkInCore2", "text": {"index": 1120494335, "text": "保护工具柜"}}, {"id": 3042, "constName": "RaidTargetChangeDefInCore1", "text": {"index": 379556354, "text": "已攻入核心层"}}, {"id": 3043, "constName": "RaidTargetChangeDefInCore2", "text": {"index": 1392926189, "text": "拆除工具柜"}}, {"id": 3044, "constName": "RaidHalfTimeItemGetAtk", "text": {"index": 1889233064, "text": "获得火焰喷射器"}}, {"id": 3045, "constName": "RaidHalfTimeItemGetDef", "text": {"index": 738967372, "text": "获得钉枪和长剑"}}, {"id": 3046, "constName": "RaidScoreBoardTitle", "text": {"index": 590651458, "text": "计分板"}}, {"id": 3047, "constName": "RaidScoreBoardKill", "text": {"index": 1891156894, "text": "K"}}, {"id": 3048, "constName": "RaidScoreBoardDie", "text": {"index": 19694169, "text": "D"}}, {"id": 3049, "constName": "RaidResultDestroyTcAtkTitle", "text": {"index": 1873148084, "text": "占领成功"}}, {"id": 3050, "constName": "RaidResultDestroyTcAtkDescribe", "text": {"index": 1341322827, "text": "工具柜被拆除"}}, {"id": 3051, "constName": "RaidResultDestroyTcDefTitle", "text": {"index": 1466446834, "text": "领地失守"}}, {"id": 3052, "constName": "RaidResultDestroyTcDefDescribe", "text": {"index": 1819235208, "text": "工具柜被拆除"}}, {"id": 3053, "constName": "RaidResultTimeoutAtkTitle", "text": {"index": 1020375112, "text": "占领失败"}}, {"id": 3054, "constName": "RaidResultTimeoutAtkDescribe", "text": {"index": 792154258, "text": "未能拆除工具柜"}}, {"id": 3055, "constName": "RaidResultTimeoutDefTitle", "text": {"index": 1267307076, "text": "战斗胜利"}}, {"id": 3056, "constName": "RaidResultTimeoutDefDescribe", "text": {"index": 1779290606, "text": "成功保卫领地"}}, {"id": 3057, "constName": "TribeChangeNameTitle", "text": {"index": 345445375, "text": "社群改名"}}, {"id": 3058, "constName": "TribeChangeNameTimeTips", "text": {"index": 392911188, "text": "每{0}{1}可修改一次"}}, {"id": 3059, "constName": "TribeChangeNameLengthLimitTips", "text": {"index": 1861458983, "text": "限制{0}-{1}字符"}}, {"id": 3060, "constName": "TribeChangeNameCostTips", "text": {"index": 215537840, "text": "本次改名需要消耗{0}"}}, {"id": 3061, "constName": "TribeChangeNameCostTips1", "text": {"index": 525079352, "text": "{0}张[color=#da622f]{1}[/color]<img src='{2}' width='39' height='39'/>"}}, {"id": 3062, "constName": "TribeChangeNameTimeLimitTips", "text": {"index": 2072032594, "text": "无法修改，下次修改日期{0}"}}, {"id": 3063, "constName": "TribeChangeNameEqualTips", "text": {"index": 1641161500, "text": "与原有名称相同"}}, {"id": 3064, "constName": "TXProtect", "text": {"index": 994371668, "text": "守护"}}, {"id": 3065, "constName": "TXSupport", "text": {"index": 700886483, "text": "客服"}}, {"id": 3066, "constName": "RecruitTeamTitle", "text": {"index": 855811521, "text": "{0} {1}[color=#98938D]/{2}[/color]"}}, {"id": 3067, "constName": "RaidMaptargetBeatEnemy", "text": {"index": 151289420, "text": "迎战敌人"}}, {"id": 3068, "constName": "RaidHalftimeitemgetAtkArmed", "text": {"index": 1503811235, "text": "获得物资补给"}}, {"id": 3069, "constName": "RaidMaptargetAtkTc", "text": {"index": 1632288176, "text": "拆除"}}, {"id": 3070, "constName": "RaidMaptargetDefTc", "text": {"index": 860300766, "text": "保护"}}, {"id": 3071, "constName": "MallGacha", "text": {"index": 2141695489, "text": "幸运抽奖"}}, {"id": 3072, "constName": "ChatChannelEmpty_Private", "text": {"index": 1981925491, "text": "暂无私聊玩家"}}, {"id": 3073, "constName": "ChatChannelEmpty_Team", "text": {"index": 290238009, "text": "暂无队伍"}}, {"id": 3074, "constName": "PhotoPathReset", "text": {"index": 552906679, "text": "重置路径"}}, {"id": 3075, "constName": "PhotoPlaceLookAtPoint", "text": {"index": 709476788, "text": "放置观察点"}}, {"id": 3076, "constName": "PhotoAttachLookAtPoint", "text": {"index": 1734763540, "text": "吸附观察点"}}, {"id": 3077, "constName": "PhotoPlaceWayPoint", "text": {"index": 1903400584, "text": "放置路点"}}, {"id": 3078, "constName": "PhotoTakeUpWayPoint", "text": {"index": 1073300039, "text": "拿起路点"}}, {"id": 3079, "constName": "PhotoRemoveWayPoint", "text": {"index": 935701311, "text": "删除路点"}}, {"id": 3080, "constName": "WarSituationTitle", "text": {"index": 1738802473, "text": "我的战局{0}/{1}"}}, {"id": 3081, "constName": "TribeSearchPromptText", "text": {"index": 1312918407, "text": "输入社群名称/编号或选择一个标签搜索"}}, {"id": 3082, "constName": "DailyMissionTitle", "text": {"index": 534560970, "text": "每日简报"}}, {"id": 3083, "constName": "MainQuestUnlockDesc", "text": {"index": 963801915, "text": "是否开启{0}特训？"}}, {"id": 3084, "constName": "UnlockTheSafe", "text": {"index": 596295566, "text": "解锁保险箱"}}, {"id": 3085, "constName": "UnlockTheSafeNotice", "text": {"index": 1055027139, "text": "退出游戏视为开锁失败，扣除的钥匙耐久值不返还"}}, {"id": 3086, "constName": "UnlockTheSafeCancel", "text": {"index": 1253272798, "text": "取消"}}, {"id": 3087, "constName": "UnlockTheSafeConfirm", "text": {"index": 1124933150, "text": "确认"}}, {"id": 3088, "constName": "TreasureHuntGameReset", "text": {"index": 1204994711, "text": "重置"}}, {"id": 3089, "constName": "StorySettlementSuccessed", "text": {"index": 1447221051, "text": "成功"}}, {"id": 3090, "constName": "StorySettlementFailed", "text": {"index": 1254191418, "text": "失败"}}, {"id": 3091, "constName": "DetailedDataKilled", "text": {"index": 689538258, "text": "击杀"}}, {"id": 3092, "constName": "DetailedDataDestroyBuilding", "text": {"index": 2079093231, "text": "摧毁建筑"}}, {"id": 3093, "constName": "DetailedDataObtainWaste", "text": {"index": 2084752142, "text": "获取废料"}}, {"id": 3094, "constName": "DetailedDataObtainDocuments", "text": {"index": 346532495, "text": "获取情报文件"}}, {"id": 3095, "constName": "DetailedDataBuildAndUpgradeBuildings", "text": {"index": 753481717, "text": "建造和升级建筑"}}, {"id": 3096, "constName": "DetailedDataOnlineDuration", "text": {"index": 1114949073, "text": "在线时长"}}, {"id": 3097, "constName": "DetailedDataUnknownPlayer", "text": {"index": 927495469, "text": "未知玩家"}}, {"id": 3098, "constName": "DetailedDataBattleRecords", "text": {"index": 1674074892, "text": "详细战绩"}}, {"id": 3099, "constName": "WarSituationApocalypseAgent", "text": {"index": 488318730, "text": "破译{0}"}}, {"id": 3100, "constName": "WarSituationIntelligenceDecryption", "text": {"index": 814952319, "text": "情报"}}, {"id": 3101, "constName": "WarSituationSettlementTime", "text": {"index": 471524848, "text": "用时"}}, {"id": 3102, "constName": "WarSituationTimeSpent", "text": {"index": 1191491010, "text": "{0}天 {1}: {2}: {3}"}}, {"id": 3103, "constName": "StoryPlanBoxActionObjective", "text": {"index": 1775207316, "text": "行动目标：提升情报等级"}}, {"id": 3104, "constName": "StoryPlanBoxSurvivalGuide", "text": {"index": 981690680, "text": "生存手册"}}, {"id": 3105, "constName": "StorySettlementLevel", "text": {"index": 1683254816, "text": "情报等级{0}级"}}, {"id": 3106, "constName": "MedalUnlockTips3", "text": {"index": 916610640, "text": "完成下列任务解锁勋章"}}, {"id": 3107, "constName": "And", "text": {"index": 1508218324, "text": "与"}}, {"id": 3108, "constName": "SavePicFolderName", "text": {"index": 2123231388, "text": "SKJH"}}, {"id": 3109, "constName": "ChatTribeCreated", "text": {"index": 1919871473, "text": "我创建的社群"}}, {"id": 3110, "constName": "ChatTribeJoined", "text": {"index": 1540639861, "text": "我加入的社群"}}, {"id": 3111, "constName": "CreateRoleEscTitle", "text": {"index": 1135595470, "text": "返回"}}, {"id": 3112, "constName": "ChatTeamRecruitTitle", "text": {"index": 2082655392, "text": "好伙伴一起来战斗！"}}, {"id": 3113, "constName": "TreasureHuntGameTitle", "text": {"index": 1439636729, "text": "接通讯号"}}, {"id": 3114, "constName": "TechLevel", "text": {"index": 1509616378, "text": "科技等级"}}, {"id": 3115, "constName": "AutoOpenDoor", "text": {"index": 1853992647, "text": "自动开关门"}}, {"id": 3116, "constName": "UiAddFriend", "text": {"index": 1188712251, "text": "添加好友"}}, {"id": 3117, "constName": "BlackList", "text": {"index": 2024941354, "text": "黑名单"}}, {"id": 3118, "constName": "ChatTribe", "text": {"index": 1679627378, "text": "社群"}}, {"id": 3119, "constName": "PhotoPCEsc", "text": {"index": 716988118, "text": "退出"}}, {"id": 3120, "constName": "PhotoPCFP", "text": {"index": 1856698379, "text": "第一人称"}}, {"id": 3121, "constName": "PhotoPCTP", "text": {"index": 1051765122, "text": "第三人称"}}, {"id": 3122, "constName": "PhotoPCHideUI", "text": {"index": 259765173, "text": "隐藏界面"}}, {"id": 3123, "constName": "PhotoPCShowUI", "text": {"index": 618909455, "text": "显示界面"}}, {"id": 3124, "constName": "PhotoPCResetAll", "text": {"index": 778522308, "text": "重置相机"}}, {"id": 3125, "constName": "TribeChatMsgNewMemberJoin", "text": {"index": 22553810, "text": "{0}加入社群"}}, {"id": 3126, "constName": "PathCameraSpeedTip", "text": {"index": 359634364, "text": "按照路点改变速度时，越稀疏的地方越快，越密集的地方越慢"}}, {"id": 3127, "constName": "PathCameraScreenMoveTip", "text": {"index": 1790069147, "text": "相对屏幕中心偏移的百分比，0代表屏幕中心"}}, {"id": 3128, "constName": "GachaProbabilityTip", "text": {"index": 790924408, "text": "概率"}}, {"id": 3129, "constName": "GachaProbabilityTitle", "text": {"index": 248945281, "text": "概率一览"}}, {"id": 3130, "constName": "GachaRuleTip", "text": {"index": 520997269, "text": "规则"}}, {"id": 3131, "constName": "RankMatchTips1", "text": {"index": 453752724, "text": "当前存在正在进行的勋章对战，无法开启"}}, {"id": 3132, "constName": "RankMatchTips2", "text": {"index": 2061851333, "text": "是否将生存评级结算切换至本场战局？"}}, {"id": 3133, "constName": "InteractiveShortcutTitleSwitchList", "text": {"index": 567266915, "text": "切换列表"}}, {"id": 3134, "constName": "InteractiveShortcutTitleReload", "text": {"index": 1161582505, "text": "手动填弹"}}, {"id": 3135, "constName": "InteractiveShortcutTitleAutoOpenDoorTure", "text": {"index": 417004508, "text": "自动门：开"}}, {"id": 3136, "constName": "InteractiveShortcutTitleAutoOpenDoorFalse", "text": {"index": 1548764537, "text": "自动门：关"}}, {"id": 3137, "constName": "InteractiveShortcutTitleSwitchFood", "text": {"index": 2115969260, "text": "切换食物"}}, {"id": 3138, "constName": "InteractiveShortcutTitleFullScreen", "text": {"index": 31824690, "text": "全屏拾取"}}, {"id": 3139, "constName": "InteractiveShortcutTitleCancelFood", "text": {"index": 1470628961, "text": "取消喂食"}}, {"id": 3140, "constName": "SettlementClear", "text": {"index": 1830117268, "text": "清档结算"}}, {"id": 3141, "constName": "NextStage", "text": {"index": 583886992, "text": "下个阶段"}}, {"id": 3142, "constName": "NoLootingRecord", "text": {"index": 1813918341, "text": "无物资掠夺记录"}}, {"id": 3143, "constName": "NoBuildingRecord", "text": {"index": 126486258, "text": "无建筑摧毁记录"}}, {"id": 3144, "constName": "OpenMedalRankScore", "text": {"index": 1326708254, "text": "开启生存评级"}}, {"id": 3145, "constName": "SwitchMedalRankScoreTip", "text": {"index": 158529694, "text": "是否将本场战局生存评级结算删除？"}}, {"id": 3146, "constName": "DeleteMedalRankScoreTip", "text": {"index": 775278213, "text": "是否将生存评级结算切换至本场战局?切换冷却时间为{0}小时"}}, {"id": 3147, "constName": "MedalRankScoreCDTime", "text": {"index": 893253768, "text": "生存评级切换冷却{0}"}}, {"id": 3148, "constName": "NoMedalRankScoreTip", "text": {"index": 544027568, "text": "无勋章积分，是否开启游戏"}}, {"id": 3149, "constName": "GachaPoolPreviewTabTitle", "text": {"index": 564604401, "text": "奖池内容"}}, {"id": 3150, "constName": "GachaTimeDayHour", "text": {"index": 1919672812, "text": "{0}天{1}小时"}}, {"id": 3151, "constName": "GachaTimeHourMinute", "text": {"index": 1472238017, "text": "{0}小时{1}分钟"}}, {"id": 3152, "constName": "GachaTimeMinute", "text": {"index": 1752636121, "text": "0时{0}分"}}, {"id": 3153, "constName": "LastPage", "text": {"index": 1344897795, "text": "上一页"}}, {"id": 3154, "constName": "NextPage", "text": {"index": 59909296, "text": "下一页"}}, {"id": 3155, "constName": "TribeTagListEmptyTip", "text": {"index": 1013519979, "text": "未设置标签"}}, {"id": 3156, "constName": "NewbieServerTime", "text": {"index": 86716656, "text": "战局持续时间：[color=#da622f]{0}[/color]"}}, {"id": 3157, "constName": "WeaponPartPreview", "text": {"index": 81820069, "text": "配件预览"}}, {"id": 3158, "constName": "WarSituationReputationLevel", "text": {"index": 676161551, "text": "Lv.{0}"}}, {"id": 3159, "constName": "ImportantHotKeyTip", "text": {"index": 203977284, "text": "重要设置需自主输入快捷键"}}, {"id": 3160, "constName": "HotKeyCannotModify", "text": {"index": 581257248, "text": "该按键不可修改"}}, {"id": 3161, "constName": "BoxGameLocked", "text": {"index": 56189786, "text": "已锁死（{0}s）"}}, {"id": 3162, "constName": "NoSearchPlayer", "text": {"index": 1342646029, "text": "未搜索到玩家"}}, {"id": 3163, "constName": "NoBlackPlayer", "text": {"index": 158633971, "text": "没有屏蔽玩家"}}, {"id": 3164, "constName": "TribeInThisTribe", "text": {"index": 2008442520, "text": "已在此群"}}, {"id": 3165, "constName": "CurrentBattleRecruitment", "text": {"index": 1425479734, "text": "当前战局招募"}}, {"id": 3166, "constName": "NextMedalSyncTime", "text": {"index": 163107312, "text": "下次信号回传：{0}"}}, {"id": 3167, "constName": "CurrFatigueValueTxt", "text": {"index": 1288309822, "text": "当前疲劳值："}}, {"id": 3168, "constName": "OpenSafeBoxBlock", "text": {"index": 445180300, "text": "保险柜激活失败，请先将暂存物品取出"}}, {"id": 3169, "constName": "RankName100201", "text": {"index": 89599930, "text": "拾荒新人Ⅰ"}}, {"id": 3170, "constName": "RankName100202", "text": {"index": 198104668, "text": "拾荒新人Ⅱ"}}, {"id": 3171, "constName": "RankName100203", "text": {"index": 1110516685, "text": "拾荒新人Ⅲ"}}, {"id": 3172, "constName": "RankName100204", "text": {"index": 1600881091, "text": "资源猎手Ⅰ"}}, {"id": 3173, "constName": "RankName100205", "text": {"index": 943511158, "text": "资源猎手Ⅱ"}}, {"id": 3174, "constName": "RankName100206", "text": {"index": 1163345018, "text": "资源猎手Ⅲ"}}, {"id": 3175, "constName": "RankName100207", "text": {"index": 400565017, "text": "开采专家Ⅰ"}}, {"id": 3176, "constName": "RankName100208", "text": {"index": 1835376346, "text": "开采专家Ⅱ"}}, {"id": 3177, "constName": "RankName100209", "text": {"index": 1917875984, "text": "开采专家Ⅲ"}}, {"id": 3178, "constName": "RankName100210", "text": {"index": 19348248, "text": "掠食达人Ⅰ"}}, {"id": 3179, "constName": "RankName100211", "text": {"index": 108607311, "text": "掠食达人Ⅱ"}}, {"id": 3180, "constName": "RankName100212", "text": {"index": 259823980, "text": "掠食达人Ⅲ"}}, {"id": 3181, "constName": "RankName100213", "text": {"index": 17611524, "text": "丰饶之主Ⅰ"}}, {"id": 3182, "constName": "RankName100214", "text": {"index": 1206442155, "text": "丰饶之主Ⅱ"}}, {"id": 3183, "constName": "RankName100215", "text": {"index": 1151691450, "text": "丰饶之主Ⅲ"}}, {"id": 3184, "constName": "RankName100301", "text": {"index": 85988060, "text": "荒野新兵Ⅰ"}}, {"id": 3185, "constName": "RankName100302", "text": {"index": 667427404, "text": "荒野新兵Ⅱ"}}, {"id": 3186, "constName": "RankName100303", "text": {"index": 1425403721, "text": "荒野新兵Ⅲ"}}, {"id": 3187, "constName": "RankName100304", "text": {"index": 2051776354, "text": "战场老兵Ⅰ"}}, {"id": 3188, "constName": "RankName100305", "text": {"index": 1059015465, "text": "战场老兵Ⅱ"}}, {"id": 3189, "constName": "RankName100306", "text": {"index": 1286325434, "text": "战场老兵Ⅲ"}}, {"id": 3190, "constName": "RankName100307", "text": {"index": 2070601851, "text": "百战精锐Ⅰ"}}, {"id": 3191, "constName": "RankName100308", "text": {"index": 1115676898, "text": "百战精锐Ⅱ"}}, {"id": 3192, "constName": "RankName100309", "text": {"index": 1431269055, "text": "百战精锐Ⅲ"}}, {"id": 3193, "constName": "RankName100310", "text": {"index": 280854207, "text": "战争大师Ⅰ"}}, {"id": 3194, "constName": "RankName100311", "text": {"index": 1104971810, "text": "战争大师Ⅱ"}}, {"id": 3195, "constName": "RankName100312", "text": {"index": 1677814901, "text": "战争大师Ⅲ"}}, {"id": 3196, "constName": "RankName100313", "text": {"index": 1544802459, "text": "废土主宰Ⅰ"}}, {"id": 3197, "constName": "RankName100314", "text": {"index": 963762398, "text": "废土主宰Ⅱ"}}, {"id": 3198, "constName": "RankName100315", "text": {"index": 373059230, "text": "废土主宰Ⅲ"}}, {"id": 3199, "constName": "RankName100401", "text": {"index": 1685460859, "text": "初级工匠Ⅰ"}}, {"id": 3200, "constName": "RankName100402", "text": {"index": 111607047, "text": "初级工匠Ⅱ"}}, {"id": 3201, "constName": "RankName100403", "text": {"index": 86301232, "text": "初级工匠Ⅲ"}}, {"id": 3202, "constName": "RankName100404", "text": {"index": 110496903, "text": "工程学徒Ⅰ"}}, {"id": 3203, "constName": "RankName100405", "text": {"index": 380736727, "text": "工程学徒Ⅱ"}}, {"id": 3204, "constName": "RankName100406", "text": {"index": 963242552, "text": "工程学徒Ⅲ"}}, {"id": 3205, "constName": "RankName100407", "text": {"index": 2071165382, "text": "基建专家Ⅰ"}}, {"id": 3206, "constName": "RankName100408", "text": {"index": 1664283883, "text": "基建专家Ⅱ"}}, {"id": 3207, "constName": "RankName100409", "text": {"index": 52118528, "text": "基建专家Ⅲ"}}, {"id": 3208, "constName": "RankName100410", "text": {"index": 2145339982, "text": "建筑大师Ⅰ"}}, {"id": 3209, "constName": "RankName100411", "text": {"index": 124439523, "text": "建筑大师Ⅱ"}}, {"id": 3210, "constName": "RankName100412", "text": {"index": 1703001517, "text": "建筑大师Ⅲ"}}, {"id": 3211, "constName": "RankName100413", "text": {"index": 1324999907, "text": "奇观造主Ⅰ"}}, {"id": 3212, "constName": "RankName100414", "text": {"index": 1740448173, "text": "奇观造主Ⅱ"}}, {"id": 3213, "constName": "RankName100415", "text": {"index": 819695685, "text": "奇观造主Ⅲ"}}, {"id": 3214, "constName": "RankName100501", "text": {"index": 2025322096, "text": "边缘行者Ⅰ"}}, {"id": 3215, "constName": "RankName100502", "text": {"index": 1916065987, "text": "边缘行者Ⅱ"}}, {"id": 3216, "constName": "RankName100503", "text": {"index": 1373096492, "text": "边缘行者Ⅲ"}}, {"id": 3217, "constName": "RankName100504", "text": {"index": 484117583, "text": "遗迹猎手Ⅰ"}}, {"id": 3218, "constName": "RankName100505", "text": {"index": 1636253686, "text": "遗迹猎手Ⅱ"}}, {"id": 3219, "constName": "RankName100506", "text": {"index": 827058429, "text": "遗迹猎手Ⅲ"}}, {"id": 3220, "constName": "RankName100507", "text": {"index": 888758131, "text": "开拓先驱Ⅰ"}}, {"id": 3221, "constName": "RankName100508", "text": {"index": 2019335587, "text": "开拓先驱Ⅱ"}}, {"id": 3222, "constName": "RankName100509", "text": {"index": 865637079, "text": "开拓先驱Ⅲ"}}, {"id": 3223, "constName": "RankName100510", "text": {"index": 703252570, "text": "荒野先锋Ⅰ"}}, {"id": 3224, "constName": "RankName100511", "text": {"index": 644984347, "text": "荒野先锋Ⅱ"}}, {"id": 3225, "constName": "RankName100512", "text": {"index": 2110887525, "text": "荒野先锋Ⅲ"}}, {"id": 3226, "constName": "RankName100513", "text": {"index": 463560189, "text": "行者无疆Ⅰ"}}, {"id": 3227, "constName": "RankName100514", "text": {"index": 540601068, "text": "行者无疆Ⅱ"}}, {"id": 3228, "constName": "RankName100515", "text": {"index": 1548389543, "text": "行者无疆Ⅲ"}}, {"id": 3229, "constName": "Lobby<PERSON><PERSON>on<PERSON><PERSON><PERSON>", "text": {"index": 883521033, "text": "装备&时装"}}, {"id": 3230, "constName": "LobbyWeaponTitle", "text": {"index": 490391039, "text": "武器&战备"}}, {"id": 3231, "constName": "Pick<PERSON>ll", "text": {"index": 73753196, "text": "全部拾取"}}, {"id": 3232, "constName": "ChangeTribe", "text": {"index": 1187761131, "text": "更换社群"}}, {"id": 3233, "constName": "QuickUse", "text": {"index": 1128945386, "text": "快捷使用"}}, {"id": 3234, "constName": "QuickEquip", "text": {"index": 367366518, "text": "快捷装备"}}, {"id": 3235, "constName": "NotInAnyTeam", "text": {"index": 1974495549, "text": "您没在队伍中"}}]}