using Combat;
using CommonUnity.Runtime.Animation;
using CommonUnity.Runtime.Character.Resource;
using Contexts;
using KAnimation.RootMotionWarping;
using System;
using System.Collections.Generic;
using Unity.Collections;
using Unity.Collections.LowLevel.Unsafe;
using Unity.Jobs;
using Unity.Mathematics;
using UnityEngine;
using Utilities;
using WizardGames.Editor;
using WizardGames.Soc.Common.Character;
using WizardGames.Soc.Common.Combat;
using WizardGames.Soc.Common.Contexts;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data.DataItem;
using WizardGames.Soc.Common.DataSet;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.Profile;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.State.Character;
using WizardGames.Soc.Common.Synchronization;
using WizardGames.Soc.Common.Systems;
using WizardGames.Soc.Common.Unity.Animation;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.Character.Job;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Go;
using WizardGames.Soc.Common.Unity.Manager;
using WizardGames.Soc.Common.Unity.ObjPool;
using WizardGames.Soc.Common.Unity.Utility;
using WizardGames.Soc.Common.Unity.Utility.Define;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Weapon;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Player.Animation;

namespace WizardGames.Soc.SocClient.Systems
{
    public class PlayerTpAnimatorSystem : IBaseSystem
    {
        public EProfileFunc ProfileFuncEnum => EProfileFunc.AfterLoop_PlayerTpAnimatorSystem;
        public static SocLogger logger = LogHelper.GetLogger(typeof(PlayerTpAnimatorSystem));
        /// <summary>
        /// 主角
        /// </summary>
        private Dictionary<long, PlayerEntity> LocalPlayerEntities;
        /// <summary>
        /// 其他玩家
        /// </summary>
        private Dictionary<long, PlayerEntity> playerEntities;
        /// <summary>
        /// 上下文
        /// </summary>
        private ClientContext clientContext;

        /// <summary>
        /// 主角entity
        /// </summary>
        private PlayerEntity myEntity;

        public NativeArray<TpAniPlayerDebugBone> DebugBones;
        
        private NativeArray<TpAnimationJobData> tpAnimationJobDataArray;
        private TpAniConstData tpConstData;
        private NativeArray<TpMaskWeightConfGroup> MaskArray;
        private NativeArray<TpMaskWeightConfGroup> AoMaskArray;
        private NativeArray<AnimProcedureCtrlJobConfGroup> MatchRules;
        /// <summary>
        /// tp oc层的时长数组
        /// </summary>
        private NativeArray<float> OverrideLayerTimeArray;

        private NativeArray<float> LocomotionLayerTimeArray;
        /// <summary>
        /// tp oc层的state循环数组
        /// </summary>
        private NativeArray<bool> OverrideLayerStateLoopArray;
        /// <summary>
        /// tp oc层的时长数组
        /// </summary>
        private NativeArray<bool> LocomotionLayerSpecialArray;
        /// <summary>
        /// 移动层动态权重
        /// </summary>
        private NativeArray<TpAniBoneMask> LocomotionLayerWeightArray;
        private Dictionary<WeaponUniqueIndex, int> TpLocomotionLayerWeightIndexDic;
        /// <summary>
        /// oc层curve的数组
        /// nativearray不支持嵌套，所以这里用指针
        /// </summary>
        [NativeDisableUnsafePtrRestriction]
        private IntPtr OverrideWeightAnimationCurves;
        private int OverrideWeightAnimationCurvesSize;
        /// <summary>
        /// turninplace的yaw曲线
        /// nativearray不支持嵌套，所以这里用指针
        /// </summary>
        [NativeDisableUnsafePtrRestriction]
        private IntPtr TurnInPlaceYawnCurves;
        private int TurnInPlaceYawnCurvesSize;
        /// <summary>
        /// 快照事件数据
        /// </summary>
        private NativeArray<SnapEventData> snapEventDataArray;
        /// <summary>
        /// 快照时间数量
        /// </summary>
        private int snapEventCount = 0;
        /// <summary>
        /// 当前的jobData数量
        /// </summary>
        public int jobDataCount = 0;
        /// <summary>
        /// 每把武器所需要的fp配置的数据
        /// </summary>
        private Dictionary<WeaponUniqueIndex, FpMetaData> tpFpMetaData;
        private Dictionary<WeaponUniqueIndex, TpClipLengthCollect> tpClipLengthCollectDic;
        private Dictionary<long, TpVehicleSeatTbData> tpVehicleSeatTbData;
        private CommonDelegate<QuickDrawEvent, ESnapEvent>.EventDelegate2 quickDrawEventDelegate;
        private CommonDelegate<FireDataEvent, ESnapEvent>.EventDelegate2 fireEventDelegate;
        private CommonDelegate<OpenDoorEvent, ESnapEvent>.EventDelegate2 openDoorEventDelegate;
        private CommonDelegate<PickUpEvent, ESnapEvent>.EventDelegate2 pickUpEventDelegate;
        /// <summary>
        /// 手持物列表的每把武器对应的数组的下标
        /// </summary>
        private Dictionary<WeaponUniqueIndex, int> tpAniHeldItemDataIndex;
        /// <summary>
        /// 手持物列表的每把武器对应的数组的下标，oc层的time index
        /// </summary>
        private Dictionary<WeaponUniqueIndex, int> OcHeldItemTimeIndex;
        /// <summary>
        /// 移动转身下标
        /// </summary>
        private Dictionary<WeaponUniqueIndex, int> tpTipCurveIndex;
        /// <summary>
        /// 手持物列表的每把武器对应的数组的下标，oc层的time index
        /// </summary>
        private Dictionary<WeaponUniqueIndex, int> LocHeldItemTimeIndex;

        private Dictionary<WeaponUniqueIndex, int> LocHeldItemSpecialIndex;

        /// <summary>
        /// 缓存当前没使用过的快照事件
        /// </summary>
        private Dictionary<long, List<SnapEventData>> SnapEventDataDic;
        
        // private long logicStateSubscribeId = -1;
        #region 状态变化监听
        private long UnaliveStateSubscribeId = -1;
        private long MoveStateSubscribeId = -1;
        private long MoveJumpStateSubscribeId = -1;
        private long MoveLadderStateSubscribeId = -1;
        private long MoveSwimStateSubscribeId = -1;
        private long MoveZiplineSubscribeId = -1;
        private long PoseStateSubscribeId = -1;
        private long PoseDyingStateSubscribeId = -1;
        private long ActionStateSubscribeId = -1;
        private long AttackSubStateSubscribeId = -1;
        private long ReloadStateSubscribeId = -1;
        private long BowStateSubscribeId = -1;
        private long ThrowStateSubscribeId = -1;
        private long ActionHoldStateSubscribeId = -1;
        private long AdsStateSubscribeId = -1;
        #endregion
        private long gestureSubscribeId = -1;
        private long ladderTargetIdSubscribeId = -1;
        private long ladderAbsorbTargetIdSubscribeId = -1;
        private long ladderEnterDirSubscribeId = -1;
        private long ladderLeaveDirSubscribeId = -1;
        private long ladderMoveFlagSubscribeId = -1;

        private long mountableIdSubscribeId = -1;
        private long horseMountDirSubscribeId = -1;
        private long vehicleTypeSubscribeId = -1;
        private long mountableTypeSubscribeId = -1;
        private long isDriverSubscribeId = -1;
        private long rotateYSubscribeId = -1;
        private long cmdYawSubscribeId = -1;
        private long adsOffsetProgressSubscribeId = -1;
        private long currentWeaponIdSubscribeId = -1;
        private long movement8DirectionSubscribeId = -1;
        private long movement4DirectionSubscribeId = -1;
        private long rootMotionWarpingIndexSubscribeId = -1;
        private long inputSprintSubscribeId = -1;
        private long HeightSubscribeId = -1;
        private long speedXSubscribeId = -1;
        private long speedZSubscribeId = -1;
        private long speedYSubscribeId = -1;
        private long stateRecoveryReasonSubscribeId = -1;
        private long tryFireSubscribeId = -1;
        private long cmdPitchSubscribeId = -1;
        private long useAnimIndexSubscribeId = -1;
        private long item8SubscribeId = -1;
        private long mountSeatIndexSubscribeId = -1;
        private long attachHitMatIndexSubscribeId = -1;
        private long useHitIndexSubscribeId = -1;
        private long interactiveIdSubscribeId = -1;
        
        private AnimationCurve defaultCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if !PUBLISH && !POCO
        public static List<string> DefaultDbgBonePaths = new List<string>()
        {
            "Root/Bip01",
            "Root/Bip01/Bip01 Pelvis",
            "Root/Bip01/Bip01 Pelvis/Bip01 L Thigh",
            "Root/Bip01/Bip01 Pelvis/Bip01 L Thigh/Bip01 L Calf",
            "Root/Bip01/Bip01 Pelvis/Bip01 R Thigh",
            "Root/Bip01/Bip01 Pelvis/Bip01 R Thigh/Bip01 R Calf",
        };
        
        public static List<string> SpineDbgBonePaths = new List<string>()
        {
            "Root/Bip01/Bip01 Pelvis/Bip01 Spine",
            "Root/Bip01/Bip01 Pelvis/Bip01 Spine/Bip01 Spine1",
            "Root/Bip01/Bip01 Pelvis/Bip01 Spine/Bip01 Spine1/Bip01 Spine2",
        };
        
        public static List<string> LeftClavDbgBonePaths = new List<string>()
        {
            "Root/Bip01/Bip01 Pelvis/Bip01 Spine/Bip01 Spine1/Bip01 Spine2/Bip01 L Clavicle",
            "Root/Bip01/Bip01 Pelvis/Bip01 Spine/Bip01 Spine1/Bip01 Spine2/Bip01 L Clavicle/Bip01 L UpperArm",
            "Root/Bip01/Bip01 Pelvis/Bip01 Spine/Bip01 Spine1/Bip01 Spine2/Bip01 L Clavicle/Bip01 L UpperArm/Bip01 L Forearm",
            "Root/Bip01/Bip01 Pelvis/Bip01 Spine/Bip01 Spine1/Bip01 Spine2/Bip01 L Clavicle/Bip01 L UpperArm/Bip01 L Forearm/Bip01 L Hand",
            "Root/Bip01/Bip01 Pelvis/Bip01 Spine/Bip01 Spine1/Bip01 Spine2/Bip01 L Clavicle/Bip01 L UpperArm/Bip01 L Forearm/Bip01 L Hand/LeftWeaponLocator",
        };
        
        public static List<string> RightClavDbgBonePaths = new List<string>()
        {
            "Root/Bip01/Bip01 Pelvis/Bip01 Spine/Bip01 Spine1/Bip01 Spine2/Bip01 R Clavicle",
            "Root/Bip01/Bip01 Pelvis/Bip01 Spine/Bip01 Spine1/Bip01 Spine2/Bip01 R Clavicle/BaseWeaponLocator",
            "Root/Bip01/Bip01 Pelvis/Bip01 Spine/Bip01 Spine1/Bip01 Spine2/Bip01 R Clavicle/Bip01 R UpperArm",
            "Root/Bip01/Bip01 Pelvis/Bip01 Spine/Bip01 Spine1/Bip01 Spine2/Bip01 R Clavicle/Bip01 R UpperArm/Bip01 R Forearm",
            "Root/Bip01/Bip01 Pelvis/Bip01 Spine/Bip01 Spine1/Bip01 Spine2/Bip01 R Clavicle/Bip01 R UpperArm/Bip01 R Forearm/Bip01 R Hand",
            "Root/Bip01/Bip01 Pelvis/Bip01 Spine/Bip01 Spine1/Bip01 Spine2/Bip01 R Clavicle/Bip01 R UpperArm/Bip01 R Forearm/Bip01 R Hand/RightWeaponLocator",
        };
        
        public static List<string> HeadDbgBonePaths = new List<string>()
        {
            "Root/Bip01/Bip01 Pelvis/Bip01 Spine/Bip01 Spine1/Bip01 Spine2/Bip01 Neck",
            "Root/Bip01/Bip01 Pelvis/Bip01 Spine/Bip01 Spine1/Bip01 Spine2/Bip01 Neck/Bip01 Head",
        };
#endif
        public unsafe void BuildBone(ETpDebugBoneGroup groupId,int offset,ref NativeArray<TpAniPlayerDebugBone> debugBones,List<string> bones,Allocator allocator)
        {
            for (int i = 0; i < bones.Count; i++)
            {
                var fullbonePath = bones[i];

                ref var dbgBone =
                    ref UnsafeUtility.ArrayElementAsRef<TpAniPlayerDebugBone>(debugBones.GetUnsafePtr(), offset + i);
                dbgBone.Weight = 1;
                dbgBone.Group = groupId;

             
                // dbgBone.BonePath = new FixedString512Bytes(fullbonePath);
                dbgBone.BonePathHash = Animator.StringToHash(fullbonePath);

                var boneName = fullbonePath;
                if (boneName.Contains("/"))
                {
                    boneName = boneName.Substring(boneName.LastIndexOf("/") + 1);
                }

                dbgBone.BoneName = new FixedString64Bytes(boneName);;
            }
        }
        
        public unsafe void OnCreate(Context context)
        {
            Mc.Entity.OnFullEntityCreate += OnCreateEntity;
            Mc.Entity.OnFullEntityDowngrade += OnEntityDowngrade;
            clientContext = context as ClientContext;
            if (clientContext == null)
            {
                return;
            }
            playerEntities = context.MgrEntity.GetEntitiesViaType<PlayerEntity>();
            LocalPlayerEntities = context.MgrEntity.GetEntitiesViaType<PlayerEntity>(true);
            Mc.Msg.AddListener<long>(EventDefine.OnLadderTransformChange,OnTransChange);
            SnapEventDataDic = new Dictionary<long, List<SnapEventData>>();

            using (var step1 = new PrintMemoryUsage("TpConstData"))
            {
                
#if !PUBLISH && !POCO
                var total = DefaultDbgBonePaths.Count + SpineDbgBonePaths.Count + LeftClavDbgBonePaths.Count +
                            RightClavDbgBonePaths.Count + HeadDbgBonePaths.Count;
                DebugBones = new NativeArray<TpAniPlayerDebugBone>(total, Allocator.Persistent);
                
                BuildBone(ETpDebugBoneGroup.Group, 0, ref DebugBones, DefaultDbgBonePaths, Allocator.Persistent);
                BuildBone(ETpDebugBoneGroup.Spine, DefaultDbgBonePaths.Count, ref DebugBones, SpineDbgBonePaths, Allocator.Persistent);
                BuildBone(ETpDebugBoneGroup.LeftClav, DefaultDbgBonePaths.Count + SpineDbgBonePaths.Count, ref DebugBones, LeftClavDbgBonePaths, Allocator.Persistent);
                BuildBone(ETpDebugBoneGroup.RightClav, DefaultDbgBonePaths.Count + SpineDbgBonePaths.Count + LeftClavDbgBonePaths.Count, ref DebugBones, RightClavDbgBonePaths, Allocator.Persistent);
                BuildBone(ETpDebugBoneGroup.Head, DefaultDbgBonePaths.Count + SpineDbgBonePaths.Count + LeftClavDbgBonePaths.Count + RightClavDbgBonePaths.Count, ref DebugBones, HeadDbgBonePaths, Allocator.Persistent);
#endif
                
                tpAnimationJobDataArray = new NativeArray<TpAnimationJobData>(2, Allocator.Persistent);
                clientContext.TpAnimationResultJobDataArray =
                    new NativeArray<TpAnimationResultJobData>(2, Allocator.Persistent);
                tpConstData = new TpAniConstData()
                {
                    PaddleRowDriveAnimLerp = McCommonUnity.Tables.TbChracterParameter.PaddleRowDriveAnimLerp,
                    HorseSpeacialWeightCurve = SocUnityConstName.HorseSpeacialWeightCurve,
                    VectorForward = new float3(0, 0, 1),
                    VectorUp = new float3(0, 1, 0),
                    LocomotionLerpSpeed = PlayerLoader.CharacterConfig.TpAniCollections.LocomotionLerpSpeed,
                    DrawNameHash = SocUnityConstName.stand_hip_draw,
                    AttackRNameHash = SocUnityConstName.stand_hip_attack_r,
                    AttackLNameHash = SocUnityConstName.stand_hip_attack_l,
                    AttackR2NameHash = SocUnityConstName.stand_hip_attack_r2,
                    AttackL2NameHash = SocUnityConstName.stand_hip_attack_l2,
                    AttackUNameHash = SocUnityConstName.stand_hip_attack_u,
                    AttackCNameHash = SocUnityConstName.stand_hip_attack_c,
                    AimOffsetDynamicWeightCurve = SocUnityConstName.AimOffset_Dynamic_WeightCruve,
                    AimOffsetSpineWeightCurve = SocUnityConstName.AimOffset_Spine_WeightCruve,
                    AimOffsetSpine1WeightCurve = SocUnityConstName.AimOffset_Spine1_WeightCruve,
                    AimOffsetSpine2WeightCurve = SocUnityConstName.AimOffset_Spine2_WeightCruve,
                    AimOffsetHeadWeightCurve = SocUnityConstName.AimOffset_Head_WeightCruve,
                    AimOffsetHandWeightCurve = SocUnityConstName.AimOffset_Hand_WeightCruve,
                    PitchLimitAngle = McCommonUnity.Tables.TbChracterParameter.PitchLimitAngle,
                    LadderLeftRotAngle = McCommonUnity.Tables.TbChracterParameter.LadderLeftRotAngle,
                    LadderRightRotAngle = McCommonUnity.Tables.TbChracterParameter.LadderRightRotAngle,
                    LadderTurnAngle = McCommonUnity.Tables.TbChracterParameter.LadderTurnAngle,
                    LadderRightAniRotAngle = McCommonUnity.Tables.TbChracterParameter.LadderRightAniRotAngle,
                    LadderLeftAniRotAngle = McCommonUnity.Tables.TbChracterParameter.LadderLeftAniRotAngle,
                    JogDynamicWeightCurve = SocUnityConstName.JogDynamicWeight_WeightCruve,
                    JogSpineWeightCurve = SocUnityConstName.JogSpineWeight_WeightCruve,
                    JogSpine1WeightCurve = SocUnityConstName.JogSpine1Weight_WeightCruve,
                    JogSpine2WeightCurve = SocUnityConstName.JogSpine2Weight_WeightCruve,
                    JogLClavicleWeightCurve = SocUnityConstName.JogLClavicleWeight_WeightCruve,
                    JogHeadWeightCurve = SocUnityConstName.JogHeadWeight_WeightCruve,
                    JogRClavicleWeightCurve = SocUnityConstName.JogRClavicleWeight_WeightCruve,
                    JogWeaponWeightCurve = SocUnityConstName.JogWeaponWeight_WeightCruve,
                    JogLeftArmDynamicWeightCurve = SocUnityConstName.JogLeftArmWeight_WeightCruve,
                    JogRightArmDynamicWeightCurve = SocUnityConstName.JogRightArmWeight_WeightCruve,
                    SprintDynamicWeightCurve = SocUnityConstName.SprintDynamicWeight_WeightCruve,
                    SprintSpineWeightCurve = SocUnityConstName.SprintSpineWeight_WeightCruve,
                    SprintSpine1WeightCurve = SocUnityConstName.SprintSpine1Weight_WeightCruve,
                    SprintSpine2WeightCurve = SocUnityConstName.SprintSpine2Weight_WeightCruve,
                    SprintLClavicleWeightCurve = SocUnityConstName.SprintLClavicleWeight_WeightCruve,
                    SprintHeadWeightCurve = SocUnityConstName.SprintHeadWeight_WeightCruve,
                    SprintRClavicleWeightCurve = SocUnityConstName.SprintRClavicleWeight_WeightCruve,
                    SprintWeaponWeightCurve = SocUnityConstName.SprintWeaponWeight_WeightCruve,
                    SprintLeftArmDynamicWeightCurve = SocUnityConstName.SprintLeftArmWeight_WeightCruve,
                    SprintRightArmDynamicWeightCurve = SocUnityConstName.SprintRightArmWeight_WeightCruve,
                    JumpDynamicWeightCurve = SocUnityConstName.JumpDynamicWeight_WeightCruve,
                    JumpSpineWeightCurve = SocUnityConstName.JumpSpineWeight_WeightCruve,
                    JumpSpine1WeightCurve = SocUnityConstName.JumpSpine1Weight_WeightCruve,
                    JumpSpine2WeightCurve = SocUnityConstName.JumpSpine2Weight_WeightCruve,
                    JumpLClavicleWeightCurve = SocUnityConstName.JumpLClavicleWeight_WeightCruve,
                    JumpHeadWeightCurve = SocUnityConstName.JumpHeadWeight_WeightCruve,
                    JumpRClavicleWeightCurve = SocUnityConstName.JumpRClavicleWeight_WeightCruve,
                    JumpWeaponWeightCurve = SocUnityConstName.JumpWeaponWeight_WeightCruve,
                    JumpLeftArmDynamicWeightCurve = SocUnityConstName.JumpLeftArmWeight_WeightCruve,
                    JumpRightArmDynamicWeightCurve = SocUnityConstName.JumpRightArmWeight_WeightCruve,
                    SwimIdleDynamicWeightCurve = SocUnityConstName.SwimIdleDynamicWeight_WeightCruve,
                    SwimIdleSpineWeightCurve = SocUnityConstName.SwimIdleSpineWeight_WeightCruve,
                    SwimIdleSpine1WeightCurve = SocUnityConstName.SwimIdleSpine1Weight_WeightCruve,
                    SwimIdleSpine2WeightCurve = SocUnityConstName.SwimIdleSpine2Weight_WeightCruve,
                    SwimIdleLClavicleWeightCurve = SocUnityConstName.SwimIdleLClavicleWeight_WeightCruve,
                    SwimIdleHeadWeightCurve = SocUnityConstName.SwimIdleHeadWeight_WeightCruve,
                    SwimIdleRClavicleWeightCurve = SocUnityConstName.SwimIdleRClavicleWeight_WeightCruve,
                    SwimIdleWeaponWeightCurve = SocUnityConstName.SwimIdleWeaponWeight_WeightCruve,
                    SwimIdleLeftArmDynamicWeightCurve = SocUnityConstName.SwimIdleLeftArmWeight_WeightCruve,
                    SwimIdleRightArmDynamicWeightCurve = SocUnityConstName.SwimIdleRightArmWeight_WeightCruve,
                    SwimDynamicWeightCurve = SocUnityConstName.SwimDynamicWeight_WeightCruve,
                    SwimSpineWeightCurve = SocUnityConstName.SwimSpineWeight_WeightCruve,
                    SwimSpine1WeightCurve = SocUnityConstName.SwimSpine1Weight_WeightCruve,
                    SwimSpine2WeightCurve = SocUnityConstName.SwimSpine2Weight_WeightCruve,
                    SwimLClavicleWeightCurve = SocUnityConstName.SwimLClavicleWeight_WeightCruve,
                    SwimHeadWeightCurve = SocUnityConstName.SwimHeadWeight_WeightCruve,
                    SwimRClavicleWeightCurve = SocUnityConstName.SwimRClavicleWeight_WeightCruve,
                    SwimWeaponWeightCurve = SocUnityConstName.SwimWeaponWeight_WeightCruve,
                    SwimLeftArmDynamicWeightCurve = SocUnityConstName.SwimLeftArmWeight_WeightCruve,
                    SwimRightArmDynamicWeightCurve = SocUnityConstName.SwimRightArmWeight_WeightCruve,
                    CrouchDynamicWeightCurve = SocUnityConstName.CrouchDynamicWeight_WeightCruve,
                    CrouchSpineWeightCurve = SocUnityConstName.CrouchSpineWeight_WeightCruve,
                    CrouchSpine1WeightCurve = SocUnityConstName.CrouchSpine1Weight_WeightCruve,
                    CrouchSpine2WeightCurve = SocUnityConstName.CrouchSpine2Weight_WeightCruve,
                    CrouchLClavicleWeightCurve = SocUnityConstName.CrouchLClavicleWeight_WeightCruve,
                    CrouchHeadWeightCurve = SocUnityConstName.CrouchHeadWeight_WeightCruve,
                    CrouchRClavicleWeightCurve = SocUnityConstName.CrouchRClavicleWeight_WeightCruve,
                    CrouchWeaponWeightCurve = SocUnityConstName.CrouchWeaponWeight_WeightCruve,
                    CrouchLeftArmDynamicWeightCurve = SocUnityConstName.CrouchLeftArmWeight_WeightCruve,
                    CrouchRightArmDynamicWeightCurve = SocUnityConstName.CrouchRightArmWeight_WeightCruve,
                    LadderDynamicWeightCurve = SocUnityConstName.LadderDynamicWeight_WeightCruve,
                    LadderSpineWeightCurve = SocUnityConstName.LadderSpineWeight_WeightCruve,
                    LadderSpine1WeightCurve = SocUnityConstName.LadderSpine1Weight_WeightCruve,
                    LadderSpine2WeightCurve = SocUnityConstName.LadderSpine2Weight_WeightCruve,
                    LadderLClavicleWeightCurve = SocUnityConstName.LadderLClavicleWeight_WeightCruve,
                    LadderHeadWeightCurve = SocUnityConstName.LadderHeadWeight_WeightCruve,
                    LadderRClavicleWeightCurve = SocUnityConstName.LadderRClavicleWeight_WeightCruve,
                    LadderWeaponWeightCurve = SocUnityConstName.LadderWeaponWeight_WeightCruve,
                    LadderLeftArmDynamicWeightCurve = SocUnityConstName.LadderLeftArmWeight_WeightCruve,
                    LadderRightArmDynamicWeightCurve = SocUnityConstName.LadderRightArmWeight_WeightCruve,
                };
            }

            using (var step2 = new PrintMemoryUsage("TpMatchRules"))
            {
                SpeedModule.RebuildMatchRules(ref MatchRules,PlayerLoader.CharacterConfig.TpMoveSpeedMatchRules,AnimParametersTp.floatOffsetMap,AnimParametersTp.intOffsetMap,AnimParametersTp.boolOffsetMap);
            }

            var tpSkeletonDynamicsMaskData = PlayerLoader.CharacterConfig.TpAniCollections.TpSkeletonDynamicsMasks;
            if(tpSkeletonDynamicsMaskData == null)
            {
                logger.Error("tpSkeletonDynamicsMaskData is null");
                return;
            }

            using (var step3 = new PrintMemoryUsage("tpSkeletonDynamicsMaskData"))
            {
                MaskArray = new NativeArray<TpMaskWeightConfGroup>(tpSkeletonDynamicsMaskData.Count, Allocator.Persistent);

                int maskCnt = 0;
                foreach (var (key,maskdata) in tpSkeletonDynamicsMaskData)
                {
                    var masklist = maskdata.MaskList;
                    if (masklist.Count > TpMaskWeightConfGroup.Capacity)
                    {
                        logger.ErrorFormat("{0} masklist.Count > TpMaskWeightConfGroup.Capacity:{1} > {2}",key, masklist.Count, TpMaskWeightConfGroup.Capacity);
                        continue;
                    }
                    TpMaskWeightConfGroup targetmask = new TpMaskWeightConfGroup();
                    for (int i=0;i<masklist.Count;i++)
                    {
                        var mask = masklist[i];
                        targetmask.Set(i, mask);
                    }
                    MaskArray[maskCnt] = targetmask;

                    maskCnt++;
                }
            }

            
            var tpSkeletonDynamicsAoMaskData = PlayerLoader.CharacterConfig.TpAniCollections.TpSkeletonDynamicsAoMasks;
            if(tpSkeletonDynamicsAoMaskData == null)
            {
                logger.Error("tpSkeletonDynamicsAoMaskData is null");
                return;
            }

            using (var step4 = new PrintMemoryUsage("tpSkeletonDynamicsAoMaskData"))
            {
                AoMaskArray= new NativeArray<TpMaskWeightConfGroup>(tpSkeletonDynamicsAoMaskData.Count, Allocator.Persistent);

                int aoMaskCnt = 0;
                foreach (var (key,maskdata) in tpSkeletonDynamicsAoMaskData)
                {
                    var masklist = maskdata.MaskList;
                    if (masklist.Count > TpMaskWeightConfGroup.Capacity)
                    {
                        logger.ErrorFormat("{0} ao masklist.Count > TpMaskWeightConfGroup.Capacity:{1} > {2}",key, masklist.Count, TpMaskWeightConfGroup.Capacity);
                        continue;
                    }
                    TpMaskWeightConfGroup targetmask = new TpMaskWeightConfGroup();
                    for (int i=0;i<masklist.Count;i++)
                    {
                        var mask = masklist[i];
                        targetmask.Set(i, mask);
                    }
                    AoMaskArray[aoMaskCnt] = targetmask;

                    aoMaskCnt++;
                }
            }

            snapEventDataArray = new NativeArray<SnapEventData>(2, Allocator.Persistent);

            var tpCollection = PlayerLoader.CharacterConfig.TpAniCollections.ClipSettingMetas;
            var tpCollectionCount = tpCollection.Count;

            tpAniHeldItemDataIndex = new Dictionary<WeaponUniqueIndex, int>();
            var eOverrideLayerSum = (int)AnimParametersTp.EOverrideLayer.Sum;
            var eLocomotionLayerSum = (int)AnimParametersTp.ELocomotionLayer.Sum;
            using (var step6 = new PrintMemoryUsage("OverrideWeightAnimationCurves"))
            {
                OverrideWeightAnimationCurvesSize = tpCollection.Count * eOverrideLayerSum;
                OverrideWeightAnimationCurves = (IntPtr)UnsafeUtility.Malloc(
                    UnsafeUtility.SizeOf<TpAniOverrideLayerWeight>() * OverrideWeightAnimationCurvesSize,
                    UnsafeUtility.AlignOf<TpAniOverrideLayerWeight>(), Allocator.Persistent);
                var tpAniHeldItemDataIndexSum = 0;
                foreach (var item in tpCollection)
                {
                    var key = item.Key;
                    var curSetting = item.Value;
                    for (var i = 0; i < eOverrideLayerSum; i++)
                    {
                        var tpAniOverrideLayerWeight = new TpAniOverrideLayerWeight();
                        bool hasData = false;
                        if (curSetting.OcLayerWeightCollection.TryGetValue((AnimParametersTp.EOverrideLayer)i,
                                out var collection))
                        {
                            if (collection != null)
                            {
                                logger.InfoFormat("开始设置OcLayerWeightCollection curSetting {0}  layer:{1} collection key =:{2}",curSetting,(AnimParametersTp.EOverrideLayer)i, key.TableId);
                                
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.IdleStandWeightCurve,
                                    collection.fastIdleStandWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.IdleCrouchWeightCurve,
                                    collection.fastIdleCrouchWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.JogStandWeightCurve,
                                    collection.fastJogStandWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.JogCrouchWeightCurve,
                                    collection.fastJogCrouchWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.SprintWeightCurve,
                                    collection.fastSprintWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.JumpWeightCurve,
                                    collection.fastJumpWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.SwimIdleWeightCurve,
                                    collection.fastSwimIdleWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.SwimJogWeightCurve,
                                    collection.fastSwimJogWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.SwimSprintWeightCurve,
                                    collection.fastSwimSprintWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.LadderWeightCurve,
                                    collection.fastLadderWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.HorseWeightCurve,
                                    collection.fastHorseWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.VehicleWeightCurve,
                                    collection.fastVehicleWeightCurve);

                                hasData = true;
                            }
                            else
                            {
                                logger.InfoFormat("配置的OcLayerWeightCollection {0} collection is null key =:{1}",
                                    (AnimParametersTp.EOverrideLayer)i, key);
                            }
                        }

                        if (!hasData)
                        {
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.IdleStandWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.IdleCrouchWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.JogStandWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.JogCrouchWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.SprintWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.JumpWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.SwimIdleWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.SwimJogWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.SwimSprintWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.LadderWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.HorseWeightCurve, null, true);
                        }

                        var nowIndex = tpAniHeldItemDataIndexSum * eOverrideLayerSum + i;
                        UnsafeUtility.WriteArrayElement((void*)OverrideWeightAnimationCurves, nowIndex,
                            tpAniOverrideLayerWeight);
                    }

                    tpAniHeldItemDataIndex.Add(key, tpAniHeldItemDataIndexSum++);
                }
            }

            //缓存oc层所有武器的时长和loop
            OcHeldItemTimeIndex = new Dictionary<WeaponUniqueIndex, int>();
            LocHeldItemTimeIndex = new Dictionary<WeaponUniqueIndex, int>();
            var ocHeldItemTimeIndexSum = 0;
            var locHeldItemTimeIndexSum = 0;
            using (var step7 = new PrintMemoryUsage("OverrideLayerTime"))
            {
                OverrideLayerTimeArray =
                    new NativeArray<float>(tpCollectionCount * eOverrideLayerSum, Allocator.Persistent);
                OverrideLayerStateLoopArray =
                    new NativeArray<bool>(tpCollectionCount * eOverrideLayerSum, Allocator.Persistent);
                LocomotionLayerTimeArray =
                    new NativeArray<float>(tpCollectionCount * eLocomotionLayerSum, Allocator.Persistent);

                foreach (var item in tpCollection)
                {
                    var key = item.Key;
                    var curSetting = item.Value;
                    
                    logger.InfoFormat("开始设置 OverrideLayerTime curSetting {0} key =:{1}", curSetting, key.TableId);
                    
                    OcHeldItemTimeIndex.Add(key, ocHeldItemTimeIndexSum);
                    LocHeldItemTimeIndex.Add(key, locHeldItemTimeIndexSum);
                    if (curSetting != null && curSetting.OverrideLayerStateTime != null &&
                        curSetting.OverrideLayerStateTime.Length == eOverrideLayerSum)
                    {
                        //数量对其index
                        for (int i = 0; i < eOverrideLayerSum; i++)
                        {
                            OverrideLayerTimeArray[ocHeldItemTimeIndexSum] = curSetting.OverrideLayerStateTime[i];
                            OverrideLayerStateLoopArray[ocHeldItemTimeIndexSum] = curSetting.OverrideLayerStateLoop[i];
                            ocHeldItemTimeIndexSum++;
                        }
                    }
                    else
                    {
#if UNITY_EDITOR
                        logger.ErrorFormat(
                            "tp oc layer setting is null or length not match eOverrideLayerSum:{0} curSetting:{1}",
                            eOverrideLayerSum, curSetting);
#endif
                        //对不齐，自动填充0
                        for (int i = 0; i < eOverrideLayerSum; i++)
                        {
                            OverrideLayerTimeArray[ocHeldItemTimeIndexSum] = 0;
                            OverrideLayerStateLoopArray[ocHeldItemTimeIndexSum] = false;
                            ocHeldItemTimeIndexSum++;
                        }
                    }

                    if (curSetting != null && curSetting.LocomotionLayerStateTime != null &&
                        curSetting.LocomotionLayerStateTime.Length == eLocomotionLayerSum)
                    {
                        //数量对其index
                        for (int i = 0; i < eLocomotionLayerSum; i++)
                        {
                            LocomotionLayerTimeArray[locHeldItemTimeIndexSum] = curSetting.LocomotionLayerStateTime[i];
                            locHeldItemTimeIndexSum++;
                        }
                    }
                    else
                    {
#if UNITY_EDITOR
                        // if (curSetting != null && curSetting.LocomotionLayerStateTime != null &&curSetting.LocomotionLayerStateTime.Length != 0)
                        //     logger.ErrorFormat("tp oc layer setting is null or length not match eOverrideLayerSum:{0} key:{1}", eLocomotionLayerSum, key);
#endif
                        //对不齐，自动填充0
                        for (int i = 0; i < eLocomotionLayerSum; i++)
                        {
                            LocomotionLayerTimeArray[locHeldItemTimeIndexSum] = 0;
                            locHeldItemTimeIndexSum++;
                        }
                    }
                }
            }


            var fpClipMetaDataDict = new Dictionary<WeaponUniqueIndex, Dictionary<int, ClipMetaData>>();
            var fpClipSetttings = PlayerLoader.CharacterConfig.FpAniCollections.FpClipSettingMetas;
            using (var step = new PrintMemoryUsage("FpClipSettingMetas"))
            {
                foreach (var subClipSetting in fpClipSetttings)
                {
                    var clipIndex = subClipSetting.Key;
                    var clipDataDic = subClipSetting.Value.NewClipInfos;
                    var dic = new Dictionary<int, ClipMetaData>();
                    foreach (var clipDataItem in clipDataDic)
                    {
                        var str = clipDataItem.Key.GetStr(PlayerLoader.CharacterConfig.AnimStrGroup);
                        var name = Animator.StringToHash(str);
                        var clipData = clipDataItem.Value;

                        dic.Add(name, new ClipMetaData()
                        {
                            ClipNameHash = name,
                            AnimationTime = clipData.AnimationTime,
                            AnimatorStateTime = clipData.AnimatorStateTime,
                            RecoveryPercentage = clipData.RecoveryPercentage,
                        });
                    }

                    fpClipMetaDataDict.TryAdd(clipIndex, dic);
                }
            }

            using (var step = new PrintMemoryUsage("tpFpMetaData"))
            {
                tpFpMetaData = new Dictionary<WeaponUniqueIndex, FpMetaData>();
                foreach (var map in fpClipMetaDataDict)
                {
                    var holdTbId = map.Key;
                    var clipMetaDic = map.Value;
                    clipMetaDic.TryGetValue(tpConstData.DrawNameHash, out var drawClipMetaData);
                    clipMetaDic.TryGetValue(tpConstData.AttackRNameHash, out var attackRClipMeta);
                    clipMetaDic.TryGetValue(tpConstData.AttackR2NameHash, out var attackR2ClipMeta);
                    clipMetaDic.TryGetValue(tpConstData.AttackCNameHash, out var attackCClipMeta);
                    clipMetaDic.TryGetValue(tpConstData.AttackLNameHash, out var attackLClipMeta);
                    clipMetaDic.TryGetValue(tpConstData.AttackL2NameHash, out var attackL2ClipMeta);
                    clipMetaDic.TryGetValue(tpConstData.AttackUNameHash, out var attackUClipMeta);
                    var fpMetaData = new FpMetaData()
                    {
                        DrawClipMetaData = drawClipMetaData,
                        AttackCClipMetaData = attackCClipMeta,
                        AttackUClipMetaData = attackUClipMeta,
                        AttackLClipMetaData = attackLClipMeta,
                        AttackR2ClipMetaData = attackR2ClipMeta,
                        AttackL2ClipMetaData = attackL2ClipMeta,
                        AttackRClipMetaData = attackRClipMeta,
                    };
                    tpFpMetaData.TryAdd(holdTbId, fpMetaData);
                }
            }

            using (var step = new PrintMemoryUsage("tpClipLengthCollectDic"))
            {
                tpClipLengthCollectDic = new Dictionary<WeaponUniqueIndex, TpClipLengthCollect>();
                foreach (var map in tpCollection)
                {
                    var clipIndex = map.Key;
                    var meta = map.Value;
                    if(meta==null)
                        continue;
                    var clipDic = meta.NewClipInfos;
                    var tpClipLengthCollect = new TpClipLengthCollect();
                    if (McCommonUnity.Tables.TbChracterParameter.JumpStartUpSpeed>0)
                    {
                        tpClipLengthCollect.JumpStart.AniTime =  (McCommonUnity.Tables.TbChracterParameter.JumpStartUpSpeed*2)
                                                                 /(Mathf.Abs(McCommonUnity.Tables.TbChracterParameter.GravityCoef)* Mathf.Abs(Physics.gravity.y));
                    }
                    else
                    {
                        if (clipDic.TryGetValue("stand_jumpstart",PlayerLoader.CharacterConfig.AnimStrGroup, out var stand_jumpstart))
                        {
                            tpClipLengthCollect.JumpStart.AniTime = stand_jumpstart?.AnimationTime ?? 0;
                        }
                    }
                
                    if (clipDic.TryGetValue("stand_jumpend",PlayerLoader.CharacterConfig.AnimStrGroup, out var stand_jumpend))
                    {
                        tpClipLengthCollect.JumpEnd.AniTime = stand_jumpend?.AnimationTime ?? 0;
                    }
                    if (clipDic.TryGetValue("stand_hip_fireend",PlayerLoader.CharacterConfig.AnimStrGroup, out var stand_hip_fireend))
                    {
                        tpClipLengthCollect.Bolt.AniTime = stand_hip_fireend?.AnimationTime ?? 0;
                    }
                    tpClipLengthCollectDic.TryAdd(clipIndex, tpClipLengthCollect);
                }
            }

            var seatTb = McCommon.Tables.TbSeat;
            using (var step = new PrintMemoryUsage("tpVehicleSeatTbData"))
            {
                tpVehicleSeatTbData = new Dictionary<long, TpVehicleSeatTbData>();
                foreach (var map in seatTb.DataMap)
                {
                    var seatTbId = map.Key;
                    var seatTbData = map.Value;
                    var tpVehicleSeatData = new TpVehicleSeatTbData()
                    {
                        SeatTbIsOmnidirectional = seatTbData.IsOmnidirectional,
                        SeatTbFrontOverlapLeft = seatTbData.FrontOverlapLeft,
                        SeatTbFrontOverlapRight = seatTbData.FrontOverlapRight,
                        SeatTbBackOverlapLeft = seatTbData.BackOverlapLeft,
                        SeatTbBackOverlapRight = seatTbData.BackOverlapRight,
                        SeatTbFrontAnimOverlapLeft = seatTbData.FrontAnimOverlapLeft,
                        SeatTbFrontAnimOverlapRight = seatTbData.FrontAnimOverlapRight,
                        YawLeft = seatTbData.YawLeft,
                        YawRight = seatTbData.YawRight,
                        AnimYawLeft = seatTbData.AnimYawLeft,
                        AnimYawRight = seatTbData.AnimYawRight,
                        LowerSeatPoseType = seatTbData.LowerPoseType,
                    };
                    tpVehicleSeatTbData.TryAdd(seatTbId, tpVehicleSeatData);
                }
            }
            
            tpTipCurveIndex = new Dictionary<WeaponUniqueIndex, int>();
            TurnInPlaceYawnCurvesSize = tpCollection.Count * 4;

            using (var step = new PrintMemoryUsage("TurnInPlace"))
            {
                TurnInPlaceYawnCurves = (IntPtr)UnsafeUtility.Malloc(
                    UnsafeUtility.SizeOf<SocJobAnimationCurve>() * TurnInPlaceYawnCurvesSize,
                    UnsafeUtility.AlignOf<SocJobAnimationCurve>(), Allocator.Persistent);
                var tpTurnInPlaceSum = 0;
                foreach (var item in tpCollection)
                {
                    var key = item.Key;
                    var curSetting = item.Value;
                    var leftCurve = new SocJobAnimationCurve(curSetting.TurnInPlaceLeftCurve);
                    
                    var nowIndex = tpTurnInPlaceSum * 4 + 0;
                    UnsafeUtility.WriteArrayElement((void*)TurnInPlaceYawnCurves, nowIndex, leftCurve);
                    var rightCurve = new SocJobAnimationCurve(curSetting.TurnInPlaceRightCurve);
                    
                    nowIndex = tpTurnInPlaceSum * 4 + 1;
                    UnsafeUtility.WriteArrayElement((void*)TurnInPlaceYawnCurves, nowIndex, rightCurve);
                    var leftCrouchCurve = new SocJobAnimationCurve(curSetting.TurnInPlaceCrouchLeftCurve);
                    
                    nowIndex = tpTurnInPlaceSum * 4 + 2;
                    UnsafeUtility.WriteArrayElement((void*)TurnInPlaceYawnCurves, nowIndex, leftCrouchCurve);
                    var rightCrouchCurve = new SocJobAnimationCurve(curSetting.TurnInPlaceCrouchRightCurve);
                    
                    nowIndex = tpTurnInPlaceSum * 4 + 3;
                    UnsafeUtility.WriteArrayElement((void*)TurnInPlaceYawnCurves, nowIndex, rightCrouchCurve);
                    tpTipCurveIndex.Add(key, tpTurnInPlaceSum++);
                }
            }
            
            //缓存oc层所有武器的时长和loop
            LocomotionLayerSpecialArray = new NativeArray<bool>(tpCollectionCount * eLocomotionLayerSum, Allocator.Persistent);
            LocHeldItemSpecialIndex = new Dictionary<WeaponUniqueIndex, int>();
            var locHeldItemSpecialIndexSum = 0;
             foreach (var item in tpCollection)
            {
                var key = item.Key;
                var curSetting = item.Value;
                LocHeldItemSpecialIndex.Add(key, locHeldItemSpecialIndexSum);
                if (curSetting != null && curSetting.LocomotionSpecial != null)
                {
                    //数量对其index
                    for (int i = 0; i < eLocomotionLayerSum; i++)
                    {
                        if (curSetting.LocomotionSpecial.Contains((AnimParametersTp.ELocomotionLayer)i))
                        {
                            LocomotionLayerSpecialArray[locHeldItemSpecialIndexSum] = true;
                        }
                        else
                        {
                            LocomotionLayerSpecialArray[locHeldItemSpecialIndexSum] = false;
                        }
                        locHeldItemSpecialIndexSum++;
                    }
                }
                else
                {
                    //对不齐，自动填充0
                    for (int i = 0; i < eLocomotionLayerSum; i++)
                    {
                        LocomotionLayerSpecialArray[locHeldItemSpecialIndexSum] =false;
                        locHeldItemSpecialIndexSum++;
                    }
                }
            }

            UpdateLocomotionLayerWeightArray(tpCollection);
        }

        /// <summary>
        /// 刷新所有移动层权重缓存
        /// </summary>
        private void UpdateLocomotionLayerWeightArray(SerializableDictionary<WeaponUniqueIndex, TpClipSettingMeta> clipSettingMetas)
        {
            TpLocomotionLayerWeightIndexDic = new Dictionary<WeaponUniqueIndex, int>();
            var heldItemIndexSum = 0;
            var eLocomotionLayerSum = (int)AnimParametersTp.ELocomotionLayer.Sum;
            var tpCollectionCount = clipSettingMetas.Count;
            var sum = tpCollectionCount * eLocomotionLayerSum;
            using (var step = new PrintMemoryUsage("LocomotionLayerWeightArray"))
            {
                LocomotionLayerWeightArray =
                    new NativeArray<TpAniBoneMask>(sum, Allocator.Persistent);
                var nowIndex = 0;
                foreach (var item in clipSettingMetas)
                {
                    var key = item.Key;
                    var curSetting = item.Value;
                    TpLocomotionLayerWeightIndexDic.Add(key, heldItemIndexSum);
                    if (curSetting != null && curSetting.LocomotionLayerWeightCollection != null)
                    {
                        //数量对其index
                        for (int i = 0; i < eLocomotionLayerSum; i++)
                        {
                            var eType = (AnimParametersTp.ELocomotionLayer)i;
                            if (curSetting.LocomotionLayerWeightCollection.TryGetValue(eType, out var weight))
                            {
                                //存在
                                var tempTpAniBone = new TpAniBoneMask();
                                SetMaskWeight(ref tempTpAniBone, weight.WeightList);
                                LocomotionLayerWeightArray[nowIndex++] = tempTpAniBone;
                            }
                            else
                            {
                                //不存在
                                var tempTpAniBone = new TpAniBoneMask();
                                SetMaskWeight(ref tempTpAniBone, null, true);
                                LocomotionLayerWeightArray[nowIndex++] = tempTpAniBone;
                            }
                        }
                    }
                    else
                    {
                        //数量对其index
                        for (int i = 0; i < eLocomotionLayerSum; i++)
                        {
                            //不存在
                            var tempTpAniBone = new TpAniBoneMask();
                            SetMaskWeight(ref tempTpAniBone, null, true);
                            LocomotionLayerWeightArray[nowIndex++] = tempTpAniBone;
                        }
                    }
                    heldItemIndexSum++;
                }
            }
        }

        private void SetMaskWeight(ref TpAniBoneMask aniBoneMask, SkeletonMaskWeightValue[] weightDics, bool defaultValue = false)
        {
            aniBoneMask.BaseWeaponLocator = defaultValue?1:weightDics[(int)TpBoneNameConf.BaseWeaponLocator].Weight;
            aniBoneMask.Bip01 =defaultValue?1: weightDics[(int)TpBoneNameConf.Bip01].Weight;
            aniBoneMask.Bip01Head =defaultValue?1: weightDics[(int)TpBoneNameConf.Bip01Head].Weight; 
            aniBoneMask.Bip01LForearm = defaultValue?1:weightDics[(int)TpBoneNameConf.Bip01LForearm].Weight;
            aniBoneMask.Bip01LCalf =defaultValue?1: weightDics[(int)TpBoneNameConf.Bip01LCalf].Weight;
            aniBoneMask.Bip01LThigh = defaultValue?1:weightDics[(int)TpBoneNameConf.Bip01LThigh].Weight;
            aniBoneMask.Bip01LClavicle = defaultValue?1:weightDics[(int)TpBoneNameConf.Bip01LClavicle].Weight;
            aniBoneMask.Bip01LUpperArm =defaultValue?1: weightDics[(int)TpBoneNameConf.Bip01LUpperArm].Weight;
            aniBoneMask.Bip01LHand =defaultValue?1: weightDics[(int)TpBoneNameConf.Bip01LHand].Weight;
            aniBoneMask.Bip01Neck = defaultValue?1:weightDics[(int)TpBoneNameConf.Bip01Neck].Weight;
            aniBoneMask.Bip01Pelvis = defaultValue?1:weightDics[(int)TpBoneNameConf.Bip01Pelvis].Weight;
            aniBoneMask.Bip01RForearm = defaultValue?1:weightDics[(int)TpBoneNameConf.Bip01RForearm].Weight;
            aniBoneMask.Bip01RCalf = defaultValue?1:weightDics[(int)TpBoneNameConf.Bip01RCalf].Weight;
            aniBoneMask.Bip01RThigh =defaultValue?1: weightDics[(int)TpBoneNameConf.Bip01RThigh].Weight;
            aniBoneMask.Bip01RClavicle =defaultValue?1: weightDics[(int)TpBoneNameConf.Bip01RClavicle].Weight;
            aniBoneMask.Bip01RUpperArm =defaultValue?1: weightDics[(int)TpBoneNameConf.Bip01RUpperArm].Weight;
            aniBoneMask.Bip01RHand =defaultValue?1: weightDics[(int)TpBoneNameConf.Bip01RHand].Weight;
            aniBoneMask.Bip01Spine =defaultValue?1: weightDics[(int)TpBoneNameConf.Bip01Spine].Weight;
            aniBoneMask.Bip01Spine1 =defaultValue?1: weightDics[(int)TpBoneNameConf.Bip01Spine1].Weight;
            aniBoneMask.Bip01Spine2 = defaultValue?1:weightDics[(int)TpBoneNameConf.Bip01Spine2].Weight;
        }
        
        private void SetBoneWeightCurve(ref TpOcAniBoneCurveMask aniBoneMask, AnimCurveKey[] weightDics, bool defaultValue=false)
        {
            AnimCurveKey weight = weightDics == null ? default : weightDics[(int)TpBoneNameConf.Bip01];
            var flag = weight.isValid;
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01 =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01 = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }

            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.Bip01Pelvis];
            flag = weight.isValid;
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01Pelvis =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01Pelvis = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
            
            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.Bip01LThigh];
            flag = weight.isValid;
            
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01LThigh =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01LThigh = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
            
            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.Bip01LCalf];
            flag = weight.isValid;
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01LCalf =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01LCalf = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
            
            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.Bip01RThigh];
            flag = weight.isValid;
            
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01RThigh =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01RThigh = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
            
            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.Bip01RCalf];
            flag = weight.isValid;
            
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01RCalf =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01RCalf = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
            
            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.Bip01Spine];
            flag = weight.isValid;
            
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01Spine =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01Spine = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
            
            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.Bip01Spine1];
            flag = weight.isValid;
            
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01Spine1 =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01Spine1 = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
            
            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.Bip01Spine2];
            flag = weight.isValid;
            
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01Spine2 =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01Spine2 = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
            
            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.Bip01LClavicle];
            flag = weight.isValid;

            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01LClavicle =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01LClavicle = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
            
            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.Bip01Head];
            flag = weight.isValid;
            
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01Head =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01Head = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
            
            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.Bip01Neck];
            flag = weight.isValid;
            
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01Neck =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01Neck = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
            
            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.Bip01RClavicle];
            flag = weight.isValid;
            
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01RClavicle =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01RClavicle = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
            
            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.BaseWeaponLocator];
            flag = weight.isValid;
            
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.BaseWeaponLocator =defaultSocCurve;
            }
            else
            {
                aniBoneMask.BaseWeaponLocator = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
            
            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.Bip01LUpperArm];
            flag = weight.isValid;
            
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01LUpperArm =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01LUpperArm = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
            
            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.Bip01LForearm];
            flag = weight.isValid;
            
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01LForearm =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01LForearm = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
            
            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.Bip01LHand];
            flag = weight.isValid;
            
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01LHand =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01LHand = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
            
            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.Bip01RUpperArm];
            flag = weight.isValid;
            
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01RUpperArm =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01RUpperArm = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
            
            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.Bip01RForearm];
            flag = weight.isValid;
            
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01RForearm =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01RForearm = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
            
            weight = weightDics == null ? default :weightDics[(int)TpBoneNameConf.Bip01RHand];
            flag = weight.isValid;
            if (defaultValue || !flag)
            {
                var defaultSocCurve = new SocJobAnimationCurve(defaultCurve);
                aniBoneMask.Bip01RHand =defaultSocCurve;
            }
            else
            {
                aniBoneMask.Bip01RHand = new SocJobAnimationCurve(weight.Get(PlayerLoader.CharacterConfig.AnimCurves));
            }
        }

        public void OnUpdate(Context context, int deltaTime, IChangeWorld changeWorld)
        {
            if (clientContext == null)
                return;
            //获取主角
            if (myEntity == null)
            {
                if (LocalPlayerEntities.TryGetValue(Mc.MyPlayer.MyEntityId, out var myPlayerEntity))
                {
                    myEntity = myPlayerEntity;
                    //订阅属性变化
                    SubscribePropertyChange(myEntity);
                }
                else
                {
                    return;
                }
            }

#if UNITY_EDITOR
            var tpCollection = PlayerLoader.CharacterConfig.TpAniCollections.ClipSettingMetas;
            foreach (var item in tpCollection)
            {
                var key = item.Key;
                var curSetting = item.Value;
                if (curSetting.NeedUpdateWeight)
                {
                    curSetting.NeedUpdateWeight = false;
                    UpdateTpAniHeldItemData(myEntity);
                }
            }
            
            //刷新oc权重
            var needUpdateOcWeight = false;
            foreach (var tp in tpCollection)
            {
                var tpKey = tp.Key;
                var curSetting = tp.Value;
                if (curSetting.NeedUpdateOcWeight)
                {
                    curSetting.NeedUpdateOcWeight = false;
                    needUpdateOcWeight = true;
                    break;
                }
            }

            if (needUpdateOcWeight)
            {
                tpAniHeldItemDataIndex.Clear();
                unsafe
                {
                    if (OverrideWeightAnimationCurves != IntPtr.Zero)
                    {
                        // 1.转换为 TpAniOverrideLayerWeight*
                        TpAniOverrideLayerWeight* curvesPtr = (TpAniOverrideLayerWeight*)OverrideWeightAnimationCurves;
                        // 2. 递归调用 Dispose() 释放所有子内存（KeyFrames）
                        for (int i = 0; i < OverrideWeightAnimationCurvesSize; i++)
                        {
                            if (curvesPtr != null)
                            {
                                curvesPtr[i].Dispose(); // 调用 TpAniOverrideLayerWeight.Dispose()
                            }
                        }
                            
                        UnsafeUtility.Free((void*)OverrideWeightAnimationCurves, Allocator.Persistent);
                        OverrideWeightAnimationCurves = IntPtr.Zero;
                    }
                }

                var eOverrideLayerSum = (int)AnimParametersTp.EOverrideLayer.Sum;
                unsafe
                {
                    OverrideWeightAnimationCurvesSize = tpCollection.Count * eOverrideLayerSum;
                    OverrideWeightAnimationCurves = (IntPtr)UnsafeUtility.Malloc(
                        UnsafeUtility.SizeOf<TpAniOverrideLayerWeight>() * OverrideWeightAnimationCurvesSize,
                        UnsafeUtility.AlignOf<TpAniOverrideLayerWeight>(), Allocator.Persistent);
                }

                var tpAniHeldItemDataIndexSum = 0;
                foreach (var item in tpCollection)
                {
                    var key = item.Key;
                    var curSetting = item.Value;
                    for (var i = 0; i < eOverrideLayerSum; i++)
                    {
                        var tpAniOverrideLayerWeight = new TpAniOverrideLayerWeight();
                        bool hasData = false;
                        if (curSetting.OcLayerWeightCollection.TryGetValue((AnimParametersTp.EOverrideLayer)i,
                                out var collection))
                        {
                            if (collection != null)
                            {
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.IdleStandWeightCurve,
                                    collection.fastIdleStandWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.IdleCrouchWeightCurve,
                                    collection.fastIdleCrouchWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.JogStandWeightCurve,
                                    collection.fastJogStandWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.JogCrouchWeightCurve,
                                    collection.fastJogCrouchWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.SprintWeightCurve,
                                    collection.fastSprintWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.JumpWeightCurve,
                                    collection.fastJumpWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.SwimIdleWeightCurve,
                                    collection.fastSwimIdleWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.SwimJogWeightCurve,
                                    collection.fastSwimJogWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.SwimSprintWeightCurve,
                                    collection.fastSwimSprintWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.LadderWeightCurve,
                                    collection.fastLadderWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.HorseWeightCurve,
                                    collection.fastHorseWeightCurve);
                                SetBoneWeightCurve(ref tpAniOverrideLayerWeight.VehicleWeightCurve,
                                    collection.fastVehicleWeightCurve);
                                hasData = true;
                            }
                            else
                            {
                                logger.InfoFormat("配置的OcLayerWeightCollection {0} collection is null key =:{1}",
                                    (AnimParametersTp.EOverrideLayer)i, key);
                            }
                        }

                        if (!hasData)
                        {
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.IdleStandWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.IdleCrouchWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.JogStandWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.JogCrouchWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.SprintWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.JumpWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.SwimIdleWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.SwimJogWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.SwimSprintWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.LadderWeightCurve, null, true);
                            SetBoneWeightCurve(ref tpAniOverrideLayerWeight.HorseWeightCurve, null, true);
                        }
                        unsafe
                        {
                            var nowIndex = tpAniHeldItemDataIndexSum * eOverrideLayerSum + i;
                            UnsafeUtility.WriteArrayElement((void*)OverrideWeightAnimationCurves, nowIndex,
                                tpAniOverrideLayerWeight);
                        }
                    }
                    tpAniHeldItemDataIndex.Add(key, tpAniHeldItemDataIndexSum++);
                }

                UpdateTpAniHeldItemData(myEntity);
            }
            
            //编辑器下，动态更新匹配规则
            SpeedModule.RebuildMatchRules(ref MatchRules, PlayerLoader.CharacterConfig.TpMoveSpeedMatchRules,
                AnimParametersTp.floatOffsetMap, AnimParametersTp.intOffsetMap, AnimParametersTp.boolOffsetMap);
            //编辑器下，动态更新配置
            if (playerEntities != null && playerEntities.Count > 0)
            {
                foreach (var (id, entity) in playerEntities)
                {
                    for (int i = 0; i < (int)AnimMoveEnum.End; i++)
                    {
                        var em = (AnimMoveEnum)i;
                        var speedConf = PlayerLoader.GetTpMoveSpeedConf((int)entity.CurrentWeaponId,
                            (int)entity.CurrentWeaponSkinId, em);

                        if (entity.EntityId == myEntity.EntityId)
                        {
                            myEntity.TpAniPlayerStateData.MoveSpeedConfList.SetConf(em, speedConf);
                        }
                        else
                        {
                            entity.TpAniPlayerStateData.MoveSpeedConfList.SetConf(em, speedConf);
                        }
                    }
                }
            }
#endif

            // ProfilerApi.BeginSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_CacheLastData);
            // //缓存上一帧的数据
            // for (var i = 0; i < jobDataCount; i++)
            // {
            //     var tpAnimationResultJobData = clientContext.TpAnimationResultJobDataArray[i];
            //     var entityId = tpAnimationResultJobData.EntityId;
            //     if (myEntity.EntityId == entityId)
            //     {
            //         //主角
            //         TpAnimationResultJobData.CopyTo(tpAnimationResultJobData, myEntity);
            //         // logger.InfoFormat("job 结果， forward1：({0},{1},{2}),forward2:({3},{4},{5})",
            //         //     myEntity.TpAniPlayerLocalData.DebugPara1, myEntity.TpAniPlayerLocalData.DebugPara2, myEntity.TpAniPlayerLocalData.DebugPara3,
            //         //     myEntity.TpAniPlayerLocalData.DebugPara4, myEntity.TpAniPlayerLocalData.DebugPara5, myEntity.TpAniPlayerLocalData.DebugPara6);
            //     }
            //     else
            //     {
            //         if (playerEntities.TryGetValue(entityId, out var entity))
            //         {
            //             TpAnimationResultJobData.CopyTo(tpAnimationResultJobData, entity);
            //         }
            //     }
            // }
            //
            // ProfilerApi.EndSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_CacheLastData);

            ProfilerApi.BeginSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_DynamicsExpandNativeArrayCapacity);
            //动态扩容
            if (playerEntities != null && playerEntities.Count > 0)
            {
                //避免playerEntity中不存在主角
                DynamicsExpandNativeArrayCapacity(ref tpAnimationJobDataArray, playerEntities.Count + 1);
                DynamicsExpandNativeArrayCapacity(ref clientContext.TpAnimationResultJobDataArray, playerEntities.Count + 1);
            }

            ProfilerApi.EndSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_DynamicsExpandNativeArrayCapacity);

            ProfilerApi.BeginSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_UpdateSnapEvent);
            //收集快照事件
            UpdateSnapEvent(clientContext);
            ProfilerApi.EndSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_UpdateSnapEvent);

            //收集数据
            jobDataCount = 0;

            ProfilerApi.BeginSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_MyPlayerInitData);
            //主角
            var myAnimator_runtime = GetTpAnimator(myEntity);
            if (myAnimator_runtime != null)
            {
                var animator = myAnimator_runtime.GetAnimator();
                var runtimeCtrl = animator.runtimeAnimatorController;
                if (animator != null && runtimeCtrl != null)
                {
                    var myTpAnimationJobData = new TpAnimationJobData();
                    var myTpAnimationResultJobData = new TpAnimationResultJobData();
                    ProfilerApi.BeginSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_MyPlayerInitData_Jobdata);
             
                    //自己开启跳跃落地容差
                    myEntity.EnableJumpLandTolerant = true;
                    if (myEntity.NeedUpdateVehicleData)
                    {
                        myEntity.NeedUpdateVehicleData = false;
                        UpdateTpAniVehicleData(myEntity);
                    }
                    
                    var weaponItemEntity = myEntity.GetCurrentHandEntity<WeaponCustom>();
                    if (weaponItemEntity != null && weaponItemEntity.IsWarmupWeapon)
                    {
                        myEntity.TpAniPlayerStateData.warmupAnimType = weaponItemEntity.WarmupAnimState;
                        myEntity.TpAniPlayerStateData.warmupProgress = weaponItemEntity.WarmupProgress;
                    }


                    
                    myEntity.TpAniPlayerStateData.InSkyDiving = myEntity.InSkyDiving;
                    myEntity.TpAniPlayerStateData.IsDead = myEntity.HasDieFlag;
                    var mountGo = Mc.Go.GetGo(myEntity.MountableId);
                    if (mountGo != null && mountGo is ClientParachuteGo parachuteGo)
                    {
                        parachuteGo.ParachuteEntity.CalcBlend(ref myEntity.TpAniVehicleData.ParachuteRotateSpeed, ref myEntity.TpAniVehicleData.ParachuteMoveSpeed);
                        myEntity.TpAniVehicleData.ParachuteState = parachuteGo.ParachuteEntity.MoveState;
                    }
                    else
                    {
                        myEntity.TpAniVehicleData.ParachuteState = -1;
                    }
               
                    TpAnimationJobData.Init(animator, ref myTpAnimationJobData, tpVehicleSeatTbData, myEntity, myEntity);
                    ProfilerApi.EndSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_MyPlayerInitData_Jobdata);

                    ProfilerApi.BeginSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_MyPlayerInitData_ResultJobdata);
                    //初始化
                    var ctrl_id = runtimeCtrl.GetInstanceID();

                    //确保参数正常，当前切换状态机之后
                    // if (myAnimator_runtime.LastCtrlVersion != ctrl_id)
                    {
                        var flush_mem = Animator.GetFlushMemUnSafe(animator.GetInstanceID());
                        myEntity.TpParameters.CopyParams(ref flush_mem);
                        myAnimator_runtime.LastCtrlVersion = ctrl_id;
                    }

                    TpAnimationResultJobData.Init(ref myTpAnimationResultJobData, clientContext, myEntity, true, deltaTime);
                    CollectSnapEvent(myEntity);
                    ProfilerApi.EndSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_MyPlayerInitData_ResultJobdata);
                    if (myTpAnimationResultJobData.PlayerLocalData.LogicFrame ||
                        myTpAnimationResultJobData.PlayerLocalData.RenderFrame)
                    {
                        tpAnimationJobDataArray[jobDataCount] = myTpAnimationJobData;
                        clientContext.TpAnimationResultJobDataArray[jobDataCount] = myTpAnimationResultJobData;
                        ++jobDataCount;
                    }
                }
            }
            else
            {
                //死亡后得重置状态机
                myEntity.TpAniPlayerLocalData.UpdateIndex = 0;
            }

            ProfilerApi.EndSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_MyPlayerInitData);

            ProfilerApi.BeginSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_OtherPlayerInitData);
            foreach (var (id, entity) in playerEntities)
            {
                if (myEntity.EntityId == entity.EntityId)
                    continue;
                var animator_runtime = GetTpAnimator(entity);
                if (animator_runtime == null)
                {
                    entity.TpAniPlayerLocalData.UpdateIndex = 0;
                    continue;
                }
                var animator = animator_runtime.GetAnimator();
                if(animator==null)
                {
                    entity.TpAniPlayerLocalData.UpdateIndex = 0;
                    continue;
                }
                var runtimeCtrl = animator.runtimeAnimatorController;
                if (runtimeCtrl == null)
                {
                    entity.TpAniPlayerLocalData.UpdateIndex = 0;
                    continue;
                }
                var tpAnimationJobData = new TpAnimationJobData();
                var tpAnimationResultJobData = new TpAnimationResultJobData();
                ProfilerApi.BeginSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_OtherPlayerInitData_Jobdata);
                if (entity.NeedUpdateVehicleData)
                {
                    entity.NeedUpdateVehicleData = false;
                    UpdateTpAniVehicleData(entity);
                }
                
                var weaponItemEntity = entity.GetCurrentHandEntity<WeaponCustom>();
                if (weaponItemEntity != null && weaponItemEntity.IsWarmupWeapon)
                {
                    entity.TpAniPlayerStateData.warmupAnimType = weaponItemEntity.WarmupAnimState;
                    entity.TpAniPlayerStateData.warmupProgress = weaponItemEntity.WarmupProgress;
                }
                // var weight = "weight info: 1,w:"+entity.TpAniPlayerLocalData.DebugPara1+";2,w:"+entity.TpAniPlayerLocalData.DebugPara2+
                //              ";3,w:"+entity.TpAniPlayerLocalData.DebugPara3+";4,w:"+entity.TpAniPlayerLocalData.DebugPara4+
                //              ";5,w:"+entity.TpAniPlayerLocalData.DebugPara5+";6,w:"+entity.TpAniPlayerLocalData.DebugPara6+
                //              ";7,w:"+entity.TpAniPlayerLocalData.DebugPara7+";8,w:"+entity.TpAniPlayerLocalData.DebugPara8+
                //              ";9,w:"+entity.TpAniPlayerLocalData.DebugPara9+";10,w:"+entity.TpAniPlayerLocalData.DebugPara10+
                //              ";11,w:"+entity.TpAniPlayerLocalData.DebugPara11+";12,w:"+entity.TpAniPlayerLocalData.DebugPara12+
                //              ";13,w:"+entity.TpAniPlayerLocalData.DebugPara13+";14,w:"+entity.TpAniPlayerLocalData.DebugPara14+
                //              ";15,w:"+entity.TpAniPlayerLocalData.DebugPara15+";16,w:"+entity.TpAniPlayerLocalData.DebugPara16+
                //              ";17,w:"+entity.TpAniPlayerLocalData.DebugPara17+";18,w:"+entity.TpAniPlayerLocalData.DebugPara18+
                //              ";19,w:"+entity.TpAniPlayerLocalData.DebugPara19+";20,w:"+entity.TpAniPlayerLocalData.DebugPara20;
                //logger.InfoFormat("weight1: playerId:{0}, state:{1}, weight:{2}",entity.EntityId, entity.TpAniPlayerLocalData.EOverrideLayer, weight);
                TpAnimationJobData.Init(animator, ref tpAnimationJobData, tpVehicleSeatTbData, entity, myEntity);
                ProfilerApi.EndSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_OtherPlayerInitData_Jobdata);

                ProfilerApi.BeginSample(EProfileFunc
                    .AfterLoop_PlayerTpAnimatorSystem_OtherPlayerInitData_ResultJobdata);

                //初始化
                var ctrl_id = runtimeCtrl.GetInstanceID();

                //确保参数正常，当前切换状态机之后
                // 由于URO会将状态机更新降频，必须每帧都从状态机获取Params，否则可能导致状态机中的曲线数据被覆盖
                // if (animator_runtime.LastCtrlVersion != ctrl_id)
                {
                    var flush_mem = Animator.GetFlushMemUnSafe(animator.GetInstanceID());
                    entity.TpParameters.CopyParams(ref flush_mem);
                    animator_runtime.LastCtrlVersion = ctrl_id;
                }

                TpAnimationResultJobData.Init(ref tpAnimationResultJobData, clientContext, entity, false, deltaTime);
                CollectSnapEvent(myEntity);
                ProfilerApi.EndSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_OtherPlayerInitData_ResultJobdata);
                if (!tpAnimationResultJobData.PlayerLocalData.LogicFrame &&
                    !tpAnimationResultJobData.PlayerLocalData.RenderFrame)
                {
                    continue;
                }
                tpAnimationJobDataArray[jobDataCount] = tpAnimationJobData;
                clientContext.TpAnimationResultJobDataArray[jobDataCount] = tpAnimationResultJobData;
                ++jobDataCount;
            }

            ProfilerApi.EndSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_OtherPlayerInitData);
            clientContext.TpJobDataCount = jobDataCount;
            if (jobDataCount <= 0)
            {
                return;
            }
            
            ProfilerApi.BeginSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_TpAnimationJob);
            //执行job
            var tpAnimationJob = new TpAnimationJob()
            {
                tpAnimationJobDataArray = tpAnimationJobDataArray,
                tpAnimationResultJobDataArray = clientContext.TpAnimationResultJobDataArray,
                tpConstData = tpConstData,
                MatchRules = MatchRules,
                MaskArray = MaskArray,
                AoMaskArray = AoMaskArray,
                snapEventDataArray = snapEventDataArray,
                OverrideWeightAnimationCurves = OverrideWeightAnimationCurves,
                OverrideLayerTimeArray = OverrideLayerTimeArray,
                LocomotionLayerSpecialArray = LocomotionLayerSpecialArray,
                LocomotionLayerTimeArray = LocomotionLayerTimeArray,
                OverrideLayerStateLoopArray = OverrideLayerStateLoopArray,
                TurnInPlaceYawnCurves = TurnInPlaceYawnCurves,
                SnapEventCount = snapEventCount,
                LocomotionLayerWeightArray = LocomotionLayerWeightArray,
            };
            
#if !PUBLISH && !POCO
            //拷贝部分数据
            tpAnimationJob.DebugBones = DebugBones;
#endif

            tpAnimationJob.transitFromSnapshotPose = CombatConfig.transitFromSnapshotPose;
            tpAnimationJob.FrameNumber = Time.frameCount;
            tpAnimationJob.WalkSpeed = Mc.Tables.TbChracterParameter.StandWalkSpeed;
            tpAnimationJob.StandCCHeight = Mc.Tables.TbChracterParameter.CcHeight;
            tpAnimationJob.SelfEntityId = myEntity?.EntityId??-1;
            
            ProfilerApi.EndSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_TpAnimationJob);
            ProfilerApi.BeginSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_TpAnimationJob_Schedule);
            if (!GlobalOptimizationParameters.IsEnableAnimatorThreadSafeAPI())
            {
                GlobalOptimizationParameters.EnableAnimatorThreadSafeAPI();
            }

#if UNITY_EDITOR
            if (CombatConfig.AnimSpeedDbg)
            {
                //遍历更新
                for (int jobidx = 0; jobidx < jobDataCount; jobidx++)
                {
                    var jobdata = tpAnimationJobDataArray[jobidx];
                    var resultData = tpAnimationJob.tpAnimationResultJobDataArray[jobidx];
                    for (int speedIdx = 0; speedIdx < (int)AnimMoveEnum.End; speedIdx++)
                    {
                        var speedConf = jobdata.PlayerStateData.MoveSpeedConfList.GetConf((AnimMoveEnum)speedIdx);
                        ref var speedCache = ref AnimMoveSpeedLocalCacheGroup.GetCache(ref  resultData.PlayerLocalData.MoveSpeedLocalCaches, (AnimMoveEnum)speedIdx);

                        bool canPrint = speedCache.Running || speedCache.Exiting || speedCache.Entering;
                        if (!canPrint)
                        {
                            continue;
                        }
                        logger.InfoFormat("entityId:{0},AnimMoveEnum:{1} Running:{2},Entering:{3},Exiting:{4}",
                            jobdata.EntityId, (AnimMoveEnum)speedIdx, speedCache.Running, speedCache.Entering, speedCache.Exiting);

                        var flush_mem = Animator.GetFlushMemUnSafe(jobdata.AnimatorInstanceId);
                        var applyRuleId = speedConf.ApplyRuleId;
                        if (applyRuleId >= 0 && applyRuleId < MatchRules.Length)
                        {
                            var ruleArr = MatchRules[applyRuleId];
                            var ruleCnt = ruleArr.Count;

                            for (int ruleIdx = 0; ruleIdx < ruleCnt; ruleIdx++)
                            {
                                ref var rule = ref ruleArr.Get(ruleIdx);
                                var animtp = new AnimParametersTp();
                                SpeedModule.Print(rule, flush_mem,animtp);
                            }
                        }
                    }
                }
            }
#endif
            clientContext.TpAnimatorJobHandle = tpAnimationJob.Schedule(jobDataCount, 1);
            //clientContext.TpAnimatorJobHandle.Complete();
            ProfilerApi.EndSample(EProfileFunc.AfterLoop_PlayerTpAnimatorSystem_TpAnimationJob_Schedule);
        }

        /// <summary>
        /// 动态扩容
        /// </summary>
        /// <param name="dataArray"></param>
        /// <param name="count"></param>
        /// <typeparam name="T"></typeparam>
        public void DynamicsExpandNativeArrayCapacity<T>(ref NativeArray<T> dataArray, int count) where T : struct
        {
            if (dataArray.Length > count)
            {
                return;
            }

            var tempDataArray = new NativeArray<T>(dataArray.Length * 2, Allocator.Persistent);
            NativeArray<T>.Copy(dataArray, tempDataArray, dataArray.Length);
            dataArray.Dispose();
            dataArray = tempDataArray;
        }

        public void OnDestroy()
        {
            MatchRules.Dispose();

            DebugBones.Dispose();

            tpAnimationJobDataArray.Dispose();
            clientContext.TpAnimationResultJobDataArray.Dispose();
            MaskArray.Dispose();
            AoMaskArray.Dispose();

            snapEventDataArray.Dispose();
            // foreach (var (key, value) in fpClipMetaDataDict)
            // {
            //     value.Dispose();
            // }
            tpFpMetaData.Clear();
            tpClipLengthCollectDic.Clear();
            tpVehicleSeatTbData.Clear();
            LocalPlayerEntities.Clear();
            playerEntities.Clear();

            Mc.Entity.OnFullEntityCreate -= OnCreateEntity;
            Mc.Entity.OnFullEntityDowngrade -= OnEntityDowngrade;
            
            OverrideLayerTimeArray.Dispose();
            OverrideLayerStateLoopArray.Dispose();
            OcHeldItemTimeIndex.Clear();
            LocomotionLayerTimeArray.Dispose();
            LocHeldItemTimeIndex.Clear();
            LocomotionLayerSpecialArray.Dispose();
            LocHeldItemSpecialIndex.Clear();
            
            clientContext.Clear();
            clientContext = null;

            foreach (var item in SnapEventDataDic)
            {
                var itemV = item.Value;
                Pool.ReleaseList(itemV);
            }
            SnapEventDataDic.Clear();

            unsafe
            {
                if (OverrideWeightAnimationCurves != IntPtr.Zero)
                {
                    // 1.转换为 TpAniOverrideLayerWeight*
                    TpAniOverrideLayerWeight* curvesPtr = (TpAniOverrideLayerWeight*)OverrideWeightAnimationCurves;
                    // 2. 递归调用 Dispose() 释放所有子内存（KeyFrames）
                    for (int i = 0; i < OverrideWeightAnimationCurvesSize; i++)
                    {
                        if (curvesPtr != null)
                        {
                            curvesPtr[i].Dispose(); // 调用 TpAniOverrideLayerWeight.Dispose()
                        }
                    }

                    UnsafeUtility.Free((void*)OverrideWeightAnimationCurves, Allocator.Persistent);
                    OverrideWeightAnimationCurves = IntPtr.Zero;
                }
            }
            
            tpTipCurveIndex.Clear();
            unsafe
            {
                if (TurnInPlaceYawnCurves != IntPtr.Zero)
                {
                    SocJobAnimationCurve* curvesPtr = (SocJobAnimationCurve*)TurnInPlaceYawnCurves;
                    // 2. 递归调用 Dispose() 释放所有子内存（KeyFrames）
                    for (int i = 0; i < TurnInPlaceYawnCurvesSize; i++)
                    {
                        if (curvesPtr != null)
                        {
                            curvesPtr[i].Dispose(); // 调用 TpAniOverrideLayerWeight.Dispose()
                        }
                    }

                    UnsafeUtility.Free((void*)TurnInPlaceYawnCurves, Allocator.Persistent);
                    TurnInPlaceYawnCurves = IntPtr.Zero;
                }
            }
            
            TpLocomotionLayerWeightIndexDic.Clear();
            LocomotionLayerWeightArray.Dispose();
        }

        private SocAnimatorRuntime GetTpAnimator(PlayerEntity entity)
        {
            var playerAnimatorRuntime = SocAnimationManager.Get(entity.EntityId) as SocAnimatorRuntime;
            if (playerAnimatorRuntime == null || !playerAnimatorRuntime.HasInit())
            {
                //logger.Info("animator is null or not init");
                return null;
            }

            return playerAnimatorRuntime;
        }

        private void UpdateSnapEvent(ClientContext context)
        {
            snapEventCount = 0;
            UpdateDrawEvent(context);
            UpdateQuickDrawEvent(context);
            UpdateFireEvent(context);
            UpdateOpenDoorEvent(context);
            UpdatePickUpEvent(context);
        }
        
        /// <summary>
        /// 当帧job收集快照事件
        /// </summary>
        /// <param name="context"></param>
        private void CollectSnapEvent(PlayerEntity entity)
        {
            if(!entity.TpAniPlayerLocalData.LogicFrame)
                return;
            if(!SnapEventDataDic.TryGetValue(entity.EntityId, out var snapEventData))
                return;
            foreach (var item in snapEventData)
            {
                DynamicsExpandNativeArrayCapacity(ref snapEventDataArray, snapEventCount + 1);
                snapEventDataArray[snapEventCount++] = item;
            }
            Pool.ReleaseList(snapEventData);
            SnapEventDataDic.Remove(entity.EntityId);
        }

        /// <summary>
        /// 创建快照data，并加入array
        /// </summary>
        /// <param name="snapshotEvent"></param>
        /// <param name="snapEvent"></param>
        private void MakeSnapEvent(AbstractLocationBasedEvent snapshotEvent, ESnapEvent snapEvent)
        {
            var entityId = snapshotEvent.SourceId;
            var isMaster = Mc.MyPlayer.MyEntityId == entityId;
            if (isMaster && snapshotEvent.bFromServer)
            { 
                return;
            }
            
            if (!SnapEventDataDic.TryGetValue(entityId, out var snapEventDataList))
            {
                snapEventDataList = Pool.GetList<SnapEventData>();
                SnapEventDataDic.Add(entityId, snapEventDataList);
            }

            var snapEventData = new SnapEventData()
            {
                bFromServer = snapshotEvent.bFromServer, SourceId = snapshotEvent.SourceId, SnapEvent = snapEvent,
            };
            snapEventDataList.Add(snapEventData);

            // DynamicsExpandNativeArrayCapacity(ref snapEventDataArray, snapEventCount + 1);
            // snapEventDataArray[snapEventCount++] = snapEventData;
        }

        private void MakeSnapEvent(FireDataEvent snapshotEvent, ESnapEvent snapEvent)
        {
            var entityId = snapshotEvent.SourceId;
            var isMaster = Mc.MyPlayer.MyEntityId == entityId;
            if (isMaster && snapshotEvent.bFromServer)
            { 
                return;
            }
            
            if (!SnapEventDataDic.TryGetValue(entityId, out var snapEventDataList))
            {
                snapEventDataList = Pool.GetList<SnapEventData>();
                SnapEventDataDic.Add(entityId, snapEventDataList);
            }
            
            var snapEventData = new SnapEventData()
            {
                bFromServer = snapshotEvent.bFromServer,
                SourceId = snapshotEvent.SourceId,
                SnapEvent = snapEvent,
                Param1 = snapshotEvent.FireEvent,
            };
            snapEventDataList.Add(snapEventData);
            // DynamicsExpandNativeArrayCapacity(ref snapEventDataArray, snapEventCount + 1);
            // snapEventDataArray[snapEventCount++] = snapEventData;
        }

        /// <summary>
        /// 切枪事件
        /// </summary>
        /// <param name="context"></param>
        private void UpdateDrawEvent(ClientContext context)
        {
            var tpDataSet = McCommon.SystemRequestMgr.BindRequestQueue(ESystemRequest.ClientSwitchEventTpDataSet);
            while (tpDataSet.HasData())
            {
                var pooledObject = tpDataSet.Dequeue();
                var switch_event = pooledObject as ClientSwitchEventTp;
                // 生成事件
                MakeSnapEvent(switch_event, ESnapEvent.Draw);
                MidApi.Free(pooledObject.Mid);
            }
        }

        private void UpdateQuickDrawEvent(ClientContext context)
        {
            if (quickDrawEventDelegate == null)
                quickDrawEventDelegate = MakeSnapEvent;
            //检查是否有拾取事件，尝试触发拾取事件
            McCommon.LocationBasedEvent.IteratorAllEvents<QuickDrawEvent, ESnapEvent>(QuickDrawEvent.StaticClassHash,
                quickDrawEventDelegate, ESnapEvent.QuickDraw);
        }

        private void UpdateFireEvent(ClientContext context)
        {
            if (fireEventDelegate == null)
                fireEventDelegate = MakeSnapEvent;
            //检查是否有拾取事件，尝试触发拾取事件
            McCommon.LocationBasedEvent.IteratorAllEvents<FireDataEvent, ESnapEvent>(FireDataEvent.StaticClassHash,
                fireEventDelegate, ESnapEvent.Fire);
        }

        private void UpdateOpenDoorEvent(ClientContext context)
        {
            if (openDoorEventDelegate == null)
                openDoorEventDelegate = MakeSnapEvent;
            //检查是否有拾取事件，尝试触发拾取事件
            McCommon.LocationBasedEvent.IteratorAllEvents<OpenDoorEvent, ESnapEvent>(OpenDoorEvent.StaticClassHash,
                openDoorEventDelegate, ESnapEvent.OpenDoor);
        }

        private void UpdatePickUpEvent(ClientContext context)
        {
            if (pickUpEventDelegate == null)
                pickUpEventDelegate = MakeSnapEvent;
            //检查是否有拾取事件，尝试触发拾取事件
            McCommon.LocationBasedEvent.IteratorAllEvents<PickUpEvent, ESnapEvent>(PickUpEvent.StaticClassHash,
                pickUpEventDelegate, ESnapEvent.PickUp);
        }

        /// <summary>
        /// 玩家属性变化的监听
        /// </summary>
        /// <param name="entity"></param>
        private void OnCreateEntity(IEntity entity)
        {
            if (entity.EntityType == EntityTypeId.PlayerEntity)
            {
                var playerEntity = (PlayerEntity)entity;
                var mySelf = Mc.MyPlayer.MyEntityId == playerEntity.EntityId;
                SubscribePropertyChange(playerEntity);
            }
        }

        private void OnEntityDowngrade(IEntity entity)
        {
            if (entity.EntityType == EntityTypeId.PlayerEntity)
            {
                var playerEntity = (PlayerEntity)entity;
                UnSubscribePropertyChange(playerEntity);
            }
        }
        
        void OnTransChange(long entityId)
        {
            var entity = Mc.Entity.GetEntity(entityId) as PartEntity;
            if (entity == null)
            {
                return;
            }

            //处理自己
            if (Mc.MyPlayer != null && Mc.MyPlayer.MyEntityLocal.LadderTargetId == entityId)
            {
                UpdateLadder(Mc.MyPlayer.MyEntityLocal);
            }
            
            //处理其他玩家，都要处理，可能自己和别人都在同一梯子上
            if (playerEntities != null)
            {
                foreach (var (id, player) in playerEntities)
                {
                    if (player.LadderTargetId == entityId)
                    {
                        UpdateLadder(player);
                        break;
                    }
                }
            }
        }

        private void SubscribePropertyChange(PlayerEntity entity)
        {
            //保证第一次有初始的所有数值
            InitPlayerJobData(entity);
            // logicStateSubscribeId = entity.SubscribePropertyChange<long>(PlayerEntity.PropertyIds.LOGIC_STATE, LogicStateChangeCallBack);
            #region 状态变化监听
            UnaliveStateSubscribeId = entity.SubscribeByteEnumFieldChange<PlayerUnAliveStateEnum>(PlayerEntity.PropertyIds.UN_ALIVE_STATE,PlayerUnaliveStateChange);
            MoveStateSubscribeId = entity.SubscribeByteEnumFieldChange<PlayerMoveStateEnum>(PlayerEntity.PropertyIds.MOVE_STATE,PlayerMoveStateChange);
            MoveJumpStateSubscribeId = entity.SubscribePropertyChange<byte>(PlayerEntity.PropertyIds.MOVE_JUMP_STATE,PlayerMoveJumpStateChange);
            MoveLadderStateSubscribeId = entity.SubscribePropertyChange<byte>(PlayerEntity.PropertyIds.MOVE_LADDER_STATE,PlayerMoveLadderStateChange);
            MoveSwimStateSubscribeId = entity.SubscribePropertyChange<byte>(PlayerEntity.PropertyIds.MOVE_SWIM_STATE,PlayerMoveSwimStateChange);
            MoveZiplineSubscribeId = entity.SubscribePropertyChange<byte>(PlayerEntity.PropertyIds.MOVE_ZIPLINE_STATE,PlayerMoveZiplineStateChange);
            PoseStateSubscribeId = entity.SubscribePropertyChange<byte>(PlayerEntity.PropertyIds.POSE_STATE,PlayerPoseStateChange);
            PoseDyingStateSubscribeId = entity.SubscribePropertyChange<byte>(PlayerEntity.PropertyIds.POSE_DYING_STATE,PlayerPoseDyingStateChange);
            ActionStateSubscribeId = entity.SubscribePropertyChange<byte>(PlayerEntity.PropertyIds.ACTION_STATE,PlayerActionStateChange);
            AttackSubStateSubscribeId = entity.SubscribePropertyChange<byte>(PlayerEntity.PropertyIds.ATTACK_SUB_STATE,PlayerAttackSubStateChange);
            ReloadStateSubscribeId = entity.SubscribePropertyChange<byte>(PlayerEntity.PropertyIds.RELOAD_STATE,PlayerReloadStateChange);
            BowStateSubscribeId = entity.SubscribePropertyChange<byte>(PlayerEntity.PropertyIds.BOW_STATE,PlayerBowStateChange);
            ThrowStateSubscribeId = entity.SubscribePropertyChange<byte>(PlayerEntity.PropertyIds.THROW_STATE,PlayerThrowStateChange);
            AdsStateSubscribeId = entity.SubscribePropertyChange<byte>(PlayerEntity.PropertyIds.ADS_STATE,PlayerAdsStateChange);
            #endregion
            gestureSubscribeId = entity.SubscribePropertyChange<int>(PlayerEntity.PropertyIds.GESTURE_INDEX, GestureChangeCallBack);
            ladderTargetIdSubscribeId = entity.SubscribePropertyChange<long>(PlayerEntity.PropertyIds.LADDER_TARGET_ID, LadderTargetIdChangeCallBack);
            ladderAbsorbTargetIdSubscribeId = entity.SubscribePropertyChange<long>(PlayerEntity.PropertyIds.LADDER_ADSORB_TARGET_INDEX, LadderAbsorbTargetIdChangeCallBack);
            ladderEnterDirSubscribeId = entity.SubscribePropertyChange<int>(PlayerEntity.PropertyIds.LADDER_ENTER_DIR, LadderEnterDirChangeCallBack);
            ladderLeaveDirSubscribeId = entity.SubscribePropertyChange<int>(PlayerEntity.PropertyIds.LADDER_LEAVE_DIR, LadderLeaveDirChangeCallBack);
            ladderMoveFlagSubscribeId = entity.SubscribePropertyChange<int>(PlayerEntity.PropertyIds.LADDER_MOVE_FLAG, LadderMoveFlagChangeCallBack);
            
            mountableIdSubscribeId =
                entity.SubscribePropertyChange<long>(PlayerEntity.PropertyIds.MOUNTABLE_ID, MountableIdChangeCallBack);
            horseMountDirSubscribeId =
                entity.SubscribePropertyChange<int>(PlayerEntity.PropertyIds.HORSE_MOUNT_DIR,
                    HorseMountDirChangeCallBack);
            vehicleTypeSubscribeId =
                entity.SubscribePropertyChange<int>(PlayerEntity.PropertyIds.VEHICLE_TYPE, VehicleTypeChangeCallBack);
            mountableTypeSubscribeId =
                entity.SubscribePropertyChange<int>(PlayerEntity.PropertyIds.MOUNTABLE_TYPE, MountableTypeChangeCallBack);
            isDriverSubscribeId =
                entity.SubscribePropertyChange<bool>(PlayerEntity.PropertyIds.IS_DRIVER, IsDriverChangeCallBack);
            rotateYSubscribeId =
                entity.AddEvent<EntityPositionChangeEvent>(RotateYChangeCallBack);
            cmdYawSubscribeId =
                entity.SubscribePropertyChange<float>(PlayerEntity.PropertyIds.VIEW_YAW, CmdYawChangeCallBack);
            adsOffsetProgressSubscribeId =
                entity.SubscribePropertyChange<float>(PlayerEntity.PropertyIds.ADS_OFFSET_PROGRESS,
                    AdsOffsetProgressChangeCallBack);
            currentWeaponIdSubscribeId = entity.SubscribePropertyChange<long>(PlayerEntity.PropertyIds.CURRENT_WEAPON_ID,
                CurrentWeaponIdChangeCallBack);
            movement8DirectionSubscribeId =
                entity.SubscribePropertyChange<int>(PlayerEntity.PropertyIds.MOVEMENT8_DIRECTION,
                    Movement8DirectionChangeCallBack);
            movement4DirectionSubscribeId =
                entity.SubscribePropertyChange<int>(PlayerEntity.PropertyIds.MOVEMENT4_DIRECTION,
                    Movement4DirectionChangeCallBack);
            HeightSubscribeId = entity.SubscribePropertyChange<float>(PlayerEntity.PropertyIds.HEIGHT, HeightChangeCallBack);
            rootMotionWarpingIndexSubscribeId = entity.SubscribePropertyChange<int>(
                PlayerEntity.PropertyIds.ROOT_MOTION_WARPING_INDEX, RootMotionWarpingIndexChangeCallBack);
            inputSprintSubscribeId =
                entity.SubscribePropertyChange<bool>(PlayerEntity.PropertyIds.INPUT_SPRINT, InputSprintChangeCallBack);
            speedXSubscribeId =
                entity.SubscribePropertyChange<float>(PlayerEntity.PropertyIds.SPEED_X, SpeedXChangeCallBack);
            speedZSubscribeId =
                entity.SubscribePropertyChange<float>(PlayerEntity.PropertyIds.SPEED_Z, SpeedZChangeCallBack);
            speedYSubscribeId =
                entity.SubscribePropertyChange<float>(PlayerEntity.PropertyIds.SPEED_Y, SpeedYChangeCallBack);
            stateRecoveryReasonSubscribeId =
                entity.SubscribePropertyChange<int>(PlayerEntity.PropertyIds.STATE_RECOVERY_REASON,
                    StateRecoveryReasonChangeCallBack);
            tryFireSubscribeId =
                entity.SubscribePropertyChange<int>(PlayerEntity.PropertyIds.ACTION_STATE_TRIGGER, TryFireChangeCallBack);
            // usingForOtherSubscribeId = entity.SubscribePropertyChange<bool>(PlayerEntity.PropertyIds.USING_FOR_OTHER,
            //     UsingForOtherChangeCallBack);
            cmdPitchSubscribeId =
                entity.SubscribePropertyChange<float>(PlayerEntity.PropertyIds.VIEW_PITCH, CmdPitchChangeCallBack);
            useAnimIndexSubscribeId =
                entity.SubscribePropertyChange<byte>(PlayerEntity.PropertyIds.USE_ANIM_INDEX, UseAnimIndexChangeCallBack);
            item8SubscribeId = entity.SubscribePropertyChange(PlayerEntity.PropertyIds.CUSTOM_ITEM8, Item8ChangeCallBack);
            mountSeatIndexSubscribeId = entity.SubscribePropertyChange<int>(PlayerEntity.PropertyIds.MOUNT_SEAT_INDEX,
                MountSeatIndexChangeCallBack);

            attachHitMatIndexSubscribeId = entity.SubscribePropertyChange<int>(PlayerEntity.PropertyIds.ATTACK_HIT_MAT,
                AttackHitMatChangeCallBack);
            useHitIndexSubscribeId = entity.SubscribePropertyChange<byte>(PlayerEntity.PropertyIds.USE_HIT_INDEX,
                UseHitIndexChangeCallBack);
            interactiveIdSubscribeId = entity.SubscribePropertyChange<int>(PlayerEntity.PropertyIds.INTERACTIVE_ID,
                InteractiveIdChangeCallBack);
        }

        private void HeightChangeCallBack(CustomTypeBase entity, float oldValue, float newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.CCHeight = newValue;
        }

        private void UnSubscribePropertyChange(PlayerEntity entity)
        {
            Mc.Msg.RemoveListener<long>(EventDefine.OnLadderTransformChange,OnTransChange);
            // entity.UnSubscribePropertyChange(logicStateSubscribeId);
            entity.UnSubscribePropertyChange(UnaliveStateSubscribeId);
            entity.UnSubscribePropertyChange(MoveStateSubscribeId);
            entity.UnSubscribePropertyChange(MoveJumpStateSubscribeId);
            entity.UnSubscribePropertyChange(MoveLadderStateSubscribeId);
            entity.UnSubscribePropertyChange(MoveSwimStateSubscribeId);
            entity.UnSubscribePropertyChange(MoveZiplineSubscribeId);
            entity.UnSubscribePropertyChange(PoseStateSubscribeId);
            entity.UnSubscribePropertyChange(PoseDyingStateSubscribeId);
            entity.UnSubscribePropertyChange(ActionStateSubscribeId);
            entity.UnSubscribePropertyChange(AttackSubStateSubscribeId);
            entity.UnSubscribePropertyChange(ReloadStateSubscribeId);
            entity.UnSubscribePropertyChange(BowStateSubscribeId);
            entity.UnSubscribePropertyChange(ThrowStateSubscribeId);
            entity.UnSubscribePropertyChange(ActionHoldStateSubscribeId);
            entity.UnSubscribePropertyChange(AdsStateSubscribeId);

            entity.UnSubscribePropertyChange(gestureSubscribeId);
            entity.UnSubscribePropertyChange(ladderTargetIdSubscribeId);
            entity.UnSubscribePropertyChange(ladderAbsorbTargetIdSubscribeId);
            entity.UnSubscribePropertyChange(ladderEnterDirSubscribeId);
            entity.UnSubscribePropertyChange(ladderLeaveDirSubscribeId);
            entity.UnSubscribePropertyChange(ladderMoveFlagSubscribeId);

            entity.UnSubscribePropertyChange(mountableIdSubscribeId);
            entity.UnSubscribePropertyChange(horseMountDirSubscribeId);
            entity.UnSubscribePropertyChange(vehicleTypeSubscribeId);
            entity.UnSubscribePropertyChange(mountableTypeSubscribeId);
            entity.UnSubscribePropertyChange(isDriverSubscribeId);
            entity.SafeRemoveEvent(rotateYSubscribeId);
            entity.UnSubscribePropertyChange(cmdYawSubscribeId);
            entity.UnSubscribePropertyChange(adsOffsetProgressSubscribeId);
            entity.UnSubscribePropertyChange(currentWeaponIdSubscribeId);
            entity.UnSubscribePropertyChange(movement8DirectionSubscribeId);
            entity.UnSubscribePropertyChange(movement4DirectionSubscribeId);
            entity.UnSubscribePropertyChange(rootMotionWarpingIndexSubscribeId);
            entity.UnSubscribePropertyChange(inputSprintSubscribeId);
            entity.UnSubscribePropertyChange(speedXSubscribeId);
            entity.UnSubscribePropertyChange(speedZSubscribeId);
            entity.UnSubscribePropertyChange(speedYSubscribeId);
            entity.UnSubscribePropertyChange(stateRecoveryReasonSubscribeId);
            entity.UnSubscribePropertyChange(tryFireSubscribeId);
            entity.UnSubscribePropertyChange(cmdPitchSubscribeId);
            entity.UnSubscribePropertyChange(useAnimIndexSubscribeId);
            entity.UnSubscribePropertyChange(item8SubscribeId);
            entity.UnSubscribePropertyChange(mountSeatIndexSubscribeId);
            entity.UnSubscribePropertyChange(attachHitMatIndexSubscribeId);
            entity.UnSubscribePropertyChange(useHitIndexSubscribeId);
            entity.UnSubscribePropertyChange(HeightSubscribeId);
            entity.UnSubscribePropertyChange(interactiveIdSubscribeId);
        }

        /// <summary>
        /// 第一次初始化玩家数据
        /// </summary>
        private void InitPlayerJobData(PlayerEntity entity)
        {
            InitTpAniPlayerStateData(entity);
            UpdateTpAniHeldItemData(entity);
            UpdateTpAniHeldItemDataItem8(entity);
            UpdateTpAniVehicleData(entity);
        }

        private void InitTpAniPlayerStateData(PlayerEntity entity)
        {
            entity.TpAniPlayerStateData.MountableId = entity.MountableId;
            entity.TpAniPlayerStateData.VehicleType = entity.VehicleType;
            entity.TpAniPlayerStateData.MountableType = entity.MountableType;
            entity.TpAniPlayerStateData.IsDriver = entity.IsDriver;
            entity.TpAniPlayerStateData.PoseState = entity.PoseState;
            entity.TpAniPlayerStateData.MoveState = entity.MoveState;
            entity.TpAniPlayerStateData.RotateY = entity.RotateY;
            entity.TpAniPlayerStateData.ViewYaw = entity.ViewYaw;
            entity.TpAniPlayerStateData.CCHeight = entity.CcHeight;
            entity.TpAniPlayerStateData.LastViewYaw = entity.ViewYaw;
            entity.TpAniPlayerStateData.AdsOffsetProgress = entity.AdsOffsetProgress;
            entity.TpAniPlayerStateData.UnAliveState = entity.UnAliveState;
            // entity.TpAniPlayerStateData.Horizontal = entity.Horizontal;
            // entity.TpAniPlayerStateData.Vertical = entity.Vertical;
            // entity.TpAniPlayerStateData.Slope = entity.Slope;
            entity.TpAniPlayerStateData.CurrentWeaponId = entity.CurrentWeaponId;
            entity.TpAniPlayerStateData.Movement8Direction = entity.Movement8Direction;
            entity.TpAniPlayerStateData.Movement4Direction = entity.Movement4Direction;
            entity.TpAniPlayerStateData.MoveLadderState = entity.MoveLadderState;
            entity.TpAniPlayerStateData.RootMotionWarpingIndex = entity.RootMotionWarpingIndex;
            entity.TpAniPlayerStateData.MoveJumpState = entity.MoveJumpState;
            entity.TpAniPlayerStateData.MoveSwimState = entity.MoveSwimState;
            entity.TpAniPlayerStateData.InputSprint = entity.InputSprint;
            entity.TpAniPlayerStateData.SpeedX = entity.SpeedX;
            entity.TpAniPlayerStateData.SpeedZ = entity.SpeedZ;
            entity.TpAniPlayerStateData.SpeedY = entity.SpeedY;
            entity.TpAniPlayerStateData.ActionState = entity.ActionState;
            // entity.TpAniPlayerStateData.GestureIndex = entity.GestureIndex;
            entity.TpAniPlayerStateData.PlayerEntityId = entity.EntityId;
            entity.TpAniPlayerStateData.StateRecoveryReason = entity.StateRecoveryReason;
            entity.TpAniPlayerStateData.ReloadState = entity.ReloadState;
            entity.TpAniPlayerStateData.AttackSubState = entity.AttackSubState;
            entity.TpAniPlayerStateData.TryFire = entity.IsActionTriggerOn(ActionTriggerBlock.EokaPistolTryFire);
            // entity.TpAniPlayerStateData.ItemFireState = entity.ItemFireState;
            entity.TpAniPlayerStateData.ThrowState = entity.ThrowState;
            // entity.TpAniPlayerStateData.UsingForOther = entity.UsingForOther;
            entity.TpAniPlayerStateData.BowState = entity.BowState;
            entity.TpAniPlayerStateData.AdsState = entity.AdsState;
            entity.TpAniPlayerStateData.PoseDyingState = entity.PoseDyingState;
            entity.TpAniPlayerStateData.MoveZiplineState = entity.MoveZiplineState;
            entity.TpAniPlayerStateData.CmdPitch = entity.ViewPitch;
            entity.TpAniPlayerStateData.AttackHitMat = entity.AttackHitMat;
            entity.TpAniPlayerStateData.UseHitIndex = entity.UseHitIndex;

            entity.TpAniPlayerStateData.InSkyDiving = entity.InSkyDiving;
            entity.TpAniPlayerStateData.IsDead = entity.HasDieFlag;
            LadderEnterDirChangeCallBack(entity,  (int)entity.TpAniPlayerStateData.LadderEnterDir, entity.LadderEnterDir);
            LadderLeaveDirChangeCallBack(entity, (int)entity.TpAniPlayerStateData.LadderExitDir, entity.LadderLeaveDir);
            UpdateLadder(entity);
            
            var weaponItemEntity = entity.GetCurrentHandEntity<WeaponCustom>();
            if (weaponItemEntity != null && weaponItemEntity.IsWarmupWeapon)
            {
                entity.TpAniPlayerStateData.warmupAnimType = weaponItemEntity.WarmupAnimState;
                entity.TpAniPlayerStateData.warmupProgress = weaponItemEntity.WarmupProgress;
            }
            entity.TpAniPlayerStateData.InteractiveStateTime = 0f;
        }

        private void UpdateTpAniHeldItemData(PlayerEntity entity)
        {
            //这里不调用接口，是因为主角这个时候，本地的数据还没有初始化，拿不到本地玩家的武器entity
            var currentWeaponTableId = entity.GetByEntityId<IHeldItemEntity>(entity.CurrentWeaponId)?.TableId ?? 0;
            var currentWeaponTableIdInt = (int)currentWeaponTableId;
            var currentWeaponSkinId = entity.GetByEntityId<IHeldItemEntity>(entity.CurrentWeaponId)?.SkinId ?? 0;
            entity.TpAniHeldItemData.CurrentWeaponTableId = currentWeaponTableId;
            entity.TpAniHeldItemData.CurrentTableItemEnum = (int)GameEnumUtils.GetItemEnum(currentWeaponTableId);
            entity.TpAniHeldItemData.CurrentTableItemType = GameEnumUtils.GetItemEntityType(currentWeaponTableId);
            var weaponUniqueIndex = new WeaponUniqueIndex()
            {
                TableId = currentWeaponTableIdInt,
                AppearanceId = (int)ItemUtility.ItemApperanceID(currentWeaponTableId, currentWeaponSkinId),
            };
            if (tpFpMetaData.TryGetValue(weaponUniqueIndex, out var fpMetaData))
            {
                entity.TpAniHeldItemData.FpMetaData = fpMetaData;
            }

            if (tpClipLengthCollectDic.TryGetValue(weaponUniqueIndex, out var tpClipLengthCollect))
            {
                entity.TpAniPlayerLocalData.TpClipCollect = tpClipLengthCollect;
            }

            if (currentWeaponTableId != 0)
            {
                var itemConfig = McCommon.Tables.TbItemConfig.GetOrDefault(currentWeaponTableId);
                if (itemConfig != null)
                {
                    entity.TpAniHeldItemData.DisplayOnLadder = PlayerBaseState.CanUseByItemArea(entity).Item1 == EItemDisableReason.None;
                    entity.TpCacheCurrentTableItemType = itemConfig.Type;
                    switch (itemConfig.Type)
                    {
                        case ItemEntityType.Weapon: //远程
                            var gunTb = McCommon.Tables.TbGunBase.GetOrDefault(currentWeaponTableId);
                            entity.TpAniHeldItemData.SlingSort = gunTb.SlingSort;
                            var weaponItemEntity = entity.GetCurrentHandEntity<WeaponCustom>();
                            if (weaponItemEntity == null)
                                break;
                            entity.TpAniHeldItemData.HoldType =
                                SocAnimationUtility.GetHeldItemSocketType(weaponItemEntity, false);
                            entity.TpAniHeldItemData.IsWarmupWeapon = weaponItemEntity.IsWarmupWeapon;
                            break;
                        case ItemEntityType.ThrowItem: //投掷物
                            //var throwTb = McCommon.Tables.TbThrowWeapon.GetOrDefault(currentWeaponTableId);
                            var throwItemEntity = entity.GetCurrentHandEntity<ThrowWeaponCustom>();
                            if (throwItemEntity == null)
                                break;
                            entity.TpAniHeldItemData.HoldType =
                                SocAnimationUtility.GetHeldItemSocketType(throwItemEntity, false);
                            entity.TpAniHeldItemData.FarThrow = throwItemEntity.FarThrow;
                            break;
                        case ItemEntityType.HoldItem: //手持道具
                        case ItemEntityType.Melee: //近战
                        case ItemEntityType.Use: //手持
                            var holdTb = McCommon.Tables.TbHoldItemBase.GetOrDefault(currentWeaponTableId);
                            // if (PlayerLogicState.GetMeleeAttackInfos(entity, holdTb, out var infos))
                            // {
                            //     entity.TpAniHeldItemData.MeleeAttackIndex =
                            //         holdTb.AttackInfos[entity.UseAnimIndex].Type;
                            // }

                            entity.TpAniHeldItemData.CanPlayHitAnim = holdTb.IsHitAnimation;
                            entity.TpAniHeldItemData.MeleeAttackTime = holdTb.AttackTime;
                            var meleeEntity = entity.GetCurrentHandEntity<MeleeCustom>();
                            if (meleeEntity == null)
                                break;
                            entity.TpAniHeldItemData.HoldType =
                                SocAnimationUtility.GetHeldItemSocketType(meleeEntity, false);
                            entity.TpAniHeldItemData.FarThrow = true;
                            break;
                    }
                }

            }
            else
            {
                entity.TpAniHeldItemData.DisplayOnLadder = false;

            }

            for (int i = 0; i < (int)AnimMoveEnum.End; i++)
            {
                var em = (AnimMoveEnum)i;
                var speedConf = PlayerLoader.GetTpMoveSpeedConf((int)currentWeaponTableId, (int)currentWeaponSkinId, em);
                entity.TpAniPlayerStateData.MoveSpeedConfList.SetConf(em, speedConf);
            }

            entity.TpAniHeldItemData.EquipType = HeldItemUtility.GetEquipType(currentWeaponTableId);

            var appearanceId = ItemUtility.ItemApperanceID(currentWeaponTableId, currentWeaponSkinId);
            var weaponIndex = new WeaponUniqueIndex()
            {
                TableId = currentWeaponTableIdInt, AppearanceId = (int)appearanceId,
            };
            if (tpAniHeldItemDataIndex.TryGetValue(weaponIndex, out var currentTpAniOcWeightIndex))
            {
                entity.TpAniHeldItemData.TpAniOcWeightIndex = currentTpAniOcWeightIndex;
            }
            if(OcHeldItemTimeIndex.TryGetValue(weaponIndex, out var currentOcHeldItemTimeIndex))
            {
                entity.TpAniHeldItemData.OcTimeIndex = currentOcHeldItemTimeIndex;
            }

            if (TpLocomotionLayerWeightIndexDic.TryGetValue(weaponIndex, out var curTpAniLocomotionWeightIndex))
            {
                entity.TpAniHeldItemData.TpAniLocomotionWeightIndex = curTpAniLocomotionWeightIndex;
            }
            
            if (LocHeldItemTimeIndex.TryGetValue(weaponIndex, out var currentLocHeldItemTimeIndex))
            {
                entity.TpAniHeldItemData.LocTimeIndex = currentLocHeldItemTimeIndex;
            }
            if (LocHeldItemSpecialIndex.TryGetValue(weaponIndex, out var currentLocHeldItemSpecialIndex))
            {
                entity.TpAniHeldItemData.LocTimSpecialIndex = currentLocHeldItemSpecialIndex;
            }
            
            var clipSettingMeta = PlayerLoader.GetWeaponTpAniMeta((int)currentWeaponTableId, (int)currentWeaponSkinId);
            if (tpTipCurveIndex.TryGetValue(weaponIndex, out var currentTpTipCurveIndex))
            {
                entity.TpAniHeldItemData.TpAniTipCurveIndex = currentTpTipCurveIndex;
            }
        }

        private void UpdateTpAniHeldItemDataItem8(PlayerEntity playerEntity)
        {
            var itemLeft = playerEntity.GetItemEntity((int)HoldItemIndex.Item8);
            if (itemLeft != null)
            {
                playerEntity.TpAniHeldItemData.ItemLeftEntityId = itemLeft.EntityId;
            }
        }

        private void UpdateTpAniVehicleData(PlayerEntity entity)
        {
            var mountGo = Mc.Go.GetGo(entity.MountableId);
            if (entity.MountableId > 0 && mountGo == null)
            {
                //后续要持续刷新要有为止，不然数值对不上,因为go可能时异步加载的，不会那么快
                entity.NeedUpdateVehicleData = true;
            }

            if (mountGo != null)
            {
                var baseVehicleModuleGo = mountGo as BaseMountableGo;
                var mountPointConfig = baseVehicleModuleGo.MountPointConfig;
                if (baseVehicleModuleGo != null && mountPointConfig != null)
                {
                    var mountPoints = baseVehicleModuleGo.MountPointConfig.MountPoints;
                    if (mountPoints != null && mountPoints.Count > entity.MountSeatIndex && entity.MountSeatIndex >= 0)
                    {
                        entity.TpAniVehicleData.SeatStyleType = (int)(mountPoints[entity.MountSeatIndex].SeatStyleType);
                        tpVehicleSeatTbData.TryGetValue(entity.TpAniVehicleData.SeatStyleType,
                            out entity.TpAniVehicleData.VehicleSeatTbData);
                    }
                    else
                    {
                        entity.TpAniVehicleData.SeatStyleType = -1;
                    }
                }
                else
                {
                    entity.TpAniVehicleData.SeatStyleType = -1;
                }

                if (baseVehicleModuleGo is ClientSocHorseGo horseGo)
                {
                    entity.TpAniVehicleData.HaveHorseData = true;
                    //预测的
                    var horseEntity = horseGo.HorseEntity;
                    if (Mc.MyPlayer.MyEntityId == entity.EntityId && Mc.MyPlayer.MyEntityLocal.IsHorseDriver)
                    {
                        //是主角
                        horseEntity = horseGo.HorseEntity;
                    }
                    else
                    {
                        //是主角，或者是其他人的马不管驾驶还是乘客
                        horseEntity = horseGo.ServerEntity;
                    }
                    entity.TpAniVehicleData.HorseVertical = horseEntity.AniVertical;
                    entity.TpAniVehicleData.HorseHorizontal = horseEntity.AniHorizontal;
                    entity.TpAniVehicleData.HorseLocomotionMultiplier = horseEntity.AniLocomotionMultiplier;
                    //entity.TpAniVehicleData.Slope = horseGo.HorseEntity.Slope;
                    entity.TpAniVehicleData.HorseAniType = (int)horseEntity.AnimType;
                    entity.TpAniVehicleData.HorseJumpIndex = horseEntity.JumpAnimIndex;
                    entity.TpAniVehicleData.HorseJumpType = (int)horseEntity.JumpAnimType;

                    // if (entity.TpAniVehicleData.HorseVertical > 1f || entity.TpAniVehicleData.HorseHorizontal > 0.5f)
                    // {
                    //     var info = horseGo.Animator.GetCurrentAnimatorStateInfo(0);
                    //     entity.TpAniVehicleData.HorseLocomotionNormalizedTime = info.normalizedTime;
                    // }
                }
                else if (baseVehicleModuleGo is ClientParachuteGo parachuteGo)
                {
                    entity.TpAniVehicleData.ParachuteState = parachuteGo.ServerEntity.MoveState;
                    float maxSpeed = Mc.Tables.TbParachute.ParachuteDefaultSpeedZ +
                                     PlayerLoader.CharacterConfig.ParachuteMeta.VelocityCurveFZ.Evaluate(1);
                    float minSpeed = Mc.Tables.TbParachute.ParachuteDefaultSpeedZ -
                                     PlayerLoader.CharacterConfig.ParachuteMeta.VelocityCurveBZ.Evaluate(1);
                    if (parachuteGo.ServerEntity.MoveSpeed > Mc.Tables.TbParachute.ParachuteDefaultSpeedZ)
                    {
                        entity.TpAniVehicleData.ParachuteMoveSpeed =
                            UtilsMath.GetMappedRangeValueClamped(Mc.Tables.TbParachute.ParachuteDefaultSpeedZ, maxSpeed, 0, 1, parachuteGo.ServerEntity.MoveSpeed);
                    }
                    else
                    {
                        entity.TpAniVehicleData.ParachuteMoveSpeed =
                            UtilsMath.GetMappedRangeValueClamped(minSpeed, Mc.Tables.TbParachute.ParachuteDefaultSpeedZ, -1, 0, parachuteGo.ServerEntity.MoveSpeed);
                    }
                
                    float rotSpeed = PlayerLoader.CharacterConfig.ParachuteMeta.RotSpeedCurve.Evaluate(1);
                    entity.TpAniVehicleData.ParachuteRotateSpeed =
                        UtilsMath.GetMappedRangeValueClamped(-rotSpeed, rotSpeed, -1, 1, parachuteGo.ServerEntity.RotateSpeed);
                }
            }
            else
            {
                entity.TpAniVehicleData.SeatStyleType = -1;
            }

            var vehicleEntity = Mc.Entity.GetEntity(entity.MountableId) as IBaseVehicleEntity;
            if (vehicleEntity != null)
            {
                entity.TpVehicleEntity = vehicleEntity;
                entity.TpAniVehicleData.SteerAngle = vehicleEntity.SteerAngle;
            }
        }

        private void UpdateTpAniVehicleDataSeatIndex(PlayerEntity entity)
        {
            var mountGo = Mc.Go.GetGo(entity.MountableId);
            if (mountGo != null)
            {
                var baseVehicleModuleGo = mountGo as BaseMountableGo;
                var mountPointConfig = baseVehicleModuleGo.MountPointConfig;
                if (baseVehicleModuleGo != null && mountPointConfig != null)
                {
                    var mountPoints = baseVehicleModuleGo.MountPointConfig.MountPoints;
                    if (mountPoints != null && mountPoints.Count > entity.MountSeatIndex && entity.MountSeatIndex >= 0)
                    {
                        entity.TpAniVehicleData.SeatStyleType = (int)(mountPoints[entity.MountSeatIndex].SeatStyleType);
                        tpVehicleSeatTbData.TryGetValue(entity.TpAniVehicleData.SeatStyleType,
                            out entity.TpAniVehicleData.VehicleSeatTbData);
                    }
                    else
                    {
                        entity.TpAniVehicleData.SeatStyleType = -1;
                    }
                }
                else
                {
                    entity.TpAniVehicleData.SeatStyleType = -1;
                }
            }
            else
            {
                entity.TpAniVehicleData.SeatStyleType = -1;
            }
        }
#region 状态变化监听
        private void PlayerUnaliveStateChange(CustomTypeBase inEntity, PlayerUnAliveStateEnum oldValue, PlayerUnAliveStateEnum newValue)
        {
            var entity = inEntity as PlayerEntity;
            if (entity == null)
                return;
            entity.TpAniPlayerStateData.UnAliveState = entity.UnAliveState;
        }
        private void PlayerMoveStateChange(CustomTypeBase inEntity, PlayerMoveStateEnum oldValue, PlayerMoveStateEnum newValue)
        {
            var entity = inEntity as PlayerEntity;
            if (entity == null)
                return;
            entity.TpAniPlayerStateData.MoveState = entity.MoveState;
        }
        private void PlayerMoveJumpStateChange(CustomTypeBase inEntity, byte oldValue, byte newValue)
        {
            var entity = inEntity as PlayerEntity;
            if (entity == null)
                return;
            entity.TpAniPlayerStateData.MoveJumpState = entity.MoveJumpState;
        }
        private void PlayerMoveLadderStateChange(CustomTypeBase inEntity, byte oldValue, byte newValue)
        {
            var entity = inEntity as PlayerEntity;
            if (entity == null)
                return;
            entity.TpAniPlayerStateData.MoveLadderState = entity.MoveLadderState;
        }
        private void PlayerMoveSwimStateChange(CustomTypeBase inEntity, byte oldValue, byte newValue)
        {
            var entity = inEntity as PlayerEntity;
            if (entity == null)
                return;
            entity.TpAniPlayerStateData.MoveSwimState = entity.MoveSwimState;
        }
        private void PlayerMoveZiplineStateChange(CustomTypeBase inEntity, byte oldValue, byte newValue)
        {
            var entity = inEntity as PlayerEntity;
            if (entity == null)
                return;
            entity.TpAniPlayerStateData.MoveZiplineState = entity.MoveZiplineState;
        }
        private void PlayerPoseStateChange(CustomTypeBase inEntity, byte oldValue, byte newValue)
        {
            var entity = inEntity as PlayerEntity;
            if (entity == null)
                return;
            entity.TpAniPlayerStateData.PoseState = entity.PoseState;
        }
        private void PlayerPoseDyingStateChange(CustomTypeBase inEntity, byte oldValue, byte newValue)
        {
            var entity = inEntity as PlayerEntity;
            if (entity == null)
                return;
            entity.TpAniPlayerStateData.PoseDyingState = entity.PoseDyingState;
        }
        private void PlayerActionStateChange(CustomTypeBase inEntity, byte oldValue, byte newValue)
        {
            var entity = inEntity as PlayerEntity;
            if (entity == null)
                return;
            entity.TpAniPlayerStateData.ActionState = entity.ActionState;
        }
        private void PlayerAttackSubStateChange(CustomTypeBase inEntity, byte oldValue, byte newValue)
        {
            var entity = inEntity as PlayerEntity;
            if (entity == null)
                return;
            entity.TpAniPlayerStateData.AttackSubState = entity.AttackSubState;
        }
        private void PlayerReloadStateChange(CustomTypeBase inEntity, byte oldValue, byte newValue)
        {
            var entity = inEntity as PlayerEntity;
            if (entity == null)
                return;
            entity.TpAniPlayerStateData.ReloadState = entity.ReloadState;
        }
        private void PlayerBowStateChange(CustomTypeBase inEntity, byte oldValue, byte newValue)
        {
            var entity = inEntity as PlayerEntity;
            if (entity == null)
                return;
            entity.TpAniPlayerStateData.BowState = entity.BowState;
            entity.TpAniPlayerLocalData.BowStateChange = true;
        }
        private void PlayerThrowStateChange(CustomTypeBase inEntity, byte oldValue, byte newValue)
        {
            var entity = inEntity as PlayerEntity;
            if (entity == null)
                return;
            entity.TpAniPlayerStateData.ThrowState = entity.ThrowState;
        }

        private void PlayerAdsStateChange(CustomTypeBase inEntity, byte oldValue, byte newValue)
        {
            var entity = inEntity as PlayerEntity;
            if (entity == null)
                return;
            entity.TpAniPlayerStateData.AdsState = entity.AdsState;
        }
        #endregion

        private void GestureChangeCallBack(CustomTypeBase entity, int oldValue, int newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.GestureIndex = newValue;
        }

        private void LadderTargetIdChangeCallBack(CustomTypeBase entity, long oldValue, long newValue)
        {
            UpdateLadder(entity as PlayerEntity);
        }
        private void LadderAbsorbTargetIdChangeCallBack(CustomTypeBase entity, long oldValue, long newValue)
        {
            UpdateLadder(entity as PlayerEntity);
        }

        private void UpdateLadder(PlayerEntity player)
        {
            if (player == null)
                return;

            if (player.LadderTargetId == 0)
            {
                return;
            }

            var ladderGo = Mc.Go.GetGo(player.LadderTargetId) as ILadderGo;
            if(ladderGo == null)
            {
                #if UNITY_EDITOR
                logger.ErrorFormat("ladderGo is null,playerId:{0},ladderTargetId:{1}",player.EntityId,player.LadderTargetId);
                #endif
                return;
            }

            AdsorbCollideConfig climbAdsorbConfig = null;
            if(player.LadderAdsorbTargetIndex >= 0 && player.LadderAdsorbTargetIndex < ladderGo.adsorbCollideConfigs.Length)
            {
                climbAdsorbConfig = ladderGo.adsorbCollideConfigs[player.LadderAdsorbTargetIndex];
            }

            if (climbAdsorbConfig == null)
            {
#if UNITY_EDITOR
                logger.ErrorFormat("climbAdsorbConfig is null,playerId:{0},ladderTargetId:{1},ladderAdsorbTargetIndex:{2}",player.EntityId,player.LadderTargetId,player.LadderAdsorbTargetIndex);
#endif
                return;
            }

            player.TpAniPlayerStateData.LadderQuat = climbAdsorbConfig.GetLadderQuat();
            player.TpAniPlayerStateData.LadderNormal = climbAdsorbConfig.GetLadderNormal();
            player.TpAniPlayerStateData.LadderForward = climbAdsorbConfig.GetLadderForward();
            player.TpAniPlayerStateData.LadderRight = climbAdsorbConfig.GetLadderRight();
            player.TpAniPlayerStateData.LadderMoveQuat = climbAdsorbConfig.moveRotation;
        }

        private void LadderEnterDirChangeCallBack(CustomTypeBase entity, int oldValue, int newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.LadderEnterDir = (LadderTestDir)newValue;
            
            if (player.TpAniPlayerStateData.LadderEnterDir != LadderTestDir.None)
            {
                //程序化移动时间
                int intAnimMoveTime = 0;
                int intAnimYawTime = 0;
                int speed = 4;
                if( PlayerLoader.LadderConfig.LadderPreliminaryRules.TryGetValue(player.TpAniPlayerStateData.LadderEnterDir, out var enterConf))
                {
                    PlayerLogicState.CalculateLadderTime(player, enterConf, out intAnimMoveTime, out intAnimYawTime,out speed);
                }
                
                int rmTime = 0;
                RootMotionWarpingData rmData = AnimWarpingSystemUtils.FindRootMotion(player, player.RootMotionResNumber);
                if (rmData != null)
                {
                    rmTime = rmData.RawClipLength;
                }
                
                player.TpAniPlayerStateData.AnimMoveTime = intAnimMoveTime;
                player.TpAniPlayerStateData.AnimTurnTime = intAnimYawTime;
                player.TpAniPlayerStateData.AnimRMTotalTime = rmTime;
                player.TpAniPlayerStateData.AnimSpeed = speed;
            }
            else
            {
                player.TpAniPlayerStateData.AnimMoveTime = 0;
                player.TpAniPlayerStateData.AnimTurnTime = 0;
                player.TpAniPlayerStateData.AnimRMTotalTime = 0;
                player.TpAniPlayerStateData.AnimSpeed = 0;
            }
        }

        private void LadderMoveFlagChangeCallBack(CustomTypeBase entity, int oldValue, int newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;

            player.TpAniPlayerStateData.LadderMoveFlag = newValue;
        }
        private void LadderLeaveDirChangeCallBack(CustomTypeBase entity, int oldValue, int newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.LadderExitDir = (LadderTestDir)newValue;
            player.TpAniPlayerStateData.AnimMoveTime = 0;
            player.TpAniPlayerStateData.AnimTurnTime = 0;
            player.TpAniPlayerStateData.AnimRMTotalTime = 0;
            player.TpAniPlayerStateData.AnimSpeed = 0;
        }
        private void MountableIdChangeCallBack(CustomTypeBase entity, long oldValue, long newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.MountableId = newValue;
            UpdateTpAniVehicleData(player);
        }

        private void HorseMountDirChangeCallBack(CustomTypeBase entity, int o, int n)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerLocalData.HorseMountDir = n;
        }

        private void VehicleTypeChangeCallBack(CustomTypeBase entity, int oldValue, int newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.VehicleType = newValue;
        }

        private void MountableTypeChangeCallBack(CustomTypeBase entity, int oldValue, int newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.MountableType = newValue;
        }

        private void IsDriverChangeCallBack(CustomTypeBase entity, bool oldValue, bool newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.IsDriver = newValue;
        }

        private void RotateYChangeCallBack(EntityPositionChangeEvent e)
        {
            var player = e.Entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.RotateY = player.RotateY;
        }

        private void CmdYawChangeCallBack(CustomTypeBase entity, float oldValue, float newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.LastViewYaw = player.TpAniPlayerStateData.ViewYaw ;
            player.TpAniPlayerStateData.ViewYaw = newValue;
        }


        private void AdsOffsetProgressChangeCallBack(CustomTypeBase entity, float oldValue, float newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.AdsOffsetProgress = newValue;
        }

        private void CurrentWeaponIdChangeCallBack(CustomTypeBase entity, long oldValue, long newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.CurrentWeaponId = newValue;
            //武器的相关配置的刷新
            UpdateTpAniHeldItemData(player);
        }

        private void Movement8DirectionChangeCallBack(CustomTypeBase entity, int oldValue, int newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.Movement8Direction = newValue;
        }

        private void Movement4DirectionChangeCallBack(CustomTypeBase entity, int oldValue, int newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.Movement4Direction = newValue;
        }

        private void RootMotionWarpingIndexChangeCallBack(CustomTypeBase entity, int oldValue, int newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.RootMotionWarpingIndex = newValue;
            player.TpAniPlayerStateData.RootMotionRefresh=true;
        }

        private void InputSprintChangeCallBack(CustomTypeBase entity, bool oldValue, bool newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.InputSprint = newValue;
        }

        private void SpeedXChangeCallBack(CustomTypeBase entity, float oldValue, float newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.SpeedX = newValue;
        }

        private void SpeedZChangeCallBack(CustomTypeBase entity, float oldValue, float newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.SpeedZ = newValue;
        }

        private void SpeedYChangeCallBack(CustomTypeBase entity, float oldValue, float newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.SpeedY = newValue;
        }

        private void StateRecoveryReasonChangeCallBack(CustomTypeBase entity, int oldValue, int newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.StateRecoveryReason = newValue;
        }

        private void TryFireChangeCallBack(CustomTypeBase entity, int oldValue, int newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            if (ActionStateUtility.TriggerChanged(oldValue, newValue, ActionTriggerBlock.EokaPistolTryFire))
            {
                player.TpAniPlayerStateData.TryFire = player.IsActionTriggerOn(ActionTriggerBlock.EokaPistolTryFire);
            }
        }

        private void AttackHitMatChangeCallBack(CustomTypeBase entity, int oldValue, int newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.AttackHitMat = newValue;
        }

        private void UseHitIndexChangeCallBack(CustomTypeBase entity, byte oldValue, byte newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.UseHitIndex = newValue;
        }

        private void InteractiveIdChangeCallBack(CustomTypeBase entity, int oldValue, int newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            var tb = McCommon.Tables.TbPlayerInteractiveState.GetOrDefault(player.InteractiveId);
            if(tb==null)
                return;
            player.TpAniPlayerStateData.InteractiveStateTime = tb.StateTime/1000.0f;
        }

        // private void UsingForOtherChangeCallBack(CustomTypeBase entity, bool oldValue, bool newValue)
        // {
        //     var player = entity as PlayerEntity;
        //     if (player == null)
        //         return;
        //     player.TpAniPlayerStateData.UsingForOther = newValue;
        // }

        private void CmdPitchChangeCallBack(CustomTypeBase entity, float oldValue, float newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniPlayerStateData.CmdPitch = newValue;
        }

        private void UseAnimIndexChangeCallBack(CustomTypeBase entity, byte oldValue, byte newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            player.TpAniHeldItemData.MeleeAttackIndex = newValue;
        }

        private void Item8ChangeCallBack(CustomTypeBase entity, CustomTypeBase oldValue,
            CustomTypeBase newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            UpdateTpAniHeldItemDataItem8(player);
        }

        private void MountSeatIndexChangeCallBack(CustomTypeBase entity, int oldValue, int newValue)
        {
            var player = entity as PlayerEntity;
            if (player == null)
                return;
            UpdateTpAniVehicleDataSeatIndex(player);
        }

        /// <summary>
        /// 播放交互行为，包括返回空
        /// </summary>
        public static void PlayInteractionState(PlayerEntity entity, AnimParametersTp.EOverrideLayer nextState, float transitionTime, float playRate =1.0f, AnimParametersTp.EOverrideLayer nowState=default)
        {
            if (entity == null)
                return;
            //如果是要结束
            if (nextState == AnimParametersTp.EOverrideLayer.OverrideEmpty)
            {
                if(nowState== nextState)
                    return;
                if (nowState != entity.TpAniPlayerLocalData.EOverrideLayer)
                    return;
            }

            entity.TpAniPlayerLocalData.PlayInteraction = true;
            entity.TpAniPlayerLocalData.InteractionType = nextState;
            entity.TpAniPlayerLocalData.ToInteractionTransitionTime = transitionTime;
           
            if (nextState == AnimParametersTp.EOverrideLayer.InteractionHalfBody_Gesture ||
                nextState == AnimParametersTp.EOverrideLayer.OverrideEmpty)
            {
                //因为是oc替换的，需要强制刷新
                entity.TpAniPlayerLocalData.SwitchTpAnimator = true;
            }
        }
    }
}