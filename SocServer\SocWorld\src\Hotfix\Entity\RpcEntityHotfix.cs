﻿using Cysharp.Text;
using Gameserver;
using SimpleJSON;
using System;
using System.Buffers;
using System.Collections.Generic;
using System.Linq;
using WizardGames.Soc.Common.Algorithm;
using WizardGames.Soc.Common.Combat;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Config;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Data.DataItem;
using WizardGames.Soc.Common.Data.Play;
using WizardGames.Soc.Common.Data.resource;
using WizardGames.Soc.Common.Electric;
using WizardGames.Soc.Common.Entity.Interface;
using WizardGames.Soc.Common.Framework.Algorithm;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Framework.Network.Client;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Event;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.Ability;
using WizardGames.Soc.SocWorld.Entity;
using WizardGames.Soc.SocWorld.Entity.Utils;
using WizardGames.Soc.SocWorld.Event;
using WizardGames.Soc.SocWorld.Framework.Network;
using WizardGames.Soc.SocWorld.Framework.Network.Server;
using WizardGames.Soc.SocWorld.Indicator;
using WizardGames.Soc.SocWorld.Main;
using WizardGames.Soc.SocWorld.NodeSystem;
using WizardGames.Soc.SocWorld.RuleGraph;
using WizardGames.Soc.SocWorld.Spawn;
using WizardGames.Soc.SocWorld.WorldCommon;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.Common.Entity
{
    [HotfixClass]
    public static partial class RpcEntityHotfix
    {
        [RpcHandler(ExposeToClient = true, CallInterval = .5f)]
        public static void RemoteConsoleReply(this RpcEntity self, int consoleClientId, string reply)
        {
            WorldNetAgent.Instance.ConsoleListener.RemoteConsoleReply(consoleClientId, reply);
        }

        [RpcHandler]
        public static void CreateCorpse(this RpcEntity self, CreateCorpseRequest data)
        {
            self.Logger.InfoFormat("CreateCorpse hostId:{0} isPlayer:{1}", data.HostEntityId, data.IsPlayer);
            var corpse = new CorpseEntity(data);
            EntityManager.Instance.AddEntity(corpse);
        }

        [RpcHandler]
        public static void CreateHorseCorpse(this RpcEntity host, CreateCorpseRequest data)
        {
            data.CorpseType = ECorpseTypeEnum.Horse;
            var corpse = new CorpseEntity(data);
            EntityManager.Instance.AddEntity(corpse);

            var horse = EntityManager.Instance.GetEntity(data.HostEntityId) as HorseEntity;
            horse?.ComponentHorse.CancelDefecation();
        }

        [RpcHandler]
        public static void SceneItemCombined(this RpcEntity self, long fromSceneEntityId, long toSceneEntityId)
        {
            if (fromSceneEntityId == toSceneEntityId)
            {
                self.Logger.Error($"SceneItemCombined called with same entity id: {fromSceneEntityId} to {toSceneEntityId}");
                return;
            }
            var toSceneItemEnt = EntityManager.Instance.GetEntity(toSceneEntityId) as SceneItemEntity;
            if (toSceneItemEnt == null)
            {
                self.Logger.InfoFormat("toSceneItemEnt not found. from {0} to {1}", fromSceneEntityId, toSceneEntityId);
                return;
            }
            var fromSceneItemEnt = EntityManager.Instance.GetEntity(fromSceneEntityId) as SceneItemEntity;
            if (fromSceneItemEnt == null)
            {
                self.Logger.WarnFormat("fromSceneItemEnt not found. from {0} to {1}", fromSceneEntityId, toSceneEntityId);
                return;
            }
            toSceneItemEnt.CombineSceneItem(fromSceneItemEnt);
        }

        [RpcHandler(ExposeToClient = true)]
        public static void GMRequirePathNode(this RpcEntity host, float srcX, float srcY, float srcZ, float tarX, float tarY, float tarZ)
        {
            var roleId = RpcContext.CurrentRoleId;
            var player = EntityManager.Instance.GetPlayerEntityByRoleId(roleId);
            player?.RemoteCallGMRequirePathNodeSim(ERpcTarget.Simulator, srcX, srcY, srcZ, tarX, tarY, tarZ, player.RoleId);
        }

        [RpcHandler]
        public static void TestSummonMonsterFromServer(this RpcEntity host, long templateId, int createCount, float posX, float posY, float posZ)
        {
            for (int i = 0; i < createCount; i++)
            {
                WorldEntityFactory.CreateEntity(new WorldResourceRecord()
                {
                    EntityType = EntityTypeId.MonsterEntity,
                    TemplateId = (int)templateId,
                    Pos = new Common.Algorithm.Vector3(posX, posY, posZ),
                });
            }
        }

        [RpcHandler(ExposeToClient = true)]
        public static void TestSummonTrap(this RpcEntity self, long templateId, float posX, float posY, float posZ, long playerId)
        {
            PlayerEntity player = EntityManager.Instance.GetPlayerEntity(playerId);
            float minDis = 1e9f;
            MonsterEntity selectedMonster = null;
            foreach (var entity in EntityManager.Instance.Entities)
            {
                if (entity.Value is MonsterEntity monster)
                {
                    float dis = (monster.PosX - player.PosX) * (monster.PosX - player.PosX) + (monster.PosZ - player.PosZ) * (monster.PosZ - player.PosZ);
                    if (dis < minDis)
                    {
                        selectedMonster = monster;
                        minDis = dis;
                    }
                }
            }
            TrapEntity trap = new TrapEntity(IdGeneratorUtil.GenInWorldCommonId());
            self.Logger.Info($"TestSummonTrap, posX:{posX}, posY: {posY}, posZ{posZ}, monster id:{selectedMonster?.EntityId ?? -1}");
            trap.PosX = posX;
            trap.PosY = posY;
            trap.PosZ = posZ;
            trap.TemplateId = (int)templateId;
            trap.InterestedEntityId = selectedMonster.EntityId;
            selectedMonster.MoveToTargetPosX = posX;
            selectedMonster.MoveToTargetPosY = posY;
            selectedMonster.MoveToTargetPosZ = posZ;
            selectedMonster.IsWild = true;

            EntityManager.Instance.AddEntity(trap);
        }

        [RpcHandler]
        public static void TestSummonPlayerFromServer(this RpcEntity host, ulong roleId, int createCount, float posX, float posY, float posZ)
        {
            var player = new PlayerEntity()
            {
                LongRoleId = (long)roleId,
                IsRobot = false,
                ActionState = PlayerActionStateEnum.Hold,
                MountSeatIndex = -1,
                CharacterState = PlayerCharacterStateEnum.Active,
                UnAliveState = PlayerUnAliveStateEnum.UnAliveNone,
                CreateTimeStamp = TimeStampUtil.GetNowTimeStampSec(),
            };
            var entityId = player.EntityId;
            player.NullHand = new MeleeCustom(IdGeneratorUtil.GenGlobalUniqueId())
            {
                TemplateId = 0,
                OwnerEntityId = entityId,
                Position = -1,
                TableId = 0,
                SkinId = 0,
            };


            player.PosX = posX;
            player.PosY = posY;
            player.PosZ = posZ;
            player.CurrentWeaponId = player.NullHand.EntityId;//初始化当前武器id为空手
            EntityManager.Instance.AddEntity(player);
        }

        [RpcHandler]
        public static void RecordEnemyAttackTime(this RpcEntity self, long terrId, DamageDataEvent damageData)
        {
            var terrEnt = TerritoryManagerEntity.Instance.GetTerritoryEntity(terrId);
            if (terrEnt == null)
            {
                self.Logger.Info($"RecordEnemyAttackTime territory center not found. terr: {terrId}");
                return;
            }
            self.Logger.Debug($"RecordEnemyAttackTime terrId {terrId}, damageData {damageData}");
            terrEnt.PlayComp.RecordEnterAttacked();
            if (damageData.SourcePlayerId == 0)
                return;
            if (EntityManager.Instance.GetPlayerEntity(damageData.SourcePlayerId) is not PlayerEntity sourcePlayer)
            {
                self.Logger.Error($"RecordEnemyAttackTime sourcePlayer not found. sourcePlayerId: {damageData.SourcePlayerId}");
                return;
            }
            var directItemId = damageData.AmmoTableId != 0 ? damageData.AmmoTableId : damageData.WeaponTableId;
            var attackPartEvent = new AttackPartEvent(damageData.SourceId, damageData.SourcePlayerId, damageData.WeaponTableId, directItemId, (int)damageData.Damage, damageData.SourceClientTime, terrId, damageData.KillType == KillType.Kill);
            EntityStaticCallback<PlayerEntity>.InvokeAllEventCallback(sourcePlayer, attackPartEvent);
        }

        [RpcHandler]
        public static void PartDead(this RpcEntity self, long partId, long sourceId, long weaponTableId)
        {
            var partEntity = EntityManager.Instance.GetEntity(partId) as PartEntity;
            if (partEntity == null)
            {
                self.Logger.Info($"[PartDead] {partId} part destroy by {sourceId}, but removed in advance");
                return;
            }

            var player = EntityManager.Instance.GetPlayerEntity(sourceId);
            if (player != null)
            {
                var owner = UserManagerEntity.Instance.GetPlayerEntity((ulong)partEntity.OwnerId);
                if (owner != null)
                {
                    player.FriendComp.AddEncounteredPlayer(owner.RoleId, Const.EEncounteredReason.Plunder);
                    owner.FriendComp.AddEncounteredPlayer(player.RoleId, Const.EEncounteredReason.Plunder);
                }
            }
            self.Logger.Info($"[PartDead] {partId} part destroy by {sourceId}");

            ConstructionManagerEntity.Instance.RequestDestroyPart(partEntity, EDestroyPartReason.NoHp, sourceId: sourceId, weaponTableId: weaponTableId);
        }

        [RpcHandler]
        public static void GroundWatchRemovePart(this RpcEntity self, long partId, long sourceId)
        {
            var partEnt = EntityManager.Instance.GetEntity(partId) as PartEntity;
            if (partEnt == null)
            {
                self.Logger.Info($"[GroundWatchRemovePart] {partId} part dropped by {sourceId}, but removed in advance");
                return;
            }
            self.Logger.Info($"[GroundWatchRemovePart] {partId} part dropped by {sourceId}");
            ConstructionManagerEntity.Instance.RequestDestroyPart(partEnt, EDestroyPartReason.GroundWatchRemove, sourceId: sourceId);
        }

        [RpcHandler]
        public static void OnBoxEntityFallDown(this RpcEntity host, long entityId)
        {
            var boxEntity = EntityManager.Instance.GetEntity(entityId) as BoxEntity;
            if (boxEntity == null) return;
            if (Mathf.Abs(boxEntity.PosX - boxEntity.PosX) > 1f || Mathf.Abs(boxEntity.PosY - boxEntity.PosY) > 1f || Mathf.Abs(boxEntity.PosZ - boxEntity.PosZ) > 1f)
            {
                EntityManager.Instance.OctreeManager.MoveEntity(boxEntity);
            }
        }

        /// <summary>
        /// 召唤空投
        /// </summary>
        /// <param name="posX"></param>
        /// <param name="posY"></param>
        /// <param name="posZ"></param>

        [RpcHandler]
        public static void CallAirDrop(this RpcEntity self, ulong roleId, float posX, float posY, float posZ)
        {
            AirDropPlaneEntity entity = new AirDropPlaneEntity(IdGeneratorUtil.GenInWorldCommonId());
            self.Logger.Info($"call air drop, posX:{posX}, posY: {posY}, posZ{posZ}, box id:{AirdropControllerEntity.Instance.DropBoxId}, airdrop entity id: {entity.EntityId}");
            entity.DropX = posX;
            entity.DropY = posY;
            entity.DropZ = posZ;
            entity.IsRandom = false;
            entity.CallerUserId = (long)roleId;

            entity.DropBoxId = AirdropControllerEntity.Instance.DropBoxId;
            entity.TimeScale = AirdropControllerEntity.Instance.AirDropPlaneTimeScale;
            entity.DisScale = AirdropControllerEntity.Instance.AirDropPlaneDisScale;
            entity.Height = AirdropControllerEntity.Instance.AirDropPlaneHeight;

            EntityManager.Instance.AddEntity(entity);
        }

        /// <summary>
        /// 召唤随机空投
        /// </summary>
        /// <param name="index"></param>
        /// <param name="posX"></param>
        /// <param name="posY"></param>
        /// <param name="posZ"></param>

        [RpcHandler]
        public static void CallRandomAirDrop(this RpcEntity self, long index, float posX, float posY, float posZ)
        {
            if (!AirdropControllerEntity.Instance.AirdropData.TryGetValue(index, out var data))
            {
                self.Logger.Error($"call random air drop failed, index:{index} is not exist!");
                return;
            }

            AirDropPlaneEntity entity = new AirDropPlaneEntity(IdGeneratorUtil.GenInWorldCommonId());
            self.Logger.Info($"call random air drop, index:{index}, posX:{posX}, posY: {posY}, posZ{posZ}, box id:{data.DropBoxId}, airdrop entity id: {entity.EntityId}");
            entity.DropX = posX;
            entity.DropY = posY;
            entity.DropZ = posZ;
            entity.IsRandom = true;
            entity.CallerUserId = 0;

            entity.DropBoxId = data.DropBoxId;
            entity.TimeScale = data.AirDropPlaneTimeScale;
            entity.DisScale = data.AirDropPlaneDisScale;
            entity.Height = data.AirDropPlaneHeight;

            EntityManager.Instance.AddEntity(entity);

            AirdropControllerEntity.Instance.AirdropData.Remove(index);
        }

        [RpcHandler]
        public static void CreateAirDropBox(this RpcEntity self, AirDropWorldResource record, bool IsRandom)
        {
            self.Logger.InfoFormat("CreateAirDropBox {0} {1}", record.entityId, record.templateId);
            var param = new BoxEntitySpawnParam()
            {
                SpawnType = record.spawnType,
            };

            var box = new AirdropEntity(record.templateId, record.posx, record.posy, record.posz, record.rotx, record.roty, record.rotz, param, record.entityId);
            box.IsFromRandomPlane = IsRandom;
            EntityManager.Instance.AddEntity(box);
        }

        [RpcHandler(ExposeToClient = true)]
        public static void WroldGMKillMonster(this RpcEntity entity, long entityId, int distance)
        {
            var roleId = RpcContext.CurrentRoleId;
            if (UserManagerEntity.Instance.GetPlayerEntity(roleId) is not PlayerEntity pe || pe.DebugComp == null) return;
            entity.RemoteCallUnityDsGMKillMonster(ERpcTarget.Simulator, entityId, distance);
        }

        [RpcHandler(ExposeToClient = true)]
        public static void WorldGMEnableBt(this RpcEntity self, bool enabled)
        {
            var roleId = RpcContext.CurrentRoleId;
            if (UserManagerEntity.Instance.GetPlayerEntity(roleId) is not PlayerEntity pe || pe.DebugComp == null) return;
            self.Logger.Info("GMEnableBt, new state is enable: " + enabled + " player name: " + pe.Name + " role id: " + pe.RoleId);
            self.RemoteCallUnityDsGMEnableBt(ERpcTarget.Simulator, enabled);
        }

        [RpcHandler(ExposeToClient = true)]
        public static void WorldGMMonsterInvincible(this RpcEntity self, bool invincible)
        {
            var roleId = RpcContext.CurrentRoleId;
            if (UserManagerEntity.Instance.GetPlayerEntity(roleId) is not PlayerEntity pe || pe.DebugComp == null) return;
            self.RemoteCallUnityDsGMMonsterInvincible(ERpcTarget.Simulator, invincible);
        }

        [RpcHandler(ExposeToClient = true)]
        public static void WorldGMAutoTurretSetSwitchOn(this RpcEntity entity, long entityId, bool isOn)
        {
            var roleId = RpcContext.CurrentRoleId;
            if (UserManagerEntity.Instance.GetPlayerEntity(roleId) is not PlayerEntity pe || pe.DebugComp == null) return;
            entity.RemoteCallUnityDsGMAutoTurretSetSwitchOn(ERpcTarget.Simulator, entityId, isOn);
        }

        [RpcHandler(ExposeToClient = true)]
        public static void WorldGMEnableSwarmAI(this RpcEntity entity, bool enableSwarm)
        {
            var roleId = RpcContext.CurrentRoleId;
            if (UserManagerEntity.Instance.GetPlayerEntity(roleId) is not PlayerEntity pe || pe.DebugComp == null) return;
            entity.RemoteCallUnityDsGMEnableSwarmAI(ERpcTarget.Simulator, enableSwarm);
        }

        [RpcHandler]
        public static void SetAutoTurretHasTarget(this RpcEntity self, long entityId, bool hasTarget)
        {
            ConstructionManagerEntity.Instance.SetAutoTurretHasTarget(entityId, hasTarget);
        }

        /// <summary>
        /// 密码箱呼叫增援，召唤载有科学家的CH47运输机
        /// </summary>
        /// <param name="posX"></param>
        /// <param name="posY"></param>
        /// <param name="posZ"></param>
        /// <param name="targetPosX"></param>
        /// <param name="targetPosY"></param>
        /// <param name="targetPosZ"></param>
        /// <param name="rotationY"></param>
        [RpcHandler]
        public static void CallReinforcement(this RpcEntity self, long boxEntityId, float posX, float posY, float posZ, float targetPosX, float targetPosY, float targetPosZ, float rotationY)
        {
            //创建运输机
            var vehicleEntityId = WorldEntityFactory.CreateEntity(new WorldResourceRecord()
            {
                EntityType = EntityTypeId.VehicleEntity,
                VehicleType = (int)VehicleType.Ch47,
                TemplateId = 22030006,
                Pos = new Vector3(posX, posY, posZ),
                Rot = new Vector3(0, rotationY, 0),
                TargetPos = new Vector3(targetPosX, targetPosY, targetPosZ),
            });
            var vehicleEntity = EntityManager.Instance.GetEntity<VehicleEntity>(vehicleEntityId);
            var boxEntity = EntityManager.Instance.GetEntity<BoxEntity>(boxEntityId);
            int[] monsterList = McCommon.Tables.TbGlobalConfig.CH47PassengerIds;
            for (int i = 0; i < monsterList.Length; i++)
            {
                var monsterEntityId = WorldEntityFactory.CreateEntity(new WorldResourceRecord()
                {
                    EntityType = EntityTypeId.MonsterEntity,
                    TemplateId = monsterList[i],
                    Pos = Vector3.Zero,
                    MountableId = vehicleEntityId,
                    MountableType = EntityTypeId.VehicleEntity,
                    VehicleType = (int)VehicleType.Ch47,
                    MountSeatIndex = i,
                    RemoveWhenBoxRefresh = true,
                    MonumentId = boxEntity.MonumentId,
                });
                VehicleUtil.SetVehicleSeatInfo(vehicleEntity, i, monsterEntityId);
            }
        }

        [RpcHandler(ExposeToClient = true)]
        public static void CallAttackHelicopter(this RpcEntity self, float posX, float posY, float posZ)
        {
            //创建武装直升机
            var vehicleEntityId = WorldEntityFactory.CreateEntity(new WorldResourceRecord()
            {
                EntityType = EntityTypeId.VehicleEntity,
                VehicleType = (int)VehicleType.AttackHelicopter,
                TemplateId = BaseMountableEntity.GUNSHIP_TEMPLATE_ID,
                Pos = new Vector3(posX, posY, posZ),
            });
        }

        [RpcHandler(ExposeToClient = true)]
        public static void MountOneMonsterPassenger(this RpcEntity self, long vehicleEntityId, int monsterTemplateId, int seatIndex)
        {
            var vehicleEntity = EntityManager.Instance.GetEntity<VehicleEntity>(vehicleEntityId);
            if (vehicleEntity == null)
            {
                return;
            }

            var monsterEntityId = WorldEntityFactory.CreateEntity(new WorldResourceRecord()
            {
                EntityType = EntityTypeId.MonsterEntity,
                TemplateId = monsterTemplateId,
                Pos = Vector3.Zero,
                MountableId = vehicleEntityId,
                MountableType = EntityTypeId.VehicleEntity,
                VehicleType = (int)VehicleType.AttackHelicopter,
                MountSeatIndex = seatIndex,
            });

            VehicleUtil.SetVehicleSeatInfo(vehicleEntity, seatIndex, monsterEntityId);
        }

        [RpcHandler]
        public static void TestCreateCommercialVehiclesFromServer(this RpcEntity host, float posX, float posY,
            float posZ) // 创建商用车辆
        {
            WorldResourceRecord record = new WorldResourceRecord()
            {
                EntityType = EntityTypeId.SpecializedVehicleEntity,
                TemplateId = 22030006,
                Pos = new Vector3(posX, posY, posZ),
            };
            WorldEntityFactory.CreateEntity(record);
        }
        private static void BuildPassengers(long carEntityId, int monsterTemplateId, int slotIndex,
            SpecializedVehicleEntity carEntity)
        {
            var monsterEntityId = WorldEntityFactory.CreateEntity(new WorldResourceRecord()
            {
                EntityType = EntityTypeId.MonsterEntity,
                TemplateId = monsterTemplateId,
                Pos = Vector3.Zero,
                MountableId = carEntityId,
                MountSeatIndex = slotIndex,
            });
        }

        [RpcHandler(ExposeToClient = true)]
        public static void CallParadeCar(this RpcEntity self, float posX, float posY, float posZ) // 创建巡游车队
        {
            //整合到SpecializedVehicleEntity，不再使用VehicleEntity，业务物理隔离，BaseVehicleEntity后续根据业务看看要不要额外实现
            var EntityId = WorldEntityFactory.CreateEntity(new WorldResourceRecord()
            {
                EntityType = EntityTypeId.SpecializedVehicleEntity,
                TemplateId = 22030007,
                Pos = new Vector3(posX, posY, posZ),
            });
        }

        [RpcHandler(ExposeToClient = true)]
        public static void SetOneMonsterParadeCarSlot(this RpcEntity self, long carEntityId, int monsterTemplateId,
            int slotIndex)
        {
            self.Logger.InfoFormat("SetOneMonsterParadeCarSlot {0}, {1}, {2}", carEntityId, monsterTemplateId, slotIndex);
            var carEntity = EntityManager.Instance.GetEntity<SpecializedVehicleEntity>(carEntityId);
            if (carEntity == null)
            {
                return;
            }
            BuildPassengers(carEntityId, monsterTemplateId, slotIndex, carEntity);
        }


        [RpcHandler(ExposeToClient = true)]
        public static void SetOneBoxParadeCarSlot(this RpcEntity self, long carEntityId, int boxTemplateId,
            int slotIndex)
        {
            self.Logger.InfoFormat("SetOneBoxParadeCarSlot {0}, {1}, {2}", carEntityId, boxTemplateId, slotIndex);
            var carEntity = EntityManager.Instance.GetEntity<SpecializedVehicleEntity>(carEntityId);
            if (carEntity == null)
            {
                return;
            }

            var boxEntityId = WorldEntityFactory.CreateEntity(new WorldResourceRecord()
            {
                EntityType = EntityTypeId.BoxEntity,
                TemplateId = boxTemplateId,
                Pos = Vector3.Zero,
                MountableId = carEntityId,
                MountSeatIndex = slotIndex,
            });
        }

        /// <summary>
        /// 创建世界宝箱Collection和对应的Entity
        /// </summary>
        [RpcHandler]
        public static void CreateBox(this RpcEntity self, CreateBoxRequest boxRequest)
        {
            var param = new BoxEntitySpawnParam()
            {
                SpawnType = boxRequest.SpawnType,
                SourceEntityId = boxRequest.SourceEntityId,
                SrcEntityType = boxRequest.SourceEntityTypeId,
                SrcEntityTemplateId = boxRequest.SourceEntityTemplateId
            };
            var box = new BoxEntity(boxRequest.TemplateId,
                boxRequest.PosX, boxRequest.PosY, boxRequest.PosZ,
                boxRequest.RotateX, boxRequest.RotateX, boxRequest.RotateZ,
                param, boxRequest.EntityId);

            if (boxRequest.SpawnLinkId > 0)
            {
                box.AddComponent(new SpawnComponent() { SpawnLinkId = boxRequest.SpawnLinkId });
            }

            EntityManager.Instance.AddEntity(box);
        }

        /// <summary>
        /// 召唤分解机
        /// </summary>
        /// <param name="netPeerId"></param>
        /// <param name="fromEndpoint"></param>
        /// <param name="posX">玩家位置X</param>
        /// <param name="posZ">玩家位置Z</param>
        [RpcHandler(ExposeToClient = true)]
        public static void TestSummonCarShredder(this RpcEntity self, long templateId, float posX, float posY, float posZ)
        {
            var roleId = RpcContext.CurrentRoleId;
            if (UserManagerEntity.Instance.GetPlayerEntity(roleId) is not PlayerEntity pe || pe.DebugComp == null) return;

            self.Logger.InfoFormat("Summon CarShredder posX:{0}, posY: {1}, posZ{2}, templateId{3}", posX, posY, posZ, templateId);

            var id = IdGeneratorUtil.GenInWorldCommonId();
            var monster = new CarshredderEntity(id);

            monster.PosX = posX;
            monster.PosZ = posZ;
            monster.PosY = posY;
            monster.TemplateId = templateId;
            EntityManager.Instance.AddEntity(monster);
        }

        [RpcHandler]
        public static void SummonMonster(this RpcEntity self, CreateMonsterRequest monsterRequest)
        {
            MonsterRecord monsterRecord = new MonsterRecord();
            monsterRecord.SpecialSpawn = monsterRequest.SpecialSpawn;
            monsterRecord.TargetId = monsterRequest.TargetId;
            monsterRecord.SummonMonsterMaxDis = monsterRequest.SummonMonsterMaxDis;
            WorldEntityFactory.CreateEntity(new WorldResourceRecord()
            {
                EntityType = EntityTypeId.MonsterEntity,
                TemplateId = monsterRequest.TemplateId,
                Pos = new Common.Algorithm.Vector3(monsterRequest.PosX, monsterRequest.PosY, monsterRequest.PosZ),
                MonumentId = monsterRequest.MonumentId,
                Rot = new Vector3(0, monsterRequest.RotateY, 0),
                MonsterRecord = monsterRecord,
            });
        }

        [RpcHandler]
        public static void SetAutoTurretMonster(this RpcEntity self, long partEntityId, long monsterId)
        {
            ConstructionManagerEntity.Instance.SetAutoTurretMonster(partEntityId, monsterId);
        }

        [RpcHandler]
        public static void SetAutoTurretAmmoState(this RpcEntity self, long partEntityId, bool lowAmmo, bool noAmmo)
        {
            ConstructionManagerEntity.Instance.SetAutoTurretAmmoState(partEntityId, lowAmmo, noAmmo);
        }

        [RpcHandler(ExposeToClient = true)]
        public static void SetAutoTurretMode(this RpcEntity self, long entityId, int modeType)
        {
            var roleId = RpcContext.CurrentRoleId;
            var pe = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            ConstructionManagerEntity.Instance.SetAutoTurretMode(pe, entityId, modeType);
        }

        [RpcHandler]
        public static void DestroyPartAutoTurret(this RpcEntity self, long partEntityId)
        {
            var partObj = ConstructionManagerEntity.Instance.GetConstructionById(partEntityId);
            if (partObj == null) return;
            ConstructionManagerEntity.Instance.RequestDestroyPart(partObj.ParentPartEntity, EDestroyPartReason.TurretDestroy);
        }

        /// <summary>
        /// 客户端发起炮塔换弹
        /// </summary>
        [RpcHandler(ExposeToClient = true)]
        public static void WorldAutoTurretReload(this RpcEntity self, long entityId)
        {
            var playerEnt = UserManagerEntity.Instance.GetPlayerEntity(RpcContext.CurrentRoleId);
            var monster = EntityManager.Instance.GetEntity(entityId) as MonsterEntity;
            if (monster == null)
            {
                self.Logger.Info("[Client2WorldRpc]AutoTurretReload, monster is null, entity id: " + entityId);
                return;
            }
            var partEntity = EntityManager.Instance.GetEntity(monster.PartEntityId) as PartEntity;
            if (partEntity == null)
            {
                self.Logger.Info("[Client2WorldRpc]AutoTurretReload, part is null, entity id: " + monster.PartEntityId);
                return;
            }
            // 权限检测失败
            if (!BuildingPrivilegeChecker.Instance.CheckPermissionAndDistance(EPrivilegeType.Immunity, playerEnt, partEntity))
            {
                self.Logger.Info("[Client2WorldRpc]AutoTurretReload fail, check permission fail, entity id: " + entityId + " role id: " + RpcContext.CurrentRoleId);
                return;
            }
            self.RemoteCallUnityDsAutoTurretReload(ERpcTarget.Simulator, entityId);
        }

        // not sure
        /// <summary>
        /// 炮塔切换模式
        /// </summary>
        /// <param name="netPeerId"></param>
        /// <param name="fromPoint"></param>
        /// <param name="userId"></param>
        /// <param name="entityId"></param>
        /// <param name="turretMode"></param>
        [RpcHandler(ExposeToClient = true)]
        public static void WorldAutoTurretRotateDone(this RpcEntity self, long entityId, int curTransitionState)
        {
            self.RemoteCallUnityDsAutoTurretRotateDone(ERpcTarget.Simulator, entityId, curTransitionState);
        }

        [RpcHandler]
        public static void SimulatorPickUpMine(this RpcEntity self, ulong roleId, long mineEntityId)
        {
            var player = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (player == null)
            {
                self.Logger.Info($"SimulatorPickUpMine player not found {roleId}");
                return;
            }

            var ent = EntityManager.Instance.GetEntity(mineEntityId);
            if (ent == null)
            {
                self.Logger.Info($"SimulatorPickUpMine entity not found {mineEntityId}");
                return;
            }

            var rootNode = ent.GetComponent<RootNodeComponent>(EComponentIdEnum.RootNodeComponent);
            if (rootNode == null)
            {
                self.Logger.Error($"SimulatorPickUpMine unknown entity {ent.EntityId} {ent.EntityType}");
                return;
            }

            long itemNodeId = 0;
            foreach (var pair in rootNode.Node2System)
            {
                var item = rootNode.GetNodeById(pair.Key) as BaseItemNode;
                if (item != null)
                {
                    itemNodeId = pair.Key;
                    break;
                }
            }

            var req = new SimulatorPickUpRequest()
            {
                EntityId = ent.EntityId,
                ItemNodeId = itemNodeId,
                TargetPath = new List<long>() { NodeSystemType.PlayerInventory, NodePathConst.Anywhere },
                WithConfirm = false,
            };

            self.SimulatorPickUpItem(roleId, req);
        }

        [RpcHandler]
        public static void SimulatorPickUpItem(this RpcEntity self, ulong roleId, SimulatorPickUpRequest request)
        {
            var ent = EntityManager.Instance.GetEntity(request.EntityId);
            if (ent == null)
            {
                self.Logger.Info($"SimulatorPickUpItem entity not found {request.EntityId}");
                return;
            }

            var rootNode = ent.GetComponent<RootNodeComponent>(EComponentIdEnum.RootNodeComponent);
            if (rootNode == null)
            {
                self.Logger.Error($"SimulatorPickUpItem unknown entity {ent.EntityId} {ent.EntityType}");
                return;
            }

            var pickableDir = rootNode.GetNodeByPath(NodeSystemType.PickableItem, GridNodeIndex.Pickable, request.EntityId) as PickableDirectoryNode;
            if (rootNode == null)
            {
                self.Logger.Error($"SimulatorPickUpItem entity {ent.EntityId} {ent.EntityType}, PickableDirectoryNode is not exist");
                return;
            }

            var source = OpContextConst.PICK_UP;
            if (pickableDir.NodeLifeInfo.TryGetValue(request.ItemNodeId, out var info) && info.FromOil)
            {
                source = OpContextConst.PICK_UP_FROM_OIL_BARREL;
            }
            else if (ent is CollectableEntity)
            {
                source = OpContextConst.PICK_UP_RESOURCE;
            }

            var player = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (player == null)
            {
                self.Logger.Error($"SimulatorPickUpItem Player {roleId} not found");
                return;
            }

            var item = rootNode.GetNodeById(request.ItemNodeId) as BaseItemNode;
            if (item == null)
            {
                // 客户端刷新不及时 就会走这里
                self.Logger.Info($"SimulatorPickUpItem itemNodeId not found, entity: {request.EntityId}, node: {request.ItemNodeId}");
                return;
            }

            var path = item.GetPath();
            path[0] = NodeSystemType.PickUpSystem;
            path[1] = request.EntityId;

            List<long> targetPath;
            if (BizId2SystemTypeConst.BizId2SystemType.TryGetValue(item.BizId, out var systemId))
            {
                targetPath = new List<long>() { systemId, NodePathConst.Anywhere };
            }
            else
            {
                targetPath = request.TargetPath;
            }

            player.ComponentInventory.PickUp(path, targetPath, source);
        }

        [RpcHandler]
        public static void CheckInRoomRet(this RpcEntity self, bool isInRoom, long entityId)
        {
            var ent = EntityManager.Instance.GetEntity(entityId);
            var component = ent?.GetComponent<PlantBoxComponent>(EComponentIdEnum.PlantBox) ?? null;
            component.OnCheckInRoomCallback(isInRoom);
        }

        [RpcHandler]
        public static void DetachedSceneItem(this RpcEntity self, long ownerEntityId, long nodeId, int count)
        {
            if (EntityManager.Instance.GetEntity(ownerEntityId) is not PlayerEntity playerCol)
            {
                self.Logger.Error($"DetachedSceneItem cannot find owner {ownerEntityId}");
                return;
            }

            using var _ = NodeOpContext.GetNew(OpContextConst.THROW_WEAPON_OR_GRENADE).SetOpRoleId(playerCol.RoleId);

            if (playerCol.Root.GetNodeById(nodeId) is not BaseItemNode node)
            {
                self.Logger.Error($"DetachedItemNode itemNode null nodeId:{nodeId}");
                return;
            }

            // 纠正数量
            if (node is StackableItemNode stackNode)
            {
                if (stackNode.Count < count)
                {
                    self.Logger.Warn($"DetachedItemNode {nodeId} count {count} more than stack {stackNode.Count}");
                    count = stackNode.Count;
                }
            }
            else
            {
                if (count > 1)
                {
                    self.Logger.Warn($"DetachedItemNode {nodeId} count {count} more than 1");
                    count = 1;
                }
            }

            var itemPath = node.GetPath();
            var code = playerCol.Root.RequireNode(new NodeOpByIndex(itemPath, new IntCount(count)));
            if (code != EOpCode.Success)
            {
                self.Logger.Warn($"DetachedItemNode fail playerCollectionId:{playerCol.EntityId} nodeId:{nodeId}");
                return;
            }
            node = NodeOpContext.GetCurrent().RequireList.Last() as BaseItemNode;
            if (node == null) return;

            if (GridEntity.SceneItemNodeDic.ContainsKey(nodeId))
            {
                self.Logger.Warn($"DetachedSceneItem {nodeId} duplicate");
                return;
            }

            GridEntity.SceneItemNodeDic.Add(nodeId, node);
        }

        /// <summary>
        /// 只创建附身掉落物Node（插在身上的箭、插在身上的投矛等），不创建对应的Entity
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="entityId">在快捷栏或者手上时候的EntityId</param>
        /// <param name="sceneItemId">掉地上后生成的EntityId</param>
        /// <param name="posX"></param>
        /// <param name="posZ"></param>
        [RpcHandler]
        public static void CreateThrowAttachedSceneItem(this RpcEntity self, long entityId, long sceneItemId, long skinId)
        {
            if (GridEntity.SceneItemNodeDic.TryGetValue(entityId, out var itemNode))
            {
                GridEntity.SceneItemNodeDic.Remove(entityId);
                itemNode.DropAttached(sceneItemId, skinId);
            }
            else
            {
                self.Logger.Error($"CreateAttachedSceneItem SceneItemNodeDic not find entityId {entityId}");
            }
        }

        [RpcHandler]
        public static void CreateAttachedSceneItem(this RpcEntity self, long entityId, long itemId, int count, long skinId)
        {
            NodeSystemHelper.CreateItem(itemId, count).DropAttached(entityId, skinId);
        }

        /// <summary>
        /// 只创建哑弹Node，不创建对应的Entity
        /// </summary>
        [RpcHandler]
        public static void CreateDudThrownItem(this RpcEntity self, long entityId, long itemId, int count)
        {
            NodeSystemHelper.CreateItem(itemId, count).AsThrown(entityId);
        }

        /// <summary>
        /// 创建遥控C4Node，不创建对应的Entity
        /// </summary>
        [RpcHandler]
        public static void CreateRFThrownItem(this RpcEntity self, long entityId, int count)
        {
            var thrownEntity = EntityManager.Instance.GetEntity(entityId) as ThrownEntity;
            if (thrownEntity == null)
            {
                self.Logger.Error($"[CreateRFThrownItem] {entityId} ThrownEntity not found");
                return;
            }
            bool isRFDetonation = thrownEntity.IsRFDetonation;
            if (!isRFDetonation)
            {
                self.Logger.Error($"[CreateRFThrownItem] itemId {entityId} is not TimeBombNode");
            }
            long tableId = thrownEntity.TableId;
            var itemNode = NodeSystemHelper.CreateItem(tableId, 1) as TimeBombNode;
            if (itemNode == null)
            {
                self.Logger.Error($"[CreateRFThrownItem] {entityId} {tableId} is not TimeBombNode");
                return;
            }
            itemNode.IsRFDetonation = true;
            itemNode.Frequency = thrownEntity.Frequency;
            itemNode.AsThrown(entityId);
        }

        /// <summary>
        /// 超时的c4变为为普通掉落物
        /// </summary>
        /// <param name="self"></param>
        /// <param name="entityId"></param>
        /// <param name="pos">掉落物的初始位置</param>
        /// <param name="rot">掉落物的初始旋转</param>
        /// <param name="velocity">掉落物的初始速度</param>
        /// <param name="freq">掉落的c4之前的频率</param>
        [RpcHandler]
        private static void ChangeThrownEntityToSceneItem(this RpcEntity self, long entityId)
        {
            var entity = EntityManager.Instance.GetEntity(entityId) as ThrownEntity;
            if (entity == null)
            {
                self.Logger.Error($"[ChangeThrownEntityToSceneItemEntity] entityId {entityId}");
                return;
            }
            Vector3 rot = new Vector3(entity.RotateX, entity.RotateY, entity.RotateZ);
            Vector3 pos = new Vector3(entity.PosX, entity.PosY, entity.PosZ) + (Quaternion.Euler(rot) * Vector3.Up).normalized * 0.2f;
            var frequency = entity.Frequency;
            long tableId = entity.TableId;
            EntityManager.Instance.RemoveEntity(entity.EntityId);

            if (RFTransmitterNodeHotfix.IsInvalidFrequency(frequency))
            {
                self.Logger.Warn($"[ChangeThrownEntityToSceneItemEntity] {entityId} {tableId} freq {frequency} invalid");
                frequency = RFTransmitterNodeHotfix.RandomFrequency();
            }
            var itemNode = NodeSystemHelper.CreateItem(tableId, 1) as TimeBombNode;
            if (itemNode == null)
            {
                self.Logger.Error($"[ChangeThrownEntityToSceneItemEntity] {entityId} {tableId} is not TimeBombNode");
                return;
            }


            itemNode.Frequency = frequency;
            itemNode.IsRFDetonation = true;
            itemNode.Drop(pos, rot, velocityY: -1f, isNeedInitForce: true);
        }


        /// <summary>
        /// 创建可采集物Node对应的Entity
        /// todo@wq 待世界生成移动到World侧之后，该RPC可能会被删除
        /// </summary>
        [RpcHandler]
        public static void CreateCollectable(this RpcEntity self, long entityId, long templateId, SimpleVector3 pos, SimpleVector3 rot, int spawnType, long spawnLinkId)
        {
            var entity = WorldResourceEntityUtil.Create(entityId, templateId, EntityTypeId.CollectableEntity,
                pos.ToVector3(), rot.ToVector3(), spawnType, null) as CollectableEntity;
            entity.AddCollectable(templateId);
        }

        /// <summary>
        /// 创建掉落物Node和对应的Entity
        /// </summary>
        [RpcHandler]
        public static void CreateSceneItem(this RpcEntity self, long entityId, long templateId, SimpleVector3 pos, SimpleVector3 rot, int spawnType, long spawnLinkId)
        {
            var itemNode = NodeSystemHelper.CreateItem(templateId, 1);
            var newEntity = new SceneItemEntity(entityId)
            {
                TemplateId = templateId,
                PosX = pos.X,
                PosY = pos.Y,
                PosZ = pos.Z,
                RotateX = rot.X,
                RotateY = rot.Y,
                RotateZ = rot.Z,
                Speed = McCommon.Tables.TbGlobalConfig.DroppedInitialVelocityMultiplier,
                InitVelocityX = 0,
                InitVelocityY = 0,
                InitVelocityZ = 0,
                SpawnType = 0,
                Amount = itemNode.GetCount(),
                HorseCanEat = itemNode.HasFlag(ItemFlags.HorseFood),
            };
            EntityManager.Instance.AddEntity(newEntity);
            newEntity.AddSceneItem(itemNode, false);
        }

        [RpcHandler]
        public static void CreateTargetEntity(this RpcEntity self, int tableId, float posX, float posY, float posZ)
        {
            var id = IdGenerator.GenWarZoneUniqueId();
            var entity = new TargetEntity(id)
            {
                TemplateId = tableId,
                PosX = posX,
                PosY = posY,
                PosZ = posZ,
                RotateX = 0,
                RotateY = 0,
                RotateZ = 0,
                SpawnType = 0,
            };
            EntityManager.Instance.AddEntity(entity);
        }

        [RpcHandler]
        public static void CheckBlueprintCreateLegalOver(this RpcEntity self, long requestId, bool passCheck, int errorInfo, string errorParam)
        {
            ConstructionManagerEntity.Instance.CheckBlueprintCreateLegalOver(requestId, passCheck, errorInfo, errorParam);
        }
        [RpcHandler]
        public static void CheckPartLegalOver(this RpcEntity self, long requestId, bool passCheck, int errorCode, string extraInfo)
        {

            ConstructionManagerEntity.Instance.AfterSimulatorCheckCreatePart(requestId, passCheck, errorCode, extraInfo);
            ConstructionManagerEntity.Instance.ResetCurrentPartOpInfo();
            //  ConstructionManagerEntity.Instance.CheckPartLegalOver(requestId, passCheck, errorCode, extraInfo);
        }

        [RpcHandler]
        public static void CheckPlayerBlueprintLegalResp(this RpcEntity self, long requestId, int resultCode,
            string resultParam)
        {
            PlayerConstructionBlueprintManagerEntity.Instance.CheckPlayerBlueprintLegalResp(requestId, resultCode,
                resultParam);
        }


        [RpcHandler]
        public static void CheckComboPartLegalOver(this RpcEntity self, long requestId, bool passCheck, int errorCode)
        {
            ConstructionManagerEntity.Instance.AfterSimulatorCheckCreatePart(requestId, passCheck, errorCode, "");
        }


        [RpcHandler]
        public static void CheckRecoverPartLegalOver(this RpcEntity self, long requestId, bool passCheck, int errorCode)
        {
            ConstructionManagerEntity.Instance.CheckRecoverPartLegalOver(requestId, passCheck, errorCode);
        }

        [RpcHandler]
        public static void CheckDeployUpgradeLegalOver(this RpcEntity self, long requestId, bool passCheck, int errorCode)
        {
            //    ConstructionManagerEntity.Instance.CheckDeployUpgradeLegalOver(requestId, passCheck, errorCode);
            ConstructionManagerEntity.Instance.AfterSimulatorCheckUpgradeDeployPart(requestId, passCheck, errorCode, "");
            ConstructionManagerEntity.Instance.ResetCurrentPartOpInfo();
        }

        [RpcHandler]
        public static void CheckChangeSkinPartLegalOver(this RpcEntity self, long requestId, bool passCheck, int errorCode)
        {
            //   ConstructionManagerEntity.Instance.CheckChangeSkinPartLegalOver(requestId, passCheck, errorCode);
            ConstructionManagerEntity.Instance.AfterSimulatorCheckChangePartSkin(requestId, passCheck, errorCode, "");
            ConstructionManagerEntity.Instance.ResetCurrentPartOpInfo();
        }

        [RpcHandler]
        public static void CheckMoveDeployLegalOver(this RpcEntity self, long requestId, bool passCheck, int errorCode)
        {
            //    ConstructionManagerEntity.Instance.CheckMoveDeployLegalOver(requestId, passCheck, errorCode);
            ConstructionManagerEntity.Instance.AfterSimulatorCheckMoveDeployPart(requestId, passCheck, errorCode, "");
            ConstructionManagerEntity.Instance.ResetCurrentPartOpInfo();
        }

        [RpcHandler]
        public static void CheckRepairDebrisPartLegalOver(this RpcEntity self, long requestId, bool passCheck, int errorCode)
        {
            ConstructionManagerEntity.Instance.AfterSimulatorCheckRepairDebrisPart(requestId, passCheck, errorCode, "");
            ConstructionManagerEntity.Instance.ResetCurrentPartOpInfo();
        }


        [RpcHandler]
        public static void CheckTransformationPartLegalOver(this RpcEntity self, long requestId, bool passCheck, int errorCode, List<long> partList)
        {
            // ConstructionManagerEntity.Instance.CheckTransformationPartLegalOver(requestId, passCheck, errorCode, partList);
            ConstructionManagerEntity.Instance.AfterSimulatorCheckTransformPart(requestId, passCheck, partList, errorCode, "");
            ConstructionManagerEntity.Instance.ResetCurrentPartOpInfo();
        }

        [RpcHandler]
        public static void CheckChangePartLegalOver(this RpcEntity self, long requestId, bool passCheck, int errorCode)
        {
            // ConstructionManagerEntity.Instance.CheckTransformationPartLegalOver(requestId, passCheck, errorCode, partList);
            ConstructionManagerEntity.Instance.AfterSimulatorCheckChangePart(requestId, passCheck, errorCode, "");
            ConstructionManagerEntity.Instance.ResetCurrentPartOpInfo();
        }


        [RpcHandler]
        public static void OnGRpcCommand(this RpcEntity self, long seq, int cmdIdx, string req)
        {
            JSONObject cmd;
            try
            {
                cmd = JSONNode.Parse(req) as JSONObject;
                var ret = IDIPHandlers.Process(cmdIdx, cmd);
                GateRpcHandler.OnGmOperateResponse(seq, ret.ToString());
            }
            catch (Exception e)
            {
                self.Logger.Warn($"OnGRpcCommand parse json exception {e}");

                var ret = new JSONObject();
                ret.Add("result", -1);
                ret.Add("retmsg", $"cannot parse request json {req}");
                GateRpcHandler.OnGmOperateResponse(seq, ret);
            }
        }

        [RpcHandler]
        public static void CreateTeam(this RpcEntity self, long seq, ReadOnlySequence<byte> teamData)
        {
            var req = CreateTeamReq.Parser.ParseFrom(teamData);
            TeamUtil.CreateTeamFromGrpc(seq, req);
        }

        [RpcHandler]
        public static void StartDynamicBattle(this RpcEntity self, long seq, ReadOnlySequence<byte> data)
        {
            var req = StartDynamicBattleReq.Parser.ParseFrom(data);

            ServerControlEntity.Instance.StopAllocatedWaitTimer();

            string token = string.Empty;
            self.Logger.Info($"StartDynamicBattle {req.BattleID} {req.GameSetting} {req.Players}");

            if (ServerControlEntity.Instance is not RoomServerControlEntity controlEntity)
            {
                self.SendStartDynamicBattleRsp(seq, token, StartBattleErrorCode.PLAY_ID_ERROR);
                return;
            }
            if (!controlEntity.CacheStartDynamicBattleParam(req))
            {
                self.SendStartDynamicBattleRsp(seq, token, StartBattleErrorCode.INIT_BATTLE_DATA_FAILED);
                return;
            }

            var code = controlEntity.StartBattle(seq);
            if (code == StartBattleErrorCode.WAIT_ASYNC_OP)
            {
                // 先不回复，等待异步操作完成
                self.Logger.Info($"StartBattle ret WAIT_ASYNC_OP, response delayed.");
                return;
            }
            token = controlEntity.GenToken();
            self.SendStartDynamicBattleRsp(seq, token, code);
        }


        public static void SendStartDynamicBattleRsp(this RpcEntity self, long seq, string token, int code)
        {
            StartDynamicBattleRsp rsp = new() { Token = token };
            self.Logger.Info($"[SendStartDynamicBattleRsp] seq:{seq}, code:{code}");
#if !PUBLISH
            if (ServerControlEntity.IsNewbieDevMode())
            {
                self.Logger.Info($"SendStartDynamicBattleRsp ignored seq {seq}.");
                return;
            }
#endif
            GateRpcHandler.OnStartDynamicBattleResponse(seq, code, rsp);
            if (code != StartBattleErrorCode.SUC)
            {
                self.Logger.Error($"SendStartDynamicBattleRsp failed seq {seq}, code {code}. Stop Server Now.");
                ServerControlEntity.Instance.StopServer(EShutdownReason.StartBattleFail);
            }
        }
        [RpcHandler]
        public static void JoinDynamicBattle(this RpcEntity self, long seq, ReadOnlySequence<byte> data)
        {
            var bombHomeServerControl = ServerControlEntity.Instance as BombHomeServerControlEntity;
            if (bombHomeServerControl == null)
            {
                self.Logger.Error($"JoinDynamicBattle bombHomeServerControl is null, seq {seq}");
                GateRpcHandler.OnJoinDynamicBattle(seq, EOpCode.NotBombHome);
                return;
            }
            var req = JoinDynamicBattleReq.Parser.ParseFrom(data);
            var code = bombHomeServerControl.JoinBattle(seq, req);

            GateRpcHandler.OnJoinDynamicBattle(seq, code);
        }

        [RpcHandler]
        public static void ForceShutdownDynamicBattle(this RpcEntity self, long seq)
        {
            if (ServerCommandLine.Config.Agones == 0)
            {
                self.Logger.Info($"room server not support force shutdown operation! seq {seq}");
                return;
            }
            self.Logger.Info($"ForceShutdownDynamicBattle seq {seq}");
            ServerControlEntity.Instance.StopServer(EShutdownReason.GateForceShutdown);
        }

        /// <summary>
        /// toRoleId接受fromRoleId的组队邀请
        /// </summary>
        /// <param name="seq"></param>
        /// <param name="toRoleId"></param>
        /// <param name="fromRoleId"></param>
        [RpcHandler]
        public static void ReplyTeamInvitationFromLobby(this RpcEntity self, long seq, ReadOnlySequence<byte> data)
        {
            TeamUtil.ReplyTeamInvitationFromLobby(seq, data);
        }

        [RpcHandler]
        public static void PlayerDeleteBattle(this RpcEntity self, ulong roleId)
        {
            var pe = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (pe == null)
            {
                self.Logger.Warn($"Lobby player delete battle but he is not online {roleId}");
                return;
            }
            if (pe.MyTeam != null)
            {
                pe.ComponentTeam.TeamLeaveTeam();
            }
        }

        /// <summary>
        /// 修改物品耐久
        /// </summary>
        /// <remarks>
        /// 注意：不会同时修改对应的IItemEntity的耐久
        /// 如果一个物品有对应的IItemEntity，请尝试修改IItemEntity的耐久，而不是调用本函数
        /// World侧会自动同步IItemEntity的耐久到物品上
        /// </remarks>
        [RpcHandler]
        public static void ItemLoseCondition(this RpcEntity self, long entityId, long uid, float amount)
        {
            if (EntityManager.Instance.GetEntity(entityId)?.GetComponent(EComponentIdEnum.RootNodeComponent) is not RootNodeComponent root)
            {
                self.Logger.ErrorFormat("ItemLoseCondition Cannot find entity {0}", entityId);
                return;
            }
            if (root.GetNodeById(uid) is not BaseItemNode node)
            {
                self.Logger.ErrorFormat("ItemLoseCondition Item uid {0} is not BaseItemNode", uid);
                return;
            }
            using var _ = NodeOpContext.GetNew(OpContextConst.DAMAGED);
            node.SetCondition(node.Condition - amount);
            self.Logger.DebugFormat("ItemLoseCondition {0} Condition to {1}", uid, node.Condition);
        }

        /// <summary>
        /// IO 交互物减耐久
        /// </summary>
        /// <param name="self"></param>
        /// <param name="roleId"></param>
        /// <param name="ioTemplateId">交互表主键</param>
        /// <param name="durabilityItemIndex">使用第几个交互物</param>
        [RpcHandler]
        public static void IoLoseItemDurability(this RpcEntity self, ulong roleId, long ioTemplateId,
            long durabilityItemIndex)
        {
            var ioconfig = McCommon.Tables.TbIOInteractive.GetOrDefault(ioTemplateId);
            if (ioconfig == null || ioconfig.InteractiveItemID.Length < durabilityItemIndex ||
                ioconfig.DurabilityDeductionParam.Length < durabilityItemIndex)
            {
                self.Logger.ErrorFormat(
                    "[LoseItemDurability]Cannot get IOInteractiveConfig! TemplateId: {0}, InteractiveItemID length: {1}, index: {2}",
                    ioTemplateId, ioconfig?.InteractiveItemID.Length, durabilityItemIndex);
                return;
            }

            var interactiveItemId = ioconfig.InteractiveItemID[durabilityItemIndex];
            var durabilityParam = ioconfig.DurabilityDeductionParam[durabilityItemIndex];

            var player = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (player == null)
            {
                self.Logger.Error(
                    $"[LoseItemDurability]Cannot get PlayerCollection or PlayerEntity! RoleId: {roleId}, player is null");
                return;
            }

            var minConditionItem =
                ConsumeItemComponentHotfix.FindBestItemToLoseCondition(player, interactiveItemId, durabilityParam);
            if (minConditionItem == null)
            {
                self.Logger.ErrorFormat("[LoseItemDurability]Cannot get minConditionItem! RoleId: {0}", roleId);
                return;
            }

            using var _ = NodeOpContext.GetNew(OpContextConst.DAMAGED);
            if (player.GetCustomTypeById(minConditionItem.Id) is IItemEntity itemEntity)
            {
                itemEntity.Condition -= durabilityParam;
                minConditionItem.SetCondition(itemEntity.Condition);
            }
            else
            {
                minConditionItem.SetCondition(minConditionItem.Condition - durabilityParam);
            }

            self.Logger.Info($"{player} use card {minConditionItem.BizId}");
            EntityStaticCallback<PlayerEntity>.InvokeAllEventCallback(player, new UseItemEvent(minConditionItem.BizId, 1, player.EntityId));
        }

        [RpcHandler]
        public static void SceneItemLoseCondition(this RpcEntity self, long entityId, float posX, float posZ, long uid, float amount)
        {
            var entity = EntityManager.Instance.GetEntity(entityId);
            // 可能entity在world上删除了
            if (entity == null) return;
            if (entity.GetComponent(EComponentIdEnum.RootNodeComponent) is RootNodeComponent rootNodeComp)
            {
                if (rootNodeComp.GetNodeByPath(NodeSystemType.PickableItem, GridNodeIndex.Pickable, entityId, uid) is UniqueItemNode uin)
                {
                    using var _ = NodeOpContext.GetNew(OpContextConst.DAMAGED);
                    uin.SetCondition(uin.Condition - amount);
                }
                else
                {
                    self.Logger.Info($"Failed to find node to lose condition {entityId} {uid} {amount}, check if combined.");
                }
            }
            else
            {
                self.Logger.Error($"SceneItemLoseCondition entity {entityId} has no root node component");
            }
        }


        [RpcHandler]
        public static void CreateIOCollection(this RpcEntity self, long entityId)
        {
            //直接变成了 IOEntity的组件
            var ioEntity = EntityManager.Instance.GetEntity(entityId) as IOEntity;
            if (ioEntity == null)
            {
                self.Logger.Info($"CreateIOCollection {entityId} not found");
                return;
            }
            ioEntity.AddComponent(new IOComponent());
        }


        /// <summary>
        /// 创建商店/前哨战Collection和对应的Entity
        /// todo@wq 待世界生成移动到World侧之后，该RPC可能会被删除
        /// </summary>
        [RpcHandler]
        public static void CreateShop(this RpcEntity self, CreateShopParam param)
        {
            WorldResourceEntityUtil.Create(param.EntityId, param.ShopId, EntityTypeId.ShopEntity, new Vector3(param.PosX, param.PosY, param.PosZ), new Vector3(param.RotX, param.RotY, 0), param.SpawnType, null);
        }



        [RpcHandler]
        public static void OnBoxEntityHit(this RpcEntity self, long entityId, int hitPart)
        {
            var comp = EntityManager.Instance.GetEntity(entityId)?.GetComponent<BoxComponent>(EComponentIdEnum.Box);
            comp?.OnBoxAttack(hitPart);
        }


        /// <summary>
        /// 采集树 尸体 矿石
        /// </summary>
        [RpcHandler]
        public static void Gather(this RpcEntity self, long playerId, long entityId, float gatherDamage, float destroyFraction, int bonusLevel)
        {
            var entity = EntityManager.Instance.GetEntity(entityId);
            if (entity is not IGatherEntity gatherEntity)
            {
                self.Logger.Warn($"[Gather] entity {entityId} is not gather entity");
                return;
            }
            gatherEntity.Gather(playerId, gatherDamage, destroyFraction, bonusLevel);
        }

        /// <summary>
        /// Bot 专用采集树 油桶 矿石
        /// </summary>
        [RpcHandler]
        public static void MonsterGather(this RpcEntity self, long playerId, long entityId, float gatherDamage)
        {
            var entity = EntityManager.Instance.GetEntity(entityId);
            if (entity is IGatherEntity gatherEntity)
            {
                //logger.Warn($"[MonsterGather] entity {entityId} is gather entity");
                gatherEntity.MonsterGather(playerId, gatherDamage);
                return;
            }

            if (entity is BoxEntity boxEntity)
            {
                if (boxEntity.TryGetIHitable(out IHitableEntity hit))
                {
                    //logger.Warn($"[MonsterGather] entity {entityId} is boxEntity hp = {hit.Hp}");
                    var hp = hit.Hp;
                    hp -= gatherDamage;
                    if (hp < 0)
                        hp = 0;
                    hit.Hp = hp;
                }
            }
        }

        [RpcHandler]
        public static void UpdateInTriggerEntity(this RpcEntity self, long entityId, Dictionary<long, long> addSet, Dictionary<long, long> removeSet)
            => ConstructionManagerEntity.Instance.UpdateInTriggerEntity(entityId, addSet, removeSet);


        [RpcHandler]
        public static void UploadDamegeTlog(this RpcEntity self, ulong roleId, List<DamageTLogInfo> Infos)
        {
            if (TLogUtil.DisableTLog) return;

            List<string> logs = new();
            foreach (var info in Infos)
            {
                if (info.DamageTargetVRoleID == 0 && info.DamageSourceVRoleID == 0)
                    continue;
                PlayerEntity damageTarget = null;
                PlayerEntity damageSource = null;
                if (info.DamageTargetVRoleID != 0)
                {
                    damageTarget = UserManagerEntity.Instance.GetPlayerEntity(info.DamageTargetVRoleID);
                    var damageTargetEntity = UserManagerEntity.Instance.GetPlayerEntity(info.DamageSourceVRoleID);
                    if (damageTarget == null || damageTargetEntity == null)
                    {
                        info.DamageTargetRoleReputation = 0;
                    }
                    else
                    {
                        var reputationComponent = damageTargetEntity.GetComponent<ReputationComponent>(EComponentIdEnum.ReputationComponentId);
                        info.DamageTargetRoleReputation = reputationComponent?.ReputationLevel ?? 0;
                        info.DamageTargetEntityId = damageTarget.EntityId;
                        info.DamageTargetRoleRegisterTime = damageTarget.CreateTimeStamp;
                        info.DamageTargetDeviceLevel = damageTarget.DeviceLevel.ToString();
                    }
                }

                if (info.DamageSourceVRoleID != 0)
                {
                    damageSource = UserManagerEntity.Instance.GetPlayerEntity(info.DamageSourceVRoleID);
                    var damageSourceEntity = UserManagerEntity.Instance.GetPlayerEntity(info.DamageSourceVRoleID);
                    if (damageSource == null || damageSourceEntity == null)
                    {
                        info.DamageSourceRoleReputation = 0;
                    }
                    else
                    {
                        var reputationComponent = damageSourceEntity.GetComponent<ReputationComponent>(EComponentIdEnum.ReputationComponentId);
                        info.DamageTargetRoleReputation = reputationComponent?.ReputationLevel ?? 0;
                        info.DamageSourceEntityId = damageSource.EntityId;
                        info.DamageSourceRoleRegisterTime = damageSource.CreateTimeStamp;
                        info.DamageSourceDeviceLevel = damageSource.DeviceLevel.ToString();
                        info.DamageSourceFlowID = damageSource.CombatStat.DamageFlowId++;
                    }
                    if (roleId == 0) roleId = info.DamageSourceVRoleID;//sim传入roleid一定是受击方，如果为0，尝试用伤害源的赋值，还是0，则是非法数据，不发log
                }
                if (roleId == 0)
                    continue;
                string log;
                if (damageTarget != null)
                    log = $"Damage|{TLogUtil.GetPlayerCommonLogString(damageTarget, info.ToLog())}";
                else if (damageSource != null)
                    log = $"Damage|{TLogUtil.GetPlayerCommonLogString(damageSource, info.ToLog())}";
                else
                {
                    self.Logger.Error($"UploadDamegeTlog error. both target {info.DamageTargetVRoleID} and source {info.DamageSourceVRoleID} are null");
                    continue;
                }
                logs.Add(log);
            }
            TLogUtil.BatchLog(logs);
        }


        [RpcHandler]
        public static void UploadWeaponTLog(this RpcEntity self, ulong roleId, List<WeaponTLogInfo> infoList)
        {
            if (TLogUtil.DisableTLog) return;

            var player = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (player == null)
            {
                self.Logger.Error($"UploadWeaponTLog player col not found. {roleId}");
                return;
            }
            var playerBaseInfo = TLogUtil.GetPlayerCommonLogString(player, null);
            TLogUtil.BatchLog(infoList.Select(info => $"Weapon|{playerBaseInfo}|{info.ToLog()}"));
        }


        [RpcHandler]
        public static void TryConsumeOil(this RpcEntity self, long vehicleId, long bizId, int count)
        {
            VehicleUtil.TryConsumeOil(self.Logger, (vehicleId, code, bidId, count) =>
            {
                RpcEntity.Instance.RemoteCallOnConsumeOilAck(ERpcTarget.Simulator, vehicleId, code, bidId, count);
            }, vehicleId, bizId, count);
        }

        //陷阱类建筑被触发，请求消耗道具
        [RpcHandler]
        public static void TriggerTrap(this RpcEntity self, long entityId, int count)
        {
            var trapCol = EntityManager.Instance.GetEntity(entityId);
            if (trapCol != null && trapCol.GetComponent(EComponentIdEnum.GunTrap) is GunTrapComponent comp)
            {
                var config = McCommon.Tables.TbContainer.DataMap[comp.TrapContainerId];
                if (config != null)
                {
                    long bizId = config.IdDivision[0];
                    comp.ConsumeMaterials(bizId, count);
                }
                else
                {
                    RpcEntity.Instance.RemoteCallOnTrapConsumeAck(ERpcTarget.Simulator, entityId, (int)EOpCode.ItemNotExisted);
                }
            }
            else
            {
                RpcEntity.Instance.RemoteCallOnTrapConsumeAck(ERpcTarget.Simulator, entityId, (int)EOpCode.ArgsError);
            }
        }


        [RpcHandler]
        public static void HorseEatFood(this RpcEntity self, long horseId, long entityId)
        {
            var entity = EntityManager.Instance.GetEntity(entityId);
            if (entity == null)
            {
                self.Logger.Warn($"HorseEatFood entityId:{entityId} not found");
                RpcEntity.Instance.RemoteCallOnHorseEatFoodAck(ERpcTarget.Simulator, horseId, (int)EOpCode.ArgsError, null);
                return;
            }
            var hitchingPostComp = entity.GetComponent<HitchTroughComponent>(EComponentIdEnum.HitchTrough);
            Dictionary<long, int> eatFoods = new();
            // 吃拴马桩的食物
            if (hitchingPostComp != null)
            {
                var eatFoodId = hitchingPostComp.HorseEatFood();
                if (eatFoodId > 0)
                {
                    eatFoods[eatFoodId] = 1;
                }
            }
            else
            {
                var posEntity = entity as IPosition2Entity;
                if (entity is not CollectableEntity && entity is not SceneItemEntity)
                {
                    self.Logger.Error($"entityId:{entityId} is not collectable entity");
                    RpcEntity.Instance.RemoteCallOnHorseEatFoodAck(ERpcTarget.Simulator, horseId, (int)EOpCode.ArgsError, null);
                    return;
                }

                var isColletable = entity is CollectableEntity;
                var rootNode = entity.GetComponent<RootNodeComponent>(EComponentIdEnum.RootNodeComponent);
                if (rootNode == null)
                {
                    self.Logger.Error($"entityId:{entityId} cannot find root node");
                    RpcEntity.Instance.RemoteCallOnHorseEatFoodAck(ERpcTarget.Simulator, horseId, (int)EOpCode.ArgsError, null);
                    return;
                }

                var pickableDir = rootNode.GetNodeById(entityId) as PickableDirectoryNode;
                if (pickableDir == null)
                {
                    self.Logger.Warn($"entityId:{entityId} cannot find pickableDir");
                    RpcEntity.Instance.RemoteCallOnHorseEatFoodAck(ERpcTarget.Simulator, horseId, (int)EOpCode.ArgsError, null);
                    return;
                }

                foreach (var (_, node) in pickableDir)
                {
                    var itemNode = node as BaseItemNode;
                    if (itemNode == null) continue;
                    if (!itemNode.HasFlag(ItemFlags.HorseFood)) continue;
                    // 采集物直接吃完
                    if (isColletable)
                    {
                        eatFoods[itemNode.BizId] = itemNode.GetCount();
                        pickableDir.RemoveChildNode(itemNode);
                    }
                    else
                    {
                        if (itemNode.GetCount() > 1)
                        {
                            var stackItemNode = itemNode as StackableItemNode;
                            --stackItemNode.Count;
                        }
                        else
                        {
                            pickableDir.RemoveChildNode(itemNode);
                        }
                        eatFoods[itemNode.BizId] = 1;
                        break;
                    }
                }
                if (pickableDir.ChildCount == 0 || isColletable)
                {
                    pickableDir.Destroy();
                }
            }

            if (eatFoods.Count > 0)
            {
                RpcEntity.Instance.RemoteCallOnHorseEatFoodAck(ERpcTarget.Simulator, horseId, (int)EOpCode.Success, eatFoods);
                var horseEntity = EntityManager.Instance.GetEntity(horseId) as HorseEntity;
                if (horseEntity != null)
                {
                    horseEntity.ComponentHorse.EatFoodSuccess();
                }
            }
            else
            {
                RpcEntity.Instance.RemoteCallOnHorseEatFoodAck(ERpcTarget.Simulator, horseId, (int)EOpCode.ItemNotExisted, eatFoods);
            }
        }

        [RpcHandler]
        public static void FirstMountIPurchasableMountableEntity(this RpcEntity self, long id, ulong roleId)
        {
            var entity = EntityManager.Instance.GetEntity(id);
            if (entity is not IPurchasableMountableEntity ipme)
            {
                self.Logger.Warn($"FirstMountIPurchasableMountableEntity {entity} is not IPurchasableMountableEntity");
                return;
            }
            ipme.JustBought = false;
            ipme.NotFreedom = false;
        }
        [RpcHandler]
        public static void DropWaterToOtherCollection(this RpcEntity self, long playerId, long waterId, int waterCount, List<long> partIds, List<long> magicIds)
        {
            var plantBoxes = new List<(long, int)>();
            var needWater = 0;
            foreach (var entId in partIds)
            {
                var ent = EntityManager.Instance.GetEntity(entId) as PartEntity;
                if (ent != null)
                {
                    var dropWaterAbility = new DropWaterAbility();
                    var splash = ent.AbilityInvoke<DropWaterAbility, ISplashable>(dropWaterAbility);
                    if (splash != null)
                    {
                        if (splash.SpType == SplashType.AddWater)
                        {
                            plantBoxes.Add((ent.EntityId, splash.GetSplashAmount()));
                            needWater += splash.GetSplashAmount();
                            continue;
                        }

                        var costWater = splash.DoSplash(waterId, waterCount);
                        waterCount -= costWater;

                        if (costWater != 0)
                        {
                            self.Logger.Info($"drop water to oven, costWater:{costWater}, do putoutFire cost");
                        }

                        // 如果剩余水量为0。 就不需要继续了
                        if (waterCount == 0) return;
                    }
                }
            }

            foreach (var magicId in magicIds)
            {
                var magicEntity = EntityManager.Instance.GetEntity(magicId) as MagicFieldEntity;
                if (magicEntity == null)
                {
                    self.Logger.Warn($"DropWaterToOtherCollection {magicId} is not MagicFieldEntity");
                    continue;
                }
                var magicConfig = McCommon.Tables.TbSkillSpellField.GetOrDefault(magicEntity.TemplateId);
                if (magicConfig == null)
                {
                    self.Logger.Warn($"DropWaterToOtherCollection {magicId} no config");
                    continue;
                }

                if (magicConfig.WaterInTake <= 0) continue;

                var expectCost = magicConfig.WaterInTake;
                waterCount -= expectCost;
                EntityManager.Instance.RemoveEntity(magicId);
                self.Logger.Info($"drop water to magic {magicId} {expectCost}");
                if (waterCount <= 0) return;
            }

            // 剩余的是淡水，就可以浇植物箱子
            if (waterCount > 0 && waterId == ItemConst.PureWaterItemId)
            {
                plantBoxes.Sort((a, b) => a.Item2.CompareTo(b.Item2));
                int remainCount = plantBoxes.Count;
                int averageWater = 0;

                foreach (var box in plantBoxes)
                {
                    var plantBoxEntity = EntityManager.Instance.GetEntity(box.Item1);
                    var plantBox = plantBoxEntity.GetComponent<PlantBoxComponent>(EComponentIdEnum.PlantBox);
                    int addWater = 0;
                    if (averageWater == 0)
                    {
                        if (waterCount < box.Item2)
                        {
                            averageWater = Mathf.CeilToInt(waterCount * 1f / remainCount);
                            addWater = averageWater;
                        }
                        else
                        {
                            waterCount -= box.Item2;
                            addWater = box.Item2;
                        }
                    }
                    else
                    {
                        addWater = averageWater;
                    }

                    plantBox.DoSplash(ItemConst.PureWaterItemId, addWater);
                    self.Logger.Info($"BucketAddWater, addWater:{addWater}, do plantAddWater cost");
                    remainCount--;
                }
            }
            else
            {
                if (waterCount == 0)
                {
                    self.Logger.Info($"playerId {playerId} item:{waterId}, water is empty break add water to plantbox");
                }

                if (waterId != ItemConst.PureWaterItemId)
                {
                    self.Logger.Info($"playerId {playerId} item:{waterId}, is salt water break add water to plantbox");
                }
            }
            self.Logger.Info($"playerId {playerId} waterId:{waterId}, cost all water {waterCount}");
        }

        const int SIMULATOR_NEARLY_OVERLOAD_THRESHOLD = 22;
        const int BETA1_CONSERVATIVE_COUNT = 100;
        static int overLoopLimitCount = 0;
        [RpcHandler]
        public static void CollectionIndicatorData(this RpcEntity self, Dictionary<string, double> indicatorData)
        {
            if (ServerConfig.Instance.DisableOnlineLimitByLoopLimit != 1)
            {
                var simulatorLoopTime = indicatorData.GetValueOrDefault("simulator_loop_time", 0);
                if (simulatorLoopTime >= SIMULATOR_NEARLY_OVERLOAD_THRESHOLD)
                {
                    // 最大帧时间超过22ms
                    overLoopLimitCount++;
                    self.Logger.Info($"CollectionIndicatorData max loop time = {simulatorLoopTime}");
                    if (overLoopLimitCount == 10)
                    {
                        self.Logger.Info("Simulator seems to run slow, setting MaxOnlineCount");
                        // 达到10次，说明超过22ms的次数超过低于22ms累计达到10s
                        // 降低同时在线
                        LoginQueueEntity.Instance.SetMaxOnlineCount(BETA1_CONSERVATIVE_COUNT);
                        // 后面继续跑逻辑也没关系，SetMaxOnlineCount()是幂等的
                    }
                }
                else
                {
                    if (overLoopLimitCount > 0)
                    {
                        overLoopLimitCount--;
                    }
                }
            }
            PrometheusMetrics.Instance.CollectIndicatorData(indicatorData);
        }

        [RpcHandler]
        public static void OnPlayerDead(this RpcEntity self, ulong srcRoleId, ulong dstRoleId)
        {
            var playercol = UserManagerEntity.Instance.GetPlayerEntity(srcRoleId);
            playercol.CombatStat.DeadCount++;
        }

        [RpcHandler]
        public static void GatherWithNoDrop(this RpcEntity self, long entityId, float damage)
        {
            var entity = EntityManager.Instance.GetEntity(entityId);
            if (entity == null)
            {
                self.Logger.Info($"[GatherWithNoDrop] entity null {entityId} {damage}");
                return;
            }

            if (entity is not IGatherEntity gatherEntity)
            {
                self.Logger.Error($"[GatherWithNoDrop] entity not gather entity {entityId} {damage}");
                return;
            }

            gatherEntity.GatherWithNoGive(damage);
        }

        [RpcHandler]
        public static void UploadCombatStatTlog(this RpcEntity self, BattleLogInOutTLogInfo info)
        {
            var player = UserManagerEntity.Instance.GetPlayerEntity(info.RoleID);
            if (player != null)
            {
                player.CombatStat.KillCount += info.BattleLogKillCount;
                player.CombatStat.GunKillCount += info.BattleLogGunKillingTimes;
                player.CombatStat.MeleeKillCount += info.BattleLogMeleeKillTimes;
                player.CombatStat.GunHitCount += info.BattleLogGunHitTimes;
                player.CombatStat.GunShotCount += info.BattleLogGunShotTimes;
                player.CombatStat.DamageAmount += (int)(info.BattleLogDamageAmount * 100);
                player.CombatStat.GunDamageAmount += (int)(info.BattleLogGunDamageAmount * 100);
                player.CombatStat.MeleeDamageAmount += (int)(info.BattleLogMeleeDamageAmount * 100);
                player.CombatStat.HeadshotRateRecently = info.BattleLogShootHeadShotRateRecently;
                player.CombatStat.HitRateRecently = info.BattleLogShootHitRateRecently;

                var component = player.GetComponent(EComponentIdEnum.AntiCheatCheck) as AntiCheatReportCheckComponent;
                component?.CheckAbnormalData(info);
            }
        }

        [RpcHandler]
        public static void ThrowWeaponOrGrenade(this RpcEntity self, ulong roleId, long uid, int count)
        {
            var player = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (player.Root.GetNodeById(uid) is not BaseItemNode itemNode)
            {
                self.Logger.Error($"[SimulatorConsumeItemByUid] Cannot find item {uid} to consume. Collection: {player.EntityId}");
                return;
            }
            using var ctx = NodeOpContext.GetNew(OpContextConst.THROW_WEAPON_OR_GRENADE).SetOpRoleId(player.RoleId).SetTriggerBeltAutoSupply(true);
            var ret = player.Root.RequireNode(new NodeOpByIndex(itemNode.GetPath(), new IntCount(count)));
            if (ret != EOpCode.Success)
            {
                self.Logger.Warn($"[SimulatorConsumeItemByUid] fail with code {ret}, bizId = {itemNode.BizId}");
            }
        }

        [RpcHandler]
        public static void RemoveCorpse(this RpcEntity self, long entityId)
        {
            self.Logger.Info($"RemoveCorpse {entityId}");
            var ent = EntityManager.Instance.GetEntity(entityId);
            if (ent is CorpseEntity)
            {
                EntityManager.Instance.RemoveEntity(entityId);
            }
        }

        [RpcHandler]
        public static void CreateRobot(this RpcEntity self, int number)
        {
            for (int i = 0; i < number; i++)
            {
                ulong roleId = (ulong)IdGenerator.GenWarZoneUniqueId();
                var playerEntity = LoginEntityHotfix.CreateAndAddPlayerEntity(roleId, true);
                playerEntity.AddComponent(new PlayerDebugComponent());
            }
        }

        [RpcHandler]
        public static void RemoveCampingTent(this RpcEntity self, ulong roleId)
        {
            var playerEntity = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (playerEntity == null)
            {
                self.Logger.Error($"[RemoveCampingTent] role id:{roleId} is not find.");
                return;
            }

            var campingTentEntityId = playerEntity.CampingTentComp.CampingTentEntityId;
            if (campingTentEntityId == 0)
            {
                self.Logger.Warn($"[RemoveCampingTent] role id:{roleId}, camping tent entity is 0 when try remove.");
                return;
            }

            EntityManager.Instance.RemoveEntity(campingTentEntityId);
            self.Logger.Info($"[RemoveCampingTent] role id:{roleId}, remove camping tent entity:{campingTentEntityId} successfully.");
        }

        [RpcHandler]
        public static void OnGenerateKatyushaMissileSuc(this RpcEntity self, ulong roleId, int triggerId)
        {
            self.Logger.Info($"OnGenerateKatyushaMissileSuc roleId {roleId} triggerId {triggerId}");
            var playerEnt = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            EntityStaticCallback<PlayerEntity>.InvokeStaticCallback(playerEnt, new UnityDsTriggerEvent(triggerId));
        }

        [RpcHandler]
        public static void KickPlayer(this RpcEntity self, long entityId)
        {
            var p = EntityManager.Instance.GetPlayerEntity(entityId);
            if (p == null)
            {
                self.Logger.ErrorFormat("KickPlayer {0} not found", entityId);
                return;
            }
            if (p.CurrentNetPeerId == NetConst.INVALID_NET_PEER_ID) return;

            self.Logger.InfoFormat("KickPlayer {0} {1}", entityId, p.CurrentNetPeerId);
            ProcessEntity.Instance.KickPlayer(p.RoleId, p.CurrentNetPeerId, EFrameworkKickReason.GameLogic);
        }

        [RpcHandler]
        public static void NotifyRoleResourceChange(this RpcEntity self, ReadOnlySequence<byte> data)
        {
            var req = ResourceNotifyReq.Parser.ParseFrom(data);
            var roleId = req.RoleID;
            if (UserManagerEntity.Instance.GetPlayerEntity(roleId) is not PlayerEntity playerEntity)
            {
                self.Logger.Error($"NotifyRoleResourceChange role {roleId} not found");
                return;
            }

            using var ctx = NodeOpContext.GetNew("NotifyRoleResourceChange").SetOpRoleId(roleId);
            foreach (var item in req.Resources)
            {
                var resType = item.RType;
                var itemId = (int)item.ItemID;
                switch (resType)
                {
                    case (int)ENUMResourceType.SKIN:
                        {
                            var skinConfig = McCommon.Tables.TBSkin.GetOrDefault((int)item.ItemID);
                            if (skinConfig == null)
                            {
                                self.Logger.Error($"NotifyRoleResourceChange role {roleId} not found skin config {item.ToString()}");
                                continue;
                            }

                            if (item.Amount <= 0)
                            {
                                self.Logger.Error($"NotifyRoleResourceChange role {roleId} skin amount <= 0 {item.ToString()}");
                                continue;
                            }

                            var expireAt = item.ExpireAt;
                            var skinNode = new SkinNode(itemId, 0, ProcessEntity.Instance.NowTs, 0, expireAt);
                            var code = playerEntity.Root.MergeNode(new NodeOpByExisting(skinNode).WithOverrideSystem(NodeSystemType.Skin));
                            if (code != EOpCode.Success)
                            {
                                self.Logger.Warn($"NotifyRoleResourceChange role {roleId} merge skin node fail {item.ToString()}");
                                continue;
                            }
                            else
                            {
                                self.Logger.Info($"NotifyRoleResourceChange role {roleId} merge skin node success {item.ToString()}");
                            }
                            break;
                        }
                    default:
                        {
                            self.Logger.Error($"NotifyRoleResourceChange role {roleId} not found {item.ToString()}");
                            break;
                        }
                }
            }
        }

        [RpcHandler]
        public static void PushStyleRankChange(this RpcEntity self, ReadOnlySequence<byte> data)
        {
            var req = PushStyleRankChangeReq.Parser.ParseFrom(data);
            var roleId = req.RoleID;
            if (UserManagerEntity.Instance.GetPlayerEntity(roleId) is not PlayerEntity playerEntity)
            {
                self.Logger.Error($"PushStyleRankChange role {roleId} not found");
                return;
            }
            if (playerEntity.ComponentTask?.GetTaskContainer<MedalTaskContainer>(PlayerTaskContainerIndex.Medal) is MedalTaskContainer medalTaskContainer)
            {
                foreach (var (styleId, styleRank) in req.StyleRankUpdates)
                {
                    medalTaskContainer.StyleToRank[(int)styleId] = styleRank;
                }
            }
        }

        [RpcHandler]
        public static void ApplyJoinBattleRecruitment(this RpcEntity self, long seq, ReadOnlySequence<byte> data)
        {
            var req = ApplyJoinBattleRecruitmentReq.Parser.ParseFrom(data);
            var teamId = (long)req.TeamID;
            var teamEntity = EntityManager.Instance.GetEntity((long)teamId) as TeamEntity;
            // 队伍已经解散了， 招募信息已失效
            if (teamEntity == null)
            {
                self.Logger.Info($"ApplyJoinBattleRecruitment team {teamId} not found");
                var rsp = new ApplyJoinBattleRecruitmentRsp() { Success = false };
                rsp.MsgID = CommonTipConst.RecruitmentLapses;
                GateRpcHandler.OnApplyJoinBattleRecruitment(seq, rsp);
                return;
            }

            // 模式不匹配
            if (req.GameMode != ServerInstanceEntity.Instance.GameModeId)
            {
                self.Logger.Info($"ApplyJoinBattleRecruitment team {teamId} game mode not match {req.GameMode} server mode {ServerInstanceEntity.Instance.GameModeId}");
                var rsp = new ApplyJoinBattleRecruitmentRsp() { Success = false };
                rsp.MsgID = CommonTipConst.RecruitmentLapses;
                GateRpcHandler.OnApplyJoinBattleRecruitment(seq, rsp);
                return;
            }

            teamEntity.ApplyJoinBattleRecruitment(seq, req);
        }

        [RpcHandler]
        public static void OnGraphNodeFinish(this RpcEntity self, GraphNodeInfo graphNodeInfo)
        {
            FlowContext context = MgrGraph.Instance.GetFlowContext(graphNodeInfo.GraphInstanceId, graphNodeInfo.ContextId);
            var node = context.GetNode(graphNodeInfo.NodeInstanceId) as IAsyncGraphNode;
            if (node == null)
            {
                self.Logger.Error($"[RuleGraph] OnGraphNodeFinish node {graphNodeInfo} not found");
                return;
            }
            self.Logger.Info($"[RuleGraph] OnGraphNodeFinish node {graphNodeInfo}");
            node.OnFinish(context, graphNodeInfo);
        }


        [RpcHandler]
        public static void GetOneTerritoryOutside(this RpcEntity self)
        {
            var netPeerId = RpcContext.CurrentNetPeerId;
            if (TerritoryManagerEntity.Instance.DirtyPhotoTerritory.Count == 0)
            {
                TerritoryManagerEntity.Instance.NoDirtyPhotoTerritory();
                SendNoTerritoryPacket();

                self.Logger.Debug($"No Territory need Photo");
                return;
            }

            ulong ownerId = 0;
            EntityBase entity = null;
            foreach (var terrId in TerritoryManagerEntity.Instance.DirtyPhotoTerritory)
            {
                entity = EntityManager.Instance.GetEntity(terrId);
                if (entity == null)
                {
                    self.Logger.Error($"GetOneTerritoryOutside territory {terrId} not found");
                    // 这里暂时不用纠错 当里面都是非法的后 会清空
                    continue;
                }
                else
                {
                    if (entity is TerritoryEntity territory)
                    {
                        ownerId = territory.BaseComp.CreatorRoleId;
                    }
                    break;
                }
            }

            if (entity is IPhotoEntity ipe)
            {
                entity.TryGetComponent<OutsideDataSetComponent>(EComponentIdEnum.OutsideDataSet, out var outside);

                string roles = string.Empty;
                if (outside != null)
                {
                    using (var sb = ZString.CreateStringBuilder())
                    {
                        var countMax = 20;
                        var numNow = 0;
                        foreach (var roleId in outside.OpRoleIds)
                        {
                            if (ownerId == roleId) { continue; }
                            if (numNow != 0) { sb.Append(";"); }
                            sb.Append(roleId);
                            if (++numNow >= countMax) { break; }
                        }
                        roles = sb.ToString();
                        // 清空本次建造的参与者信息
                        outside.OpRoleIds.Clear();
                    }
                    var timeNow = TimeStampUtil.GetNowTimeStampMSec();
                    outside.ChangeCount = 0;
                    outside.NxtPhotoTime = timeNow + TerritoryManagerEntityHotfix.PhotoMinimalInterval;
                }

                using var writer = CustomTypeHelper.GetDisposableArrayWriter();
                var seq = ipe.SerializeForPhoto(writer);
                var packet = EntityRpcPacket.GetFromPool().Init(self.EntityId, ComponentBase.INVALID_ID, FixedRpcMethod.RPC_ENTITY_GET_ONE_TERRITORY_OUTSIDE_ACK, ERpcTarget.OwnClient, entity.EntityId, ownerId.ToString(), roles, seq);
                WorldNetAgent.Instance.ServiceListener.SendPacket(netPeerId, packet);
                self.Logger.Info($"get one territory outside {entity.EntityId}, owner {ownerId}, roles {roles}");
                outside.OpRoleIds.Clear();
            }
            else
            {
                self.Logger.Info("no valid territory entity");
                TerritoryManagerEntity.Instance.DirtyPhotoTerritory.Clear();
                TerritoryManagerEntity.Instance.NoDirtyPhotoTerritory();
                SendNoTerritoryPacket();
            }

            void SendNoTerritoryPacket()
            {
                var noTerritoryPacket = EntityRpcPacket.GetFromPool().Init(self.EntityId, ComponentBase.INVALID_ID, FixedRpcMethod.RPC_ENTITY_NO_TERRITORY_OUTSIDE, ERpcTarget.OwnClient);
                WorldNetAgent.Instance.ServiceListener.SendPacket(netPeerId, noTerritoryPacket);
            }
        }

        [RpcHandler]
        public static void TerritoryOutsideDone(this RpcEntity self, long entityId)
        {
            var managerEntity = TerritoryManagerEntity.Instance;
            managerEntity.DirtyPhotoTerritory.Remove(entityId);
            self.Logger.Info($"photo one territory outside done {entityId}");

            if (managerEntity.DirtyPhotoTerritory.Count == 0)
            {
                managerEntity.NoDirtyPhotoTerritory();
            }
        }

        [RpcHandler]
        public static void PushChangeTalents(this RpcEntity self, long seq, ReadOnlySequence<byte> serialized)
        {
            var req = PushChangeTalentsReq.Parser.ParseFrom(serialized);
            var roleId = req.RoleID;
            var playerEntity = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (playerEntity == null)
            {
                self.Logger.Error($"PushChangeTalents player {roleId} not found");
                GateRpcHandler.PushChangeTalentsAck(seq, EOpCode.PlayerNotFound);
                return;
            }

            playerEntity.BuffComp.TalentChange(req);
            GateRpcHandler.PushChangeTalentsAck(seq, EOpCode.Success);
        }

        [RpcHandler]
        public static void PushNewTaskGroups(this RpcEntity self, ReadOnlySequence<byte> data)
        {
            var req = PushNewTaskGroupsReq.Parser.ParseFrom(data);
            var player = UserManagerEntity.Instance.GetPlayerEntity(req.RoleID);
            if (player == null)
            {
                self.Logger.Error($"[PushNewTaskGroups] role {req.RoleID} not found");
                return;
            }

            if (player.ComponentTask == null)
            {
                self.Logger.Error($"[PushNewTaskGroups] role {req.RoleID} has no ComponentTask");
                return;
            }
            var lobbyTaskContainer = player.ComponentTask.GetTaskContainer<LobbyMainTaskContainer>(PlayerTaskContainerIndex.Lobby);
            if (lobbyTaskContainer == null)
            {
                self.Logger.Error($"[PushNewTaskGroups] role {req.RoleID} task component has no LobbyMainTaskContainer");
                return;
            }
            lobbyTaskContainer.AcceptNewTaskFromLobby(req.DSNewTaskGroups);
            self.Logger.Info($"[PushNewTaskGroups] req :{req}");
        }

        [RpcHandler]
        public static void GMTaskFinish(this RpcEntity self, ReadOnlySequence<byte> data)
        {
            var req = GMTaskFinishReq.Parser.ParseFrom(data);
            if (UserManagerEntity.Instance.GetPlayerEntity(req.RoleID) is not PlayerEntity player)
            {
                self.Logger.Error($"[GMTaskFinish] role {req.RoleID} not found");
                return;
            }

            if (player.ComponentTask is not PlayerTaskComponent componentTask)
            {
                self.Logger.Error($"[GMTaskFinish] role {req.RoleID} has no ComponentTask");
                return;
            }

            if (componentTask.GetTaskContainer<LobbyMainTaskContainer>(PlayerTaskContainerIndex.Lobby) is not LobbyMainTaskContainer lobbyTaskContainer)
            {
                self.Logger.Error($"[GMTaskFinish] role {req.RoleID} task component has no LobbyMainTaskContainer");
                return;
            }

            self.Logger.Info($"[GMTaskFinish] Processing task finish request for role {req.RoleID}, taskGroups count: {req.TaskGroups.Count}");

            // 遍历每个任务组，完成其中的每个任务
            foreach (var taskGroup in req.TaskGroups)
            {
                foreach (var taskId in taskGroup.TaskIDs)
                {
                    // 查找进行中的任务节点
                    if (lobbyTaskContainer.GetChildNode((int)TaskNodeIndex.InProgress) is not DirectoryNode inProgressDir)
                    {
                        self.Logger.Warn($"[GMTaskFinish] No in-progress tasks directory for role {req.RoleID}");
                        continue;
                    }

                    if (inProgressDir.GetChildNode(taskId) is not LobbyMainTaskNode taskNode)
                    {
                        self.Logger.Warn($"[GMTaskFinish] Task {taskId} not found in progress tasks for role {req.RoleID}");
                        continue;
                    }

                    // 强制完成所有子任务
                    using var subTaskEnumerator = taskNode.GetEnumeratorWithoutReset();
                    while (subTaskEnumerator.MoveNext())
                    {
                        var (_, subNode) = subTaskEnumerator.Current;
                        if (subNode is SubTaskNode subTaskNode)
                        {
                            if (McCommon.Tables.TbQuestPhase.GetOrDefault(subTaskNode.BizId) is QuestPhase subTaskConfig)
                            {
                                if (subTaskConfig.EndConditionParameter.Length > 0)
                                {
                                    subTaskNode.Count = (int)subTaskConfig.EndConditionParameter[0];
                                    subTaskNode.IsComplete = true;
                                }
                            }
                        }
                    }

                    lobbyTaskContainer.CompleteTask(taskNode);
                }
            }
        }
    }
}