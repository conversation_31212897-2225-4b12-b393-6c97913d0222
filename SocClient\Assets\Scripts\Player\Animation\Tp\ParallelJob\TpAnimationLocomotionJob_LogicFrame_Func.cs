﻿using Unity.Mathematics;
using UnityEngine;
using WizardGames.Soc.Common.Character;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.State.Character;
using WizardGames.Soc.Common.Unity.Animation;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.Parachute;
using WizardGames.Soc.Common.Unity.Utility.Define;

namespace WizardGames.Soc.SocClient.Player.Animation
{
    /// <summary>
    /// 相关func
    /// </summary>
    public partial struct TpAnimationJob
    {
        private void UpdateCrawlState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            if (!InLocomotionInjured(playerLocalData.ELocomotionLayer))
                return;
            ref var playerStateData = ref jobData.PlayerStateData;
            switch (playerLocalData.ELocomotionLayer)
            {
                case AnimParametersTp.ELocomotionLayer.Injured_ToInjured:
                    //进度90%
                    var over = playerLocalData.TpClipCollect.NowLocState.Percent >= 0.75f;
                    if (over)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Injured_InjuredIdle_NameId,
                            AnimParametersTp.ELocomotionLayer.Injured_InjuredIdle,
                            0.25f, 0, true, true);
                    }

                    break;
                case AnimParametersTp.ELocomotionLayer.Injured_InjuredIdle:
                    if (playerStateData.PoseDyingState != PlayerPoseDyingStateEnum.Crawl)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_LocomotionEmpty_NameId,
                            AnimParametersTp.ELocomotionLayer.LocomotionEmpty,
                            0f, 0f, true, true);
                        return;
                    }

                    if (playerStateData.Movement8Direction != 0)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Injured_InjuredMove_NameId,
                            AnimParametersTp.ELocomotionLayer.Injured_InjuredMove,
                            0.1f, 0, true, true);
                    }

                    break;
                case AnimParametersTp.ELocomotionLayer.Injured_InjuredMove:
                    if (playerStateData.PoseDyingState != PlayerPoseDyingStateEnum.Crawl)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_LocomotionEmpty_NameId,
                            AnimParametersTp.ELocomotionLayer.LocomotionEmpty,
                            0f, 0f, true, true);
                        return;
                    }

                    if (playerStateData.Movement8Direction == 0)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Injured_InjuredIdle_NameId,
                            AnimParametersTp.ELocomotionLayer.Injured_InjuredIdle,
                            0.1f, 0, true, true);
                    }

                    break;
            }
        }

        private void UpdateLieDownState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var inCap = playerLocalData.ELocomotionLayer == AnimParametersTp.ELocomotionLayer.InCap_LieDownLoop ||
                        playerLocalData.ELocomotionLayer == AnimParametersTp.ELocomotionLayer.InCap_ToLieDown;
            if (!inCap)
                return;
            ref var playerStateData = ref jobData.PlayerStateData;
            //读取进度
            switch (playerLocalData.ELocomotionLayer)
            {
                case AnimParametersTp.ELocomotionLayer.InCap_ToLieDown:
                    //进度90%
                    var over = playerLocalData.TpClipCollect.NowLocState.Percent >= .9f;
                    if (over)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_InCap_LieDownLoop_NameId,
                            AnimParametersTp.ELocomotionLayer.InCap_LieDownLoop,
                            0.1f, 0, true, true);
                    }

                    break;
                case AnimParametersTp.ELocomotionLayer.InCap_LieDownLoop:
                    if (playerStateData.PoseDyingState != PlayerPoseDyingStateEnum.Incapacitated)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_LocomotionEmpty_NameId,
                            AnimParametersTp.ELocomotionLayer.LocomotionEmpty,
                            0f, 0f, true, true);
                    }

                    break;
            }
        }

        private void UpdateRiderLocomotion(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData, bool isRide)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            if (!InLocomotionRiderLocomotion(playerLocalData.ELocomotionLayer))
                return;
            ref var vehicleData = ref jobData.VehicleData;
            if (isRide == false)
            {
                LogicToStandDetailState(ref jobData, ref resultJobData, ref animParams, constData, 0f, true);
            }
            else if (vehicleData.HorseAniType == (int)EHorseAnimType.Jump)
            {
                ToRiderJumpState(ref jobData, ref resultJobData, 0f, 0, true);
            }
            else if (vehicleData.HorseAniType == (int)EHorseAnimType.Halter)
            {
                ToLocomotionLayerState(ref jobData, ref resultJobData,
                    AnimParametersTp.LocomotionLayer_RiderJump_Neigh_NameId,
                    AnimParametersTp.ELocomotionLayer.RiderJump_Neigh,
                    0, 0, true);
            }
        }

        private void UpdateRiderHalter(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData, bool isRide)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            if (!InLocomotionRiderHalter(playerLocalData.ELocomotionLayer))
                return;
            ref var vehicleData = ref jobData.VehicleData;
            if (isRide == false)
            {
                LogicToStandDetailState(ref jobData, ref resultJobData, ref animParams, constData, 0f, true);
            }
            else if (vehicleData.HorseAniType == (int)EHorseAnimType.Jump)
            {
                ToRiderJumpState(ref jobData, ref resultJobData, 0f, 0, true);
            }
            else if (vehicleData.HorseAniType == (int)EHorseAnimType.Halter)
            {
            }
            else
            {
                ToLocomotionLayerState(ref jobData, ref resultJobData,
                    AnimParametersTp.LocomotionLayer_RiderLocomotion_RiderLocomotionNode_NameId,
                    AnimParametersTp.ELocomotionLayer.RiderLocomotion_RiderLocomotionNode,
                    0, 0, true);
            }
        }

        private void UpdateRiderJump(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData, bool isRide)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            if (!InLocomotionRiderJump(playerLocalData.ELocomotionLayer))
                return;
            ref var vehicleData = ref jobData.VehicleData;
            if (isRide == false)
            {
                LogicToStandDetailState(ref jobData, ref resultJobData, ref animParams, constData, 0f, true);
            }
            else if (vehicleData.HorseAniType == (int)EHorseAnimType.Halter)
            {
                ToLocomotionLayerState(ref jobData, ref resultJobData,
                    AnimParametersTp.LocomotionLayer_RiderJump_Neigh_NameId,
                    AnimParametersTp.ELocomotionLayer.RiderJump_Neigh,
                    0, 0, true);
            }
            else if (vehicleData.HorseAniType == (int)EHorseAnimType.Jump)
            {
                KeepingHorseJumpState(ref jobData, ref resultJobData, 0f, 0, false);
            }
            else
            {
                ToLocomotionLayerState(ref jobData, ref resultJobData,
                    AnimParametersTp.LocomotionLayer_RiderLocomotion_RiderLocomotionNode_NameId,
                    AnimParametersTp.ELocomotionLayer.RiderLocomotion_RiderLocomotionNode,
                    0, 0, true);
            }
        }

        private void ToRiderJumpState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            float transitionTime, float normalizeTime, bool fixTime)
        {
            ref var vehicleData = ref jobData.VehicleData;
            var jumpState = vehicleData.HorseJumpType;
            switch (jumpState)
            {
                case (int)EHorseJumpAnimType.JumpStart:
                    ToHorseJumpStartState(ref jobData, ref resultJobData, transitionTime, normalizeTime, fixTime);
                    break;
                case (int)EHorseJumpAnimType.InAir:
                    ToHorseJumpLoopState(ref jobData, ref resultJobData, transitionTime, normalizeTime, fixTime);
                    break;
                case (int)EHorseJumpAnimType.JumpEnd:
                    ToHorseJumpEndState(ref jobData, ref resultJobData, transitionTime, normalizeTime, fixTime);
                    break;
            }
        }


        private void UpdateSwimIdleState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            ref var playerStateData = ref jobData.PlayerStateData;
            var inSwim = InLocomotionSwimIdle(playerLocalData.ELocomotionLayer);
            var isSwim = playerStateData.PoseState == PlayerPoseStateEnum.Dive ||
                         playerStateData.PoseState == PlayerPoseStateEnum.Swim;
            if (!inSwim)
                return;
            if (isSwim)
            {
                if (playerStateData.Movement8Direction != 0)
                {
                    SwitchSwimLocomotion(ref jobData, ref resultJobData, constData, 0.15f, 0f, true);
                }
            }
            else
            {
                var tpLerpSpeed = new float3(playerLocalData.TpLerpAniSpeedX, 0, playerLocalData.TpLerpAniSpeedZ);
                var lerpSpeedValue = math.length(tpLerpSpeed);
                if (playerStateData.Movement8Direction == 0 &&
                    lerpSpeedValue < TpAniConstData.MoveToIdleSpeedThreshold)
                {
                    LogicToStandDetailState(ref jobData, ref resultJobData, ref animParams, constData, 0.15f, true);
                }
                else if (playerStateData.MoveState == PlayerMoveStateEnum.Sprint)
                {
                    //疾跑
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_SprintF_NameId,
                        AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_SprintF, 0.15f, 0, true,
                        transitFromSnapshotPose: transitFromSnapshotPose);
                }
                else
                {
                    SwitchHipLocomotion(ref jobData, ref resultJobData, ref animParams, in constData, 0.15f, 0f, true);
                }
            }
        }

        private void UpdateSwimJogState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            ref var playerStateData = ref jobData.PlayerStateData;
            var inSwim = InLocomotionSwimJog(playerLocalData.ELocomotionLayer);
            var isSwim = playerStateData.PoseState == PlayerPoseStateEnum.Dive ||
                         playerStateData.PoseState == PlayerPoseStateEnum.Swim;
            if (!inSwim)
                return;
            if (isSwim)
            {
                if (playerStateData.Movement8Direction == 0)
                {
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_Swim_SwimIdle_NameId,
                        AnimParametersTp.ELocomotionLayer.Swim_SwimIdle, 0.15f, 0, true,
                        transitFromSnapshotPose: transitFromSnapshotPose);
                }
                else if (playerStateData.MoveSwimState == PlayerMoveSwimStateEnum.SwimSprint)
                {
                    //疾跑
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_Swim_Swim_SprintF_NameId,
                        AnimParametersTp.ELocomotionLayer.Swim_Swim_SprintF, 0.15f, 0, true,
                        transitFromSnapshotPose: transitFromSnapshotPose);
                }
                else
                {
                    SwitchSwimLocomotion(ref jobData, ref resultJobData, constData, 0.15f, 0f, true);
                }
            }
            else
            {
                var tpLerpSpeed = new float3(playerLocalData.TpLerpAniSpeedX, 0, playerLocalData.TpLerpAniSpeedZ);
                var lerpSpeedValue = math.length(tpLerpSpeed);
                if (playerStateData.Movement8Direction == 0 &&
                    lerpSpeedValue < TpAniConstData.MoveToIdleSpeedThreshold)
                {
                    LogicToStandDetailState(ref jobData, ref resultJobData, ref animParams, constData, 0.15f, true);
                }
                else if (playerStateData.MoveState == PlayerMoveStateEnum.Sprint)
                {
                    //疾跑
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_SprintF_NameId,
                        AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_SprintF, 0.15f, 0, true,
                        transitFromSnapshotPose: transitFromSnapshotPose);
                }
                else
                {
                    var transitionTime = 0.15f;
                    SwitchHipLocomotion(ref jobData, ref resultJobData, ref animParams, in constData, transitionTime,
                        0f, true);
                }
            }
        }

        private void UpdateSwimSprintState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            ref var playerStateData = ref jobData.PlayerStateData;
            var inSwim = InLocomotionSwimSprint(playerLocalData.ELocomotionLayer);
            var isSwim = playerStateData.PoseState == PlayerPoseStateEnum.Dive ||
                         playerStateData.PoseState == PlayerPoseStateEnum.Swim;
            if (!inSwim)
                return;
            if (isSwim)
            {
                if (playerStateData.Movement8Direction == 0)
                {
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_Swim_SwimIdle_NameId,
                        AnimParametersTp.ELocomotionLayer.Swim_SwimIdle, 0f, 0, true,
                        transitFromSnapshotPose: transitFromSnapshotPose);
                }
                else if (playerStateData.MoveSwimState != PlayerMoveSwimStateEnum.SwimSprint &&
                         playerStateData.Movement8Direction != 0)
                {
                    SwitchSwimLocomotion(ref jobData, ref resultJobData, constData, 0.15f, 0f, true);
                }
            }
            else
            {
                var tpLerpSpeed = new float3(playerLocalData.TpLerpAniSpeedX, 0, playerLocalData.TpLerpAniSpeedZ);
                var lerpSpeedValue = math.length(tpLerpSpeed);
                if (playerStateData.Movement8Direction == 0 &&
                    lerpSpeedValue <
                    TpAniConstData
                        .MoveToIdleSpeedThreshold) //new Vector3(entity.SpeedX,0,entity.SpeedZ).magnitude<0.1f)
                {
                    LogicToStandDetailState(ref jobData, ref resultJobData, ref animParams, constData, 0.15f, true);
                }
                else if (playerStateData.MoveState == PlayerMoveStateEnum.Sprint)
                {
                    //疾跑
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_SprintF_NameId,
                        AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_SprintF, 0.15f, 0, true,
                        transitFromSnapshotPose: transitFromSnapshotPose);
                }
                else if (playerStateData.MoveState != PlayerMoveStateEnum.Sprint &&
                         playerStateData.Movement8Direction != 0)
                {
                    SwitchHipLocomotion(ref jobData, ref resultJobData, ref animParams, in constData, 0.1f, 0f, true);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="jobData"></param>
        /// <param name="resultJobData"></param>
        /// <param name="animParams"></param>
        /// <param name="constData"></param>
        /// <param name="transitionTime"></param>
        /// <param name="fixTime"></param>
        /// <returns></returns>
        private void LogicToStandDetailState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams,
            TpAniConstData constData, float transitionTime, bool fixTime)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            playerLocalData.LogicWantToIdleState = true;
            playerLocalData.LogicWantToIdleStateTransitionTime = transitionTime;
        }



        /// <summary>
        /// 进入站蹲的状态
        /// </summary>
        /// <returns>true要跳转，false不需要</returns>
        private bool ToStandDetailState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams,
            TpAniConstData constData, float transitionTime, bool fixTime)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            //设置相关数据
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var nowPoseState = playerStateData.PoseState;
            var nextType = AnimParametersTp.ELocomotionLayer.Sum;
            var forceToStandIdle = playerStateData.PoseState == PlayerPoseStateEnum.Drive ||
                                   playerStateData.PoseState == PlayerPoseStateEnum.Dive;
            if (forceToStandIdle)
            {
                nextType = AnimParametersTp.ELocomotionLayer.HipStance_HipStandIdle;
            }

            var normalizeTime = 0f;
            if (nextType == AnimParametersTp.ELocomotionLayer.Sum)
            {
                switch (playerLocalData.ELocomotionLayer)
                {
                    case AnimParametersTp.ELocomotionLayer.HipStance_HipStandIdle:
                        CommonStandCrouchChangeLogic(ref jobData, ref resultJobData, ref nextType, ref normalizeTime,
                            ref transitionTime);
                        break;
                    case AnimParametersTp.ELocomotionLayer.HipStance_Stand2Crouch:
                        var scProgress = 0f;
                        switch (nowPoseState)
                        {
                            case PlayerPoseStateEnum.Stand:
                                if (playerLocalData.TurnInPlaceDirection == 1)
                                {
                                    //触发原地转身
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRStand;
                                    transitionTime = playerLocalData.LerpStandCrouchTimeTp / 1000.0f;
                                }
                                else if (playerLocalData.TurnInPlaceDirection == -1)
                                {
                                    //触发原地转身
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLStand;
                                    transitionTime = playerLocalData.LerpStandCrouchTimeTp / 1000.0f;
                                }
                                else
                                {
                                    //cs
                                    scProgress = GetStateNormalizedTimeLocomotionLayer(ref jobData,
                                        AnimParametersTp.LocomotionLayer_HipStance_Stand2Crouch_NameId);
                                    scProgress = math.clamp(scProgress, 0, 1);
                                    normalizeTime =
                                        math.clamp(1 - scProgress, 0, 1) * TpAniConstData.LerpStandCrouchTime /
                                        1000.0f;
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_Crouch2Stand;
                                }

                                break;
                            case PlayerPoseStateEnum.Crouch:
                                if (playerLocalData.TurnInPlaceDirection == 1)
                                {
                                    //触发原地转身
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRCrouch;
                                    transitionTime = playerLocalData.LerpStandCrouchTimeTp / 1000.0f;
                                }
                                else if (playerLocalData.TurnInPlaceDirection == -1)
                                {
                                    //触发原地转身
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLCrouch;
                                    transitionTime = playerLocalData.LerpStandCrouchTimeTp / 1000.0f;
                                }
                                else
                                {
                                    //scr
                                    scProgress = GetStateNormalizedTimeLocomotionLayer(ref jobData,
                                        AnimParametersTp.LocomotionLayer_HipStance_Stand2Crouch_NameId);
                                    scProgress = math.clamp(scProgress, 0, 1);
                                    if (Approximately(scProgress, 1.0f))
                                    {
                                        nextType = AnimParametersTp.ELocomotionLayer.HipStance_Stand2CrouchReady;
                                    }
                                }

                                break;
                        }

                        break;
                    case AnimParametersTp.ELocomotionLayer.HipStance_Stand2CrouchReady:
                        switch (nowPoseState)
                        {
                            case PlayerPoseStateEnum.Stand:
                                if (playerLocalData.TurnInPlaceDirection == 1)
                                {
                                    //触发原地转身
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRStand;
                                    transitionTime = playerLocalData.LerpStandCrouchTimeTp / 1000.0f;
                                }
                                else if (playerLocalData.TurnInPlaceDirection == -1)
                                {
                                    //触发原地转身
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLStand;
                                    transitionTime = playerLocalData.LerpStandCrouchTimeTp / 1000.0f;
                                }
                                else
                                {
                                    //cs
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_Crouch2Stand;
                                }

                                break;
                            case PlayerPoseStateEnum.Crouch:
                                if (playerLocalData.TurnInPlaceDirection == 1)
                                {
                                    //触发原地转身
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRCrouch;
                                    transitionTime = 0f;
                                }
                                else if (playerLocalData.TurnInPlaceDirection == -1)
                                {
                                    //触发原地转身
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLCrouch;
                                    transitionTime = 0f;
                                }
                                else
                                {
                                    //c
                                    var scReadyProgress = GetStateNormalizedTimeLocomotionLayer(ref jobData,
                                        AnimParametersTp.LocomotionLayer_HipStance_Stand2CrouchReady_NameId);
                                    scReadyProgress = math.clamp(scReadyProgress, 0, 1);
                                    if (Approximately(scReadyProgress, 1.0f))
                                    {
                                        nextType = AnimParametersTp.ELocomotionLayer.HipStance_HipCrouchIdle;
                                    }
                                }

                                break;
                        }

                        break;
                    case AnimParametersTp.ELocomotionLayer.HipStance_HipCrouchIdle:
                        CommonStandCrouchChangeLogic(ref jobData, ref resultJobData, ref nextType, ref normalizeTime,
                            ref transitionTime);
                        break;
                    case AnimParametersTp.ELocomotionLayer.HipStance_Crouch2Stand:
                        var csProgress = 0f;
                        switch (nowPoseState)
                        {
                            case PlayerPoseStateEnum.Stand:
                                if (playerLocalData.TurnInPlaceDirection == 1)
                                {
                                    //触发原地转身
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRStand;
                                    transitionTime = playerLocalData.LerpStandCrouchTimeTp / 1000.0f;
                                }
                                else if (playerLocalData.TurnInPlaceDirection == -1)
                                {
                                    //触发原地转身
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLStand;
                                    transitionTime = playerLocalData.LerpStandCrouchTimeTp / 1000.0f;
                                }
                                else
                                {
                                    //csr
                                    csProgress = GetStateNormalizedTimeLocomotionLayer(ref jobData,
                                        AnimParametersTp.LocomotionLayer_HipStance_Crouch2Stand_NameId);
                                    csProgress = math.clamp(csProgress, 0, 1);
                                    if (Approximately(csProgress, 1.0f))
                                    {
                                        nextType = AnimParametersTp.ELocomotionLayer.HipStance_Crouch2StandReady;
                                    }
                                }

                                break;
                            case PlayerPoseStateEnum.Crouch:
                                if (playerLocalData.TurnInPlaceDirection == 1)
                                {
                                    //触发原地转身
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRCrouch;
                                    transitionTime = playerLocalData.LerpStandCrouchTimeTp / 1000.0f;
                                }
                                else if (playerLocalData.TurnInPlaceDirection == -1)
                                {
                                    //触发原地转身
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLCrouch;
                                    transitionTime = playerLocalData.LerpStandCrouchTimeTp / 1000.0f;
                                }
                                else
                                {
                                    //sc
                                    csProgress = GetStateNormalizedTimeLocomotionLayer(ref jobData,
                                        AnimParametersTp.LocomotionLayer_HipStance_Crouch2Stand_NameId);
                                    csProgress = math.clamp(csProgress, 0, 1);
                                    normalizeTime =
                                        math.clamp(1 - csProgress, 0, 1) * TpAniConstData.LerpStandCrouchTime /
                                        1000.0f;
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_Stand2Crouch;
                                }

                                break;
                        }

                        break;
                    case AnimParametersTp.ELocomotionLayer.HipStance_Crouch2StandReady:
                        switch (nowPoseState)
                        {
                            case PlayerPoseStateEnum.Stand:
                                if (playerLocalData.TurnInPlaceDirection == 1)
                                {
                                    //触发原地转身
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRStand;
                                    transitionTime = 0f;
                                }
                                else if (playerLocalData.TurnInPlaceDirection == -1)
                                {
                                    //触发原地转身
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLStand;
                                    transitionTime = 0f;
                                }
                                else
                                {
                                    //s
                                    var csReadyProgress = GetStateNormalizedTimeLocomotionLayer(ref jobData,
                                        AnimParametersTp.LocomotionLayer_HipStance_Crouch2StandReady_NameId);
                                    csReadyProgress = math.clamp(csReadyProgress, 0, 1);
                                    if (Approximately(csReadyProgress, 1.0f))
                                    {
                                        nextType = AnimParametersTp.ELocomotionLayer.HipStance_HipStandIdle;
                                    }
                                }

                                break;
                            case PlayerPoseStateEnum.Crouch:
                                if (playerLocalData.TurnInPlaceDirection == 1)
                                {
                                    //触发原地转身
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRCrouch;
                                    transitionTime = playerLocalData.LerpStandCrouchTimeTp / 1000.0f;
                                }
                                else if (playerLocalData.TurnInPlaceDirection == -1)
                                {
                                    //触发原地转身
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLCrouch;
                                    transitionTime = playerLocalData.LerpStandCrouchTimeTp / 1000.0f;
                                }
                                else
                                {
                                    //sc
                                    nextType = AnimParametersTp.ELocomotionLayer.HipStance_Stand2Crouch;
                                }

                                break;
                        }

                        break;
                    case AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRStand:
                    case AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLStand:
                    case AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLCrouch:
                    case AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRCrouch:
                        CommonStandCrouchChangeLogic(ref jobData, ref resultJobData, ref nextType, ref normalizeTime,
                            ref transitionTime);
                        break;
                    default:
                        CommonStandCrouchChangeLogic(ref jobData, ref resultJobData, ref nextType, ref normalizeTime,
                            ref transitionTime);
                        break;
                }
            }

            var inTip = nextType == AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLCrouch ||
                        nextType == AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRCrouch ||
                        nextType == AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLStand ||
                        nextType == AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRStand;

            if (nextType == playerLocalData.ELocomotionLayer && !inTip)
                return false;
            var vehicleType = GetVehicleType(ref jobData, constData, out var isDriver, out var isPassenger);
            playerLocalData.TpToStanceTime = transitionTime;
            switch (nextType)
            {
                case AnimParametersTp.ELocomotionLayer.HipStance_HipStandIdle:
                    var tempBone = (VehicleType)vehicleType == VehicleType.Parachute
                        ? GetLocomotionLayerBone(ref jobData, ref resultJobData,
                            AnimParametersTp.ELocomotionLayer.Parachute_Start)
                        : GetLocomotionLayerBone(ref jobData, ref resultJobData,
                            AnimParametersTp.ELocomotionLayer.HipStance_HipStandIdle);
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_HipStance_HipStandIdle_NameId,
                        AnimParametersTp.ELocomotionLayer.HipStance_HipStandIdle, transitionTime, normalizeTime,
                        fixTime, ocBoneFlag:true, ocAniBoneMask: tempBone);
                    return true;
                case AnimParametersTp.ELocomotionLayer.HipStance_Stand2Crouch:
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_HipStance_Stand2Crouch_NameId,
                        AnimParametersTp.ELocomotionLayer.HipStance_Stand2Crouch, transitionTime, normalizeTime,
                        fixTime, 
                        transitFromSnapshotPose: transitFromSnapshotPose);
                    return true;
                case AnimParametersTp.ELocomotionLayer.HipStance_Stand2CrouchReady:
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_HipStance_Stand2CrouchReady_NameId,
                        AnimParametersTp.ELocomotionLayer.HipStance_Stand2CrouchReady, transitionTime, normalizeTime,
                        fixTime);
                    return true;
                case AnimParametersTp.ELocomotionLayer.HipStance_HipCrouchIdle:
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_HipStance_HipCrouchIdle_NameId,
                        AnimParametersTp.ELocomotionLayer.HipStance_HipCrouchIdle, transitionTime, normalizeTime,
                        fixTime);
                    return true;
                case AnimParametersTp.ELocomotionLayer.HipStance_Crouch2Stand:
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_HipStance_Crouch2Stand_NameId,
                        AnimParametersTp.ELocomotionLayer.HipStance_Crouch2Stand, transitionTime, normalizeTime,
                        fixTime);
                    return true;
                case AnimParametersTp.ELocomotionLayer.HipStance_Crouch2StandReady:
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_HipStance_Crouch2StandReady_NameId,
                        AnimParametersTp.ELocomotionLayer.HipStance_Crouch2StandReady, transitionTime, normalizeTime,
                        fixTime);
                    return true;
                case AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLStand:
                    normalizeTime = ApplyTurnInPlaceAnim(ref jobData, ref resultJobData, ref animParams, constData);
                    //transitionTime算百分比
                    //transitionTime /= 1f;
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_HipStance_TurnInPlaceLStand_NameId,
                        AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLStand, transitionTime, normalizeTime,
                        false);
                    //playerLocalData.TpToStanceTime = transitionTime;
                    return true;
                case AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRStand:
                    normalizeTime = ApplyTurnInPlaceAnim(ref jobData, ref resultJobData, ref animParams, constData);
                    //transitionTime算百分比
                    //transitionTime /= 1f;
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_HipStance_TurnInPlaceRStand_NameId,
                        AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRStand, transitionTime, normalizeTime,
                        false);
                    //playerLocalData.TpToStanceTime = transitionTime;
                    return true;
                case AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLCrouch:
                    normalizeTime = ApplyTurnInPlaceAnim(ref jobData, ref resultJobData, ref animParams, constData);
                    //transitionTime算百分比
                    //transitionTime /= 1f;
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_HipStance_TurnInPlaceLCrouch_NameId,
                        AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLCrouch, transitionTime, normalizeTime,
                        false);
                    //playerLocalData.TpToStanceTime = transitionTime;
                    return true;
                case AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRCrouch:
                    normalizeTime = ApplyTurnInPlaceAnim(ref jobData, ref resultJobData, ref animParams, constData);
                    //transitionTime算百分比
                    //transitionTime /= 1f;
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_HipStance_TurnInPlaceRCrouch_NameId,
                        AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRCrouch, transitionTime, normalizeTime,
                        false);
                    //playerLocalData.TpToStanceTime = transitionTime;
                    break;
            }
            return false;
        }

        /// <summary>
        /// 通用的站蹲切换逻辑
        /// </summary>
        /// <returns></returns>
        private void CommonStandCrouchChangeLogic(ref TpAnimationJobData jobData,
            ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp.ELocomotionLayer nextType, ref float normalizeTime, ref float transitionTime)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            //设置相关数据
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var nowPoseState = playerStateData.PoseState;
            var lerpTime = playerLocalData.LerpStandCrouchTimeTp;
            switch (nowPoseState)
            {
                case PlayerPoseStateEnum.Stand:
                    if (playerLocalData.TurnInPlaceDirection == 1)
                    {
                        //触发原地转身
                        nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRStand;
                        if (lerpTime > 0)
                        {
                            transitionTime = playerLocalData.LerpStandCrouchTimeTp / 1000.0f;
                        }
                        else
                        {
                            transitionTime = 0f;
                        }
                    }
                    else if (playerLocalData.TurnInPlaceDirection == -1)
                    {
                        //触发原地转身
                        nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLStand;
                        if (lerpTime > 0)
                        {
                            transitionTime = playerLocalData.LerpStandCrouchTimeTp / 1000.0f;
                        }
                        else
                        {
                            transitionTime = 0f;
                        }
                    }
                    else if (lerpTime > 0 && playerLocalData.ELocomotionLayer !=
                             AnimParametersTp.ELocomotionLayer.HipStance_Crouch2Stand)
                    {
                        //还在cs过渡中
                        nextType = AnimParametersTp.ELocomotionLayer.HipStance_Crouch2Stand;
                        normalizeTime = (TpAniConstData.LerpStandCrouchTime - playerLocalData.LerpStandCrouchTimeTp) /
                                        1000.0f;
                        transitionTime = 0.1f;
                    }
                    else if (lerpTime <= 0)
                    {
                        //直接变成s
                        nextType = AnimParametersTp.ELocomotionLayer.HipStance_HipStandIdle;
                    }

                    break;
                case PlayerPoseStateEnum.Crouch:
                    if (playerLocalData.TurnInPlaceDirection == 1)
                    {
                        //触发原地转身
                        nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRCrouch;
                        if (lerpTime > 0)
                        {
                            transitionTime = playerLocalData.LerpStandCrouchTimeTp / 1000.0f;
                        }
                        else
                        {
                            transitionTime = 0f;
                        }
                    }
                    else if (playerLocalData.TurnInPlaceDirection == -1)
                    {
                        //触发原地转身
                        nextType = AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLCrouch;
                        if (lerpTime > 0)
                        {
                            transitionTime = playerLocalData.LerpStandCrouchTimeTp / 1000.0f;
                        }
                        else
                        {
                            transitionTime = 0f;
                        }
                    }
                    else if (lerpTime > 0 && playerLocalData.ELocomotionLayer !=
                             AnimParametersTp.ELocomotionLayer.HipStance_Stand2Crouch)
                    {
                        //还在sc过渡中
                        nextType = AnimParametersTp.ELocomotionLayer.HipStance_Stand2Crouch;
                        normalizeTime = (TpAniConstData.LerpStandCrouchTime - playerLocalData.LerpStandCrouchTimeTp) /
                                        1000.0f;
                        transitionTime = 0.1f;
                    }
                    else if (lerpTime <= 0)
                    {
                        //直接变成c
                        nextType = AnimParametersTp.ELocomotionLayer.HipStance_HipCrouchIdle;
                    }

                    break;
            }
        }

        private void ToHorseJumpStartState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            float transitionTime, float normalizeTime, bool fixTime)
        {
            ref var vehicleData = ref jobData.VehicleData;
            var jumpIndex = vehicleData.HorseJumpIndex;
            var indexname = 0;
            var locoLayer = AnimParametersTp.ELocomotionLayer.LocomotionEmpty;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            switch (jumpIndex)
            {
                case 0:
                    indexname = AnimParametersTp.LocomotionLayer_RiderJump_JumpForward_JumpForwardStart_NameId;
                    locoLayer = AnimParametersTp.ELocomotionLayer.RiderJump_JumpForward_JumpForwardStart;
                    break;
                case 1:
                    indexname = AnimParametersTp.LocomotionLayer_RiderJump_JumpCanter_JumpCanterStart_NameId;
                    locoLayer = AnimParametersTp.ELocomotionLayer.RiderJump_JumpCanter_JumpCanterStart;
                    break;
                case 2:
                    indexname = AnimParametersTp.LocomotionLayer_RiderJump_JumpGallop_JumpGallopStart_NameId;
                    locoLayer = AnimParametersTp.ELocomotionLayer.RiderJump_JumpGallop_JumpGallopStart;
                    break;
                case 3:
                    indexname = AnimParametersTp.LocomotionLayer_RiderJump_JumpSprint_JumpSprintStart_NameId;
                    locoLayer = AnimParametersTp.ELocomotionLayer.RiderJump_JumpSprint_JumpSprintStart;
                    break;
            }

            ToLocomotionLayerState(ref jobData, ref resultJobData, indexname, locoLayer,
                transitionTime, normalizeTime, fixTime);
            playerLocalData.TpHorseJumpIndex = jumpIndex;
        }

        private void ToHorseJumpLoopState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            float transitionTime, float normalizeTime, bool fixTime)
        {
            ref var vehicleData = ref jobData.VehicleData;
            var jumpIndex = vehicleData.HorseJumpIndex;
            var indexname = 0;
            var locoLayer = AnimParametersTp.ELocomotionLayer.LocomotionEmpty;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            switch (jumpIndex)
            {
                case 0:
                    indexname = AnimParametersTp.LocomotionLayer_RiderJump_JumpForward_JumpForwardLoop_NameId;
                    locoLayer = AnimParametersTp.ELocomotionLayer.RiderJump_JumpForward_JumpForwardLoop;
                    break;
                case 1:
                    indexname = AnimParametersTp.LocomotionLayer_RiderJump_JumpCanter_JumpCanterLoop_NameId;
                    locoLayer = AnimParametersTp.ELocomotionLayer.RiderJump_JumpCanter_JumpCanterLoop;
                    break;
                case 2:
                    indexname = AnimParametersTp.LocomotionLayer_RiderJump_JumpGallop_JumpGallopLoop_NameId;
                    locoLayer = AnimParametersTp.ELocomotionLayer.RiderJump_JumpGallop_JumpGallopLoop;
                    break;
                case 3:
                    indexname = AnimParametersTp.LocomotionLayer_RiderJump_JumpSprint_JumpSprintLoop_NameId;
                    locoLayer = AnimParametersTp.ELocomotionLayer.RiderJump_JumpSprint_JumpSprintLoop;
                    break;
            }

            ToLocomotionLayerState(ref jobData, ref resultJobData, indexname, locoLayer,
                transitionTime, normalizeTime, fixTime);
            playerLocalData.TpHorseJumpIndex = jumpIndex;
        }

        private void ToHorseJumpEndState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            float transitionTime, float normalizeTime, bool fixTime)
        {
            ref var vehicleData = ref jobData.VehicleData;
            var jumpIndex = vehicleData.HorseJumpIndex;
            var indexname = 0;
            var locoLayer = AnimParametersTp.ELocomotionLayer.LocomotionEmpty;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            switch (jumpIndex)
            {
                case 0:
                    indexname = AnimParametersTp.LocomotionLayer_RiderJump_JumpForward_JumpForwardEnd_NameId;
                    locoLayer = AnimParametersTp.ELocomotionLayer.RiderJump_JumpForward_JumpForwardEnd;
                    break;
                case 1:
                    indexname = AnimParametersTp.LocomotionLayer_RiderJump_JumpCanter_JumpCanterEnd_NameId;
                    locoLayer = AnimParametersTp.ELocomotionLayer.RiderJump_JumpCanter_JumpCanterEnd;
                    break;
                case 2:
                    indexname = AnimParametersTp.LocomotionLayer_RiderJump_JumpGallop_JumpGallopEnd_NameId;
                    locoLayer = AnimParametersTp.ELocomotionLayer.RiderJump_JumpGallop_JumpGallopEnd;
                    break;
                case 3:
                    indexname = AnimParametersTp.LocomotionLayer_RiderJump_JumpSprint_JumpSprintEnd_NameId;
                    locoLayer = AnimParametersTp.ELocomotionLayer.RiderJump_JumpSprint_JumpSprintEnd;
                    break;
            }

            ToLocomotionLayerState(ref jobData, ref resultJobData, indexname, locoLayer,
                transitionTime, normalizeTime, fixTime);
            playerLocalData.TpHorseJumpIndex = jumpIndex;
        }

        private void KeepingHorseJumpState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            float transitionTime, float normalizeTime, bool fixTime)
        {
            ref var vehicleData = ref jobData.VehicleData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var jumpType = vehicleData.HorseJumpType;
            var jumpIndex = vehicleData.HorseJumpIndex;
            if (jumpType == GetTpHorseJumpType(playerLocalData.ELocomotionLayer) && jumpIndex == playerLocalData.TpHorseJumpIndex)
                return;
            ToRiderJumpState(ref jobData, ref resultJobData, transitionTime, normalizeTime, fixTime);
        }

        private void ToParachuteLocomotion(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            float transitionTime, float normalizeTime, bool fixTime)
        {
            switch (jobData.VehicleData.ParachuteState)
            {
                case (int)EParachuteMoveState.Start:
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_Parachute_Start_NameId,
                        AnimParametersTp.ELocomotionLayer.Parachute_Start,
                        0f, 0, true);
                    break;
                case (int)EParachuteMoveState.Cut:
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_Parachute_Cut_NameId,
                        AnimParametersTp.ELocomotionLayer.Parachute_Cut,
                        0f, 0, true);
                    break;
                case (int)EParachuteMoveState.Fall:
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_Parachute_ParachuteIdle_NameId,
                        AnimParametersTp.ELocomotionLayer.Parachute_ParachuteIdle,
                        0f, 0, true);
                    break;
            }
        }

        /// 切换Swim 移动的状态
        private void SwitchSwimLocomotion(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            TpAniConstData constData, float transitionTime, float normalizeTime, bool fixTime)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;

            var locomotionPhase = 0f;
            switch (playerLocalData.ELocomotionLayer)
            {
              case AnimParametersTp.ELocomotionLayer.Swim_Swim_JogF:
                  locomotionPhase = GetStateNormalizedTimeLocomotionLayer(ref jobData,
                      AnimParametersTp.LocomotionLayer_Swim_Swim_JogF_NameId);
                  break;
              case AnimParametersTp.ELocomotionLayer.Swim_Swim_JogB:
                  locomotionPhase = GetStateNormalizedTimeLocomotionLayer(ref jobData,
                      AnimParametersTp.LocomotionLayer_Swim_Swim_JogB_NameId);
                  break;
              case AnimParametersTp.ELocomotionLayer.Swim_Swim_JogL:
                  locomotionPhase = GetStateNormalizedTimeLocomotionLayer(ref jobData,
                      AnimParametersTp.LocomotionLayer_Swim_Swim_JogL_NameId);
                  break;
              case AnimParametersTp.ELocomotionLayer.Swim_Swim_JogR:
                  locomotionPhase = GetStateNormalizedTimeLocomotionLayer(ref jobData,
                      AnimParametersTp.LocomotionLayer_Swim_Swim_JogR_NameId);
                  break;
            }
            locomotionPhase = math.clamp(locomotionPhase, 0, 1);
            switch (playerStateData.Movement4Direction)
            {
                case 1:
                    if (playerLocalData.ELocomotionLayer != AnimParametersTp.ELocomotionLayer.Swim_Swim_JogF)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Swim_Swim_JogF_NameId,
                            AnimParametersTp.ELocomotionLayer.Swim_Swim_JogF, transitionTime, locomotionPhase, fixTime,
                            transitFromSnapshotPose: transitFromSnapshotPose);
                    }

                    break;
                case 2:
                    if (playerLocalData.ELocomotionLayer != AnimParametersTp.ELocomotionLayer.Swim_Swim_JogB)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Swim_Swim_JogB_NameId,
                            AnimParametersTp.ELocomotionLayer.Swim_Swim_JogB, transitionTime, locomotionPhase, fixTime,
                            transitFromSnapshotPose: transitFromSnapshotPose);
                    }

                    break;
                case 3:
                    if (playerLocalData.ELocomotionLayer != AnimParametersTp.ELocomotionLayer.Swim_Swim_JogL)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Swim_Swim_JogL_NameId,
                            AnimParametersTp.ELocomotionLayer.Swim_Swim_JogL, transitionTime, locomotionPhase, fixTime,
                            transitFromSnapshotPose: transitFromSnapshotPose);
                    }

                    break;
                case 4:
                    if (playerLocalData.ELocomotionLayer != AnimParametersTp.ELocomotionLayer.Swim_Swim_JogR)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Swim_Swim_JogR_NameId,
                            AnimParametersTp.ELocomotionLayer.Swim_Swim_JogR, transitionTime, locomotionPhase, fixTime,
                            transitFromSnapshotPose: transitFromSnapshotPose);
                    }

                    break;
            }
        }

        /// <summary>
        /// 切换hip 移动的状态
        /// </summary>
        /// <param name="jobData"></param>
        /// <param name="resultJobData"></param>
        /// <param name="constData"></param>
        /// <param name="transitionTime"></param>
        /// <param name="normalizeTime"></param>
        /// <param name="fixTime"></param>
        private void SwitchHipLocomotion(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData, float transitionTime, float normalizeTime,
            bool fixTime)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            //inlocomotion
            var inLocomotion = InLocomotionJog(playerLocalData.ELocomotionLayer);
            var nowInStand = false;
            if (playerLocalData.NowPoseStateCacheTp == PlayerPoseStateEnum.Crouch)
            {
                //ToCrouch
                nowInStand = false;
            }
            else
            {
                //ToStand
                nowInStand = true;
            }

            if (inLocomotion)
            {
                if (playerLocalData.LerpStandCrouchTimeTp > 0)
                {
                    transitionTime = playerLocalData.LerpStandCrouchTimeTp / 1000.0f;
                }
            }

            float3 speedXZ = new float3(playerStateData.SpeedX, 0, playerStateData.SpeedZ);
            var finalAnimSpeed = math.length(speedXZ);

            //拿到当前的LocomotionLayer
            AnimParametersTp.ELocomotionLayer currentLocomotion = AnimParametersTp.ELocomotionLayer.LocomotionEmpty;
            //TpLocomotionDir是上一次的值
            bool lastIsJog = InLocomotionJog(playerLocalData.ELocomotionLayer);
            if (lastIsJog)
            {
                currentLocomotion = playerLocalData.ELocomotionLayer;
            }

            //因为策划倾向于使用jog当做2.8速度(walk,和jogb等都是2.8)下的姿态，所以判定适配下
            bool isWalk = finalAnimSpeed < WalkSpeed - 0.1f;

            //拿到期望的LocomotionLayer
            AnimParametersTp.ELocomotionLayer expectLocomotion = AnimParametersTp.ELocomotionLayer.LocomotionEmpty;
            if (playerStateData.Movement8Direction != 0)
            {
                if (nowInStand)
                {
                    // var speedConf = playerStateData.MoveSpeedConfList.GetConf(AnimMoveEnum.JogMotion);
                    if (isWalk)
                    {
                        expectLocomotion = MoveWalkEnumIds[playerStateData.Movement8Direction];
                    }
                    else
                    {
                        expectLocomotion = MoveJogEnumIds[playerStateData.Movement8Direction];
                    }
                }
                else
                {
                    expectLocomotion = MoveCrouchEnumIds[playerStateData.Movement8Direction];
                }
            }

            if (currentLocomotion != expectLocomotion &&
                expectLocomotion != AnimParametersTp.ELocomotionLayer.LocomotionEmpty)
            {
                //如果之前已经在jog整个状态内，那就不需要blend jogweight权重
                bool skipWeight = lastIsJog;

                if (nowInStand)
                {
                    if (isWalk)
                    {
                        ToLocomotionGenericWalk(ref jobData, ref resultJobData, expectLocomotion, transitionTime,
                            0, fixTime, skipWeight);
                    }
                    else
                    {
                        ToLocomotionGenericJog(ref jobData, ref resultJobData, expectLocomotion, transitionTime,
                            0, fixTime, skipWeight);
                    }
                }
                else
                {
                    ToLocomotionGenericCrouch(ref jobData, ref resultJobData, expectLocomotion, transitionTime,
                        0, fixTime, skipWeight);
                }
            }

            if (expectLocomotion != AnimParametersTp.ELocomotionLayer.LocomotionEmpty)
            {
                CalculateJogSpeed(ref resultJobData, ref animParams, ref playerLocalData, ref playerStateData);
            }
            playerLocalData.TpToLocomotionTime = transitionTime;
        }

        private void ToLocomotionGenericJog(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            AnimParametersTp.ELocomotionLayer locomotionLayerType, float transitionTime, float normalizeTime,
            bool fixTime, bool SkipWeight = false, bool changeState = true)
        {
            //查找,数量级少，应该还好
            for (int i = 0; i < MoveJogEnumCache.Length; ++i)
            {
                ref var data = ref MoveJogEnumCache[i];
                if (data.Item1 == locomotionLayerType)
                {
                    ToLocomotionLayerState(ref jobData, ref resultJobData, data.Item2, locomotionLayerType,
                        transitionTime, normalizeTime, fixTime, 
                        transitFromSnapshotPose: transitFromSnapshotPose, skipWeight: SkipWeight,
                        changeState: changeState);
                    break;
                }
            }
        }

        private void ToLocomotionGenericWalk(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            AnimParametersTp.ELocomotionLayer locomotionLayerType, float transitionTime, float normalizeTime,
            bool fixTime, bool SkipWeight = false, bool changeState = true)
        {
            //查找,数量级少，应该还好
            for (int i = 0; i < MoveWalkEnumCache.Length; ++i)
            {
                ref var data = ref MoveWalkEnumCache[i];
                if (data.Item1 == locomotionLayerType)
                {
                    ToLocomotionLayerState(ref jobData, ref resultJobData, data.Item2, locomotionLayerType,
                        transitionTime, normalizeTime, fixTime, 
                        transitFromSnapshotPose: transitFromSnapshotPose, skipWeight: SkipWeight,
                        changeState: changeState);
                    break;
                }
            }
        }

        private void ToLocomotionGenericCrouch(ref TpAnimationJobData jobData,
            ref TpAnimationResultJobData resultJobData, AnimParametersTp.ELocomotionLayer locomotionLayerType,
            float transitionTime, float normalizeTime, bool fixTime, bool SkipWeight = false, bool changeState = true)
        {
            //查找,数量级少，应该还好
            for (int i = 0; i < MoveCrouchEnumCache.Length; ++i)
            {
                ref var data = ref MoveCrouchEnumCache[i];
                if (data.Item1 == locomotionLayerType)
                {
                    ToLocomotionLayerState(ref jobData, ref resultJobData, data.Item2, locomotionLayerType,
                        transitionTime, normalizeTime, fixTime, 
                        transitFromSnapshotPose: transitFromSnapshotPose, skipWeight: SkipWeight,
                        changeState: changeState);
                    break;
                }
            }
        }

        private void UpdateNewLadderState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams,
            in TpAniConstData constData)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            bool isBlendIn = playerStateData.MoveLadderState == PlayerMoveLadderStateEnum.BleedInLadder;
            bool isBlendOut = playerStateData.MoveLadderState == PlayerMoveLadderStateEnum.BleedOutLadder;
            bool isLadder = playerStateData.MoveState == PlayerMoveStateEnum.MoveLadder;

            //设置相关数据
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            //当前和期望判定
            TpLadderEnum expectLadder = TpLadderEnum.Empty;
            TpLadderEnum curLadder = resultJobData.PlayerLocalData.TpLadder;

            //计算期望
            if (isLadder)
            {
                if (isBlendIn)
                {
                    if (playerStateData.LadderEnterDir == LadderTestDir.Top)
                    {
                        int curTime = playerStateData.rmCurTime;
                        int animMoveTime = playerStateData.AnimMoveTime;
                        int animYawTime = playerStateData.AnimTurnTime;
                        int rmStartTime = animYawTime + animMoveTime;

                        //看是否到了rootmotion的时间
                        if (curTime >= rmStartTime)
                        {
                            expectLadder = TpLadderEnum.DownEnter;
                        }
                        //看是否到了程序位移时间
                        else if (curTime >= 0 && curTime < animMoveTime)
                        {
                            expectLadder = TpLadderEnum.ProcedureAnim;
                        }
                        else
                        {
                            expectLadder = TpLadderEnum.Wait;
                        }
                    }
                    else if (playerStateData.LadderEnterDir == LadderTestDir.Bottom)
                    {
                        expectLadder = TpLadderEnum.UpEnter;
                    }
                    else
                    {
                        float3 speed = new float3(playerStateData.SpeedX, playerStateData.SpeedY,
                            playerStateData.SpeedZ);
                        if (GameEnumUtils.HasBitFlag(playerStateData.LadderMoveFlag, LadderMoveType.FastLadder) &&
                            math.lengthsq(speed) > 0)
                        {
                            var speedF = math.projectsafe(speed, playerStateData.LadderForward);
                            var fdot = math.dot(speedF, playerStateData.LadderForward);
                            bool isForward = fdot > 0.0f;
                            if (isForward)
                            {
                                expectLadder = TpLadderEnum.FastUp;
                            }
                            else
                            {
                                expectLadder = TpLadderEnum.FastDown;
                            }
                        }
                        else
                        {
                            expectLadder = TpLadderEnum.LadderMove;
                        }
                    }
                }
                else if (isBlendOut)
                {
                    if (playerStateData.LadderExitDir == LadderTestDir.Top)
                    {
                        expectLadder = TpLadderEnum.UpLeave;
                    }
                    else if (playerStateData.LadderExitDir == LadderTestDir.Bottom)
                    {
                        expectLadder = TpLadderEnum.DownLeave;
                    }
                }
                else
                {
                    float3 speed = new float3(playerStateData.SpeedX, playerStateData.SpeedY, playerStateData.SpeedZ);
                    if (GameEnumUtils.HasBitFlag(playerStateData.LadderMoveFlag, LadderMoveType.FastLadder) &&
                        math.lengthsq(speed) > 0)
                    {
                        var speedF = math.projectsafe(speed, playerStateData.LadderForward);
                        var fdot = math.dot(speedF, playerStateData.LadderForward);
                        bool isForward = fdot > 0.0f;
                        if (isForward)
                        {
                            expectLadder = TpLadderEnum.FastUp;
                        }
                        else
                        {
                            expectLadder = TpLadderEnum.FastDown;
                        }
                    }
                    else
                    {
                        expectLadder = TpLadderEnum.LadderMove;
                    }
                }
            }

            if (playerStateData.PoseState != PlayerPoseStateEnum.Ladder && expectLadder == TpLadderEnum.Empty)
            {
                OverrideLerpStandCrouch(ref jobData, ref resultJobData, ref animParams, constData);
                var transition = 0.2f;
                if (jobData.PlayerStateData.PoseState == PlayerPoseStateEnum.Crouch)
                {
                    transition = 0.2f;
                }
                LogicToStandDetailState(ref jobData, ref resultJobData, ref animParams, constData, transition, true);
            }

            //判断期望是否和当前一致
            if (expectLadder != curLadder)
            {
                switch (expectLadder)
                {
                    case TpLadderEnum.Empty:
                        break;
                    case TpLadderEnum.UpEnter:
                        {
                            ToLocomotionLayerState(ref jobData, ref resultJobData,
                                LadderAnimIds[(int)TpLadderEnum.UpEnter], LadderAnimEnumIds[(int)TpLadderEnum.UpEnter],
                                0.1f, 0, true);
                            break;
                        }
                    case TpLadderEnum.UpLeave:
                        {
                            ToLocomotionLayerState(ref jobData, ref resultJobData,
                                LadderAnimIds[(int)TpLadderEnum.UpLeave], LadderAnimEnumIds[(int)TpLadderEnum.UpLeave],
                                0.1f, 0, true);
                            break;
                        }
                    case TpLadderEnum.DownEnter:
                        {
                            ToLocomotionLayerState(ref jobData, ref resultJobData,
                                LadderAnimIds[(int)TpLadderEnum.DownEnter],
                                LadderAnimEnumIds[(int)TpLadderEnum.DownEnter],
                                0.1f, 0, true);
                            break;
                        }
                    case TpLadderEnum.DownLeave:
                        {
                            ToLocomotionLayerState(ref jobData, ref resultJobData,
                                LadderAnimIds[(int)TpLadderEnum.DownLeave],
                                LadderAnimEnumIds[(int)TpLadderEnum.DownLeave],
                                0.1f, 0, true);
                            break;
                        }
                    case TpLadderEnum.LadderMove:
                        {
                            ToLocomotionLayerState(ref jobData, ref resultJobData,
                                LadderAnimIds[(int)TpLadderEnum.LadderMove],
                                LadderAnimEnumIds[(int)TpLadderEnum.LadderMove],
                                0.1f, 0, true);
                            break;
                        }
                    case TpLadderEnum.FastUp:
                        {
                            ToLocomotionLayerState(ref jobData, ref resultJobData,
                                LadderAnimIds[(int)TpLadderEnum.FastUp], LadderAnimEnumIds[(int)TpLadderEnum.FastUp],
                                0.1f, 0, true);
                            break;
                        }
                    case TpLadderEnum.FastDown:
                        {
                            ToLocomotionLayerState(ref jobData, ref resultJobData,
                                LadderAnimIds[(int)TpLadderEnum.FastDown],
                                LadderAnimEnumIds[(int)TpLadderEnum.FastDown],
                                0.1f, 0, true);
                            break;
                        }
                    case TpLadderEnum.ProcedureAnim:
                        {
                            break;
                        }
                    case TpLadderEnum.Wait:
                        {
                            float transitionTime = 0.15f;
                            var stateInt = 0;
                            var resultType = AnimParametersTp.ELocomotionLayer.Sum;
                            if (playerLocalData.LerpStandCrouchTp == 0)
                            {
                                stateInt = AnimParametersTp.LocomotionLayer_Ladder_LadderProcedureCrouchWait_NameId;
                                resultType = AnimParametersTp.ELocomotionLayer.Ladder_LadderProcedureCrouchWait;
                            }
                            else
                            {
                                stateInt = AnimParametersTp.LocomotionLayer_Ladder_LadderProcedureWait_NameId;
                                resultType = AnimParametersTp.ELocomotionLayer.Ladder_LadderProcedureWait;
                            }

                            ToLocomotionLayerState(ref jobData, ref resultJobData, stateInt, resultType, transitionTime,
                                0f, true, ocBoneFlag:true, ocAniBoneMask: GetLocomotionLayerBone(ref jobData, ref resultJobData, AnimParametersTp.ELocomotionLayer.HipStance_HipStandIdle));
                            break;
                        }
                }

                playerLocalData.TpLadder = expectLadder;
                if (expectLadder != TpLadderEnum.ProcedureAnim)
                {
                    playerLocalData.tpLadderMove = false;
                    animParams.floatParams.SpeedAngle = 0;
                }
            }

            if (expectLadder == TpLadderEnum.ProcedureAnim)
            {
                //有速度
                if (!playerLocalData.tpLadderMove &&
                    (math.abs(playerStateData.SpeedX) > 0 || math.abs(playerStateData.SpeedZ) > 0))
                {
                    int movement8Direction = 0;

                    float3 forward = new float3(0, 0, 1);
                    float3 right = new float3(1, 0, 0);
                    float3 speed = new float3(playerStateData.SpeedX, 0, playerStateData.SpeedZ);
                    var movequat = quaternion.Euler(0f, math.radians(playerStateData.RotateY), 0f);

                    forward = math.normalizesafe(math.mul(movequat, forward));
                    right = math.normalizesafe(math.mul(movequat, right));

                    var nSpeed = math.normalizesafe(speed);
                    var fdot = math.dot(nSpeed, forward);
                    var rdot = math.dot(nSpeed, right);

                    int fb = 0;
                    int lr = 0;
                    float threshold = 0.001f;
                    if (fdot > threshold)
                    {
                        fb = 1;
                    }
                    else if (fdot < -threshold)
                    {
                        fb = -1;
                    }

                    if (rdot > threshold)
                    {
                        lr = 1;
                    }
                    else if (rdot < -threshold)
                    {
                        lr = -1;
                    }

                    //输入 0无或者抵消 1f 2b 3l 4r 5lf 6rf 7lb 8rb 八方向
                    movement8Direction = GetDirectionCode(fb, lr);

                    if (movement8Direction != 0)
                    {
                        //暂时写死
                        var nowInStand = playerStateData.CCHeight > StandCCHeight - 0.1f;
                        bool isWalk = playerStateData.AnimSpeed < WalkSpeed - 0.1f;

                        AnimParametersTp.ELocomotionLayer expectLocomotion =
                            AnimParametersTp.ELocomotionLayer.LocomotionEmpty;
                        if (nowInStand)
                        {
                            // var speedConf = playerStateData.MoveSpeedConfList.GetConf(AnimMoveEnum.JogMotion);
                            if (isWalk)
                            {
                                expectLocomotion = MoveWalkEnumIds[movement8Direction];
                            }
                            else
                            {
                                expectLocomotion = MoveJogEnumIds[movement8Direction];
                            }
                        }
                        else
                        {
                            expectLocomotion = MoveCrouchEnumIds[movement8Direction];
                        }

                        float transitionTime = 0.15f;
                        if (nowInStand)
                        {
                            if (isWalk)
                            {
                                ToLocomotionGenericWalk(ref jobData, ref resultJobData, expectLocomotion,
                                    transitionTime, 0,
                                    true, true, false);
                            }
                            else
                            {
                                ToLocomotionGenericJog(ref jobData, ref resultJobData, expectLocomotion, transitionTime,
                                    0,
                                    true, true, false);
                            }
                        }
                        else
                        {
                            ToLocomotionGenericCrouch(ref jobData, ref resultJobData, expectLocomotion, transitionTime,
                                0,
                                true, true, false);
                        }

                        playerLocalData.tpLadderMove = true;
                    }
                }

                if (playerLocalData.tpLadderMove)
                {
                    CalculateJogSpeed(ref resultJobData, ref animParams, ref playerLocalData, ref playerStateData);
                }
            }
            else if (expectLadder == TpLadderEnum.Wait)
            {
                animParams.floatParams.LadderVelocityFB = 0;
                animParams.floatParams.LadderVelocityLR = 0;
            }
        }

        //这样定义fb和br的话，没办法输直接根据一个公式来计算。先这么写
        static int GetDirectionCode(int fb, int lr)
        {
            if (fb == 0 && lr == 0) return 0;
            if (fb == 1 && lr == 0) return 1;
            if (fb == -1 && lr == 0) return 2;
            if (fb == 0 && lr == -1) return 3;
            if (fb == 0 && lr == 1) return 4;
            if (fb == 1 && lr == -1) return 5;
            if (fb == 1 && lr == 1) return 6;
            if (fb == -1 && lr == -1) return 7;
            if (fb == -1 && lr == 1) return 8;
            return 0; // 其他情况，比如无效输入，但根据题目输入应为合法值
        }

        //跳转到跳跃状态
        private void ToRootMotionWarpingState(ref TpAnimationJobData jobData,
            ref TpAnimationResultJobData resultJobData)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            switch ((RootMotionWarpingIndexEnum)playerStateData.RootMotionWarpingIndex)
            {
                case RootMotionWarpingIndexEnum.MantleOnLowNear:
                case RootMotionWarpingIndexEnum.MantleOnHighNear:
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_Mantle_MantleOnNear_NameId,
                        AnimParametersTp.ELocomotionLayer.Mantle_MantleOnNear,
                        0, 0, true);
                    break;
                case RootMotionWarpingIndexEnum.MantleOnLowFar:
                case RootMotionWarpingIndexEnum.MantleOnHighFar:
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_Mantle_MantleOnFar_NameId,
                        AnimParametersTp.ELocomotionLayer.Mantle_MantleOnFar,
                        0, 0, true);
                    break;
                default:
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_Mantle_MantleOnNear_NameId,
                        AnimParametersTp.ELocomotionLayer.Mantle_MantleOnNear,
                        0, 0, true);
                    break;
            }
        }

        /// <summary>
        /// 跳转到跳跃状态
        /// </summary>
        /// <param name="jobData"></param>
        /// <param name="resultJobData"></param>
        /// <param name="transitionTime"></param>
        /// <param name="normalizeTime"></param>
        /// <param name="fixTime"></param>
        private void ToJumpState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            float transitionTime, float normalizeTime, bool fixTime)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            //到InJump
            if (playerStateData.MoveState == PlayerMoveStateEnum.Fall)
            {
                if (!jobData.PlayerStateData.Disable2JumpLoop)
                {
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_Jump_JumpLoop_NameId,
                        AnimParametersTp.ELocomotionLayer.Jump_JumpLoop,
                        transitionTime, 0, true, needComparse: true);
                }
            }
            else if (playerStateData.MoveState == PlayerMoveStateEnum.Jump)
            {
                if (playerStateData.MoveJumpState == PlayerMoveJumpStateEnum.LeaveJump)
                {
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_Jump_JumpEnd_NameId,
                        AnimParametersTp.ELocomotionLayer.Jump_JumpEnd,
                        transitionTime, 0, true, needComparse: true);
                    playerLocalData.InJumpEndInput = jobData.PlayerStateData.Movement8Direction != 0;
                }
                else
                {
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_Jump_JumpStart_NameId,
                        AnimParametersTp.ELocomotionLayer.Jump_JumpStart,
                        transitionTime, 0, true, needComparse: true);
                }
            }
        }

        private TpAniBoneMask GetLocomotionLayerBone(ref  TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData, AnimParametersTp.ELocomotionLayer eLocomotionLayer)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var heldIndex = jobData.HeldItemData.TpAniLocomotionWeightIndex;
            var eSum =  (int)AnimParametersTp.ELocomotionLayer.Sum;
            var index = heldIndex * eSum + (int)eLocomotionLayer;
            return LocomotionLayerWeightArray[index];
        }
    }
}