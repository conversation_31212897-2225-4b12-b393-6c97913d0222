using System.Text;
using UnityEngine;
using FairyGUI;
using SimpleJSON;
using Assets.Scripts.MicroServiceClient;
using System;
using System.Collections.Generic;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Audio;
using WizardGames.Soc.Common.Unity.Loader;
using DeviceInfo = WizardGames.Soc.SocClient.Device.DeviceInfo;
using WizardGames.Soc.Client.Login;
using WizardGames.Soc.Common.Download;
using WizardGames.Soc.SocClient.Utility;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Manager;
using WizardGames.Soc.SDK;
using WizardGames.Soc.SocClient.PersistentData;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.Common.Data.DataItem;
using GCloud.MSDK;




#if UNITY_EDITOR
using UnityEditor;
#endif

#if MSDK
using GCloud.MSDK;
using GUA.MSDK;
#endif

namespace WizardGames.Soc.SocClient.Ui
{
    public partial class UiLogin : WindowComBase, IUiFps10Update, IUiFps1Update
    {
        private enum EAnnouncementPullState
        {
            /// <summary>
            /// 拉取公告中
            /// </summary>
            Pulling,
            /// <summary>
            /// 有新公告, 弹窗
            /// </summary>
            Popup,
            /// <summary>
            /// 拉取结束
            /// </summary>
            Finish,
        }

        private readonly SocLogger log = LogHelper.GetLogger(typeof(UiLogin));
        private GComponent content;
        private Controller ctrlStyle;
        private GTextField versionInfo;
        private GTextField textEditionInfo;
        private GButton btnClose;
        private GComponent comAccount;
        private GButton btnBoard;
        private GGroup groupLoginBtns;
        private GComponent enterPanel;
        private GGroup enterGroup;
        private GButton tryAgainConnectBtn;
        private GButton exitSdkBtn;
        private Controller ctrlShowTryAgain;
        private EAnnouncementPullState pullAnnouncementStateInternal = EAnnouncementPullState.Finish;
        private float fPullAnnouncementTime = -1;           // 开始拉取公告的时间
        private const float ANNOUN_PULL_TIME_OUT = 2f;      // 拉取公告超时时间
        private bool tryAutoLogin = false;           // 是否进行自动登录
        private static bool hasAutoLoginned = false;        // 期望是只有进程起来那一次进行自动登录, 后续手动返回登录界面不应该自动登录, 所以设置一个静态变量简单记录一下

        /// <summary>
        /// 公告拉取状态
        /// </summary>
        private EAnnouncementPullState pullAnnouncementState
        {
            get => pullAnnouncementStateInternal;
            set
            {
                pullAnnouncementStateInternal = value;
                log.InfoFormat<string>("[Announcement]UiLogin Pull Announcement State: {0}", value.ToString());
            }
        }

        public override void MakeFullScreen()
        {
            base.MakeFullScreen();
            if (null != UiFullScreenVideoMono.Instance)
            {
                UiFullScreenVideoMono.Instance.MakeFullScreen();
            }
        }

        protected override void OnInit()
        {
            base.OnInit();
            // 未成年人保护设置, MSDK需要在登录前设置语言与地区
            MSDKWrapper.SetLanandRegion();

            string strLobbyVideo = "Video/socm_lobby";
            if (null == UiFullScreenVideoMono.Instance)
            {
                UiFullScreenVideoMono.LoadVideoObjAndPlay(strLobbyVideo, true, () => Mc.Ui.RemoveWindow("UiLoading"));
                HallAudioManager.InitPlayHallMusic("Music_Hall", "Login");
            }

#if ENABLE_PROXIMA
            var OpenProxima = PlayerPrefs.GetInt("OpenProxima", 0);
            if (Mc.SettingClient != null && OpenProxima > 0)
            {
                Mc.SettingClient.OnStartProxima();
            }
#endif
            content = ContentPane.GetChild("root").asCom;
            ctrlStyle = content.GetController("style");
            versionInfo = content.GetChild("versionInfo").asTextField;
            textEditionInfo = content.GetChild("textEditionInfo").asTextField;
            textEditionInfo.text = SocAppContext.Client_Version;
            btnBoard = content.GetChild("btnBoard").asButton;
            enterPanel = content.GetChild("enterPanel").asCom;
            enterGroup = enterPanel.GetChild("group").asGroup;
            tryAgainConnectBtn = enterPanel.GetChild("tryAgainConnectBtn").asButton;
            exitSdkBtn = enterPanel.GetChild("exitBtn").asButton;
            ctrlShowTryAgain = enterPanel.GetController("showTryAgain");
            ctrlStyle.SetSelectedPage("normal");
            tryAgainConnectBtn.SetSoundClick(Mc.Tables.TbUiAudio.UniversalButton, OnClickAgainLogin);
            exitSdkBtn.SetSoundClick(Mc.Tables.TbUiAudio.UniversalButton, OnEscClose);
            btnBoard.onClick.Set(() =>
            {
                Mc.Ui.OpenWindow("UiAnnouncement", (win) =>
                {
                    var uiAnnouncement = win as UiAnnouncement;
                    if (uiAnnouncement != null)
                    {
                        uiAnnouncement.SetNoPopVisible(false);
                    }
                });
            });

            var btnReplay = content.GetChild("btnReplay").asButton;
            // 重播入口的显影暂时和开发登录的显影一致
            bool replayVisible = Application.isEditor || ApplicationConfig.Instance.NeedDeveloperLogin();
            if (null != btnReplay)
            {
                btnReplay.visible = replayVisible;
                if (replayVisible) btnReplay.onClick.Set(OnClickReplay);
            }

            var btnRepair = content.GetChild("btnRepair").asButton;
#if PUBLISH
            btnRepair.visible = false;
#else
            btnRepair.onClick.Set(OnClickRepair);
            btnRepair.visible = true;
#endif

            var btnService = content.GetChild("btnService").asButton;
            btnService.onClick.Set(OnClickService);
            groupLoginBtns = content.GetChild("loginBtns").asGroup;

            InitView();
            InitAllLoginBtns();

            //客户端微服务
            MicroServiceClient.InitMicroServiceClient();

            // 显示性能指标
            Mc.Ui.OpenWindowT<UiPerformance>("UiPerformance");
            Mc.Ui.OpenWindowT<UiVersionInfo>("UiVersionInfo");

            RegisterEvent<WindowComBase>(EventDefine.UiHideEvent, OnUiHide);
            PullAnnouncement();

            // 尝试自动登录
            if (!hasAutoLoginned) tryAutoLogin = true;
            hasAutoLoginned = true;

            var btnAgeAdvice = content.GetChild("btnAgeAdvice").asButton;
            if (null != btnAgeAdvice) btnAgeAdvice.onClick.Add(() => Mc.Ui.OpenWindow("UiAgeAdvice"));

            McCommonUnity.Scene.CurScene = Common.Unity.Scene.SceneName.Login;
            //SDK登录成功
            MicroServiceClient.Instance.LoginSdkFinish += SdkLoginFinish;
            // BundleRuntimeContext.AssetBundleRes.OnAssetBundleNeedUpdate += OnAssetBundleUpdate;

            //使用局内GM水印设置项默认值，控制是否显示水印 - 无GM权限时保持显示，有GM权限时判断默认值
            bool waterMaskShow = Mc.SettingClient.GetConfig("ToggleShowWaterMark") == null;
            if (!waterMaskShow) bool.TryParse(Mc.SettingClient.GetConfig("ToggleShowWaterMark").DefaultValue, out waterMaskShow);
            if (waterMaskShow)
            {
                if (Mc.Ui.GetWindow("UiWaterMark") == null)
                    Mc.Ui.OpenWindowT<UiWaterMark>("UiWaterMark");
            }
            Mc.Msg.FireMsgAtOnce(EventDefine.UiLoginInit);
            Mc.Msg.AddListener(EventDefine.LobbyLoginFail, this.OnLobbyLoginFail);
        }

        private void OnClickService(EventContext context)
        {
            HyperlinkUtil.OpenUrl(Mc.Tables.TbGlobalConfig.PrivacyPolicy);
        }


        protected override void OnEnable()
        {
            ShowDeviceInfo();

        }
        private void OnLobbyLoginFail()
        {
            //大厅登录失败，显示重试按钮
            this.ctrlShowTryAgain.selectedIndex = 1;
            WeGameSDKWrapper.ReportEvent(EWeGameSDKEvent.rail_player_login, EWeGameSDKEventStatus.failure);
        }
        private void SdkLoginFinish(bool isDevChannel, JSONObject param)
        {
            this.isDevChannel = isDevChannel;
            this.param = param;

            if (null == enterPanel || null == enterGroup) return;
            if (LoginChannel.MSDKChannel.Equals(LoginChannel.DevLoginChannel))
            {
                ctrlStyle.SetSelectedPage("login");
                enterPanel.GetChild("name").asTextField.text = "";

            }
            else
            {
#if MSDK
                var info = MSDKAccountService.Instance.GetAccountResult();
                if( info != null )
                {
                    ctrlStyle.SetSelectedPage("login");
                    enterPanel.GetChild("name").asTextField.text =info.UserName;
                }
                else
                {
                    ctrlStyle.SetSelectedPage("normal");
                    return;
                }
#endif
            }
            var childs = enterPanel.GetChildren();
            if (null == childs || 0 == childs.Length) return;
            foreach (var child in childs)
            {
                var image = child;
                if (null == image || null == image.group) continue;
                if (!image.group.Equals(enterGroup)) continue;
                // 在这个组下的按钮, 按钮名称即对应渠道名称, 当名称为 "DevLogin" 时, 为内网登录渠道
                if (image.name.Equals(LoginChannel.MSDKChannel))
                {
                    image.visible = true;
                }
                else
                {
                    image.visible = false;
                }
            }

        }

        public override void OnEscClose(EventContext context)
        {
            Action<JSONNode> doLogout = jsonNode =>
            {
                Mc.Config.accountName = null;
                Mc.Config.roleId = null;
                Mc.Config.token = null;
                Mc.PersistentData.Reset(PersistentDataBindingType.Account);
                // 在大厅场景就直接打开登录界面
                UiFullScreenVideoMono.Instance.PlayVideo("Video/socm_lobby");
                HallAudioManager.InitPlayHallMusic("Music_Hall", "Login");
                ctrlStyle.SetSelectedPage("normal");
            };
            MicroServiceClient.Instance.Logout(doLogout, false);
        }

        public void OnClickAgainLogin(EventContext e)
        {
            TryAgainLogin(isDevChannel, param);
        }
        bool isDevChannel; JSONObject param;
        private void TryAgainLogin(bool isDevChannel, JSONObject param)
        {
            this.ctrlShowTryAgain.selectedIndex = 0;
            MicroServiceClient.Instance.Login(isDevChannel, param);
        }

        /// <summary>
        /// 监听窗口打开事件
        /// </summary>
        private void OnUiHide(WindowComBase win)
        {
            if (win == null) return;
            string uiName = win.uiName;
            if (string.IsNullOrEmpty(uiName)) return;
            switch (uiName)
            {
                case "UiAnnouncement":
                    pullAnnouncementState = EAnnouncementPullState.Finish;
                    break;
                case "UiLiteLoading":
                    //ctrlStyle.SetSelectedPage("normal");
                    break;
            }
        }

        /// <summary>
        /// 拉取登录前公告
        /// </summary>
        private void PullAnnouncement()
        {
            fPullAnnouncementTime = Time.realtimeSinceStartup;
            pullAnnouncementState = EAnnouncementPullState.Pulling;
            // 尝试拉取一次登录前公告
            // TODO QC 确认分组
            Mc.Announcement.ReqAnnouncement("0", () =>
            {
                Mc.Announcement.curAnnouncementFilter = AnnouncementFilter.BeforeLogin;
                fPullAnnouncementTime = -1f;
                // 如果存在新的公告, 则自动弹出公告页面
                if (!Mc.Announcement.HasNewAnnouncements())
                {
                    pullAnnouncementState = EAnnouncementPullState.Finish;
                    return;
                }
                // 公告回来的时候如果已经不在登录界面了, 就不弹了
                if (!Mc.Ui.IsWindowOpen("UiLogin") || Mc.LoginStep.IsInLoginFlow) return;
                pullAnnouncementState = EAnnouncementPullState.Popup;
                Mc.Ui.OpenWindow("UiAnnouncement");
            });
        }

        private bool LoginEnableCheck()
        {
            // 在拉取公告
            if (pullAnnouncementState != EAnnouncementPullState.Finish)
            {
                return false;
            }
            // 在获取数据
            if (SystemUtil.IsGettingInfo)
            {
                return false;
            }
            return true;
        }

        /// <summary>
        /// 初始化所有的登录按钮
        /// </summary>
        private void InitAllLoginBtns()
        {
            if (null == content || null == groupLoginBtns) return;
            var childs = content.GetChildren();
            if (null == childs || 0 == childs.Length) return;
            foreach (var child in childs)
            {
                var btnLogin = child.asButton;
                if (null == btnLogin || null == btnLogin.group) continue;
                if (!btnLogin.group.Equals(groupLoginBtns)) continue;
                // 在这个组下的按钮, 按钮名称即对应渠道名称, 当名称为 "DevLogin" 时, 为内网登录渠道
                if (btnLogin.name.Equals(LoginChannel.DevLoginChannel))
                {
                    // 开发登录按钮是否显示，走包体配置
                    btnLogin.visible = Application.isEditor || ApplicationConfig.Instance.NeedDeveloperLogin();
                    if (btnLogin.visible)
                    {
                        btnLogin.SetSoundClick(Mc.Tables.TbUiAudio.UniversalButtonClick, ctx =>
                        {
                            if (!LoginEnableCheck()) return;
                            LoginChannel.MSDKChannel = null;
                            OnClickDevLogin();
                        });
                    }
                }
                else if (btnLogin.name.Equals(LoginChannel.WeGameLoginChannel))
                {
                    //PC端，DEBUG包显示，PUBLISH包直接自动登录
#if UNITY_STANDALONE_WIN
                    btnLogin.visible = !Application.isEditor;
                    btnLogin.SetSoundClick(Mc.Tables.TbUiAudio.UniversalButtonClick, ctx =>
                    {
                        if (!WeGameSDKWrapper.IsInitialized)
                        {
                            return;
                        }
                        UiLiteLoading.ShowLoading(LanguageConst.LobbyLogin);
                        WeGameSDKWrapper.AutoLogin();
                    });
#else
                    btnLogin.visible = false;
#endif
                }
                else
                {
                    // MSDK登录渠道的按钮, 在没有启用MSDK宏的时候, 全部隐藏
#if MSDK && !UNITY_EDITOR
#if SOC_OVERSEA
                    if (btnLogin.name == MSDKChannel.QQ || btnLogin.name == MSDKChannel.WeChat)
                    {
                        btnLogin.visible = false;
                        continue;
                    }
#if !UNITY_IOS
                    if (btnLogin.name == MSDKChannel.Apple)
                    {
                        btnLogin.visible = false;
                        continue;
                    }
#endif
#if UNITY_IOS
                    if (btnLogin.name == MSDKChannel.Google)// ios暂时不接Google
                    {
                        btnLogin.visible = false;
                        continue;
                    }
#endif
#if !SOC_ENABLE_APPLE
                    if (btnLogin.name == MSDKChannel.Apple)
                    {
                        btnLogin.visible = false;
                        continue;
                    }
#endif
#if !SOC_ENABLE_FACEBOOK
                    if (btnLogin.name == MSDKChannel.Facebook)
                    {
                        btnLogin.visible = false;
                        continue;
                    }
#endif
#if !SOC_ENABLE_GOOGLE
                    if (btnLogin.name == MSDKChannel.Google)
                    {
                        btnLogin.visible = false;
                        continue;
                    }
#endif
                    btnLogin.visible = true;
                    btnLogin.SetSoundClick(Mc.Tables.TbUiAudio.UniversalButtonClick, ctx =>
                    {
                        if (!LoginEnableCheck()) return;
                        UiLiteLoading.ShowLoading(LanguageConst.LobbyLogin);
                        LoginChannel.MSDKChannel = btnLogin.name;
                        var permissions = "";
                        if (LoginChannel.MSDKChannel == MSDKChannel.Facebook)
                        {
                            permissions = "public_profile";
                        }
                        MSDKWrapper.Login(LoginChannel.MSDKChannel, permissions);
                    });
#else
                  
                    if (btnLogin.name == MSDKChannel.Google || btnLogin.name == MSDKChannel.Facebook || btnLogin.name == MSDKChannel.Apple)
                    {
                        btnLogin.visible = false;
                        continue;
                    }

#if !ENABLE_QQ
                    if (btnLogin.name == MSDKChannel.QQ)
                    {
                        btnLogin.visible = false;
                        continue;
                    }
#endif

#if !ENABLE_WECHAT
                    if (btnLogin.name == MSDKChannel.WeChat)
                    {
                        btnLogin.visible = false;
                        continue;
                    }
#elif UNITY_IOS
                    if (btnLogin.name == MSDKChannel.WeChat)
                    {
                        //未安装微信时，iOS设备，微信登录按钮要隐藏，QRCode登录也不需要
                        var isWeChatInstalled = MSDKTools.IsAppInstalled("WeChat");
                        if(!isWeChatInstalled){
                            btnLogin.visible = false;
                            continue;
                        }
                    }
#endif
                    btnLogin.visible = true;
                    btnLogin.SetSoundClick(Mc.Tables.TbUiAudio.UniversalButtonClick, ctx =>
                    {
                        if (!LoginEnableCheck()) return;
                        UiLiteLoading.ShowLoading(LanguageConst.LobbyLogin);
                        LoginChannel.MSDKChannel = btnLogin.name;
                        var permissions = "";
                        if (LoginChannel.MSDKChannel == MSDKChannel.QQ)
                        {
                            permissions = "all";
                        }
                        MSDKWrapper.Login(LoginChannel.MSDKChannel, permissions);
                    });  
#endif
#else
                    btnLogin.visible = false;
#endif
                }
            }
        }

        public void OnFps10Update(float dt)
        {
            // 拉取公告超时
            if (pullAnnouncementState == EAnnouncementPullState.Pulling)
            {
                if (Time.realtimeSinceStartup - fPullAnnouncementTime >= ANNOUN_PULL_TIME_OUT)
                {
                    fPullAnnouncementTime = -1;
                    pullAnnouncementState = EAnnouncementPullState.Finish;
                }
            }
            // 自动登录
            if (tryAutoLogin)
            {
                // 如果还在拉取公告或者正在获取系统信息， 则先不进行自动登录
                if (!LoginEnableCheck()) return;
                tryAutoLogin = false;
                Mc.LoginStep.TrySDKAutoLogin();
            }

            // 资源修复Update
            UpdateManager.Update();
        }

        /// <summary>
        /// 打开开发登录弹窗
        /// </summary>
        private void OnClickDevLogin()
        {
            Mc.Ui.OpenWindow("UiLoginPop");
        }


        private void ShowDeviceInfo()
        {
            var info = DeviceInfo.Collect();
            StringBuilder infoBuilder = new StringBuilder("[UiLogin] Device info: ");

            infoBuilder.AppendFormat("Id: {0}, ", info.Id);
            infoBuilder.AppendFormat("DeviceName: {0}, ", info.DeviceName);
            infoBuilder.AppendFormat("DeviceModel: {0}, ", info.DeviceModel);
            infoBuilder.AppendFormat("GraphicsName: {0}, ", info.GraphicsName);
            infoBuilder.AppendFormat("GraphicsType: {0}, ", info.GraphicsType);
            infoBuilder.AppendFormat("GraphicsVersion: {0}, ", info.GraphicsVersion);
            infoBuilder.AppendFormat("GraphicsMemorySize: {0}, ", info.GraphicsMemorySize);
            infoBuilder.AppendFormat("ProcessorType: {0}, ", info.ProcessorType);
            infoBuilder.AppendFormat("OSVersion: {0}", info.OSVersion);

            log.Info(infoBuilder.ToString());
        }

        private void OnClickReplay(EventContext ctx)
        {
            Mc.Ui.OpenWindow("UiReplayBox");
        }

        private void OnClickRepair(EventContext ctx)
        {
            var boxInfo = new MsgBoxInfo(MsgBoxConst.GameFixTips, new()
            {
                null,
                ()=>
                {
                    UiLoading.ShowLoading(0)?.SetMsg(LanguageManager.GetTextConst(LanguageConst.GameInFix), 0);
                    UpdateManager.StartSourceRepair((result) =>
                    {
                        logger.InfoFormat("Source repair finished. Result: {0}", result);
                        var finishBoxInfo = new MsgBoxInfo(MsgBoxConst.GameFixFinish, new()
                        {()=>{
#if UNITY_EDITOR
                            EditorApplication.isPlaying = false;
#else
                            Application.Quit();
#endif
                        }});
                        finishBoxInfo.HasCloseBtn = false;
                        UiMsgBox.ShowMsgBox(finishBoxInfo);
                        UpdateManager.Destroy();
                    },
                    (message, downloadSpeed, curSize, totalSize, progress) =>
                    {
                        UiLoading.ShowProgress(progress);
                        if (progress >= 100) UiLoading.HideLoading();
                    },
                    (label, leftBtnText, leftCb, rightBtnText, rightCb) =>
                    {
                        List<ModalBtnInfo> btns = new()
                        {
                            new(leftBtnText, leftCb),
                            new(rightBtnText, rightCb)
                        };
                        UiMsgBox.ShowMsgBox(new()
                        {
                            Msg = label,
                            BtnList = btns,
                            SetCenter = true
                        });
                    });
                }
            });
            boxInfo.HasCloseBtn = false;
            UiMsgBox.ShowMsgBox(boxInfo);
        }
        protected override void OnDisable()
        {
            base.OnDisable();
        }
        public override void OnDestroy()
        {
            MicroServiceClient.Instance.LoginSdkFinish -= SdkLoginFinish;
            base.OnDestroy();
            Mc.Msg.RemoveListener(EventDefine.LobbyLoginFail, this.OnLobbyLoginFail);
        }

        public void OnFps1Update(float dt)
        {
#if MSDK
            MSDKWrapper.OnFps1Update();
#endif
        }
    }
}