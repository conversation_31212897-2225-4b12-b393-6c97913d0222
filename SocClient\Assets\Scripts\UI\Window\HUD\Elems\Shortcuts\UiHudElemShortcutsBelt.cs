using FairyGUI;
using System;
using System.Collections.Generic;
using System.Drawing;
using UnityEngine;
using WizardGames.Soc.Common.Const;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Data.DataItem;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Common.State.Character;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.Character.State.Event;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Manager;
using WizardGames.Soc.Common.Unity.Systems.Weapon;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Weapon;
using WizardGames.Soc.SDK;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocClient.ActionExecutor;
using WizardGames.Soc.SocClient.Audio;
using WizardGames.Soc.SocClient.Collection;
using WizardGames.Soc.SocClient.Construction;
using WizardGames.Soc.SocClient.Control;
using WizardGames.Soc.SocClient.GameDebug;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Ui.Utils;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.SocClient.Ui
{
    /// <summary>
    /// 物品快捷栏作为物品使用的分流中心, 点击或选中物品后, 将物品使用的逻辑分流给 系统/战斗/建造 侧逻辑进行处理
    /// </summary>
    public partial class UiHudElemShortcutsBelt : ClientContainerFixed
    {
        private class UseItemCD
        {
            private float max;
            private float cur;

            public float Progress => cur / max;

            public UseItemCD(float maxTime)
            {
                Reset(maxTime);
            }

            public void Reset(float maxTime)
            {
                max = maxTime;
                cur = maxTime;
            }

            public bool Reduce(float dt)
            {
                cur -= dt;
                return cur > 0;
            }
        }

        private static SocLogger logger = LogHelper.GetLogger(typeof(UiHudElemShortcutsBelt));

        private UiHudElemShortcuts root = null;
        public GObject dragSafeArea = null;
        public GButton btnMenuFolder = null;
        private Controller ctrlBtnMenuArrowState = null;
        private Controller ctrlMenuChooseSign = null;
        private Controller ctrlBeltStyle = null;

        public bool IsFolderUp => btnMenuFolder is { selected: false };

        /// <summary>
        /// 物品id到图标的映射
        /// </summary>
        private Dictionary<long, ComItemIcon> itemId2Icon = new();

        /// <summary>
        /// 点击快捷栏图标的CD
        /// </summary>
        private const float ClickCDT = 200;
        private float clickCDRemainT = 0;

        /// <summary>
        /// 当前选中的物品ID
        /// </summary>
        private static long curSelectedUid = 0;

        /// <summary>
        /// 是否选中了远程武器
        /// </summary>
        private bool isChooseReloadableWeapon = false;

        /// <summary>
        /// 每次间隔此时间检查一次entity
        /// </summary>
        private const float CheckEntityOffsetT = 500f;

        /// <summary>
        /// 检查entity的剩余时间
        /// </summary>
        private float checkEntityRemainT = CheckEntityOffsetT;

        /// <summary>
        /// 需要检查entity的标记
        /// </summary>
        private int checkEntityFlag = 0;

        /// <summary>
        /// 停止更新枪械信息
        /// </summary>
        private bool stopUpdateWeaponInfo = false;

        /// <summary>
        /// 图标在禁用时的目标透明度
        /// </summary>
        private const float fIconDisableTarAlpha = 0.3f;

        /// <summary>
        /// 图标在禁用时的背板目标透明度
        /// </summary>
        private const float fIconBottomDisableTarAlpha = 0.08f;

        /// <summary>
        /// 图标背板正常状态的透明度
        /// </summary>
        private float fIconBottomEnableAlpha = -1;

        /// <summary>
        /// 当快捷栏上的物品Entity变化时，选中此图标
        /// </summary>
        private ComItemIcon toSelectOnShortcutsEntityChange = null;

        /// <summary>
        /// 快捷栏下一帧选中此图标
        /// </summary>
        private ComItemIcon toSelectOnNextFrame = null;

        /// <summary>
        /// 选中标记
        /// </summary>
        private GComponent comChooseMenuSign = null;

        /// <summary>
        /// 选中标记样式
        /// </summary>
        private Controller ctrlChooseMenuSignStyle = null;

        /// <summary>
        /// 物品选单
        /// </summary>
        private UiHudElemShortcutsChooseMenu chooseMenu;

        /// <summary>
        /// 安全区大小控制器
        /// </summary>
        private Controller ctrlSafeArea;

        /// <summary>
        /// 拖拽提示
        /// </summary>
        private GComponent comDragTip;

        /// <summary>
        /// 自动触发自动物品选单的最大y值
        /// </summary>
        private float triggerAutoChooseMenuY;

        /// <summary>
        /// 是否是自动触发打开的物品选单
        /// </summary>
        private bool isAutoOpenChooseMenu = false;

        /// <summary>
        /// 用于恢复选中的物品UID
        /// </summary>
        private long selectUidToRecover = -1;

        /// <summary>
        /// 当前更新的物品节点
        /// </summary>
        private BaseItemNode curChangeItemNode;

        /// <summary>
        /// 当前选中的物品图标
        /// </summary>
        public ComItemIcon CurSelectedIcon { get; private set; } = null;

        /// <summary>
        /// 当前选中的物品
        /// </summary>
        public BaseItemNode CurSelectItem
        {
            get
            {
                if (null == CurSelectedIcon) return null;
                return CurSelectedIcon.InstItem;
            }
        }

        protected bool inMenuChoose => chooseMenu != null && chooseMenu.Visible;

        /// <summary>
        /// 当前物品选单对象索引
        /// </summary>
        private int curMenuChooseIndex
        {
            get
            {
                if (!inMenuChoose) return -1;
                if (null == comChooseMenuSign || !comChooseMenuSign.visible) return -1;
                return ctrlMenuChooseSign?.selectedIndex ?? -1;
            }
        }

        /// <summary>
        /// 跳过一次通知战斗测物品变化, 临时修复, @LiuHongbin
        /// </summary>
        public static bool SkipNotifyBattleSideOnce = false;

        /// <summary>
        /// 跳过WeaponEquip装备状态矫正
        /// </summary>
        public static bool SkipWeaponEquipRectify = false;

        protected override bool onlyMission => true;

        /// <summary>
        /// ItemClick 自治icons ，武器栏需要改为按下触发，此列表中的icon不统一调用Click
        /// </summary>
        public List<ComItemIcon> clickExceptIcons = new List<ComItemIcon>();

        /// <summary>
        /// 快捷栏图标选择样式控制器
        /// </summary>
        private List<Controller> bottomIconChooseStyles = new();

        /// <summary>
        /// 物品使用CD
        /// </summary>
        private Dictionary<long, UseItemCD> useCDItems = new();

        /// <summary>
        /// 待移除的物品使用CD
        /// </summary>
        private List<long> toRemoveUseCD = new();


        /// <summary>
        /// 物品使用CD额外的偏移时间
        /// </summary>
        private const float UseCDOffset = 300f;

        /// <summary>
        /// 是否正从建造模式退出
        /// </summary>
        public static bool IsExitingFromBuild = false;

        /// <summary>
        /// 物品选择菜单是否在快捷栏上方
        /// </summary>
        public bool isChooseMenuAtTop { get; private set; } = true;

        private ActionName[] hotKeys =
        {
            ActionName.Shortcut3,
            ActionName.Shortcut4,
            ActionName.Shortcut5,
            ActionName.Shortcut6,
            ActionName.Shortcut7,
            ActionName.Shortcut8,
        };


        public UiHudElemShortcutsBelt(UiHudElemShortcuts rootElem) : base()
        {
            root = rootElem;
            var node = rootElem.TryGetNode();
            ctrlBeltStyle = node.GetController("style");
            if (Mc.MyPlayer.MyEntityServer != null)
                Bind(node.GetChild("list").asList, Mc.CollectionItem.InventoryCom.ContainerBeltNode);

            dragSafeArea = node.GetChild("dragSafeArea");
            rootElem.GetStageBlockArea = GetBlockArea;

            comDragTip = node.GetChild("dragTip").asCom;
            comDragTip.visible = false;
            comChooseMenuSign = node.GetChild("menuChoose").asCom;
            ctrlChooseMenuSignStyle = comChooseMenuSign.GetController("style");
            ResetMenuChooseData();
            chooseMenu = new UiHudElemShortcutsChooseMenu(this, node.GetChild("chooseMenu").asCom, OnChooseMenuChooseItem);
            ctrlSafeArea = node.GetController("safeAreaCtrl");
            if (null != ctrlSafeArea) ctrlSafeArea.SetSelectedPage("small");

            ctrlMenuChooseSign = node.GetController("chooseSign");
            btnMenuFolder = node.GetChild("folder").asButton;
            ctrlBtnMenuArrowState = btnMenuFolder.GetController("arrowState");
            btnMenuFolder.changeStateOnClick = false;
            btnMenuFolder.onClick.Add(OnClickMenuFolder);
            btnMenuFolder.visible = !PlayHelper.IsNewbie;   // 新手关需要屏蔽物品选单

            // 这里不需要其他逻辑, 只是作为安全区接纳一下图标防止被空处理视为丢弃行为
            //node.AddEventListener("onItemDrop", () => UiItemIconDragDrop.CancelItemDrag());

            Mc.Msg.AddListener<long, long>(EventDefine.Ui_MyPlayer_HeldItemSwitchSuccess, OnWeaponEquiped);
            Mc.Msg.AddListener(EventDefine.StartReload, OnStartReload);
            Mc.Msg.AddListener<int>(EventDefine.ReloadSetClip, OnReloadSetClip);
            Mc.Msg.AddListener(EventDefine.ClientReloadCdFinish, OnReloadCDFinished);
            Mc.Msg.AddListener(EventDefine.UpdateSingleMission, RefreshTags);
            Mc.Msg.AddListener<long, bool, EItemDisableReason>(EventDefine.Battle_ItemUsableUpdate, UsableChanged);
            Mc.Msg.AddListener(EventDefine.ProactivelyExitBuildMode, OnProactivelyExitBuildMode);
            Mc.Msg.AddListener(EventDefine.UpdateSingleMission, OnMissionChanged);
            Mc.Msg.AddListener(EventDefine.OnHudMakeFullScreen, OnMakeFullScreen);
        }

        public void OnEnable()
        {
            if (null != chooseMenu)
            {
                var menuPos = root.GetDynamicPanelPosition(chooseMenu.Root, EDynamicLayoutType.VERTICAL, new Vector2(chooseMenu.Root.width + chooseMenu.Root.x, 0), true, true, false);
                chooseMenu.Root.position = menuPos;
                isChooseMenuAtTop = menuPos.y < 0;
                chooseMenu.RefreshContetnCenterX();
                chooseMenu.RowCount = GetChooseMenuRowCount();
            }
            ctrlBeltStyle.SetSelectedPage(isChooseMenuAtTop ? "up" : "down");
            if (isChooseMenuAtTop) triggerAutoChooseMenuY = comDragTip.LocalToStage(Vector2.up * comDragTip.height).y;
            else triggerAutoChooseMenuY = comDragTip.LocalToStage(Vector2.zero).y;
            RefreshBtnMenuChooseShow(false);
        }

        private void OnMissionChanged()
        {
            if (!PlayHelper.IsNewbie || !Mc.Mission.HasMission) return;
            SetIconRootVisibleExceptFirst(Mc.Mission.NewbieRemoteData.Id != 2040040);
        }

        private void UsableChanged(long tableId, bool newCanUse, EItemDisableReason Reason)
        {
            foreach (var icon in icons)
            {
                if (icon == null || icon.InstItem == null)
                {
                    continue;
                }

                //不是同一个物品，则跳过
                if (icon.InstItem.ItemId != tableId)
                {
                    continue;
                }
                icon.SetIconEnable(newCanUse);
            }
        }


        /// <summary>
        /// 由于快捷栏在展开菜单时有形态变化, 这里使用自定义的获取阻挡区域的函数
        /// </summary>
        private Rect GetBlockArea()
        {
            if (null == dragSafeArea) return Rect.zero;
            Rect area = new Rect();

            var rootNode = root.statusTarget;
            if (rootNode == null) return Rect.zero;

            var inStagePos = rootNode.LocalToStage(Vector2.zero);
            area.x = inStagePos.x;
            area.y = inStagePos.y + dragSafeArea.y;
            if (rootNode.pivotAsAnchor)
            {
                area.x -= rootNode.width * rootNode.scaleX * rootNode.pivot.x;
                area.y -= rootNode.height * rootNode.scaleY * rootNode.pivot.y;
            }
            area.width = dragSafeArea.width * rootNode.scaleX;
            area.height = dragSafeArea.height * rootNode.scaleY;
            return area;
        }

        public void OnStartReload()
        {
            //stopUpdateWeaponInfo = true;
        }

        public void OnReloadSetClip(int ammoId)
        {
            UiHudElemWeapons.CondRefreshIconInventoryClipNum = ammoId;
            OnReloadCDFinished();
        }

        public void OnReloadCDFinished()
        {
            //stopUpdateWeaponInfo = false;
            UpdateHoldItemInfo();
        }

        public void OnDestory()
        {
            Mc.Msg.RemoveListener<long, long>(EventDefine.Ui_MyPlayer_HeldItemSwitchSuccess, OnWeaponEquiped);
            Mc.Msg.RemoveListener(EventDefine.StartReload, OnStartReload);
            Mc.Msg.RemoveListener<int>(EventDefine.ReloadSetClip, OnReloadSetClip);
            Mc.Msg.RemoveListener(EventDefine.ClientReloadCdFinish, OnReloadCDFinished);
            Mc.Msg.RemoveListener(EventDefine.UpdateSingleMission, RefreshTags);
            Mc.Msg.RemoveListener<long, bool, EItemDisableReason>(EventDefine.Battle_ItemUsableUpdate, UsableChanged);
            Mc.Msg.RemoveListener(EventDefine.ProactivelyExitBuildMode, OnProactivelyExitBuildMode);
            Mc.Msg.RemoveListener(EventDefine.UpdateSingleMission, OnMissionChanged);
            Mc.Msg.RemoveListener(EventDefine.OnHudMakeFullScreen, OnMakeFullScreen);

            for (uint i = 2; i < 8; i++)
            {
                HotKeyUtils.RemoveWheelHotKey(i);
            }
        }

        protected override void CollectIcons()
        {
            if (null == iconList) return;
            icons = new List<ComItemIcon>();
            var allIcons = iconList.GetChildren();
            for (int i = 0; i < allIcons.Length; ++i)
            {
                var iconRoot = allIcons[i].asCom;
                var cacheIcon = iconRoot.GetChild("icon") as ComItemIcon;
                cacheIcon.SetTriggerHotArea(iconRoot);

                var chooseBac = iconRoot.GetChild("chooseState");
                cacheIcon.ReplaceChooseFlag(chooseBac);
                var chooseStyle = chooseBac.asCom?.GetController("style");
                if (null != chooseStyle)
                {
                    bottomIconChooseStyles.Add(chooseStyle);
                }

                cacheIcon.Position = i;
                DealIconWhenCollect(cacheIcon);
                icons.Add(cacheIcon);
                var backTouch = iconRoot.GetChild("bacTouch").asButton;
                backTouch.onClick.Set(() => OnClickItem(cacheIcon));

                iconRoot.InitKeyTips("key", hotKeys[i]);
                iconRoot.SetKeyTipsAction("key",()=>
                {	
                    //选择道具
					backTouch.onClick.Call();
                    //完成引导
                    cacheIcon.onClick.Call();
                });
                
                var index = i;
                HotKeyUtils.AddWheelHotKey((uint)i + 2, () => OnWheelHotKey(index));
            }
        }

        private bool OnWheelHotKey(int i)
        {
            var clickIcon = iconList.GetChildAt(i).asCom.GetChild("icon") as ComItemIcon;
            var instItem = clickIcon?.InstItem;
            var typeId = instItem?.TypeId ?? ItemEntityType.None;
            if (typeId == ItemEntityType.Use ||
                typeId == ItemEntityType.Weapon ||
                typeId == ItemEntityType.Melee ||
                typeId == ItemEntityType.ThrowItem ||
                typeId == ItemEntityType.Construction ||
                typeId == ItemEntityType.HoldItem
                )
            {
                if (typeId == ItemEntityType.Use)
                {
                    // 这个类型的，必须要是手持的，直接使用的不能滚动到
                    var configId = instItem?.ConfigId ?? 0;
                    var config = McCommonUnity.Tables.TbMedicalBase.GetOrDefault(configId);
                    if (config == null)
                    {
                        return false;
                    }
                }
                return TryClickItem(clickIcon);
            }

            return false;
        }

        public void OnClickItemByIndex(int index)
        {
            var clickIcon = iconList.GetChildAt(index).asCom.GetChild("icon") as ComItemIcon;
            TryClickItem(clickIcon);
        }

        /// <summary>
        /// 追加协同管理的武器图标
        /// </summary>
        public void AppendWeaponIcon(ComItemIcon icon)
        {
            if (icons == null)
                return;

            icon.Position = icons.Count;
            DealIconWhenCollect(icon);
            icons.Add(icon);
        }

        private bool OnBeltIconDragStart(ItemDragInfo info, ComItemIcon icon)
        {
            if (chooseMenu.Visible)
            {
                chooseMenu.OnBeltAndMenuIconDragStart();
            }
            else
            {
                info.dir = isChooseMenuAtTop ? EIconDragDir.Up : EIconDragDir.Down;
                comDragTip.visible = true;
            }
            ResetMenuChooseData();
            UiItemIconDragDrop.UseFromIconSize = true;
            UiItemIconDragDrop.OnDraggingIcon = OnIconDragging;
            icon.RelockUpdateAfterFrameIfLock();
            return true;
        }

        private void OnBeltIconDragEnd(ComItemIcon icon)
        {
            if (chooseMenu.Visible)
            {
                chooseMenu.OnBeltAndMenuIconDragEnd();
                if (isAutoOpenChooseMenu)
                {
                    isAutoOpenChooseMenu = false;
                    // 如果策划配置了快速拖拽自动关闭，则需要关闭选单
                    if (Mc.Tables.TbGlobalConfig.BeltFastReplaceAutoClose)
                    {
                        SetChooseMenuState(false);
                    }
                }
            }
            comDragTip.visible = false;
            icon.SetIconEnable(IsItemEnableInCurState(icon.InstItem));
            UiItemIconDragDrop.OnDraggingIcon = null;
            UiItemIconDragDrop.UseFromIconSize = false;
            icon.RelockUpdateAfterFrameIfLock();
        }

        private void OnIconDragging(float x, float y)
        {
            if (chooseMenu.Visible)
            {
                chooseMenu.OnIconDragging(x, y);
            }
            // 当图标在纵向进入拖拽提示的高度范围时，显示物品选单
            else if ((isChooseMenuAtTop && y <= triggerAutoChooseMenuY) || (!isChooseMenuAtTop && y >= triggerAutoChooseMenuY))
            {
                comDragTip.visible = false;
                if (UiItemIconDragDrop.IsDragging)
                {
                    UiItemIconDragDrop.SetDragIconDir(EIconDragDir.None);
                }
                SetChooseMenuState(true);
                chooseMenu.OnBeltAndMenuIconDragStart();
                isAutoOpenChooseMenu = true;
            }
        }

        protected override void DealIconWhenCollect(ComItemIcon icon)
        {
            base.DealIconWhenCollect(icon);
            bool isHudWeaponIcon = icon is ComHUDWeaponItemIcon;


            // 其他图标设置
            //武器栏不允许拖拽，物品栏拖拽功能保留
            if (isHudWeaponIcon)
            {
                icon.CanDrag = false;
                icon.EnableUpdateLock = false;
            }
            else
            {
                // 新手关之外，开启图标拖动
                icon.CanDrag = !PlayHelper.IsNewbie;
                // 锁帧更新相关
                icon.EnableUpdateLock = true;
                //快捷栏物品不显示稀有标识
                icon.SetShowRareBg(false);
                // 非远程武器(底部的快捷栏图标)在接受拖拽时需要同时触发选中这个物品
                icon.OnIconAcceptDrag = OnBottomBeltIconAcceptDrag;
                // 非远程武器不能丢弃， 只能交换
                icon.OnNoDragReceiver = ctx => UiItemIconDragDrop.CancelItemDrag();
                // HUD图标空背景，资源染色，图标内不染色
                icon.FlagEmptyStyle = IconEmptyFlagStyle.WHITE;
                icon.OnIconDragStart = info => OnBeltIconDragStart(info, icon);
                icon.OnIconDragEnd = () => OnBeltIconDragEnd(icon);
            }
            icon.StopParentMoveWhenDrag = false;
            icon.IsClickSoundEnable = false;
            icon.OnMouseOrTouchMoveIn = ctx =>
            {
                if (UiItemIconDragDrop.IsDragging && UiItemIconDragDrop.CurDragIcon != icon)
                {
                    icon.SetHighlight(true);
                }
            };
            icon.OnMouseOrTouchMoveOut = ctx => icon.SetHighlight(false);
            // 图标禁用处理
            if (fIconBottomEnableAlpha < 0) fIconBottomEnableAlpha = icon.BacAlpha;
            icon.OnSetIconEnable = (icon, state) =>
            {
                icon.SetAlpha(state ? icon.IconStandardAlpha : fIconDisableTarAlpha);
                icon.BacAlpha = state ? fIconBottomEnableAlpha : (fIconBottomDisableTarAlpha / fIconDisableTarAlpha);
            };
            icon.OnIconDisableClick = icon =>
            {
                if (icon is not ComItemIcon itemIcon || null == itemIcon.InstItem) return;
                var item = itemIcon.InstItem;
                OnClickDisaleIcon(itemIcon);
            };
            icon.OnItemDamaged = OnItemDamaged;
        }

        private void OnClickDisaleIcon(ComItemIcon icon)
        {
            // 如果在物品选单状态, 点击逻辑为设置物品选单
            if (inMenuChoose)
            {
                DoClickItemChooseMenu(icon);
                return;
            }
            var item = icon.InstItem;
            if (null == item) return;
            var checkRes = HeldItemAPI.CheckItemByRule(BattleItemCheckRule.CanUse, item.ItemId);
            if (checkRes.IsSuccess) return;
            // 只能在潜水状态下使用的物品，不能使用说明不在潜水状态，直接提示
            if (item.HasFlag(ItemFlags.DivingOnly))
            {
                Mc.MsgTips.ShowRealtimeWeakTip(2017);
                return;
            }
            // 根据道具不能使用的场景分别做提示
            switch (checkRes.Reason)
            {
                case EItemDisableReason.Swim:
                    Mc.MsgTips.ShowRealtimeWeakTip(2012);
                    break;
                case EItemDisableReason.Dive:
                    Mc.MsgTips.ShowRealtimeWeakTip(2016);
                    break;
                default:
                    Mc.MsgTips.ShowRealtimeWeakTip(11039);
                    break;
            }
        }

        private void OnItemDamaged(ComItemIcon icon)
        {
            if (null == icon.InstItem || icon.InstItem.Id != curSelectedUid) return;
            // 当前持有物耐久度耗尽损坏时, 需要取消物品的持有
            UnloadIfItemIsSelected(CurSelectedIcon.InstItem);
            isChooseReloadableWeapon = false;
        }

        protected bool OnBottomBeltIconAcceptDrag(ComItemIcon myItemIcon, BaseItemNode dropItem)
        {
            if (null == myItemIcon || null == dropItem) return false;
            if (!ItemUtility.CanBeltAcceptItem(myItemIcon.Position, dropItem.Config)) return false;
            if (!myItemIcon.DoDefaultDragAcceptAction(dropItem)) return false;
            // 拖拽成功的情况下, 如果myIcon已经在选中状态，需要重新选中myIcon上新的物品:
            if (myItemIcon.Choose)
            {
                PrepareForAutoChoose(myItemIcon);
            }
            ResetMenuChooseData();
            return true;
        }

        protected bool OnBottomBeltIconAcceptDrag(ComBaseIcon myIcon, ItemDragInfo dragInfo)
        {
            OnBottomBeltIconAcceptDrag_Platform(myIcon, dragInfo);
            if (null == myIcon || myIcon is not ComItemIcon myItemIcon) return false;
            if (null == dragInfo || null == dragInfo.item) return false;
            return OnBottomBeltIconAcceptDrag(myItemIcon, dragInfo.item);
        }

        protected void SetCheckEntityFlag(int index, bool on)
        {
            if (on) checkEntityFlag |= 1 << index;
            else checkEntityFlag &= ~(1 << index);
        }

        protected bool HasCheckEntityFlag(int index)
        {
            return (checkEntityFlag & (1 << index)) > 0;
        }

        /// <summary>
        /// 指定物品在当前状态是否激活可使用
        /// </summary>
        public static bool IsItemEnableInCurState(BaseItemNode itemNode)
        {
            if (null == itemNode) return true;

            //之前好像是只判断游泳？
            bool logicEnable = HeldItemAPI.CheckItemByRule(BattleItemCheckRule.CanUse, itemNode.ItemId).IsSuccess;
            //TODO 战斗那边好像没这个逻辑，可能是ui的规则，@cyj，@xxj，这个后面要废弃掉，统一使用道具的disablearea来控制，等待策划梳理
            // bool disableOnDriver = IsInVehicleDriver(itemNode.ItemConfig);
            return logicEnable;
        }

        public override void SetItemDatas()
        {
            itemId2Icon.Clear();
            base.SetItemDatas();
        }

        public override void DealIconWhenSetData(ComItemIcon icon)
        {
            base.DealIconWhenSetData(icon);
            long itemId = icon.InstItem?.Id ?? 0;
            if (itemId > 0) itemId2Icon[itemId] = icon;
            if (0 == itemId || !useCDItems.ContainsKey(itemId)) icon.SetCD(0);
            // 隐藏图标背板
            if (icon is not ComHUDWeaponItemIcon) icon.SetIconBgVisible(false);
            // 设置图标禁用状态
            icon.SetIconEnable(IsItemEnableInCurState(icon.InstItem));
            icon.RefreshTag(true);
            // 对应位置标记置否
            SetCheckEntityFlag(icon.Position, false);
            if (IsIconShowReloadableWeapon(icon))
            {
                // 如果物品是远程武器, 并且绑定了实例, 那么这个物品的数量需要读取实例上的弹药数量
                // 理论上每一把远程武器都需要对应一个持有物entity用于显示弹药数量和耐久
                // 如果这里拿不到, 可能是entity还没有下来, 记个标记供检查
                Mc.MyPlayer.MyEntityLocal.TryGetHeldItem(icon.InstItem.NodeId, out IHaveBulletEntity entity);
                //Mc.MyPlayer.TryGetMyWeaponInterface(icon.InstItem.NodeId, out IHaveBulletEntity entity);
                if (null == entity && icon.InstItem.HoldEntityId > 0)
                {
                    entity = Mc.Entity.GetEntity(icon.InstItem.HoldEntityId) as IHaveBulletEntity;
                }
                if (entity == null)
                {
                    SetCheckEntityFlag(icon.Position, true);
                }
            }
        }

        /// <summary>
        /// 载具司机位，除了了马和木舟，不能掏出手持物
        /// </summary>
        private static bool IsInVehicleDriver(ItemConfig itemCfg)
        {
            var itemTableId = itemCfg.Id;
            var itemType = ItemUtility.GetItemEntityTypeByTableId(itemTableId);
            if (Mc.MyPlayer.MyEntityLocal.IsDriver) // 载具司机位，除了了马和木舟，不能掏出手持物
            {
                var mountableGo = Mc.MyPlayer.GetMountableGo();
                if (mountableGo == null)
                {
                    return false;
                }
                var mountableEntity = mountableGo.MountableEntity;
                bool notOnHoseAndKayak = mountableEntity.VehicleType != (int)VehicleType.Kayak &&
                                         Mc.MyPlayer.MyEntityLocal.MountableType != EntityTypeId.HorseEntity;
                bool isHoldItem = itemType is
                    ItemEntityType.Construction or
                    ItemEntityType.Melee or
                    ItemEntityType.Use or
                    ItemEntityType.Weapon or
                    ItemEntityType.HoldItem or
                    ItemEntityType.ThrowItem;
                if (notOnHoseAndKayak && isHoldItem)
                    return true;

                if (itemType is ItemEntityType.Construction)
                    return true;
            }

            if (Mc.MyPlayer.MyEntityLocal.IsOnMount && itemType is ItemEntityType.Construction or ItemEntityType.Vehicle)
            {
                return true;
            }


            return false;
        }

        /// <summary>
        /// 重置图标选择数据
        /// </summary>
        protected void ResetIconSelect()
        {
            if (ShortcutsBeltDebug.isShowLog) ShortcutsBeltDebug.Info(logger, "ResetIconSelect");
            if (null != CurSelectedIcon && CurSelectedIcon.Choose) CurSelectedIcon.Choose = false;
            CurSelectedIcon = null;
            curSelectedUid = 0;
        }

        /// <summary>
        /// 记录图标选择
        /// </summary>
        public void RecordIconSelect(ComItemIcon icon)
        {
            if (null == icon || null == icon.InstItem) return;
            if (ShortcutsBeltDebug.isShowLog) ShortcutsBeltDebug.Info(logger, $"ReocrdIconSelect: {icon.InstItem.ItemName}");
            if (!icon.Choose) icon.Choose = true;
            curSelectedUid = icon.InstItem.NodeId;
            CurSelectedIcon = icon;
            Mc.Msg.FireMsg(EventDefine.TryGuideHandheldProp, icon.InstItem.ConfigId);
        }

        /// <summary>
        /// 获取一个物品实例对于的持有Entity
        /// </summary>
        /// <param name="item"></param>
        protected IHeldItemEntity GetItemHoldEntity(BaseItemNode item)
        {
            long holdId = 0;
            if (null != item) holdId = item.HoldEntityId;
            if (holdId <= 0) return null;
            return Mc.Entity.GetEntity(holdId) as IHeldItemEntity;
        }

        /// <summary>
        /// 通知战斗侧物品选择, 战斗侧根据物品与状态决定是否表现玩家动作或通知战斗服务器, 实现战斗相关道具的逻辑
        /// </summary>
        protected void NotifyBattlesideItemSelect(HoldItemIndex index, bool unload)
        {
            if (SkipNotifyBattleSideOnce)
            {
                ShortcutsBeltDebug.Info(logger, "SkipNotifyBattleSideOnce");
                SkipNotifyBattleSideOnce = false;
                return;
            }
            ShortcutsBeltDebug.Info(logger, $"SwitchHoldItem {index}, unload is {unload}");

            if (unload)
            {
                Mc.MyPlayer.MyEntityLocal.DrawComponent.UnEquip();
            }
            else
            {
                Mc.MyPlayer.MyEntityLocal.DrawComponent.Equip(index);
            }
        }

        /// <summary>
        /// 执行物品立即使用的逻辑
        /// </summary>
        /// <param name="item">物品实例</param>
        /// <returns>是否执行了使用逻辑</returns>
        protected bool DoItemUseImmediately(BaseItemNode item)
        {
            ItemOperationType? optType = null;
            if (null != item && null != item.Config) optType = item.Config.OperationType;
            if (optType != ItemOperationType.DirectUse) return false;
            var entityId = item.GetBelongComponent()?.ParentEntity.EntityId ?? 0;
            if (entityId <= 0) return false;

            if (item != null && item.ItemConfig != null && item.ItemConfig.Manufacturing == ItemType.Bundle)
            {
                var giftCfg = Mc.Tables.TbItemPackages.GetOrDefault(item.ItemConfig.Id);
                if (giftCfg == null || giftCfg.DropIDList.Length <= 0)
                {
                    logger.ErrorFormat("[UiItemTipsBoardBtns] [OnClickNormalUse] Invalid params: giftCfg null={0}, DropIDList Length={1}", giftCfg == null, giftCfg == null ? 0 : giftCfg.DropIDList.Length);
                    return false;
                }

                UiSelectGift.emptyCell = item.Amount > 1 ? giftCfg.EmptyCell : giftCfg.EmptyCell - 1;
                if (giftCfg.Optional == 1)
                {
                    UiSelectGift.OpenWindow();
                    var win = UiSelectGift.GetWindow();
                    win?.Setup(item);
                    return true;
                }
                else
                {
                    if (Mc.CollectionItem.RemoteCallOpenPackage(item.NodeId, giftCfg.DropIDList[0]))
                    {
                        MgrAudio.PlayAudioEventAsync("UI_GiftBag_Open", null, null);
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
            }
            else
            {
                // 食物存在一个统一的CD，只要吃了一个食物，其他的食物都要加上一个转圈CD，CD时间内不能再次使用
                if (item.HasFlag(ItemFlags.Eatable))
                {
                    if (useCDItems.ContainsKey(item.Id)) return true;
                    var foodCfg = Mc.Tables.TbFood.GetOrDefault(item.BizId);
                    float cd = foodCfg?.AttackAgainTime ?? 0;
                    if (cd > 0)
                    {
                        foreach (var icon in icons)
                        {
                            var instItem = icon.InstItem;
                            if (null == instItem || !instItem.HasFlag(ItemFlags.Eatable)) continue;
                            var id = instItem.Id;
                            if (useCDItems.ContainsKey(id)) useCDItems[id].Reset(cd + UseCDOffset);
                            else useCDItems[id] = new UseItemCD(cd + UseCDOffset);
                        }
                    }
                }
                Mc.CollectionItem.RemoteCallUseItem(entityId, item.NodeId, 1);
                if (item.ItemConfig != null && item.ItemConfig.Manufacturing == ItemType.Expendable && item.ItemConfig.SecondaryClassification == 8)
                {
                    var dataList = Mc.Tables.TbBoxTask.DataList;
                    var boxData = dataList[0].Tasks;
                    long taskId = -1;
                    for (int i = 0; i < boxData.Count; i++)
                    {
                        var data = boxData[i];
                        if (data.CoefficientValue == item.ItemConfig.Id)
                        {
                            taskId = data.Id;
                            break;
                        }
                    }
                    if (!Mc.Mission.HasBoxTask(taskId))
                    {
                        Mc.Mission.boxTaskSelectId = item.ItemConfig.Id;
                        UiMissionView.OpenWindow((win) =>
                        {
                            win.JumpToPage(Common.Data.MissionTabType.Box);
                        });
                    }
                }
                return true;
            }
        }

        /// <summary>
        /// 执行物品选择状态改变的逻辑
        /// </summary>
        protected void DoItemLogicOnStateChange(BaseItemNode item, bool state)
        {
            if (null == item || null == item.Config) return;
            // 其他物品使用逻辑分流
            switch (item.Config.OperationType)
            {
                case ItemOperationType.SwitchConstruction:
                    Mc.Construction.DoBuildItemUse(item, state);
                    break;
            }
        }

        private bool DoClickItemInternal(ComItemIcon clickIcon)
        {
            // 如果物品是一个损坏的状态, 那么不允许点击
            if (clickIcon.IsItemBroken())
            {
                ShortcutsBeltDebug.Info(logger, $"ClickItem {clickIcon.TbItem.Name} fail, ItemBroken");
                if (clickIcon.TbItem.Id == ItemConst.ElectricPickItemId)
                {
                    Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.PickaxeDamaged);
                }
                else
                {
                    Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.CannotSelectBrokenItem);
                }
                return false;
            }

            // 点击空的图标
            if (clickIcon.InstItem == null)
            {
                DoClickEmptyItem(clickIcon);
                return false;
            }

            var myEntity = Mc.MyPlayer.MyEntityLocal;
            if (!IsItemEnableInCurState(clickIcon.InstItem))
                return false;

            // 立即使用的物品不会影响其他图标的选中状态
            if (DoItemUseImmediately(clickIcon.InstItem)) return false;

            // 判断当前点击图标的结果是否为选中: 含有实例物品的新物品
            bool iconChoose = null != clickIcon.InstItem && CurSelectedIcon != clickIcon;
            // 能持有的物品还必须有对应的 holdEntity
            bool hasHoldEntity = null != GetItemHoldEntity(clickIcon.InstItem);

            // 取消选中旧的物品图标
            if (null != CurSelectedIcon)
            {
                // 如果当前点击的图标没有选中, 说明是点击了空白图标或者重复点击了已选择的图标
                // 这种情况下的取消选中是需要通知给战斗侧的, 否则只需要在选中新物品的时候给战斗侧通知一次就好
                if (!iconChoose) NotifyBattlesideItemSelect((HoldItemIndex)CurSelectedIcon.Position, true);
                DoItemLogicOnStateChange(CurSelectedIcon.InstItem, false);
                ResetIconSelect();
            }
            else
            {
                // 如果没有选中的图标, 但是在摆件模式, 可能是背包发起的使用, 需要先退出建造模式
                if (Mc.Construction.IsDeployMode) Mc.Construction.ExitBuildMode();
                Mc.Construction.IsFromBackpackUseDeploy = false;
                Mc.CollectionItem.CurUseDeployId = 0;
            }

            // 选中新的物品图标
            if (iconChoose)
            {
                RecordIconSelect(clickIcon);
                DoItemLogicOnStateChange(clickIcon.InstItem, true);
                NotifyBattlesideItemSelect((HoldItemIndex)clickIcon.Position, false);
            }

            // 更新持有物信息
            UpdateHoldItemInfo();
            // 如果物品没有选中, 或者没有绑定的entity, 也需要打断战斗侧关于该槽位的执行
            return iconChoose && hasHoldEntity;
        }

        /// <summary>
        /// 刷新所有锁帧的图标
        /// </summary>
        public void AllIconsRelockUpdateAfterFrame(int frame = 1)
        {
            foreach (var icon in icons)
            {
                if (!icon.EnableUpdateLock) continue;
                icon.RelockUpdateAfterFrameIfLock(frame);
            }
        }

        public override void OnClickItem(ComBaseIcon icon)
        {
            TryClickItem(icon);
        }

        /// <summary>
        /// 检查非法的建造物品
        /// </summary>
        private void CheckInvalidBuildItemState(IEntity weaponEntity)
        {
            if (null == weaponEntity || weaponEntity is not MeleeCustom meleeWeapon) return;
            var playerEntity = Mc.MyPlayer.MyEntityLocal;
            var weaponID = weaponEntity.EntityId;
            if (weaponID == playerEntity.GetItemModel(HoldItemIndex.ItemConstructModel)?.EntityId && !Mc.Construction.IsInBuildPlaceMode())
            {
                NotifyBattlesideItemSelect(HoldItemIndex.ItemConstructModel, true);
            }
            else if (weaponID == playerEntity.GetItemModel(HoldItemIndex.ItemEditModel)?.EntityId && !Mc.Construction.IsInRepairMode)
            {
                NotifyBattlesideItemSelect(HoldItemIndex.ItemEditModel, true);
            }
            else if (weaponID == playerEntity.GetItemModel(HoldItemIndex.ItemGugujiModel)?.EntityId)
            {
                //NotifyBattlesideItemSelect(HoldItemIndex.ItemGugujiModel, true);
            }
            else if (weaponID == playerEntity.GetItemModel(HoldItemIndex.ItemElectricModel)?.EntityId && !Mc.Construction.IsBuildWireMode)
            {
                NotifyBattlesideItemSelect(HoldItemIndex.ItemElectricModel, true);
                NotifyBattlesideItemSelect(HoldItemIndex.ItemFluidicModel, true);
            }
        }

        /// <summary>
        /// 武器在战斗侧装备完毕后的回调
        /// </summary>
        public void OnWeaponEquiped(long oldWeaponId, long weaponID)
        {
            // 建造模式中不响应这个方法，防止手持锤子和建造图纸等情况影响快捷栏手持物数据
            if (Mc.Construction.IsInBuildMode) return;
            // 如果出现不在建造或电力模式, 但是却拿着锤子和电线的情况,
            // 说明是在建造或电力模式中下线, 然后上线, 这个时候需要切换到空手
            var weaponEntity = Mc.Entity.GetEntity(weaponID);
            CheckInvalidBuildItemState(weaponEntity);
            // 如果战斗侧的装备结果和UI表现上的选中不同, 这里要对表现进行校正:
            // 1. 如果装备结果是空手,
            //      a. 如果快捷栏上选中的道具没有对应holdEntity, 说明是因为持有无holdEntity物品导致的空手, 那么不对选择结果进行修正
            //      b. 否则, 将道具选中态取消, 因为绑定holdEntity的物品在选中态下不可能为空手状态
            // 2. 如果装备结果是一个非空手的装备entity id, 那么快捷栏上必须只有 entity id对应的图标才能是选中态
            if (SkipWeaponEquipRectify)
            {
                SkipWeaponEquipRectify = false;
            }
            else
            {
                var playerEntity = Mc.MyPlayer.MyEntityLocal;
                bool isNullHand = weaponID <= 0 || null == playerEntity || null == playerEntity.NullHand || weaponID == playerEntity.NullHand.EntityId;
                if (ShortcutsBeltDebug.isShowLog)
                {
                    ShortcutsBeltDebug.Info(logger, $"快捷栏检测修正: isNullHand = {isNullHand}, weaponID = {weaponID}, itemEntityID = {(null == CurSelectedIcon ? "空图标" : (null == CurSelectedIcon.InstItem ? "空物品" : $"{CurSelectedIcon.InstItem.ItemName}({CurSelectedIcon.InstItem.NodeId})"))}", true);
                }
                icons.ForEach(icon =>
                {
                    if (isNullHand)
                    {
                        var holdEntity = GetItemHoldEntity(icon.InstItem);
                        bool isSwitchItem = !icon.IsEmpty && icon.TbItem.OperationType == ItemOperationType.SwitchModel;
                        if (isSwitchItem && icon.Choose)
                        {
                            icon.Choose = false;
                            DoItemLogicOnStateChange(icon.InstItem, false);
                            ResetIconSelect();
                        }
                    }
                    else
                    {
                        bool isChoose = null != icon.InstItem && icon.InstItem.NodeId == weaponID;
                        bool beforeChoose = icon.Choose;
                        icon.Choose = isChoose;
                        if (isChoose && !beforeChoose)
                        {
                            RecordIconSelect(icon);
                            DoItemLogicOnStateChange(icon.InstItem, true);
                        }
                        else if (!isChoose && beforeChoose)
                        {
                            ResetIconSelect();
                        }
                    }
                });
            }
            UpdateHoldItemInfo();
        }

        /// <summary>
        /// 指定图标是否显示远程武器
        /// </summary>
        protected bool IsIconShowReloadableWeapon(ComItemIcon icon)
        {
            if (null == icon || null == icon.InstItem) return false;
            return icon.InstItem is IHaveBulletItemNode;
        }

        /// <summary>
        /// 更新当前持有物信息
        /// </summary>
        protected void UpdateHoldItemInfo()
        {
            isChooseReloadableWeapon = IsIconShowReloadableWeapon(CurSelectedIcon);
            if (!isChooseReloadableWeapon) return;
            // 有标记说明entity还没有下来
            if (HasCheckEntityFlag(CurSelectedIcon.Position)) return;
            UpdateRemoteWeaponNum();
        }

        /// <summary>
        /// 更新当前选中的远程武器弹药数量
        /// </summary>
        protected void UpdateRemoteWeaponNum()
        {
            if (!isChooseReloadableWeapon || stopUpdateWeaponInfo) return;
            if (null == CurSelectedIcon || CurSelectedIcon is not ComHUDWeaponItemIcon weaponIcon) return;
            weaponIcon.RefreshAmmo();
        }

        private void UpdateItemUseCD(float dt)
        {
            if (0 == useCDItems.Count) return;
            foreach (var pair in useCDItems)
            {
                if (!itemId2Icon.TryGetValue(pair.Key, out var icon))
                {
                    toRemoveUseCD.Add(pair.Key);
                    continue;
                }
                icon.RelockUpdateAfterFrameIfLock();
                if (!pair.Value.Reduce(dt))
                {
                    toRemoveUseCD.Add(pair.Key);
                    icon.SetCD(0);
                    continue;
                }
                icon.SetCD(pair.Value.Progress, EIconCDStyle.Use);
            }
            if (toRemoveUseCD.Count > 0)
            {
                foreach (var id in toRemoveUseCD)
                {
                    useCDItems.Remove(id);
                }
                toRemoveUseCD.Clear();
            }
        }

        public void DoUpdateFps10(float dt)
        {
            if (clickCDRemainT > 0) clickCDRemainT -= dt;
            if (isChooseReloadableWeapon) UpdateRemoteWeaponNum();
            if (checkEntityFlag > 0)
            {
                if (checkEntityRemainT <= 0)
                {
                    checkEntityRemainT = CheckEntityOffsetT;
                    root.Weapons?.Refresh();
                }
                else
                {
                    checkEntityRemainT -= dt;
                }
            }

            if (toSelectOnNextFrame != null)
            {
                var icon = toSelectOnNextFrame;
                toSelectOnNextFrame = null;
                if (CurSelectedIcon != icon)
                {
                    DoClickItemInternal(icon);
                    RecordIconSelect(icon);

                }
            }

            UpdateHoldItem();
        }

        public void DoUpdateFps30(float dt)
        {
            if (chooseMenu.Visible) chooseMenu.CheckAutoScroll(dt);
            UpdateItemUseCD(dt);
        }

        private void UpdateHoldItem()
        {
            if (Mc.MyPlayer.MyEntityLocal == null || Mc.MyPlayer.MyEntityLocal.NullHand == null) return;
            var curHoldEntityID = Mc.MyPlayer.MyEntityLocal.CurrentWeaponId;
            bool isNullHand = curHoldEntityID == Mc.MyPlayer.MyEntityLocal.NullHand.EntityId;
            if (!isNullHand && curHoldEntityID > 0)
            {
                var holdEntity = Mc.MyPlayer.MyEntityLocal.GetHeldItemByEntityId(curHoldEntityID) as IHeldItemEntity;
                if (null == holdEntity) return;
                var itemConfig = Mc.Tables.TbItemConfig.GetOrDefault(holdEntity.TemplateId);
                if (null != itemConfig && itemConfig.Itemflags.Contains(ItemFlags.HiddenTool) &&
                    holdEntity.ItemUid != curSelectedUid)
                {
                    curSelectedUid = holdEntity.ItemUid;
                    Mc.Msg.FireMsg(EventDefine.TryGuideHandheldProp, holdEntity.TemplateId);
                    Refresh();
                }
            }
        }

        public void OnShortcutsItemEntityChange(int index)
        {
            if (null == toSelectOnShortcutsEntityChange || index != toSelectOnShortcutsEntityChange.Position)
            {
                return;
            }
            var tarIcon = toSelectOnShortcutsEntityChange;
            toSelectOnShortcutsEntityChange = null;
            if (CurSelectedIcon != tarIcon) DoClickItemInternal(tarIcon);
        }

        /// <summary>
        /// 自动选择预备
        /// </summary>
        private void PrepareForAutoChoose(ComItemIcon icon, ItemOperationType opType)
        {
            if (toSelectOnNextFrame == icon || toSelectOnShortcutsEntityChange == icon)
            {
                return;
            }
            toSelectOnNextFrame = null;
            toSelectOnShortcutsEntityChange = null;
            switch (opType)
            {
                case ItemOperationType.SwitchNull:
                case ItemOperationType.SwitchConstruction:
                    toSelectOnNextFrame = icon;
                    break;
                case ItemOperationType.SwitchModel:
                    toSelectOnShortcutsEntityChange = icon;
                    break;
            }
        }

        /// <summary>
        /// 自动选择预备
        /// </summary>
        private void PrepareForAutoChoose(ComItemIcon icon)
        {
            if (null == icon?.InstItem) return;
            var opType = icon.InstItem.Config.OperationType;
            PrepareForAutoChoose(icon, opType);
        }

        public override void Refresh()
        {
            // 调用基类方法刷新物品列表
            base.Refresh();
            HandelIconSelectAfterRefresh();
            SkipNotifyBattleSideOnce = false;
            UpdateHoldItemInfo();
        }

        private void HandelIconSelectAfterRefresh()
        {
            if (curSelectedUid <= 0) return;
            // 物品图标会因为刷新而重置选中状态，且物品位置也有可能发生了变化
            // 所以快捷栏在刷新完毕后，需要根据之前记录的物品信息根据规则重新指定图标的选中状态
            var beforeIcon = CurSelectedIcon;  // 之前选中的图标
            long beforeId = curSelectedUid;    // 之前选中的物品的uid
            ComItemIcon curShowIcon = null;    // 之前选中的物品在刷新后的快捷栏中位于哪个图标
            ResetIconSelect();
            // 查找curShowIcon
            foreach (var icon in icons)
            {
                var curInstItem = icon.InstItem;
                if (null == curInstItem || curInstItem.NodeId != beforeId) continue;
                curShowIcon = icon;
                break;
            }
            // 如果没有之前选中的图标, 但是有之前选中的id, 且能当前的列表能找到这个物品
            // 说明可能选中过程中hud界面被移除了, 需要重新定向以选中
            // 或者如果前后是同一个图标，说明位置没有变，直接重新记录这个图标的选中即可
            if ((null != curShowIcon) && (null == beforeIcon || beforeIcon == curShowIcon))
            {
                var itemNode = curShowIcon.InstItem;
                // 如果是从建造返回，则直接重新选中进建造前的手持物
                if (IsExitingFromBuild)
                {
                    IsExitingFromBuild = false;
                    if (IsItemEnableInCurState(itemNode))
                    {
                        PrepareForAutoChoose(curShowIcon, ItemOperationType.SwitchNull);    // 强制使用ItemOperationType.SwitchNull指定下一帧选中
                    }
                }
                // 如果不是建造返回，则尝试直接将当前的图标标记为选中态即可
                else
                {
                    bool canSelect = false;
                    if (null != itemNode)
                    {
                        bool isConstruction = itemNode.Config.OperationType == ItemOperationType.SwitchConstruction;
                        canSelect = !isConstruction || Mc.Construction.IsInBuildMode;
                    }
                    if (canSelect)
                    {
                        RecordIconSelect(curShowIcon);
                    }
                }
                return;
            }
            // 如果之前的图标已经空了，但列表中还是能找到curShowIcon，说明是物品位置变更，选中新位置的图标
            if (null != beforeIcon && beforeIcon.IsEmpty && curShowIcon != null)
            {
                if (IsItemEnableInCurState(curShowIcon.InstItem))
                {
                    // 物品数据先于entity同步，需要记录标记，等entity下来之后再实际执行选中
                    PrepareForAutoChoose(curShowIcon);
                }
                return;
            }
            // 对旧的格子取消选中, 执行快捷栏的反选逻辑
            if (null != curShowIcon) curShowIcon.Choose = false;
            var beforeItem = Mc.CollectionItem.GetItemByNodeId(beforeId);
            // 如果物品是被移除，这个时候可能从玩家身上已经取不到了，尝试使用通过物品移除事件转发过来的节点
            if (null == beforeItem && null != curChangeItemNode && curChangeItemNode.Id == beforeId) beforeItem = curChangeItemNode;
            // 卸下逻辑
            if (null != beforeItem) DoItemLogicOnStateChange(beforeItem, false);
            // 1. 如果是手持物, 如果物品已经不在快捷栏了服务端会因为entity的移除而触发自动卸下, 无需客户端特殊处理， 如果还在快捷栏且是与非手持物的交换，需要通知卸下
            if (null != curShowIcon && null != beforeItem && beforeItem.ParentId == NodeConst.BeltItemContainerNodeId)
            {
                bool beforeItemIsHoldModel = beforeItem.Config.OperationType == ItemOperationType.SwitchModel;
                bool curItemIsHoldModel = null != beforeIcon && !beforeIcon.IsEmpty && beforeIcon.InstItem.Config.OperationType == ItemOperationType.SwitchModel;
                if (beforeItemIsHoldModel && !curItemIsHoldModel) NotifyBattlesideItemSelect((HoldItemIndex)curShowIcon.Position, true);
            }
            // 2. 如果非手持物, 则需要对摆件进行特殊处理: 退出建造模式
            if (Mc.Construction.IsDeployMode || Mc.Construction.IsBuildChristmasLightMode) Mc.Construction.ExitBuildMode();
            ResetIconSelect();
            // 当前的规则是选中状态跟着位置走，所以需要重新选中之前位置的格子
            if (null != beforeIcon && !beforeIcon.IsEmpty && IsItemEnableInCurState(beforeIcon.InstItem))
            {
                // 物品数据先于entity同步，需要记录标记，等entity下来之后再实际执行选中
                PrepareForAutoChoose(beforeIcon);
            }
        }

        public void OnAddOrRemoveItemNode(BaseItemNode itemNode)
        {
            curChangeItemNode = itemNode;
            Refresh();
            curChangeItemNode = null;
            if (chooseMenu.Visible) chooseMenu.OnUdateItemNode(itemNode);
        }

        /// <summary>
        /// 如果这个物品当前被选中, 那么执行物品卸下的逻辑
        /// </summary>
        public bool UnloadIfItemIsSelected(BaseItemNode item)
        {
            if (null == item || item.NodeId != curSelectedUid) return false;
            DoItemLogicOnStateChange(item, false);
            if (null != CurSelectedIcon) NotifyBattlesideItemSelect((HoldItemIndex)CurSelectedIcon.Position, true);
            ResetIconSelect();
            return true;
        }

        /// <summary>
        /// 卸下当前选中的物品
        /// </summary>
        public bool UnloadCurrentSelectItem()
        {
            var curItem = CurSelectItem;
            if (null == curItem) return false;
            return UnloadIfItemIsSelected(curItem);
        }

        /// <summary>
        /// 图标拖出快捷栏的处理
        /// </summary>
        protected bool OnIconDragOutOfBelf(object info)
        {
            if (null == info) return true;
            var dragInfo = info as ItemDragInfo;
            if (null == dragInfo || null == dragInfo.item) return true;
            var dropItem = dragInfo.item;
            // 执行丢弃前, 如果是当前选中的物品, 需要先将物品取消选中
            UnloadIfItemIsSelected(dropItem);
            // 发送物品丢弃RPC
            //C2S.Instance.DropItem(dropItem.uid, dropItem.amount);
            Mc.CollectionItem.DropItem(Mc.MyPlayer.RoleId, dropItem.NodeId, dropItem.Amount);
            return true;
        }

        /// <summary>
        /// 使快捷栏选中物品表ID对应的物品
        /// </summary>
        public bool ChooseItemByExcelID(int id)
        {
            if (null == icons) return false;
            foreach (var icon in icons)
            {
                if (icon.IsEmpty || icon.TbItem.Id != id) continue;
                OnClickItem(icon);
                return true;
            }
            ;
            return false;
        }

        private void SetChooseStyle(bool holdStateOnly)
        {
            foreach (var ctrl in bottomIconChooseStyles)
            {
                ctrl.SetSelectedPage(holdStateOnly ? "holdOnly" : "normal");
            }
        }

        private void RefreshBtnMenuChooseShow(bool state)
        {
            // 物品选择栏在快捷栏上方和下方时，按钮箭头的朝向是相反的
            ctrlBtnMenuArrowState.SetSelectedPage(state ? (isChooseMenuAtTop ? "down" : "up") : (isChooseMenuAtTop ? "up" : "down"));
        }

        public void SetChooseMenuState(bool state)
        {
            if (inMenuChoose == state) return;
            btnMenuFolder.selected = state;
            RefreshBtnMenuChooseShow(state);
            if (state)
            {
                Mc.Ui.AddEscStack(EEscCloseName.ShotcutsBelt, () =>
                {
                    btnMenuFolder.onClick.Call();
                    return true;
                }, LayerIdConst.HUD);
            }
            else
            {
                Mc.Ui.RemoveEscStack(EEscCloseName.ShotcutsBelt);
            }
            SetChooseStyle(state);
            if (state)
            {
                chooseMenu.SetData();
                // 显示选单后，快捷栏的物品图标也要做一次刷新
                // 否则选单触发根节点排序后, 会导致图标随机显示错乱
                AllIconsRelockUpdateAfterFrame();
            }
            else
            {
                foreach (var icon in icons)
                {
                    icon.SetHighlight(false);
                }
                chooseMenu.SetEmpty();
            }
            chooseMenu.Visible = state;
            Mc.Ui.UpdateCursorRegistration(nameof(UiHudElemShortcutsBelt), state);
            if (null != ctrlSafeArea) ctrlSafeArea.SetSelectedPage(state ? (isChooseMenuAtTop ? "bigUp" : "bigDown") : "small");
            ResetMenuChooseData();
            UiHud.SetHudBgBlockState(state ? () => { OnHudBlockElemChange(null, true); } : null);
            Mc.Msg.FireMsgAtOnce(EventDefine.ShortcutsChooseMenuStateChange, state);
        }

        public void OnDisable()
        {
            Mc.Ui.RemoveEscStack(EEscCloseName.ShotcutsBelt);
        }
        /// <summary>
        /// 点击物品选单按钮
        /// </summary>
        public void OnClickMenuFolder()
        {
            SetChooseMenuState(!btnMenuFolder.selected);
        }

        /// <summary>
        /// 物品选单选中一个物品
        /// </summary>
        private bool OnChooseMenuChooseItem(BaseItemNode itemNode, int chooseIndex)
        {
            if (chooseIndex < 0) chooseIndex = curMenuChooseIndex;
            ResetMenuChooseData();
            if (chooseIndex < 0) return false;
            // 当itemNode为空时，说明点击了列表的空格子，需要将当前选中物品往背包内转移
            if (null == itemNode)
            {
                if (chooseIndex >= 0 && chooseIndex < icons.Count && !icons[chooseIndex].IsEmpty)
                {
                    Mc.CollectionItem.MoveItem(icons[chooseIndex].InstItem.NodeId, NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Main, -1);
                    return true;
                }
                return false;
            }
            // 当itemNode有值时，回调表示将物品itemNode移动到快捷栏位置chooseIndex
            ComItemIcon chooseIcon = null;
            if (chooseIndex < icons.Count)
            {
                chooseIcon = icons[chooseIndex];
            }
            if (null == chooseIcon) return false;
            Mc.CollectionItem.MoveItem(itemNode.NodeId, NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Belt, chooseIndex);
            return true;
        }

        public void ResetMenuChooseData()
        {
            comChooseMenuSign.visible = false;
        }


        /// <summary>
        /// 记录并取消选中当前的位置
        /// </summary>
        public void RecordAndUnSelectCurPos()
        {
            selectUidToRecover = -1;
            if (curSelectedUid <= 0 || null == CurSelectedIcon || !CurSelectedIcon.Choose) return;
            selectUidToRecover = curSelectedUid;
            UnloadIfItemIsSelected(CurSelectedIcon.InstItem);
        }

        /// <summary>
        /// 恢复选中记录的位置
        /// </summary>
        public void RecoverRecoredSelectPos()
        {
            // 如果没有记录的选中,则切空手
            if (selectUidToRecover <= 0)
            {
                if (curSelectedUid > 0 && null != CurSelectedIcon)
                {
                    UnloadIfItemIsSelected(CurSelectedIcon.InstItem);
                }
                return;
            }
            long toSelect = selectUidToRecover;
            selectUidToRecover = -1;
            // 如果当前选中的物品和待恢复的物品一样, 则不再选中
            if (curSelectedUid == selectUidToRecover) return;
            foreach (var icon in icons)
            {
                if (null == icon.InstItem || icon.InstItem.NodeId != toSelect) continue;
                DoClickItemInternal(icon);
                break;
            }
        }

        public override GObject GetGuideObjByID(long id)
        {
            if (id == 0 && root != null)
            {
                var node = root.TryGetNode();
                if (node != null)
                {
                    try
                    {
                        return node.GetChild("chooseTabs").asCom.GetChild("0");
                    }
                    catch
                    {
                        return null;
                    }
                }
                return null;
            }
            else
            {
                return base.GetGuideObjByID(id);
            }
        }

        public void SetIconLockStateIfLock(bool iconLock)
        {
            foreach (var icon in icons)
            {
                if (icon is ComHUDWeaponItemIcon) continue;
                if (iconLock) icon.RelockUpdateIfLock();
                else icon.UnlockUpdateIfLock();
            }
        }


        public void SetIconRootVisibleExceptFirst(bool visible)
        {
            var allIcoms = iconParent.GetChildren();
            for (int i = 1; i < allIcoms.Length; ++i)
            {
                SafeUtil.SafeSetVisible(allIcoms[i], visible);
            }
        }

        /// <summary>
        /// 主动退出建造模式时的处理
        /// </summary>
        private void OnProactivelyExitBuildMode()
        {
            // 主动退出建造模式时（如受击），如果当前持有的是摆件，需要将摆件卸下
            var curItem = CurSelectItem;
            if (null == curItem) return;
            if (curItem.Config.OperationType == ItemOperationType.SwitchConstruction)
            {
                UnloadIfItemIsSelected(curItem);
            }
        }
        /// <summary>
        /// 屏幕尺寸变动
        /// 折叠屏
        /// </summary>
        private void OnMakeFullScreen()
        {
            if (null != chooseMenu)
            {
                var menuPos = root.GetDynamicPanelPosition(chooseMenu.Root, EDynamicLayoutType.VERTICAL, new Vector2(chooseMenu.Root.width + chooseMenu.Root.x, 0), true, true, false);
                chooseMenu.Root.position = menuPos;
                isChooseMenuAtTop = menuPos.y < 0;
                chooseMenu.RefreshContetnCenterX();
            }
        }
    }
}