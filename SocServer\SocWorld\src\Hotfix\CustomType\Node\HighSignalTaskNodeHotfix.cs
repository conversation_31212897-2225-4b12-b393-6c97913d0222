﻿using System.Collections.Generic;
using WizardGames.Soc.Common.Algorithm;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework.Event;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.Entity;
using WizardGames.Soc.SocWorld.Spawn;
using WizardGames.Soc.SocWorld.TerrianMeta;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.Common.CustomType
{
    [HotfixClass]
    public static partial class HighSignalTaskNodeHotfix
    {
        private static List<GroupPoint> spawnPosList = new();

        [Hotfix]
        public static void StartVirtual(this HighSignalTaskNode self)
        {
            self.CreatePowerlineInteractionEntity(self.GetPoiTaskTemplated());
        }

        [Hotfix]
        public static void StopVirtual(this HighSignalTaskNode self, TaskContainer container)
        {
            BaseStop.Invoke(self, container);
        }

        public static void CreatePowerlineInteractionEntity(this HighSignalTaskNode self, int templateId)
        {
            // 选择遗迹
            var powerlineMonumentId = self.ChoseNearestPowerline();
            if (powerlineMonumentId < 0)
            {
                self.Logger.Error($"[CreatePowerlineInteractionEntity] get nearest powerline monument failed");
                return;
            }

            var matchedPowerlines = WorldTerrainMeta.MatchedPowerlines.GetMatchedPowerlines(powerlineMonumentId);
            if (matchedPowerlines.Count <= 0)
            {
                self.Logger.Error($"[CreatePowerlineInteractionEntity] powerline monument id:{powerlineMonumentId} get nearest matched powerlines monument failed");
                return;
            }

            var matchedPowerlineMonumentId = matchedPowerlines[0];
            self.CreatePowerlineInteractionEntity(powerlineMonumentId, templateId);
            self.CreatePowerlineInteractionEntity(matchedPowerlineMonumentId, templateId);
        }

        public static void CreatePowerlineInteractionEntity(this HighSignalTaskNode self, int monumentId, int templateId)
        {
            // 生成交互点
            var taskConfig = McCommon.Tables.TbQuestPhase.GetOrDefault(self.BizId);
            var spawnPosList = SpawnControlEntity.Instance.GetPointListInMonument(monumentId, templateId);
            if (spawnPosList.Count == 0)
            {
                self.Logger.Warn($"CreatePowerlineInteractionEntity failed. spawnPosList is empty. player:{self.Player.EntityId} templateId:{templateId}");
                return;
            }

            var randIndex = RandomUtil.Instance.Next(0, spawnPosList.Count);
            randIndex = (randIndex + 1) % spawnPosList.Count;
            var pos = spawnPosList[randIndex].Position;
            var rot = spawnPosList[randIndex].EulerAngles;
            var interactionEntityId = WorldEntityFactory.CreateEntity(new WorldResourceRecord()
            {
                EntityType = EntityTypeId.InteractionEntity,
                TemplateId = templateId,
                Pos = pos,
                Rot = rot,
                PoiLinkedPlayerId = self.Player.EntityId,
                SpawnType = SpawnTypeDefine.SpawnPoiTask,
            });

            self.GuideData.Add(new TaskGuideData(interactionEntityId, new CustomVector3(pos.x, pos.y, pos.z)));
            self.TaskComp.ListenTargetEvent(interactionEntityId, RemoveInteractionEntityCallbackHotfix);
            self.Logger.Info($"CreatePowerlineInteractionEntity. player:{self.Player.EntityId} task:{self.BizId} entityId:{interactionEntityId}");
        }

        [Hotfix(EventCallback = true)]
        public static void RemoveInteractionEntityCallback(this PlayerTaskComponent self, EntityBeforeRemoveEvent entityRemoveEvent)
        {
            var taskNode = self.Root.GetNodeByPath(NodeSystemType.TaskSystem, PlayerTaskContainerIndex.Poi, TaskNodeIndex.InProgress, HighSignalTaskNode.POI_TASK_HIGH_SIGNAL) as HighSignalTaskNode;
            if (null == taskNode)
            {
                return;
            }

            for (int i = 0; i < taskNode.GuideData.Count; i++)
            {
                if (taskNode.GuideData[i].EntityId == entityRemoveEvent.Entity.EntityId)
                {
                    taskNode.GuideData.RemoveAt(i);
                    break;
                }
            }
        }

        // 选择遗迹
        public static int ChoseNearestPowerline(this HighSignalTaskNode self)
        {
            // 选择遗迹
            float minDistance = -1;
            int nearestMonumentId = 0;
            foreach (var monumentId in WorldTerrainMeta.MatchedPowerlines.MatchedData.Keys)
            {
                var monument = WorldTerrainMeta.Monuments.GetTerrainMonument(monumentId);
                var distance = Vector3.Distance(monument.Transform.Position, new Vector3(self.Player.PosX, 0, self.Player.PosZ));
                if (minDistance < 0 || distance < minDistance)
                {
                    minDistance = distance;
                    nearestMonumentId = monumentId;
                }
            }

            self.Logger.Info($"ChoseNearestMonument. player:{self.Player.EntityId} monumentId:{nearestMonumentId}");
            return nearestMonumentId;
        }
    }
}
