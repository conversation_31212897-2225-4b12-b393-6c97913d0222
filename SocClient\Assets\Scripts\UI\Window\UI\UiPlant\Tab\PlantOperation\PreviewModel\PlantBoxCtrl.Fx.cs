using com.pixui;
using Effect;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Unity.ObjPool;
using WizardGames.Soc.SocClient.GoLoader;
using WizardGames.Soc.SocClient.Manager;

namespace WizardGames.Soc.SocClient.Ui
{
    public partial class PlantBoxCtrl
    {
        public class FxData
        {
            public ulong effectAsyncId;
            public GameObject goFx;
            public string fxPath;
            public int defaultLayer;
            private Renderer[] rendererlist;
            public void SetLayerToUI()
            {
                if (goFx != null)
                {
                    rendererlist = goFx.GetComponentsInChildren<Renderer>(true);
                    for (int i = 0;i < rendererlist.Length; i++)
                    {
                        if (rendererlist[i] != null)
                        {
                            rendererlist[i].gameObject.layer = LayerMask.NameToLayer("UI");
                        }
                    }
                }
            }
            
            public void RsetLayer()
            {
                if (rendererlist != null)
                {
                    for (int i = 0;i < rendererlist.Length; i++)
                    {
                        if (rendererlist[i] != null)
                        {
                            rendererlist[i].gameObject.layer = defaultLayer;
                        }
                    }
                }
            }
        }
        
        private List<EffectItemHandle<EffectItem>> effectItemHandleList = new List<EffectItemHandle<EffectItem>>();
        
        private List<FxData> fxDataList = new List<FxData>();
        // 采集尘土_小尘土（土豆，亚麻，蓝莓，玉米）
        private const int FX_ID = 9650;
        // 采集尘土_大尘土（南瓜）
        private const int FX_ID_PUMPKIN = 9651;
        
        private const long pumpkinItemId = 23070004;
        private long timerHandler = 0;

        private void Start()
        {
            if (IsInUIMode)
            {
                fxDataList = new List<FxData>(slots.Length);
                for (int i = 0; i < slots.Length; i++)
                {
                    FxData fxData = new FxData();
                    fxData.effectAsyncId = 0;
                    fxData.goFx = null;
                    fxData.fxPath = "";
                    fxDataList.Add(fxData);
                }
            }
            else
            {
                effectItemHandleList.Clear();
                
                for (int i =0;i< slots.Length; i++)
                {
                    EffectItemHandle<EffectItem> effectItemHandle = default;
                    effectItemHandleList.Add(effectItemHandle);
                } 
            }
        }
        private void OnHarvestSuccess(long collectionId, int idx)
        {
            if (collectionId == curPlantBox.EntityId)
            {
                if (IsInUIMode)
                {
                    ClearFx();
                    timerHandler = Mc.TimerWheel.AddTimerOnce(1000, (timerId,data,delete) =>
                    {
                        timerHandler = 0;
                        ClearFx();
                    });
                }
                else
                {
                    ClearEffectItemHandle();
                }

                if (idx == -1)
                {
                    for (int j = 0; j < slots.Length; j++)
                    {
                        Transform tr = slots[j];
                        PlayFx(j, collectionId, tr);
                    }
                }
                else
                {
                    if (idx < slots.Length)
                    {
                        Transform tr =  slots[idx];
                        PlayFx(idx, collectionId, tr);
                    }
                }
            }
        }

        private void PlayFx(int idx, long entityId, Transform trParent)
        {
            if (curPlantDic.TryGetValue(idx, out var playFxData))
            {
                if (playFxData.Stage == PlantStage.Harvest)
                {
                    if (IsInUIMode)
                    {
                        if (playFxData.SeedId == pumpkinItemId)
                        {
                            Common.Data.effect.EffectBase effectBase = Mc.Tables.TbEffectBase.GetOrDefault(FX_ID_PUMPKIN);
                            fxDataList[idx].fxPath = effectBase.Path;
                        }
                        else
                        {
                            Common.Data.effect.EffectBase effectBase = Mc.Tables.TbEffectBase.GetOrDefault(FX_ID);
                            fxDataList[idx].fxPath = effectBase.Path;
                        }
                        fxDataList[idx].effectAsyncId = GoPool.GetAsync(fxDataList[idx].fxPath, (go, objs) =>
                        {
                            fxDataList[idx].effectAsyncId = 0;
                            if (go == null)
                            {
                                return;
                            }

                            fxDataList[idx].goFx = go;
                            fxDataList[idx].defaultLayer = go.layer;
                            fxDataList[idx].SetLayerToUI();
                            fxDataList[idx].goFx.SetParent(trParent);
                            fxDataList[idx].goFx.transform.localPosition = Vector3.zero;
                            fxDataList[idx].goFx.transform.localScale = Vector3.one;
                            fxDataList[idx].goFx.SetActive(true); 
                        }, InstanceTypeId.DEFAULT_INSTANCE_TYPE_ID);
                    }
                    else
                    {
                        if (playFxData.SeedId == pumpkinItemId)
                        {
                            effectItemHandleList[idx] = Mc.Effect.PlayEffect<EffectItem>(FX_ID_PUMPKIN, entityId, trParent);
                        }
                        else
                        {
                            effectItemHandleList[idx] = Mc.Effect.PlayEffect<EffectItem>(FX_ID, entityId, trParent);
                        }
                    
                        effectItemHandleList[idx].AddOrCallLoadCompletedCallback(
                        (bool success, EffectItem effectItem, object o) =>
                        {
                            if (success)
                            {
                                effectItem.EffectGo.SetActive(true);
                            }
                        });
                    }
                }
            }
        }

        private void OnDestroyFx()
        {
            if (IsInUIMode)
            {
                if (timerHandler > 0)
                {
                    Mc.TimerWheel.CancelTimer(timerHandler);
                    timerHandler = 0;
                }
                ClearFx();
                fxDataList.Clear();
            }
            else
            {
                ClearEffectItemHandle();
                effectItemHandleList.Clear(); 
            }
        }
        
        private void ClearFx()
        {
            if (fxDataList == null)
            {
                return;
            }

            for (int i = 0; i < fxDataList.Count; i++)
            {
                if (fxDataList[i].effectAsyncId != 0)
                {
                    GoPool.CancelAsync(fxDataList[i].fxPath, fxDataList[i].effectAsyncId);
                    fxDataList[i].effectAsyncId = 0;
                }

                if (fxDataList[i].goFx != null)
                {
                    fxDataList[i].RsetLayer();
                    GoPool.Release(fxDataList[i].fxPath, fxDataList[i].goFx);
                }
            }
        }
        
        private void ClearEffectItemHandle()
        {
            if (effectItemHandleList != null)
            {
                for (int i = 0; i < effectItemHandleList.Count; i++)
                {
                    Mc.Effect.Release(effectItemHandleList[i],true);
                }
            }
        }
    }
}
