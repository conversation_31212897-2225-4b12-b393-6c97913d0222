{"hash": 416324876, "data": [{"guideId": 101, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [2001, 1001], "guideStep": [2001, 1001], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 893474981, "text": ""}, "entityInfo": {"index": 2124240763, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 102, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 8, "conditionParameter": ["14010004"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1812471246, "text": ""}, "entityInfo": {"index": 1006438366, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 103, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [2020], "guideStep": [2020], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 534590589, "text": ""}, "entityInfo": {"index": 712259237, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 104, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [2030], "guideStep": [2030], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1074881022, "text": ""}, "entityInfo": {"index": 269924088, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 105, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1050], "guideStep": [1050], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 806521926, "text": ""}, "entityInfo": {"index": 1851171234, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 106, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1060, 5050, 5051, 5052, 5053], "guideStep": [1060, 5050, 5051, 5052, 5053], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1370889469, "text": ""}, "entityInfo": {"index": 1064470377, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 107, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1001], "guideStep": [1001], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 164813813, "text": ""}, "entityInfo": {"index": 489079521, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 108, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1080], "guideStep": [1080], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 640032861, "text": ""}, "entityInfo": {"index": 662305379, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 109, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1090], "guideStep": [1090], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 2052798373, "text": ""}, "entityInfo": {"index": 788562580, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 110, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [2040, 2041, 2044, 2043], "guideStep": [2040, 2041, 2042, 2043], "pcStartStep": 2, "startStep": 2, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1706642260, "text": ""}, "entityInfo": {"index": 1554031801, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 111, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1535, 11111, 1115, 1113, 1114], "guideStep": [1535, 11111, 1111, 1112, 1113, 1114], "pcStartStep": 5, "startStep": 5, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1776021999, "text": ""}, "entityInfo": {"index": 1287536185, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 112, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1119, 1120, 1121, 1122], "guideStep": [1119, 1120, 1121, 1122], "pcStartStep": 2, "startStep": 2, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1972276099, "text": ""}, "entityInfo": {"index": 374638559, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 113, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1535, 1131], "guideStep": [1535, 1131], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1004302864, "text": ""}, "entityInfo": {"index": 2053047851, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 114, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1141, 1143, 1142, 1146], "guideStep": [1141, 1143, 1142, 1146], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1685885857, "text": ""}, "entityInfo": {"index": 690684993, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 115, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1150], "guideStep": [1150], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1031385927, "text": ""}, "entityInfo": {"index": 1655630661, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 116, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1160], "guideStep": [1160], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 577532711, "text": ""}, "entityInfo": {"index": 1497902337, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 117, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1170], "guideStep": [1170], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 936999935, "text": ""}, "entityInfo": {"index": 2072858271, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 118, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [209900, 1180], "guideStep": [209900, 1180], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1892252579, "text": ""}, "entityInfo": {"index": 781048509, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 119, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1190], "guideStep": [1190], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1219264885, "text": ""}, "entityInfo": {"index": 345903905, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 120, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1200, 1201, 1203, 1204], "guideStep": [1200, 1201, 1203, 1204], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 757154309, "text": ""}, "entityInfo": {"index": 62412425, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 121, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1210], "guideStep": [1210], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 846719180, "text": ""}, "entityInfo": {"index": 1382691387, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 122, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1220], "guideStep": [1220], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 41933610, "text": ""}, "entityInfo": {"index": 383392625, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 123, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1230], "guideStep": [1230], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 485028576, "text": ""}, "entityInfo": {"index": 1134479339, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 124, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1240], "guideStep": [1240], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 772010160, "text": ""}, "entityInfo": {"index": 298344476, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 125, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1250], "guideStep": [1250], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1282489580, "text": ""}, "entityInfo": {"index": 1862307800, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 126, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1260], "guideStep": [1260], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 207819432, "text": ""}, "entityInfo": {"index": 1551527459, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 127, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1270], "guideStep": [1270], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 35884823, "text": ""}, "entityInfo": {"index": 1349437868, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 128, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1280], "guideStep": [1280], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 342070302, "text": ""}, "entityInfo": {"index": 724722234, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 129, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1290, 1291, 1292, 1293, 1294], "guideStep": [1290, 1291, 1292, 1293, 1294], "pcStartStep": 2, "startStep": 2, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1281092288, "text": ""}, "entityInfo": {"index": 213256817, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 130, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1299, 5140, 5141, 5142], "guideStep": [1299, 5140, 5141, 5142], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1652668429, "text": ""}, "entityInfo": {"index": 1856466027, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 131, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1311, 1312, 1313, 1314], "guideStep": [1311, 1312, 1313, 1314], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1379452631, "text": ""}, "entityInfo": {"index": 1261819370, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 132, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1544, 6630001, 6631008, 6631002, 6631003, 6631004], "guideStep": [1535, 6630001, 6631008, 6631002, 6631003, 6631004], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 864249795, "text": ""}, "entityInfo": {"index": 1335445023, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 133, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1330], "guideStep": [1330], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 285338132, "text": ""}, "entityInfo": {"index": 457052826, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 134, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1340, 1341, 1342], "guideStep": [1340, 1341, 1342], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 705886350, "text": ""}, "entityInfo": {"index": 1693608104, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 135, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1001, 1350, 1333], "guideStep": [1001, 1350, 1333], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 2023455828, "text": ""}, "entityInfo": {"index": 754835780, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 136, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1332], "guideStep": [1332], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1964461990, "text": ""}, "entityInfo": {"index": 807926314, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 137, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1370], "guideStep": [1370], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 622522878, "text": ""}, "entityInfo": {"index": 988222340, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 138, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1381], "guideStep": [1381], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 817349908, "text": ""}, "entityInfo": {"index": 598145385, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 139, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1390, 1391, 1392], "guideStep": [1390, 1391, 1392], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 32920334, "text": ""}, "entityInfo": {"index": 1943218005, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 140, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1400], "guideStep": [1400], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 2095882085, "text": ""}, "entityInfo": {"index": 1786905201, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 141, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6627001, 6627002, 6627006, 6627003, 6627004, 6627005], "guideStep": [6627001, 6627002, 6627006, 6627003, 6627004, 6627005], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1557510005, "text": ""}, "entityInfo": {"index": 136317800, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 142, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1530, 1531, 1532, 1533, 1536, 1537, 1534], "guideStep": [1530, 1531, 1532, 1533, 1536, 1537, 1534], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1336864957, "text": ""}, "entityInfo": {"index": 997405290, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 150, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1500], "guideStep": [1500], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1672958193, "text": ""}, "entityInfo": {"index": 922568016, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 151, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1510], "guideStep": [1510], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 128739262, "text": ""}, "entityInfo": {"index": 1641422788, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 152, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1520, 1521, 1522], "guideStep": [1520, 1521, 1522], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 915140526, "text": ""}, "entityInfo": {"index": 1733708850, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 153, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1530, 1531, 1532, 1533, 1534], "guideStep": [1530, 1531, 1532, 1533, 1534], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 365946444, "text": ""}, "entityInfo": {"index": 1213088004, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 154, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1540, 1545, 1542, 1543], "guideStep": [1540, 1541, 1542, 1543], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 859074861, "text": ""}, "entityInfo": {"index": 613485099, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 155, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [10004, 10002, 10003], "guideStep": [10001, 10002, 10003], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 2086885280, "text": ""}, "entityInfo": {"index": 658280989, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 160, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1600, 1601, 1602, 1603], "guideStep": [1600, 1601, 1602, 1603], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 2134708734, "text": ""}, "entityInfo": {"index": 439091455, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 161, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1701, 1702, 1703, 1704, 1705], "guideStep": [1701, 1702, 1703, 1704, 1705], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1880445334, "text": ""}, "entityInfo": {"index": 1643288874, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 162, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1801, 1802, 1803, 1804, 1805, 1806, 1807], "guideStep": [1801, 1802, 1803, 1804, 1805, 1806, 1807], "pcStartStep": 6, "startStep": 6, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 397678105, "text": ""}, "entityInfo": {"index": 890841685, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 163, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1901], "guideStep": [1901], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1494693142, "text": ""}, "entityInfo": {"index": 1395161478, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 164, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1200, 1205, 1203, 1204], "guideStep": [1200, 1205, 1203, 1204], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 526404340, "text": ""}, "entityInfo": {"index": 590228692, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 165, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1210], "guideStep": [1210], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1821736631, "text": ""}, "entityInfo": {"index": 434809215, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 166, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1811, 1812, 1813, 1814], "guideStep": [1811, 1812, 1813, 1814], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1011820603, "text": ""}, "entityInfo": {"index": 1087731063, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 167, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6627001], "guideStep": [6627001], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1634137477, "text": ""}, "entityInfo": {"index": 325747103, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 168, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1680], "guideStep": [1680], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 851375833, "text": ""}, "entityInfo": {"index": 1061304646, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 169, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1690], "guideStep": [1690], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1754178411, "text": ""}, "entityInfo": {"index": 824704128, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 201, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 1, "conditionParameter": ["TreeEntity", "50010", "50011", "50020", "50021", "50022", "50023", "50030", "50031", "50032", "50033", "50040", "50041", "50042", "50050", "50051", "50052", "50060", "50061", "50062", "50063"], "pcGuideStep": [3001], "guideStep": [3001], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1321915776, "text": ""}, "entityInfo": {"index": 2138832194, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 202, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 9, "conditionParameter": ["TreeEntity", "50010", "50011", "50020", "50021", "50022", "50023", "50030", "50031", "50032", "50033", "50040", "50041", "50042", "50050", "50051", "50052", "50060", "50061", "50062", "50063"], "pcGuideStep": [3002], "guideStep": [3002], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1926496807, "text": ""}, "entityInfo": {"index": 789738013, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 203, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 8, "conditionParameter": ["11010014"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 562430811, "text": ""}, "entityInfo": {"index": 818012927, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 302, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 3, "conditionParameter": ["106"], "pcGuideStep": [4010], "guideStep": [4010], "pcStartStep": 0, "startStep": 0, "activationTime": 3, "activationType": 1, "entityTitle": {"index": 1904875938, "text": ""}, "entityInfo": {"index": 1327851846, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 303, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 3, "conditionParameter": ["110"], "pcGuideStep": [4020], "guideStep": [4020], "pcStartStep": 0, "startStep": 0, "activationTime": 3, "activationType": 1, "entityTitle": {"index": 1579985979, "text": ""}, "entityInfo": {"index": 1334002324, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 304, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 3, "conditionParameter": ["107"], "pcGuideStep": [3040], "guideStep": [3040], "pcStartStep": 0, "startStep": 0, "activationTime": 3, "activationType": 1, "entityTitle": {"index": 597753466, "text": ""}, "entityInfo": {"index": 1478969210, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 305, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 6, "conditionParameter": ["13010049"], "pcGuideStep": [3050], "guideStep": [3050], "pcStartStep": 0, "startStep": 0, "activationTime": 3, "activationType": 1, "entityTitle": {"index": 249262926, "text": ""}, "entityInfo": {"index": 859785523, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 401, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 4, "conditionParameter": [], "pcGuideStep": [5001], "guideStep": [5001], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1214781541, "text": ""}, "entityInfo": {"index": 61934926, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 402, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 14, "conditionParameter": [], "pcGuideStep": [5010], "guideStep": [5010], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 1, "entityTitle": {"index": 555687216, "text": ""}, "entityInfo": {"index": 21418186, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 403, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 16, "conditionParameter": [], "pcGuideStep": [5011], "guideStep": [5011], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 1, "entityTitle": {"index": 1473121821, "text": ""}, "entityInfo": {"index": 1146718807, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 501, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 2, "conditionParameter": ["UiInventory"], "pcGuideStep": [6002, 6003, 6004], "guideStep": [6002, 6003, 6004], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1372581339, "text": ""}, "entityInfo": {"index": 1476429192, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 502, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 539628280, "text": ""}, "entityInfo": {"index": 759661745, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 503, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 2, "conditionParameter": ["UiSwipeCardGame"], "pcGuideStep": [5031, 5032, 5033], "guideStep": [5031, 5032, 5033], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 203255195, "text": ""}, "entityInfo": {"index": 1345385952, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 504, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 2, "conditionParameter": ["UiMissionView"], "pcGuideStep": [5040], "guideStep": [5040], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 539406477, "text": ""}, "entityInfo": {"index": 89086454, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 506, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 2, "conditionParameter": ["UiOtherSide"], "pcGuideStep": [5060, 5061, 5062], "guideStep": [5060, 5061, 5062], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 803468593, "text": ""}, "entityInfo": {"index": 413772673, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 507, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 2, "conditionParameter": ["UiInventory"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 177421146, "text": ""}, "entityInfo": {"index": 1219552127, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 508, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 2, "conditionParameter": ["UiReputationReward"], "pcGuideStep": [5080, 5081], "guideStep": [5080, 5081], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 764773910, "text": ""}, "entityInfo": {"index": 995193016, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 509, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 2, "conditionParameter": ["UiRespawn"], "pcGuideStep": [5090, 5091], "guideStep": [5090, 5091], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 2060296117, "text": ""}, "entityInfo": {"index": 963521500, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": true}, {"guideId": 510, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1001], "guideStep": [1001], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1394570925, "text": ""}, "entityInfo": {"index": 578334507, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 511, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 8, "conditionParameter": ["11010004"], "pcGuideStep": [5110], "guideStep": [5110], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1746987946, "text": ""}, "entityInfo": {"index": 1250452511, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 512, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 10, "conditionParameter": ["11010001"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1370178465, "text": ""}, "entityInfo": {"index": 2127617107, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 513, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 10, "conditionParameter": ["10010002"], "pcGuideStep": [5130], "guideStep": [5130], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1759618050, "text": ""}, "entityInfo": {"index": 1792978457, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 514, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 2, "conditionParameter": ["UiOtherSideNew"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1938071022, "text": ""}, "entityInfo": {"index": 1830869042, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 515, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [5150, 5151, 5152], "guideStep": [5150, 5151, 5152], "pcStartStep": 2, "startStep": 2, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1660917645, "text": ""}, "entityInfo": {"index": 571758807, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 1, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 516, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [5160, 5163], "guideStep": [5160, 5163], "pcStartStep": 2, "startStep": 2, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1091052504, "text": ""}, "entityInfo": {"index": 1794877728, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 1, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 517, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [5161, 5163], "guideStep": [5161, 5163], "pcStartStep": 2, "startStep": 2, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 806097605, "text": ""}, "entityInfo": {"index": 608298406, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 1, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 518, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [5162, 5163], "guideStep": [5162, 5163], "pcStartStep": 2, "startStep": 2, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1278610693, "text": ""}, "entityInfo": {"index": 890279850, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 1, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 601, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 5, "conditionParameter": [], "pcGuideStep": [4030], "guideStep": [4030], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 805882506, "text": ""}, "entityInfo": {"index": 2138460995, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 701, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 6, "conditionParameter": ["22010001"], "pcGuideStep": [7001], "guideStep": [7001], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1624806058, "text": ""}, "entityInfo": {"index": 1879500793, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 801, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 11, "conditionParameter": [], "pcGuideStep": [8001], "guideStep": [8001], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 610678342, "text": ""}, "entityInfo": {"index": 1143326112, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 901, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 10, "conditionParameter": ["10010044"], "pcGuideStep": [9001], "guideStep": [9001], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 601241967, "text": ""}, "entityInfo": {"index": 834503584, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 905, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 13, "conditionParameter": ["3000", "3001"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1718653590, "text": ""}, "entityInfo": {"index": 903424935, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1001, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["10", "TreeEntity", "50020", "50021", "50022", "50023", "50024", "50025", "50026", "50027", "50028", "50029", "50030", "50031", "50032", "50050", "50051", "50052", "50053", "50054", "50055", "50056", "50057", "50058", "50059", "50062", "50063", "50064", "50065", "50066", "50067", "50068", "50069", "50070", "50071", "50072", "50073", "50074", "50075", "50076", "50077", "50078", "50079", "50080", "50081", "50082", "50083", "50086", "50087", "50088", "50089", "50090", "50091", "50092", "50093", "50094", "50095", "50096", "50097", "50100", "50101", "50102", "50103", "50104"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 196267737, "text": "树"}, "entityInfo": {"index": 569983659, "text": "装备石斧等工具采集可获得木头"}, "entityOffset": [[50010, {"x": 0, "y": 10, "z": 0}], [0, {"x": 0, "y": 3, "z": 0}]], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1002, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["10", "OreEntity", "52003"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 161640572, "text": "石头"}, "entityInfo": {"index": 1738690365, "text": "装备石镐等工具采集可获得石头"}, "entityOffset": [[0, {"x": 0, "y": 3, "z": 0}]], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1003, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["10", "OreEntity", "52002"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1211942552, "text": "金属矿"}, "entityInfo": {"index": 1262099996, "text": "装备石镐等工具采集可获得金属矿石"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1004, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["10", "OreEntity", "52001"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 500335429, "text": "硫矿"}, "entityInfo": {"index": 557604075, "text": "装备石镐等工具采集可获得硫磺"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1010, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["10", "CollectableEntity", "53006"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 603943213, "text": "玉米"}, "entityInfo": {"index": 1932523995, "text": "采集可获得玉米，是常见的食物之一，常在河边出现"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1011, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["10", "CollectableEntity", "53007"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 996677405, "text": "南瓜"}, "entityInfo": {"index": 626329053, "text": "采集可获得南瓜，是常见的食物之一，常在河边出现"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1012, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["10", "CollectableEntity", "53008"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 942186591, "text": "土豆"}, "entityInfo": {"index": 1036496253, "text": "采集可获得土豆，是常见的食物之一，常在河边出现"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1013, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["10", "CollectableEntity", "53009", "53010", "53011", "53012", "53013", "53014"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1487756382, "text": "浆果"}, "entityInfo": {"index": 1939419234, "text": "采集可获得浆果，常在温带森林中出现"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1014, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["10", "CollectableEntity", "53015"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 596583736, "text": "亚麻纤维"}, "entityInfo": {"index": 1242577109, "text": "采集可获得布，常用的制作资源"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1015, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["5", "CollectableEntity", "53016", "53017", "53018", "53019"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 2078746638, "text": "蘑菇"}, "entityInfo": {"index": 1345261245, "text": "采集可获得蘑菇，常在温带森林中出现"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1016, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["10", "TreeEntity", "50000", "50001", "50002"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1271945707, "text": "仙人掌"}, "entityInfo": {"index": 185867727, "text": "采集可获得布，常用的制作资源"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1200, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["30", "MonsterEntity", "10001"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 2081415679, "text": "熊"}, "entityInfo": {"index": 327505632, "text": "攻击性高，且防御值高，建议采用枪械进行狩猎"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1201, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["30", "MonsterEntity", "10002"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1333738648, "text": "北极熊"}, "entityInfo": {"index": 512788183, "text": "攻击性高，且防御值高，建议采用枪械进行狩猎"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1202, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["30", "MonsterEntity", "10003"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 248276507, "text": "野猪"}, "entityInfo": {"index": 143589584, "text": "具备攻击性，击败后可采集获得皮毛与碎骨"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1203, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["30", "MonsterEntity", "10004"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 112834166, "text": "鹿"}, "entityInfo": {"index": 239888655, "text": "无攻击性，但警惕性强，建议装备远程武器狩猎"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1204, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["30", "MonsterEntity", "10005"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 208187222, "text": "鸡"}, "entityInfo": {"index": 916207336, "text": "无攻击性，击败后可采集获得鸡肉"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1205, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["30", "MonsterEntity", "10006"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 2031467788, "text": "狼"}, "entityInfo": {"index": 1382915822, "text": "攻击性强，移动速度快，建议采用枪械进行狩猎"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1250, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 12, "conditionParameter": ["10", "CorpseEntity", "60001", "60002", "60003", "60004", "60005", "60006"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1880102629, "text": "动物尸体"}, "entityInfo": {"index": 2106965725, "text": "装备石斧、石镐等肉类采集工具可进行分解"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1300, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["20", "PartEntity", "11010017", "11010021"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1818269175, "text": "修理台"}, "entityInfo": {"index": 947684997, "text": "放置工具并消耗废料可进行维修"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1301, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["20", "PartEntity", "11010015", "11010022"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 141723813, "text": "研究台"}, "entityInfo": {"index": 1368550274, "text": "放置工具并消耗废料可研究获取对应蓝图用于制造"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1302, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["20", "PartEntity", "11010013", "11010023"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1555121416, "text": "分解机"}, "entityInfo": {"index": 1972704368, "text": "可将物资分解为基础物资资源"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1303, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["20", "PartEntity", "11010024"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1071465893, "text": "炼油机"}, "entityInfo": {"index": 728492746, "text": "可将原油冶炼至低品质燃油"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1304, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["10", "PartEntity", "11010014"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 175649142, "text": "睡袋"}, "entityInfo": {"index": 304160724, "text": "睡袋可作为淘汰后的重生点"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1305, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["10", "PartEntity", "11010004", "11010025"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 489399158, "text": "一级工作台"}, "entityInfo": {"index": 908243791, "text": "在工作台可制作或解锁进阶物品"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1306, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["10", "DigEntity", "20000", "20001", "20002"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1855276208, "text": "挖矿机"}, "entityInfo": {"index": 184318784, "text": "在燃料桶添加柴油后打开开关发动挖矿机"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1307, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["10", "PartEntity", "11010003"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 69654122, "text": "营火"}, "entityInfo": {"index": 22730668, "text": "打开营火放置木头后可进行点燃"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1400, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["40", "TrainCarEntity", "22030009", "22030013"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 291015087, "text": "火车"}, "entityInfo": {"index": 903072787, "text": "使用低品质燃油可作为燃料驾驶火车"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1401, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["40", "HorseEntity", "22030010"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1626389275, "text": "马匹"}, "entityInfo": {"index": 1423162495, "text": "可驾驶马匹移动得更快"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1402, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["40", "ModularCarEntity", "22030003", "22030004", "22030005"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1168165755, "text": "载具"}, "entityInfo": {"index": 1250097244, "text": "安装载具对应配件可驾驶载具"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1403, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["40", "VehicleEntity", "22030007"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1512033989, "text": "小型直升机"}, "entityInfo": {"index": 1281824392, "text": "小型直升机，可承载2人，最基础的空中载具"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1404, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["40", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2000"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 697486992, "text": "多管火箭系统"}, "entityInfo": {"index": 1245875007, "text": "多管火箭车，需要两个道具操控多管火箭系统，对指定区域进行轰炸"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1405, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["40", "VehicleEntity", "22030001"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 790943385, "text": "快艇"}, "entityInfo": {"index": 345735620, "text": "更加快速的水上载具，以低品质燃油作为燃料，通常在海边可以找到"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1406, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["40", "VehicleEntity", "11010043"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1205817712, "text": "木舟"}, "entityInfo": {"index": 1162475852, "text": "最基础的水上载具，乘坐后装备浆可移动"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1500, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["20", "BoxEntity", "1", "4"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 196131815, "text": "物资箱"}, "entityInfo": {"index": 1315904006, "text": "科伯特运输队掉落下的物资箱"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1501, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["20", "BoxEntity", "1", "9"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 403337120, "text": "食物盒"}, "entityInfo": {"index": 1544057233, "text": "里面或许会有食物"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1502, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["20", "BoxEntity", "1", "10"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1860905869, "text": "蓝油桶"}, "entityInfo": {"index": 2047207459, "text": "打碎可获得其中的废料和配件"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1503, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["20", "BoxEntity", "1", "11"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 401782547, "text": "黄油桶"}, "entityInfo": {"index": 1712430138, "text": "打碎可获得其中的废料和配件"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1504, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["20", "BoxEntity", "1", "12"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1044081369, "text": "红油桶"}, "entityInfo": {"index": 1437773002, "text": "打碎可较大概率获得燃油资源"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1505, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["20", "BoxEntity", "1", "13"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 71006032, "text": "零件盒"}, "entityInfo": {"index": 1890123824, "text": "装有废料及零件"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1506, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["20", "BoxEntity", "1", "14"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1081494926, "text": "空投"}, "entityInfo": {"index": 911736237, "text": "飞机投放的物资箱，或许会有较多资源"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1507, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["20", "BoxEntity", "1", "15"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 135967267, "text": "木板"}, "entityInfo": {"index": 516860947, "text": "看起来是很脆弱的木板，或许可以打碎"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1508, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["20", "BoxEntity", "1", "16"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 771434087, "text": "柴油箱"}, "entityInfo": {"index": 1241732212, "text": "装满了柴油，可直接拾取获得"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1509, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["20", "BoxEntity", "1", "17"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 214486031, "text": "战利品"}, "entityInfo": {"index": 718000448, "text": "被淘汰后所携带物资会掉落在背包中"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1510, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["20", "BoxEntity", "1", "18"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 633980969, "text": "路标"}, "entityInfo": {"index": 2048371343, "text": "有人把路标拆了做成装备"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 1511, "disablePlayid": [5, 6, 101, 102], "dialogId": 0, "guideType": 2, "triggerCondition": 12, "conditionParameter": ["20", "BoxEntity", "1", "19"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 119218991, "text": "滑索"}, "entityInfo": {"index": 590152414, "text": "可快速移动至另一高塔"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2001, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": ["UiLobbyMain", "UiSelectPlayMode", "UiLobbyTeam"], "pcGuideStep": [20001, 20002, 20003], "guideStep": [20001, 20002, 20003], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 2127876247, "text": ""}, "entityInfo": {"index": 1706077227, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 2, "ignoreDeathLimit": false}, {"guideId": 3001, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 13, "conditionParameter": ["2"], "pcGuideStep": [30010], "guideStep": [30010], "pcStartStep": 0, "startStep": 0, "activationTime": 3, "activationType": 1, "entityTitle": {"index": 669016982, "text": ""}, "entityInfo": {"index": 49326708, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 3002, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 13, "conditionParameter": ["7"], "pcGuideStep": [30020], "guideStep": [30020], "pcStartStep": 0, "startStep": 0, "activationTime": 3, "activationType": 1, "entityTitle": {"index": 285905577, "text": ""}, "entityInfo": {"index": 813452679, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 3003, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 13, "conditionParameter": ["18"], "pcGuideStep": [30030], "guideStep": [30030], "pcStartStep": 0, "startStep": 0, "activationTime": 3, "activationType": 1, "entityTitle": {"index": 1294803662, "text": ""}, "entityInfo": {"index": 1769201781, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 3004, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 13, "conditionParameter": ["22"], "pcGuideStep": [30040], "guideStep": [30040], "pcStartStep": 0, "startStep": 0, "activationTime": 3, "activationType": 1, "entityTitle": {"index": 1295241318, "text": ""}, "entityInfo": {"index": 2139613643, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 3005, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 13, "conditionParameter": ["9"], "pcGuideStep": [30050], "guideStep": [30050], "pcStartStep": 0, "startStep": 0, "activationTime": 3, "activationType": 1, "entityTitle": {"index": 1261873303, "text": ""}, "entityInfo": {"index": 1995767812, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 3006, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 13, "conditionParameter": ["10"], "pcGuideStep": [30060], "guideStep": [30060], "pcStartStep": 0, "startStep": 0, "activationTime": 3, "activationType": 1, "entityTitle": {"index": 109711010, "text": ""}, "entityInfo": {"index": 105793610, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 3007, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 13, "conditionParameter": ["5000", "5001"], "pcGuideStep": [30070], "guideStep": [30070], "pcStartStep": 0, "startStep": 0, "activationTime": 3, "activationType": 1, "entityTitle": {"index": 795400393, "text": ""}, "entityInfo": {"index": 1045551353, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 3008, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 13, "conditionParameter": ["11"], "pcGuideStep": [30080], "guideStep": [30080], "pcStartStep": 0, "startStep": 0, "activationTime": 3, "activationType": 1, "entityTitle": {"index": 975253393, "text": ""}, "entityInfo": {"index": 2047156342, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 3009, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 13, "conditionParameter": ["17"], "pcGuideStep": [30090], "guideStep": [30090], "pcStartStep": 0, "startStep": 0, "activationTime": 3, "activationType": 1, "entityTitle": {"index": 1679056116, "text": ""}, "entityInfo": {"index": 1755349087, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6601, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 3, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 864320654, "text": ""}, "entityInfo": {"index": 533087255, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6602, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [662001, 662002, 662003], "guideStep": [662001, 662002, 662003], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 798159769, "text": ""}, "entityInfo": {"index": 2056458884, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6603, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [663001, 663002, 663003], "guideStep": [663001, 663002, 663003], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1229245849, "text": ""}, "entityInfo": {"index": 1529550915, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6604, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [664001, 664003, 664004], "guideStep": [664001, 664003, 664004], "pcStartStep": 2, "startStep": 2, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 991255375, "text": ""}, "entityInfo": {"index": 1650676811, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6605, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [665001], "guideStep": [665001], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 603855496, "text": ""}, "entityInfo": {"index": 1148269941, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6606, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [666001, 666002, 666003], "guideStep": [666001, 666002, 666003], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1128631047, "text": ""}, "entityInfo": {"index": 837921157, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6607, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1147, 1381], "guideStep": [1147, 1381], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1871991738, "text": ""}, "entityInfo": {"index": 194648616, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6608, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [668001], "guideStep": [668001], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1170126803, "text": ""}, "entityInfo": {"index": 856567227, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6609, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [669001, 669002, 669003], "guideStep": [669001, 669002, 669003], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 987145904, "text": ""}, "entityInfo": {"index": 1643559281, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6610, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6610002, 6610003], "guideStep": [6610002, 6610003], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1209871380, "text": ""}, "entityInfo": {"index": 1353834885, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6611, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6611001], "guideStep": [6611001], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 939426721, "text": ""}, "entityInfo": {"index": 1809931258, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6612, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6612001, 6612002], "guideStep": [6612001, 6612002], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1298050645, "text": ""}, "entityInfo": {"index": 519013917, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6613, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6613001], "guideStep": [6613001], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1419282567, "text": ""}, "entityInfo": {"index": 2142180580, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6614, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6614001, 6614002], "guideStep": [6614001, 6614002], "pcStartStep": 2, "startStep": 2, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 855543032, "text": ""}, "entityInfo": {"index": 56279095, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6615, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6615002], "guideStep": [6615002], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1189261037, "text": ""}, "entityInfo": {"index": 538956255, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6616, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1290], "guideStep": [1290], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1584532762, "text": ""}, "entityInfo": {"index": 250369675, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6617, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1050, 6617001], "guideStep": [1050, 6617001], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 564664864, "text": ""}, "entityInfo": {"index": 1491083979, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6618, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6618001, 6618002], "guideStep": [6618001, 6618002], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 556373805, "text": ""}, "entityInfo": {"index": 828666583, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6619, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6625001, 6625002], "guideStep": [6625001, 6625002], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1853527195, "text": ""}, "entityInfo": {"index": 1822198010, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6620, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1060], "guideStep": [1060], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 19888334, "text": ""}, "entityInfo": {"index": 395302500, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6621, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6629001], "guideStep": [6629001], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 327223024, "text": ""}, "entityInfo": {"index": 773989248, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6622, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6628001, 1146], "guideStep": [6628001, 1146], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 612765624, "text": ""}, "entityInfo": {"index": 1016565576, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6623, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6623001], "guideStep": [6623001], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 403344439, "text": ""}, "entityInfo": {"index": 1993579150, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6624, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6623003], "guideStep": [6623003], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 294505168, "text": ""}, "entityInfo": {"index": 724017564, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6625, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6625001, 6625002], "guideStep": [6625001, 6625002], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 52952033, "text": ""}, "entityInfo": {"index": 1487043297, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6627, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6627001, 6627002, 6627006, 6627003, 6627004, 6627005], "guideStep": [6627001, 6627002, 6627006, 6627003, 6627004, 6627005], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1566647894, "text": ""}, "entityInfo": {"index": 2039948078, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6628, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6626003], "guideStep": [6626003], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1759811735, "text": ""}, "entityInfo": {"index": 798388247, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6629, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6627001, 6627002, 6627006], "guideStep": [6627001, 6627002, 6627006], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 284228222, "text": ""}, "entityInfo": {"index": 179835504, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6630, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6617001, 6617002], "guideStep": [6617001, 6617002], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 228485923, "text": ""}, "entityInfo": {"index": 819101512, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6631, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6631005, 6631006], "guideStep": [6631005, 6631006], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1752204149, "text": ""}, "entityInfo": {"index": 1121683630, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6632, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6631007], "guideStep": [6631007], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1929790175, "text": ""}, "entityInfo": {"index": 2120361998, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6633, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6627008], "guideStep": [6627008], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 129988272, "text": ""}, "entityInfo": {"index": 820669005, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6634, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1535], "guideStep": [1535], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 152089482, "text": ""}, "entityInfo": {"index": 457548868, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6635, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6629002], "guideStep": [6629002], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 590474710, "text": ""}, "entityInfo": {"index": 1096417508, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6636, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [6615001], "guideStep": [6615001], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 187341665, "text": ""}, "entityInfo": {"index": 300015984, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 7004, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [7004001, 7004002, 7004003, 7004004, 7004005, 7004006, 7004007], "guideStep": [7004001, 7004002, 7004003, 7004004, 7004005, 7004006, 7004007], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 564174615, "text": ""}, "entityInfo": {"index": 69113810, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 7005, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [30100], "guideStep": [30100], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 919809611, "text": ""}, "entityInfo": {"index": 841064558, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 7006, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [30200], "guideStep": [30200], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1665090293, "text": ""}, "entityInfo": {"index": 512015947, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 7007, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 1, "conditionParameter": ["5", "HorseEntity", "22030010"], "pcGuideStep": [30300], "guideStep": [30300], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 1077333818, "text": ""}, "entityInfo": {"index": 423647712, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 7008, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [70080, 70081, 70082], "guideStep": [70080, 70081, 70082], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1030036280, "text": ""}, "entityInfo": {"index": 2094072239, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 7009, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [70090, 70091], "guideStep": [70090, 70091], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 524176535, "text": ""}, "entityInfo": {"index": 1358171168, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 7010, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [70100], "guideStep": [70100], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1670050029, "text": ""}, "entityInfo": {"index": 1973394331, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2060, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [206000], "guideStep": [206000], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1642028638, "text": ""}, "entityInfo": {"index": 4517831, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2061, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [206100], "guideStep": [206100], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 76431627, "text": ""}, "entityInfo": {"index": 664309957, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2062, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [206200], "guideStep": [206200], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 517709449, "text": ""}, "entityInfo": {"index": 249022028, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2063, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [206300, 206302, 206305], "guideStep": [206300, 206302, 206305], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1967215448, "text": ""}, "entityInfo": {"index": 248738953, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2064, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [206400, 206400, 206400], "guideStep": [206400, 206400, 206400], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 298731282, "text": ""}, "entityInfo": {"index": 1842429838, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2065, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [206500], "guideStep": [206500], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 513959718, "text": ""}, "entityInfo": {"index": 583900686, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2066, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [206600, 206640], "guideStep": [206600, 206640], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1195209682, "text": ""}, "entityInfo": {"index": 1615081406, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2067, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [206700], "guideStep": [206700], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 388528053, "text": ""}, "entityInfo": {"index": 1339278173, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2068, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [206800, 206802, 206805], "guideStep": [206800, 206802, 206805], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 821386573, "text": ""}, "entityInfo": {"index": 350570755, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2069, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [206900], "guideStep": [206900], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1095089524, "text": ""}, "entityInfo": {"index": 2083351437, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2070, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [207000, 207020], "guideStep": [207000, 207020], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 42831156, "text": ""}, "entityInfo": {"index": 592106747, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2071, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [207100, 207130], "guideStep": [207100, 207130], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 903038332, "text": ""}, "entityInfo": {"index": 1775653822, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2072, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1147], "guideStep": [1147], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 196937993, "text": ""}, "entityInfo": {"index": 638019361, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2073, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [206650, 206700], "guideStep": [206650, 206700], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1367171759, "text": ""}, "entityInfo": {"index": 361415571, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2074, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [207140], "guideStep": [207140], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 536163770, "text": ""}, "entityInfo": {"index": 429426396, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2075, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [206650, 207500], "guideStep": [206650, 207500], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 529029366, "text": ""}, "entityInfo": {"index": 564931360, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2076, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [206810, 6610002, 6610003], "guideStep": [206810, 6610002, 6610003], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1325909992, "text": ""}, "entityInfo": {"index": 263477833, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2077, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [207700], "guideStep": [207700], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 86334712, "text": ""}, "entityInfo": {"index": 1613436726, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2078, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [207800], "guideStep": [207800], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 208071131, "text": ""}, "entityInfo": {"index": 261788408, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2079, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [207900], "guideStep": [207900], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 489162148, "text": ""}, "entityInfo": {"index": 2095576821, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2080, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [207500], "guideStep": [207500], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 299264557, "text": ""}, "entityInfo": {"index": 1687624270, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2081, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [208100], "guideStep": [208100], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 106535644, "text": ""}, "entityInfo": {"index": 2118603716, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2082, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [208200], "guideStep": [208200], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 320775821, "text": ""}, "entityInfo": {"index": 1972436348, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2083, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [208210], "guideStep": [208210], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 2047929846, "text": ""}, "entityInfo": {"index": 335329639, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2084, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [208400, 208401, 208405], "guideStep": [208400, 208401, 208405], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 2303210, "text": ""}, "entityInfo": {"index": 581596855, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2085, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [208510, 208500, 208505], "guideStep": [208510, 208500, 208505], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 500952833, "text": ""}, "entityInfo": {"index": 1164085929, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2086, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [208600], "guideStep": [208600], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1056314478, "text": ""}, "entityInfo": {"index": 1639707493, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2087, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 4, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 87768248, "text": ""}, "entityInfo": {"index": 1289437700, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2089, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [208900], "guideStep": [208900], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 354991676, "text": ""}, "entityInfo": {"index": 918835374, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2090, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [209000], "guideStep": [209000], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 205030861, "text": ""}, "entityInfo": {"index": 1758246213, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2091, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [209100], "guideStep": [209100], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 494905927, "text": ""}, "entityInfo": {"index": 1105609693, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2092, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [209200], "guideStep": [209200], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 135697409, "text": ""}, "entityInfo": {"index": 956844667, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 2, "ignoreDeathLimit": false}, {"guideId": 209201, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [1381], "guideStep": [1381], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 128869167, "text": ""}, "entityInfo": {"index": 2026299403, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 209202, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 12, "conditionParameter": ["20", "BoxEntity", "0", "20601"], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 1, "entityTitle": {"index": 872692675, "text": "路障"}, "entityInfo": {"index": 700537102, "text": "制作石斧，击破路障"}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 209203, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [], "guideStep": [], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 2031104710, "text": ""}, "entityInfo": {"index": 688830253, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2093, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [209300, 209301, 209302, 209303, 209304], "guideStep": [209300, 209301, 209302, 209303, 209304], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 770537261, "text": ""}, "entityInfo": {"index": 1938332996, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 2, "ignoreDeathLimit": false}, {"guideId": 2094, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [209400, 209401, 209402], "guideStep": [209400, 209401, 209402], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1382613272, "text": ""}, "entityInfo": {"index": 1118890254, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 2, "ignoreDeathLimit": false}, {"guideId": 2095, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [209503, 209500, 209501, 209502], "guideStep": [209503, 209500, 209501, 209502], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 596462078, "text": ""}, "entityInfo": {"index": 59820697, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 2, "ignoreDeathLimit": false}, {"guideId": 2096, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [209600, 209604, 209605, 209608, 209606, 209607], "guideStep": [209600, 209604, 209605, 209608, 209606, 209607], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1080813029, "text": ""}, "entityInfo": {"index": 1554635815, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 2, "ignoreDeathLimit": false}, {"guideId": 2097, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [209700, 209701, 209702], "guideStep": [209700, 209701, 209702], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1217212446, "text": ""}, "entityInfo": {"index": 1217359744, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2098, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [209800, 209801, 209802, 209803, 209804, 209805, 209806, 209807], "guideStep": [209800, 209801, 209802, 209803, 209804, 209805, 209806, 209807], "pcStartStep": 5, "startStep": 5, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1135112074, "text": ""}, "entityInfo": {"index": 1487762419, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2100, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [210002, 210000, 210001], "guideStep": [210002, 210000, 210001], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1278146041, "text": ""}, "entityInfo": {"index": 725829511, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2101, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [210100, 210101], "guideStep": [210100, 210101], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1953129495, "text": ""}, "entityInfo": {"index": 480700137, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 2, "ignoreDeathLimit": false}, {"guideId": 2102, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [210100, 210200], "guideStep": [210100, 210200], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 210842856, "text": ""}, "entityInfo": {"index": 18614466, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 2, "ignoreDeathLimit": false}, {"guideId": 2103, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [210300, 210301, 210302, 210303, 210304, 210305], "guideStep": [210300, 210301, 210302, 210303, 210304, 210305], "pcStartStep": 0, "startStep": 0, "activationTime": -1, "activationType": 2, "entityTitle": {"index": 1217296646, "text": ""}, "entityInfo": {"index": 176221170, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 2, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 2104, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 0, "conditionParameter": [], "pcGuideStep": [209606, 209607], "guideStep": [209606, 209607], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 419926915, "text": ""}, "entityInfo": {"index": 2118132416, "text": ""}, "entityOffset": [], "interruptHandling": 2, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 2, "ignoreDeathLimit": false}, {"guideId": 6010, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 2, "conditionParameter": ["UiTreasureHuntGame"], "pcGuideStep": [601000], "guideStep": [601000], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 323026604, "text": ""}, "entityInfo": {"index": 343065927, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6020, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 2, "conditionParameter": ["UiBoxOpeningGame"], "pcGuideStep": [602000], "guideStep": [602000], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 1023419364, "text": ""}, "entityInfo": {"index": 425265784, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6030, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 2, "conditionParameter": ["UiTerritoryEdit"], "pcGuideStep": [603000], "guideStep": [603000], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 50004876, "text": ""}, "entityInfo": {"index": 320846199, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}, {"guideId": 6040, "disablePlayid": [6, 101, 102], "dialogId": 0, "guideType": 1, "triggerCondition": 2, "conditionParameter": ["UiCommonTeamBattle"], "pcGuideStep": [604000], "guideStep": [604000], "pcStartStep": 0, "startStep": 0, "activationTime": 1, "activationType": 2, "entityTitle": {"index": 2011343384, "text": ""}, "entityInfo": {"index": 1836191213, "text": ""}, "entityOffset": [], "interruptHandling": 1, "storageType": 1, "isUnique": false, "guidePriority": 0, "guidePlace": 1, "ignoreDeathLimit": false}]}