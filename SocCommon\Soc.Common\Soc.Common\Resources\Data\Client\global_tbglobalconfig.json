{"hash": 2105523301, "data": [{"MapExtension": 1500, "SpawnGridSize": 100, "WorldSpawnRange": 10, "SpawnHorseAroundPlayerRadius": [20, 40], "SpawnHorseAroundPlayerInternal": 15, "SpawnHorseDestroyDistance": 150, "SpawnHorseNoRideCD": 10, "DefaultGraphName": "GraphEmpty", "JunkPileBoundsRadius": 5, "MuiltLanInfo": {"index": 1361357773, "text": "EnUS"}, "CorpseTime": 300, "RewardBoxDestroyTime": 3600, "CommonComposeQueueMaxSize": 10, "CommonComposeDiffLevel1Rate": 0.5, "CommonComposeDiffLevel2Rate": 0.25, "CraftCommonBlueprintCount": 6, "SpawnTrainCountInSplie_3x3": [1, 1], "SpawnTrainCountInSplie_4x4": [1, 1], "SpawnTrainDistanceFromFork": [30, 50], "TrainUnloadNormalCard": 22010004, "TrainUnloadUltraCard": 22010005, "TrainDischargeTemplateId": 700, "Fuel2TemperatureRate": 200, "QuickLootCdTime": 400, "PickupCdTime": 450, "ForbidPickupCdTime": 300000, "ShootPutAwayPickup": true, "HurtPutAwayPickup": true, "ThrowPutAwayPickup": true, "PickupRestoreTime": 600, "PickupTriggerTime": 300, "AirDropBoxId": 30014, "AirDropPlaneHeight": 50, "AirDropRandomCd": 3600, "AirDropPlaneDisScale": 1.4, "AirDropPlaneTimeScale": 1, "PlaneTrail1": 7016, "PlaneTrail2": 7017, "DroppedGravityCoefficient": 0.7, "DroppedInitialVelocityMultiplier": 2.3, "AttackInteractiveDroppedGravityCoefficient": 1.8, "AttackInteractiveDroppedInitialVelocityMultiplier": 0.6, "DiscardedMergingDistance": 3, "DiscardedMergeDistanceUP": 1.9, "DiscardedMergeDistanceDown": 3, "SecondaryMergeTime": 30, "MergeDelayTime": 1, "WorkBenchSearchDistance": 4, "ShowNameDistance": 4, "CharacterSpawn": 9999, "NumberOfDisappearanceTimeReductions": 100000, "RecycleTime": 5000, "RecycleItemScrap": 12040003, "DecomposeOutputContainerId": 10014, "TeamRefreshCD": 5, "PartyInvitationExpiration": 300, "InvitationWindowDisappearTime": 10, "HostileHitTimeType1": 60, "HostileHitTimeType2": 300, "HostileFireTime": 60, "HostileHoldItemMaxTime": 10, "HostileHoldItemTime": 60, "HotspotStartTime": 3, "HotspotEndTime": 23, "ResearchConsumesTime": 10, "MaximumTeamSize": 4, "RiverInteractiveId": 7, "SeaInteractiveId": 8, "ShowThrowCycleUI": 0.15, "RepairBenchConditionLoss": 0.2, "RepairBenchCostFraction": 0.199999, "ThrowLinelong": 50, "ThrowLineCheckRadius": 0.15, "ChatMessageMaxLength": 100, "WorldChatCd": 30000, "ChatCd": 2000, "ChannelMaxChatMessage": 150, "MaxReservedPrivateChatChannels": 30, "MaximumNumberOfDisplays": 20, "MaxUnreadCountWhenLogin": 150, "JunkPileCheckDistanceTimeLoop": 20000, "JunkPileCheckGroupAvailableTimeLoop": 60000, "JunkPileWorldDestroyTime": 1000, "JunkPileWorldReturnRange": 25, "JunkPileWorldDestroyRange": 5, "JunkPileWorldSpawnRange": 3, "DefaultAvatarID": 1001, "MapDefaultScaleRatio": 2, "DeathmapDefaultScaleRatio": 2, "MapMaxScaleRatio": 4.5, "MapMinScaleRatio": 1, "DeathMapMaxScaleRatio": 4.5, "DeathMapMinScaleRatio": 1, "MapScaleBtnClickStep": 0.1, "BedPointVisibleBorder": 200, "BedBloodIntervals": 20, "BedDeductBlood": 1, "BedTotalNumber": 15, "MapGestureScaleFactor": 0.9, "MiniMapDefaultSize": 4096, "Clusterdetectiondistance": 150, "MapGridSize": 150, "ViewingAngleMovementRate": 0.3, "OxygenDamageTick": 2, "OxygenDamageMultiply": 0.5, "VoiceMinDistance": 20, "VoiceMaxDistance": 70, "VoiceRolloff": 0.8, "WorldChatEnhancedDisLimit": 30, "DrinkChangeWater": 50, "DrinkChangeHydration": 30, "DrinkChangeRadiationPoison": -5, "DrinkChangePoison": -3, "DrinkChangePendingHealth": 5, "SuddenRecoverConstructionCheckRange": 100, "SuddenRecoverConstructionCheckMaxTime": 45, "MaximumInvincibilityTimeAtResurrection": 10, "NearbyRebirthCD": 180, "NearbyRebirthRadiusRange": 180, "NearbyRebirthSwitch": true, "OutpostRebirthCD": 300, "NearbyRebirthUI": 50, "RebirthPlayerDistance": 50, "RebirthMonsterDistance": 50, "TimingAfterPlaced": 10800, "TimingAfterAttacked": 30, "NotInWater": 0.35, "HPBarDisplayDuration": 2, "HPBarDisplayMaxDistance": 40, "HPBarScaleMinCoeff": 0.4, "HPBarScaleMaxDistance": 21, "HPBarScaleMaxCoeff": 0.6, "HPBarScaleMinDistance": 10, "HPBarVirtualBarReductionSpeed": 1, "HPBarMaxNumSingleTime": 3, "HPBarMaxNumGlobalTime": 3, "HPBarSpellFieldDamgeThreshold": 1, "AimHPBarModSwitchDistance": 4, "DrinkWaterCD": 3400, "GetWaterTriggerTime": 200, "TipsDropWaterAmount": 100, "AutoGatherInterruptedByUI": true, "DrinkingDelayTime": 700, "DrinkWaterEveryTime": 50, "ReceivesWaterPerSecond": 250, "DrinkChangeSaltWater": 50, "DrinkingSaltWaterChangesCalories": -15, "DrinkSaltWaterChangeHydration": -20, "DrinkSaltWaterChangeRadiationPoison": 0, "DrinkSaltWaterChangeHealth": -2, "InteractionHighlightRange": 40, "OreSpotDistance": 30, "ConstructionShowOreSpotDistance": 20, "HeldItemShowOreSpotDistance": 20, "WeaponAccessoryShowOreSpotDistance": 20, "EquipShowOreSpotDistance": 20, "ExtinguishRadius": 3, "ElevatorReached": 11044, "CallElevatorSuccess": 11045, "CallElevatorFailure": 11046, "CallElevatorFailureNext": 11047, "ElevatorIntervalTime": 2000, "TimeZone": 8, "MonthlyRewardLimitResetTime": 1, "WeeklyRewardLimitResetTime": 1, "DailyRewardLimitResetTime": 4, "DailyTaskResetTime": [4, 0, 0], "DailyTaskUnlockTaskId": 0, "DailyTaskRewardMailTemplateId": 17, "ItemIntroductionNotice": true, "ItemIntroductionNoticeTime": 3, "EntityHUDTime": 10, "EntityHUDCD": 1, "UnlockNotificationTime": 4, "IsActiveBeeBuzzGameMode": true, "BeeBuzzRewardLimitResetTime": [4, 0, 0], "TreasureResetTime": [4, 0, 0], "IsActiveReputationGameMode": false, "IsActiveReputationAchievement": false, "ReputationBuildLimit": 1, "ReputationRangeLimit": 200, "ReputationAirDropCD": 1800, "ReputationRewardCostItem": 26010005, "ActivateReputationCabinetCD": 10, "ReputationExpCoverDebuff": 50, "ReputationExpCoverBuff": 200, "RechargeConditionLoss": 0.1, "FirstQuestId": [], "TiroQuestId": 0, "OpenningQuestId": 204, "OffLineRaidQuickQuitRange": 3, "OffLineRaidQuickQuitPointLimit": 1, "OffLineRaidQuickQuitTimeLimit": 60, "RebornRecoverConstructionCheckRange": 50, "ActiveGameplayCampID": [1, 2], "SceneItemEntityDotHeight": 0, "MarkRefreshDistance": 50, "ActiveDeadSheep": true, "VendingMachineMaxOrder": 7, "VendingMachineMaxLength": 11, "TrackingTheNearestRecycler": 402, "WaterItemId": 12020015, "CompostingConversionTime": 25, "OxygenHurtRate": 4, "OxygenDepleteRate": 0.08, "ManufactureRange": 15, "CircuitboxTime": 2400, "EscapeInitCD": 5, "EscapeOverlayCD": 10, "ItemInterRange": 15, "BulletHoleRiggingEntityID": [5, 8, 17, 21, 27, 32, 33, 34], "BackpackSlowDragTriggerTime": 0.3, "BackpackSlowDragTriggerAngle": 0.86, "BackpackAutoScrollTriggerTime": 0, "BackpackAutoScrollStep": 1, "InventorySlowDragTriggerTime": 0.3, "InventorySlowDragTriggerAngle": 0.86, "InventoryAutoScrollTriggerTime": 0, "InventoryAutoScrollStep": 0.5, "WeaponChooseSlowDragTriggerTime": 0.3, "WeaponChooseSlowDragTriggerAngle": 0.86, "WeaponChooseAutoScrollTriggerTime": 0, "WeaponChooseAutoScrollStep": 0.5, "BackpackSlowDragTriggerTime_PC": 0.3, "BackpackSlowDragTriggerAngle_PC": 0.86, "BackpackAutoScrollTriggerTime_PC": 0, "BackpackAutoScrollStep_PC": 1, "InventorySlowDragTriggerTime_PC": 0.3, "InventorySlowDragTriggerAngle_PC": 0.86, "InventoryAutoScrollTriggerTime_PC": 0, "InventoryAutoScrollStep_PC": 0.5, "WeaponChooseSlowDragTriggerTime_PC": 0.3, "WeaponChooseSlowDragTriggerAngle_PC": 0.86, "WeaponChooseAutoScrollTriggerTime_PC": 0, "WeaponChooseAutoScrollStep_PC": 0.5, "HorseDestructionTime": 300, "HorseSpawnTime": 30, "TreeMarkerRadius": 0.26, "TreeMarkerHitDistance": 0.17, "AttachedSceneItemCombineCount": 2, "AttachedSceneItemCombineDistance": 0.2, "UpwardDetection": 1.9, "Downwards": 3, "ParallelDetection": 3, "VehicleLimit": 10, "HKGVoiceUrl": "udp://hk.voice.gcloudcs.com:10013", "SGGVoiceUrl": "udp://sg.voice.gcloudcs.com:8700", "USGVoiceUrl": "udp://us.voice.gcloudcs.com:8700", "DEGvoiceUrl": "udp://de.voice.gcloudcs.com:8700", "HorseSoundMinInterval": 2.5, "HorseSoundMaxInterval": 5.2, "SaddleSoundMinInterval": 0.6, "SaddleSoundMaXInterval": 0.9, "HorseDefecateTime": 30, "GlobalLightFadeTime": 1.2, "SoftDisconnectTime": 180, "ServerQueueTimeoutDuration": 300, "LobbyManRotateFactor": 0.2, "BackpackManRotateFactor": 1, "BackpackManViewerRotateThreshold": 100, "MaximumNumberOfRecruitment": 100, "InGameRecruitmentCD": 30, "InGameRecruitmentCap": 100, "CombatMarker_VoiceCd": 3, "CombatMarker_CreateTimeLimit": 5, "CombatMarker_CreateAngleLimit": 10, "CombatMarker_AimRange": 100, "CombatMarker_OverlapCapsuleDistance": 20, "CombatMarker_OverlapCapsuleRadius": 0.25, "CombatMarker_AimStayShowInfoTimeMs": 500, "CombatMarker_HudShowCombatMarkerCount": 3, "CombatMarker_MarkerSortByTimeDistance": 40, "CombatMarker_TranslucencyExtraTime": 0.5, "CombatMarker_HudCircleRange1": 100, "CombatMarker_HudCircleRange2": 250, "CombatMarker_HudCircleRange3": 250, "CombatMarker_GoingtoEffect": 9552, "RecruitmentDefault": 1, "PileRange": 3, "AutoMaxElectricalWireLength": 45, "MaxElectricalWirePoint": 30, "safeboxresettingcd": 21600, "safeboxopencd": 21600, "ManualMaxElectricalWireLength": 45, "MaxReconnectCount": 3, "MaxWiringSavingDistance": 50, "UniversalClothBagModel": "Weapon/Item/DropItem/BurlapSack/LV1/Prefabs/ITM_BurlapSack_LV1_Wm.prefab", "DailyPointsTasksGet": 3, "ReduceSpreadOnHorseAndTacticalGlove": 0.1, "NearbyRebirthPVPCD": 300, "BoxTaskIfnewbie": 0, "IntegraTaskIfnewbie": 0, "GameTimeRuleId": 10000, "AutoCreateToolCupboard": false, "StandToDeadAnim": "Anim/Char/Tp/Dead/common/Tp_ani_common_tp/common_stand_2death.anim", "NetworkWaveTimeA": 5, "NetworkWaveTimeB": 30, "BaseTasksTrackCD": 300, "InteractiveTickDistance": 50, "saftAreaTipsShowTime": [5, 5, 5, 5, 5], "ScreenRatioThreshold": 1.85, "CorpseShowDeathMaxDistance": 100, "AdsorptionTypeDrop": [2, 5, 8, 20, 21, 39, 40, 41, 43, 26, 19, 17, 32, 33, 34, 35, 36, 46, 47], "IgnorePlatformSpeedType": [33, 34], "CorpseBoxAdsorptionType": [5, 8, 20, 39, 43, 19, 32, 34, 35, 36], "AnnouncementRefreshHour": 5, "IsChinaVersion": true, "HudHitEffect": [17, 20, 21, 33], "MaxWorldflagDist": 10, "RaidValidTime": "9:00-24:00", "IsOpenRaid": true, "NewbieProtection": true, "NewbieProtectionRemovalTime": 30, "NewbieProtectionRemovalEquipment": [16, 1, 16, 2], "OreSpotDistanceDay": 30, "OreSpotScaleDay": [0, 1.4, 27, 1.26, 30, 1.12], "OreSpotIntensityDay": [0, 1, 27, 0.8, 30, 0.3], "OreSpotDistanceNightWithLight": 25, "OreSpotScaleNightWithLight": [0, 1.2, 22, 1.08, 25, 1.96], "OreSpotIntensityNightWithLight": [0, 0.25, 22, 0.05, 25, 0.03], "OreSpotDistanceNightNoLight": 15, "OreSpotScaleNightNoLight": [0, 1, 13, 0.9, 15, 0.8], "OreSpotIntensityNightNoLight": [0, 0.1, 13, 0.05, 15, 0.03], "BattleMailLimit": 50, "ClientMailLimit": 50, "BattleMailLimitTime": 30, "NumberOfAnnouncements": 3, "dotMaxNum": 5, "MarkerFlySpeed": 1100, "MarkerFlyDelayTime": 1, "MarkerFlyStayTime": 1, "MetroMapTriggerY": -40, "WaterCheckFPDist": 3, "WaterCheckTPDist": 5, "RiverHudShowTime": 3000, "SeaHudShowTime": 2000, "DeviceAvailableCheck": false, "RadiationAlphaRange": [0.1, 0.3, 0.5, 0.7, 0.9, 1], "FullScreenEffectBreathingRate": 2.5, "MeleeHitTriggerParaPlayer": 0.9, "MeleeHitTriggerParaAI": 0.95, "NumberOfQuickPostsSaved": 10, "QuickPostsCD": 500, "PlayerNameDistance": 3, "HungerThirstMinThreshold": 0, "HPRedFlashing": 0.2, "HPOrangeFlashing": 0.4, "GlobalWorldParameterConfig": "Env/Config/GlobalWorldParameterConfig.prefab", "ZiplinePrefabPath": "Env/InteractiveItem/General/ZiplineGrip/Prefabs/II_ZiplineMountable_Depolyed.prefab", "MaximumDistanceFromTheFoundation": 7, "MaximumNumberOfAttachments": 20, "HudMarkerAlphaOnADS": 0.3, "InformationTask": [2030260, 2030270, 2030280, 2030290, 2030300, 2030310], "MapGridLineAlphaRange": [0.1, 0.3], "MapGridLabelAlphaRange": [0.3, 0.9], "BeyondSafeAreaBuff": 403, "WithinSafeAreaBuff": 402, "HostileBuff": 401, "CraftUseBlueprintCount": 54, "EffectSetWantLodLevelTime": 1000, "MainMapDeathPointMarkerNum": 3, "RespawnMapDeathPointMarkerNum": 3, "DisplayRewardTask": 2030236, "IsShowHudPlayerName": false, "LampTypeLimitSwitch": true, "GLOBAL_MAX_HORSE_NUM": 100, "GLOBAL_MAX_CAR_NUM": 100, "GLOBAL_MAX_PLANE_NUM": 100, "GLOBAL_MAX_BOAT_NUM": 100, "GLOBAL_MAX_TRAIN_NUM": 100, "MAX_CONSTRUCT_PER_BUILDING": 2000, "MAX_CONSTRUCT_PER_ZONE": 5000, "KatyushaMapMaxScaleRatio": 4.5, "KatyushaMapMinScaleRatio": 1, "KatyushaMapDefaultScaleRatio": 1.5, "DeathCompensationSwitch": false, "OreShatterPrefabPath1": "Env/Nature/Rocks/_Runtime/Ore/Shatter/ore_Break1.prefab", "OreShatterPrefabPath2": "Env/Nature/Rocks/_Runtime/Ore/Shatter/ore_Break2.prefab", "LobbyShopPath001": "FX/FX_UI/ShopMall/Prefab/FX_UI_ShopMall_001.prefab", "LobbyScene": "LobbyUnityScene/LobbyStageCamSwitch.unity", "PlayerRT": "Dev/PreviewRT/PreviewRT/PlayerPreviewRT.prefab", "ModularCarRT": "Dev/PreviewRT/PreviewRT/ModularCarPreviewRT.prefab", "LobbyPlayerRT": "Dev/PreviewRT/PreviewRT/LobbyPlayerPreviewRT.prefab", "ItemRT": "Dev/PreviewRT/PreviewRT/ItemPreviewRT.prefab", "LobbyItemRT": "Dev/PreviewRT/PreviewRT/LobbyItemPreviewRT.prefab", "CableRT": "Dev/PreviewRT/PreviewRT/CablePreviewRT.prefab", "PlantRT": "Dev/PreviewRT/PreviewRT/PlantPreviewRT.prefab", "BlueCardFxPath": "FX/FX_UI/FriedHome/Prefab/FX_UI_ FriedHome_ blue.prefab", "RedCardFxPath": "FX/FX_UI/FriedHome/Prefab/FX_UI_ FriedHome_ red.prefab", "ConstructionRT": "Dev/PreviewRT/PreviewRT/ConstructionPreviewRT.prefab", "GetHostileTime": 5000, "MarkScalingFactor": 0.3, "SleepThreshold": 60, "WakeupThreshold": 5, "AimFadeOutTime": 0.6, "AimFadeInTime": 0.2, "MantleAimAlpha": 0.5, "RunAimAlpha": 0.5, "ReloadAimAlpha": 0.5, "AidAimAlpha": 0.5, "DyingAimAlpha": 0.5, "BoltAimAlpha": 0.5, "LadderAimAlpha": 0.5, "SwitchAimAlpha": 0.5, "HitAimOffsetAngle": 6, "AimAlphaLerpSpeed": 0.05, "AimScaleLerpSpeed": 0.05, "AimPackAngle": 4, "CH47PassengerIds": [31001, 31001, 31001, 31002, 31002, 31002], "KatyushaMapGridAlpha": 0.2, "LinkBattleServerOutTime": 3000, "LobbyTeamAnimatorOver1": "LobbyScene/SceneModel/Animation/team_animator_over_1.overrideController", "LobbyTeamAnimatorOver2": "LobbyScene/SceneModel/Animation/team_animator_over_2.overrideController", "LobbyTeamAnimatorOver3": "LobbyScene/SceneModel/Animation/team_animator_over_3.overrideController", "LobbyTeamAnimatorOver4": "LobbyScene/SceneModel/Animation/team_animator_over_4.overrideController", "NewbieGuideLine": "Cutscene/NewbieLevel/Default/Effects/FX_Cutsence_Guideline/Prefab/FX_Guideline.prefab", "CharacterBaseFpAnimator": "Char/CharAnimator/Ac_Common@ac_common_fp/Ac_Basic_Fp.controller", "CharacterBaseTpAnimator": "Char/CharAnimator/Ac_Common@ac_common_fp/Ac_Basic_Fp.controller", "HeldItemBaseFpAnimator": "Char/CharAnimator/Ac_Common@ac_common_tp/Ac_Basic_Tp.controller", "HeldItemBaseTpAnimator": "Char/CharAnimator/Ac_Common@ac_common_tp/Ac_Basic_Wpn_Tp.controller", "EmptyDropSubstitute": 50034, "DefaultScrollDecelerationRate": 0.985, "FpSwimPitchLimit": 0, "RouletteCancelAreaDiameter": 120, "RouletteJoystickSensitivity": 3, "ItemUnuseAreaCategory": [], "ItemUnuseAreaLittleCategory": [], "TerritorialCenterSmokeFx": "FX/FX_UI/TerritorialCenter/Prefab/FX_UI_TerritorialCenter_01.prefab", "AirWallTipsDist": 1, "CampingTentProgressTime": 10000, "PlayerOffLineHudShowDistance": 30, "CampingTentVanishTipsTime": 300, "CampingTentValidTime": 3600, "SurvivalLaterUnlockNum": 5, "PlayerOfflineInSafetyAreaKickOutTime": 600, "HorseDeadZoneY": 1, "HorseDeadZoneX": 2, "toleranceAngleFB": 6, "toleranceAngleLR": 10, "HorseIdle2SprintTime": 3, "HorseIdle2BackTime": 0.8, "HorseSprint2RunZone": 0.4, "HorseSteeringBackFactor": 1.3, "HorseBackFactor": 1.5, "TerritoryHeight": 60, "HorseSteeringAC": 2, "HorseSteeringBackAC": 1, "HorseSprintSteeringAC": 3, "HorseJumpSteerRate": 2, "HorseJumpFrozen": 2, "HorseGravityFactor": 2, "HorseJumpThreshold": 1.3, "jumpFrowardThreshold": 2.5, "JumpCanterThreshold": 6, "JumpGallopThreshold": 9, "JumpSprintThreshold": 16, "jumpFrowardSpeedForZAxis": 7, "JumpCanterSpeedForZAxis": 8, "JumpGallopSpeedForZAxis": 10, "JumpSprintSpeedForZAxis": 15, "HorseFalThresholdl": 0.6, "HorseBrakeSpeed": 9, "NewbieLevelNPCController": "Cutscene/NewbieLevel/Default/Animations/AnimatorController/DefaultNPCController.controller", "UITeamDefaultModel": 13020014, "UITeamDefaultModelShowTime": 0.5, "NewbieNPCAnimTransitTime": 0.2, "TlogGatherInterval": 60, "IdipDeleteConstructionLength": 30, "MountEyeCheckRadius": 0.28, "UiTeamDepartureTipsTime": 5, "InvitationWindowShowsLimit": 5, "DefaultShirt": 13010071, "DefaultTrousers": 13010072, "BeltFastReplaceAutoClose": true, "UiSwipeCardGameFX": "FX/FX_UI/SwipeCardGame/Prefab/FX_UI_SwipeCardGame.prefab", "ChatBanUpdateInterval": 600, "OpenReputation": false, "HumanFallDamageSound": "UI_Fall_Damage", "ContainerDetectionRange": 8, "MaximumDetectionLimit": 20, "OpenCoreSkin": true, "SetReportBufferTime": 60, "TeamMemberColors": ["#FFE400", "#FF9C00", "#00B4FF", "#A2FF00", "#CEA2A2", "#A097D8", "#0022FF", "#A2FF00", "#FF0004", "#343434"], "ForceRecyleMinute": 60, "IsHideRespawnBtns": false, "IsHideReputationBtns": false, "CampingTentCDAfterHitted": 20, "RecoilRecoveryTime": 0.4, "ClearRecoilPitch": 0.3, "StopRecoverRecoilPitch": 2, "YawAngularSpeedThreshold": 10, "PitchRecoilReductionPercent": 0.65, "OpenGenericAttacked": true, "OpenHungerAttacked": true, "OpenThirstAttacked": true, "OpenColdAttacked": true, "OpenDrownedAttacked": true, "OpenHeatAttacked": true, "OpenBleedingAttacked": true, "OpenPoisonAttacked": true, "OpenSuicideAttacked": false, "OpenBulletAttacked": true, "OpenSlashAttacked": true, "OpenBluntAttacked": true, "OpenFallAttacked": true, "OpenRadiationAttacked": false, "OpenBiteAttacked": true, "OpenStabAttacked": true, "OpenExplosionAttacked": true, "OpenRadiationExposureAttacked": false, "OpenColdExposureAttacked": false, "OpenDecayAttacked": false, "OpenElectricShockAttacked": false, "OpenArrowAttacked": true, "OpenAntiVehicleAttacked": true, "OpenCollisionAttacked": true, "OpenFunwaterAttacked": false, "EnableTeamUnlockShare": true, "CameraPreviewTrackDelay": 100, "PostProcessEffectTime": 0.3, "RFDetonationWaitTime": 2000, "RFDetonationMaxCount": 20, "TimerBombConvert2SceneItemTime": 72, "PCUIScale": 0.8, "PCUIMultiScale": 0.8, "GoToOfficialWeb": "", "PreviewUIFxPath": "FX/FX_UI/Container/Prefabs/FX_UI_Container.prefab", "PreviewUIFxPathDestroyTimer": 1500, "HotKeyProgressTime": 1, "HotKeyProgressStartTime": 0.2, "CostumeDefaultIdleAnim": "LobbyScene/SceneModel/Animation/t_male01/lobby_char_unarmed_stand_idle01.anim", "CostumeFemaleDefaultIdleAnim": "LobbyScene/SceneModel/Animation/t_male01/lobby_char_unarmed_stand_femaleidle01.anim", "WorkbenchHeight": 50, "FeedbackDamageType": [0, 5, 9, 10, 11, 12, 14, 15, 16, 21, 22, 23], "PhotoFPCameraPath": "Dev/Photo/PhotoFPCamera.prefab", "PhotoTPCameraPath": "Dev/Photo/PhotoTPCamera.prefab", "PhotoTrackCameraPath": "Dev/Photo/PhotoTrackCamera.prefab", "PhotoReferencePointPath": "Dev/Photo/Path/ReferencePoint.prefab", "PhotoWayPointPath": "Dev/Photo/Path/WayPoint.prefab", "PhotoLookAtPointPath": "Dev/Photo/Path/LookAtPoint.prefab", "EngineDriveForceFactors": [0.9, 0.5, 0.41, 0.35], "VehicleLimitedAuthorityTs": 300, "MallEntryAutoScrollCDMs": 4000, "MallHotSaleAutoScrollCDMs": 10000, "MallBuyItemMaxCount": 999, "PlayerHomepageRT": "Dev/PreviewRT/PreviewRT/PlayerHomepageRT.prefab", "AnimalTrapRadius": 40, "VendorPriceAdjustmentFrequency": 1, "SendRecruitmentApplyForLimit": 50, "VehicleRideLimitedTime": 40, "RFReceiverIconShowDis": 15, "RFReceiverIconShowCount": 20, "BeeBuzzPointItemID": 22010007, "LightIntensityMaxDelta": 0.05, "MaxAspectRatio": 2.23, "MinAspectRatio": 0.4, "FillBgExPixel": 180, "FeedbackShowMax": 3, "UiTopUpFlashFx": "FX/FX_UI/TopUp/Prefabs/FX_UI_TopUp.prefab", "TeamFilterJobNum": 2, "TeamFilterJobNum2": 3, "TeamRecruitRefreshCd": 5, "DisplayWeaponAnimator": "Weapon/Melee/UI/UI_Tp/weapon_overrideAnimator.overrideController", "DisplayFpIdlePose": "Anim/Char/Fp/Locomotion/unarmed/Fp_ani_unarmed_fp/unarmed_stand_hip_idle.anim", "TeamApplyDescLimit": 60, "PrivacyPolicy": "https://skjh.qq.com/", "Useragreement": "https://skjh.qq.com/", "Attribution": "https://skjh.qq.com/", "OpenTraningDeadSheep": true, "TrainSignalPrefabFP": "Weapon/Tool/Roosterwithcard/LV1/Prefabs/TOL_Roosterwithcard_LV1_Fp.prefab", "TrainSignalPrefabTP": "Weapon/Tool/Roosterwithcard/LV1/Prefabs/TOL_Roosterwithcard_LV1_Tp.prefab", "TrainSignalAnimationFP": "Anim/Char/Fp/Common/common/Fp_ani_common_fp/common_stand_hip_transmit_signal.anim", "TrainSignalAnimationTP": "Anim/Char/Tp/CommonOverride/common/Tp_ani_common_tp/common_stand_hip_transmit_signal.anim", "RedDotDataPushTime": 600, "CallOnlineButtonCD": 300, "CallOnlineClickLimit": 3, "PowerOptimizationFullScreenTime": 120, "SkinPreviewFunction": [1, 2, 3, 4, 7, 8], "HRTFOpenQuality": 3, "ControlCurveConfig": "Dev/Control/ControlConfig/ControlCurve.asset", "AimSnapConfig": "Dev/Control/ControlConfig/AimSnapGlobalConfig.asset", "ZiplineCamConfig": "Dev/Camera/TPSCameraStates/TPSOnZiplineCameraListData.asset", "ThrowLineMat": "FX/FX_Common/FX_Throwline/Prefab/M_Throwline.mat", "ThrowLinePrefab": "FX/FX_Common/FX_Throwline/Prefab/FX_Throwline.prefab", "CameraHitConfig": "Char/CharAnimator/Camera_Hit_Curve@camera_hit_curve/CameraHitConfigs.asset", "CorrectEffectConfig": "Char/CharAnimator/Character_Config@character_config/CorrectEffectConfig.asset", "PlayerRayCastConfig": "Char/CharAnimator/Character_Config@character_config/PlayerRayCastConfig.asset", "SocAudioScriptConfig": "Vehicle/Config/SocAudioScriptConfig.asset", "SocGibConfig": "Vehicle/Gib/SocGibConfig.asset", "CharacterConfig": "Char/CharAnimator/Character_Config@character_config/CharacterConfig.asset", "DefaultMaleModel": [9010001, 9010020, 9010001], "DefaultFemaleModel": [9010041, 9010061, 9010041], "LobbyDayLightPath": "Light/Day", "LobbyNightLightPath": "Light/Night", "NewbieHelicopterActTaskID": 2060020, "NewbieHelicopterController": "Cutscene/NewbieLevel/Default/Animations/AnimatorController/ScrapHeliCarrier.controller", "SpawnShopItemDecayFactor": 2, "MiniMapPcDefaultSize": 4096, "DefaultSkinLevel": 0, "MallVideoAutoPlayKVShowTime": 3, "MallPlayFullScreenVideoWaitSecs": 3, "PickRestoreSkinList": [2, 3, 4, 5, 6], "TeamSharesSkinLevel": [0], "TankCorpsePreloadThreshold": 0.3, "AttackHeliCorpsePreloadThreshold": 0.1, "DeathInfomapScale": 3, "NewbieMissionGasStationId": 2050225, "BattleTipDisappearTime": 2000, "CampingTentAppPushRemainMins": 10, "RouletteDragThreshold": 10, "PenetrationLevelSwitch": 1, "RecruitmentExpirationTime": 6, "ObserverModeMaxRadius": 30, "ObserverModeMinRadius": 1, "ObserverModeDefualtRadius": 20, "ObserverStatisticsAvgTime": 15, "ObserverStatisticsMoveAvgTime": 1, "FXSprayPath": "FX/FX_Item/FX_Spraycan/Prefab/FX_Spraycan03.prefab", "CaptainImpeachmentTime": 48, "CaptainTimingPrompt": 720, "CountdownClosingPopup": 20000, "ImpeachmentVoteTiming": 180, "VotingSuccessfulCountdown": 3000, "TerritoryTransferTime": 48, "NumberOfBattleWorldChannels": 20, "MaximumNumberOfInvitedPlayers": 6, "ClosingPopupTime": 4, "MountStayMonumentSec": 600, "PlayerRaycastHpbarDelayTime": 2, "BattlePassMainBgCameraPos": [0, 0, -100], "BattlePassPreviewBgCameraPos": [0.5, 0, -100], "CaptainImpeachmentLobbyTipTime": 24, "VideoCanPlayMemRemain": 500, "LobbyWeaponOffset": [-0.5, 0, 0], "TeamShareToTribeCd": 10, "PlayerHpbarDistance": 0.33, "GetTalentTreeInfoCd": 120, "AutoActiveTalentDelay": 1000, "SprayRouletteCancelAreaDiameter": 130, "SprayRouletteJoystickSensitivity": 3, "SprayRouletteDragThreshold": 10, "SprayRouletteDragMove": 5, "MarkerRouletteCancelAreaDiameter": 130, "MarkerRouletteJoystickSensitivity": 3, "MarkerRouletteDragThreshold": 10, "MarkerRouletteDragMove": 5, "ReloadRouletteCancelAreaDiameter": 130, "ReloadRouletteJoystickSensitivity": 3, "ReloadRouletteDragThreshold": 10, "ReloadRouletteDragMove": 10, "RespwanListSelectHintCd": 15, "ComTopBarCurrencyTipsDelay": 0, "ComTopBarCurrencyTipsDelay_PC": 1000, "ChargetimeEnduranceReduce": 0.1, "SoftReconnectCheckConstructionDistance": 20, "HumanoidDeathAnimator": "Char/CharAnimator/Ac_Common@ac_common_tp/PlayerCorpseAnimator.overrideController", "PhoneUIScale": 0.9, "KeyVoiceDelayTriggerTime": 400, "QuickSelectTab": 1, "AreaReportDis": 100, "EnableIOSWinCacheWhiteList": true, "IOSWinCacheWhiteList": ["UiInventory", "UiCenterConsole", "UiFullScreenLoading"]}]}