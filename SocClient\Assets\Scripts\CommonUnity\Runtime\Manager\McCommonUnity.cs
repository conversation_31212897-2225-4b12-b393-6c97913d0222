using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.Synchronization;
using WizardGames.Soc.Common.Unity.Audio;
using WizardGames.Soc.Common.Unity.Construction;
using WizardGames.Soc.Common.Unity.Scene;
using WizardGames.Soc.Common.Unity.Systems;

namespace WizardGames.Soc.Common.Unity.Manager
{
    /// <summary>
    /// Unity中的Mgr管理类， 关联Common中的Mgr管理类
    /// </summary>
    public class McCommonUnity
    {
        #region all_mgr_variables

        public static MgrTables Tables => McCommon.Tables;

        public static ThreadTaskManager ThreadTask;
        public static MgrScene Scene;
        public static MgrRes Res;
        public static WebSocketNoticeManager WebSocket;
        public static MgrAudioBase AudioBase;
        public static MgrMaterial Material;

        public static MgrEvent Event;
        public static MgrMapConfig MapConfig;

        public static MgrSnapshotReceiver SnapshotReceiver; // 由Mc赋值
        //局内/局外实例
        public static TimerWheel.TimerWheel TimerWheel; // 由Mc赋值
        public static MgrWeapon Weapon;
        public static MgrConstructionUnity Construction; //由Mc或McSimulator赋值
        #endregion

        /// <summary>
        /// 实例初始化，在这里注册所有的Entity
        /// 需要在上面all_mgr_variables的区域中定义一个静态成员， 成员名字必须是MgrXXX去掉Mgr后的XXX
        /// </summary>
        public static void InitAddonManager()
        {
            McCommon.Instance.RegFixedMgr<MgrScene>((mgr) => Scene = mgr as MgrScene, MgrRange.Persistent);
            McCommon.Instance.RegFixedMgr<MgrAudioBase>((mgr) => AudioBase = mgr as MgrAudioBase, MgrRange.GameOnly);
            McCommon.Instance.RegFixedMgr<MgrMaterial>((mgr) => Material = mgr as MgrMaterial, MgrRange.GameOnly);
            McCommon.Instance.RegFixedMgr<MgrRes>((mgr) => Res = mgr as MgrRes, MgrRange.Persistent);
            McCommon.Instance.RegFixedMgr<MgrEvent>((mgr) => Event = mgr as MgrEvent, MgrRange.GameOnly);
            McCommon.Instance.RegFixedMgr<MgrMapConfig>((mgr) => MapConfig = mgr as MgrMapConfig, MgrRange.GameOnly);
            McCommon.Instance.RegFixedMgr<ThreadTaskManager>((mgr) => ThreadTask = mgr as ThreadTaskManager, MgrRange.Persistent);
            McCommon.Instance.RegFixedMgr<WebSocketNoticeManager>((mgr) => WebSocket = mgr as WebSocketNoticeManager, MgrRange.Persistent);
            McCommon.Instance.RegFixedMgr<MgrWeapon>((mgr) => Weapon = mgr as MgrWeapon, MgrRange.GameOnly);
        }
    }

}
