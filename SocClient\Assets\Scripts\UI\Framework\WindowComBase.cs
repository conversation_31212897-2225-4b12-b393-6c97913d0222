using System;
using System.Collections.Generic;
using Cysharp.Text;
using FairyGUI;
using UnityEngine;
using Utilities;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Common.Profile;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Ui;
using WizardGames.Soc.SocClient.Ui.Utils;

namespace WizardGames.Soc.Common.Unity.Ui
{
    /// <summary>
    /// 窗口基类
    /// </summary>
    public partial class WindowComBase : GComponent
    {
        public static SocLogger logger = LogHelper.GetLogger(typeof(WindowComBase));
        #region 内部实现
        /// <summary>
        /// 绑定的FGUI资源
        /// </summary>
        private GComponent contentPane;
        /// <summary>
        /// 是否已经初始化
        /// </summary>
        protected bool inited = false;

        /// <summary>
        /// 当前正在播放的动画实例
        /// </summary>
        private HashSet<Transition> anims = null;

        /// <summary>
        /// 当前正在播放的动画的回调
        /// </summary>
        private Dictionary<Transition, PlayCompleteCallback> animCallbacks = null;

        /// <summary>
        /// 播放动画是解锁容器的更新
        /// </summary>
        private bool unlockContainerUpdateOnAnim = false;

        /// <summary>
        /// 窗口管理类
        /// </summary>
        protected MgrUiBase mgr = null;

        /// <summary>
        /// 自定义标记
        /// </summary>
        protected HashSet<string> customFlags = null;

        /// <summary>
        /// 窗口至少显示过一次
        /// </summary>
        protected bool isAtLeastShowOnce = false;

        /// <summary>
        /// 窗口是否初始化
        /// </summary>
        public bool IsInited => inited;

        /// <summary>
        /// 窗口名称
        /// </summary>
        public string uiName { get; private set; } = null;

        /// <summary>
        /// 窗口信息
        /// </summary>
        public Data.Ui.Window WinInfo { get; private set; } = null;

        /// <summary>
        /// 初始化完成后需要执行的操作
        /// </summary>
        public Action<WindowComBase> doAfterEnabled = null;

        /// <summary>
        /// 停止窗口自动移除倒计时
        /// </summary>
        public bool StopRemoveCountDown = false;

        /// <summary>
        /// 窗口自动移除倒计时
        /// </summary>
        public float LastHideTime { get; private set; } = 0;

        /// <summary>
        /// 窗口开始打开的时间
        /// </summary>
        public float StartOpenTime = 0;

        /// <summary>
        /// 播放动画时时可以触摸
        /// </summary>
        protected bool CanTouchWhileAnim = false;

        /// <summary>
        /// 是否对根节点开启FairyBatch
        /// </summary>
        protected virtual bool RootFairyBatch => true;

        /// <summary>
        /// 窗口是否被修改
        /// </summary>
        public bool ChangeScreenVerOnExit = false;

        /// <summary>
        /// 存在引导配置时，具体界面指定是否可在打开时触发引导
        /// </summary>
        public bool autoShowGuide = true;

        /// <summary>
        /// 界面所在的层级
        /// </summary>
        public LayerComBase Layer => mgr?.GetLayerByID(WinInfo.Layer);

        /// <summary>
        /// 是否是半屏界面
        /// </summary>
        public bool isHalfScreenWin = false;

        private const string SIDE_SHOT_1 = "ComSideShot";            // 半屏组件1        
        private const string SIDE_SHOT_2 = "ComTransparentSideShot"; // 半屏组件2 

        protected ComTopBar comTopBar;

        /// <summary>
        /// root节点初始化的时候是否可触摸
        /// </summary>
        private bool rootTouchable = true;
        /// <summary>
        /// 从UIPackage加载的窗口面板
        /// </summary>
        public GComponent ContentPane
        {
            set
            {
                if (contentPane == value) return;
                if (contentPane != null) RemoveChild(contentPane);
                contentPane = value;
                ComRoot = null;
                topBarCom = null;
                if (contentPane == null) return;
                gameObjectName = $"{uiName}({contentPane.gameObjectName})";
                contentPane.gameObjectName = "ContentPane";
                AddChild(contentPane);
                contentPane.SetSize(width, height);
                contentPane.position = Vector2.zero;
                contentPane.AddRelation(this, RelationType.Size);
                contentPane.fairyBatching = RootFairyBatch;
                contentPane.container.SkipFrameUpdate = WinInfo.SkipFrameUpdate;
                ComRoot = contentPane.GetChild("root")?.asCom;
                rootTouchable = ComRoot?.touchable ?? true;
                if (ComRoot == null)
                {
                    logger.ErrorFormat("窗口{0}没有找到根节点root，请检查界面配置", uiName);
                }
                else
                {
                    //查找root下的ComTopBar组件
                    topBarCom = ComRoot.GetChild("topBar");
                    if (topBarCom != null)
                    {
                        comTopBar = topBarCom as ComTopBar;
                    }
                    InitFactor();
                }
            }
            get
            {
                return contentPane;
            }
        }
        /// <summary>
        /// 界面的内容节点
        /// </summary>
        public GComponent ComRoot { get; private set; }

        /// <summary>
        /// 界面的comTopBar节点
        /// </summary>
        private GObject topBarCom;
        /// <summary>
        /// 界面的容器
        /// </summary>
        public Container ContentContainer => ContentPane?.container;

        /// <summary>
        /// 获取窗口所属UI节点
        /// </summary>
        public GameObject GameObject
        {
            get { return displayObject.gameObject; }
        }

        /// <summary>
        /// 窗口在游戏中是否还有效
        /// </summary>
        public bool IsValid
        {
            get { return !isDisposed && (null != displayObject) && (null != displayObject.gameObject) && !displayObject.gameObject.Equals(null); }
        }

        /// <summary>
        /// 窗口的游戏节点是否激活
        /// </summary>
        public bool IsActive
        {
            get { return inited && IsValid && (parent != null); }
        }

        /// <summary>
        /// 窗口的游戏节点是否可见
        /// </summary>
        public bool IsVisible
        {
            get
            {
                if (displayObject == null || displayObject.gameObject == null)
                {
                    return false;
                }

                return displayObject.gameObject.activeInHierarchy;
            }
        }

        /// <summary>
        /// 是否正在播放进场动画
        /// </summary>
        public bool IsPlayingShowAnim
        {
            get
            {
                var showAnim = GetShowAnim();
                if (null == showAnim) return false;
                return anims != null && anims.Contains(showAnim);
            }
        }

        /// <summary>
        /// 是否正在播放退场动画
        /// </summary>
        public bool IsPlayingHideAnim
        {
            get
            {
                var hideAnim = GetHideAnim();
                if (null == hideAnim) return false;
                return anims != null && anims.Contains(hideAnim);
            }
        }

        public bool IsValidComTopBar
        {
            get
            {
                return comTopBar != null;
            }
        }

        public void ComTopBarExit()
        {
            comTopBar?.FireBackButtonClick();
        }

        public WindowComBase() : base()
        {
            displayObject.onAddedToStage.Add(SafeAddToStage);
            displayObject.onRemovedFromStage.Add(SafeRemoveFromStage);
        }

        /// <summary>
        /// 绑定UI管理类
        /// </summary>
        /// <returns></returns>
        public WindowComBase BindMgr(MgrUiBase uiMgr)
        {
            mgr = uiMgr;
            return this;
        }

        /// <summary>
        /// 使用此接口可以提前调用界面的初始化，而非等到界面第一次打开
        /// </summary>
        public void DoInit()
        {
            if (inited) return;
            inited = true;
            try
            {
                OnInit();
                // 先简单认为，界面初始化时主动锁定容器更新的情况下，播放动画时解锁容器更新
                unlockContainerUpdateOnAnim = ContentContainer.IsLocked;

                // 如果是半屏界面, 则需要设置半屏标记
                if (!isHalfScreenWin)
                {
                    var bgCom = ContentPane?.GetChild("bg")?.asCom;
                    isHalfScreenWin = bgCom != null && (bgCom.gameObjectName.Equals(SIDE_SHOT_1) ||
                        bgCom.gameObjectName.Equals(SIDE_SHOT_2));
                }
            }
            catch (Exception e)
            {
                logger.ErrorFormat("OnInit失败，请检查脚本或者界面是否有更新: exception:{0}", e);
                logger.Info("", e);
            }
        }

        private void DoShowAnimFinish()
        {
            OnShowAnimFinish();
            Mc.Ui.HandleWinShowAnimFinished(this);
            Mc.Msg.FireMsg(EventDefine.UiShowAnimFinishEvent, this);
        }

        protected void BreakInput()
        {
            Stage.inst.ResetInputState(touch =>
            {
                if (touch.target != null && touch.began) touch.End();
            });
        }

        private void SafeAddToStage()
        {
            try
            {
                mgr.BrforeWindowAddToStage(this);
                OnAddedToStage();
            }
            catch (Exception e)
            {
                logger.ErrorFormat("SafeAddToStage 发生错误:{0}", e);
            }
        }

        private void SafeRemoveFromStage()
        {
            try
            {
                mgr.BrforeWindowRemoveFromStage(this);
                OnRemoveFromStage();
            }
            catch (Exception e)
            {
                logger.ErrorFormat("SafeRemoveFromStage 发生错误:{0}", e);
            }
        }

        /// <summary>
        /// 窗口被添加到舞台的回调
        /// </summary>
        private void OnAddedToStage()
        {
            isAtLeastShowOnce = true;
            bool recDebugInfo = mgr.NeedRecActDebugInfo(uiName);
            if (!inited)
            {
                if (recDebugInfo) UiActDebugStopWatch.Start();
                DoInit();
                if (recDebugInfo) mgr.ActionDebugInfo.OnInitTime = UiActDebugStopWatch.Stop();
            }
            if (HasCustomFlag(LayerWinFlag.REC_BY_LAYER_STACK, true))
            {
                OnShowByLayerStack();
            }
            if (recDebugInfo) UiActDebugStopWatch.Start();
            if (WinInfo.HideWithStack)
            {
                mgr.AppendWinToFullScreenExtraStack(this);
            }
            LastHideTime = 0;
            if (WinInfo.BreakTouchOnEnable)
            {
                BreakInput();
            }
            OnEnable();
            doAfterEnabled?.Invoke(this);
            if (recDebugInfo) mgr.ActionDebugInfo.OnEnableTime = UiActDebugStopWatch.Stop();
            if (IsPlayingHideAnim) StopAnimIfPlaying(GetHideAnim(), false);
            alpha = 1f;    // 播放入场动画前恢复界面透明度

            if (WinInfo.HideLayerAtOnce)
            {
                Mc.Ui.HandleWinOptimization(Layer, this);
            }

            bool tryPlayShowAnim = !HasCustomFlag(LayerWinFlag.SKIP_SHOW_ANIM, true);
            if (tryPlayShowAnim)
            {
                var showAnim = GetShowAnim();
                DoWithAnimBetter(showAnim, DoShowAnimFinish);
            }
            else
            {
                DoShowAnimFinish();
            }
            mgr.OnWindowAddToStage(this);
        }

        /// <summary>
        /// 窗口从舞台移除的回调
        /// </summary>
        private void OnRemoveFromStage()
        {
            LastHideTime = Time.realtimeSinceStartup;
            bool recDebugInfo = mgr.NeedRecActDebugInfo(uiName);
            if (recDebugInfo) UiActDebugStopWatch.Start();
            if (WinInfo.HideWithStack)
            {
                mgr.RemoveWinFromFullScreenExtraStack(this);
            }
            OnDisable();
            if (recDebugInfo) mgr.ActionDebugInfo.OnDisableTime = UiActDebugStopWatch.Stop();
            mgr.OnWindowRemoveFromStage(this);
        }

        /// <summary>
        /// 清理doAfterEnabled回调
        /// </summary>
        public void ClearAfterEnableCallback()
        {
            doAfterEnabled = null;
        }

        /// <summary>
        /// 强行设置窗口过期
        /// </summary>
        public void ForceSetWindowOutOfDate()
        {
            LastHideTime = 1;
        }

        /// <summary>
        /// 设置窗口信息
        /// </summary>
        /// <param name="name">窗口名称</param>
        /// <param name="info">窗口信息</param>
        /// <param name="home">默认的父对象</param>
        public void SetUIBaseInfo(string name, Data.Ui.Window info, GComponent home = null)
        {
            uiName = name;
            WinInfo = info;
            gameObjectName = ZString.Format("{0}({1}-{2})", name, info.Pack, info.Com);
            SetHome(home ?? GRoot.inst);
        }

        public override string ToString()
        {
            return gameObjectName;
        }

        private void SetUITouchable(bool on)
        {
            //如果根节点一开始就不可触摸，那么不需要设置
            if (!rootTouchable)
            {
                return;
            }
            if (ComRoot != null)
            {
                ComRoot.touchable = on;
            }
        }

        /// <summary>
        /// 指定一个操作, 存在约定的动画, 则动画执行完毕后执行操作, 否则立即执行操作
        /// </summary>
        /// <param name="tween">动画</param>
        /// <param name="opt">操作</param>
        public void DoWithAnimBetter(Transition tween, PlayCompleteCallback opt, float delay = 0)
        {
            // 没有对应动画的界面直接执行回调
            if (null == tween)
            {
                opt?.Invoke();
                return;
            }
            // 播放动画的过程中不再允许点击界面, 避免产生预期外的结果
            SetUITouchable(CanTouchWhileAnim);
            // 记录当前正在播放的动画, 如果需要播放的动画已经记录在列
            // 则将回调合并即可, 不要播放多次动画
            if (null == anims) anims = new HashSet<Transition>();
            if (null == animCallbacks) animCallbacks = new Dictionary<Transition, PlayCompleteCallback>();
            if (anims.Contains(tween))
            {
                if (!animCallbacks.ContainsKey(tween)) animCallbacks[tween] = opt;
                else animCallbacks[tween] += opt;
                return;
            }
            // 播放动画前, 关闭跳帧与锁帧
            bool skipFramePre = ContentContainer.SkipFrameUpdate;
            bool lockFramePre = ContentContainer.IsLocked || unlockContainerUpdateOnAnim;
            if (skipFramePre) ContentContainer.SkipFrameUpdate = false;
            if (lockFramePre) ContentContainer.UnlockUpdate();
            // 播放新的动画
            anims.Add(tween);
            animCallbacks[tween] = opt;
            tween.Play(1, delay, () =>
            {
                if (!IsActive)
                {
                    if (null != animCallbacks && animCallbacks.ContainsKey(tween))
                    {
                        animCallbacks.Remove(tween);
                    }
                    anims.Remove(tween);
                    tween.Dispose();
                    return;
                }
                SetUITouchable(true);
                if (null != animCallbacks && animCallbacks.ContainsKey(tween))
                {
                    animCallbacks[tween].Invoke();
                    animCallbacks.Remove(tween);
                }
                anims.Remove(tween);
                tween.Dispose();
                // 所有动画播放完毕后, 恢复跳帧与锁帧
                if (0 == anims.Count)
                {
                    if (skipFramePre) ContentContainer.SkipFrameUpdate = true;
                    if (lockFramePre) ContentContainer.RelockUpdateAfterFrame();
                }
            });
        }

        /// <summary>
        /// 指定一个操作, 存在约定的动画, 则动画执行完毕后执行操作, 否则立即执行操作
        /// </summary>
        /// <param name="animName">动画</param>
        /// <param name="opt">操作</param>
        public void DoWithAnimBetter(string animName, PlayCompleteCallback opt, float delay = 0)
        {
            var tween = ContentPane.GetTransition(animName);
            DoWithAnimBetter(tween, opt, delay);
        }

        /// <summary>
        /// 如果执行名称的动画正在播放, 则将其停止
        /// </summary>
        public bool StopAnimIfPlaying(Transition anim, bool setToComplete = true, bool processCallback = true)
        {
            if (null == anim || null == anims || !anims.Contains(anim)) return false;
            if (animCallbacks.ContainsKey(anim))
            {
                if (processCallback) animCallbacks[anim].Invoke();
                animCallbacks.Remove(anim);
            }
            anims.Remove(anim);
            anim.Stop(setToComplete, processCallback);
            SetUITouchable(true);
            return true;
        }

        /// <summary>
        /// 获取UI开场动画
        /// </summary>
        public Transition GetShowAnim()
        {
            return ContentPane.GetTransition("show_anim");
        }

        /// <summary>
        /// 获取UI退场动画
        /// </summary>
        public Transition GetHideAnim()
        {
            return ContentPane.GetTransition("hide_anim");
        }

        /// <summary>
        /// 设置自定义标记
        /// </summary>
        public void SetCustomFlag(params string[] keys)
        {
            if (null == keys || 0 == keys.Length) return;
            if (null == customFlags) customFlags = new HashSet<string>();
            for (int i = 0; i < keys.Length; ++i)
            {
                string key = keys[i];
                if (string.IsNullOrEmpty(key)) continue;
                customFlags.Add(key);
            }
        }

        /// <summary>
        /// 是否拥有自定义标记
        /// </summary>
        public bool HasCustomFlag(string key, bool removeAfterGet = false)
        {
            bool res = null != customFlags && customFlags.Contains(key);
            if (removeAfterGet) RemoveCustomFlag(key);
            return res;
        }

        /// <summary>
        /// 移除自定义标记
        /// </summary>
        public void RemoveCustomFlag(string key)
        {
            if (null == customFlags) return;
            customFlags.Remove(key);
        }

        /// <summary>
        /// 是否允许自动移除
        /// </summary>
        public bool CanAutoRemove()
        {
            //新手关不要移除
            if (PlayHelper.IsNewbie) return false;
            // 如果窗口正在显示或将要显示, 不需要自动移除
            if (IsActive) return false;
            // 如果是常驻的窗口, 不自动移除
            if (mgr.IsWindowPersistent(this)) return false;
            // 如果窗口一次都没有显示过, 可能是预加载的窗口, 不需要自动移除
            if (!isAtLeastShowOnce) return false;
            // 外部指定此窗口不需要自动移除
            if (StopRemoveCountDown) return false;
            return true;
        }

        /// <summary>
        /// 检查窗口是否过期
        /// </summary>
        public bool CheckOutOfDate()
        {
            if (LastHideTime <= 0) return false;
            // 检查过期
            float hideTime = Time.realtimeSinceStartup - LastHideTime;
            return hideTime >= mgr.WinOutOfDateTime;
        }

        /// <summary>
        /// 刷新窗口安全区信息
        /// </summary>
        public virtual void RefreshWinSafeArea()
        {
            if (null == ContentPane) return;
            ProfilerApi.BeginSample(EProfileFunc.UI_UiRefreshWinSafeArea);
            // 如果ContentPane根节点上有安全区配置, 那么直接刷新根节点
            // 否则刷新其第一层子节点的安全区
            if (!ContentPane.RefreshSafeArea(true))
            {
                ContentPane.RefreshChildsSafeArea();
            }
            ProfilerApi.EndSample(EProfileFunc.UI_UiRefreshWinSafeArea);
        }
        #endregion

        #region 辅助方法
        /// <summary>
        /// esc关闭界面时调用的接口。
        /// 建议各界面若关闭时有额外逻辑时，重写此函数
        /// 建议ComTopBar返回按钮直接用此函数注册
        /// 默认使用HideSelf
        /// </summary>
        public virtual void OnEscClose(EventContext context)
        {
            HideSelf();
        }
        /// <summary>
        /// 移除窗口
        /// </summary>
        /// <param name="immediately">跳过动画直接隐藏</param>
        public void HideSelf(bool immediately = false)
        {
            mgr.HideWindow(uiName, immediately);
        }

        /// <summary>
        /// 移除窗口
        /// </summary>
        /// <param name="immediately">跳过动画直接移除</param>
        public void RemoveSelf(bool immediately = false)
        {
            mgr.RemoveWindow(uiName, immediately);
        }

        /// <summary>
        /// 显示窗口
        /// </summary>
        public void OpenSelf(bool immediately = false)
        {
            mgr.OpenWindow(uiName, null, immediately);
        }

        /// <summary>
        /// 将窗口拉到所在层级的最上层
        /// </summary>
        public void SetTopmost()
        {
            if (null == parent) return;
            parent.AddChild(this);
        }
        #endregion

        #region 事件相关
        // 此部分已经转移到文件 WindowComBaseEvent.cs 中, 请跳转查看
        #endregion

        #region 供子类重载的生命周期函数
        /// <summary>
        /// 重载这个方法做窗口的初始化操作, 初始化时调用一次
        /// </summary>
        protected virtual void OnInit()
        {

        }

        /// <summary>
        /// 重载这个方法做窗口变为显示状态的逻辑, 每次显示调用
        /// </summary>
        protected virtual void OnEnable()
        {

        }

        /// <summary>
        /// 重载这个方法做窗口入场动画播放完毕的逻辑, 存在入场动画的情况下, 动画播放完毕调用
        /// </summary>
        protected virtual void OnShowAnimFinish()
        {

        }

        /// <summary>
        /// 重载这个方法做窗口变为隐藏状态的逻辑, 隐藏和销毁都会调用, 销毁时在OnDestroy之前调用
        /// </summary>
        protected virtual void OnDisable()
        {

        }

        /// <summary>
        /// 重载这个方法做窗口销毁时的逻辑, 销毁时调用
        /// </summary>
        public virtual void OnDestroy()
        {
            // 释放非实例化对象
            ReleaseNonInstanceAssets();
        }

        /// <summary>
        /// 窗口所在层级的显示状态发生变化时调用
        /// </summary>
        public virtual void OnLayerVisibleChanged(bool layerVisible)
        {

        }

        /// <summary>
        /// 当界面为一级界面时, 该方法在界面因为其他一级界面压栈而被隐藏时调用<br/>
        /// 该方法调用时机为OnDisable之前
        /// </summary>
        /// <param name="coverUiName">新打开的一级界面的名称</param>
        public virtual void OnHideByLayerStack(string coverUiName)
        {

        }

        /// <summary>
        /// 当界面为一级界面时, 该方法在界面因为其他一级界面出栈而被显示时调用<br/>
        /// 该方法调用时机为OnEnable之前
        /// </summary>
        public virtual void OnShowByLayerStack()
        {

        }
        #endregion

        #region NonInstanceAssets

        private Dictionary<int, int> _nonInstanceAssetIds = new Dictionary<int, int>();
        protected void SaveNonInstanceAssetTarget(UnityEngine.Object iAssetObj)
        {
            if (null == iAssetObj) return;

            var instanceId = iAssetObj.GetInstanceID();
            if (!_nonInstanceAssetIds.TryGetValue(instanceId, out var oLoadedCount))
            {
                _nonInstanceAssetIds.TryAdd(iAssetObj.GetInstanceID(), 1);
                return;
            }

            _nonInstanceAssetIds[instanceId] = oLoadedCount + 1;
        }

        /// <summary>
        /// 释放非实例化对象
        /// </summary>
        protected void ReleaseNonInstanceAssets()
        {
            if (0 >= _nonInstanceAssetIds.Count) return;

            foreach (var it in _nonInstanceAssetIds)
            {
                UtilsLoad.UnloadAssetSync(it.Key, it.Value);
            }
            _nonInstanceAssetIds.Clear();
        }

        #endregion

        /// <summary>
        /// 属性执行性能检测
        /// </summary>
        public virtual EProfileFunc GetProfileFuncEnum(ELoopType loopType)
        {
            return EProfileFunc.None;
        }

        /// <summary>
        /// 根据配置ID获取引导对象
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public virtual GObject GetGuideObjByID(long id)
        {
            return null;
        }

        /// <summary>
        /// 支持查找多个，按各自条件返回合适的引导对象
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public virtual GObject GetGuideObjByIDArray(List<long> idArray)
        {
            return null;
        }

        public virtual bool CanGuideState(string state)
        {
            return false;
        }

        /// <summary>
        /// 半屏界面隐藏区域（和这个区域有重叠的需要隐藏）
        /// </summary>
        /// <returns></returns>
        public virtual Rect GetHalfScreenHideArea()
        {
            if (!isHalfScreenWin) return Rect.zero;
            if (ComRoot == null) return Rect.zero;
            return new Rect(this.width / 2, 0, this.width / 2, this.height);
        }

        #region 背景边缘填黑边
        /// <summary>
        /// 获取填充bg节点
        /// </summary>
        /// <returns></returns>
        private GObject GetFillBg(string fillName, string resUrl, int index)
        {
            var bgFill = contentPane.GetChild(fillName);
            if (bgFill == null)
            {
                bgFill = UIPackage.CreateObjectFromURL(resUrl);
                bgFill.name = fillName;
                contentPane.AddChildAt(bgFill, index);
            }
            return bgFill;
        }
        /// <summary>
        /// 移除填充bg节点
        /// </summary>
        /// <param name="fillName"></param>
        private void RemoveFillBg(string fillName)
        {
            var bgFill = contentPane.GetChild(fillName);
            if (bgFill != null)
            {
                contentPane.RemoveChild(bgFill);
            }
        }
        /// <summary>
        /// 判断bg节点是否填充满屏幕，如果不满则用图片填充边缘
        /// </summary>
        public void FillBackground()
        {
            var bg = contentPane?.GetChild("bg");
            if (bg == null) return;
            var index = contentPane.GetChildIndex(bg) + 1;
            var bgPos = bg.displayObject.position;
            var bgSize = bg.size * bg.scale;
            var bgEx = Mc.Tables.TbGlobalConfig.FillBgExPixel; //与bg重叠部分像素数量
            if (bgPos.x > 0)
            {
                var bgLeft = GetFillBg("bgLeft", FGUIResPathDef.BG_FILL_LEFT, index);
                bgLeft.SetSize(bgPos.x + bgEx, GRoot.inst.height);
                bgLeft.position = new Vector2(0, 0);
            }
            else
            {
                RemoveFillBg("bgLeft");
            }

            if (bgPos.x + bgSize.x < GRoot.inst.width)
            {
                var bgRight = GetFillBg("bgRight", FGUIResPathDef.BG_FILL_RIGHT, index);
                bgRight.SetSize(GRoot.inst.width - bgPos.x - bgSize.x + bgEx, GRoot.inst.height);
                bgRight.position = new Vector2(bgPos.x + bgSize.x - bgEx, 0);
            }
            else
            {
                RemoveFillBg("bgRight");
            }

            if (bgPos.y > 0)
            {
                var bgTop = GetFillBg("bgTop", FGUIResPathDef.BG_FILL_TOP, index);
                bgTop.SetSize(GRoot.inst.width, bgPos.y + bgEx);
                bgTop.position = new Vector2(0, 0);
            }
            else
            {
                RemoveFillBg("bgTop");
            }

            if (bgPos.y + bgSize.y < GRoot.inst.height)
            {
                var bgBottom = GetFillBg("bgBottom", FGUIResPathDef.BG_FILL_BOTTOM, index);
                bgBottom.SetSize(GRoot.inst.width, GRoot.inst.height - bgPos.y - bgSize.y + bgEx);
                bgBottom.position = new Vector2(0, bgPos.y + bgSize.y - bgEx);
            }
            else
            {
                RemoveFillBg("bgBottom");
            }
        }
        #endregion
    }
}