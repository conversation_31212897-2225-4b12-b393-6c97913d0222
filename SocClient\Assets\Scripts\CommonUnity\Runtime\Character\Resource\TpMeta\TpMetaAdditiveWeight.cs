﻿#if UNITY_EDITOR
using Sirenix.Utilities.Editor;
using UnityEditor;
#endif
using Sirenix.OdinInspector;
using System.IO;
using CommonUnity.Runtime.Animation;
using CommonUnity.Runtime.Character.Resource;
using System;
using UnityEngine;

namespace WizardGames.Soc.Common.Unity.Character
{
    [CreateAssetMenu(fileName = "TpMetaAdditiveWeight", menuName = "Character/TpMetaAdditiveWeight", order = 0)]
    public class TpMetaAdditiveWeight: ScriptableObject
    {
        [Header("当前state时对移动动态权重的影响")]
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawBone", OnEndListElementGUI = "EndDrawBone", HideAddButton = false, HideRemoveButton = true)]
        public AnimCurveKey[] LJogWeightCurve = new AnimCurveKey[(int)TpBoneNameConf.End];
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawBone", OnEndListElementGUI = "EndDrawBone", HideAddButton = false, HideRemoveButton = true)]
        public AnimCurveKey[] LSprintWeightCurve = new AnimCurveKey[(int)TpBoneNameConf.End];
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawBone", OnEndListElementGUI = "EndDrawBone", HideAddButton = false, HideRemoveButton = true)]
        public AnimCurveKey[] LJumpWeightCurve = new AnimCurveKey[(int)TpBoneNameConf.End];
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawBone", OnEndListElementGUI = "EndDrawBone", HideAddButton = false, HideRemoveButton = true)]
        public AnimCurveKey[] LSwimMoveWeightCurve = new AnimCurveKey[(int)TpBoneNameConf.End];
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawBone", OnEndListElementGUI = "EndDrawBone", HideAddButton = false, HideRemoveButton = true)]
        public AnimCurveKey[] LSwimIdleWeightCurve = new AnimCurveKey[(int)TpBoneNameConf.End];
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawBone", OnEndListElementGUI = "EndDrawBone", HideAddButton = false, HideRemoveButton = true)]
        public AnimCurveKey[] LIdleCrouchWeightCurve = new AnimCurveKey[(int)TpBoneNameConf.End];
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawBone", OnEndListElementGUI = "EndDrawBone", HideAddButton = false, HideRemoveButton = true)]
        public AnimCurveKey[] LLadderWeightCurve = new AnimCurveKey[(int)TpBoneNameConf.End];
        [Header("当前state时对AO的动态权重的影响")]
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawBone", OnEndListElementGUI = "EndDrawBone", HideAddButton = false, HideRemoveButton = true)]
        public AnimCurveKey[] AoWeightCurve = new AnimCurveKey[(int)TpBoneNameConf.End];
        
        [Header("当前state时在马上的ik的影响")]
        public AnimCurveKey HorseIKWeightCurve = new AnimCurveKey();
        
#if  UNITY_EDITOR
        private void BegDrawBone(int index)
        {
            SirenixEditorGUI.BeginBox(string.Format("[{0}] {1}", index, ((TpBoneNameConf)index).ToString()));
        }

        private void EndDrawBone(int index)
        {
            SirenixEditorGUI.EndBox();
        }
        
        [Button("InitActionToLoco")]
        public void InitActionToLoco()
        {
            InitAddLayerToLocomotionWeightCollection();
            EditorUtility.SetDirty(this);
            AssetDatabase.SaveAssetIfDirty(this);
        }
        
        [Button("InitAo")]
        public void InitAo()
        {
            InitAOCollection();
            EditorUtility.SetDirty(this);
            AssetDatabase.SaveAssetIfDirty(this);
        }
        
        [Button("InitHorseIK")]
        public void InitHorseIK()
        {
            var assetPath = AssetDatabase.GetAssetPath(this);
            var folderPath = Path.GetDirectoryName(assetPath);
            //在目标目录下创建一个字符串文件夹
            var strFilePath = Path.Combine(folderPath, AnimStrKey.localStrFile);
            AnimStrGroupRef animStrGroupRef = null;
            if (!File.Exists(strFilePath))
            {
                animStrGroupRef = ScriptableObject.CreateInstance<AnimStrGroupRef>();
                AssetDatabase.CreateAsset(animStrGroupRef, strFilePath);
            }
            else
            {
                animStrGroupRef = AssetDatabase.LoadAssetAtPath<AnimStrGroupRef>(strFilePath);
            }
            
            var curveFilePath = Path.Combine(folderPath, AnimCurveKey.localStrFile);
            AnimCurveGroupRef animCurveGroup = null;
            if (!File.Exists(curveFilePath))
            {
                animCurveGroup = ScriptableObject.CreateInstance<AnimCurveGroupRef>();
                AssetDatabase.CreateAsset(animCurveGroup, curveFilePath);
            }
            else
            {
                animCurveGroup = AssetDatabase.LoadAssetAtPath<AnimCurveGroupRef>(curveFilePath);
            }
            
            AnimCurveKey curveKey = AnimCurveKey.CreateKey(animCurveGroup, AnimationCurve.Linear(0, 0, 1, 0));
            HorseIKWeightCurve = curveKey;
            
            EditorUtility.SetDirty(animCurveGroup);
            EditorUtility.SetDirty(animStrGroupRef);
            EditorUtility.SetDirty(this);
            AssetDatabase.SaveAssetIfDirty(this);
        }
        
        [Button("Fix")]
        public void Fix()
        {
            //现在比较临时，后面采用使用一个通用的方式来处理，就是扫描字段自动适配，这样就不需要手动维护了。
            var assetPath = AssetDatabase.GetAssetPath(this);
            var folderPath = Path.GetDirectoryName(assetPath);
            
            Debug.Log("开始修复TpMetaAdditiveWeight "+assetPath);
            
            //在目标目录下创建一个字符串文件夹
            var strFilePath = Path.Combine(folderPath, AnimStrKey.localStrFile);
            AnimStrGroupRef animStrGroupRef = null;
            if (!File.Exists(strFilePath))
            {
                animStrGroupRef = ScriptableObject.CreateInstance<AnimStrGroupRef>();
                AssetDatabase.CreateAsset(animStrGroupRef, strFilePath);
            }
            else
            {
                animStrGroupRef = AssetDatabase.LoadAssetAtPath<AnimStrGroupRef>(strFilePath);
            }
            
            var curveFilePath = Path.Combine(folderPath, AnimCurveKey.localStrFile);
            AnimCurveGroupRef animCurveGroup = null;
            if (!File.Exists(curveFilePath))
            {
                animCurveGroup = ScriptableObject.CreateInstance<AnimCurveGroupRef>();
                AssetDatabase.CreateAsset(animCurveGroup, curveFilePath);
            }
            else
            {
                animCurveGroup = AssetDatabase.LoadAssetAtPath<AnimCurveGroupRef>(curveFilePath);
            }
            Fix( ref LJogWeightCurve, animStrGroupRef, animCurveGroup);
            Fix( ref LSprintWeightCurve, animStrGroupRef, animCurveGroup);
            Fix( ref LJumpWeightCurve, animStrGroupRef, animCurveGroup);
            Fix( ref LSwimMoveWeightCurve, animStrGroupRef, animCurveGroup);
            Fix( ref LSwimIdleWeightCurve, animStrGroupRef, animCurveGroup);
            Fix( ref LIdleCrouchWeightCurve, animStrGroupRef, animCurveGroup);
            Fix( ref LLadderWeightCurve, animStrGroupRef, animCurveGroup);
            
            EditorUtility.SetDirty(animCurveGroup);
            AssetDatabase.SaveAssetIfDirty(animCurveGroup);
            EditorUtility.SetDirty(animStrGroupRef);
            AssetDatabase.SaveAssetIfDirty(animStrGroupRef);
            
            EditorUtility.SetDirty(this);
            AssetDatabase.SaveAssetIfDirty(this);
        }
        
        public void Fix<T>(ref T[] values)
        {
            var total = (int)TpBoneNameConf.End;
            if (values.Length != total)
            {
                //长度异常
                var oldvalues = values;
                values = new T[total];
                
                Array.Copy(oldvalues, values, Math.Min(oldvalues.Length, total));
            }
        }
        
        private void Fix(ref AnimCurveKey[] values, AnimStrGroupRef animStrGroupRef, AnimCurveGroupRef animCurveGroup)
        {
            var total = (int)TpBoneNameConf.End;
            Fix(ref values);

            for (var i = 0; i < total; i++)
            {
               ref var data = ref values[i];

               if (data.cache != animCurveGroup)
               {
                   var curveIdx = animCurveGroup.Add(data.displayCurve);

                   data.index = (short)curveIdx;
                   data.cache = animCurveGroup;
                   data.displayCurve = data.Get(animCurveGroup);
               }
               
               data.TryFixRef();
            }
        }
        
        private void Fix<T>(AnimKeyDict<T> old,ref T[] values, AnimStrGroupRef animStrGroupRef, AnimCurveGroupRef animCurveGroup)
        {
            var total = (int)TpBoneNameConf.End;
            Fix(ref values);
            foreach (var (k,v) in old)
            {
                var str = k.displayName;
                //去除空格
                str = str.Replace(" ", "");
                
                if(Enum.TryParse<TpBoneNameConf>(str, out var tpBoneNameConf))
                {
                    var index = (int)tpBoneNameConf;
                    ref var value = ref values[index];
                    value = v;
                }
                else
                {
                    Debug.LogError($"TpClipSettingMeta.Fix<T>方法只支持TpBoneNameConf类型的参数，当前字符串为{str}");
                    // throw new ArgumentException($"TpClipSettingMeta.Fix<T>方法只支持TpBoneNameConf类型的参数，当前字符串为{str}");
                }
            }
        }
        
        //
        public void AddCurve( ref AnimCurveKey[] curves,TpBoneNameConf nameConf,AnimationCurve curve,AnimCurveGroupRef animCurveGroup)
        {
            var curveIdx = animCurveGroup.Add(curve);

            AnimCurveKey curveKey = AnimCurveKey.CreateKey(animCurveGroup, curve);
                
            curves[(int)nameConf] = curveKey;
        }


        /// <summary>
        /// action对loc的权重
        /// </summary>
        private void InitAddLayerToLocomotionWeightCollection()
        {
            InitActionStateToLocoBoneCurve( ref LJogWeightCurve , 0);
            InitActionStateToLocoBoneCurve( ref LSprintWeightCurve , 0);
            InitActionStateToLocoBoneCurve( ref LJumpWeightCurve , 0);
            InitActionStateToLocoBoneCurve( ref LSwimMoveWeightCurve , 0);
            InitActionStateToLocoBoneCurve( ref LSwimIdleWeightCurve , 0);
            InitActionStateToLocoBoneCurve( ref LIdleCrouchWeightCurve , 0);
            InitActionStateToLocoBoneCurve( ref LLadderWeightCurve , 0);
        }

        private void InitActionStateToLocoBoneCurve(ref AnimCurveKey[] curves, float weight)
        {
            Fix(ref curves);
            
            var assetPath = AssetDatabase.GetAssetPath(this);
            var folderPath = Path.GetDirectoryName(assetPath);
            //在目标目录下创建一个字符串文件夹
            var strFilePath = Path.Combine(folderPath, AnimStrKey.localStrFile);
            AnimStrGroupRef animStrGroupRef = null;
            if (!File.Exists(strFilePath))
            {
                animStrGroupRef = ScriptableObject.CreateInstance<AnimStrGroupRef>();
                AssetDatabase.CreateAsset(animStrGroupRef, strFilePath);
            }
            else
            {
                animStrGroupRef = AssetDatabase.LoadAssetAtPath<AnimStrGroupRef>(strFilePath);
            }
            
            var curveFilePath = Path.Combine(folderPath, AnimCurveKey.localStrFile);
            AnimCurveGroupRef animCurveGroup = null;
            if (!File.Exists(curveFilePath))
            {
                animCurveGroup = ScriptableObject.CreateInstance<AnimCurveGroupRef>();
                AssetDatabase.CreateAsset(animCurveGroup, curveFilePath);
            }
            else
            {
                animCurveGroup = AssetDatabase.LoadAssetAtPath<AnimCurveGroupRef>(curveFilePath);
            }

            AddCurve(ref curves,TpBoneNameConf.Bip01, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref curves,TpBoneNameConf.Bip01Pelvis, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref curves,TpBoneNameConf.Bip01LThigh, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref curves,TpBoneNameConf.Bip01LCalf, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref curves,TpBoneNameConf.Bip01RThigh, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref curves,TpBoneNameConf.Bip01RCalf, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            
            AddCurve(ref curves,TpBoneNameConf.Bip01Spine, AnimationCurve.Linear(0, 0, 1, weight),animCurveGroup);
            AddCurve(ref curves,TpBoneNameConf.Bip01Spine1, AnimationCurve.Linear(0, 0, 1, weight),animCurveGroup);
            AddCurve(ref curves,TpBoneNameConf.Bip01Spine2, AnimationCurve.Linear(0, 0, 1, weight),animCurveGroup);
            AddCurve(ref curves,TpBoneNameConf.Bip01LClavicle, AnimationCurve.Linear(0, 0, 1, weight),animCurveGroup);
            AddCurve(ref curves,TpBoneNameConf.Bip01Neck, AnimationCurve.Linear(0, 0, 1, weight),animCurveGroup);
            AddCurve(ref curves,TpBoneNameConf.Bip01Head, AnimationCurve.Linear(0, 0, 1, weight),animCurveGroup);
            AddCurve(ref curves,TpBoneNameConf.Bip01RClavicle, AnimationCurve.Linear(0, 0, 1, weight),animCurveGroup);
            AddCurve(ref curves,TpBoneNameConf.BaseWeaponLocator, AnimationCurve.Linear(0, 0, 1, weight),animCurveGroup);
            AddCurve(ref curves,TpBoneNameConf.Bip01LUpperArm, AnimationCurve.Linear(0, 0, 1, weight),animCurveGroup);
            AddCurve(ref curves,TpBoneNameConf.Bip01LForearm, AnimationCurve.Linear(0, 0, 1, weight),animCurveGroup);
            AddCurve(ref curves,TpBoneNameConf.Bip01LHand, AnimationCurve.Linear(0, 0, 1, weight),animCurveGroup);
            AddCurve(ref curves,TpBoneNameConf.Bip01RUpperArm, AnimationCurve.Linear(0, 0, 1, weight),animCurveGroup);
            AddCurve(ref curves,TpBoneNameConf.Bip01RForearm, AnimationCurve.Linear(0, 0, 1, weight),animCurveGroup);
            AddCurve(ref curves,TpBoneNameConf.Bip01RHand, AnimationCurve.Linear(0, 0, 1, weight),animCurveGroup);
            
            EditorUtility.SetDirty(animCurveGroup);
            EditorUtility.SetDirty(animStrGroupRef);
        }

        private void InitAOCollection()
        {
            Fix(ref AoWeightCurve);
            
            var assetPath = AssetDatabase.GetAssetPath(this);
            var folderPath = Path.GetDirectoryName(assetPath);
            //在目标目录下创建一个字符串文件夹
            var strFilePath = Path.Combine(folderPath, AnimStrKey.localStrFile);
            AnimStrGroupRef animStrGroupRef = null;
            if (!File.Exists(strFilePath))
            {
                animStrGroupRef = ScriptableObject.CreateInstance<AnimStrGroupRef>();
                AssetDatabase.CreateAsset(animStrGroupRef, strFilePath);
            }
            else
            {
                animStrGroupRef = AssetDatabase.LoadAssetAtPath<AnimStrGroupRef>(strFilePath);
            }
            
            var curveFilePath = Path.Combine(folderPath, AnimCurveKey.localStrFile);
            AnimCurveGroupRef animCurveGroup = null;
            if (!File.Exists(curveFilePath))
            {
                animCurveGroup = ScriptableObject.CreateInstance<AnimCurveGroupRef>();
                AssetDatabase.CreateAsset(animCurveGroup, curveFilePath);
            }
            else
            {
                animCurveGroup = AssetDatabase.LoadAssetAtPath<AnimCurveGroupRef>(curveFilePath);
            }

            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01Pelvis, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01LThigh, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01LCalf, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01RThigh, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01RCalf, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            
            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01Spine, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01Spine1, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01Spine2, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01LClavicle, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01Neck, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01Head, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01RClavicle, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref AoWeightCurve,TpBoneNameConf.BaseWeaponLocator, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01LUpperArm, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01LForearm, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01LHand, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01RUpperArm, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01RForearm, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            AddCurve(ref AoWeightCurve,TpBoneNameConf.Bip01RHand, AnimationCurve.Linear(0, 0, 1, 0),animCurveGroup);
            
            EditorUtility.SetDirty(animCurveGroup);
            EditorUtility.SetDirty(animStrGroupRef);
        }
#endif
    }
}