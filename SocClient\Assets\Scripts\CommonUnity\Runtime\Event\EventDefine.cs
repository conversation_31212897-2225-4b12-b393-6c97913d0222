﻿namespace WizardGames.Soc.Common.Unity.Event
{
    public enum EventTag
    {
        Lobby,
        Game
    }

    public enum EventDefine
    {
        UiOpenEvent,
        UiShowAnimFinishEvent,
        LayerOpenEvent,
        UiHideEvent,
        LayerHideEvent,
        UiInventoryAnyUpdate,
        UiInventoryMainUpdate,
        UiInventoryBeltUpdate,
        UiInventoryWearUpdate,
        UiInventoryOpen,
        ChangeBuildMode,
        OnShopDataChanged,
        ConstructionDestroyCurAimPart,
        ConstructionCancelSelectUpgradeLevel,
        ConstructionSelectToRepair,
        ConstructionSelectToPickup,
        ConstructionSelectToClearWire,
        ConstructionSelectToDestroy,
        ConstructionRepairCurAimPart,
        ConstructionUpgradeCurAimPart,
        ConstructionClearWireAimPart,
        ConstructionSelectUpgradeLevel,
        ConstructionDoorStateChange,
        ConstructionBlueprintCdChange,
        BuildPanelSelectChange,
        BuildItemCancelSelect,
        BuildModeCancelEvent,
        UiBuildPanelSelectMode,
        UiBuildPanelSelectTag,
        UiBuildPanelRecoverState,
        UiBuildPanelSelectWireTag,
        UiFoldScreenChange,
        UiLoginInit,
        UiLobbyInit,
        LobbyLoginFail,
        LobbyLoginSuccess,
        TimerReset,

        #region Beta1BuildEvent
        BuildPanelRefresh,
        BuildEditDelete,
        BuildDeployMoveFinish,
        WireToolWireStateChange,
        WireToolPendingSlotRefresh,
        WireToolPendingSlotChange,
        BuildSkinBatchChange,
        SwitchManualWire,
        BuildFirstModeChange,
        BuildModeChange,
        BuildPartTransformChange,
        PartLastMoveTimeChange,
        #endregion


        HitShowHp,
        AimShowHp,
        PlayerStateChange,
        OnMyPlayerDie,  // 自己的玩家死亡
        WireToolBuildModeRefreshBatteryInfo,
        ProtectionUpdated,
        BatchRecoverCostData,
        BatchUpgradeConstructionData,
        BatchUpgradeConstructionChange,
        ShowUpkeepCostInfo,
        UpdateResearchComposeInfo,
        OnPlayMemberChange,
        OnChangePlayCollectionMembers,
        ServerStageUpdate,
        CloseUiSelectCamp,
        RefreshServerListAfterRequest,
        CoreConstructionBuildModeClickConstructionType,
        CoreBuildUiItemClick,
        ClearHoldingConstruction,
        ConstructionConfirmBuildOk,
        ConstructionCampingTentConfirm,
        ConstructionCampingTentCancel,
        ConstructionRotateAimPart,
        ConstructionChangeSkinOver,//皮肤变化完毕推送 RPC
        ConstructionSkinChanged,//皮肤id发生变化 （所有建筑都生效）
        BuildAutoSnapOptionRefresh,
        BuildItemSelect,
        ShowBombHomeTip,
        PlayMemberInfoChangeEvent,
        PlayerNumChangeEvent,
        BombHomeGameOver,
        SyncBombHomeTime,
        BombHomeCleanup,
        HitEntity,
        HitEntityPredict,
        Damage,
        UiSelectCableColorUseColor,
        ShowCupboardTip,
        UiClickInventory,
        UiHideAllInventoryIcons,
        UiPlayerLootUpdate,
        UiPickableDataChange,
        UiQuickLootStateChanged,
        UiQuickLootFinished,
        UiQuickDropStateChanged,
        UiQuickMoveStateChanged,
        UiQuickDropFinished,
        UiQuickPutOneFinished,
        UiQuickLootBagFull,
        UiShowInventoryDropArea,
        UiItemDragEnd,
        UiCraftQueueUpdate,
        UiBlueprintUpdate,
        UiCraftQueueCancelFail,
        //UiMainPackRefresh,
        UTechTreeNodeStateUpdate,
        UiClickItemTips,
        UiClothChoose,
        UiClothUnChoose,
        UiItemDragStart,
        UiItemClick,
        UiItemDoubleClick,
        UiPlayerLootOvenUpdate,
        AutoTurretModeChange,
        AutoTurretSwitchChange,
        UiPlayerLootDecomposeUpdate,
        UiPlayerLootDecomposeAnyUpdate,
        UiPlayerLootDigUpdate,
        UiPlayerLootComposterStateUpdate, //堆肥机开关和开启时间变化
        UiPlayerLootComposterAnyUpdate, //堆肥机变化
        ComItemIconChoose,
        DoResearchBenchOpenResponse,
        OnChatRecvMessage,
        OnChatRecvListMessage,
        DeployCampingTentResult,
        OnRecvTotalMessage,
        OnChatHudChangeEvent,
        OnQuickTeamChatSendSuccess,
        SwitchTeamInfo,
        StartReload,
        ReloadSetClip,
        ClientReloadCdFinish,
        AidingOtherEvent,
        OnChangeWireSlotInfo,
        WireToolBuildModeOnChangeWireSlot,
        WireToolBuildModeChangeWireColour,
        OnHudStateChanged,
        OnHudRootCaptureTouch,
        OnHudRootCaptureTouchExcludeJoyStick,
        OnChatChannelModify,
        OnGetMultiPlayerBriefInfo,
        OnCloseChatMain,
        UpdatePlayerHudMsgIdList,
        WireToolBuildModeClearWire,
        ComboConstructionSelect,
        EditTargetForceRefresh,
        BuildBluePrintLockTip,
        ComboConstructionSelectUpgradeTarget,
        RespawnRebornPointRefresh,
        CloseAllMutexDyingWindow,
        CloseAllMutexRespawnWindow,
        DoGamePreLoad,
        ObserverEnterWorld,
        WireToolBuildModeOnClickAdd,
        WireToolBuildModeOnPressRemove,
        WireToolBuildModeOnReleaseRemove,
        WireToolBuildModeFinishWire,
        OnWireCreate,
        PowerLightDeploySuccess,
        PowerLightFinishDeploy,
        OnClickBed,
        OnClickBedCDGroup,
        OnClickBedClusteringGroup,
        BlankClickOnMap,
        EnterOverviewMode,
        FitMapScaleAndPosForCDGroups,
        EnterDyingState,
        UiRespawnCdListActive,
        OnTrackDeathPoint,
        UpdateWeaponSpread,
        AidBtnStateChange,
        ShortcutsItemEntityChange,
        ShortcutsChooseMenuStateChange,
        UiLoadingDisable,
        CloseManModel,
        UiHideAllItemDetailIcons,
        HudInEdit,
        HoldingConstructionInfoChange,
        PartContainerLinkUpdate,
        TouchThroughSettingChange,
        MonsterRemoveWeaponAudio,
        #region 任务
        UpdateSingleMission,
        UpdateSingleMissionCount,
        UpdateMissionMarker,
        MissionGuideDataChange,
        UpdateTreasureBoxMissionMarker,
        UpdateTreasureHuntMissionMarker,
        UpdateRefreshTreasureTask,
        UpdateTreasureTaskPanel,
        AddTaskDoneNotify,
        UpdateFreeModeTask,
        UpdateTrackMissionId,
        UpdateMonumentTrackId,
        UpdateMonumentTypeId,
        UpdateMonumentMission,
        ShowTaskMarkerFlyAnimation,
        TutorialFinishNewbieMissionCG,
        UpdateDailyMission,

        UpdateFactionLevel,
        UpdateFactionMission,
        #endregion

        #region 新手关
        TutorialFinishSubtitle,
        TutorialFinishTimeline,
        TutorialStartCartoon,
        TutorialFinishCartoon,
        TutorialFinishShowUi,
        TutorialStoryModeChanged,
        TutorialStoryNodeChanged,
        TutorialFinishViewGuide,
        HitFakeMonster,
        TutorialClickRemoteControl,
        TimelineUpdatePlayableStatus,
        TimelineShowSound,
        TimelineStopSound,
        CloseTelescope,
        TimelineHideAim,
        TimelineShowAim,
        TimelineUseTelescope,
        SetTeachingNpcMarkerDesc,
        #endregion

        #region 种子背包
        UpdateSeedBackpack,
        #endregion

        OnUpdateBombHomeOfflineMembers,
        OnUpdateBombHomeOfflineStage,
        OnUpdateBombHomeOfflineStageTime,
        OnUpdateBombHomeOfflineBattleId,
        OnUpdateBombHomeOfflineInitScore,
        OnUpdateBombHomeOfflineIncomeScore,
        OnUpdateBombHomeOfflineCostScore,
        OnUpdateBombHomeOfflinePlayTime,
        OnRecvBombHomeOfflineStart,
        OnRecvBombHomeOfflineStop,
        KatyushaGetAimPoint,
        SubscribeAck,
        SmallGeneratorUpdate,
        OnKatyushaLaunchStateChanged,
        OnKatyushaMissileNumChanged,
        OnUpdateReputationName,
        RecoverVirtualPart,
        RecoverAllParts,
        ClientPartGoRemove,
        MultiPlayerBriefInfosGetReply,
        PlayerSearchResultGetReply,
        PermPlayerInfoCacheDirty,
        OnPermCenterMembersCacheRefresh,
        OnPermDeleteFromGroup,
        UiPermPlayerSearchResultCacheDirty,
        OnPermAcceptJoinCb,
        OnPermRejectJoinCb,
        OnPermPassivelyAddToGroup,
        OnPermPassivelyDeleteFromGroup,
        OnPermDeleteMemberCompletely,
        OnPermHubRenamed,
        OnPermApplyListChanged,
        OnPermAdminChanged,
        OnPermGroupChanged,
        OnPermApplyResultChanged,
        OnPermGroupCreatedAck,
        OnMyPermChanged,
        OnNearbyCorpseChange,
        OnNearbyRewardBoxChange,
        OnNearbyCollectionChanged,
        OnAutoPickSettingChange,
        OnClosePickSettingChange,
        OnDiePickAtTopSettingChange,
        OnPickUpSettingChange,
        UpdateMonumentGuidePoint,
        #region 种植
        /// <summary>
        /// 种植箱水量更新
        /// </summary>
        UpdatePlantWater,
        /// <summary>
        /// 种植成功
        /// </summary>
        PlantSuccess,
        /// <summary>
        /// 浇水成功
        /// </summary>
        WaterSuccess,
        /// <summary>
        /// 施肥成功
        /// </summary>
        ManureSuccess,
        /// <summary>
        /// 收获成功
        /// </summary>
        HarvestSuccess,
        /// <summary>
        /// 移除成功
        /// </summary>
        RemovePlantSuccess,
        /// <summary>
        /// 种植信息更新
        /// </summary>
        UpdatePlantsInfo,
        
        /// <summary>
        /// RT 种植箱中的格子被选中（包含拖拽）
        /// </summary>
        PlantBoxModelSelectedSlot,
        /// <summary>
        /// 详情页，格子被选中
        /// </summary>
        DetailPageSelectedSlot,
        /// <summary>
        /// 种植页，格子被选中
        /// </summary>
        PlantPageSelectedSlot,
        /// <summary>
        /// 施肥页,格子被选中
        /// </summary>
        ManurePageSelectedSlot,
        /// <summary>
        /// 打开施肥页时，选中所有可施肥的格子
        /// </summary>
        SelectMultipleSlotWhenOpenManurePage,
        /// <summary>
        /// 预种植
        /// </summary>
        PrePlant,
        /// <summary>
        /// 取消预种植
        /// </summary>
        CancelPrePlant,
        
        // OnChangePlantMainPage,
        OnHybridizeStart,
        OnHybridizeFinish,
        #endregion
        HudBlockElemChange,
        ShopPurchaseSuccess,
        UpdateShopMallInfo,
        LobbyCoinUpdate,
        UpdatePickItemsNum,
        OnDropItemFromBag,
        OnGetMultiPlayerNames,
        OnUiOthersideNearByOpen,
        OnUiInteractiveListVisibleChange,
        OnLobbyStepChange,
        UpdateBedInfo,
        InventoryMainSelectType,
        HideEntryList,


        #region 建筑蓝图
        EnterConstructionBlueprintMode,
        ExitConstructionBlueprintMode,
        SelectConstructionBlueprint,
        StartWujiTurn,
        StopWujiTurn,
        RaiseStart,
        RaiseEnd,
        RaiseStep,


        #endregion

        #region 新手引导
        TryGuideRespawn,
        TryGuideEnterState,
        TryGuideNight,
        TryGuideGetItem,
        TryGuideOpenOrnament,
        TryGuideAirdrop,
        TryGuideEnterMonument,
        TryGuideInterface,
        TryGuideHandheldProp,
        TryGuideCollection,
        TryGuidePlaceDecoration,
        TryGuideSafetyBoxActived,
        TryGuideSafetyBoxDestroy,

        TryManualInterface,

        FinishGetPlayerData,
        EndGuideStepClose,
        UnlockManual,
        BreakGuide,
        #endregion

        UpdateEquips,
        UpdateItemNode,
        AddItemNode,
        RemoveItemNode,

        ShowUsingItemLoading,
        HideUsingItemLoading,

        ShowEscapeLoadingBar,
        HideEscapeLoadingBar,

        UpdateSwipeCardGameState,
        BlinkInventoryItemsByCond,
        BlinkInventoryItemsByCondNoScroll,
        VendingMachineLootCountChange,
        VendingMachineGoodsOrPriceItemChange,
        VendingMachineOpenSelectItem,
        VendingMachineSelectItem,
        ItemDetailGoBlueprint,

        #region 一键升级
        OnBatchUpgradeStatusChange,
        OnBatchUpgradeFinish,
        OnTerrCenterRemoved,
        #endregion

        HidePermissionCenterEffect,
        ChangePermissionCenterEffect,
        LoadGameSpaceScene,

        #region 大厅组队
        LobbyTeamStartGame,
        LobbyTeamUpdateMode,
        LobbyTeamTeamNotice,
        OnFriendRequestSend,
        LobbyTeamMemberUpdate,
        UpdateTeamPermission,
        LobbyTeamInviteNotify,
        LoobyTeamReadyNotify,
        AppointServerSoonOpenNotice,
        AppointServerOpenNotice,
        AddTeamMemberNotice,
        CreateLobbyTeamNotice,
        AppointmentTeamTimeOutNotice,
        LobbyTeamReconnectedNotice,//正常队伍重连
        LobbyTeamSelfLeaveNotice,//大厅自己主动离队
        LobbyTeamMemberLeaveNotice,//大厅队伍离队
        LobbyTeamDataInitNotice,//大厅队伍数据初始化
        AppointmentLobbyTeamDataInitNotice,// 预约队伍数据初始化
        LobbyTeamMemberReadyNotice,//大厅队伍成员准备状态变化，匹配模式
        #endregion

        JoinOldBattleNotice,

        #region 大厅仓库
        OnLobbyStashDataChange,
        OnLobbyStashItemTimeOutNotice,
        ShowUiResTitleNotice,
        #endregion

        #region 大厅声望
        OnReputationRewardItemClicked,
        OnUnlockReputationRewardClicked,
        #endregion
        #region 局内信息
        OnEntityBriefInfoAck,
        #endregion
        #region 局内声望
        OnRewardIconClickedToSelect,
        OnExitReputationEditMode,
        OnReplaceRewardAck,
        OnReputationCallDrop,
        OnGetReputationLevel,
        OnGetReputationReward,
        UpdateBadgeSlotInfo,
        UpdateBadgeInfo,
        OnBadgeTaskChange,
        ReputationLevelChange,
        ReputationLevelHideChange,
        ReputationBadgeWearChange,
        OnReputationRecordsChange,
        OnReputationRewardDictChange,
        OnReputationFatigueValueChange,

        OnOutputContainChange,//声望输出容器变化
        OnInputContainChange,//声望输入容器变化
        OnReputationEfficiencyChange,//转化效率变化
        OnCabineLevelChange,//情报柜等级变化
        #endregion

        ReloadBulletEnd,
        ReloadBulletStart,

        CombatMarkerAddOrRemove,

        #region 局内组队
        OnSearchBattlePlayRoleIds,
        AcceptInvite,
        OnHandleBattleTeamRedNotice,
        OnTeamMemberDictRemove,
        OnTeamMemberDictInsertOrOverride,
        OnTeamMemberDictModify,
        OnTeamMemberDictClear,
        OnTeamMemberOnlineChange,
        OnTeamEntityClientCleanUp,
        OnCheckTeammateTerritoryCabinetAck,
        ImpeachStateChange,
        VoteRolesChange,
        OnTeammateLoginNotice,
        OnTeamUpdateDisplayData,
        #endregion
        SearchPlayerError,

        #region 异端容器
        OnStartLootingAck,
        OnSwitchLootingAck,
        OtherSideChooseNext,
        OtherSideMoveItemToPathAck,
        OtherSideMoveItemToPathFailure,
        HorseUnEquipSuccess,
        OvenQueueDataUpdate,
        OvenQueueClaimUpdate,
        OvenQueueCancelUpdate,
        OvenQueueMove,
        OvenNameUpdate,
        OvenLootingIdsUpdate,
        #endregion

        #region 大厅招募
        LobbyPublishRecruitNotice,
        LobbyAgreeAppleforNotice,
        LobbyAddRecruitNotice,
        LobbyRecruitChange,
        GameRecruitListChange,
        LobbyRecruitMembersChange,
        GameToLobbyRecruitDeleteNotice, // 局内发到大厅的招募删除通知
        GameRecruitDeleteNotice, // 局内招募删除通知

        #endregion
        SelfGameRecruitChange, // 局内自己队伍的招募信息变化了
        ClosePublishGameRecruitView, // 关闭局内发布招募界面
        JumpToGameTeamTab, // 跳转到局内队伍的某个页签界面
        ReqGameRecruitListResult, // 局内请求招募列表结果
        GameApplyInfosChange,
        NewRecruitmentApplicationNotify, // 有新的局内入队申请
        CloseApplyJoinView,

        #region 保险柜
        ItemChangeSafeBoxAck,
        StateChangeSafeBoxAck,
        TriggerPutInAll,
        SafeBoxTriggerToEmpty,//保险柜内全部取完
        #endregion

        OnEnterTerritory, // 进入领地
        OnExitTerritory, // 离开领地
        OnTerritoryAtted, // 领地遇袭 参数为领地entityid  和 遇袭时间

        BackpackJumpToCraft,
        RefreshItemTips,

        UiResHandleCurPanelChange,
        UserInfoUpdate,
        UserInfoUpdateReq,
        ChangePlayerNameNotice,
        ChangeNameError,
        ChangePlayerHeadIconNotice,
        RefreshChangeNameCDNotice,

        OnFriendChange,
        OnFriendRequestNumChange,
        OnFriendRequestChange,
        OnTempFriendChange,
        RequestRoleArchiveSuccesNotice,

        #region 一键修复
        OnQueryRecoverInfo,
        #endregion

        #region electricalData
        ReceiveElectricalData,
        ReceiveElectricalDataWithSlots,
        #endregion
        OnWireBuildModeChangeSubMode,


        #region 新版地图
        OnSidePanelSubviewItemSelect,
        OnSidePanelSubviewCreate,
        MainMapEnableNotify,
        #endregion

        #region 标记
        AddIOInfo,
        RemoveIOInfo,
        UpdateSafetyBoxMarker,
        RpcCreateCombatMarker,
        HideTeamCombatMarkerFlagChanged,
        TeamMemberDictChange,
        TeamEntityClientInit,
        ReviewCreateCombatMarker,
        #endregion

        #region 射频发射器
        CloseSetRFUi,
        #endregion

        RefreshBuildCollapseMarker,
        RemoveBuildCollapseMarker,
        AddHoldingConstructionMarker,
        RemoveHoldingConstructionMarker,
        MarkerInScreenCenter,
        CreateHudCombatMarker,
        RemoveHudCombatMarker,
        RefreshShopMarkerPosition,
        RefreshPartBaseMarkerPosition,
        ForceShowMarkerDetail, //预告事件：强制显示某一种标记详情，其他人不准用这个消息！！！！
        ForceHideMarkerDetail,
        CancelSelectMarker,
        RefreshDeathPointMarker,
        MailInfoChange,
        ShowMailRewardPanel,

        #region 兴趣部落
        MyTribeInfosChange,
        TribeMemberInfoChange,
        MyTribeDetailInfoChange,
        #endregion


        PackUpPickList,
        BtnTpsSwitchUpdated,
        Interactive2LabelShow,

        CloseFocusInfoNotice,

        HudOpenStatesChange,
        HudWeaponIconClipForceRefresh,
        HudWeaponIconDragStart,
        HudWeaponIconDragEnd,
        OnHudMakeFullScreen,
        RefreshChatUi,
        UiSettingOpen,
        UpdateResearchBench,
        UpdateResearchBenchBlueprint,

        BedPlayerSearchNotice,
        RepairBenchContainerChange,
        KatyushaCdStateChange,
        KatyushaStateChange,
        HitEntityDamageEmitFont,
        TpHeldItemBlocked,
        ChangeGMOpenNotice,

        QuickActHightLightBackpack,
        QuickActHightLightEquip,
        QuickActHightLightWeapon,
        QuickActHightLightTool,
        QuickActHightLightOther,
        QuickActAcceptDragBackpack,
        QuickActAcceptDragEquip,
        QuickActAcceptDragWeapon,
        QuickActAcceptDragTool,
        QuickActAcceptDragOther,
        OpenUIDyingNotice,
        CloseUIDyingNotice,

        UiInventorySuitState,
        UiInventoryBatchDropSelectAll,
        OnTriggerDataLoadFinish,
        RefreshUiActDebugInfo,

        PartTransformationOver,

        ElemEntryListHide,
        ElemEntryListShow,
        OnSoftDisconnected,
        UiUpdateExtendPackIcon,
        OnReconnectFailed,

        PhysicsSimulate,
        OnFastReconnected,
        HideRoleInfo,
        ServerChangeTime,
        OnMyPlayerEntityCreate,
        OnStartGameErrorCallbackNotice,

        ForceUpdateEquip,
        TryStartEngine,
        DebugEndBubble,
        ShadowCacheChange,

        CenterRouletteOpen,
        CenterRouletteClose,
        MyPlayerOnHurt,

        //道具可用状态更新
        Battle_ItemUsableUpdate,
        Ui_MyPlayer_HeldItemSwitchSuccess,
        AfterHUDSwitchHeldItem,
        VehicleModuleHpChange,
        VehicleAutoDriveChange,

        CampingTentLogoutSuccess,
        SafetyStateChange,
        SeatChange,

        TeleScopeOnAimFinish, //望远镜开启动作完成
        TeleScopeOffAimFinish, //望远镜收起动作完成
        TeleScopeStateChange,   //望远镜状态改变        
        //官方小屋创建完成
        BlueprintCreatComplete,

        GetTeamMemberDisplayInfoAck,//拉取局内组队模型数据
        OnHideItemTips,//关闭物品tips时触发
        LobbyOnReconnected,//大厅重连成功事件
        OnRejectLobbyInvite,//拒绝大厅的组队邀请
        OnRejectAllTeamInvite,//拒绝所有的组队邀请
        OnShowTerrGuild,//展示领地柜引导
        OnHideTerrGuild,//隐藏领地柜引导
        OnWireCreateLinkInfo,//电线创建时，连接的俩端EntityID
        OnStoryTaskReceiveTaskResult,//领取剧本奖励结果
        DeadSheepStateChanged,//死羊领地状态变化
        CheckWireHaveElectricity,//电力查看模式下，所选端口是否有电
        WireSlotComboBoxChange,//输入输出端口切换
        LevelOrExpChanged,//等级或者经验值变化

        TipsBubbleStart,    // 快捷操作气泡开始
        TipsBubbleEnd,      // 快捷操作气泡结束
        PreloadSceneModelFinish,    //预加载场景模型完成
        PlayerSelfDisplayDataUpdate,//玩家自身显示数据更新
        OnStoryStageChange,//剧本阶段变化

        UseCardAllowed,//允许咕咕鸡刷卡
        BagUseCardAllowed,//背包允许咕咕鸡刷卡
        RefreshPandoraEntryState,//刷新潘多拉入口状态
        OnVoiceBand,//语音被封禁
        OnLiftingPlatformOperationNotify,   // 载具升降台操作通知
        OnBeeBuzzTaskChange,                // 蜂鸣行动任务变化
        OnBeeBuzzDataChange,                // 蜂鸣数据变化
        PlaySurvivalEffect,//播放生存手册按钮动效

        WireModeChange,//接线模式变化
        InterruptPickListMsg,               // 打断拾取列表消息
        ProactivelyExitBuildMode,           // 主动退出建造模式
        OnSkinGoLoaded,                     // 建造皮肤加载完毕
        AfterAnimBegin,
        BeforeSkin,
        WeaponOnDeploy, //武器装备上
        OpenEntryList,//打开中控台
        TriggerBtnInteractiveIdChange,      // 触发按钮交互ID变化
        LadderStateChange,                  // 爬梯状态变化
        LadderBtnClickEvent,                // 爬梯按钮点击事件

        OnPlayerIsLocking,                  // 玩家使用毒刺导弹开始锁定
        OnLockableEntitiesChange,           // （毒刺导弹）可被锁定实体变化
        OnLockingEntityChange,              // （毒刺导弹）锁定实体变化

        OnPartEntityInit,//建筑Init
        OnPartEntityTransChangeAtTransformAfter,//摆件旋转或位置改变,在Transform赋值之后
        
        OnLadderTransformChange,            // 爬梯位置变化

        #region 快捷键
        HotKeySettingChanged,//快捷键设置修改
        UpdateHotKeyTips,//更新快捷键ComKeyTips
        HotKeySideGuideChanged,//快捷键引导修改
        HotKeyCenterGuideChanged,//快捷键引导修改
        IsInGameChanged,//游戏状态改变
        HotKeyInputStateChanged,            //快捷键输入状态改变
        SkipHotKeyPressInterrupt,            //跳过快捷键按下中断
        #endregion

        UnlockConditionChange,                  // 解锁条件变化
        ParachuteStateChange,                   // 降落伞状态变化

        OnLittleEyeEntered, //小眼睛进入
        OnLittleEyeExited, //小眼睛退出
        OnChoseSuitPlan,//选择个人主页的套装方案
        BeforePhotoModeEnter,//进入拍照模式前
        OnPhotoModeEntered,//进入拍照模式
        OnPhotoModeExited,//退出拍照模式
        AfterPhotoModeExit,//退出拍照模式后
        OnPhotoFpEntered,//进入FP模式
        OnPhotoTpEntered,//进入TP模式
        OnPhotoFreeEntered,//进入自由相机模式
        OnPhotoFreeLeaved,
        OnPhotoPathLoopChange, // 相机路径循环变化
        OnPhotoTrackEntered,//进入轨道相机模式
        OnPhotoTrackLeaved,
        OnPhotoShowVehicleHud,//显示载具HUD
        OnPhotoHideVehicleHud,//隐藏载具HUD
        OnPhotoHideAllUI,//隐藏所有UI
        OnPhotoDataChanged,//拍照数据变化
        OnPhotoControlChanged,//拍照控制变化
        OnPhotoControlReset,//拍照控制重置
        PhotoUIUpdate, //拍照UI更新

        OnScreenCaptureFinished,//屏幕截图完成

        OnCurrencyUpdate, //货币更新
        MallBundleInfoUpdate,   // 商城-捆绑包信息更新
        MallInfoUpdate,         // 商城-商品信息更新
        OnDisplayDataUpdate,
        ComOngoingDisappearNotice,
        OnChangeRoleAvatar,  //修改人物模型
        ComItemTipDisappearNotice,
        ComLeftInfoTipDisappearNotice,
        OnSkinPlanChange,//皮肤套装数据发送变化
        OnGetGestureData,//请求手势数据
        OnGetSpraysData,//请求喷漆数据
        SettingValueChanged,            // 设置项的值变化
        RefreshScreenSettingUi,            // 刷新画面设置项UI
        ModifyScreenSettingCountChange,       // 画面设置项的修改数量变化

        OnFireStateChange, //熔炉火焰燃烧
        UiLobbyMainAnimFinish,

        OnConstructionReportPhotoModeEntered,//进入建筑举报拍照模式
        OnConstructionReportPhotoModeExited,//退出建筑举报拍照模式

        #region 势力/天赋
        TalentRewardReceived, //天赋奖励领取
        ForceLevelExpUpdate,//势力等级经验更新
        TalentUpdate,//天赋更新
        #endregion
        LobbyBattleServerDataUpdate,//战斗结算奖励通知

        LayerComStackCoverUpdate,//栈更新

        OnAdminOpen,//打开管理员工具界面
        OnAdminClose,//关闭管理员工具界面
        AdminUIUpdate,//管理员工具界面更新
        OnTouchBegin,//触摸开始
        RippleSystemInit,//水面涟漪系统初始化

        UpdateBattlePassInfo,   // 战令信息更新
        UpdateBattlePassReward, // 战令奖励更新

        OnGetCosReportTempCredential, // 获取Cos临时授权
        OnGetOutsideCreatorId, // 获取外壳数据的创建者id

        ObserverGetAllTerritory,
        ObserverGetTerritoryDetail,
        ObserverGetAllPlayer,
        ObserverGetPlayerDetail,

        BuildPanelExpandState,//建筑导航栏展开状态

        DeadCameraEvent,//死亡相机事件
        RedDotPulled,//拉回红点数据
        MedalLevelCount,
        MedalMaxLevel,
        RecentStyleScore,
        TribeTipChanged,            // 社群提示变化

        TopBarReportSelect,//topbar举报按钮选中通知
        ReportInfoUpdate,//举报
        EncounteredPlayersUpdate,//最近遭遇玩家信息

        UiShowLogo,
        UiShowPhoneInfo,
        DeleteBattleServerSuccessNotice, // 删除战斗结算服务器

        GachaInfoUpdate,//不放回抽奖奖池更新
        DrawGachaInfoUpdate,//抽奖后更新
        LobbyTeamVoiceChange,//大厅语音状态改变

        HotKeyPressed,//快捷键按下
        UiSettingChangeSafeArea,
        GetSettleStyleRankPointsSwitch,

        DeleteSettleSwitchSuccess,
        SetSettleSwitchSuccess,
        PlayerHomePageSwitch,
    }
}