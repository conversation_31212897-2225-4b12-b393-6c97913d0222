﻿using WizardGames.Soc.Common.Algorithm;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Data.IOInteractiveEnum;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Event;

namespace WizardGames.Soc.SocWorld.Event
{
    public interface IEventWithMark
    {
        string Mark { get; }
    }

    /// <summary>
    /// 玩家武器配件变化
    /// </summary>
    [Event]
    public class PlayerAccessoryChange(long bizId, long accessoryId) : IEventWithBizId
    {
        public long BizId { get; private set; } = bizId;
        public long AccessoryId { get; private set; } = accessoryId;
    }

    /// <summary>
    /// 玩家装备变化
    /// </summary>
    [Event]
    public class PlayerEquipChange : IEventWithBizId
    {
        public PlayerEquipChange(long bizId)
        {
            BizId = bizId;
        }

        public long BizId { get; private set; }
    }

    /// <summary>
    /// 玩家背包道具变化
    /// </summary>
    [Event]
    public class ItemAdd : IEventWithBizId, IEventWithMark, IEventWithCount
    {
        public ItemAdd(long bizId, int incrementCount, string mark)
        {
            BizId = bizId;
            IncrementCount = incrementCount;
            Mark = mark;
            HasEnteredInventoryBefore = false;
        }
        public ItemAdd(long bizId, int incrementCount, string mark, bool hasEnteredInventoryBefore)
        {
            BizId = bizId;
            IncrementCount = incrementCount;
            Mark = mark;
            HasEnteredInventoryBefore = hasEnteredInventoryBefore;
        }

        public long BizId { get; private set; }
        public int IncrementCount { get; private set; }
        public string Mark { get; private set; }
        public bool HasEnteredInventoryBefore { get; private set; }

        public int Count => IncrementCount;
    }

    /// <summary>
    /// 玩家背包道具变化
    /// </summary>
    [Event]
    public class ItemRemove : IEventWithBizId, IEventWithMark, IEventWithCount
    {
        public ItemRemove(long bizId, int removeCount, string mark)
        {
            BizId = bizId;
            RemoveCount = removeCount;
            Mark = mark;
        }

        public long BizId { get; private set; }
        public int RemoveCount { get; private set; }
        public string Mark { get; private set; }

        public int Count => RemoveCount;
    }

    /// <summary>
    /// 制造道具
    /// </summary>
    [Event]
    public class ManufactureItem : IEventWithBizId, IEventWithCount
    {
        public ManufactureItem(long bizId, int count)
        {
            BizId = bizId;
            Count = count;
        }

        public long BizId { get; private set; }
        public int Count { get; private set; }
    }

    /// <summary>
    /// 建造
    /// </summary>
    [Event]
    public class ConstructEvent : IEventWithBizId
    {
        public enum EType
        {
            Construct,
            GradeChange,
            Remove
        }

        public ConstructEvent(long templateId, EType type, long territoryId)
        {
            TemplateId = templateId;
            Type = type;
            TerritoryId = territoryId;
        }

        public ConstructEvent WithNewGrade(int newGrade)
        {
            NewGrade = newGrade;
            return this;
        }

        public ConstructEvent WithOldGrade(int oldGrade)
        {
            OldGrade = oldGrade;
            return this;
        }

        public long TemplateId { get; private set; }
        public EType Type { get; private set; }

        public int NewGrade { get; private set; }

        public int OldGrade { get; private set; }

        public long BizId => TemplateId;

        public long TerritoryId { get; private set; }
    }

    /// <summary>
    /// 掠夺异端容器
    /// </summary>
    [Event]
    public class LootingStorageChangeEvent : IEventWithBizId, IEventWithTargetEntity, IEventWithEntityId
    {
        public LootingStorageChangeEvent(long templateId, long bizId, int incrementCount, long targetEntityId, long entityId, bool isQuickOperate = false)
        {
            TemplateId = templateId;
            BizId = bizId;
            IncrementCount = incrementCount;
            //掠夺者
            TargetEntityId = targetEntityId;
            EntityId = entityId;
            IsQuickOperate = isQuickOperate;
        }

        public long TemplateId { get; private set; }

        public long BizId { get; private set; }

        public int IncrementCount { get; private set; }
        //掠夺者的EntityId
        public long TargetEntityId { get; private set; }
        //被掠夺的EntityId
        public long EntityId { get; private set; }
        public bool IsQuickOperate { get; private set; }
    }

    /// <summary>
    /// 进入某类区域的事件
    /// </summary>
    [Event]
    public class AreaEvent : IEventWithBizId
    {
        public AreaEvent(int monumentTypeId)
        {
            MonumentTypeId = monumentTypeId;
        }

        public int MonumentTypeId { get; private set; }

        public long BizId => MonumentTypeId;
    }

    /// <summary>
    /// 维修道具事件
    /// </summary>
    [Event]
    public class RepairItemEvent : IEventWithBizId, IEventWithCount
    {
        public RepairItemEvent(long bizId, int count)
        {
            BizId = bizId;
            Count = count;
        }

        public long BizId { get; private set; }
        public int Count { get; private set; }
    }

    [Event]
    public class HuntEvent : IEventWithBizId
    {
        public HuntEvent(long monsterId)
        {
            MonsterId = monsterId;
        }

        public long MonsterId { get; private set; }

        public long BizId => MonsterId;
    }

    [Event]
    public class UnlockTechEvent : IEventWithBizId
    {

        public UnlockTechEvent(long bizId, ulong roleId)
        {
            BizId = bizId;
            RoleId = roleId;
        }

        public long BizId { get; private set; }
        public ulong RoleId { get; private set; }
    }

    [Event]
    public class UseItemEvent : IEventWithBizId, IEventWithCount, IEventWithEntityId
    {
        public UseItemEvent(long bizId, int useCount, long entityId)
        {
            BizId = bizId;
            UseCount = useCount;
            EntityId = entityId;
        }

        public long BizId { get; private set; }
        public int UseCount { get; private set; }
        public long EntityId { get; private set; }
        public int Count => UseCount;
    }
    [Event]
    public class StartTrainCarEvent : IEventWithBizId
    {
        public StartTrainCarEvent(long templateId)
        {
            TemplateId = templateId;
        }

        public long TemplateId { get; private set; }

        public long BizId => TemplateId;
    }

    [Event]
    public class TrainUnloadEvent : IEventWithBizId
    {
        public TrainUnloadEvent(long trainEntityId, long templateId, float healthPercent, int unloadType = 0)
        {
            TrainEntityId = trainEntityId;
            BizId = templateId;
            HealthPercent = healthPercent;
            UnloadType = unloadType;
        }
        public long TrainEntityId { get; private set; }

        public float HealthPercent { get; private set; }

        public long BizId { get; private set; }

        public int UnloadType { get; private set; }
    }

    /// <summary>
    /// 完成任务领奖类型事件
    /// </summary>
    [Event]
    public class GetTaskRewardEvent : IEventWithBizId
    {
        public GetTaskRewardEvent(long bizId, int incrementCount)
        {
            BizId = bizId;
            IncrementCount = incrementCount;
        }

        public long BizId { get; private set; }
        public int IncrementCount { get; private set; }
    }

    [Event]
    public class OpenCardRoomEvent : IEventWithBizId
    {
        public OpenCardRoomEvent(IOType ioType)
        {
            IOType = ioType;
        }

        public IOType IOType { get; private set; }

        public long BizId => (long)IOType;
    }


    [Event]
    public class DriveEvent : IEventWithBizId
    {
        public DriveEvent(long mountId)
        {
            MountId = mountId;
        }

        public long MountId { get; private set; }

        public long BizId => MountId;
    }

    [Event]
    public class ExitVehicleEvent : IEventWithBizId
    {
        public ExitVehicleEvent(long vehicleEntityId)
        {
            VehicleEntityId = vehicleEntityId;
        }

        public long VehicleEntityId { get; private set; }

        public long BizId => VehicleEntityId;
    }

    [Event]
    public class DamageEvent : IEventWithBizId, IEventWithCount, IEventWithTargetEntity, IEventWithEntityId
    {
        public DamageEvent(long bizId, int count, long targetEntityId, long entityId)
        {
            BizId = bizId;
            Count = count;
            TargetEntityId = targetEntityId;
            EntityId = entityId;
        }

        public long BizId { get; private set; }
        public int Count { get; private set; }
        public long TargetEntityId { get; private set; }
        public long EntityId { get; private set; }
    }
    /// <summary>
    /// 这个事件的常量由任务系统定义，有机会可以改为全局常量定义
    /// </summary>
    [Event]
    public class FinishActionEvent : IEventWithBizId
    {
        public FinishActionEvent(long bizId)
        {
            BizId = bizId;
        }

        public long BizId { get; private set; }
    }

    [Event]
    public class CollectEvent : IEventWithBizId
    {
        public CollectEvent(long bizId)
        {
            BizId = bizId;
        }

        public long BizId { get; private set; }
    }

    [Event]
    public class UnlockBlueprintEvent : IEventWithBizId
    {
        public UnlockBlueprintEvent(long bizId, ulong roleId)
        {
            BizId = bizId;
            RoleId = roleId;
        }

        public long BizId { get; private set; }
        public ulong RoleId { get; private set; }
    }

    [Event]
    public class CallIntelligenceBonusEvent : IEventWithBizId, IEventWithCount
    {
        public CallIntelligenceBonusEvent(long bizId, int count)
        {
            BizId = bizId;
            Count = count;
        }

        public long BizId { get; private set; }
        public int Count { get; private set; }
    }

    [Event]
    public class UnityDsTriggerEvent : IEventWithBizId
    {
        public UnityDsTriggerEvent(int triggerId)
        {
            BizId = triggerId;
        }

        public long BizId { get; private set; }
    }

    [Event]
    public class DestroyPartEvent(long templateId, int grade, long entityId, long territoryId, long weaponTableId)
        : IEventWithBizId, IEventWithEntityId
    {
        public long BizId { get; private set; } = templateId;
        public int Grade { get; private set; } = grade;
        public long EntityId { get; private set; } = entityId;

        public long TerritoryId { get; private set; } = territoryId;
        public long WeaponTableId { get; private set; } = weaponTableId;
    }

    public class DamageData
    {
        public readonly long SourcePlayerId;
        public readonly long SourceId;
        public readonly long SourceTableId;
        public readonly long WeaponTableId;
        public readonly int DamageType;
        public DamageData(long sourcePlayerId, long sourceId, long sourceTableId, long weaponTableId, int damageType)
        {
            SourcePlayerId = sourcePlayerId;
            SourceId = sourceId;
            SourceTableId = sourceTableId;
            WeaponTableId = weaponTableId;
            DamageType = damageType;
        }
    }
    // 自身死亡
    [Event]
    public class SelfDieEvent
    {
        public readonly long DeadPlayerId;
        public readonly DamageData DamageData;
        public readonly long KillerPlayerId;
        public SelfDieEvent(long deadPlayerId, DamageData damageData, long killerPlayerId)
        {
            DeadPlayerId = deadPlayerId;
            DamageData = damageData;
            KillerPlayerId = killerPlayerId;
        }
    }

    /// <summary>
    /// 自己倒地
    /// </summary>
    [Event]
    public class SelfDownEvent
    {
        public readonly long DownPlayerId;
        public readonly DamageData DamageData;
        public SelfDownEvent(long downPlayerId, DamageData damageData)
        {
            DownPlayerId = downPlayerId;
            DamageData = damageData;
        }
    }


    // 角色死亡
    [Event]
    public class PlayerDieEvent
    {
        public ulong RoleId;
        public ulong KillerRoleId;
        public long KillerPlayerId;
    }

    // 角色起身
    [Event]
    public class PlayerGetUpEvent
    {
        public ulong RoleId;
    }

    // 角色重生
    [Event]
    public class PlayerRebornEvent
    {
        public ulong RoleId;
    }

    [Event]
    public class PlayerLoginEvent
    {
        public ulong RoleId;
        public bool IsFirst;
    }

    [Event]
    public class GameStartEvent
    {
    }

    [Event]
    public class GraphStartEvent
    {
    }

    // 用于测试蓝图节点的事件 用完删
    [Event]
    public class NodeTestEvent { }
    /// <summary>
    /// 抄家保护状态变化
    /// </summary>
    [Event]
    public class RaidTimeChangeEvent
    {
        public bool IsRaidTime;
    }

    [Event]
    public class PlayerBotSpawn
    {
        public static PlayerBotSpawn Instance = new();
    }

    [Event]
    public class StartStoryStageEvent
    {
        public static StartStoryStageEvent Instance = new();
    }

    // 玩家进入遗迹
    [Event]
    public class PlayerEnterMonumentEvent
    {
        public ulong RoleId;
    }

    // 玩家退出遗迹遗迹
    [Event]
    public class PlayerLeaveMonumentEvent
    {
        public ulong RoleId;
        public int MonumentId;
    }

    // 玩家恢复play
    [Event]
    public class ClientResumedControlEvent
    {
        public static ClientResumedControlEvent Instance = new();
    }

    // 玩家挂机
    [Event]
    public class ClientPauseControlEvent
    {
        public static ClientPauseControlEvent Instance = new();
    }

    /// <summary>
    /// Poi == position of interest
    /// </summary>
    [Event]
    public class PoiTeamKillEvent : IEventWithBizId
    {
        public enum EType
        {
            Patrol,
            Boyband,
            Gunship,
        }
        public PoiTeamKillEvent(long templateId, EType type)
        {
            TemplateId = templateId;
            Type = type;
        }
        public long TemplateId { get; private set; }
        public EType Type { get; private set; }

        public long BizId => TemplateId;
    }

    [Event]
    public class VisitShopEvent : IEventWithBizId
    {
        public VisitShopEvent(long templateId)
        {
            TemplateId = templateId;
        }
        public long TemplateId { get; private set; }

        public long BizId => TemplateId;
    }

    [Event]
    public class CompleteInteractionEvent : IEventWithBizId
    {
        public CompleteInteractionEvent(long bizId, int incrementCount)
        {
            BizId = bizId;
            IncrementCount = incrementCount;
        }
        public long BizId { get; private set; }
        public int IncrementCount { get; private set; }
    }

    [Event]
    public class CompleteMiniGameEvent : IEventWithBizId
    {
        public CompleteMiniGameEvent(long bizId, int incrementCount)
        {
            BizId = bizId;
            IncrementCount = incrementCount;
        }
        public long BizId { get; private set; }
        public int IncrementCount { get; private set; }
    }

    [Event]
    public class EnterAreaBoxEvent : IEventWithBizId
    {
        public EnterAreaBoxEvent(long bizId, int incrementCount)
        {
            BizId = bizId;
            IncrementCount = incrementCount;
        }
        public long BizId { get; private set; }
        public int IncrementCount { get; private set; }
    }

    [Event]
    public class PlayerStartLootingEvent
    {
        public long EntityId { get; private set; }
        public long TemplateId { get; private set; }
        public PlayerStartLootingEvent(long entityId, long templateId)
        {
            EntityId = entityId;
            TemplateId = templateId;
        }
    }

    [Event]
    public class PlayerLootingBoxEvent : IEventWithBizId, IEventWithCount, IEventWithEntityId, IEventWithTargetEntity
    {
        public long BizId { get; private set; }
        public int Count { get; private set; }
        public long EntityId { get; private set; }
        public long TargetEntityId { get; private set; }
        public bool HasLootedBefore { get; private set; }
        public bool IsCorpseBox { get; private set; }

        public long PlayerId { get; private set; }

        public PlayerLootingBoxEvent(long bizId, int count, long entityId, long targetEntityId, bool hasLootedBefore, bool isCorpseBox, long playerId)
        {
            BizId = bizId;
            Count = count;
            EntityId = entityId;
            TargetEntityId = targetEntityId;
            HasLootedBefore = hasLootedBefore;
            IsCorpseBox = isCorpseBox;
            PlayerId = playerId;
        }
    }

    [Event]
    public class PlayerStopLootingEvent
    {
        public static PlayerStopLootingEvent Instance { get; private set; } = new();
    }

    [Event]
    public class PickSoundEvent
    {
        public PickAndDropSuccessEvent PickEvent { get; private set; }
        public bool ExceptSelf { get; private set; }

        public PickSoundEvent(PickAndDropSuccessEvent pickEvent, bool exceptSelf)
        {
            PickEvent = pickEvent;
            ExceptSelf = exceptSelf;
        }
    }

    [Event]
    public class PlayerWeaponChangeEvent(ulong roleId)
    {
        public ulong RoleId { get; private set; } = roleId;
    }

    [Event]
    public class PlayerEquipmentChangeEvent(ulong roleId)
    {
        public ulong RoleId { get; private set; } = roleId;
    }

    [Event]
    public class TrainUnloadDoneEvent
    {
        public static readonly TrainUnloadDoneEvent Instance = new();
    }

    [Event]
    public class TrainUnloadInterruptEvent
    {
        public static readonly TrainUnloadInterruptEvent Instance = new();
    }

    [Event]
    public class CorpseCreatedEvent
    {
        public long CorpseEntityId;
        public long PoiLinkedTaskId;
        public bool IsTriggeredByTrap;

        public CorpseCreatedEvent(long entityId, long taskId, bool isTriggeredByTrap)
        {
            CorpseEntityId = entityId;
            PoiLinkedTaskId = taskId;
            IsTriggeredByTrap = isTriggeredByTrap;
        }
    }

    [Event]
    public class GraphCustomEvent
    {
        public string eventName;
        public long entityId;
        public ulong roleId;
        public CustomTypeList<CustomEventParam> ParamList;

        public GraphCustomEvent(string eventName, long entityId, ulong roleId, CustomTypeList<CustomEventParam> paramList)
        {
            this.eventName = eventName;
            this.entityId = entityId;
            this.roleId = roleId;
            ParamList = paramList;
        }
    }

    [Event]
    public class TaskFinishEvent : IEventWithBizId
    {
        public TaskFinishEvent(long bizId)
        {
            BizId = bizId;
        }
        public long BizId { get; private set; }
    }

    [Event]
    public class PlayerOfflineEvent
    {
        public ulong RoleId;
    }

    [Event]
    public class DisassembleItemEvent : IEventWithBizId, IEventWithCount
    {
        public long BizId { get; private set; }

        public int Count { get; private set; }

        public DisassembleItemEvent(long bizId, int count)
        {
            BizId = bizId;
            Count = count;
        }
    }

    [Event]
    public class PlayerCreateTeamEvent
    {
        public ulong RoleId;
        public PlayerCreateTeamEvent(ulong roleId)
        {
            RoleId = roleId;
        }
    }

    [Event]
    public class JoinTeamWithRoleId
    {
        public readonly ulong JoinRoleId;
        public JoinTeamWithRoleId(ulong roleId)
        {
            JoinRoleId = roleId;
        }
    }

    [Event]
    public class JoinTeamWithPlayerEntity
    {
        public readonly PlayerEntity JoinPlayer;
        public JoinTeamWithPlayerEntity(PlayerEntity player)
        {
            JoinPlayer = player;
        }
    }

    [Event]
    public class LeaveTeam
    {
        public readonly ulong RoleId;

        public LeaveTeam(ulong roleId)
        {
            RoleId = roleId;
        }
    }

    [Event]
    public class BeforeJoinTeam
    {
        public readonly long TeamId;
        public BeforeJoinTeam(long teamId)
        {
            TeamId = teamId;
        }
    }

    [Event]
    public class BuffChangeEvent
    {
        public readonly BuffType ChangedBuffType;
        public readonly int OldBuffTenThousandths;
        public readonly int OldBuffAbsoluteValue;
        public readonly int BuffTenThousandths;
        public readonly int BuffAbsoluteValue;

        public BuffChangeEvent(BuffType changedBuffType, int oldBuffTenThousandths, int oldBuffAbsoluteValue, int buffTenThousandths, int buffAbsoluteValue)
        {
            ChangedBuffType = changedBuffType;
            OldBuffTenThousandths = oldBuffTenThousandths;
            OldBuffAbsoluteValue = oldBuffAbsoluteValue;
            BuffTenThousandths = buffTenThousandths;
            BuffAbsoluteValue = buffAbsoluteValue;
        }
    }

    [Event]
    public class LobbyTaskLoadSuccessEvent
    {
        public static readonly LobbyTaskLoadSuccessEvent Instance = new();
    }

    [Event]
    public class BeforeLeaveTeam
    {
        public readonly long TeamId;
        public BeforeLeaveTeam(long teamId)
        {
            TeamId = teamId;
        }
    }

    [Event]
    public class PlayerWoundedEvent
    {
        public readonly ulong RoleId;
        public PlayerWoundedEvent(ulong roleId)
        {
            RoleId = roleId;
        }
    }

    [Event]
    public class PlayerRescuedEvent
    {
        public readonly ulong RescuedRoleId;
        public readonly ulong RescuerRoleId;
        public PlayerRescuedEvent(ulong rescuedRoleId, ulong rescuerRoleId)
        {
            RescuedRoleId = rescuedRoleId;
            RescuerRoleId = rescuerRoleId;
        }
    }

    [Event]
    public class TriggerRegionEnterEvent
    {
        public readonly long TriggerEntityId;
        public readonly long OwnerEntityId;

        public TriggerRegionEnterEvent(long triggerEntityId, long ownerEntityId)
        {
            TriggerEntityId = triggerEntityId;
            OwnerEntityId = ownerEntityId;
        }
    }

    [Event]
    public class TriggerRegionExitEvent
    {
        public readonly long TriggerEntityId;
        public readonly long OwnerEntityId;

        public TriggerRegionExitEvent(long triggerEntityId, long ownerEntityId)
        {
            TriggerEntityId = triggerEntityId;
            OwnerEntityId = ownerEntityId;
        }
    }

    [Event]
    public class TriggerRegionStayEvent
    {
        public readonly long TriggerEntityId;

        public TriggerRegionStayEvent(long triggerEntityId)
        {
            TriggerEntityId = triggerEntityId;
        }
    }

    /// <summary>
    /// 击杀事件
    /// target 不一定是 PlayerEntity
    /// </summary>
    [Event]
    public class KillEvent
    {
        public KillEvent(DamageInstance data)
        {
            Data = data;
        }

        public DamageInstance Data;
    }

    [Event]
    public class BoxDestroyEvent
    {
        public long BoxEntityId;
        public long TemplateId;
        public Vector3 Pos;

        public BoxDestroyEvent(long boxEntityId, long templateId, Vector3 pos)
        {
            BoxEntityId = boxEntityId;
            TemplateId = templateId;
            Pos = pos;
        }
    }

    [Event]
    public class PlayerResetBoxGameEvent
    {
        public static PlayerResetBoxGameEvent Instance = new();
    }

    [Event]
    public class PlayerTeamUpEvent
    {
        public static PlayerTeamUpEvent Instance = new();
    }

    [Event]
    public class GrowCropsEvent : IEventWithBizId, IEventWithCount
    {
        public GrowCropsEvent(long bizId, int count)
        {
            BizId = bizId;
            Count = count;
        }

        public long BizId { get; private set; }
        public int Count { get; private set; }
    }

    [Event]
    public class ElectricConnectionEvent : IEventWithBizId
    {
        public ElectricConnectionEvent(long inputTempalateId, long outputTempalateId)
        {
            BizId = inputTempalateId;
            OutputTempalateId = outputTempalateId;
        }

        public long BizId { get; private set; }
        public long OutputTempalateId { get; private set; }
    }

    [Event]
    public class PlayerProtectionChangedEvent
    {
        public static PlayerProtectionChangedEvent Instance = new();
    }

    [Event]
    public class CallElevatorEvent
    {
        public static CallElevatorEvent Instance = new();
    }

    [Event]
    public class GestureEvent : IEventWithBizId
    {
        public GestureEvent(long bizId)
        {
            BizId = bizId;
        }

        public long BizId { get; private set; }
    }

    [Event]
    public class TeamInviteEvent
    {
        public TeamInviteEvent(bool isFromGesture)
        {
            IsFromGesture = isFromGesture;
        }

        public bool IsFromGesture { get; private set; }
    }

    [Event]
    public class ShareTechnologyEvent
    {
        public static ShareTechnologyEvent Instance = new();
    }

    [Event]
    public class PlayerAcceptTaskEvent
    {
        public ulong RoleId { get; private set; }
        public long TaskId { get; private set; }
        public PlayerAcceptTaskEvent(ulong roleId, long taskId)
        {
            RoleId = roleId;
            TaskId = taskId;
        }
    }

    [Event]
    public class PlayerCompleteGameEvent
    {
        public long GameModeId { get; private set; }
        public bool IsWin { get; private set; }
        public long CampId { get; private set; }

        public PlayerCompleteGameEvent(long gameModeId, bool isWin, long campId)
        {
            GameModeId = gameModeId;
            IsWin = isWin;
            CampId = campId;
        }
    }

    [Event]
    public class CompleteRescueEvent
    {
        public long TargetEntityId { get; private set; }

        public CompleteRescueEvent(long targetEntityId)
        {
            TargetEntityId = targetEntityId;
        }
    }

    [Event]
    public class RepairDebrisEvent : IEventWithBizId
    {
        public RepairDebrisEvent(long bizId)
        {
            BizId = bizId;
        }

        public long BizId { get; private set; }
    }

    [Event]
    public class OccupyTerritoryEvent : IEventWithBizId
    {
        public OccupyTerritoryEvent(long bizId)
        {
            BizId = bizId;
        }

        public long BizId { get; private set; }
    }

    [Event]
    public class GatherItemEvent : IEventWithBizId
    {
        public GatherItemEvent(long bizId, int count)
        {
            BizId = bizId;
            Count = count;
        }

        public long BizId { get; private set; }
        public readonly int Count;

    }

    /// <summary>
    /// 完成声望转换事件
    /// </summary>
    [Event]
    public class FinishConvertReputationEvent : IEventWithBizId, IEventWithCount
    {
        public FinishConvertReputationEvent(long bizId, int converOutputNum)
        {
            BizId = bizId;
            ConverOutputNum = converOutputNum;
        }

        public long BizId { get; private set; }
        public int ConverOutputNum { get; private set; }
        public int Count => ConverOutputNum;
    }

    /// <summary>
    /// 摆放领地柜事件
    /// </summary>
    [Event]
    public class PlaceTerritoryCabinetEvent
    {
        public static PlaceTerritoryCabinetEvent Instance = new();
    }

    /// <summary>
    /// 打开箱子事件
    /// </summary>
    [Event]
    public class OpenBoxEvent
    {
        public long BizId { get; private set; }

        public OpenBoxEvent(long bizId)
        {
            BizId = bizId;
        }
    }


    /// <summary>
    /// 保存蓝图事件
    /// </summary>
    [Event]
    public class SaveBlueprintEvent
    {
        public static SaveBlueprintEvent Instance = new();
    }

    [Event]
    public class ConstructUseBlueprintEvent
    {
        public static ConstructUseBlueprintEvent Instance = new();
    }

    /// <summary>
    /// 购买商品事件
    /// </summary>
    [Event]
    public class PurchaseGoodsEvent : IEventWithBizId, IEventWithCount
    {
        public PurchaseGoodsEvent(long bizId, int count)
        {
            BizId = bizId;
            Count = count;
        }

        public long BizId { get; private set; }
        public int Count { get; private set; }
    }



    /// <summary>
    /// 用水瓶接水事件
    /// </summary>
    [Event]
    public class CollectWaterEvent(long bizId, int count) : IEventWithBizId, IEventWithCount
    {
        public long BizId { get; private set; } = bizId;
        public int Count { get; private set; } = count;
    }
}
