using CommonUnity.Runtime.Animation;
using System;
using System.Collections.Generic;
using UnityEngine;
using Utilities;
using WizardGames.Soc.Common.Const;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data.DataItem;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Unity.Animation;
using WizardGames.Soc.Common.Unity;
using WizardGames.Soc.Common.Unity.Combat;
using WizardGames.Soc.Common.Unity.Extend;
using WizardGames.Soc.Common.Unity.Main;
using WizardGames.Soc.Common.Unity.Manager;
using WizardGames.Soc.Common.Unity.Scene;
using WizardGames.Soc.Common.Unity.Utility;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Weapon;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocClient.Main;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Systems;
using WizardGames.Soc.SocClient.Ui;
using Object = UnityEngine.Object;

namespace WizardGames.Soc.SocClient.GoLoader
{
    public class WeaponData : IWeaponData
    {
        public long EntityId => entityId;
        public long TableId => data.TableId;
        public long SkinId => data.SkinId;
        public bool IsWeapon => true;
        public bool ListenAccessoryEvent => false;

        public long InitSkinId { get; set; }

        public int InitAccessoryCount { get; set; }

        private readonly WeaponDisplayData data;
        private readonly long entityId;

        public List<DisplayAccessory> accessories = new();

        public WeaponData(WeaponDisplayData data)
        {
            entityId = IdGenerator.GenLocalUniqueId();
            this.data = data;
            if (data.AccessoryDisplayDatas != null)
            {
                for (int i = 0; i < data.AccessoryDisplayDatas.Count; i++)
                {
                    var accessory = data.AccessoryDisplayDatas[i];
                    accessories.Add(new DisplayAccessory(this, accessory.TableId, accessory.SkinId));
                }
            }
            

            InitAccessoryCount = accessories.Count;
            InitSkinId = data.SkinId;
        }

        public void TraverseAccessory(Action<long, long, long, long, long> traverse)
        {
            if (traverse == null)
                return;
            if (!IsWeapon)
                return;

            foreach (var accessory in accessories)
            {
                if (accessory != null)
                    traverse(accessory.EntityId, accessory.TableId, accessory.SkinId, accessory.OwnerWeaponEntityId,
                        accessory.OwnerWeaponTableId);
            }
        }
    }

    /// <summary>
    /// 无entity的player数据，用到什么数据加什么数据
    /// </summary>
    public class PlayerData : IPlayerData
    {
        public long PlayerEntityId => 0;
        public long HoldWeaponId => 0;
        public bool ShowCurrentWeaponOnBack => true;

        private List<long> parts = new();

        private Dictionary<long, long> skinsCache = new();

        private readonly PlayerDisplayData displayData;

        //最好搞成结构体，然后多态部分放方法里，不要接口了
        private IWeaponData[] first2Weapons = new IWeaponData[2];

        public PlayerData(PlayerDisplayData DisplayData)
        {
            displayData = DisplayData;
        }

        public PlayerData()
        {
            displayData = new PlayerDisplayData();
        }

        //尽可能只检查skinid，skinid变化没有property callback，自己需要判断
        public bool ShouldUpdateSkin()
        {
            if (displayData == null)
            {
                return false;
            }

            foreach (var weaponData in displayData.WeaponDisplayDatas)
            {
                var tb = Mc.Tables.TbItemConfig.GetOrDefault(weaponData.TableId);
                if (tb.Type != ItemEntityType.Weapon)
                    continue;
                var pos = weaponData.HoldItemIndex == (int)HoldItemIndex.Item7 ? 0 : 1;

                //既得检查武器是否变化了，还得检查配件是否变化了
                ref var cur_weapon_data = ref first2Weapons[pos];
                if (cur_weapon_data == null)
                {
                    continue;
                }

                if (cur_weapon_data is WeaponData cur_pos_weapon_data)
                {
                    //初始化皮肤和现在皮肤不一样，就要更新
                    if (cur_pos_weapon_data.InitSkinId != weaponData.SkinId)
                    {
                        return true;
                    }

                    //配件数量变化了，就要更新
                    if (weaponData.AccessoryDisplayDatas != null && cur_pos_weapon_data.InitAccessoryCount != weaponData.AccessoryDisplayDatas.Count)
                    {
                        return true;
                    }

                    //检查配件皮肤更新
                    // for (int i = weaponData.AccessoryTableIds.Count - 1; i >= 0; i--)
                    // {
                    //     var accessory_tb_id = weaponData.AccessoryTableIds[i];
                    // }
                }
            }

            return false;
        }

        public int Sex => displayData.Sex;
        public int FaceId => displayData.FaceId;
        public int HairId => displayData.HairId;
        public int HairColorId => displayData.HairColorId;
        public bool PlayStartAnim => displayData.PlayStartAnim;
        public bool PlaySpecialIdleAnim => displayData.PlaySpecialIdleAnim;

        public IWeaponData[] GetFirst2Weapons()
        {
            //更快
            first2Weapons[0] = null;
            first2Weapons[1] = null;
            if (displayData != null && displayData.WeaponDisplayDatas != null)
            {
                // Array.Clear(first2Weapons, 0, first2Weapons.Length);
                foreach (var weaponData in displayData.WeaponDisplayDatas)
                {
                    var tb = Mc.Tables.TbItemConfig.GetOrDefault(weaponData.TableId);
                    if (tb.Type != ItemEntityType.Weapon)
                        continue;
                    var pos = weaponData.HoldItemIndex == (int)HoldItemIndex.Item7 ? 0 : 1;
                    first2Weapons[pos] = new WeaponData(weaponData);
                }
            }

            return first2Weapons;
        }

        public List<long> AllParts
        {
            get
            {
                parts.Clear();
                if (displayData != null && displayData.EquipmentDisplayDatas != null)
                {
                    foreach (var data in displayData.EquipmentDisplayDatas)
                    {
                        parts.Add(data.TableId);
                    }
                }

                return parts;
            }
        }

        public Dictionary<long, long> AllSkins
        {
            get
            {
                skinsCache.Clear();
                if (displayData != null && displayData.EquipmentDisplayDatas != null)
                {
                    foreach (var data in displayData.EquipmentDisplayDatas)
                    {
                        if (data.SkinId > 0)
                            skinsCache[data.TableId] = data.SkinId;
                    }
                }

                return skinsCache;
            }
        }

        public void CopyFrom(PlayerDisplayData other)
        {
            displayData?.CopyFrom(other);
        }
    }

    /// <summary>
    /// 有entity的player数据
    /// </summary>
    public class PlayerEntityData : IPlayerData
    {
        private readonly PlayerEntity playerEntity;
        private IWeaponData[] first2Weapons = new IWeaponData[2];

        public long HoldWeaponId => playerEntity.CurrentWeaponId;
        public bool ShowCurrentWeaponOnBack { set; get; } = false;

        public long PlayerEntityId => playerEntity.EntityId;

        public int Sex => playerEntity.Sex;
        public int FaceId => playerEntity.FaceId;
        public int HairId => playerEntity.HairId;
        public int HairColorId => playerEntity.HairColorId;
        public bool PlayStartAnim => playerEntity.PlayStartAnim;
        public bool PlaySpecialIdleAnim => playerEntity.PlaySpecialIdleAnim;

        private List<long> parts = new();

        private Dictionary<long, long> skinsCache = new();

        public PlayerEntityData(PlayerEntity playerEntity)
        {
            this.playerEntity = playerEntity;
        }

        //尽可能只检查skinid，skinid变化没有property callback，自己需要判断
        public bool ShouldUpdateSkin()
        {
            if (playerEntity == null)
            {
                return false;
            }

            int count = 0;
            for (int i = (int)HoldItemIndex.Item7; i <= (int)HoldItemIndex.Item8; i++, count++)
            {
                var item = playerEntity.GetItemEntity(i);
                if (item == null)
                    continue;

                var tb = Mc.Tables.TbItemConfig.GetOrDefault(item.TableId);
                if (tb.Type != ItemEntityType.Weapon)
                    continue;
                //既得检查武器是否变化了，还得检查配件是否变化了
                ref var cur_weapon_data = ref first2Weapons[count];
                if (cur_weapon_data == null)
                {
                    continue;
                }

                if (cur_weapon_data is WeaponEntityData cur_pos_weapon_data)
                {
                    //初始化皮肤和现在皮肤不一样，就要更新
                    if (cur_pos_weapon_data.InitSkinId != item.SkinId)
                    {
                        return true;
                    }

                    if (item is WeaponCustom weaponEntity)
                    {
                        int InitAccessoryCount = 0;
                        if (weaponEntity.Accessory0 != null)
                        {
                            ++InitAccessoryCount;
                        }

                        if (weaponEntity.Accessory1 != null)
                        {
                            ++InitAccessoryCount;
                        }

                        if (weaponEntity.Accessory2 != null)
                        {
                            ++InitAccessoryCount;
                        }

                        if (weaponEntity.Accessory3 != null)
                        {
                            ++InitAccessoryCount;
                        }

                        //配件数量变化了，就要更新
                        if (cur_pos_weapon_data.InitAccessoryCount != InitAccessoryCount)
                        {
                            return true;
                        }
                    }

                    //检查配件皮肤更新
                    // for (int i = weaponData.AccessoryTableIds.Count - 1; i >= 0; i--)
                    // {
                    //     var accessory_tb_id = weaponData.AccessoryTableIds[i];
                    // }
                }
            }

            return false;
        }

        public IWeaponData[] GetFirst2Weapons()
        {
            //更快
            first2Weapons[0] = null;
            first2Weapons[1] = null;
            // Array.Clear(first2Weapons, 0, first2Weapons.Length);

            int count = 0;
            for (int i = (int)HoldItemIndex.Item7; i <= (int)HoldItemIndex.Item8; i++, count++)
            {
                var item = playerEntity.GetItemEntity(i);
                if (item == null)
                    continue;

                var tb = Mc.Tables.TbItemConfig.GetOrDefault(item.TableId);
                if (tb.Type != ItemEntityType.Weapon)
                    continue;

                if (item.EntityId == playerEntity.CurrentWeaponId && !ShowCurrentWeaponOnBack)
                    first2Weapons[count] = null;
                else
                {
                    first2Weapons[count] = new WeaponEntityData(item);
                }
            }

            return first2Weapons;
        }

        public List<long> AllParts
        {
            get
            {
                parts.Clear();
                playerEntity.GetWearItemTableIds(parts);
                ClientEquipUpdateSystem.HandleEquipShowOption(parts, playerEntity);
                return parts;
            }
        }

        public Dictionary<long, long> AllSkins
        {
            get
            {
                skinsCache.Clear();
                if (playerEntity != null)
                {
                    foreach (var wear_data in playerEntity.EnumWearedItem())
                    {
                        if (wear_data == null)
                        {
                            continue;
                        }

                        skinsCache.Add(wear_data.TableId, wear_data.SkinId);
                    }
                }

                return skinsCache;
            }
        }
    }

    public class WeaponEntityData : IWeaponData
    {
        public IItemEntity heldEntity;

        public long InitSkinId { get; set; }

        public int InitAccessoryCount { get; }

        public WeaponEntityData(IItemEntity heldEntity)
        {
            this.heldEntity = heldEntity;
            InitSkinId = heldEntity.SkinId;
            InitAccessoryCount = 0;

            if (heldEntity is WeaponCustom weaponEntity)
            {
                if (weaponEntity.Accessory0 != null)
                {
                    ++InitAccessoryCount;
                }

                if (weaponEntity.Accessory1 != null)
                {
                    ++InitAccessoryCount;
                }

                if (weaponEntity.Accessory2 != null)
                {
                    ++InitAccessoryCount;
                }

                if (weaponEntity.Accessory3 != null)
                {
                    ++InitAccessoryCount;
                }
            }
        }

        public long EntityId => heldEntity.EntityId;
        public long TableId => heldEntity.TableId;
        public long SkinId => heldEntity.SkinId;

        public bool IsWeapon => true; // 通过TableId判断

        public bool ListenAccessoryEvent => true;

        public void TraverseAccessory(Action<long, long, long, long, long> traverse)
        {
            if (traverse == null)
                return;
            if (!IsWeapon)
                return;
            var itor = ((WeaponCustom)heldEntity).EnumWeaponAccessoryId();
            foreach (var accessory in itor)
            {
                if (accessory != null)
                    traverse(accessory.EntityId, accessory.TableId, accessory.SkinId, accessory.OwnerWeaponEntityId,
                        accessory.OwnerWeaponTableId);
            }
        }
    }

    public class DisplayAccessory : IDisplayAccessoryData
    {
        public long EntityId => entityId;
        public long TableId => tableId;
        public long SkinId => skinId;
        public long OwnerWeaponEntityId => parent.EntityId;
        public long OwnerWeaponTableId => parent.TableId;

        private readonly WeaponData parent;
        private readonly long tableId;
        private readonly long entityId;
        private readonly long skinId;

        public DisplayAccessory(WeaponData parent, long tableId, long skinId)
        {
            entityId = IdGenerator.GenLocalUniqueId();
            this.parent = parent;
            this.tableId = tableId;
            this.skinId = skinId;
        }
    }

    public class DisplayAccessoryEntity : IDisplayAccessoryData
    {
        private readonly WeaponAccessoryItemCustom accessoryItemEntity;

        public DisplayAccessoryEntity(WeaponAccessoryItemCustom accessoryItemEntity)
        {
            this.accessoryItemEntity = accessoryItemEntity;
        }

        public long EntityId => accessoryItemEntity.EntityId;
        public long TableId => accessoryItemEntity.TableId;
        public long SkinId => accessoryItemEntity.SkinId;
        public long OwnerWeaponEntityId => accessoryItemEntity.OwnerWeaponEntityId;
        public long OwnerWeaponTableId => accessoryItemEntity.OwnerWeaponTableId;
    }

    public interface IDisplayAccessoryData
    {
        public long EntityId { get; }
        public long TableId { get; }
        public long SkinId { get; }
        public long OwnerWeaponEntityId { get; }
        public long OwnerWeaponTableId { get; }
    }

    public interface IWeaponData
    {
        public long EntityId { get; }
        public long TableId { get; }
        public long SkinId { get; }
        public long InitSkinId { get; }
        public bool IsWeapon { get; }
        public bool ListenAccessoryEvent { get; }
        public void TraverseAccessory(Action<long, long, long, long, long> traverse);
    }

    public interface IPlayerData
    {
        public long PlayerEntityId { get; }

        public long HoldWeaponId { get; }
        public bool ShowCurrentWeaponOnBack { get; }

        public IWeaponData[] GetFirst2Weapons();

        public List<long> AllParts { get; }

        public Dictionary<long, long> AllSkins { get; }

        public bool ShouldUpdateSkin();

        public int Sex { get; }
        public int FaceId { get; }
        public int HairId { get; }
        public int HairColorId { get; }
        public bool PlayStartAnim { get; }
        public bool PlaySpecialIdleAnim { get; }
    }

    /// <summary>
    /// 用与展示的人物外观，不依赖entity
    /// </summary>
    public class DisplayModel
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(DisplayModel));
        private IPlayerData playerData;
        private BackWeaponMgr backWeaponMgr;
        private PartModelController partModelController;
        private readonly int layerIndex;
        public readonly ObjectPointComponent ObjPlayerModelPoint;
        public Animator ModelAnimator { get; private set; }
        private LobbyClientAnimReceiver lobbyClientAnimReceiver;

        private PlayerCustomizeData customizeData;
        public ModelType ModelType { get; private set; }
        private bool isPlaySuitAnimClip;
        private Transform cameraLocator;
        public string IdleAnimPath;
        public string StartAnimPath;
        public bool NeedReloadAnimClip = true;

        private DisplayWeapon handWeapon;
        private DisplayWeaponIK displayWeaponIK;
        //有新动画时，总是打断当前动画
        public bool alwaysBreakAnim = false;

        public DisplayModel(IPlayerData playerData, ObjectPointComponent objPlayerModelPoint, ModelType modelType,
            int layerIndex, bool isMaster = false)
        {
            this.playerData = playerData;
            this.ObjPlayerModelPoint = objPlayerModelPoint;
            this.layerIndex = layerIndex;
            this.ModelType = modelType;
            customizeData = new PlayerCustomizeData(playerData);
            partModelController = new PartModelController(playerData.PlayerEntityId, objPlayerModelPoint.gameObject,
                objPlayerModelPoint, modelType, layerIndex, isMaster, EquipOwner.HumanOrCorpse, customizeData);
            backWeaponMgr = new BackWeaponMgr(partModelController, playerData, objPlayerModelPoint, layerIndex);
            if (modelType == ModelType.FP)
            {
                objPlayerModelPoint.TryGetChildPoint(SocConstantName.FpBone_CameraLocator, out var child);
                cameraLocator = child;
            }

            // if (modelType != ModelType.FP)
            // {
            //     DefaultIdleAnimPath = Mc.Tables.TbGlobalConfig.CostumeDefaultIdleAnim;
            // }
        }

        public void AlignCameraLocator(Transform camera)
        {
            if (camera == null || cameraLocator == null)
            {
                return;
            }
            
            var inv = UnityUtility.GetSourceInvMatrix(cameraLocator, ObjPlayerModelPoint);
            ObjPlayerModelPoint.transform.position = camera.position + inv.GetPosition();
            ObjPlayerModelPoint.transform.rotation = camera.rotation * inv.rotation;
        }

        public void PlayWeaponAnim(string animPath, float normalizedTime = 0)
        {
            if (handWeapon == null || string.IsNullOrEmpty(animPath))
            {
                return;
            }
            
            handWeapon.PlayAnim(animPath, normalizedTime);
        }
        
        public bool PlayAnim(string animPath, int animLayerIndex = 0, string placeholderName = "team_start_placeholder", string stateName = "start")
        {
            if (string.IsNullOrEmpty(animPath))
            {
                return false;
            }
            
            if (ModelAnimator.runtimeAnimatorController is not AnimatorOverrideController animatorOverrideController)
            {
                return false;
            }

            // 避免重复播放相同的片段
            var clipName = animPath.Split('/')[^1].Split('.')[0];
            var oldClip = animatorOverrideController[placeholderName];
            if (oldClip != null && oldClip.name == clipName)
            {
                AnimatorStateInfo stateInfo = ModelAnimator.GetCurrentAnimatorStateInfo(animLayerIndex);
                if (stateInfo.IsName(stateName) == false|| alwaysBreakAnim)
                {
                    ModelAnimator.Play(stateName, animLayerIndex, 0);
                    ModelAnimator.Update(0);
                    return true;
                }
                return false;
            }
            
            var newClip = UtilsLoad.LoadAssetSync<AnimationClip>(animPath);
            if (newClip != null)
            {
                lobbyClientAnimReceiver?.TriggerInterruptEvent(oldClip);
                animatorOverrideController[placeholderName] = newClip;
                if (ModelAnimator.gameObject != null)
                {
                    ModelAnimator.gameObject.SaveNonInstanceAsset(newClip);
                }

                if (string.IsNullOrEmpty(stateName) == false)
                {
                    ModelAnimator.Play(stateName, animLayerIndex, 0);
                }
                ModelAnimator.Update(0);
                return true;
            }

            return false;
        }

        public void TriggerInterruptEvent(string placeholderName = "team_start_placeholder")
        {
            if (ModelAnimator.runtimeAnimatorController is not AnimatorOverrideController animatorOverrideController)
            {
                return;
            }
            
            var oldClip = animatorOverrideController[placeholderName];

            if (oldClip != null)
            {
                lobbyClientAnimReceiver?.TriggerInterruptEvent(oldClip);
            }
        }

        private void TryLoadFaceAnim()
        {
            if (ModelType != ModelType.FP)
            {
                var faceData = McCommonUnity.Tables.TbBareFace.GetOrDefault(customizeData.FaceId);
                if (faceData == null)
                {
                    return;
                }

                PlayAnim(faceData.LbAnim, 1, "idle_with_face_placeholder", "");
            }
        }
        
        public void RefreshCustomize()
        {
            if (IsAllPartsLoaded() == false)
            {
                return;
            }
            
            customizeData = new PlayerCustomizeData(playerData);
            var changeSex = partModelController.Sex != customizeData.Sex;
            partModelController.SetCustomizeData(customizeData);
            TryLoadFaceAnim();
            if (changeSex)
            {
                ReloadAnimClipAndPlay();
            }
        }
        
        public void AddOnPartModelLoadFinished(Action OnPartModelLoadFinished)
        {
            if (partModelController != null)
            {
                partModelController.OnPartModelLoadFinished -= OnPartModelLoadFinished;
                partModelController.OnPartModelLoadFinished += OnPartModelLoadFinished;
            }
        }

        public void RemoveOnPartModelLoadFinished(Action OnPartModelLoadFinished)
        {
            if (partModelController != null)
            {
                partModelController.OnPartModelLoadFinished -= OnPartModelLoadFinished;
            }
        }
        
        public void CreateAnimator(string controllerPath, bool isPlaySuitAnimClip = false)
        {
            this.isPlaySuitAnimClip = isPlaySuitAnimClip;
            
            var characterTb = McCommonUnity.Tables.TbChracterParameter;
            if (ObjPlayerModelPoint.gameObject.TryGetComponent<Animator>(out var animator))
            {
                ModelAnimator = animator;
            }
            else
            {
                ModelAnimator = ObjPlayerModelPoint.gameObject.AddComponent<Animator>();
            }
            ModelAnimator.logWarnings = false;
            ModelAnimator.cullingMode = AnimatorCullingMode.AlwaysAnimate;

            SocClientAnimUtility.SetAnimatorType(ModelAnimator, AnimatorCountType.Animator_Player);
            var avatarPath = ModelType != ModelType.FP ? characterTb.AvatarTpPath : characterTb.AvatarFpPath;
            var avatarTmp = UtilsLoad.LoadAssetSync<Avatar>(avatarPath);
            if (null != avatarTmp)
            {
                ModelAnimator.avatar = avatarTmp;
                if (null != ModelAnimator.gameObject)
                {
                    ModelAnimator.gameObject.SaveNonInstanceAsset(avatarTmp);
                }
            }
            var controllerTmp = UtilsLoad.LoadAssetSync<RuntimeAnimatorController>(controllerPath);
            if (null != controllerTmp)
            {
                if (ModelAnimator.runtimeAnimatorController == controllerTmp)
                {
                    GameObject.Destroy(ModelAnimator.runtimeAnimatorController);
                }
                // 直接加载资源引用的是同一份，所以替换片段会导致所有用这个状态机的人物都受到影响
                controllerTmp = Object.Instantiate(controllerTmp);
                ModelAnimator.runtimeAnimatorController = controllerTmp;
                if (null != ModelAnimator.gameObject)
                {
                    ModelAnimator.gameObject.SaveNonInstanceAsset(controllerTmp);
                }
            }
            bool isInGame = false;
            if (McCommonUnity.Scene != null && McCommonUnity.Scene.CurScene == SceneName.GameSpace)
            {
                isInGame = true;
            }
            if (!isInGame && ModelAnimator != null)
            {
                lobbyClientAnimReceiver = ModelAnimator.GetComponent<LobbyClientAnimReceiver>();
                if (lobbyClientAnimReceiver == null && null != ModelAnimator.gameObject)
                {
                    lobbyClientAnimReceiver = ModelAnimator.gameObject.AddComponent<LobbyClientAnimReceiver>();
                }
                lobbyClientAnimReceiver.fp = ModelType == ModelType.FP;
            }
            
            // ReloadAnimClipAndPlay();
            TryLoadFaceAnim();
        }
        
        public GameObject GetModelPoint()
        {
            return ObjPlayerModelPoint.gameObject;
        }

        /// <summary>
        /// 历史战局用，不用进场动作
        /// </summary>
        public void SetAnimIdle()
        {
            ModelAnimator.CrossFadeInFixedTime("idle", 0, 0);
        }

        public void SetParent(Transform parentTrans)
        {
            var transform = ObjPlayerModelPoint.transform;
            transform.SetParent(parentTrans);
            transform.localPosition = Vector3.zero;
            transform.localRotation = new Quaternion(0, 0, 0, 0);
        }

        public void SetLocalPosition(Vector3 pos)
        {
            ObjPlayerModelPoint.transform.localPosition = pos;
        }
        
        public void SetLocalRotation(Quaternion quaternion)
        {
            ObjPlayerModelPoint.transform.localRotation = quaternion;
        }

        public bool IsAllPartsLoaded()
        {
            return partModelController.IsAllPartsLoaded();
        }

        public void ResetAnimator()
        {
            if (ModelAnimator == null)
            {
                return;
            }

            ModelAnimator.cullingMode = AnimatorCullingMode.AlwaysAnimate;
            ModelAnimator.cullingMode = AnimatorCullingMode.CullUpdateTransforms;
        }

        public void SetActive(bool isShow)
        {
            if (ObjPlayerModelPoint == null)
            {
                return;
            }

            ObjPlayerModelPoint.gameObject.SetActive(isShow);
        }

        public void Show(bool show)
        {
            partModelController.Show(show);
        }

        private void ReloadAnimClipAndPlay()
        {
            if (NeedReloadAnimClip == false)
            {
                return;
            }
            
            string startAnimPath = "";
            string idleAnimPath = "";
            AnimationClip startAnim = null;
            AnimationClip idleAnim = null;
            AnimatorOverrideController animatorOverrideController =
                ModelAnimator.runtimeAnimatorController as AnimatorOverrideController;

            if (animatorOverrideController == null)
            {
                return;
            }
            
            if (isPlaySuitAnimClip)
            {
                var suitId = Mc.LobbySkin.GetWearSuitId(PartModelController.GetFilteredEquips(EquipOwner.HumanOrCorpse, playerData.AllParts, out var _));
                if (suitId > 0)
                {
                    var config = Mc.Tables.TbCostumeSuitAnim.GetOrDefault(suitId);
                    if (config != null)
                    {
                        if (playerData.PlayStartAnim)
                        {
                            startAnimPath = config.StartAnim;
                        }

                        if (playerData.PlaySpecialIdleAnim)
                        {
                            idleAnimPath = config.IdleAnim;
                        }
                    }
                }
            }
            
            if (string.IsNullOrEmpty(idleAnimPath))
            {
                if (string.IsNullOrEmpty(IdleAnimPath))
                {
                    idleAnimPath = customizeData.Sex == 0
                        ? Mc.Tables.TbGlobalConfig.CostumeDefaultIdleAnim
                        : Mc.Tables.TbGlobalConfig.CostumeFemaleDefaultIdleAnim;
                }
                else
                {
                    idleAnimPath = IdleAnimPath;
                }
            }
            if (!string.IsNullOrEmpty(idleAnimPath))
            {
                idleAnim = UtilsLoad.LoadAssetSync<AnimationClip>(idleAnimPath);
            }

            if (string.IsNullOrEmpty(startAnimPath) && string.IsNullOrEmpty(StartAnimPath) == false)
            {
                startAnimPath = StartAnimPath;
            }
            if (!string.IsNullOrEmpty(startAnimPath))
            {
                startAnim = UtilsLoad.LoadAssetSync<AnimationClip>(startAnimPath);
            }
            
            var playStateName = startAnim != null ? "start" : "idle";
            TryReplaceAnimClip("team_start_placeholder", "start", playStateName, 0, startAnim, animatorOverrideController);
            TryReplaceAnimClip("team_idle_placeholder", "idle", playStateName, 0, idleAnim, animatorOverrideController);
        }
        
        private bool TryReplaceAnimClip(string placeholderName, string stateName, string playStateName, int animLayerIndex, AnimationClip newClip, AnimatorOverrideController animatorOverrideController)
        {
            if (animatorOverrideController == null)
            {
                return false;
            }
            
            string newClipName = newClip?.name ?? "";
            var oldClip = animatorOverrideController[placeholderName];
            if (oldClip != null && oldClip.name == newClipName)
            {
                if (playStateName == stateName)
                {
                    AnimatorStateInfo stateInfo = ModelAnimator.GetCurrentAnimatorStateInfo(animLayerIndex);
                    if (alwaysBreakAnim || stateInfo.IsName(stateName) == false) // 避免重复播放相同的片段
                    {
                        ModelAnimator.Play(stateName, animLayerIndex, 0);
                        ModelAnimator.Update(0);
                    }
                }
                return false; // 避免重复替换相同的片段
            }
            
            lobbyClientAnimReceiver?.TriggerInterruptEvent(oldClip);
            animatorOverrideController[placeholderName] = newClip;
            if (playStateName == stateName)
            {
                ModelAnimator.Play(stateName, animLayerIndex, 0);
                ModelAnimator.Update(0);
            }
            return true;
        }
        
        public List<long> AllParts => playerData?.AllParts;
        
        public void UpdateAllPart()
        {
            ReloadAnimClipAndPlay();
            partModelController.UpdateAllPart(playerData.AllParts, playerData.AllSkins);
            // partModelController.SetRenderingLayerMask((uint)(1 << layerIndex));
            partModelController.SetGOLayer(layerIndex);
        }

        public void SetGoLayer(int layerIndex)
        {
            partModelController.SetGOLayer(layerIndex);
        }

        public void CheckBackWeapon()
        {
            backWeaponMgr?.CheckBackWeapon();
        }

        public void CopyFrom(PlayerDisplayData other)
        {
            if (playerData is PlayerData _playerData)
            {
                _playerData.CopyFrom(other);
            }
        }

        public DisplayWeapon AddHandWeapon(WeaponData weaponData, SkinWeaponDisplayAnim weaponDisplayAnim = null, Action<DisplayWeapon> afterGoLoadedHandler = null)
        {
            if (handWeapon != null)
            {
                handWeapon.Destroy();
                handWeapon = null;
            }
            
            if (weaponDisplayAnim != null)
            {
                IdleAnimPath = weaponDisplayAnim.TpIdleAnim;
                ReloadAnimClipAndPlay();
            }

            if (displayWeaponIK != null)
            {
                displayWeaponIK = null;
                SocClientApplicationLoop.LateUpdateAction -= UpdateWeaponIk;
            }
            
            handWeapon = new DisplayWeapon(partModelController, ObjPlayerModelPoint, weaponData, ShowEquipPos.Hand, true, true, afterGoLoadedHandler, weaponDisplayAnim);
            if (weaponDisplayAnim != null && weaponDisplayAnim.IKType != 3)
            {
                displayWeaponIK = new DisplayWeaponIK(this, ObjPlayerModelPoint, handWeapon, weaponDisplayAnim.IKType);
                SocClientApplicationLoop.LateUpdateAction -= UpdateWeaponIk;
                SocClientApplicationLoop.LateUpdateAction += UpdateWeaponIk;
            }

            return handWeapon;
        }

        public void UpdateWeaponIk()
        {
            if (handWeapon == null || displayWeaponIK == null)
            {
                return;
            }
            displayWeaponIK.LateUpdate();
        }
        
        public void TryRemoveHandWeapon(bool reloadAnim = false)
        {
            if (handWeapon != null)
            {
                handWeapon.Destroy();
                handWeapon = null;
                IdleAnimPath = null;
                if (reloadAnim)
                {
                    ReloadAnimClipAndPlay();
                }
            }

            if (displayWeaponIK != null)
            {
                displayWeaponIK = null;
                SocClientApplicationLoop.LateUpdateAction -= UpdateWeaponIk;
            }
        }

        private void TryTriggerInterruptEvents()
        {
            if (ModelAnimator == null || lobbyClientAnimReceiver == null 
            || ModelAnimator.runtimeAnimatorController is not AnimatorOverrideController animatorOverrideController)
            {
                return;
            }
            
            List<KeyValuePair<AnimationClip, AnimationClip>> overrides = new ();
            animatorOverrideController.GetOverrides(overrides);
            foreach (var pair in overrides)
            {
                lobbyClientAnimReceiver.TriggerInterruptEvent(pair.Value);
            }
        }
        
        public void Release()
        {
            TryTriggerInterruptEvents();
            TryRemoveHandWeapon();
            partModelController?.Release();
            partModelController = null;
            backWeaponMgr?.RemoveAllWeapons();
            backWeaponMgr = null;
            playerData = null;
            logger.Info("DisplayModel Release");
            if (ObjPlayerModelPoint != null && ObjPlayerModelPoint.gameObject != null)
            {
                logger.Info("DisplayModel objPlayerModelPoint destroy success");
                GameObject.Destroy(ObjPlayerModelPoint.gameObject);
            }
        }
    }
}