using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Event;
using WizardGames.Soc.SocWorld.Event;
using WizardGames.Soc.SocWorld.TestFramework;
using WizardGames.Soc.SocWorld.WorldCommon;

namespace WizardGames.Soc.Test
{
    public class TerritoryPlunderTest : TestCaseBase
    {
        internal TerritoryEntity territoryEntity;
        internal TerritoryPlunderComponent territoryPlunderComponent;
        internal PlayerEntity fightingPlayerEntity;
        internal PlayerEntity defenderPlayerEntity;

        public TerritoryPlunderTest()
        {
            Print($"***************** TerritoryPlunderTest constructor *****************");
            fightingPlayerEntity = new PlayerEntity();
            fightingPlayerEntity.LongRoleId = IdGeneratorUtil.GenInWorldFreqId();

            // Add base components
            fightingPlayerEntity.AddComponent(new RootNodeComponent()); // Required base component
            var teamComponent = new TeamComponent();
            teamComponent.Init(); // Initialize the component fields
            fightingPlayerEntity.AddComponent(teamComponent);
            fightingPlayerEntity.AddComponent(new ReputationComponent()); // Add required ReputationComponent
            fightingPlayerEntity.AddComponent(
                new PlayerInventoryComponent()); // Add PlayerInventoryComponent for GetEquipmentDisplayData
            fightingPlayerEntity.AddComponent(new PlayerLootingComponent()); // Add PlayerLootingComponent for looting

            fightingPlayerEntity.AddComponent(new PlayerPlunderComponent());
            AddEntityOnlyInitComponents(fightingPlayerEntity);
            UserManagerEntity.Instance.AddPlayerEntity(fightingPlayerEntity.RoleId, fightingPlayerEntity.EntityId);

            var fightingTeamEntity = new TeamEntity();
            AddEntityOnlyInitComponents(fightingTeamEntity);
            fightingTeamEntity.Init(); // Manually call Init to initialize properties
            fightingTeamEntity.AddMember(fightingPlayerEntity.RoleId, 0, 0);
            fightingPlayerEntity.ComponentTeam.TeamId = fightingTeamEntity.EntityId;

            defenderPlayerEntity = new PlayerEntity();
            defenderPlayerEntity.LongRoleId = IdGeneratorUtil.GenInWorldFreqId();

            // Add base components
            defenderPlayerEntity.AddComponent(new RootNodeComponent()); // Required base component
            var defenderTeamComponent = new TeamComponent();
            defenderTeamComponent.Init();
            defenderPlayerEntity.AddComponent(defenderTeamComponent);
            defenderPlayerEntity.AddComponent(new ReputationComponent());
            defenderPlayerEntity.AddComponent(new PlayerInventoryComponent());
            defenderPlayerEntity.AddComponent(new PlayerLootingComponent()); // Add PlayerLootingComponent for looting

            defenderPlayerEntity.AddComponent(new PlayerPlunderComponent());
            AddEntityOnlyInitComponents(defenderPlayerEntity);
            UserManagerEntity.Instance.AddPlayerEntity(defenderPlayerEntity.RoleId, defenderPlayerEntity.EntityId);

            var x = 10.0f + 1f;
            var y = 10.0f + 1f;
            var z = 10.0f + 1f;
            territoryEntity = TerritoryEntityHotfix.Create(IdGeneratorUtil.GenInWorldFreqId(), 0, x, y, z,
                defenderPlayerEntity.RoleId, 0, 0);
            territoryPlunderComponent = new TerritoryPlunderComponent();
            territoryEntity.AddComponent(new RootNodeComponent());
            territoryEntity.AddComponent(territoryPlunderComponent);
            AddEntityOnlyInitComponents(territoryEntity);
            logTree();
            Print($"***************** TerritoryPlunderTest constructor end *****************");
        }

        public override void Run(ArraySegment<string> args)
        {
            Print($"***************** TerritoryPlunderTest Run *****************");
            var damageData = new DamageDataEvent();
            damageData.SourcePlayerId = fightingPlayerEntity.EntityId;
            damageData.SourcePlayerId = (long)fightingPlayerEntity.RoleId;
            damageData.RealDamage = 100;
            TestUtility.InvokeStaticMethod(typeof(TerritoryPlunderComponentHotfix), "StartPlunderReport",
                territoryPlunderComponent, fightingPlayerEntity, damageData.RealDamage, false);
            territoryPlunderComponent.AddFightingPlayer(defenderPlayerEntity);
            territoryPlunderComponent.AddFightingPlayer(fightingPlayerEntity);
            logTree();

            var testCases = new List<Action>()
            {
                testDamage,
                testKill,
                testDestroyConstruction,
                testAttackPart,
                testPlunderContainer,
                testPlayerDie,
                testLootingCorpseBox,
                testBeDamage,
                testDestroyFurnishing,
                testUseExplosive,
                testMultiplePlayersAttack,
                testDefenderTeamBattle
            };

            foreach (var testCase in testCases)
            {
                Print($"***************** testCase {testCase.Method.Name} Run *****************");
                try
                {
                    testCase();
                    ProcessEntity.Instance.ExecuteDelayAction();
                }
                catch (Exception e)
                {
                    Print($"***************** testCase {testCase.Method.Name} Error *****************");
                    Print(e.Message);
                    Print(e.StackTrace);
                }

                logTree();
                Print($"***************** testCase {testCase.Method.Name} End *****************");
            }

            Print($"***************** TerritoryPlunderTest End *****************");
        }

        private void logTree()
        {
            Print($"territoryPlunderComponent.NodeSystem {territoryPlunderComponent.SystemRootNode.ToTreeString(0)}");
        }

        private void testDamage()
        {
            var damageData = new DamageEvent(0, 100, defenderPlayerEntity.EntityId, fightingPlayerEntity.EntityId);
            EntityStaticCallback<PlayerEntity>.InvokeAllEventCallback(fightingPlayerEntity, damageData);
        }

        private void testKill()
        {
            //var killData = new KillEvent(0, defenderPlayerEntity.EntityType, 0, 100, 0, fightingPlayerEntity.EntityId,
            //    defenderPlayerEntity.EntityId);
            //EntityStaticCallback<PlayerEntity>.InvokeAllEventCallback(fightingPlayerEntity, killData);
        }

        private void testDestroyConstruction()
        {
            EntityStaticCallback<PlayerEntity>.InvokeAllEventCallback(fightingPlayerEntity,
                new DestroyPartEvent(1, 3, fightingPlayerEntity.EntityId, territoryEntity.EntityId, 2001));
            EntityStaticCallback<PlayerEntity>.InvokeAllEventCallback(defenderPlayerEntity,
                new DestroyPartEvent(2, 4, defenderPlayerEntity.EntityId, territoryEntity.EntityId, 2001));
        }

        private void testAttackPart()
        {
            var attackPartData = new AttackPartEvent(22, fightingPlayerEntity.EntityId, 12040007, 144, 100, 0, territoryEntity.EntityId, false);
            EntityStaticCallback<PlayerEntity>.InvokeAllEventCallback(fightingPlayerEntity, attackPartData);
        }

        private void testPlunderContainer()
        {
            var param = new BoxEntitySpawnParam()
            {
                DestroySeconds = McCommon.Tables.TbGlobalConfig.RewardBoxDestroyTime,
                CustomInit = true,
                CorpseHostTemplateId = 1,
                StorageTemplateId = 1,
            };
            var box = new BoxEntity(
                templateId: McCommon.Tables.TbConstructionConstantConfig.ItemDropBoxId,
                territoryEntity.PosX, territoryEntity.PosY, territoryEntity.PosZ,
                0, 0, 0,
                param);
            box.BelongToTerritoryEntityId = territoryEntity.EntityId;
            AddEntityOnlyInitComponents(box);

            var opList = new List<int>() { 100, -10, -3 };
            foreach (var op in opList)
            {
                var plunderContainerData =
                    new LootingStorageChangeEvent(box.TemplateId, 12040007, op, fightingPlayerEntity.EntityId, box.EntityId);
                EntityStaticCallback<PlayerEntity>.InvokeAllEventCallback(fightingPlayerEntity, plunderContainerData);
            }
        }

        /// <summary>
        /// 测试玩家死亡事件 - 统计击杀和死亡数据
        /// </summary>
        private void testPlayerDie()
        {
            var playerDieEvent = new PlayerDieEvent
            {
                RoleId = defenderPlayerEntity.RoleId,
                KillerRoleId = fightingPlayerEntity.RoleId,
                KillerPlayerId = fightingPlayerEntity.EntityId
            };
            territoryPlunderComponent.OnPlayerDieEvent(playerDieEvent);
        }

        /// <summary>
        /// 测试掠夺战利品箱/尸体事件
        /// </summary>
        private void testLootingCorpseBox()
        {
            var param = new BoxEntitySpawnParam()
            {
                DestroySeconds = McCommon.Tables.TbGlobalConfig.RewardBoxDestroyTime,
                CustomInit = true,
                CorpseHostTemplateId = 1,
                StorageTemplateId = 1,
            };
            var corpseBox = new BoxEntity(
                templateId: McCommon.Tables.TbConstructionConstantConfig.ItemDropBoxId,
                territoryEntity.PosX, territoryEntity.PosY, territoryEntity.PosZ,
                0, 0, 0,
                param);
            corpseBox.BelongToTerritoryEntityId = territoryEntity.EntityId;
            AddEntityOnlyInitComponents(corpseBox);

            // 直接触发PlayerLootingBoxEvent事件进行测试
            var lootingBoxEvent = new PlayerLootingBoxEvent(12030001, 10, fightingPlayerEntity.EntityId, corpseBox.EntityId, false, true, corpseBox.PlayerId);
            territoryPlunderComponent.OnPlayerLootingCorpseBoxEvent(lootingBoxEvent);
        }

        /// <summary>
        /// 测试受伤害统计 - BeDamage节点
        /// </summary>
        private void testBeDamage()
        {
            // 使用BizId = 1001 (在NEED_COUNT_DAMAGE_SELECT_ID中)
            var damageEvent = new DamageEvent(1001, 200, defenderPlayerEntity.EntityId, fightingPlayerEntity.EntityId);
            territoryPlunderComponent.OnDamageEvent(damageEvent);
        }

        /// <summary>
        /// 测试摧毁摆件 - 区别于建筑核心
        /// </summary>
        private void testDestroyFurnishing()
        {
            // 使用一个摆件类型的BizId (非Core类型)
            var destroyFurnishingEvent =
                new DestroyPartEvent(11010007, 0, fightingPlayerEntity.EntityId, territoryEntity.EntityId, 16010019);
            territoryPlunderComponent.OnDestroyConstructionEvent(destroyFurnishingEvent);
        }

        /// <summary>
        /// 测试使用爆炸物攻击
        /// </summary>
        private void testUseExplosive()
        {
            // 创建一个建筑部件实体
            var partEntity = new PartEntity(IdGeneratorUtil.GenInWorldFreqId());
            partEntity.TemplateId = 16010019; // 建筑模板ID
            partEntity.TerritoryId = territoryEntity.EntityId;
            partEntity.AddComponent(new ConstructionBaseComponent());
            AddEntityOnlyInitComponents(partEntity);

            // 爆炸物ID（需要在TbPlunderExplosive表中存在）
            var explosiveAttackEvent =
                new AttackPartEvent(partEntity.EntityId, fightingPlayerEntity.EntityId, 144, 16040003, 100, 0, territoryEntity.EntityId, false);
            territoryPlunderComponent.OnAttackPartEvent(explosiveAttackEvent);
        }

        /// <summary>
        /// 测试多个玩家攻击场景
        /// </summary>
        private void testMultiplePlayersAttack()
        {
            // 创建第二个攻击者
            var secondAttackerEntity = new PlayerEntity();
            secondAttackerEntity.LongRoleId = IdGeneratorUtil.GenInWorldFreqId();
            secondAttackerEntity.AddComponent(new RootNodeComponent());
            secondAttackerEntity.AddComponent(new TeamComponent());
            secondAttackerEntity.AddComponent(new ReputationComponent());
            secondAttackerEntity.AddComponent(new PlayerInventoryComponent());
            secondAttackerEntity.AddComponent(new PlayerPlunderComponent());
            AddEntityOnlyInitComponents(secondAttackerEntity);
            UserManagerEntity.Instance.AddPlayerEntity(secondAttackerEntity.RoleId, secondAttackerEntity.EntityId);

            territoryPlunderComponent.AddFightingPlayer(secondAttackerEntity);

            // 两个攻击者同时进行各种攻击行为
            var damageEvent1 = new DamageEvent(1001, 150, defenderPlayerEntity.EntityId, fightingPlayerEntity.EntityId);
            var damageEvent2 = new DamageEvent(1001, 120, defenderPlayerEntity.EntityId, secondAttackerEntity.EntityId);

            territoryPlunderComponent.OnDamageEvent(damageEvent1);
            territoryPlunderComponent.OnDamageEvent(damageEvent2);

            var destroyEvent1 = new DestroyPartEvent(1, 2, fightingPlayerEntity.EntityId, territoryEntity.EntityId, 2001);
            var destroyEvent2 = new DestroyPartEvent(1, 3, secondAttackerEntity.EntityId, territoryEntity.EntityId, 2001);

            territoryPlunderComponent.OnDestroyConstructionEvent(destroyEvent1);
            territoryPlunderComponent.OnDestroyConstructionEvent(destroyEvent2);
        }

        /// <summary>
        /// 测试防守方队伍战斗场景
        /// </summary>
        private void testDefenderTeamBattle()
        {
            // 创建防守方队友
            var defenderTeamMateEntity = new PlayerEntity();
            defenderTeamMateEntity.LongRoleId = IdGeneratorUtil.GenInWorldFreqId();
            defenderTeamMateEntity.AddComponent(new RootNodeComponent());
            defenderTeamMateEntity.AddComponent(new TeamComponent());
            defenderTeamMateEntity.AddComponent(new ReputationComponent());
            defenderTeamMateEntity.AddComponent(new PlayerInventoryComponent());
            defenderTeamMateEntity.AddComponent(new PlayerPlunderComponent());
            AddEntityOnlyInitComponents(defenderTeamMateEntity);
            UserManagerEntity.Instance.AddPlayerEntity(defenderTeamMateEntity.RoleId, defenderTeamMateEntity.EntityId);

            // 防守方队友击杀攻击者
            var killByDefenderEvent = new PlayerDieEvent
            {
                RoleId = fightingPlayerEntity.RoleId,
                KillerRoleId = defenderTeamMateEntity.RoleId,
                KillerPlayerId = defenderTeamMateEntity.EntityId
            };
            territoryPlunderComponent.OnPlayerDieEvent(killByDefenderEvent);

            // 防守方队友也受到伤害
            var defenderDamageEvent = new DamageEvent(1001, 80, defenderTeamMateEntity.EntityId, fightingPlayerEntity.EntityId);
            territoryPlunderComponent.OnDamageEvent(defenderDamageEvent);
        }
    }
}