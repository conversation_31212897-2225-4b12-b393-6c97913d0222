using FairyGUI;
using System;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.SocClient.ClientItem;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Ui.Binder.GameHud;
using WizardGames.Soc.SocClient.Ui.Utils;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.SocClient.Ui
{
    /// <summary>
    /// HUD快捷栏物品选单
    /// </summary>
    public partial class UiHudElemShortcutsChooseMenu
    {
        private class MenuItem
        {
            public BaseItemNode ItemNode;
            public int Index;
            public bool IsInvalid = false;

            public long ItemId => ItemNode?.Id ?? -1;

            public MenuItem(int index = -1)
            {
                ItemNode = null;
                Index = index;
                IsInvalid = true;
            }

            public MenuItem(BaseItemNode item, int index)
            {
                ItemNode = item;
                Index = index;
                IsInvalid = false;
            }
        }

        private const int COLUMN_COUNT = 6;
        private const int ENABLE_AUTO_SCROLL_H_OFFSET = 20;

        private GComponent comRoot;
        private GComponent comContent;
        private Container contentContainer;
        private GList listIcons;
        private Dictionary<GObject, ComMenuChooseItemBinder> iconBinders = new();
        private GObject objAutoScrollTop;
        private GObject objAutoScrollBottom;
        private UiHudElemShortcutsBelt elemBelt;
        private Func<BaseItemNode, int, bool> onChooseItem;
        private List<MenuItem> curItems = new();
        private HashSet<long> preItemRecord = new();
        private HashSet<long> curItemRecord = new();
        private Queue<BaseItemNode> newAddItems = new();
        private Queue<MenuItem> removedMenuItems = new();
        private Queue<MenuItem> emptyMenuItems = new();
        private int curClickEmptyIconIndex = -1;
        private bool canScroll = false;
        private bool enableUpdateLock = true;
        private ComHudDropBar comDropbar;
        private Controller dropbarPosCtrl;
        private float contetnCenterX = 0;
        private int curSideFlag = 0;
        private MenuItem curChooseMenuItem = null;
        private int curOptOrder = 0;
        private bool canShowAutoScrollTrigger = false;
        private bool isAutoScrollTriggerVisible = false;
        protected float autoScrollStep = 0;
        protected float autoScrollFactor = 0;
        protected float autoScrollTimer = 0;
        protected float autoScrollTime = 0;

        public int RowCount { get; set; } = 2;

        // 显隐状态
        public bool Visible
        {
            get => null != comRoot && comRoot.visible;
            set
            {
                if (null != comRoot) comRoot.visible = value;
            }
        }

        public GComponent Root => comRoot;

        public UiHudElemShortcutsChooseMenu(UiHudElemShortcutsBelt elemBelt, GComponent comRoot, Func<BaseItemNode, int, bool> onChooseItem)
        {
            autoScrollTime = Mc.Tables.TbGlobalConfig.GetInventoryAutoScrollTriggerTime();
            autoScrollStep = Mc.Tables.TbGlobalConfig.GetInventoryAutoScrollStep();

            this.comRoot = comRoot;
            this.elemBelt = elemBelt;
            this.onChooseItem = onChooseItem;
            comContent = comRoot.GetChild("content").asCom;
            contentContainer = comContent.container;
            RefreshContetnCenterX();
            listIcons = comContent.GetChild("icons").asList;
            listIcons.SetVirtual();
            listIcons.itemRenderer = IconRenderer;
            iconBinders.Clear();
            listIcons.SetCustomPool(ListIconsCustomPoolGet, ListIconsCustomPoolReturn);
            listIcons.scrollPane.onScroll.Add(CheckAutoScrollTriggerState);
            listIcons.BindArrowVisibleExtCondition(() => !canShowAutoScrollTrigger);
            if (enableUpdateLock)
            {
                listIcons.scrollPane.onScroll.Add(() => contentContainer.RelockUpdateAfterFrame());
            }
            comDropbar = comRoot.GetChild("dropbar") as ComHudDropBar;
            dropbarPosCtrl = comRoot.GetController("dropbarPos");
            dropbarPosCtrl.SetSelectedPage("hide");
            dropbarPosCtrl.onChanged.Add(() => comDropbar.RelockUpdateAfterFrameIfLock());

            objAutoScrollTop = comRoot.GetChild("autoScrollTop");
            objAutoScrollBottom = comRoot.GetChild("autoScrollBottom");
            RegisterAutoScrollTigger(objAutoScrollTop.asCom);
            RegisterAutoScrollTigger(objAutoScrollBottom.asCom);

            UiHudElemShortcutsChooseMenu_Plaform(comRoot);
        }

        public void RefreshContetnCenterX()
        {
            contetnCenterX = comContent.LocalToStage(Vector2.right * comContent.width / 2).x;
        }

        private GObject ListIconsCustomPoolGet(string url)
        {
            var comIcon = listIcons.itemPool.GetObject(url).asCom;
            if (null == comIcon) return null;
            if (!iconBinders.ContainsKey(comIcon))
            {
                iconBinders.Add(comIcon, new ComMenuChooseItemBinder(comIcon));
            }
            var binder = iconBinders[comIcon];
            var loader = binder.Loader as ComItemIconLoader;
            var itemIcon = loader.RequestIcon();
            itemIcon.StopParentMoveWhenDrag = false;
            itemIcon.UseStrictClick = true;
            itemIcon.ReplaceChooseFlag(binder.Choose);
            itemIcon.SetTriggerHotArea(comIcon);
            return comIcon;
        }

        private void ListIconsCustomPoolReturn(GObject obj)
        {
            var comIcon = obj.asCom;
            if (null == comIcon || !iconBinders.ContainsKey(comIcon)) return;
            var binder = iconBinders[comIcon];
            var loader = binder.Loader as ComItemIconLoader;
            if (loader.HasIcon)
            {
                loader.Icon.ClearTriggerHotArea();
                loader.ReturnIcon();
            }
            listIcons.itemPool.ReturnObject(comIcon);
        }

        private void RegisterAutoScrollTigger(GComponent trigger)
        {
            if (null == trigger) return;
            var posStyleCtrl = trigger.GetController("posStyle");
            bool isDirectionUp = posStyleCtrl.selectedPage.Equals("bottom");
            var triggerStyleCtrl = trigger.GetController("triggerStyle");
            var triggerArea = trigger.GetChild("triggerArea");
            triggerArea.onRollOver.Add(() =>
            {
                autoScrollFactor = isDirectionUp ? autoScrollStep : -autoScrollStep;
                triggerStyleCtrl.SetSelectedPage("on");
            });
            triggerArea.onRollOut.Add(() =>
            {
                ResetAutoScroll();
                triggerStyleCtrl.SetSelectedPage("off");
            });
            trigger.visible = false;
        }

        private void CheckAutoScrollTriggerState()
        {
            if (!canShowAutoScrollTrigger)
            {
                if (isAutoScrollTriggerVisible)
                {
                    isAutoScrollTriggerVisible = false;
                    objAutoScrollTop.visible = false;
                    objAutoScrollBottom.visible = false;
                }
                return;
            }
            isAutoScrollTriggerVisible = true;
            objAutoScrollTop.visible = listIcons.scrollPane.percY > 0;
            objAutoScrollBottom.visible = !listIcons.scrollPane.isBottomMost;
        }

        public virtual void CheckAutoScroll(float dt)
        {
            if (0 == autoScrollFactor) return;
            if (autoScrollTimer < autoScrollTime)
            {
                autoScrollTimer += dt;
                return;
            }
            listIcons.scrollPane.SetPosY(listIcons.scrollPane.scrollingPosY + dt * autoScrollFactor, false);
        }

        /// <summary>
        /// 重置自动滚动状态
        /// </summary>
        protected virtual void ResetAutoScroll()
        {
            autoScrollFactor = 0;
            autoScrollTimer = 0;
        }

        private void SetAutoScrollTriggerEnable(bool enable)
        {
            // 如果列表内容高度不够触发滚动, 则不允许设置
            float deltaHeight = listIcons.scrollPane.contentHeight - listIcons.scrollPane.viewHeight;
            if (deltaHeight < ENABLE_AUTO_SCROLL_H_OFFSET) return;
            canShowAutoScrollTrigger = enable;
            listIcons.SetArrowVisible(!enable);
            CheckAutoScrollTriggerState();
            if (!enable) ResetAutoScroll();
        }

        /// <summary>
        /// 列表图标渲染
        /// </summary>
        private void IconRenderer(int index, GObject item)
        {
            if (null == curItems || index >= curItems.Count) return;
            var comIcon = item.asCom;
            if (null == comIcon || !iconBinders.ContainsKey(comIcon)) return;
            var binder = iconBinders[comIcon];
            var loader = binder.Loader as ComItemIconLoader;
            var icon = loader.Icon;
            if (null == icon) return;
            icon.SetIconQualityBgVisible(false);
            var menuItem = curItems[index];
            icon.Position = index;
            if (menuItem.IsInvalid)
            {
                icon.SetAsInvalid();
            }
            else
            {
                icon.SetInstData(menuItem.ItemNode);
                DealIconWhenSetData(icon);
            }
        }

        private bool OnMenuIconDragStart(ItemDragInfo info, ComItemIcon icon)
        {
            elemBelt?.ResetMenuChooseData();
            OnBeltAndMenuIconDragStart();
            UiItemIconDragDrop.UseFromIconSize = true;
            UiItemIconDragDrop.OnDraggingIcon = OnIconDragging;
            RelockIfEnableLock();
            return true;
        }

        private void OnMenuIconDragEnd()
        {
            OnBeltAndMenuIconDragEnd();
            UiItemIconDragDrop.OnDraggingIcon = null; 
            UiItemIconDragDrop.UseFromIconSize = false;
            RelockIfEnableLock();
        }

        /// <summary>
        /// 设置物品数据时对图标的处理
        /// </summary>
        private void DealIconWhenSetData(ComItemIcon icon)
        {
            icon.OnClickIcon = baseIcon => OnClickIcon(baseIcon as ComItemIcon);
            icon.CanDrag = true;
            var gloablConfig = Mc.Tables.TbGlobalConfig;
            if (canScroll) icon.SetVerticalDragSlow(gloablConfig.GetInventorySlowDragTriggerTime(), gloablConfig.GetInventorySlowDragTriggerAngle());
            else icon.DisableDragSlow();
            icon.OnMouseOrTouchBegin = ctx => UnlockIfEnableLock(); // 解锁以保证图标的长按行为能够正常驱动
            icon.OnMouseOrTouchEnd = ctx => RelockIfEnableLock();
            icon.OnMouseOrTouchMoveOut = ctx =>
            {
                if (icon.bInTouch) RelockIfEnableLock();
            };
            icon.OnIconDragStart = info => OnMenuIconDragStart(info, icon);
            icon.OnIconDragEnd = () => OnMenuIconDragEnd();
            icon.OnIconAcceptDrag = OnIconAcceptDrag;
            icon.OnMouseOrTouchMoveIn = ctx =>
            {
                if (UiItemIconDragDrop.IsDragging && UiItemIconDragDrop.CurDragIcon != icon)
                {
                    icon.SetHighlight(true);
                }
            };
            icon.OnMouseOrTouchMoveOut = ctx => icon.SetHighlight(false);
            icon.CanChooseOnEmpty = true;
            icon.Choose = curChooseMenuItem != null && curChooseMenuItem.Index == icon.Position;
            icon.OnIconLockUpdate = LockIfEnableLock;
            icon.OnIconUnlockUpdate = UnlockIfEnableLock;
            icon.OnIconRelockUpdate = RelockIfEnableLock;
            icon.SetCustomBg(FGUIResPathDef.GAME_HUD_THING_BJ_01);
        }

        private void UnlockIfEnableLock()
        {
            if (enableUpdateLock) contentContainer.UnlockUpdate();
        }

        private void LockIfEnableLock()
        {
            if (enableUpdateLock) contentContainer.LockUpdate();
        }

        private void RelockIfEnableLock(int frame = 1)
        {
            if (enableUpdateLock) contentContainer.RelockUpdateAfterFrame(frame);
        }

        private bool CallOnChooseItem(BaseItemNode itemNode, int position)
        {
            return onChooseItem?.Invoke(itemNode, position) ?? false;
        }

        private bool CallOnChooseItem(ComItemIcon icon, int position)
        {
            curClickEmptyIconIndex = -1;
            if (null == icon.InstItem)
            {
                OnClickOrDropOnEmptyIcon(icon);
            }
            return CallOnChooseItem(icon.InstItem, position);
        }

        /// <summary>
        /// 当选择了一个物品的情况下点击或拖入选择列表的一个空图标，则选中物品走默认进包逻辑，但需要显示在接受点击或拖拽的格子上
        /// </summary>
        private void OnClickOrDropOnEmptyIcon(ComItemIcon icon)
        {
            curClickEmptyIconIndex = icon.Position;
        }

        /// <summary>
        /// 点击图标
        /// </summary>
        private void OnClickIcon(ComItemIcon icon)
        {
            if (icon.IsInvalid())
            {
                if (null == curChooseMenuItem)
                {
                    curChooseMenuItem = null;
                    listIcons.RefreshVirtualList();
                    RelockIfEnableLock();
                }
                return;
            }
            if (CallOnChooseItem(icon, -1))
            {
                icon.DoBlink();
                return;
            }
            // 点击图标时如果快捷栏没有执行实际的选中逻辑，则将行为重定向为选中选单中的图标
            int iconIndex = icon.Position;
            if (curChooseMenuItem != null && curChooseMenuItem.Index == iconIndex) curChooseMenuItem = null;
            else if(iconIndex < curItems.Count) curChooseMenuItem = curItems[iconIndex];
            listIcons.RefreshVirtualList();
            RelockIfEnableLock();
        }

        private bool OnIconAcceptDrag(ComBaseIcon myIcon, ItemDragInfo dragInfo)
        {
            if (OnIconAcceptDrag_Platform(myIcon, dragInfo)) return false;
            if (null == myIcon || myIcon is not ComItemIcon myItemIcon || myItemIcon.IsInvalid()) return false;
            if (null == dragInfo || null == dragInfo.item) return false;
            var dropItem = dragInfo.item;
            if (dropItem.ParentId != NodeConst.BeltItemContainerNodeId) return false;
            CallOnChooseItem(myItemIcon, dropItem.Position);
            RelockIfEnableLock();
            return true;
        }

        public bool TrySwapWithMenuChoose(int clickPos)
        {
            if (null == curChooseMenuItem) return false;
            var toChoose = curChooseMenuItem;
            curChooseMenuItem = null;
            listIcons.RefreshVirtualList();
            CallOnChooseItem(toChoose.ItemNode, clickPos);
            return true;
        }

        /// <summary>
        /// 对物品列表进行排序转换
        /// </summary>
        private void ReverseAndFlipItemList(int maxEmptyCount)
        {
            // 策划要求数据从下到上，从左到右排列
            // 正常使用通用排序排序完是从上到下，从左到右
            // 需要进行额外的处理

            // 数量不足两行的，至少填充空物品到两行
            // 超过两行但单行不足一行的，需要填满一行
            int fillCount = 0;
            int minCount = COLUMN_COUNT * RowCount;
            if (curItems.Count < minCount)
            {
                fillCount = minCount - curItems.Count;
            }
            else if (curItems.Count % COLUMN_COUNT != 0)
            {
                fillCount = COLUMN_COUNT - curItems.Count % COLUMN_COUNT;
            }
            for (int i = 0; i < fillCount; i++)
            {
                // 塞入背包空格数之内的格子，算有效空格，可以容纳物品
                // 空格数之外的格子，算无效空格，不能容纳物品
                if (i < maxEmptyCount) curItems.Add(new MenuItem(null, curItems.Count));
                else curItems.Add(new MenuItem());
            }
            // 翻转，这样就是从下到上，从右往左
            curItems.Reverse();
            // 分段双指针翻转
            int rowCount = curItems.Count / COLUMN_COUNT;
            for (int row = 0; row < rowCount; row++)
            {
                int startIndex = row * COLUMN_COUNT;
                int endIndex = startIndex + COLUMN_COUNT - 1;
                while (startIndex < endIndex)
                {
                    var temp = curItems[startIndex];
                    curItems[startIndex] = curItems[endIndex];
                    curItems[endIndex] = temp;
                    curItems[startIndex].Index = startIndex;
                    curItems[endIndex].Index = endIndex;
                    ++startIndex;
                    --endIndex;
                }
            }
        }

        /// <summary>
        /// 将列表设置为空状态
        /// </summary>
        public void SetEmpty()
        {
            curItems.Clear();
            canScroll = false;
            listIcons.numItems = 0;
            listIcons.scrollPane.touchEffect = false;
            RelockIfEnableLock();
        }

        /// <summary>
        /// 交互前后两次物品记录缓存
        /// </summary>
        private void SwipItemRecord()
        {
            var temp = preItemRecord;
            preItemRecord = curItemRecord;
            curItemRecord = temp;
            curItemRecord.Clear();
        }

        private int MenuItemComapre(MenuItem a, MenuItem b)
        {
            if (null != a.ItemNode && null != b.ItemNode) return MgrCollectionItem.CommonItemNodeBagSort(a.ItemNode, b.ItemNode);
            else return null == a.ItemNode ? 1 : -1;
        }

        /// <summary>
        /// 收集或修改物品列表
        /// </summary>
        private void CollectOrModifyItemList(bool isFirstOpen = true)
        {
            // 第一次打开时，完全收集物品并排序
            // 后续刷新，只比较和前一次的差异，对排好序的物品列表进行修改
            // 因为交互策划要求后续更新位置第一次排好的顺序不变
            if (isFirstOpen)
            {
                curItems.Clear();
            }
            SwipItemRecord();
            newAddItems.Clear();
            var containerMain = Mc.CollectionItem.InventoryCom.ContainerMainNode;
            MgrCollectionItem.IterOneContainer(containerMain, itemNode =>
            {
                if (!ItemUtility.CanBeltAcceptItem((int)HoldItemIndex.Item1, itemNode.Config)) return true;
                curItemRecord.Add(itemNode.Id);
                // 第一次打开收集物品
                if (isFirstOpen)
                {
                    curItems.Add(new MenuItem(itemNode, curItems.Count));
                    return true;
                }
                // 如果这个物品之前也有，说明不是新物品
                if (preItemRecord.Contains(itemNode.Id)) preItemRecord.Remove(itemNode.Id);
                // 记录新物品
                else newAddItems.Enqueue(itemNode);
                return true;
            });
            // 第一次打开时，对物品列表进行一次全排序即可
            if (isFirstOpen)
            {
                curItems.Sort(MenuItemComapre);
                int emptyNum = containerMain.Capacity - containerMain.Count;
                ReverseAndFlipItemList(emptyNum);
                return;
            }
            // 后续更新，执行物品列表的修改
            // 按顺序收集当前所有已移除物品的格子与空格子
            if (newAddItems.Count > 0 || preItemRecord.Count > 0)
            {
                foreach (var menuItem in curItems)
                {
                    if (menuItem.IsInvalid) continue;
                    if (null == menuItem.ItemNode) emptyMenuItems.Enqueue(menuItem);
                    // preItemRecord 剩下的都是被移除的物品
                    else if (preItemRecord.Contains(menuItem.ItemId)) removedMenuItems.Enqueue(menuItem);
                }
            }
            // 优先将新增物品替换掉已移除物品， 然后塞到有效的空格子中
            // 如果还有剩余的新增物品，则往列表中继续追加新的格子到末尾
            while (newAddItems.Count > 0)
            {
                var newItem = newAddItems.Dequeue();
                // 优先放到当前点击的空格中
                if (curClickEmptyIconIndex >= 0)
                {
                    var index = curClickEmptyIconIndex;
                    curClickEmptyIconIndex = -1;
                    if (index < curItems.Count && curItems[index].Index >= 0)
                    {
                        curItems[index].ItemNode = newItem;
                        continue;
                    }
                }
                // 已移除或空的格子
                MenuItem menuItem = null;
                if (removedMenuItems.Count > 0)
                {
                    menuItem = removedMenuItems.Dequeue();
                }
                else if (emptyMenuItems.Count > 0)
                {
                    menuItem = emptyMenuItems.Dequeue();
                }
                if (null != menuItem) menuItem.ItemNode = newItem;
                // 由于不存在快捷栏往列表中新增物品导致格子增加的情况，所以这里先不处理没有格子可以放的情况
                else break;
            }
            // 如果还有剩余的已移除物品的格子，则清空其物品
            while (removedMenuItems.Count > 0)
            {
                removedMenuItems.Dequeue().ItemNode = null;
            }
            preItemRecord.Clear();
            removedMenuItems.Clear();
            emptyMenuItems.Clear();
        }

        /// <summary>
        /// 设置列表数据
        /// </summary>
        public void SetData(bool isFirstOpen = true)
        {
            CollectOrModifyItemList(isFirstOpen);
            curChooseMenuItem = null;
            canScroll = curItems.Count > 2 * COLUMN_COUNT;
            listIcons.numItems = curItems.Count;
            listIcons.scrollPane.touchEffect = canScroll;
            if (canScroll && isFirstOpen)
            {
                listIcons.scrollPane.ScrollBottom(false);
            }
            RelockIfEnableLock();
        }

        public void OnIconDragging(float x, float y)
        {
            if (!Visible) return;
            int sideFlag = x <= contetnCenterX ? -1 : 1;
            if (sideFlag == curSideFlag) return;
            curSideFlag = sideFlag;
            dropbarPosCtrl.SetSelectedPage(curSideFlag < 0 ? (elemBelt.isChooseMenuAtTop? "leftUp": "leftDown") : (elemBelt.isChooseMenuAtTop? "rightUp": "rightDown"));
        }

        public void OnBeltAndMenuIconDragStart()
        {
            SetAutoScrollTriggerEnable(true);
        }

        public void OnBeltAndMenuIconDragEnd()
        {
            curSideFlag = 0;
            dropbarPosCtrl.SetSelectedPage("hide");
            SetAutoScrollTriggerEnable(false);
        }

        public void OnUdateItemNode(BaseItemNode itemNode)
        {
            SetData(false);
            RelockIfEnableLock();
        }
    }
}