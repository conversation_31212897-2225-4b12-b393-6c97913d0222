using FairyGUI;
using WizardGames.Soc.SocClient.Plant;

namespace WizardGames.Soc.SocClient.Ui
{
    public class UiPlantBasePage
    {
        protected UiPlantOperationSubPanel uiPlantOperationSubPanel;
        public virtual EPageType PageType { get; }

        public void Init(GComponent com,UiPlantOperationSubPanel uiPlantOperationSubPanel)
        {
            OnInit(com,uiPlantOperationSubPanel);
        }

        protected virtual void OnInit(GComponent com,UiPlantOperationSubPanel uiPlantOperationSubPanel)
        {
            this.uiPlantOperationSubPanel = uiPlantOperationSubPanel;
        }

        public void Enable(object data)
        {
            OnEnable(data);
        }

        protected virtual void OnEnable(object plantBox)
        {

        }

        public void Disable()
        {
            OnDisable();
        }

        protected virtual void OnDisable()
        {

        }

        public void Refresh()
        {
            OnRefresh();
        }

        protected virtual void OnRefresh()
        {

        }

        public void Dispose()
        {
            OnDispose();
        }

        protected virtual void OnDispose()
        {

        }
    }
}
