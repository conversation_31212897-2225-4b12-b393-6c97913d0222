﻿using System;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Train;
using WizardGames.Soc.Common.Unity.Go;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Procedural;
using WizardGames.Soc.SocSimulator.Go;
using WizardGames.Soc.SocSimulator.Manager;
using static WizardGames.Soc.Procedural.TrainTrackSpline;
// A train which can we one train car, or any amount of coupled train cars
public class CompleteTrain
{
    //// The train car that runs the train simulation and decides where the train goes
    //// Always located at the front of the train in the direction it's travelling
    public TrainCarGo PrimaryTrainCar { get; private set; }

    public float TotalForces { get; private set; }
    public float TotalMass { get; private set; }

    // Always positive for a train driving forward, and negative for a train driving backwards
    // "Forward" being train car 0 moving away from train car 1 that it's coupled to, or the front of a single traincar on its own
    // Use GetTrackSpeedFor for individual train cars, since they might be coupled facing the opposite direction
    private float trackSpeed;

    // Track speed in the previous frame
    private float prevTrackSpeed;

    // Always at least one. Car 0 is the front when TrackSpeed is positive
    private List<TrainCarGo> trainCars;

    TriggerTrainCollisions mFrontCollisionTrigger;
    TriggerTrainCollisions mRearCollisionTrigger;

    // How much impact energy is retained on collisions. 1.0 = 100% retained, 0.0 = 100% loss of energy
    private const float IMPACT_ENERGY_FRACTION = 0.75f;

    // Ignore low magnitude so e.g. Players running into stationary vehicles doesn't trigger damage or FX
    private const float MIN_COLLISION_FORCE = 70000f;

    private TimeSince timeSinceLastChange = new TimeSince();

    private Action<bool> mOnTrainCollisionCallBack;

    public CompleteTrain(TrainCarGo trainCar)
    {
        List<TrainCarGo> allTrainCars = Facepunch.Pool.GetList<TrainCarGo>();
        allTrainCars.Add(trainCar);
        Init(allTrainCars);
    }

    private void Init(List<TrainCarGo> allTrainCars)
    {
        trainCars = allTrainCars;
        timeSinceLastChange = 0f;

        float speedTotal = 0f;
        PrimaryTrainCar = trainCars[0];

        trackSpeed = speedTotal;
        prevTrackSpeed = trackSpeed;
        // An initial tick to set everything else
        ParamsTick();
    }

    public void AddTrainCar(TrainCarGo trainCar)
    {
        if (trainCars.Contains(trainCar))
            return;

        trainCars.Add(trainCar);
        trainCars.Sort((car1, car2) =>
            ((TrainCarEntity)car1.Entity).SlotIndex.CompareTo(((TrainCarEntity)car2.Entity)
                .SlotIndex)); // TrainCarGo有一个SlotIndex属性用于排序
    }

    // Track speed is always positive in the forward direction for the individual train car
    public float GetTrackSpeedFor(TrainCarGo trainCar)
    {
        return trackSpeed;
    }

    // Track speed is always positive in the forward direction for the individual train car
    public float GetPrevTrackSpeedFor(TrainCarGo trainCar)
    {
        int index = trainCars.IndexOf(trainCar);
        if (index < 0)
        {
            SocUtility.Logger.ErrorFormat("{0}: Train car not found in the trainCars list.", GetType().Name);
            return 0;
        }

        return prevTrackSpeed;
    }

    // Called once per physics tick per complete coupled train (not per train car)
    public void UpdateTick(float dt)
    {
        // Every train car in the train will TRY to call UpdateTick. Make sure we only actually run it once per tick

        ParamsTick();
        MovementTick(dt);
    }

    private void ParamsTick()
    {
        // Get all forces acting on this train's cars
        TotalForces = 0f;
        TotalMass = trainCars[0].Rigidbody.mass;

        // Account for train engines facing backwards on the train
        TrainCarGo trainCar = trainCars[0];
        float forces = trainCar.GetForces();
        TotalForces = forces;
        
        mFrontCollisionTrigger = trainCars[0].FrontCollisionTrigger;
        mRearCollisionTrigger = trainCars[^1].RearCollisionTrigger;

        mFrontCollisionTrigger.TrainHead = trainCars[0]; // 车头
        mRearCollisionTrigger.TrainHead = trainCars[0]; // 车头
    }

    // Update the track speed of the train as a whole
    private void MovementTick(float dt)
    {
        prevTrackSpeed = trackSpeed;

        trackSpeed += (TotalForces * dt) / TotalMass;

        // Friction/drag
        const float DRAG_MULTIPLIER = 4.0f;
        float baseDrag = trainCars[0].Rigidbody.drag; // Assume they're all the same  摩擦力？？

        if (trackSpeed > 0)
        {
            trackSpeed -= baseDrag * DRAG_MULTIPLIER * dt;
            if (trackSpeed < 0)
                trackSpeed = 0;
        }
        else if (trackSpeed < 0)
        {
            trackSpeed += baseDrag * DRAG_MULTIPLIER * dt;
            if (trackSpeed > 0)
                trackSpeed = 0;
        }

        // Apply the effect of collisions
        trackSpeed = ApplyCollisionsToTrackSpeed(trackSpeed, TotalMass, dt);

        trackSpeed = Mathf.Clamp(trackSpeed, -(TrainCarGo.TRAINCAR_MAX_SPEED - 1f), TrainCarGo.TRAINCAR_MAX_SPEED - 1f);

        // Update the current "front" car
        if (trackSpeed > 0f)
        {
            PrimaryTrainCar = trainCars[0];
        }
        else if (trackSpeed < 0f)
        {
            PrimaryTrainCar = trainCars[trainCars.Count - 1];
        }
        else if (TotalForces > 0f) // Fall back to checking forces if TrackSpeed is zero
        {
            PrimaryTrainCar = trainCars[0];
        }
        else if (TotalForces < 0f)
        {
            PrimaryTrainCar = trainCars[trainCars.Count - 1];
        }
        else
        {
            // If the train is not moving and there are no forces on the train,
            // it doesn't matter which train car is the front one
            PrimaryTrainCar = trainCars[0];
        }

        if (trackSpeed != 0f || TotalForces != 0f)
        {
            // Move the front car - it leads the others
            PrimaryTrainCar.FrontTrainCarTick(GetTrackSelection(), dt);
            // Move remaining coupled train cars
            if (trainCars.Count > 1)
            {
                if (PrimaryTrainCar == trainCars[0])
                {
                    // Go first-to-last
                    for (int i = 1; i < trainCars.Count; i++) MoveOtherTrainCar(trainCars[i], trainCars[i - 1]);
                }
                else
                {
                    // Go last-to-first. Train is running backwards
                    for (int i = trainCars.Count - 2; i >= 0; i--) MoveOtherTrainCar(trainCars[i], trainCars[i + 1]);
                }
            }

        }
    }

    private void MoveOtherTrainCar(TrainCarGo trainCar, TrainCarGo prevTrainCar)
    {
        // Preferred alt = the spline the closest wheels on the car in front are on
        TrainTrackSpline trackSpline = prevTrainCar.FrontTrackSection;
        float splineDist = prevTrainCar.FrontWheelSplineDist;
        float distanceOffset = 0;

        if (trainCar.TrainCarCom.coupling is { frontCoupling: not null, rearCoupling: not null })
        {
            TrainCoupling frontCoupledTo = trainCar.TrainCarCom.coupling.frontCoupling.CoupledTo;
            TrainCoupling rearCoupledTo = trainCar.TrainCarCom.coupling.rearCoupling.CoupledTo;

            if (frontCoupledTo == prevTrainCar.TrainCarCom.coupling.rearCoupling)
            {
                // 🠔 ▗▟███ ▗▟███
                distanceOffset += trainCar.DistFrontWheelToFrontCoupling;
                distanceOffset += prevTrainCar.DistFrontWheelToBackCoupling;
            }
            else if (rearCoupledTo == prevTrainCar.TrainCarCom.coupling.frontCoupling)
            {
                // 🠔 ███▙▖ ███▙▖
                distanceOffset -= trainCar.DistFrontWheelToBackCoupling;
                distanceOffset -= prevTrainCar.DistFrontWheelToFrontCoupling;
            }
        }
        float newSplineDist = trackSpline.IsForward(prevTrainCar.MainTransform.forward, splineDist) ? splineDist - distanceOffset : splineDist + distanceOffset;
        trainCar.OtherTrainCarTick(trackSpline, newSplineDist, 0);
    }

    public void ReduceSpeedBy(float velChange)
    {
        prevTrackSpeed = trackSpeed;
        if (trackSpeed > 0)
        {
            trackSpeed = Mathf.Max(0, trackSpeed - velChange);
        }
        else if (trackSpeed < 0)
        {
            trackSpeed = Mathf.Min(0, trackSpeed + velChange);
        }
    }

    // Are there any players on any of the train cars that make up this train?
    public bool AnyPlayersOnTrain()
    {
        foreach (TrainCarGo trainCar in trainCars)
        {
            if (trainCar.AnyPlayersOnTrainCar())
            {
                return true;
            }
        }

        return false;
    }

    private TrackSelection GetTrackSelection()
    {
        TrackSelection result = TrackSelection.Default;
        foreach (TrainCarGo trainCar in trainCars)
        {
            // Return the input from the first train car that's giving an active driver input
            if (trainCar.LocalTrackSelection != TrackSelection.Default)
            {
                return trainCar.LocalTrackSelection;
            }
        }

        // No active driver input on any coupled train car. Use default selection
        return result;
    }

    //#region Collisions

    enum StaticCollisionState { Free, StaticColliding, StayingStill }

    StaticCollisionState mStaticCollidingAtFront;
    HashSet<GameObject> mMonitoredStaticContentF = new HashSet<GameObject>();
    StaticCollisionState mStaticCollidingAtRear;
    HashSet<GameObject> mMonitoredStaticContentR = new HashSet<GameObject>();

    Dictionary<Rigidbody, float> prevTrackSpeeds = new Dictionary<Rigidbody, float>();

    public void FreeStaticCollision()
    {
        mStaticCollidingAtFront = StaticCollisionState.Free;
        mStaticCollidingAtRear = StaticCollisionState.Free;
    }

    // Return a track speed that's modified by any collision that's occuring
    private float ApplyCollisionsToTrackSpeed(float trackSpeed, float totalMass, float deltaTime)
    {
        trackSpeed = ApplyCollisions(trackSpeed, atOurFront: true, mFrontCollisionTrigger, totalMass, ref mStaticCollidingAtFront, deltaTime); // 前方碰撞

        trackSpeed = ApplyCollisions(trackSpeed, atOurFront: false, mRearCollisionTrigger, totalMass, ref mStaticCollidingAtRear, deltaTime); // 后方碰撞
        // Clear out any rigidbodies that have now left the triggers
        Rigidbody toRemove = null;
        foreach (var entry in prevTrackSpeeds)
        {
            if (entry.Key == null || (!mFrontCollisionTrigger.mOtherRigidbodyContents.Contains(entry.Key) &&
                                      !mRearCollisionTrigger.mOtherRigidbodyContents.Contains(entry.Key)))
            {
                toRemove = entry.Key;
                // Only remove a max of one rigidbody per tick because then I don't
                // have to make a list (but let's pretend it's for performance concerns)
                break;
            }
        }

        if (toRemove != null)
        {
            prevTrackSpeeds.Remove(toRemove);
        }

        return trackSpeed;
    }

    // Detect and handle collisions at the front or rear of the train
    private float ApplyCollisions(float trackSpeed, bool atOurFront, TriggerTrainCollisions trigger, float ourTotalMass, ref StaticCollisionState wasStaticColliding, float deltaTime)
    {
        const float MAX_MASS_CONSIDERED = 13000f; // Stop huge locomotives causing crazy numbers that are impossible to balance
        if (trigger == null) return trackSpeed;

        TrainCarGo trainHead = trigger.TrainHead;
        if (trainHead == null || trainHead.MainTransform == null)
            return trackSpeed;
        
        Vector3 forwardVector = trainHead.MainTransform.forward; // 火车头的前方

        Vector3 ourVelocity = forwardVector * trackSpeed; //移动速度
        bool staticColliding = trigger.HasAnyStaticContents;
        float collisionForce = staticColliding ? ourVelocity.magnitude * Mathf.Clamp(ourTotalMass, 1f, MAX_MASS_CONSIDERED) : 0;

        trackSpeed = HandleStaticCollisions(staticColliding, atOurFront, trackSpeed, ref wasStaticColliding, trigger);

        if (!staticColliding)
        {
            bool isFrontCollision = false;
            foreach (var trainEntityId in trigger.mTrainContents)
            {
                var entityGo = Mc.Go.GetGo(trainEntityId);
                if (entityGo == null)
                    continue;
                if (entityGo is TrainCarGo theirTrainCar && theirTrainCar.MainTransform != null)
                {
                    if (theirTrainCar.GetTrainHead() != null &&
                        theirTrainCar.GetTrainHead().EntityId == trainHead.EntityId)
                        continue;

                    // Use prev track speed since sometimes collisions are calculated a frame late, where velocity has already changed
                    Vector3 theirVelocity = theirTrainCar.MainTransform.forward * theirTrainCar.GetPrevTrackSpeed();
                    TrainCarGo owner = trigger.Owner; // 撞击的火车
                    trackSpeed = HandleTrainCollision(atOurFront, forwardVector, trackSpeed, owner.MainTransform,
                        theirTrainCar, deltaTime, ref wasStaticColliding);
                    collisionForce += Vector3.Magnitude(theirVelocity - ourVelocity) *
                                      Mathf.Clamp(theirTrainCar.Rigidbody.mass, 1f, MAX_MASS_CONSIDERED);
                    if (collisionForce > 50000)
                    {
                        isFrontCollision = true;
                    }
                }
            }

            if (isFrontCollision && mOnTrainCollisionCallBack != null)
            {
                mOnTrainCollisionCallBack(atOurFront);
            }

            // Any other rigidbodies that aren't trains
            foreach (Rigidbody theirRB in trigger.mOtherRigidbodyContents)
            {
                trackSpeed = HandleRigidbodyCollision(atOurFront, trackSpeed, forwardVector, ourTotalMass, theirRB,
                    theirRB.mass, deltaTime, true);
                collisionForce += Vector3.Magnitude(theirRB.velocity - ourVelocity) *
                                  Mathf.Clamp(theirRB.mass, 1f, MAX_MASS_CONSIDERED);
            }
        }

        // timeSinceLastChange: Makes sure train cars don't damage each other when uncoupling
        if (collisionForce >= MIN_COLLISION_FORCE && timeSinceLastChange > 1.0f)
        {
            trigger.TrainHead.ApplyCollisionDamage(collisionForce);
        }

        return trackSpeed;
    }

    // Handle any collision with non-movable objects
    private float HandleStaticCollisions(bool staticColliding, bool front, float trackSpeed,
        ref StaticCollisionState wasStaticColliding, TriggerTrainCollisions trigger = null)
    {
        var trainCfg = Mc.Tables.TbTrainCarProperty.GetOrDefault(PrimaryTrainCar.TemplateId);
        float collisionBackOffSpeed = front ? -trainCfg.CollisionStopSpeed : trainCfg.CollisionStopSpeed;
        bool shouldBackOff = staticColliding &&
                             (front ? trackSpeed > collisionBackOffSpeed : trackSpeed < collisionBackOffSpeed);
        if (shouldBackOff)
        {
            // If we're colliding with something that cannot move, slowly back off until we aren't
            trackSpeed = collisionBackOffSpeed;
            wasStaticColliding = StaticCollisionState.StaticColliding;
            // Make a note of the static content, so we can check if it's destroyed later
            HashSet<GameObject> hs = front ? mMonitoredStaticContentF : mMonitoredStaticContentR;
            hs.Clear();
            if (trigger != null)
            {
                foreach (GameObject entry in trigger.mStaticContents)
                {
                    hs.Add(entry);
                }
            }
        }
        else if (wasStaticColliding == StaticCollisionState.StaticColliding)
        {
            // We've backed off enough, stop backing off and start over at a still position still rather than try to bump into it again
            trackSpeed = 0;
            wasStaticColliding = StaticCollisionState.StayingStill;
        }
        else if (wasStaticColliding == StaticCollisionState.StayingStill)
        {
            bool movingIntoCollision = front ? trackSpeed > 0.01f : trackSpeed < -0.01f;
            if (movingIntoCollision)
            {
                // Has the static content been destroyed since we backed off?
                HashSet<GameObject> hs = front ? mMonitoredStaticContentF : mMonitoredStaticContentR;
                // There can be a static collision without any content, e.g. train is at the end of the line
                // If so, don't check as the list will be empty anyway
                if (hs.Count > 0)
                {
                    bool allNull = true;
                    foreach (GameObject entry in hs)
                    {
                        if (entry != null)
                        {
                            allNull = false;
                            break;
                        }
                    }

                    if (allNull)
                    {
                        movingIntoCollision = false;
                    }
                }
            }

            if (movingIntoCollision)
            {
                // We're moving TOWARDS the previous static collision. Stay still instead
                trackSpeed = 0;
            }
            else
            {
                // We're moving AWAY from the previous static collision, or not moving. Go free
                wasStaticColliding = StaticCollisionState.Free;
            }
        }
        else
        {
            if (front)
                mMonitoredStaticContentF.Clear();
            else
                mMonitoredStaticContentR.Clear();
        }

        return trackSpeed;
    }

    // Handle other trains colliding with us
    // Lets us handle train forces better despite being kinematic, since the basic rigidbody handling doesn't
    // take into account secondary forces. For instance this lets a train realistically push several other trains or carriages
    private float HandleTrainCollision(bool front, Vector3 forwardVector, float trackSpeed, Transform ourTransform, TrainCarGo theirTrain, float deltaTime, ref StaticCollisionState wasStaticColliding)
    {
        float angleDifference = Vector3.Angle(ourTransform.forward, theirTrain.MainTransform.forward);
        Vector3 vectorTo = theirTrain.MainTransform.position - ourTransform.position;
        float dotProduct = Vector3.Dot(ourTransform.forward, vectorTo.normalized);
        // AngleDifference covers stuff like a train right in front of us but on another track that's crossing over
        // DotProduct covers stuff like a train on a parallel track that's just merging onto our track
        bool sideHit = (angleDifference > 30 && angleDifference < 150) || Mathf.Abs(dotProduct) < 0.9396926f; // cos(20 degrees)

        if (sideHit)
        {
            // Treat side hits basically like static hits since we can't push a train along a track sideways
            // A sideways hit is most likely a hit when crossing a track junction, with the other train on a different track to us
            // Slowly back off until not colliding anymore
            var trainSpeed = Mathf.Abs(trackSpeed) * 0.8f;
            trackSpeed = front ? -trainSpeed : trainSpeed;
            if (trainSpeed < 0.01f)
                trackSpeed = 0;
        }
        else
        {
            Vector3 pushDirection = front ? forwardVector : -forwardVector;
            // Handle additional mass
            // Get the total mass of the train in front plus anything else it's colliding with
            List<CompleteTrain> prevTrains = Facepunch.Pool.GetList<CompleteTrain>();
            float theirTotalMass = GetTotalPushingMass(pushDirection, forwardVector, ref prevTrains);
            if (theirTotalMass < 0)
            {
                trackSpeed = HandleStaticCollisions(true, front, trackSpeed, ref wasStaticColliding);
            }
            else
            {
                trackSpeed = HandleRigidbodyCollision(front, trackSpeed, forwardVector, TotalMass, theirTrain.Rigidbody,
                    theirTotalMass, deltaTime, false, theirTrain.GetWorldVelocity(), false);
            }

            // Handle additional forces (e.g. Other colliding trains using their engines)
            prevTrains.Clear();
            float additionalForces = GetTotalPushingForces(pushDirection, forwardVector, ref prevTrains);
            if (!front) additionalForces *= -1;
            trackSpeed += additionalForces / TotalMass * deltaTime;

            Facepunch.Pool.FreeList(ref prevTrains);
        }

        return trackSpeed;
    }

    // Handle any rigidbody collision
    private float HandleRigidbodyCollision(bool atOurFront, float trackSpeed, Vector3 forwardVector, float ourTotalMass,
        Rigidbody theirRB, float theirTotalMass, float deltaTime, bool calcSecondaryForces,
        Vector3 theirVelocity = default(Vector3), bool isUseRigidbodyVelocity = true)
    {

        var veloctity = theirRB.velocity;

        if (!isUseRigidbodyVelocity)
            veloctity = theirVelocity;

        float theirTrackSpeed = Vector3.Dot(forwardVector, veloctity);
        float relativeTrackVel = trackSpeed - theirTrackSpeed;

        // Early exit if the object is moving AWAY from us
        if ((atOurFront && relativeTrackVel <= 0) || (!atOurFront && relativeTrackVel >= 0))
        {
            return trackSpeed;
        }

        float forceOnUs = (relativeTrackVel / deltaTime) * theirTotalMass * IMPACT_ENERGY_FRACTION;

        if (calcSecondaryForces)
        {
            // Account for not only forces between us and the thing we're hitting,
            // but also secondary forces acting on the thing we're hitting
            // e.g. If we're pressing against a car which is up against a wall or another object
            if (prevTrackSpeeds.ContainsKey(theirRB))
            {
                float forceOnThem = relativeTrackVel / deltaTime * ourTotalMass * IMPACT_ENERGY_FRACTION;
                float velChangeExpected = forceOnThem / theirTotalMass * deltaTime;
                float velChangeActual = prevTrackSpeeds[theirRB] - theirTrackSpeed;
                forceOnUs -= Mathf.Clamp((velChangeActual - velChangeExpected) * ourTotalMass, 0, 1000000);
                prevTrackSpeeds[theirRB] = theirTrackSpeed;
            }
            else if (theirTrackSpeed != 0f) // Give them a chance to get pushed before we add them
            {
                prevTrackSpeeds.Add(theirRB, theirTrackSpeed);
            }
        }

        // Apply impact force to our velocity along the track
        float change = forceOnUs / ourTotalMass * deltaTime;

        // Prevent ridiculous forces
        change = Mathf.Clamp(change, -Mathf.Abs(theirTrackSpeed - trackSpeed) - 1.0f, Mathf.Abs(theirTrackSpeed - trackSpeed) + 1.0f);
        trackSpeed -= change;
        return trackSpeed;
    }

    // If this train is pushed, how much force should it exert back on the pusher?
    // Returns -1 for static collision
    private float GetTotalPushingMass(Vector3 pushDirection, Vector3 ourForward, ref List<CompleteTrain> prevTrains)
    {
        float result = 0f;
        if (prevTrains.Count > 0)
        {
            if (prevTrains.Contains(this))
            {
                return 0;
            }

            // Don't add the mass of the original CompleteTrain caller, but do it for sub-calls
            result += TotalMass;
        }

        prevTrains.Add(this);

        bool pushedForward = Vector3.Dot(ourForward, pushDirection) >= 0;

        // Against something static at the other end?
        StaticCollisionState staticState = pushedForward ? mStaticCollidingAtFront : mStaticCollidingAtRear;
        if (staticState != StaticCollisionState.Free)
        {
            // Can't move if we're up against a static object
            // Note: Tempting to return float.maxValue here for an "unpushable weight", but if this train bounces
            // back slightly while still considered static colliding you'd then have an infinite force back on the pusher
            return -1;
        }

        // The collision trigger at the opposite end to where we're being pushed
        TriggerTrainCollisions oppositeTrigger = pushedForward ? mFrontCollisionTrigger : mRearCollisionTrigger;
        if (oppositeTrigger.mTrainContents.Count > 0)
        {
            foreach (var trainEntityId in oppositeTrigger.mTrainContents)
            {
                var entityGo = Mc.Go.GetGo(trainEntityId);
                if (entityGo == null)
                    continue;
                if (entityGo is TrainCarGo { CompleteTrain: not null } trainCar)
                {
                    if (trainCar.MainTransform == null)
                        continue;

                    if (trainCar.CompleteTrain == this)
                        continue;

                    Vector3 theirForward = trainCar.MainTransform.forward;
                    float trainMass =
                        trainCar.CompleteTrain.GetTotalPushingMass(pushDirection, theirForward, ref prevTrains);
                    if (trainMass < 0)
                    {
                        // Static collision somewhere ahead of us, pass it up the chain
                        return -1;
                    }

                    result += trainMass;
                }
            }
        }

        foreach (Rigidbody rb in oppositeTrigger.mOtherRigidbodyContents)
        {
            result += rb.mass;
        }

        return result;
    }

    private float GetTotalPushingForces(Vector3 pushDirection, Vector3 ourForward, ref List<CompleteTrain> prevTrains)
    {
        float result = 0f;
        if (prevTrains.Count > 0)
        {
            if (prevTrains.Contains(this))
            {
                return 0;
            }

            // Don't add the force from the original CompleteTrain caller, but do it for sub-calls
            result += TotalForces;
        }
        prevTrains.Add(this);

        bool pushedForward = Vector3.Dot(ourForward, pushDirection) >= 0;
        TriggerTrainCollisions oppositeTrigger = pushedForward ? mFrontCollisionTrigger : mRearCollisionTrigger;
        if (!pushedForward) result *= -1;
        foreach (var trainEntityId in oppositeTrigger.mTrainContents)
        {
            IEntityGo entityGo = Mc.Go.GetGo(trainEntityId);
            if (entityGo is TrainCarGo { CompleteTrain: not null } trainCar)
            {
                if (trainCar.MainTransform == null)
                    continue;

                if (trainCar.CompleteTrain == this)
                    continue;

                Vector3 theirForward = trainCar.MainTransform.forward;
                result += trainCar.CompleteTrain.GetTotalPushingForces(pushDirection, theirForward, ref prevTrains);
            }
        }
        return result;
    }

    public void RegisterTrainCollisionCallBack(Action<bool> func)
    {
        mOnTrainCollisionCallBack += func;
    }

    public void StopEngineImmediate()
    {
        trackSpeed = 0;
    }
}