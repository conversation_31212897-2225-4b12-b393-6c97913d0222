{"hash": 1709585818, "data": [{"presetBuildingID": 12, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "ZhajiaMetal0506", "presetBuildConfigFile": "ZhajiaMetal0506config", "presetBuildReward": 100, "presetCleanRange": 20}, {"presetBuildingID": 13, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "ZhajiaMetal0506", "presetBuildConfigFile": "ZhajiaMetal0506config", "presetBuildReward": 101, "presetCleanRange": 20}, {"presetBuildingID": 14, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "ZhajiaDef0513", "presetBuildConfigFile": "ZhajiaDef0513Config", "presetBuildReward": 101, "presetCleanRange": 20}, {"presetBuildingID": 15, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "zhajiadefBIG0530", "presetBuildConfigFile": "zhajiadefBIG0530config", "presetBuildReward": 101, "presetCleanRange": 20}, {"presetBuildingID": 16, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "ZhajiaAtkBig0603", "presetBuildConfigFile": "ZhajiaAtkBig0603config", "presetBuildReward": 100, "presetCleanRange": 20}, {"presetBuildingID": 17, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "ZhaJiaAt2x2Stone", "presetBuildConfigFile": "ZhaJiaAt2x2StoneConfig", "presetBuildReward": 102, "presetCleanRange": 20}, {"presetBuildingID": 18, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "ZhajiaDef0914", "presetBuildConfigFile": "ZhajiaDef0914Config", "presetBuildReward": 101, "presetCleanRange": 50}, {"presetBuildingID": 19, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_RaidMode2", "presetBuildConfigFile": "wcfeng_RaidMode2Config", "presetBuildReward": 103, "presetCleanRange": 20}, {"presetBuildingID": 20, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_RaidMode1", "presetBuildConfigFile": "wcfeng_RaidMode1Config", "presetBuildReward": 104, "presetCleanRange": 20}, {"presetBuildingID": 21, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_RaidMode3", "presetBuildConfigFile": "wcfeng_RaidMode3Config", "presetBuildReward": 104, "presetCleanRange": 20}, {"presetBuildingID": 22, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_RaidMode4", "presetBuildConfigFile": "wcfeng_RaidMode4Config", "presetBuildReward": 103, "presetCleanRange": 20}, {"presetBuildingID": 23, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_RaidMode5", "presetBuildConfigFile": "wcfeng_RaidMode5Config", "presetBuildReward": 104, "presetCleanRange": 20}, {"presetBuildingID": 24, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_RaidMode7", "presetBuildConfigFile": "wcfeng_RaidMode7Config", "presetBuildReward": 104, "presetCleanRange": 20}, {"presetBuildingID": 25, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_RaidMode8", "presetBuildConfigFile": "wcfeng_RaidMode8Config", "presetBuildReward": 104, "presetCleanRange": 20}, {"presetBuildingID": 26, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_RaidMode9", "presetBuildConfigFile": "wcfeng_RaidMode9Config", "presetBuildReward": 104, "presetCleanRange": 20}, {"presetBuildingID": 27, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_raidmode_tier0_1", "presetBuildConfigFile": "wcfeng_raidmode_tier0_1Config", "presetBuildReward": 106, "presetCleanRange": 20}, {"presetBuildingID": 28, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_raidmode_tier0_2", "presetBuildConfigFile": "wcfeng_raidmode_tier0_2Config", "presetBuildReward": 105, "presetCleanRange": 20}, {"presetBuildingID": 29, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_raidmode_tier0_3", "presetBuildConfigFile": "wcfeng_raidmode_tier0_3Config", "presetBuildReward": 104, "presetCleanRange": 20}, {"presetBuildingID": 30, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_raidmode_tier0_1_collider", "presetBuildConfigFile": "wcfeng_raidmode_tier0_1Config", "presetBuildReward": 106, "presetCleanRange": 20}, {"presetBuildingID": 31, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_raidmode_tier3_3", "presetBuildConfigFile": "wcfeng_raidmode_tier3_3Config", "presetBuildReward": 104, "presetCleanRange": 20}, {"presetBuildingID": 32, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_raidmode_tier3_4", "presetBuildConfigFile": "wcfeng_raidmode_tier3_4Config", "presetBuildReward": 103, "presetCleanRange": 20}, {"presetBuildingID": 33, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_raidmode_tier3_5", "presetBuildConfigFile": "wcfeng_raidmode_tier3_5Config", "presetBuildReward": 104, "presetCleanRange": 20}, {"presetBuildingID": 35, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "lchengy_RaidMode_Barrier", "presetBuildConfigFile": "lchengy_RaidMode_BarrierConfig", "presetBuildReward": 104, "presetCleanRange": 20}, {"presetBuildingID": 34, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_RaidMode9_collider", "presetBuildConfigFile": "wcfeng_RaidMode9Config", "presetBuildReward": 104, "presetCleanRange": 20}, {"presetBuildingID": 1000, "presetBuildingLevel": 1, "staticCheckTag": 0, "presetBuildFile": "StandardBase_1_test", "presetBuildConfigFile": "StandardBase_1_testConfig", "presetBuildReward": 9999, "presetCleanRange": 20}, {"presetBuildingID": 2000, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "StandardBase_2_test", "presetBuildConfigFile": "StandardBase_2_testConfig", "presetBuildReward": 9998, "presetCleanRange": 30}, {"presetBuildingID": 3000, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "StandardBase_3_test", "presetBuildConfigFile": "StandardBase_3_testConfig", "presetBuildReward": 9997, "presetCleanRange": 80}, {"presetBuildingID": 1001, "presetBuildingLevel": 1, "staticCheckTag": 0, "presetBuildFile": "level1_2x2_ladder", "presetBuildConfigFile": "level1_2x2_ladderConfig", "presetBuildReward": 4001, "presetCleanRange": 20}, {"presetBuildingID": 1002, "presetBuildingLevel": 1, "staticCheckTag": 0, "presetBuildFile": "level1_2x2_ladder2", "presetBuildConfigFile": "level1_2x2_ladder2Config", "presetBuildReward": 4001, "presetCleanRange": 20}, {"presetBuildingID": 1003, "presetBuildingLevel": 1, "staticCheckTag": 0, "presetBuildFile": "level1_2x2_roofup", "presetBuildConfigFile": "level1_2x2_roofupConfig", "presetBuildReward": 4001, "presetCleanRange": 20}, {"presetBuildingID": 1004, "presetBuildingLevel": 1, "staticCheckTag": 0, "presetBuildFile": "level1_2x2_vulnerablebase", "presetBuildConfigFile": "level1_2x2_vulnerablebaseConfig", "presetBuildReward": 4001, "presetCleanRange": 20}, {"presetBuildingID": 1005, "presetBuildingLevel": 1, "staticCheckTag": 0, "presetBuildFile": "level1_2x2_vulnerableroof", "presetBuildConfigFile": "level1_2x2_vulnerableroofConfig", "presetBuildReward": 4001, "presetCleanRange": 20}, {"presetBuildingID": 1006, "presetBuildingLevel": 1, "staticCheckTag": 0, "presetBuildFile": "level1_2x2_vulnerablewall", "presetBuildConfigFile": "level1_2x2_vulnerablewallConfig", "presetBuildReward": 4001, "presetCleanRange": 20}, {"presetBuildingID": 1007, "presetBuildingLevel": 1, "staticCheckTag": 0, "presetBuildFile": "level1_4x4_wcfeng2", "presetBuildConfigFile": "level1_4x4_wcfeng2Config", "presetBuildReward": 4001, "presetCleanRange": 20}, {"presetBuildingID": 1008, "presetBuildingLevel": 1, "staticCheckTag": 0, "presetBuildFile": "level1_2x2_wcfeng3", "presetBuildConfigFile": "level1_2x2_wcfeng3Config", "presetBuildReward": 4001, "presetCleanRange": 20}, {"presetBuildingID": 1009, "presetBuildingLevel": 1, "staticCheckTag": 0, "presetBuildFile": "level1_2x3_wcfeng1", "presetBuildConfigFile": "level1_2x3_wcfeng1Config", "presetBuildReward": 4001, "presetCleanRange": 20}, {"presetBuildingID": 1010, "presetBuildingLevel": 1, "staticCheckTag": 0, "presetBuildFile": "level1_wcfeng4", "presetBuildConfigFile": "level1_wcfeng4Config", "presetBuildReward": 4001, "presetCleanRange": 20}, {"presetBuildingID": 1011, "presetBuildingLevel": 1, "staticCheckTag": 0, "presetBuildFile": "level1_wcfeng5", "presetBuildConfigFile": "level1_wcfeng5Config", "presetBuildReward": 4001, "presetCleanRange": 20}, {"presetBuildingID": 1012, "presetBuildingLevel": 1, "staticCheckTag": 0, "presetBuildFile": "level1_wcfeng6", "presetBuildConfigFile": "level1_wcfeng6Config", "presetBuildReward": 4001, "presetCleanRange": 20}, {"presetBuildingID": 1013, "presetBuildingLevel": 1, "staticCheckTag": 0, "presetBuildFile": "level1_wcfeng7", "presetBuildConfigFile": "level1_wcfeng7Config", "presetBuildReward": 4001, "presetCleanRange": 20}, {"presetBuildingID": 1014, "presetBuildingLevel": 1, "staticCheckTag": 0, "presetBuildFile": "level1_wcfeng8", "presetBuildConfigFile": "level1_wcfeng8Config", "presetBuildReward": 4001, "presetCleanRange": 20}, {"presetBuildingID": 1015, "presetBuildingLevel": 1, "staticCheckTag": 0, "presetBuildFile": "level1_wcfeng9", "presetBuildConfigFile": "level1_wcfeng9Config", "presetBuildReward": 4001, "presetCleanRange": 20}, {"presetBuildingID": 1016, "presetBuildingLevel": 1, "staticCheckTag": 0, "presetBuildFile": "level1_wcfeng10", "presetBuildConfigFile": "level1_wcfeng10Config", "presetBuildReward": 4001, "presetCleanRange": 20}, {"presetBuildingID": 1017, "presetBuildingLevel": 1, "staticCheckTag": 0, "presetBuildFile": "level1_wcfeng11", "presetBuildConfigFile": "level1_wcfeng11Config", "presetBuildReward": 4001, "presetCleanRange": 20}, {"presetBuildingID": 2001, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "level2_4x4_solid1", "presetBuildConfigFile": "level2_4x4_solid1Config", "presetBuildReward": 4002, "presetCleanRange": 30}, {"presetBuildingID": 2002, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "level2_3x2_solid2", "presetBuildConfigFile": "level2_3x2_solid2Config", "presetBuildReward": 4002, "presetCleanRange": 30}, {"presetBuildingID": 2003, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "level2_3x2_solid3", "presetBuildConfigFile": "level2_3x2_solid3Config", "presetBuildReward": 4002, "presetCleanRange": 30}, {"presetBuildingID": 2004, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "level2_3x2_trap1", "presetBuildConfigFile": "level2_3x2_trap1Config", "presetBuildReward": 4002, "presetCleanRange": 30}, {"presetBuildingID": 2005, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "level2_3x2_trap2", "presetBuildConfigFile": "level2_3x2_trap2Config", "presetBuildReward": 4002, "presetCleanRange": 30}, {"presetBuildingID": 2006, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "level2_4x4_wcfeng1", "presetBuildConfigFile": "level2_4x4_wcfeng1Config", "presetBuildReward": 4002, "presetCleanRange": 30}, {"presetBuildingID": 2007, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "level2_wcfeng3", "presetBuildConfigFile": "level2_wcfeng3Config", "presetBuildReward": 4002, "presetCleanRange": 30}, {"presetBuildingID": 2008, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "level2_5x5_wcfeng2", "presetBuildConfigFile": "level2_5x5_wcfeng2Config", "presetBuildReward": 4002, "presetCleanRange": 30}, {"presetBuildingID": 2009, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "level2_wcfeng4", "presetBuildConfigFile": "level2_wcfeng4Config", "presetBuildReward": 4002, "presetCleanRange": 30}, {"presetBuildingID": 2010, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "level2_wcfeng5_1", "presetBuildConfigFile": "level2_wcfeng5_1Config", "presetBuildReward": 4002, "presetCleanRange": 30}, {"presetBuildingID": 2011, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "level2_wcfeng5_2", "presetBuildConfigFile": "level2_wcfeng5_2Config", "presetBuildReward": 4002, "presetCleanRange": 30}, {"presetBuildingID": 2012, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "level2_wcfeng5_3", "presetBuildConfigFile": "level2_wcfeng5_3Config", "presetBuildReward": 4002, "presetCleanRange": 30}, {"presetBuildingID": 2013, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "level2_wcfeng6", "presetBuildConfigFile": "level2_wcfeng6Config", "presetBuildReward": 4002, "presetCleanRange": 30}, {"presetBuildingID": 2014, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "level2_wcfeng7", "presetBuildConfigFile": "level2_wcfeng7Config", "presetBuildReward": 4002, "presetCleanRange": 30}, {"presetBuildingID": 2015, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "level2_wcfeng8", "presetBuildConfigFile": "level2_wcfeng8Config", "presetBuildReward": 4002, "presetCleanRange": 30}, {"presetBuildingID": 2016, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "level2_wcfeng9", "presetBuildConfigFile": "level2_wcfeng9Config", "presetBuildReward": 4002, "presetCleanRange": 30}, {"presetBuildingID": 2017, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "level2_wcfeng10", "presetBuildConfigFile": "level2_wcfeng10Config", "presetBuildReward": 4002, "presetCleanRange": 30}, {"presetBuildingID": 2018, "presetBuildingLevel": 2, "staticCheckTag": 0, "presetBuildFile": "level2_wcfeng11", "presetBuildConfigFile": "level2_wcfeng11Config", "presetBuildReward": 4002, "presetCleanRange": 30}, {"presetBuildingID": 3004, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "level3_5x5_wcfeng1", "presetBuildConfigFile": "level3_5x5_wcfeng1Config", "presetBuildReward": 4003, "presetCleanRange": 50}, {"presetBuildingID": 3005, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "level3_5x5_wcfeng2", "presetBuildConfigFile": "level3_5x5_wcfeng2Config", "presetBuildReward": 4003, "presetCleanRange": 50}, {"presetBuildingID": 3006, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "level3_5x5_wcfeng2_1", "presetBuildConfigFile": "level3_5x5_wcfeng2_1Config", "presetBuildReward": 4003, "presetCleanRange": 50}, {"presetBuildingID": 3007, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "level3_5x5_wcfeng2_2", "presetBuildConfigFile": "level3_5x5_wcfeng2_2Config", "presetBuildReward": 4003, "presetCleanRange": 50}, {"presetBuildingID": 3008, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "level3_5x5_wcfeng2_3", "presetBuildConfigFile": "level3_5x5_wcfeng2_3Config", "presetBuildReward": 4003, "presetCleanRange": 50}, {"presetBuildingID": 3009, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "level3_5x5_wcfeng2_4", "presetBuildConfigFile": "level3_5x5_wcfeng2_4Config", "presetBuildReward": 4003, "presetCleanRange": 50}, {"presetBuildingID": 3010, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "level3_5x5_wcfeng2_5", "presetBuildConfigFile": "level3_5x5_wcfeng2_5Config", "presetBuildReward": 4003, "presetCleanRange": 50}, {"presetBuildingID": 3011, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "level3_5x5_wcfeng6", "presetBuildConfigFile": "level3_5x5_wcfeng6Config", "presetBuildReward": 4003, "presetCleanRange": 50}, {"presetBuildingID": 3012, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "level3_5x5_wcfeng7", "presetBuildConfigFile": "level3_5x5_wcfeng7Config", "presetBuildReward": 4003, "presetCleanRange": 50}, {"presetBuildingID": 3013, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "level3_wcfeng14", "presetBuildConfigFile": "level3_wcfeng14Config", "presetBuildReward": 4003, "presetCleanRange": 50}, {"presetBuildingID": 3014, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "level3_5x5_wcfeng15", "presetBuildConfigFile": "level3_5x5_wcfeng15Config", "presetBuildReward": 4003, "presetCleanRange": 50}, {"presetBuildingID": 3015, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "level3_wcfeng17", "presetBuildConfigFile": "level3_wcfeng17Config", "presetBuildReward": 4003, "presetCleanRange": 50}, {"presetBuildingID": 3016, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "level3_wcfeng18", "presetBuildConfigFile": "level3_wcfeng18Config", "presetBuildReward": 4003, "presetCleanRange": 50}, {"presetBuildingID": 401, "presetBuildingLevel": 4, "staticCheckTag": 0, "presetBuildFile": "level3_5x5_wcfeng13", "presetBuildConfigFile": "level3_5x5_wcfeng13Config", "presetBuildReward": 4003, "presetCleanRange": 50}, {"presetBuildingID": 402, "presetBuildingLevel": 4, "staticCheckTag": 0, "presetBuildFile": "level3_5x5_wcfeng12", "presetBuildConfigFile": "level3_5x5_wcfeng12Config", "presetBuildReward": 4003, "presetCleanRange": 50}, {"presetBuildingID": 403, "presetBuildingLevel": 4, "staticCheckTag": 0, "presetBuildFile": "level3_5x5_wcfeng11", "presetBuildConfigFile": "level3_5x5_wcfeng11Config", "presetBuildReward": 4003, "presetCleanRange": 50}, {"presetBuildingID": 404, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "level3_5x5_wcfeng5", "presetBuildConfigFile": "level3_5x5_wcfeng5Config", "presetBuildReward": 4003, "presetCleanRange": 50}, {"presetBuildingID": 4001, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "XNCS_home1", "presetBuildConfigFile": "XNCS_home1_cofig", "presetBuildReward": 6001, "presetCleanRange": 100}, {"presetBuildingID": 4002, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "XNCS_home3", "presetBuildConfigFile": "XNCS_home3_cofig", "presetBuildReward": 6002, "presetCleanRange": 100}, {"presetBuildingID": 4003, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "XNCS_home2", "presetBuildConfigFile": "XNCS_home2_config", "presetBuildReward": 6004, "presetCleanRange": 100}, {"presetBuildingID": 4004, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "XNCS_home10", "presetBuildConfigFile": "XNCS_home10_config", "presetBuildReward": 6004, "presetCleanRange": 100}, {"presetBuildingID": 4005, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "XNCS_home5", "presetBuildConfigFile": "XNCS_home5_cofig", "presetBuildReward": 6005, "presetCleanRange": 100}, {"presetBuildingID": 4006, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "TARPROOM1", "presetBuildConfigFile": "TARPROOM1_cofig", "presetBuildReward": 4006, "presetCleanRange": 1}, {"presetBuildingID": 4007, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "XNCS_home10_iron", "presetBuildConfigFile": "XNCS_home10_iron_config", "presetBuildReward": 6004, "presetCleanRange": 100}, {"presetBuildingID": 4008, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "XNCS_home12", "presetBuildConfigFile": "XNCS_home12Config", "presetBuildReward": 104, "presetCleanRange": 100}, {"presetBuildingID": 4009, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "XNCS_HugeHome_beta1", "presetBuildConfigFile": "XNCS_HugeHome_beta1_Config", "presetBuildReward": 6001, "presetCleanRange": 100}, {"presetBuildingID": 4010, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "XNCS_home_break_repair", "presetBuildConfigFile": "XNCS_home_break_repair_config", "presetBuildReward": 6010, "presetCleanRange": 100}, {"presetBuildingID": 4011, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "anti_aircraft_home", "presetBuildConfigFile": "anti_aircraft_home_config", "presetBuildReward": 6004, "presetCleanRange": 100}, {"presetBuildingID": 4012, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "XNCS_home_wcfeng15", "presetBuildConfigFile": "XNCS_home_wcfeng15_config", "presetBuildReward": 6004, "presetCleanRange": 100}, {"presetBuildingID": 4013, "presetBuildingLevel": 3, "staticCheckTag": 0, "presetBuildFile": "XNCS_home_wcfeng7", "presetBuildConfigFile": "XNCS_home_wcfeng7_config", "presetBuildReward": 6004, "presetCleanRange": 100}, {"presetBuildingID": 5001, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_entrylevel1", "presetBuildConfigFile": "wcfeng_entrylevel1Config", "presetBuildReward": 103, "presetCleanRange": 20}, {"presetBuildingID": 5002, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_entrylevel2", "presetBuildConfigFile": "wcfeng_entrylevel2Config", "presetBuildReward": 103, "presetCleanRange": 20}, {"presetBuildingID": 5003, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_entrylevel4", "presetBuildConfigFile": "wcfeng_entrylevel4Config", "presetBuildReward": 103, "presetCleanRange": 20}, {"presetBuildingID": 5004, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "wcfeng_entrylevel5", "presetBuildConfigFile": "wcfeng_entrylevel5Config", "presetBuildReward": 103, "presetCleanRange": 20}, {"presetBuildingID": 5005, "presetBuildingLevel": 0, "staticCheckTag": 0, "presetBuildFile": "XNCS_home10_trap", "presetBuildConfigFile": "XNCS_home10_trap_config", "presetBuildReward": 4006, "presetCleanRange": 100}]}