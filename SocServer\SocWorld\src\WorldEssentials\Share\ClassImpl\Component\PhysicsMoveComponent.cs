//#define PHYSICSMOVECOMPONENT_DEBUG
#if !SOC_BOT_SERVER && (SOC_SIMULATOR || SOC_CLIENT)
using SharedUnity;
using System;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Combat;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.Synchronization;
using WizardGames.Soc.Common.Systems;
using WizardGames.Soc.Common.Unity.Config;
using WizardGames.Soc.Common.Unity.Contexts;
using WizardGames.Soc.Common.Unity.Go;
using WizardGames.Soc.Common.Unity.Manager;
using WizardGames.Soc.Common.Unity.Utility;
using WizardGames.Soc.Common.Weapon;
using WizardGames.Soc.Procedural;
using Quaternion = UnityEngine.Quaternion;
using Vector3 = UnityEngine.Vector3;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocSimulator.CommonUnity.Runtime.Utility;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Unity.Entity;



#if SOC_CLIENT
using WizardGames.Soc.SocClient.Trigger;
#else
using WizardGames.Soc.SocSimulator.Trigger;
#endif

namespace WizardGames.Soc.Common.Component
{
    public partial class PhysicsMoveComponent : BaseComponentLocal, IPhysicsCastWithJob
    {
        public override int Id => (int)EComponentIdEnum.PhysicsMove;

        public override string GetTypeName() => "PhysicsMoveComponent";

        public enum MoveState
        {
            FreeFall,
            AttachIdle,
            NormalIdle,
            ContinuousCheckIdle,
        }

        private static readonly SocLogger logger = LogHelper.GetLogger(typeof(PhysicsMoveComponent));
        
        private const float k_CheckUnderGroundTime = 0.1f;

        //碰撞类型列表
        private static List<int> collisionEntityList;
        
        private static List<int> ignorePlatformSpeedList;

        private RaycastHit[] tmpHits = new RaycastHit[16];
        private List<RaycastHit> tmpHitsList = new List<RaycastHit>();
        private List<SocRaycastHit> tmpSocHitsList = new List<SocRaycastHit>();

        private Collider[] tmpColliders = new Collider[16];

        private Vector3 velocity;

        public Vector3 Velocity { get => velocity; set => velocity = value; }

        //最大掉落速度
        private float maxGravitySpeed;

        private float minGravitySpeed;

        private long ignoreCollisionId;
        
        private long ignoreEntityId;

        private bool ignorePlayer;

        private bool useCommonCollision;

        private bool rotateLandGround;

        private bool reflectInfluceY;

        //反弹系数
        private float reboundCoefficient;

        //碰撞层级
        private int layerMask;

        //处理浮力
        private bool canBuoyancy;

        private bool handleOverlapScene;

        //只要碰撞到就停下
        private bool stopWhenHit;

        //自身吸附列表,用于直接吸附到物体上
        private List<int> adsorptionList;

        //落地处理
        public Action<SocRaycastHit, Collider> OnLandGround;

        public Action<float, bool> OnPhysicsMove;

        public Action AfterStopMove;

        public Action AfterSyncTransform;

        //碰撞处理
        public Action<SocRaycastHit> OnCollisionHit;

        //吸附处理 实体id，实体类型，是否吸附
        public Action<long, int, bool> OnAttached;

        private int colliderType;

        private Collider baseCollider;

        private CapsuleCollider capsuleCollider;

        private BoxCollider boxCollider;

        private SphereCollider sphereCollider;

        private long attachedId;

        public long AttachedId
        {
            get => attachedId;
            set => attachedId = value;
        }

        private bool isLand;

        public bool IsLand => isLand;

        //本地记录的位置
        private Vector3 worldPosition;
        public Vector3 WorldPosition => worldPosition;
        private Vector3 localPosition;
        private Quaternion worldRotation;
        public Quaternion WorldRotation => worldRotation;
        private Quaternion localRotation;
        private Vector3 lastWorldPosition;
        private Quaternion lastWorldRotation;

        //吸附上的父节点
        private Transform attachParent;
        //传送带组件
        private TriggerForce triggerForce;
        private Vector3 triggerForceVelocity;
        //用于同步位置的实体
        private IPositionEntity positionEntity => ParentEntity as IPositionEntity;
        private IRotation3Entity rotation3Entity => ParentEntity as IRotation3Entity;

        private MoveState curState;

        public MoveState CurState
        {
            get => curState;
            set => curState = value;
        }

        private Vector3 lastCheckWorldPos;
        private float checkUnderGroundTimer;

        private bool UseCoordinateTrans = false;//是否需要坐标转换(由于在运动载具上扔的手雷在车厢中运动时,服务端车厢比客户端位置要领先,因此要将雷的位置往前方车厢拉,以保持碰撞结果和反弹方向和客户端保持一致).
        public delegate bool OnGetUCT();
        public OnGetUCT OnGetUseCoordinateTrans;
        public delegate Vector3 OnGetNewHistoryVector3(Vector3 v3);
        public OnGetNewHistoryVector3 OnGetNewPos, OnGetNewVelocity, OnGetRelativePos, OnGetHistoryVelocity;

        private bool needOverlapScene = false;
        private bool isHitScreenBox = false;

        private bool manageBySystem = false;

        private float castOffsetY = 0;
        
        public PhysicsShapeType shapeType { get; set; }
        private int castJobHitCount;
        private bool isAirDropBox = false;
        private bool isDropBox = false;
        //是否反弹
        private bool isRebound = false;

        private void Add2PhysicsSystem()
        {
            if (!manageBySystem)
            {
                var physicsMoveSystem = McCommon.System.GetBaseSystem<PhysicsMoveSystem>();
                physicsMoveSystem?.AddComponent(this);
                manageBySystem = true;
            }
        }
        
        private void RemoveFromPhysicsSystem(bool delayRemove)
        {
            if (manageBySystem)
            {
                var physicsMoveSystem = McCommon.System.GetBaseSystem<PhysicsMoveSystem>();
                physicsMoveSystem?.RemoveComponent(this, delayRemove);
                manageBySystem = false;
            }
        }

        /// <summary>
        /// 初始化物理数据
        /// </summary>
        /// <param name="baseCollider">资源GO collider，并非实例化</param>
        /// <param name="initGravity">是否要移动</param>
        /// <param name="velocity">初始速度</param>
        /// <param name="canBuoyancy">是否有浮力</param>
        /// <param name="handleOverlapScene">是否抬高</param>
        /// <param name="stopWhenHit">只要碰撞就停止</param>
        /// <param name="layerMask">碰撞层级掩码</param>
        /// <param name="selfAdsorptionList">自身吸附列表</param>
        /// <param name="rotateLandGround">落地时，是否需要旋转</param>
        /// <param name="rc">反弹系数</param>
        /// <param name="maxGravitySpeed">最大重力速度</param>
        /// <param name="minGravitySpeed">最小重力速度</param>
        public void InitPhysics(Collider baseCollider, bool initGravity, Vector3 velocity, bool canBuoyancy, bool handleOverlapScene, bool stopWhenHit,
            int layerMask, List<int> selfAdsorptionList = null, bool rotateLandGround = true, float rc = 1.0f, float maxGravitySpeed = 100, float minGravitySpeed = -100, 
            long ignoreCollisionId = -1, bool ignorePlayer = true, bool useCommonCollision = true, bool reflectInfluceY = true)
        {
            IsActive = true;
            needOverlapScene = true;
            isHitScreenBox = false;
            if (collisionEntityList == null)
            {
                collisionEntityList = new List<int>(16);
                //记录停止的物体 用于删除时候重新激活
                var types = McCommon.Tables.TbGlobalConfig.AdsorptionTypeDrop;
                foreach (int type in types)
                {
                    var entityType = HeldItemUtility.GetEntityTypeByCfgID(type);
                    collisionEntityList.Add(entityType);
                }
            }

            if (ignorePlatformSpeedList == null)
            {
                ignorePlatformSpeedList = new List<int>(8);
                //记录停止的物体 用于删除时候重新激活
                var types = McCommon.Tables.TbGlobalConfig.IgnorePlatformSpeedType;
                foreach (int type in types)
                {
                    var entityType = HeldItemUtility.GetEntityTypeByCfgID(type);
                    ignorePlatformSpeedList.Add(entityType);
                }
            }

            worldPosition = new Vector3(positionEntity.PosX, positionEntity.PosY, positionEntity.PosZ);
            localPosition = Vector3.zero;
            worldRotation = Quaternion.Euler(rotation3Entity.RotateX, rotation3Entity.RotateY, rotation3Entity.RotateZ);
            localRotation = Quaternion.identity;
            lastWorldPosition = worldPosition;
            lastWorldRotation = worldRotation;
            lastCheckWorldPos = worldPosition;
            this.baseCollider = baseCollider;
            this.velocity = velocity;
            this.canBuoyancy = canBuoyancy;
            this.handleOverlapScene = handleOverlapScene;
            this.stopWhenHit = stopWhenHit;
            this.layerMask = layerMask;
            this.adsorptionList = selfAdsorptionList;
            this.rotateLandGround = rotateLandGround;
            this.reboundCoefficient = rc;
            this.maxGravitySpeed = maxGravitySpeed;
            this.minGravitySpeed = minGravitySpeed;
            this.ignoreCollisionId = ignoreCollisionId;
            this.ignorePlayer = ignorePlayer;
            this.useCommonCollision = useCommonCollision;
            this.ignoreEntityId = -1;
            this.reflectInfluceY = reflectInfluceY;
            InitCollider(handleOverlapScene);

            if (colliderType > 0 && initGravity)
            {
                isLand = false;
                curState = MoveState.FreeFall;
                if (this.velocity.Equals(Vector3.zero))
                {
                    this.velocity.y += Physics.gravity.y * 0.033f;
                }

                Add2PhysicsSystem();
            }
            else
            {
                isLand = true;
                curState = MoveState.NormalIdle;
                RemoveFromPhysicsSystem(true);
                AfterStopMove?.Invoke();
            }

            if (OnGetUseCoordinateTrans != null)
            {
                UseCoordinateTrans = OnGetUseCoordinateTrans();
            }

            if (ParentEntity is IAttachedEntity attachedEntity && attachedEntity.TargetEntityID <= 0)
            {
                //处理出生时在移动载具内部时的情况
                CheckEnterMovingEntity();
            }

            logger.InfoFormat("StartPhysicsMove entity {0} entityType {1} pos {2} velocity {3}", ParentId, ParentEntity.EntityType, worldPosition, velocity);
        }

        //需要在InitPhysics后再调用
        public bool TryManualAdsorp(long targetEntityId, int colliderIndex, Vector3 localPos, Quaternion localRot)
        {
            if (!IsActive)
            {
                logger.ErrorFormat("TryManualAdsorp entity {0} is not active", ParentId);
            }
            var mgrEntityGo = ((UnityContext)(McCommon.System.Context)).BaseMgrEntityGo;
            var ownerEntity = McCommon.GetEntityFunc.Invoke(targetEntityId);
            var entityGo = mgrEntityGo.GetGo(targetEntityId);
            if (ownerEntity == null || entityGo == null)
            {
                return false;
            }
            bool isAttachMountable = false;
            if (ownerEntity is IBaseMountableEntity)
            {
                isAttachMountable = true;
                attachParent = entityGo.MainGo.transform;
            }
            else if(colliderIndex >= 0)
            {
                var collider = McCommon.Collider.GetCollider(targetEntityId, colliderIndex);
                attachParent = collider.transform;
            }
            else
            {
                if (entityGo is IPlatformGo platformGo)
                {
                    attachParent = platformGo.GetPlaneTransform();
                }
                else
                {
                    attachParent = entityGo.MainGo.transform;
                }
            }

            if (attachParent == null)
            {
                return false;
            }

            localPosition = localPos;
            localRotation = localRot;
            attachedId = targetEntityId;
            
            if (ParentEntity is IAttachedEntity attachedEntity)
            {
                attachedEntity.TargetEntityID = targetEntityId;
                attachedEntity.TargetColliderIndex = colliderIndex;
                attachedEntity.AttachedTransform = attachParent;
                if (isAttachMountable)
                {
                    attachedEntity.TargetColliderIndex = -1;
                }
            }
            worldPosition = attachParent.TransformPoint(localPosition);
            worldRotation = attachParent.rotation * localRotation;
            InitCollider(handleOverlapScene);
            localPosition = attachParent.InverseTransformPoint(worldPosition);
            
            SyncLocalPositionAndRotation();
            
            mgrEntityGo.AddChild(targetEntityId, ParentId);
            //
            // int count = Physics.RaycastNonAlloc(worldPosition + Vector3.up * GetColliderHeight(),
            //     Vector3.down, tmpHits, GetColliderHeight() + 1f, layerMask,
            //     QueryTriggerInteraction.Ignore);
            // tmpHitsList.Clear();
            // for (int j = 0; j < count; j++)
            // {
            //     tmpHitsList.Add(tmpHits[j]);
            // }
            // tmpHitsList.Sort((left, right)=> left.distance.CompareTo(right.distance));
            //
            // var hit = tmpHitsList.Count > 0 ? tmpHitsList[0] : new RaycastHit();
            // SocRaycastHit hitInfo = new SocRaycastHit(hit);
            // OnLandGround?.Invoke(hitInfo, baseCollider);
            //
            // SyncLocalPositionAndRotation();
            // velocity = Vector3.zero;
            // isLand = true;
            // curState = MoveState.AttachIdle;

            return true;
        }
        

        private void RestartFall()
        {
            checkUnderGroundTimer = 0;
            if (isLand)
            {
                isLand = false;
                curState = MoveState.FreeFall;
            }

            needOverlapScene = true;
        }

        public void ParentRemove()
        {
            logger.InfoFormat("entityId：{0} is DeAttached", ParentId);
            var attachEntity = McCommon.GetEntityFunc.Invoke(attachedId);
            if (attachEntity != null)
            {
                if (IsColliderMovingEntity(attachEntity.EntityType, attachEntity.EntityId))
                {
                     velocity += GetMovingEntityVelocity(attachedId);
                }
            }
            if (ParentEntity is IAttachedEntity attachedEntity)
            {
                attachedEntity.TargetEntityID = 0;
                attachedEntity.TargetColliderIndex = 0;
                attachedEntity.AttachedTransform = null;
            }
            attachedId = 0;
            OnAttached?.Invoke(0, 0, false);
            attachParent = null;
            RestartFall();
        }

        public CastPhysicsCommandData CollectCastCommandData(float deltaTime)
        {
            if (OnGetUseCoordinateTrans != null)
            {
                UseCoordinateTrans = OnGetUseCoordinateTrans();
            }
            
            CastPhysicsCommandData commandData = new CastPhysicsCommandData();
            commandData.position = UseCoordinateTrans ? OnGetNewPos(worldPosition) : worldPosition;
            commandData.rotation = worldRotation;
            commandData.direction =
                UseCoordinateTrans ? OnGetNewVelocity(velocity).normalized : velocity.normalized;
            commandData.distance = velocity.magnitude * deltaTime + castOffsetY;
            commandData.layerMask = layerMask;
            commandData.shapeType = shapeType;
            if (canBuoyancy)
            {
                commandData.layerMask |= MgrConfigPhysicsLayer.MaskWater;
            }
            commandData.hitTriggers = QueryTriggerInteraction.Collide;
            
            switch (shapeType)
            {
                case PhysicsShapeType.Capsule:
                    UnityUtility.CapsuleColliderEndCaps(capsuleCollider, commandData.position, commandData.rotation,
                        out commandData.point0,
                        out commandData.point1);
                    commandData.radius = capsuleCollider.radius;
                    break;
                case PhysicsShapeType.Box:
                    commandData.position += boxCollider.center;
                    commandData.boundsExtents = boxCollider.size / 2;
                    break;
                case PhysicsShapeType.Sphere:
                    commandData.position += sphereCollider.center;
                    commandData.radius = sphereCollider.radius;
                    break;
            }

            return commandData;
        }

        public void CastJobFinished(RaycastResultEnumerator raycastResultEnumerator)
        {
            castJobHitCount = 0;
            while (raycastResultEnumerator.HasNextHit(out var hit))
            {
                tmpHits[castJobHitCount++] = hit;
            }
        }

        //处理物理移动
        public void PhysicsMove(IChangeWorld changeWorld, float dt)
        {
            if (OnGetUseCoordinateTrans != null)
            {
                UseCoordinateTrans = OnGetUseCoordinateTrans();
            }

            SyncEntityPositionAndRotation();

            //获得由移动平台带来的位移和旋转
            if (attachParent != null)
            {
                worldPosition = attachParent.TransformPoint(localPosition);
                worldRotation = attachParent.rotation * localRotation;
            }

            if (curState == MoveState.FreeFall)
            {
                FreeFallMove(changeWorld, dt);
            }
            else if (curState == MoveState.ContinuousCheckIdle)
            {
                ContinuousCheckGround();
            }
            else if (curState == MoveState.AttachIdle)
            {
                SyncPositionAndRotation();
            }
        }

        private void SyncEntityPositionAndRotation()
        {
            Vector3 entityPosition = new Vector3(positionEntity.PosX, positionEntity.PosY, positionEntity.PosZ);
            if (!worldPosition.Equals(entityPosition))
            {
                if (attachParent != null)
                {
                    localPosition = attachParent.InverseTransformPoint(entityPosition);
                }
                worldPosition = entityPosition;
            }

            Quaternion entityRotation = Quaternion.Euler(rotation3Entity.RotateX, rotation3Entity.RotateY, rotation3Entity.RotateZ);
            if (worldRotation.Equals(entityRotation))
            {
                if (attachParent != null)
                {
                    localRotation = Quaternion.Inverse(attachParent.rotation) * entityRotation;
                }
                worldRotation = entityRotation;
            }
        }

        
        private void ContinuousCheckGround()
        {
            var hitCount = CollisionCheck(worldRotation, worldPosition, Vector3.down, 0.2f, false);
            if (hitCount == 0)
            {
                RestartFall();
            }
            else
            {
                bool hitAllScene = true;
                for (int j = 0; j < hitCount; j++)
                {
                    var hitInfo = tmpSocHitsList[j];
                    if (hitInfo.Collider.isTrigger)
                    {
                        continue;
                    }
                    if (hitInfo.Distance < 0.1f && !MgrConfigPhysicsLayer.MaskHasLayer(hitInfo.collider.gameObject.layer, MgrConfigPhysicsLayer.MaskScene))
                    {
                        hitAllScene = false;
                        break;
                    }
                }

                if (hitAllScene)
                {
                    RestartFall();
                }
            }
        }
        
        private void FreeFallMove(IChangeWorld changeWorld, float dt)
        {
            var costTime = dt;

#if SOC_SIMULATOR
            if (worldPosition.y < -500)
            {
                logger.ErrorFormat("go posY < -500, so delete it. entityId：{0} entityType：{1}。posX：{2} posZ：{3}", ParentEntity.EntityId, ParentEntity.EntityType, worldPosition.x, worldPosition.z);
                changeWorld.DeleteEntity(ParentId, "go posY < -500");
                return;
            }  
#endif

            for (int i = 0; i < 3; i++)
            {
                var originVelocity = velocity;
                var dir = velocity * costTime;
                var dis = dir.magnitude;
                var velocityNormal = originVelocity.normalized;
                var quaternion = worldRotation;
#if SOC_SIMULATOR
                bool isFirstCheck = i == 0;
#else
                bool isFirstCheck = false;
#endif
                int hitCount = CollisionCheck(quaternion,
                    UseCoordinateTrans ? OnGetNewPos(worldPosition) : worldPosition,
                    UseCoordinateTrans ? OnGetNewVelocity(velocity).normalized : velocity.normalized,
                    dis, isFirstCheck);
                
                // logger.Info("entity " + ParentId + "pos:" + pos + " velocity " + velocity + " deltaTime " +
                //             Time.deltaTime + " frameCount " + Time.frameCount);

                bool isHit = false;
                SocRaycastHit hitInfo = new SocRaycastHit();
                bool isHitEntity = false;
                IEntity hitEntity = null;
                long hitEntityId = 0;
                int hitEntityType = 0;
                for (int j = 0; j < hitCount; j++)
                {
                    hitInfo = tmpSocHitsList[j];
                    isHitEntity = TryGetColliderEntityId(hitInfo.ColliderGo, out hitEntity, out hitEntityId,
                        out hitEntityType);

                    if (isAirDropBox && hitInfo.ColliderGo.CompareTag("AirDropBox"))
                    {
                        continue;
                    }

                    if (isHitEntity && (hitEntityId == ignoreCollisionId || hitEntityId == ignoreEntityId))
                    {
                        continue;
                    }

                    if (ignorePlayer && hitEntityType == EntityTypeId.PlayerEntity)
                    {
                        continue;
                    }

                    if (isHitEntity && hitEntity is CorpseEntity corpseEntity && !corpseEntity.IsGibs)
                    {
                        continue;   
                    }

                    //如果是触发器，并且没有碰到怪物和水面，就跳过
                    if (hitInfo.Collider.isTrigger && (!isHitEntity || (hitEntityType != EntityTypeId.MonsterEntity && hitEntityType != EntityTypeId.PlayerEntity)))
                    {
                        if (hitInfo.ColliderGo.TryGetComponent(out TriggerForce tf) && tf != triggerForce)
                        {
                            triggerForce = tf;
                            triggerForceVelocity = triggerForce.transform.TransformDirection(triggerForce.velocity);
                        }
                        if (hitInfo.Collider.gameObject.layer != MgrConfigPhysicsLayer.LayerWater)
                        {
                            continue;
                        }
                    }

                    if (Mathf.Approximately(0, hitInfo.Distance))
                    {
                        bool isSlope = Vector3.Angle(hitInfo.Normal, Vector3.up) > 60;
                        if (isHitEntity && (isSlope || hitEntityId == ParentId ||
                                                       hitEntityType != EntityTypeId.PartEntity))
                        {
                            continue;
                        }
                    }
                    if (isHitEntity && !((adsorptionList?.Contains(hitEntityType) ?? false) || (useCommonCollision && collisionEntityList.Contains(hitEntityType))))
                    {
                        continue;
                    }

                    // if (IsCollisionAttachGO(hitInfo))
                    // {
                    //     continue;
                    // }

                    if (hitInfo.ColliderGo.layer == MgrConfigPhysicsLayer.LayerWater)
                    {
                        if (!canBuoyancy)
                        {
                            continue;
                        }
#if SOC_SIMULATOR
                        Vector3 nextPos = worldPosition + velocityNormal * hitInfo.Distance;

                        if (IsCastTriggerLightVolume(nextPos))
                        {
                            continue;
                        }
#endif
                    }

                    isHit = true;
                    break;
                }

                if (isHit)
                {
                    // logger.InfoFormat("parentId {0}hitEntityId {1} hitColliderGo {2}", ParentId, hitEntityId, hitInfo.ColliderGo.transform.GetTransformPath());
                    
                    OnCollisionHit?.Invoke(hitInfo);
                    isRebound = true;
                    if (hitInfo.ColliderGo.layer == MgrConfigPhysicsLayer.LayerWater && canBuoyancy)
                    {
                        velocity = Vector3.zero;
                    }

                    float threshold = 1f;
                    float skinWidth = 0.05f;
                    float movePercent = 1 - skinWidth;
                    var beforeVelocity = velocity;

                    if (velocity.magnitude > threshold)
                    {
                        ReflectMove(hitInfo.Normal, isHitEntity, hitEntityId, hitEntityType);
                    }

                    //判断碰撞物体坡度大于60度
                    bool isSlope = Vector3.Angle(hitInfo.Normal, Vector3.up) > 60;
                    bool canLand = (stopWhenHit && NeedAttach(hitEntity, out _, false)) || beforeVelocity.magnitude < threshold || (!isSlope && velocity.magnitude < threshold);
                    
                    if (canLand && isHitEntity && !NeedAttach(hitEntity, out _))
                    {
                        ignoreEntityId = hitEntityId;
                        canLand = false;
                    }
                    
                    if (!canLand)
                    {
                        float moveDis = hitInfo.Distance * movePercent;
                        moveDis = Mathf.Min(moveDis, dis);
                        costTime -= moveDis / originVelocity.magnitude;
                        Translate(velocityNormal * moveDis);
                        //处理在移动物体上的碰撞吸附，比如电梯
                        if (isHitEntity && IsColliderMovingEntity(hitEntityType, hitEntityId) && attachedId != hitEntityId)
                        {
                            HandleAdsorp(hitInfo, out var _);
                        }
                    }
                    else
                    {
                        float moveDis = hitInfo.Distance;
                        costTime -= moveDis / originVelocity.magnitude;
                        bool ignorePlatformSpeed = false;
                        if (hitInfo.Distance > 0)
                        {
                            if (originVelocity is { x: 0, z: 0 })
                            {
                                Vector3 point = new Vector3(worldPosition.x, hitInfo.Point.y, worldPosition.z);
                                SetPosition(point);
                            }
                            else
                            {
                                SetPosition(hitInfo.Point);
                            }
                        }

                        if (isHitEntity && attachedId == 0)
                        {
                            HandleAdsorp(hitInfo, out ignorePlatformSpeed);
                        }
                        
                        if (hitInfo.ColliderGo.layer != MgrConfigPhysicsLayer.LayerWater)
                        {
                            if (attachedId == 0)
                            {
                                int count = Physics.RaycastNonAlloc(worldPosition + Vector3.up * GetColliderHeight(),
                                    Vector3.down, tmpHits, GetColliderHeight() + 1f, layerMask,
                                    QueryTriggerInteraction.Ignore);
                                tmpHitsList.Clear();
                                for (int j = 0; j < count; j++)
                                {
                                    tmpHitsList.Add(tmpHits[j]);
                                }
                                tmpHitsList.Sort((left, right)=> left.distance.CompareTo(right.distance));
                                for (int j = 0; j < count; j++)
                                {
                                    var groundHitInfo = tmpHitsList[j];
                                    if (TryGetColliderEntityId(groundHitInfo.collider, out var hitGroundEntity, out var hitGroundEntityId, out var hitGroundEntityType) && hitGroundEntityId == ParentId)
                                    {
                                        continue;
                                    }
                                    if (groundHitInfo.distance > 0 && !(isAirDropBox && groundHitInfo.collider.CompareTag("AirDropBox")))
                                    {
                                        SetPosition(groundHitInfo.point);
                                        
                                        if (rotateLandGround)
                                        {
                                            Quaternion toRotation = Quaternion.FromToRotation(worldRotation * Vector3.up, groundHitInfo.normal);
                                            worldRotation = toRotation * worldRotation;
                                        }
                                        break;
                                    }
                                }
                                

                            }
                            else
                            {
                                if (rotateLandGround)
                                {
                                    Quaternion toRotation = Quaternion.FromToRotation(worldRotation * Vector3.up, hitInfo.Normal);
                                    SetWorldRotation(toRotation * worldRotation);
                                }
                            }
                        }
                        
                        float useTime = dt - costTime;
                        OnPhysicsMove?.Invoke(useTime, true);
                        OnLandGround?.Invoke(hitInfo, baseCollider);
                        SyncLocalPositionAndRotation();
                        velocity = Vector3.zero;
                        isLand = true;
                        curState = attachedId > 0 ? MoveState.AttachIdle : ignorePlatformSpeed ? MoveState.ContinuousCheckIdle :MoveState.NormalIdle;
                        if (curState == MoveState.NormalIdle)
                        {
                            RemoveFromPhysicsSystem(true);
                        }
                        AfterStopMove?.Invoke();
                        logger.InfoFormat("entity Land {0} entityType {1} pos {2}", ParentId, ParentEntity.EntityType, worldPosition);
                        break;
                    }

                    if (dis <= 0)
                    {
                        break;
                    }
                }
                else
                {
                    costTime = 0;
                    //没有反弹
                    Translate(dir);
                    break;
                }
            }

            if (!isLand)
            {
                costTime = Mathf.Max(0, costTime);
                float useTime = dt - costTime;
                checkUnderGroundTimer += useTime;
                OnPhysicsMove?.Invoke(useTime, false);

                var vector3 = velocity;
                vector3.y += Physics.gravity.y * dt;
                velocity.y = Mathf.Clamp(vector3.y, minGravitySpeed, maxGravitySpeed);

                if (attachedId > 0)
                {
                    CheckLeaveMovingEntity();
                }

                if (checkUnderGroundTimer > k_CheckUnderGroundTime)
                {
                    FixedIfUnderGround();
                    checkUnderGroundTimer = 0;
                }
            }

            // logger.Info("entity " + ParentId + "pos:" + worldPosition + " velocity " + velocity);

            SyncPositionAndRotation();
        }

        private void FixedIfUnderGround()
        {
            if (TerrainMeta.HeightMap == null)
            {
                return;
            }
            var minHeight = TerrainMeta.HeightMap.GetHeight(worldPosition);
            if (worldPosition.y < minHeight)
            {
                Vector3 dir = worldPosition - lastCheckWorldPos;
                float distance = dir.magnitude;
                if (Physics.Raycast(lastCheckWorldPos, dir, out var groundHitInfo, distance, MgrConfigPhysicsLayer.MaskGround, QueryTriggerInteraction.Ignore))
                {
                    logger.WarnFormat("entityId：{0} is UnderGround", ParentId);

                    if (groundHitInfo.distance > 0)
                    {
                        SetPosition(groundHitInfo.point);
                    }

                    if (rotateLandGround)
                    {
                        Quaternion toRotation = Quaternion.FromToRotation(worldRotation * Vector3.up, groundHitInfo.normal);
                        worldRotation = toRotation * worldRotation;
                    }

                    OnLandGround?.Invoke(new SocRaycastHit(groundHitInfo), baseCollider);
                    velocity = Vector3.zero;
                    isLand = true;
                    curState = MoveState.NormalIdle;
                    RemoveFromPhysicsSystem(true);
                    AfterStopMove?.Invoke();
                    logger.InfoFormat("entity Land {0} entityType {1} pos {2}", ParentId, ParentEntity.EntityType, worldPosition);
                }
            }
            
            lastCheckWorldPos = worldPosition;
        }

        public bool HasTriggerForce()
        {
            return triggerForce != null;
        }

        public void UpdateForceMove(float dt)
        {
            Translate(dt * triggerForceVelocity);

            if (isLand)
            {
                if (Physics.Raycast(worldPosition + Vector3.up * 0.5f, Vector3.down, out var groundHitInfo, 2f,MgrConfigPhysicsLayer.MaskDefault, QueryTriggerInteraction.Ignore))
                {
                    if (groundHitInfo.distance > 0)
                    {
                        SetPosition(groundHitInfo.point);
                    }

                    if (rotateLandGround)
                    {
                        Quaternion toRotation = Quaternion.FromToRotation(worldRotation * Vector3.up, groundHitInfo.normal);
                        worldRotation = toRotation * worldRotation;
                    }

                }
            }

            SyncPositionAndRotation();

            if (!IsOverlapTriggerForce())
            {
                triggerForce = null;
                triggerForceVelocity = Vector3.zero;
                if (isLand)
                {
                    isLand = false;
                    curState = MoveState.FreeFall;
                }
            }
        }

        public void Translate(Vector3 dir)
        {
            worldPosition += dir;
            if (attachParent != null)
            {
                localPosition = attachParent.InverseTransformPoint(worldPosition);
            }
        }

        public void SetPosition(Vector3 point)
        {
            worldPosition = point;
            if (attachParent != null)
            {
                localPosition = attachParent.InverseTransformPoint(worldPosition);
            }
        }

        public void Rotate(Quaternion quaternion)
        {
            worldRotation = quaternion * worldRotation;

            if (attachParent != null)
            {
                localRotation = Quaternion.Inverse(attachParent.rotation) * worldRotation;
            }
        }
        
        public void SetWorldRotation(Quaternion quaternion)
        {
            worldRotation = quaternion;

            if (attachParent != null)
            {
                localRotation = Quaternion.Inverse(attachParent.rotation) * worldRotation;
            }
        }

        private void CheckEnterMovingEntity()
        {
            Vector3 pos = UseCoordinateTrans ? OnGetNewPos(worldPosition) : worldPosition;
            var hitCount = Physics.RaycastNonAlloc( pos+ Vector3.up, Vector3.down, tmpHits, 10, layerMask,
                QueryTriggerInteraction.Ignore);
            IEntity movingEntity = null;
            RaycastHit hitInfo = new RaycastHit();
            for (int j = 0; j < hitCount; j++)
            {
                hitInfo = tmpHits[j];
                var isHitEntity = TryGetColliderEntityId(hitInfo.collider, out var hitEntity, out var hitEntityId,
                    out var hitEntityType);

                if (isHitEntity && IsColliderMovingEntity(hitEntityType, hitEntityId))
                {
                    movingEntity = hitEntity;
                    break;
                }
            }

            SocRaycastHit socRaycastHit = new SocRaycastHit(hitInfo);

            if (movingEntity != null)
            {
                HandleAdsorp(socRaycastHit, out var _);
            }

            if (attachedId > 0)
            {
                //防止一出生就在平台下面了，所以把他拉上来一点
                if (localPosition.y < 1)
                {
                    localPosition = new Vector3(localPosition.x, 1, localPosition.z);
                }

                SyncLocalPositionAndRotation();
            }
        }


        private void CheckLeaveMovingEntity()
        {
            Vector3 pos = UseCoordinateTrans ? OnGetNewPos(worldPosition) : worldPosition;
            var hitCount = Physics.RaycastNonAlloc(pos + Vector3.up, Vector3.down, tmpHits, 10, layerMask,
                QueryTriggerInteraction.Ignore);
            bool findAttachEntity = false;
            for (int j = 0; j < hitCount; j++)
            {
                var hitInfo = tmpHits[j];
                var isHitEntity = TryGetColliderEntityId(hitInfo.collider, out var hitEntity, out var hitEntityId,
                    out var _);

                if (isHitEntity && hitEntityId == attachedId)
                {
                    findAttachEntity = true;
                    break;
                }
            }

            if (!findAttachEntity)
            {
                ParentRemove();
            }
        }

        //改为反射方向
        private void ReflectMove(Vector3 normal, bool hitEntity, long hitEntityId, int hitEntityType)
        {
            //速度方向变为镜面反射出射角方向
            normal.Normalize();
            if (hitEntity && IsColliderMovingEntity(hitEntityType, hitEntityId) && attachedId != hitEntityId)
            {
                var movingEntityVelocity = GetMovingEntityVelocity(hitEntityId);
                // 计算小球相对于移动物体的速度
                Vector3 relativeVelocity = (UseCoordinateTrans ? OnGetNewVelocity(velocity) : velocity) - movingEntityVelocity;
                // 计算碰撞后的速度（完全弹性碰撞）
                Vector3 reflectedVelocity = Vector3.Reflect(relativeVelocity, normal);
                float reflectedVelocityY = reflectedVelocity.y;
                reflectedVelocity *= reboundCoefficient;
                if (!reflectInfluceY)
                {
                    reflectedVelocity.y = reflectedVelocityY;
                }
                //先衰减速度，再加上移动物体的速度
                reflectedVelocity = DecaySpeed(normal, reflectedVelocity);
                // 将反弹速度转换为世界坐标系
                velocity = reflectedVelocity + movingEntityVelocity;
                if (UseCoordinateTrans)
                    velocity = OnGetHistoryVelocity(velocity);
            }
            else
            {
                velocity = Vector3.Reflect(velocity, normal);
                float reflectedVelocityY = velocity.y;
                velocity *= reboundCoefficient;
                if (!reflectInfluceY)
                {
                    velocity.y = reflectedVelocityY;
                }
                //衰减速度
                velocity = DecaySpeed(normal, velocity);
            }
        }

        private Vector3 DecaySpeed(Vector3 normal, Vector3 velocity)
        {
            var norY = Math.Abs(normal.y);
            if (norY > Math.Cos(60f * Mathf.Deg2Rad)) //碰撞面与地平线夹角<=60
            {
                var decayRate = 0.6f; //衰减倍率：60°到30°为0.6倍，30°往下每减少10°增加0.05倍
                if (norY > Math.Cos(30f * Mathf.Deg2Rad))
                {
                    decayRate += (float)(30f - Math.Acos(normal.y) * Mathf.Rad2Deg) * 0.005f;
                }

                velocity *= (1f - decayRate);
            }
            else
            {
                velocity *= 0.4f;
            }

            return velocity;
        }

        /// <summary>
        /// 是否碰撞到了移动实体
        /// </summary>
        /// <param name="entityType"></param>
        /// <returns></returns>
        private bool IsColliderMovingEntity(int entityType, long entityId)
        {
            if (entityType == EntityTypeId.TrainCarEntity || entityType == EntityTypeId.ElevatorEntity)
                return true;
#if SOC_SIMULATOR
            if (WizardGames.Soc.SocSimulator.Manager.Mc.Go.GetGo(entityId) is BaseVehicleGo baseVehicleGo)
            {
                if (baseVehicleGo.VehicleType == Data.VehicleType.MiniCopter)
                    return true;
            }
#endif
            return false;
        }

        /// <summary>
        /// 获取当前移动实体的速度
        /// </summary>
        /// <param name="entityId"></param>
        /// <returns></returns>
        private Vector3 GetMovingEntityVelocity(long entityId)
        {
            var entity = McCommon.GetEntityFunc.Invoke(entityId);
            if (entity is TrainCarEntity trainCarEntity)
            {
                return new Vector3(trainCarEntity.VelocityX, trainCarEntity.VelocityY, trainCarEntity.VelocityZ);
            }
            else if (entity is ElevatorEntity elevatorEntity)
            {
                return new Vector3(0, elevatorEntity.IsMoving ? elevatorEntity.MoveDirection * elevatorEntity.Speed : 0, 0);
            }
            else if (entity is VehicleEntity vehicleEntity)
            {
                if (vehicleEntity.VehicleType == (int)Data.VehicleType.MiniCopter)
                {
                    return new Vector3(vehicleEntity.VelocityX, vehicleEntity.VelocityY, vehicleEntity.VelocityZ);
                }
            }
            else
            {
                logger.ErrorFormat("Unknown MovingEntityVelocity type: {0}", entity.EntityType);
            }

            return Vector3.zero;
        }

        //处理吸附
        private void HandleAdsorp(SocRaycastHit hitInfo, out bool ignoreVehicleSpeed)
        {
            ignoreVehicleSpeed = false;
            if (hitInfo.Collider != null)
            {
                var colliderConfig = hitInfo.ColliderGo.GetColliderConfig();
                if (colliderConfig != null)
                {
                    var ownerEntity = colliderConfig.OwnerEntity;
                    if (ownerEntity != null)
                    {
                        bool needAttach = NeedAttach(ownerEntity, out var isAdsorp); //需要吸附
                        if (!isAdsorp)
                        {
                            bool isPlatform = colliderConfig.OwnerEntity is IPlatformEntity platformEntity &&
                                              platformEntity.CanPlatform();
                            ignoreVehicleSpeed = !isPlatform && ignorePlatformSpeedList.Contains(ownerEntity.EntityType);
                        }
                        if (needAttach && !ignoreVehicleSpeed)
                        {
                            var mgrEntityGo = ((UnityContext)(McCommon.System.Context)).BaseMgrEntityGo;
                            bool isAttachMountable = false;
                            if (ownerEntity is IBaseMountableEntity)
                            {
                                var mountableGo = mgrEntityGo.GetGo(ownerEntity.EntityId);
                                if (mountableGo != null)
                                {
                                    isAttachMountable = true;
                                    attachParent = mountableGo.MainGo.transform;
                                }
                                else
                                {
                                    attachParent = hitInfo.ColliderGo.transform;
                                }
                            }
                            else
                            {
                                attachParent = hitInfo.ColliderGo.transform;
                            }
                            localPosition = attachParent.InverseTransformPoint(worldPosition);
                            localRotation = Quaternion.Inverse(attachParent.rotation) * worldRotation;
                            attachedId = ownerEntity.EntityId;
                            logger.InfoFormat("entityId：{0} is Attached", ParentId);

                            OnAttached?.Invoke(attachedId, ownerEntity.EntityType, true);

                            if (ParentEntity is IAttachedEntity attachedEntity)
                            {
                                attachedEntity.TargetEntityID = colliderConfig.OwnerEntityId;
                                attachedEntity.TargetColliderIndex = colliderConfig.Index;
                                attachedEntity.AttachedTransform = attachParent;
                                if (isAttachMountable)
                                {
                                    attachedEntity.TargetColliderIndex = -1;
                                }
                            }

                            mgrEntityGo.AddChild(
                                colliderConfig.OwnerEntityId, ParentId);
                        }
                    }
                }
            }
            else
            {
                attachedId = 0;
            }
        }

        /// <summary>
        /// 判断是否吸附
        /// </summary>
        /// <param name="judgeCollision">为true则带上collisionEntityList判断</param>
        /// <returns></returns>
        private bool NeedAttach(IEntity entity, out bool isAdsorp, bool judgeCollision = true)
        {
            isAdsorp = false;
            if (entity == null)
            {
                return false;
            }
            var entityType = entity.EntityType;
            isAdsorp = (adsorptionList?.Contains(entityType) ?? false);
            bool needAdsorp = isAdsorp || (judgeCollision && collisionEntityList.Contains(entityType)); //需要吸附
            if (needAdsorp)
            {
                if (entity is MonsterEntity monsterEntity && monsterEntity.Type != (int)MonsterCategory.Tank)
                {
                    needAdsorp = false;
                }
                else if (entity is PlayerEntity || entity is NPCEntity)
                {
                    needAdsorp = false;
                }
            }

            return needAdsorp;
        }

#if SOC_SIMULATOR
        private bool IsCastTriggerLightVolume(Vector3 nextPos)
        {
            int overlapCount = CollisionOverlap(Quaternion.identity, nextPos);
            bool hasLightVolume = false;

            if (overlapCount > 0)
            {
                for (int k = 0; k < overlapCount; k++)
                {
                    var collider = tmpColliders[k];
                    if (collider.GetComponent<TriggerLightVolume>() != null)
                    {
                        hasLightVolume = true;
                        break;
                    }
                }
            }

            return hasLightVolume;
        }
#endif
        private bool IsOverlapTriggerForce()
        {
            int overlapCount = CollisionOverlap(Quaternion.identity, worldPosition);
            bool hasTriggerForce = false;

            if (overlapCount > 0)
            {
                for (int k = 0; k < overlapCount; k++)
                {
                    var collider = tmpColliders[k];
                    if (collider.TryGetComponent<TriggerForce>(out var tf))
                    {
                        if (tf != triggerForce)
                        {
                            triggerForce = tf;
                            triggerForceVelocity = triggerForce.transform.TransformDirection(triggerForce.velocity);
                        }
                        hasTriggerForce = true;
                        break;
                    }
                }
            }

            return hasTriggerForce;
        }



        private bool IsCollisionAttachGO(RaycastHit hitInfo)
        {
            if (attachedId != 0) //过滤掉父对象
            {
                if (hitInfo.collider != null)
                {
                    var colliderConfig = hitInfo.collider.gameObject.GetColliderConfig();
                    if (colliderConfig != null && colliderConfig.OwnerEntity.EntityId == attachedId)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        private float GetColliderHeight()
        {
            float height = 0;
            if (colliderType == 1)
            {
                height = capsuleCollider.height;
            }
            else if (colliderType == 2)
            {
                height = boxCollider.size.y;
            }
            else if (colliderType == 3)
            {
                height = sphereCollider.radius * 2;
            }

            height = Mathf.Max(0.1f, height);

            return height;
        }
        
        private void InitCollider(bool handleOverlapScene)
        {
            if (baseCollider == null)
            {
                logger.ErrorFormat("No collider found");
                return;
            }

            isAirDropBox = baseCollider.CompareTag("AirDropBox");
            isDropBox = false;
            var quaternion = worldRotation;
            var forward = Quaternion.Euler(0,worldRotation.eulerAngles.y,0) * Vector3.forward;
            var horizontalCheckMask = MgrConfigPhysicsLayer.MaskConstruction | MgrConfigPhysicsLayer.MaskDefault | MgrConfigPhysicsLayer.MaskIgnoreRayCast;
            var forwardVec = forward * 0.5f;
            var upVec = Vector3.up * 0.5f;
            castOffsetY = 0.1f;

            if (baseCollider is CapsuleCollider tempCollider1)
            {
                capsuleCollider = tempCollider1;
                colliderType = 1;
                shapeType = PhysicsShapeType.Capsule;
                if (handleOverlapScene)
                {
                    int checkTotalCount = 5;
                    UnityUtility.CapsuleColliderEndCaps(capsuleCollider, worldPosition, quaternion,
                        out var startPoint,
                        out var endPoint);
                    for (int i = 0; i < 5; i++)
                    {
                        if (Physics.CheckCapsule(startPoint, endPoint, capsuleCollider.radius, horizontalCheckMask, QueryTriggerInteraction.Ignore))
                        {
                            startPoint -= forwardVec;
                            worldPosition -= forwardVec;
                        }
                        else
                        {
                            break;
                        }
                    }

                    for (int i = 0; i < 5; i++)
                    {
                        if (Physics.CheckCapsule(startPoint, endPoint, capsuleCollider.radius, MgrConfigPhysicsLayer.MaskTerrain, QueryTriggerInteraction.Ignore))
                        {
                            startPoint += upVec;
                            worldPosition += upVec;
                        }
                        else
                        {
                            break;
                        }
                    }
                }
            }
            else if (baseCollider is BoxCollider tempCollider2)
            {
                boxCollider = tempCollider2;
                colliderType = 2;
                shapeType = PhysicsShapeType.Box;
                castOffsetY += Mathf.Max(boxCollider.center.y, 0f);
                isDropBox = (ParentEntity is ITemplateEntity templateEntity) && (templateEntity.TemplateId == McCommon.Tables.TbConstructionConstantConfig.ItemDropBoxId ||
                            templateEntity.TemplateId == EntityConst.PlayerCorpseTemplateId);
                if (handleOverlapScene)
                {
                    if (!isDropBox)
                    {
                        for (int i = 0; i < 5; i++)
                        {
                            if (Physics.CheckBox(worldPosition + boxCollider.center, boxCollider.size / 2,
                                    quaternion, horizontalCheckMask, QueryTriggerInteraction.Ignore))
                            {
                                worldPosition -= forwardVec;
                            }
                            else
                            {
                                break;
                            }
                        }
                    }

                    int checkMask = isDropBox ? MgrConfigPhysicsLayer.MaskScene : MgrConfigPhysicsLayer.MaskTerrain;
                    for (int i = 0; i < 5; i++)
                    {
                        if (Physics.CheckBox(worldPosition + boxCollider.center, boxCollider.size / 2,
                                quaternion, checkMask, QueryTriggerInteraction.Ignore))
                        {
                            worldPosition += upVec;
                        }
                        else
                        {
                            break;
                        }
                    }
                }
                worldPosition += new Vector3(0, 0.01f, 0);
            }
            else if (baseCollider is SphereCollider tempCollider3)
            {
                sphereCollider = tempCollider3;
                colliderType = 3;
                shapeType = PhysicsShapeType.Sphere;
                if (handleOverlapScene)
                {
                    for (int i = 0; i < 5; i++)
                    {
                        if (Physics.CheckSphere(worldPosition, sphereCollider.radius, horizontalCheckMask, QueryTriggerInteraction.Ignore))
                        {
                            worldPosition -= forwardVec;
                        }
                        else
                        {
                            break;
                        }
                    }
                    
                    for (int i = 0; i < 5; i++)
                    {
                        if (Physics.CheckSphere(worldPosition, sphereCollider.radius, MgrConfigPhysicsLayer.MaskTerrain, QueryTriggerInteraction.Ignore))
                        {
                            worldPosition += upVec;
                        }
                        else
                        {
                            break;
                        }
                    }
                }
            }
            
            if (positionEntity != null)
            {
                positionEntity.PosX = worldPosition.x;
                positionEntity.PosY = worldPosition.y;
                positionEntity.PosZ = worldPosition.z;
            }
        }

        /// <summary>
        /// 过滤不同实体的非胶囊体层的碰撞结果.
        /// </summary>
        /// <param name="raycastHit">碰撞的信息</param>
        /// <returns>是否通过过滤</returns>
        bool FilterRaycastHit(RaycastHit raycastHit)
        {            
            // logger.InfoFormat("FilterRaycastHit："+ raycastHit.collider.transform.GetTransformPath());
            
            if (ParentEntity is ThrownEntity thrownEntity)
            {
                var collider = raycastHit.collider;
                if (TryGetColliderEntityId(collider, out IEntity iEntity, out long entityId, out int entityType))//分别碰撞不同实体的胶囊体层
                {                   
                    if (iEntity is MonsterEntity monsterEntity && monsterEntity.Type == 2)//Client&Server端,科学家:碰撞AI层
                    {
                        if (collider.gameObject.layer != 15)//AI
                        {
                            return false;
                        }
                    }
                    else if (iEntity.EntityType == EntityTypeId.HorseEntity) //马:关注马的用于投掷碰撞的tag
                    {
                        if (!collider.gameObject.CompareTag("ThrowCollision"))
                        {
                            return false;
                        }                        
                    }
                    else if (iEntity.EntityType == EntityTypeId.PlayerEntity) //Server玩家:碰撞tag=Player
                    {
                        if (!collider.gameObject.CompareTag("Player"))
                        {
                            return false;
                        }
                        if (!isRebound && iEntity.EntityId == thrownEntity.OwnerEntityId)
                        {
                            return false;
                        }
                    }
                }
            }
            return true;
        }

        //碰撞检测
        private int CollisionCheck(Quaternion quaternion, Vector3 pos, Vector3 velocityNormal, float dis, bool isFirstCheck)
        {
            if (canBuoyancy)
            {
                layerMask |= MgrConfigPhysicsLayer.MaskWater;
            }

            int hitCount = 0;
            if (colliderType == 1)
            {
                UnityUtility.CapsuleColliderEndCaps(capsuleCollider, pos, quaternion, out var startPoint,
                    out var endPoint);
                if (!isFirstCheck)
                {
                    hitCount = Physics.CapsuleCastNonAlloc(startPoint, endPoint, capsuleCollider.radius,
                        velocityNormal, tmpHits, dis + castOffsetY, layerMask);
                }
                else
                {
                    hitCount = castJobHitCount;
                }
                tmpHitsList.Clear();
                for (int i = 0; i < hitCount; i++)
                {
                    if (FilterRaycastHit(tmpHits[i]))
                        tmpHitsList.Add(tmpHits[i]);
                }
                tmpSocHitsList=CheckUtil.GetSocRaycastHits(tmpHitsList, velocityNormal, dis + castOffsetY,startPoint, 
                    ref isHitScreenBox,
                    raycastRaduis: capsuleCollider.radius, castType: 2, point1: startPoint, point2: endPoint, needOverlap:needOverlapScene);
                hitCount=tmpSocHitsList.Count;
            }
            else if (colliderType == 2)
            {
                if (!isFirstCheck)
                {
                    hitCount = Physics.BoxCastNonAlloc(pos + boxCollider.center, boxCollider.size / 2,
                        velocityNormal, tmpHits, quaternion, dis + castOffsetY, layerMask);
                }
                else
                {
                    hitCount = castJobHitCount;
                }
                tmpHitsList.Clear();
                for (int i = 0; i < hitCount; i++)
                {
                    if (FilterRaycastHit(tmpHits[i]))
                        tmpHitsList.Add(tmpHits[i]);
                }
                tmpSocHitsList=CheckUtil.GetSocRaycastHits(tmpHitsList, velocityNormal, dis + castOffsetY,pos + boxCollider.center,
                    ref isHitScreenBox,
                    castType: 1, halfSize:boxCollider.size / 2, orientation:quaternion, needOverlap:needOverlapScene);
                hitCount=tmpSocHitsList.Count;
            }
            else if (colliderType == 3)
            {
                if (!isFirstCheck)
                {
                    hitCount = Physics.SphereCastNonAlloc(pos+ sphereCollider.center, sphereCollider.radius, velocityNormal,
                        tmpHits, dis + castOffsetY, layerMask);
                }
                else
                {
                    hitCount = castJobHitCount;
                }
                tmpHitsList.Clear();
                for (int i = 0; i < hitCount; i++)
                {
                    if (FilterRaycastHit(tmpHits[i]))
                        tmpHitsList.Add(tmpHits[i]);
                }
                tmpSocHitsList=CheckUtil.GetSocRaycastHits(tmpHitsList, velocityNormal, dis + castOffsetY, pos,
                    ref isHitScreenBox,
                    raycastRaduis:sphereCollider.radius, castType: 3, needOverlap:needOverlapScene);
                hitCount=tmpSocHitsList.Count;
            }

            needOverlapScene = false;
            return hitCount;
        }

        //重叠检测
        private int CollisionOverlap(Quaternion quaternion, Vector3 pos)
        {
            int hitCount = 0;
            if (colliderType == 1)
            {
                UnityUtility.CapsuleColliderEndCaps(capsuleCollider, pos, quaternion, out var startPoint,
                    out var endPoint);
                hitCount = Physics.OverlapCapsuleNonAlloc(startPoint, endPoint, capsuleCollider.radius, tmpColliders,
                    MgrConfigPhysicsLayer.MaskTrigger, QueryTriggerInteraction.Collide);
            }
            else if (colliderType == 2)
            {
                hitCount = Physics.OverlapBoxNonAlloc(pos + boxCollider.center, boxCollider.size / 2, tmpColliders,
                    quaternion, MgrConfigPhysicsLayer.MaskTrigger, QueryTriggerInteraction.Collide);
            }
            else if (colliderType == 3)
            {
                hitCount = Physics.OverlapSphereNonAlloc(pos + sphereCollider.center, sphereCollider.radius, tmpColliders,
                    MgrConfigPhysicsLayer.MaskTrigger,
                    QueryTriggerInteraction.Collide);
            }

            return hitCount;
        }

        //同步位置和旋转
        private void SyncPositionAndRotation()
        {
            var angle = worldRotation.eulerAngles;

            if (positionEntity != null && lastWorldPosition != worldPosition)
            {
                positionEntity.PosX = worldPosition.x;
                positionEntity.PosY = worldPosition.y;
                positionEntity.PosZ = worldPosition.z;
                
            }

            if (rotation3Entity != null && lastWorldRotation != worldRotation)
            {
                rotation3Entity.RotateY = angle.y;
                rotation3Entity.RotateX = angle.x;
                rotation3Entity.RotateZ = angle.z;
            }

            if (attachedId > 0)
            {
                SyncLocalPositionAndRotation();
            }

            if (lastWorldPosition != worldPosition || lastWorldRotation != worldRotation)
            {
                AfterSyncTransform?.Invoke();
            }
            
            lastWorldPosition = worldPosition;
            lastWorldRotation = worldRotation;
        }

        private void SyncLocalPositionAndRotation()
        {
            if (isLand)
            {
                return;
            }

            var localPos = localPosition;

            if (ParentEntity is IAttachedEntity attachedEntity && attachedEntity.TargetEntityID > 0)
            {
#if SOC_SIMULATOR
                if (UseCoordinateTrans)
                {
                    //获取历史的相对位置
                    localPos = OnGetRelativePos(worldPosition);
                }
#endif
                attachedEntity.PosOffsetX = localPos.x;
                attachedEntity.PosOffsetY = localPos.y;
                attachedEntity.PosOffsetZ = localPos.z;
                attachedEntity.RotOffsetX = localRotation.x;
                attachedEntity.RotOffsetY = localRotation.y;
                attachedEntity.RotOffsetZ = localRotation.z;
                attachedEntity.RotOffsetW = localRotation.w;

#if PHYSICSMOVECOMPONENT_DEBUG
                logger.InfoFormat("entity {0}AttachId {1}localPos {2}", ParentId, attachedEntity.TargetEntityID, localPos);
#endif
            }
        }

        private bool TryGetColliderEntityId(Collider collider, out IEntity entity, out long entityId, out int entityType)
        {
            var colliderConfig = collider.gameObject.GetColliderConfig();
            if (colliderConfig != null && colliderConfig.OwnerEntity != null)
            {
                entity = colliderConfig.OwnerEntity;
                entityId = colliderConfig.OwnerEntity.EntityId;
                entityType = colliderConfig.OwnerEntity.EntityType;
                return true;
            }
            else
            {
                entityId = 0;
                entityType = 0;
                entity = null;
                return false;
            }
        }
        
        private bool TryGetColliderEntityId(GameObject go, out IEntity entity, out long entityId, out int entityType)
        {
            var colliderConfig = go.GetColliderConfig();
            if (colliderConfig != null && colliderConfig.OwnerEntity != null)
            {
                entity = colliderConfig.OwnerEntity;
                entityId = colliderConfig.OwnerEntity.EntityId;
                entityType = colliderConfig.OwnerEntity.EntityType;
                return true;
            }
            else
            {
                entityId = 0;
                entityType = 0;
                entity = null;
                return false;
            }
        }

        public void DrawCollider(float during = 0.33f)
        {
            if (colliderType == 1)
            {
                Vector3 up = worldPosition + capsuleCollider.center + (capsuleCollider.height / 2 - capsuleCollider.radius)* Vector3.up;
                Vector3 down = worldPosition + capsuleCollider.center - (capsuleCollider.height / 2 - capsuleCollider.radius) * Vector3.up;

                DebugDraw.DrawCapsule(up, down, capsuleCollider.radius, Color.green, during);

            }
            else if (colliderType == 2)
            {
                var obb = new OBB(worldPosition + boxCollider.center, boxCollider.size, worldRotation);
                DebugDraw.DrawBox(obb, Color.green,during);

            }
            else if (colliderType == 3)
            {
                var obb = new OBB(worldPosition + sphereCollider.center, sphereCollider.radius * Vector3.one, Quaternion.identity);
                DebugDraw.DrawBox(obb, Color.green,during);
            }
        }

        public override void Cleanup()
        {
            RemoveFromPhysicsSystem(false);
            OnGetUseCoordinateTrans = null;
            OnGetNewPos = null;
            OnGetNewVelocity = null;
            OnGetRelativePos = null;
            OnGetHistoryVelocity = null;
            OnLandGround = null;
            OnAttached = null;
            OnCollisionHit = null;
            OnPhysicsMove = null;
            isLand = true;
            attachedId = 0;
            baseCollider = null;
            boxCollider = null;
            capsuleCollider = null;
            sphereCollider = null;
            colliderType = -1;
            IsActive = false;
            checkUnderGroundTimer = 0;
            isAirDropBox = false;
            isRebound = false;
        }
    }
}
#endif