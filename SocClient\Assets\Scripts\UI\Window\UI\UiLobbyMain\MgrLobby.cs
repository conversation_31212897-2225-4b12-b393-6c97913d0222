using System;
using System.Collections.Generic;
using Assets.Scripts.MicroServiceClient;
using Cysharp.Text;
using SimpleJSON;
using WizardGames.Soc.Common.Data.common;
using WizardGames.Soc.Common.Data.Play;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Loader.CrashSight;
using WizardGames.Soc.Common.Unity.Loader.Logger;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Http;
using WizardGames.Soc.SocClient.Ui;
using WizardGames.Soc.SocClient.Utility;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Unity.Main;
using Config;


namespace WizardGames.Soc.SocClient.Manager
{
    //预约相关数据类
    public class RoundTimeData
    {
        public long startTime = 0;
        public long endTime;
        public string startTimeStr;
        public string endTimeStr;
    }
    
    /// <summary>
    /// 大厅相关Mgr,进局内会销毁
    /// </summary>
    public class MgrLobby : MgrBase
    {
        private static SocLogger Log = LogHelper.GetLogger(typeof(MgrLobby));

        /// <summary>
        /// 已经进入过的所有服务器list
        /// </summary>
        public List<LobbyBattleServerData> lobbyBattleServerDatas = new ();
        /// <summary>
        /// 根据模式区分进过的服务器list
        /// </summary>
        public Dictionary<int, List<LobbyBattleServerData>> lobbyBattleServerDatasDic = new ();


        #region 改名常量定义
        const int CHANGE_NAME_BAN_ERROR_CODE = 11008;
        public const string CHANGE_NAME_CD_KEY = "ChangeNameCD";
        #endregion

        public Dictionary<string, BattleTeamData> battleTeamDataDic = new();
        public PlayerDisplayData PlayerSelfDisplayData { get; set; } = null;

        //预约模式信息数据
        public List<RoundTimeData> RoundTimeDataList = new List<RoundTimeData>();
        private List<RoundTimeData> HistoryRoundList = new List<RoundTimeData>();
        public RoundTimeData currentRoundData = new RoundTimeData();
        
        
        public bool IsUILobbyMainShowAnimFinish { get; set; } = false; //大厅主界面动画是否播放完成
        
        public long NextCanRemoveBattleTime { get; set; } // 下次可以移除战斗服的时间

        #region 生命周期函数
        
        public override void Init()
        {
            base.Init();
            GetPlayedServerList(EHttpReqModule.Lobby);
        }

        public override void OnAccountLogout()
        {
            base.OnAccountLogout();
            PlayerSelfDisplayData = null;
            lobbyBattleServerDatas.Clear();
            lobbyBattleServerDatasDic.Clear();
            battleTeamDataDic.Clear();
            IsUILobbyMainShowAnimFinish = false;
        }

        public override void OnLogined()
        {
            base.OnLogined(); 
            lobbyBattleServerDatas.Clear();
            lobbyBattleServerDatasDic.Clear();
            battleTeamDataDic.Clear();
            IsUILobbyMainShowAnimFinish = false;
        }
        public override void OnAccountLogined()
        {
            base.OnAccountLogined();
            GetPlayedServerList(EHttpReqModule.Lobby);
            Log.Info("[MgrLobby]MgrLobby: OnAccountLogined");
        }

        public override void OnLogout()
        {
            base.OnLogout();
        }
        #endregion
        /// <summary>
        /// 登录大厅时请求账号角色数据
        /// </summary>
        public void ReqGetUserInfo(EHttpReqModule reqModule, Action callback = null)
        {
            JSONObject param = new();
            var jsonArray = new JSONArray();
            jsonArray.Add(Mc.Config.RoleId);
            param.Add("idList", jsonArray);
            Mc.LoginStep.StepLog($"[MgrLobby] 请求账号角色数据, RoleID {Mc.Config.RoleId}");
            Mc.Config.RequestUserInfo(reqModule, (res) =>
            {
                string resNickName = null;
                string resPortrait = null;
                if (res == null)
                {
                    Mc.LoginStep.StepLogError("[MgrLobby] 获得账号角色数据为空，请求账号角色数据失败");
                }
                else
                {
                    resNickName = res["nickname"] ?? String.Empty;
                    resPortrait = res["portrait"] ?? String.Empty;
                    Mc.LoginStep.StepLog(ZString.Format("[MgrLobby] 获得账号角色数据: nickname = {0}, portrait = {1}", resNickName, resPortrait));
                }
                Mc.Config.lobbyRoleInfo.SetLobbyRoleInfo(res);
                Mc.LobbyFriend.ReqGetRequestInfo(EHttpReqModule.LobbyFriend);//好友请求列表必须在设置完自己信息后
                if (Mc.Config.lobbyRoleInfo.UseChannelPortrait)
                    Mc.LoginStep.StepLog($" Mc.Config.pictureUrl {Mc.Config.pictureUrl}");
#if MSDK
                if (Mc.Config.lobbyRoleInfo.UseChannelPortrait && Mc.Config.lobbyRoleInfo.portrait != Mc.Config.pictureUrl)
                {
                    Mc.Lobby.ReqChangeAvatar(EHttpReqModule.CreateRole, Mc.Config.pictureUrl, false, () =>
                    {
                        Mc.Config.lobbyRoleInfo.portrait = Mc.Config.pictureUrl;
                    });
                }
#endif
                Mc.InputSystem.InitRebindKey();
                
                Mc.Config.ReqChatBubble(EHttpReqModule.Login);
                Mc.Config.nickName = resNickName;
                ErrorLogFormatter.SetPlayerName(Mc.Config.nickName);
                if (Mc.Voice != null)
                {
                    Mc.Voice.InitVoiceEngine();
                }

                Mc.Announcement.RequestAfterLoginAnnouncement(Mc.Announcement.isLogined);
                if (Mc.Announcement.isLogined)//不是新号且是从登录界面过来的，请求公告
                {
                    Mc.Announcement.isLogined = false;
                }
#if !POCO && !UNIT_TEST
                Mc.Config.RequestSelfRoleArchiveData(reqModule);
#endif
                callback?.Invoke();
                Mc.Msg.FireMsg(EventDefine.UserInfoUpdate);

                CrashSightWrapper.AddSceneData("playerName", Mc.Config.nickName);
                Mc.Chat.AddOrUpdateRoleInfo(res);
            }, Mc.Config.roleId);
        }

        /// <summary>
        /// 请求修改玩家名称
        /// isRecordCD 是否记录改名CD，针对第一次命名，不需要进入CD
        /// </summary>
        public void ReqChangeName(EHttpReqModule reqModule, string newName, Action callback = null, bool isRecordCD = false)
        {
            JSONObject param = new();
            param.Add("val", newName);
            MicroServiceClient.Instance.CallLobbyPost("playerservice/nickname", reqModule, res =>
            {
                Mc.Config.nickName = newName;
                if (isRecordCD)
                {
                    var cdTimeInt = Mc.Tables.TBConsts.ChangeNameCD + DateTimeOffset.Now.ToUnixTimeSeconds();
                    ClientPlayerPrefs.SetString(CHANGE_NAME_CD_KEY, cdTimeInt.ToString(), false);
                    Mc.Msg.FireMsg(EventDefine.RefreshChangeNameCDNotice);
                }
                callback?.Invoke();
                Mc.Msg.FireMsg(EventDefine.ChangePlayerNameNotice);
            }, param, false, error =>
            {
                var win = Mc.Ui.GetWindow("UiCreateRoleNew");
                if (win != null)
                {
                    var panel = win as UiCreateRoleNew;
                    panel.PlayInputBoxEffect();
                    panel.ReqChangeNameError();
                }
                if (null != Mc.Lobby)
                {
                    HandleChangeNameBanReason(error, isRecordCD);
                }
            });
        }

        /// <summary>
        /// 请求修改个性标签
        /// </summary>
        public void ReqChangePersonalTags(EHttpReqModule reqModule, List<int> tags, Action callback = null, bool isRecordCD = false)
        {
            JSONObject param = new();
            JSONArray array = new();
            foreach (var tag in tags)
            {
                array.Add(tag);
            }
            param.Add("personalTags", array);
            MicroServiceClient.Instance.CallLobbyPost("playerservice/personaltags", reqModule, res =>
            {
                callback?.Invoke();
            }, param, false, error =>
            {
                
            });
        }


        /// <summary>
        /// 请求修改游戏风格
        /// </summary>
        public void ReqChangeGameStyle(EHttpReqModule reqModule, List<int> tags, Action callback = null, bool isRecordCD = false)
        {
            JSONObject param = new();
            JSONArray array = new();
            foreach (var tag in tags)
            {
                array.Add(tag);
            }
            param.Add("styleIDs", array);
            MicroServiceClient.Instance.CallLobbyPost("playerservice/gamestyles", reqModule, res =>
            {
                callback?.Invoke();
            }, param, false, error =>
            {

            });
        }
        /// <summary>
        /// 请求修改个人主页外观方案
        /// </summary>
        public void ReqChangeSuitPlan(EHttpReqModule reqModule, int plan, Action callback = null)
        {
            JSONObject param = new();
            param.Add("planID", plan);
            MicroServiceClient.Instance.CallLobbyPost("playergrowth/equipment/useoutfitplanforprofilemodel", reqModule, res =>
            {
                callback?.Invoke();
            }, param, false, null);
        }


        /// <summary>
        /// 请求修改外观
        /// </summary>
        public void ReqChangeAppearance(EHttpReqModule reqModule, PlayerAppearanceInfo info, Action callback = null, bool isRecordCD = false)
        {
            JSONObject param = new();
            param.Add("faceID", info.FaceId);
            param.Add("hairColorID", info.HairColorID);
            param.Add("hairID", info.HairID);
            param.Add("sex", info.Sex);
            MicroServiceClient.Instance.CallLobbyPost("playerservice/appearance", reqModule, res =>
            {
                callback?.Invoke();
            }, param, false, null);
        }

        /// <summary>
        /// 修改账号等级
        /// </summary>
        public void ReqSetLevel(EHttpReqModule reqModule, string level)
        {
            JSONObject param = new();
            param.Add("id", Mc.Config.RoleId);
            Log.InfoFormat("ReqSetLevel val:{0} {1}", Mc.Config.RoleId, level);
            MicroServiceClient.Instance.CallLobbyPost("dbg/playerservice/setlevel", reqModule, null, param, false, error =>
            {
                Log.InfoFormat("ReqSetLevel failed res:{0}", error);
            });
        }

        /// <summary>
        /// 重置改名CD
        /// </summary>
        public void ReqResetNickNameCD(EHttpReqModule reqModule)
        {
            JSONObject param = new();
            param.Add("id", Mc.Config.RoleId);
            Log.InfoFormat("ReqResetNickNameCD val:{0}", Mc.Config.RoleId);
            MicroServiceClient.Instance.CallLobbyPost("dbg/playerservice/resetnicknamecd", reqModule, res =>
            {
                Mc.MsgTips.ShowRealtimeWeakTip(23091);
                ClientPlayerPrefs.SetString(CHANGE_NAME_CD_KEY, "", false);
            }, param, false, error =>
            {
                Log.InfoFormat("ReqResetNickNameCD failed res:{0}", error);
            });
        }

        public void HandleChangeNameBanReason(JSONNode error, bool isRecordCD = false)
        {
            var codeId = error["LogicError"].AsInt;
            JSONNode content = error["Content"];
            if (codeId == CHANGE_NAME_BAN_ERROR_CODE) //11008 走封禁原因表
            {
                MicroServiceClient.PopupResultError(error);
                return;
            }
            string remainSeconds = content["remainSeconds"];
            if (!string.IsNullOrEmpty(remainSeconds) && isRecordCD)
            {
                var cdTimeInt = long.Parse(remainSeconds) + DateTimeOffset.Now.ToUnixTimeSeconds();
                ClientPlayerPrefs.SetString(CHANGE_NAME_CD_KEY, cdTimeInt.ToString(), false);
                Mc.Msg.FireMsg(EventDefine.RefreshChangeNameCDNotice);
            }
            //其他的走错误码表
            var errorInfo = Mc.Tables.TBErrorCode.GetOrDefault(codeId);

            if (codeId == 11005 && Mc.Ui.GetWindow("UiCreateRoleNew") != null)
            {
                Mc.MsgTips.ShowRealtimeWeakTip(24217);
            }
            else
            {
                if (null == errorInfo) return;
                if (!string.IsNullOrEmpty(errorInfo.Text))
                    Mc.MsgTips.ShowDebugRealtimeWeakTip(errorInfo.Text);
            }
            Mc.Msg.FireMsg(EventDefine.ChangeNameError, codeId);
        }

        /// <summary>
        /// 请求修改头像为微信头像 使用系统头像不是这个接口
        /// </summary>
        public void ReqChangeAvatar(EHttpReqModule reqModule, string avatar,bool showTip = true,Action callback = null)
        {
            Mc.Config.CheckBanInfo(EBanType.BanTypeSetPortrait, reqModule, showTip, () =>
            {
                JSONObject param = new();
                param.Add("val", avatar);
                MicroServiceClient.Instance.CallLobbyPost("playerservice/portrait", reqModule, (res) =>
                {
                    callback?.Invoke();
                    Mc.Msg.FireMsg(EventDefine.ChangePlayerHeadIconNotice);
                }, param, true, error =>
                {
                });
            });
           
        }

        //teamID 是局内组队的teamID，不要和大厅队伍的teamID混淆
        public void GetBattleTeamData(EHttpReqModule reqModule, string teamID, string battleServerId, Action<BattleTeamData> callback)
        {
            //客户端自己拼接一个唯一key，大厅服务器那里是把battleServerId和teamID拼接起来的
            var strKey = ZString.Format("battleTeamData_{0}_{1}", teamID, battleServerId); 
            if(battleTeamDataDic.TryGetValue(strKey, out var battleTeamData))
            {
                callback?.Invoke(battleTeamData);
                return;
            }
            ReqBattleTeam(reqModule, teamID, battleServerId, (res) =>
            {
                Log.InfoFormat("GetBattleTeamData ReqBattleTeam res:{0}", res);
                BattleTeamData battleTeamData = new BattleTeamData(res);
                battleTeamDataDic[strKey] = battleTeamData;
                callback?.Invoke(battleTeamData);
                JSONArray jsonArray = res["memberList"].AsArray;
                for (int i = 0; i < jsonArray.Count; i++)
                {
                    var info = jsonArray[i];
                    var roleID = info["roleID"].AsULong;
                    if (roleID == Mc.Config.RoleId && IsFirstPlayerServerData(battleServerId)) //如果是自己，并且是最近的战局数据
                    {
                        string battlePlayerDataStr = info["battlePlayerData"];
                        PlayerSelfDisplayData = new PlayerDisplayData(false, JSONNode.Parse(battlePlayerDataStr));
                        Mc.Msg.FireMsg(EventDefine.PlayerSelfDisplayDataUpdate);
                        break;
                    }
                }
            });
        }

        public bool IsFirstPlayerServerData(string battleServerId)
        {
            if (lobbyBattleServerDatas == null || lobbyBattleServerDatas.Count == 0) return false;
            return lobbyBattleServerDatas[0].BattleServerId.Equals(battleServerId);
        }

        /// <summary>
        /// 获取玩家玩过的服列表,用于历史战局界面
        /// </summary>
        /// <param name="callback"></param>
        public void GetPlayedServerList(EHttpReqModule reqModule, Action callback = null)
        {
            if (MicroServiceClient.Instance == null) return;
            MicroServiceClient.Instance.CallLobbyPost("warzoneservice/playedserverlist", reqModule, (res) =>
            {
                lobbyBattleServerDatas.Clear();
                lobbyBattleServerDatasDic.Clear();
                NextCanRemoveBattleTime = res["nextCanRemoveBattleTime"].AsLong;
                JSONArray jsonArray = res["battleServerList"].AsArray;
                Log.InfoFormat("playedserverlist jsonArray.count:{0} jsonArray:{1}", jsonArray.Count, jsonArray);

                for (int i = 0; i < jsonArray.Count; i++)
                {
                    JSONNode info = jsonArray[i];
                    int modeId = info["modeID"];
                    // 过滤下私服（modeID 为0）和其他异常情况
#if !UNITY_EDITOR
                    if (modeId != 0)
#endif
                    {
                        LobbyBattleServerData lobbyBattleServerData = new LobbyBattleServerData(info);
                        var hasKey = ClientPlayerPrefs.HasKey(lobbyBattleServerData.BattleServerId);
                        if (!hasKey)
                        {
                            ClientPlayerPrefs.SetString(lobbyBattleServerData.BattleServerId, lobbyBattleServerData.BattleServerId);
                        }
                        
                        if (info.HasKey("battleSummaryData"))
                        {
                            JSONNode battleSummaryData = info["battleSummaryData"];
                            if (!string.IsNullOrEmpty(battleSummaryData.Value))
                            {
                                List<GameEndResultDetailData> detailDatas =
                                    SimpleCustomTypeHelper.DeserializeListFromBase64<GameEndResultDetailData>(
                                        battleSummaryData.Value);
                                lobbyBattleServerData.GameEndResultDetailDatas = detailDatas;
                                lobbyBattleServerData.Settlement = true;
                            }
                        }
                        Log.InfoFormat("-- BattleTeamData -- lobbyBattleServerData:{0} modeId:{1} battleServerId:{2}", lobbyBattleServerData, modeId, lobbyBattleServerData.BattleServerId);
                        lobbyBattleServerDatas.Add(lobbyBattleServerData);
                    }
                }

                lobbyBattleServerDatas.Sort((a, b) =>
                {
                    // 按照离开时间倒序排LastLeaveTime
                    return b.LastLeaveTime.CompareTo(a.LastLeaveTime);
                });

                if (lobbyBattleServerDatas.Count > 0)//拿第一个战局数据刷新大厅模型身上数据
                {
                    GetBattleTeamData(reqModule,lobbyBattleServerDatas[0].TeamId,lobbyBattleServerDatas[0].BattleServerId,null);
                }

                RegisterLocalNotification();
                if (callback != null)
                {
                    callback();
                }
                Mc.Msg.FireMsg(EventDefine.LobbyBattleServerDataUpdate);
            });
        }
        
        public string GetFirstBattleServerId()
        {
            if (lobbyBattleServerDatas.Count > 0)
            {
                return lobbyBattleServerDatas[0].BattleServerId;
            }
            return string.Empty;
        }

        private void RegisterLocalNotification()
        {
            try
            {

                bool isGet = Mc.LobbyTime.TryGetCurSvrUtc0TimeStamp(out var curSvrUtc0TimeStamp);
                if (!isGet)
                {
                    Log.Error("-- BattleServerLocalNotification -- TryGetCurSvrUtc0TimeStamp failed");
                    return; // 获取当前服务器时间戳失败
                }
                Log.InfoFormat("-- BattleServerLocalNotification -- curSvrUtc0TimeStamp: {0}", curSvrUtc0TimeStamp);
                
                if (lobbyBattleServerDatas.Count == 0)
                {
                    Log.Info("-- BattleServerLocalNotification -- lobbyBattleServerDatas.Count == 0");
                    return; // 没有战局数据
                }
                Log.InfoFormat("-- BattleServerLocalNotification --  lobbyBattleServerDatas.Count: {0}", lobbyBattleServerDatas.Count);
                int scriptPushRegistration = McCommon.Tables.TbStoryConst.ScriptPushRegistration;
                int scriptPushNotification = McCommon.Tables.TbStoryConst.ScriptPushNotification;
                List<string> battleServerIdList = new();
                ulong endTime = 0;
                foreach (var lobbyBattleServerData in lobbyBattleServerDatas)
                {
                    if (lobbyBattleServerData.Settlement)                    // 如果结算了，就不需要注册本地通知了
                    {
                        Log.InfoFormat("-- BattleServerLocalNotification -- Settlement, BattleServerId: {0}", lobbyBattleServerData.BattleServerId);
                        continue;
                    }

                    string key = ZString.Format("localNotification_{0}", lobbyBattleServerData.BattleServerId);
                    bool hasKey = ClientPlayerPrefs.HasKey(key);
                    if (hasKey)
                    {
                        string val = ClientPlayerPrefs.GetString(key);
                        Log.InfoFormat("-- BattleServerLocalNotification -- hasKey, key: {0}, val: {1}", key, val);
                    }

                    int modeId = lobbyBattleServerData.ModeId;
                    OBJPlayModeMain gameMode = McCommon.Tables.TBPlayModeMain.GetOrDefault(modeId);
                    bool isNewbie = gameMode is { IsNewbie: true };
                    if (isNewbie) // 新手模式不需要注册本地通知
                    {
                        Log.InfoFormat("-- BattleServerLocalNotification -- isNewbie, BattleServerId: {0}", lobbyBattleServerData.BattleServerId);
                        continue;
                    }

                    ulong time = lobbyBattleServerData.BattleEndTime - (ulong)curSvrUtc0TimeStamp; // 战局结束时间 - 当前时间
                    Log.InfoFormat("-- BattleServerLocalNotification -- BattleServerId: {0}, time: {1}, curSvrUtc0TimeStamp: {2}, BattleEndTime: {3}",
                        lobbyBattleServerData.BattleServerId, time, curSvrUtc0TimeStamp, lobbyBattleServerData.BattleEndTime);
                    if (time < (ulong)scriptPushRegistration && time > (ulong)scriptPushNotification)
                    {
                        if (hasKey)
                        {
                            Log.Info(
                                $"-- BattleServerLocalNotification -- hasKey, BattleServerId: {lobbyBattleServerData.BattleServerId}");
                            continue; // 已经注册过了
                        }

                        endTime = lobbyBattleServerData.BattleEndTime; // 战局结束时间
                        battleServerIdList.Add(lobbyBattleServerData.BattleServerId);
                    }
                }

                int count = battleServerIdList.Count;
                if (count == 0)
                {
                    Log.Info("-- BattleServerLocalNotification -- No need to register local notification");
                    return; // 没有需要注册的战局
                }

                ulong notificationTime = endTime - (ulong)scriptPushNotification; // 战局结束时间 - 通知提前时间
                ulong delayTime = notificationTime - (ulong)curSvrUtc0TimeStamp; // 延迟时间
                DateTime pushTime = DateTime.Now.AddSeconds(delayTime);
                switch (count)
                {
                    case > 1:
                        Mc.PushNotice.AddLocalNotification(7, pushTime, 0);
                        break;
                    case > 0:
                        Mc.PushNotice.AddLocalNotification(6, pushTime, 0);
                        break;
                }
                Log.InfoFormat("-- BattleServerLocalNotification -- Register local notification, count: {0}, delayTime: {1}, pushTime: {2}", count, delayTime, pushTime);

                foreach (var battleServerId in battleServerIdList)
                {
                    string key = ZString.Format("localNotification_{0}", battleServerId);
                    ClientPlayerPrefs.SetString(key, delayTime.ToString(), false);
                    Log.InfoFormat("-- BattleServerLocalNotification -- Set key: {0}, BattleServerId: {1}", key, battleServerId);
                }
            }
            catch
            {
                // ignored
            }
        }

        //是否有战局需要显示队长弹劾
        public bool HasCaptainImpeachment()
        {
            for (int i = 0; i < lobbyBattleServerDatas.Count; i++)
            {
                if (IsShowCaptainImpeachmentTip(lobbyBattleServerDatas[i].BattleServerId))
                {
                    return true;
                }
            }

            return false;
        }

        //显示历史战局入口红点
        public bool IsShowLobbyHistoryBattleRedDot()
        {
            //新增>队长弹劾>奖励提示
            if (ShowLobbyHistoryRedDot() || HasCaptainImpeachment() || ShowLobbyHistoryBattleRewardRedDot())//是否有新增战局
            {
                return true;
            }
            return false;
        }

        //大厅历史战局入口是否需要显示红点
        public bool ShowLobbyHistoryRedDot()
        {
            for (int i = 0; i < lobbyBattleServerDatas.Count; i++)
            {
                if (ShowRedByBattleId(lobbyBattleServerDatas[i].BattleServerId))
                {
                    return true;
                }
            }
            return false;
        }

        //大厅历史战局奖励入口是否需要显示红点
        public bool ShowLobbyHistoryBattleRewardRedDot()
        {
            for (int i = 0; i < lobbyBattleServerDatas.Count; i++)
            {
                if (lobbyBattleServerDatas[i].Settlement)
                {
                    return true;
                }
            }
            return false;
        }
        
        public bool ShowLobbyHistoryBattleRewardRedDotByBattleServerId(string battleServerId)
        {
            for (int i = 0; i < lobbyBattleServerDatas.Count; i++)
            {
                if (lobbyBattleServerDatas[i].BattleServerId == battleServerId && lobbyBattleServerDatas[i].Settlement)
                {
                    return true;
                }
            }
            return false;
        }

        public bool ShowRedByBattleId(string battleId)
        {
            var redDotId = ClientPlayerPrefs.GetString(battleId);
            Log.InfoFormat("ShowRedByBattleId battleId:{0}  redDotId:{1}", battleId, redDotId);
            return !string.IsNullOrEmpty(redDotId);
        }

        //删除一个战局
        public void ReqDeleteBattleServer(EHttpReqModule reqModule, string battleServerId, Action callback = null)
        {
            JSONObject param = new();
            param.Add("battleServerID", battleServerId);
            MicroServiceClient.Instance.CallLobbyPost("warzoneservice/deleteplayerserver", reqModule, (ret) =>
            {
                if (ret == null)
                {
                    return;
                }
                var nextCanRemoveBattleTime = ret["nextCanRemoveBattleTime"].AsLong;
                NextCanRemoveBattleTime = nextCanRemoveBattleTime;
                Mc.Lobby.DeleteBattleServer(battleServerId);
                callback?.Invoke();
                Mc.Msg.FireMsg(EventDefine.DeleteBattleServerSuccessNotice);
            }, param);
        }
        
        //已知战局结束时间，倒推剧本阶段
        public int GetStageIdByEndTime(long endTime,  out string stageName)
        {
            var isGet = Mc.LobbyTime.TryGetCurSvrUtc0TimeStamp(out var curSvrUtc0TimeStamp);
            stageName = String.Empty;
            if (isGet == false || endTime == 0) 
            {
                return -1;
            }
            long remainingTime = endTime - curSvrUtc0TimeStamp;
            long elapsedTime = 0;
            for (int i = 0; i < Mc.Tables.TbStoryStage.DataList.Count; i++)
            {
                var config = Mc.Tables.TbStoryStage.DataList[i];
                elapsedTime += config.StageTime; // 累加每个阶段的时间
                if (elapsedTime > remainingTime)
                {
                    stageName = config.StageName;
                    return config.Id; // 找到当前阶段ID
                }
            }
            return -1; // 未找到对应的阶段
        } 
        
        /// <summary>
        /// 获取当前历史战局队伍信息
        /// </summary>
        /// <param name="teamID"></param>
        /// <param name="battleServerID"></param>
        /// <param name="callback"></param>
        public void ReqBattleTeam(EHttpReqModule reqModule, string teamID, string battleServerID, Action<JSONNode> callback = null)
        {
            JSONObject param = new();
            param.Add("teamID", teamID);
            param.Add("battleServerID", battleServerID);
            MicroServiceClient.Instance.CallLobbyPost("warzoneservice/battleteam", reqModule, (res) =>
            {
                Log.InfoFormat("ReqBattleTeam res:{0}", res);
                if(callback != null)
                {
                    callback(res);
                }
            }, param);
        }

        /// <summary>
        /// 加入战斗请求,正式流程（历史战局，以及主界面快速加入用这个）
        /// </summary>
        /// <param name="serverId"></param>
        public void JoinBattle(EHttpReqModule reqModule, string serverId, bool isNeedSetReadyToJoinWarzone = true)
        {
            if (Mc.LobbyTeam.IsReadyToJoinWarzone && isNeedSetReadyToJoinWarzone)//清一下组队进服标记，避免组队readytojoinwarzone 之后,只有一种情况不清理，就是组队进服
            {
                Mc.LobbyTeam.IsReadyToJoinWarzone = false;
            }
            JSONObject param = new();
            param.Add("battleServerId", serverId);
            var platform = TeamStaticUtil.PlatformMobile;
#if STANDALONE_REAL_PC
                platform = TeamStaticUtil.PlatformPc;
#endif
            param.Add("platform", platform);
            Mc.LoginStep.StepLog($"[JoinBattle] 服务器ID {serverId} reqModule {reqModule} platform:{platform}");
            MicroServiceClient.Instance.CallLobbyPost("warzoneservice/join", reqModule, (res) =>
            {
                Mc.LoginStep.StepLog($"[JoinBattle] warzoneservice/join success res {res} reqModule {reqModule}");
                Log.InfoFormat("Medal info openBattleServerID:{0}, HasRankPoint:{1}, serverId:{2}", Mc.Medal.openBattleServerID, Mc.Medal.HasRankPoint, serverId);
                if (Mc.Medal.ConditionUnlock) //勋章解锁再给服务器发积分结算
                {                 
                    if (Mc.Medal.HasRankPoint && string.IsNullOrEmpty(Mc.Medal.openBattleServerID))
                    {
                        Mc.Medal.SetSettleStyleRankPointsSwitch(serverId);
                    }
                }
            }, param,false, (error) => {
                if (error == null || !error.HasKey("HasError") || !error.HasKey("LogicError")) return;
                var errorCode = error["LogicError"].AsInt;
                Log.InfoFormat("warzoneservice/join errCode : {0}", errorCode);
                Mc.LobbyTeam.ErrorCodeTransTip(errorCode);
                        
                if (Mc.LobbyTeam.IsReadyToJoinWarzone)//这种情况清理队伍数据，并给提示
                {
                    Mc.LobbyTeam.IsReadyToJoinWarzone = false;
                }
            });
            Mc.Voice.ServerBattleId = serverId;
            Mc.PersistentData.serverId = serverId;
        }
        
        /// <summary>
        /// 巡查者模式加入战斗请求
        /// </summary>
        /// <param name="serverId"></param>
        public void ObserverJoinBattle(EHttpReqModule reqModule, string serverId)
        {
            JSONObject param = new();
            param.Add("battleServerID", serverId);
            Mc.LoginStep.StepLog($"[ObserverJoinBattle] 服务器ID {serverId} reqModule {reqModule}");
            MicroServiceClient.Instance.CallLobbyPost("warzoneservice/observerjoin", reqModule, (res) =>
            {
                Mc.LoginStep.StepLog($"[JoinBattle] warzoneservice/join success res {res} reqModule {reqModule}");
            }, param);
            Mc.Voice.ServerBattleId = serverId;
            Mc.PersistentData.serverId = serverId;
        }

        /// <summary>
        /// 加入战斗请求,服务器列表进入用这个
        /// </summary>
        /// <param name="serverId"></param>
        public void JoinBattleByServerList(EHttpReqModule reqModule, string serverId)
        {
            JSONObject param = new();
            param.Add("battleServerId", serverId);
            var battleIdUrl = "dbg/warzoneservice/join";
            if (Mc.ServerListClient.IsPrivateServerTags())
            {
                battleIdUrl = "warzoneservice/joinwhitelistbattle";
            }
            Mc.LoginStep.StepLog($"[JoinBattleByServerList] 服务器ID {serverId} battleIdUrl {battleIdUrl}");
            MicroServiceClient.Instance.CallLobbyPost(battleIdUrl, reqModule, (res) =>
            {
                Mc.LoginStep.StepLog($"[JoinBattleByServerList] success {serverId} battleIdUrl {battleIdUrl}");
            }, param);
            Mc.Voice.ServerBattleId = serverId;
            Mc.PersistentData.serverId = serverId;
        }
        
        //获取战斗场预约列表及当前场次
        public void ReqPostBattleRoundList(EHttpReqModule reqModule,  int gameMode, Action callback)
        {
            if (!Mc.LobbyTeam.IsAppointmentModeByModeID(gameMode))//如果不是预约模式就return掉，不请求
            {
                Log.InfoFormat("ReqPostBattleRoundList gameMode:{0} is not appointment mode, return", gameMode);
                return;
            }
            var isGet = Mc.LobbyTime.TryGetCurSvrUtc0TimeStamp(out var curTime);
            var curEndTime = GetCurrentRoundEndTime();
            if (curTime < curEndTime && curEndTime != 0 && isGet)
            {
                Log.InfoFormat("ReqPostBattleRoundList get from cache curTime={0} curEndTime={1}", curTime, curEndTime);
                callback?.Invoke();
                return;
            }
            JSONObject param = new();
            int count = Mc.Tables.TBConsts.MaxAppointmentListNum;//预约时段的最大显示数量
            int historyCount = Mc.Tables.TBConsts.MaxHistoryAppointmentListNum;//预约时段的最大显示数量
            param.Add("count", count);
            param.Add("gameMode", gameMode);
            param.Add("historyCount", historyCount);
            MicroServiceClient.Instance.CallLobbyPost("warzoneservice/battleroundlist", reqModule,  (jsonNode) =>
            {
                Log.InfoFormat("warzoneservice/battleroundlist success jsonNode={0}", jsonNode);
                RoundTimeDataList.Clear();                                                               
                 var roundList = jsonNode["roundList"].AsArray;                                                      
                 for (int i = 0; i < roundList.Count; i++)                                                           
                 {                                                                                                   
                     var roundData = new RoundTimeData();                                                            
                     roundData.startTime = roundList[i]["startTime"].AsLong;                                          
                     roundData.endTime = roundList[i]["endTime"].AsLong;                                              
                     roundData.startTimeStr = roundList[i]["startTimeStr"].Value;                                    
                     roundData.endTimeStr = roundList[i]["endTimeStr"].Value;                                        
                     RoundTimeDataList.Add(roundData);                                                      
                 }       
                 var currentRound = jsonNode["currentRound"];
                 if (currentRound != null)
                 {
                     currentRoundData.startTime = currentRound["startTime"].AsLong;
                     currentRoundData.endTime = currentRound["endTime"].AsLong;
                     currentRoundData.startTimeStr = currentRound["startTimeStr"].Value;
                     currentRoundData.endTimeStr = currentRound["endTimeStr"].Value;
                 }
                 var historyRoundList = jsonNode["historyRoundList"].AsArray;
                 HistoryRoundList.Clear();
                 for (int i = 0; i < historyRoundList.Count; i++)
                 {
                     var roundData = new RoundTimeData();
                     roundData.startTime = historyRoundList[i]["startTime"].AsLong;
                     roundData.endTime = historyRoundList[i]["endTime"].AsLong;
                     roundData.startTimeStr = historyRoundList[i]["startTimeStr"].Value;
                     roundData.endTimeStr = historyRoundList[i]["endTimeStr"].Value;
                     HistoryRoundList.Add(roundData);
                 }
                 callback?.Invoke(); 
            },param, false, (error) => {
                if (error == null || !error.HasKey("HasError") || !error.HasKey("LogicError")) return;
                var errorCode = error["LogicError"].AsInt;
                Log.InfoFormat("warzoneservice/battleroundlist errCode : {0}", errorCode);
                Mc.LobbyTeam.ErrorCodeTransTip(errorCode);
            });
        }

        public List<RoundTimeData> GetHistoryRoundTimeData()
        {
            if(HistoryRoundList.Count == 0)
            {
                return null;
            }

            return HistoryRoundList;
        }

        public List<RoundTimeData> GetRoundTimeDataList()
        {
            if(RoundTimeDataList.Count == 0)
            {
                return null;
            }

            return RoundTimeDataList;
        }

        //获取预约列表的下一个开服时间
        public string GetNextCurrentRoundTimeStr()
        {
            string nextPreStartTimeStr = String.Empty;
            if (RoundTimeDataList.Count == 0)
            {
                return nextPreStartTimeStr;
            }
            
            long now = TimeStampUtil.GetNowTimeStampSec();
            for (int i = 0; i < RoundTimeDataList.Count; i++)
            {
                if (RoundTimeDataList[i].startTime > now)
                {
                    nextPreStartTimeStr = RoundTimeDataList[i].startTimeStr.Replace("-", "/");
                    break;
                }
            }

            DateTime startTime;
            if (DateTime.TryParse(nextPreStartTimeStr, out startTime))
            {
                nextPreStartTimeStr = startTime.ToString("yyyy/M/d H:mm");
            }
            else
            {
                return String.Empty; 
            }
            return nextPreStartTimeStr;
        }
        public long GetCurrentRoundTime()
        {
            return currentRoundData.startTime;
        }
        
        public long GetCurrentRoundEndTime()
        {
            if (currentRoundData == null)
            {
                return 0;
            }
            return currentRoundData.endTime;
        }
        
        public string GetCurrentRoundTimeStrNoSec()
        {
            string currentRoundTimeStr = "";
            if (currentRoundData.startTime == 0)
            {
                return currentRoundTimeStr;
            }
            DateTime startTime;
            if (DateTime.TryParse(currentRoundData.startTimeStr.Replace("-", "/"), out startTime))
            {
                currentRoundTimeStr = startTime.ToString("yyyy/M/d H:mm");
            }
            else
            {
                return String.Empty; 
            }

            return currentRoundTimeStr;
        }
        
        public string GetCurrentRoundTimeStr()
        {
            string currentRoundTimeStr = "";
            if (currentRoundData.startTime == 0)
            {
                return currentRoundTimeStr;
            }
            currentRoundTimeStr = currentRoundData.startTimeStr.Replace("-", "/");

            return currentRoundTimeStr;
        }
        public string GetStartTimeStrByStartTime(long startTime)
        {
            if(RoundTimeDataList.Count == 0 || startTime == 0)
            {
                return "";
            }

            string startTimerStr = "";
            for (int i = 0; i < RoundTimeDataList.Count; i++)
            {
                if(RoundTimeDataList[i].startTime == startTime)
                {
                    startTimerStr = RoundTimeDataList[i].startTimeStr.Replace("-", "/");
                    break;
                }
            }
            return startTimerStr;
        }

        public long GetEndTimeByStartTime(long startTime)
        {
            if(RoundTimeDataList.Count == 0)
            {
                return 0;
            }

            long endTime = 0;
            for (int i = 0; i < RoundTimeDataList.Count; i++)
            {
                if(RoundTimeDataList[i].startTime == startTime)
                {
                    endTime = RoundTimeDataList[i].endTime;
                    break;
                }
            }
            return endTime;
        }
        
        //判断当前的模式是否是预约模式 :modeid在玩法模式主表中配置的GSMatchAppointment true:预约模式 false:非预约模式
        public bool IsAppointmentMode(int modeId)
        {
            var config = Mc.Tables.TBPlayModeMain.GetOrDefault(modeId);
            if (config == null)
            {
                return false;
            }

            return config.GSMatchAppointment;
        }

        #region 大厅活动配置时间相关接口
        // 使用统一的集合来存储时间规则ID，按类型分类
        public Dictionary<ENUMSwitchTimeType, List<int>> GetTimeRules(int modeId)
        {
            var config = Mc.Tables.TBGameMode.GetOrDefault(modeId);
            var timeRuleGroup = config.TimeRuleGroup;

            // 使用统一的集合来存储时间规则ID，按类型分类
            var timeRules = new Dictionary<ENUMSwitchTimeType, List<int>>();

            foreach (var ruleId in timeRuleGroup)
            {
                var conditionSwitchConfig = Mc.Tables.TBConditionSwitch.GetOrDefault(ruleId);
                if (conditionSwitchConfig == null || !conditionSwitchConfig.Valid) continue;

                if (!timeRules.ContainsKey(conditionSwitchConfig.TimeType))
                {
                    timeRules[conditionSwitchConfig.TimeType] = new List<int>();
                }
                timeRules[conditionSwitchConfig.TimeType].Add(ruleId);
            }

            return timeRules;
        }
        
        //timeRules: 传入的时间规则字典 {[ENUMSwitchTimeType.Month, List<int>], [ENUMSwitchTimeType.Week, List<int>], [ENUMSwitchTimeType.Day, List<int>]}
        //List<int> 一般是策划配置的多个活动id，对应通用活动开关表的id
        //typesToCheck 是需要进行筛选的ENUMSwitchTimeType数组，一般是同级别的，例如ENUMSwitchTimeType.FOREVER和STABLE
        public bool CheckIfOpenInTimeframes(Dictionary<ENUMSwitchTimeType, List<int>> timeRules, ENUMSwitchTimeType[] typesToCheck)
        {
            foreach (var timeType in typesToCheck)
            {
                if (timeRules.TryGetValue(timeType, out var ids))
                {
                    foreach (var id in ids)
                    {
                        var conditionSwitchConfig = Mc.Tables.TBConditionSwitch.GetOrDefault(id);
                        if (conditionSwitchConfig == null) continue;

                        var timeParam1 = conditionSwitchConfig.TimeParam1;
                        var timeParam2 = conditionSwitchConfig.TimeParam2;
                        if (Mc.Activity.IsOpenInSpecifiedTimeframe(timeType, timeParam1, timeParam2, out long diffTime))
                        {
                            return true; // 找到配置的开放时间，立即返回 true
                        }
                    }
                }
            }
            return false; // 没有开放的时间
        }
        //传入模式，判断该模式是否在开放时间内
        //显示规则:先按照大时间判断是否在开放时间内，例如：2024-01-01 00:00:00 - 2024-11-02 00:00:00，需要考虑多段时间段
        //如果在这个时间段内，再判断月的格式时间范围，例如：11-20:30:00 - 24-20:30:00 表示每个月11号的20:30到每个月24号的20:30开放，需要考虑多段时间段
        //如果在这个时间段内，再判断周的格式时间范围，例如：3-20:30:00 - 4-20:30:00 表示每周三的20:30到周四的20:30开放，需要考虑多段时间段
        //如果在这个时间段内，再判断天的格式时间范围，例如：10:30:00 - 20:30:00 表示每天的10:30到的20:30开放，需要考虑多段时间段
        public bool IsActivityOpenByModeId(int modeId)
        {
            var config = Mc.Tables.TBGameMode.GetOrDefault(modeId);
            var timeRuleGroup = config.TimeRuleGroup;

            // 如果没有时间规则，返回 true
            if (timeRuleGroup == null || timeRuleGroup.Length == 0)
            {
                return true;
            }

            var timeRules = GetTimeRules(modeId);
            // 定义优先级顺序和条件逻辑
            var priorityGroups = new[]
            {
                new[] { ENUMSwitchTimeType.FOREVER, ENUMSwitchTimeType.STABLE }, // 高优先级配置
                new[] { ENUMSwitchTimeType.MONTH },                              // 中等优先级配置
                new[] { ENUMSwitchTimeType.WEEK },                               // 低优先级配置
                new[] { ENUMSwitchTimeType.DAY }                                 // 最低优先级配置
            };

            if((timeRules.ContainsKey(ENUMSwitchTimeType.FOREVER) || timeRules.ContainsKey(ENUMSwitchTimeType.STABLE))
               && !CheckIfOpenInTimeframes(timeRules, priorityGroups[0]))
            {
                return false; 
            } 
            //先把所有false的情况都排除掉，剩下的继续按照priorityGroups优先级依次往下判断到最后一个优先级，返回是否开启
            bool allGroupsOpen = true;
            for (int i = 1; i < priorityGroups.Length; i++)
            {
                bool isJumpCheck = false;
                for (int j = 0; j < priorityGroups[i].Length; j++)
                {
                    if (timeRules.ContainsKey(priorityGroups[i][j]))
                    {
                        isJumpCheck = true; // 如果没有这个时间类型的规则，跳过检查
                    }
                }
                if (isJumpCheck && !CheckIfOpenInTimeframes(timeRules, priorityGroups[i]))
                {
                    allGroupsOpen = false; // 如果发现有任意一组是 false，设置为 false
                }
            }
            return allGroupsOpen; 
        }
        #endregion
        /// <summary>
        /// 根据modeid获取某一个模式服务器列表
        /// </summary>
        /// <param name="modeID"></param>
        /// <returns></returns>
        public int GetBattleServerListByModeID(int modeID)
        {
            return GetLobbyBattleServerDataByModeId(modeID).Count;
        }
        
        /// <summary>
        /// 根据模式ID获取历史战局数据
        /// </summary>
        /// <param name="modeId"></param>
        /// <returns></returns>
        public List<LobbyBattleServerData> GetLobbyBattleServerDataByModeId(int modeId)
        {
            if (lobbyBattleServerDatasDic.ContainsKey(modeId))
            {
                Log.InfoFormat("lobbyBattleServerDatasDic[modeId]:{0} count:{1} modeId:{2}", lobbyBattleServerDatasDic[modeId], lobbyBattleServerDatasDic[modeId].Count, modeId);
                return lobbyBattleServerDatasDic[modeId];
            }
            List<LobbyBattleServerData> tempList = new List<LobbyBattleServerData>();
            for (int i = 0; i < lobbyBattleServerDatas.Count; i++)
            {
                if (lobbyBattleServerDatas[i].ModeId == modeId)
                {
                    tempList.Add(lobbyBattleServerDatas[i]);
                }
            }
            lobbyBattleServerDatasDic.Add(modeId, tempList);
            return tempList;
        }

        /// <summary>
        /// 判断玩家的名称是否合法
        /// </summary>
        private int IsPlayerNameLegal(string name)
        {
            
            // 名字是否为空
            if (string.IsNullOrWhiteSpace(name))
            {
                return LanguageConst.NameCardEmpty;
            }
            //命名含有非法字符
            if (!StringCheckUtility.IsLegalKeyboardCharacterByStr(name))
            {
                return LanguageConst.IllegalNaming;
            }

            // 长度是否合法
            // 策划希望一个中文字符算作两个长度, 其他字符算作一个长度
            // 在此基础上, 限制长度
            int len = 0;
            foreach (char c in name)
            {
                len += 1;
                // 如果字符是中文，增加2到长度  
                if (StringCheckUtility.IsChineseChar(c)) len += 1;
            }
            int minLen = Mc.Tables.TBConsts.PlayerNameLengthMin;
            int maxLen = Mc.Tables.TBConsts.PlayerNameLengthMax;
            if (len < minLen)
            {
                return LanguageConst.NameCardRuler;
            }
            if (len > maxLen)
            {
                return LanguageConst.NameCardRuler2;
            }
            // 是否与当前名称相同
            if (Mc.Config.nickName.Equals(name))
            {
                return LanguageConst.NameCardNoModify;
            }
            return 0;
        }

        /// <summary>
        /// 判断玩家的名称是否合法
        /// </summary>
        public bool IsPlayerNameLegal(string name, bool withErrorTips)
        {
            var error = IsPlayerNameLegal(name);
            if (withErrorTips && error > 0)
            {
                string msg = LanguageManager.GetTextConst(error);
                Mc.MsgTips.ShowDebugRealtimeWeakTip(msg);
            }
            return error == 0;
        }

        /// <summary>
        /// 记录下次可删除服务器时间
        /// </summary>
        public void DeleteBattleServer(string battleServerId)
        {
            int selectMode = 0;
            foreach (var list in lobbyBattleServerDatasDic.Values)
            {
                for (int i = 0; i < list.Count; i++)
                {
                    if (list[i].BattleServerId == battleServerId)
                    {
                        selectMode = list[i].ModeId;
                        list.RemoveAt(i);
                        break;
                    }
                }
            }

            for (int i = 0; i < lobbyBattleServerDatas.Count; i++)
            {
                if (lobbyBattleServerDatas[i].BattleServerId == battleServerId)
                {
                    lobbyBattleServerDatas.RemoveAt(i);
                    break;
                }
            }

            var lastSvrInfo = Mc.PersistentData.GetCommonAccountData()?.lastJoinSvrInfo ?? string.Empty;

            var svrInfos = lastSvrInfo.Split("#");
            if (null == svrInfos || svrInfos.Length < 3) return;
            string svrType = svrInfos[0];
            string id = svrInfos[1];
            string strLoadType = svrInfos[2];

            if (id == battleServerId)
            {
                Mc.PersistentData.ModityCommonAccountValue(data => data.lastJoinSvrInfo = string.Empty);
            }
        }

        public LobbyBattleServerData GetLobbyBattleServerData(string battleServerId)
        {
            for (int i = 0; i < lobbyBattleServerDatas.Count; i++)
            {
                if (lobbyBattleServerDatas[i].BattleServerId == battleServerId)
                {
                    return lobbyBattleServerDatas[i];
                }
            }
            return null;
        }

        public long GetCurrentUnixTimestampInSeconds()
        {
            return (long)(DateTime.UtcNow - new DateTime(1970, 1, 1)).TotalSeconds;
        }

        public bool IsHasLimitModeOpen(out string modeName)
        {
            var config = Mc.Tables.TBGameMode.DataList;
            for (int i = 0; i < config.Count; i++)
            {
                if (config[i].EntryType == ENUMGameType.Dynamic)
                {
                    var modeId = config[i].GameModeID;
                    var condition1 = config[i].UnlockLevel <= Mc.Config.level;
                    var condition2 = IsActivityOpenByModeId(modeId);
                    if (condition1 && condition2 && modeId != 1001)//1001是新手关，不进入玩法选择模式
                    {
                        modeName = config[i].GameModeName;
                        return true;
                    }
                }
            }
            modeName = string.Empty;
            return false;
        }

        //组队需求，需要根据组队返回的lastMatchWarzoneTime从lobbyBattleServerDatas里找到5s内的战斗服id,这个5秒是大厅服的潜规则
        public void GetBobbyBattleServerDataByLastMatchWarzoneTime(ulong lastMatchWarzoneTime, out string serverId)
        {
            serverId = string.Empty;
            for (int i = 0; i < lobbyBattleServerDatas.Count; i++)
            {
                var lastJoinTime = lobbyBattleServerDatas[i].LastJoinTime;
                ulong.TryParse(lobbyBattleServerDatas[i].LastLeaveTime, out var lastLeaveTime);
                Log.InfoFormat("GetBobbyBattleServerDataByLastMatchWarzoneTime--i：{0} lastMatchWarzoneTime:{1} serverOpenedTime：{2}", i, lastMatchWarzoneTime, lastJoinTime);
                ulong targetTime = int.MaxValue;
                if (ulong.TryParse(lastJoinTime, out var time) && time > lastLeaveTime)
                {
                    targetTime = time - lastMatchWarzoneTime;
                }
                    
                if (targetTime < 5)
                {
                    serverId = lobbyBattleServerDatas[i].BattleServerId;
                    Log.InfoFormat("GetBobbyBattleServerDataByLastMatchWarzoneTime--lastMatchWarzoneTime:{0} serverOpenedTime：{1} serverId:{2}", lastMatchWarzoneTime, lastJoinTime, serverId);
                }
            }
        }
        // 获取战局结算发放奖励并移除历史战局
        public void GetBattleTeamData(EHttpReqModule reqModule, string battleServerId, Action callback = null)
        {
            JSONObject param = new();
            param.Add("battleServerID", battleServerId);
            MicroServiceClient.Instance.CallLobbyPost("warzoneservice/awardandremoveserver", reqModule, (res) =>
            {
                Log.InfoFormat("GetBattleTeamData res:{0}", res);
                // 移除历史战局
                Log.InfoFormat ("-- BattleTeamData -- battleServerId:{0} lobbyBattleServerDatas.Count:{1}", battleServerId, lobbyBattleServerDatas.Count);
                for (int i = lobbyBattleServerDatas.Count - 1; i >= 0; i--)
                {
                    if (lobbyBattleServerDatas[i].BattleServerId == battleServerId)
                    {
                        lobbyBattleServerDatas.RemoveAt(i);
                        Log.InfoFormat ("-- BattleTeamData -- remove battleServerId:{0} lobbyBattleServerDatas.Count:{1}", battleServerId, lobbyBattleServerDatas.Count);
                        break;
                    }
                }

                if (callback != null)
                {
                    callback();
                }
            }, param, false, error =>
            {
                MicroServiceClient.PopupResultError(error);
            });
        }
        
        //客户端本地是否有存 key 是由TeamId和BattleServerId拼接而成的字符串
        public bool CaptainImpeachmentTipIsInClientPrefs(string battleServerId)
        {
            var serverData = GetLobbyBattleServerData(battleServerId);
            if (serverData == null || string.IsNullOrEmpty(serverData.LastLeaveTime))
            {
                return false;
            }
            //获取当前战斗服id
            var prefsKey = ZString.Format("CaptainImpeachmentTip_{0}_{1}_{2}", serverData.TeamId,battleServerId, serverData.LastLeaveTime); 
            var isPrefs =  ClientPlayerPrefs.GetString(prefsKey,"");
            if(string.IsNullOrEmpty(isPrefs))
            {
                return false;
            }
            return true;
        }

        public void SetCaptainImpeachmentTipClientPrefs(string battleServerId)
        {
            var serverData = GetLobbyBattleServerData(battleServerId);
            if (serverData == null || string.IsNullOrEmpty(serverData.LastLeaveTime))
            {
                return;
            }
            //获取当前战斗服id
            var prefsKey = ZString.Format("CaptainImpeachmentTip_{0}_{1}_{2}", serverData.TeamId,battleServerId, serverData.LastLeaveTime); 
            ClientPlayerPrefs.SetString(prefsKey," true");
        }
        
        //是否显示队长弹劾提示
        public bool IsShowCaptainImpeachmentTip(string battleServerId)
        {
            var serverData = GetLobbyBattleServerData(battleServerId);
            if (serverData == null || string.IsNullOrEmpty(serverData.LastLeaveTime))
            {
                return false;
            }
            
            var strKey = ZString.Format("battleTeamData_{0}_{1}", serverData.TeamId, battleServerId); 
            if(!battleTeamDataDic.TryGetValue(strKey, out var battleTeamData))
            {
                return false;
            }
            //如果没有队伍或者队伍人数只剩下自己，一律返回false
            if (battleTeamData.TeamMemberDataList.Count <= 1)
            {
                return false;
            }
            if(!battleTeamData.Leader.Equals(Mc.Config.roleId))
            {
                return false; //如果不是队长，直接返回false
            }
            
            var isGet = Mc.LobbyTime.TryGetCurSvrUtc0TimeStamp(out var curSvrUtc0TimeStamp);
            if (isGet == false)
            {
                return false;
            }
            var captainImpeachmentTime = Mc.Tables.TbGlobalConfig.CaptainImpeachmentLobbyTipTime;
            Log.InfoFormat("curSvrUtc0TimeStamp:{0} LastLeaveTime:{1} captainImpeachmentTime:{2}", 
                curSvrUtc0TimeStamp, serverData.LastLeaveTime, captainImpeachmentTime);
            if (curSvrUtc0TimeStamp - long.Parse(serverData.LastLeaveTime) >= captainImpeachmentTime * 3600)
            {
                return true;
            }
            return false;
        }
    }
}