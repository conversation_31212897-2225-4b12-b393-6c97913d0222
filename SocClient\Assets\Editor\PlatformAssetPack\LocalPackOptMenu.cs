﻿using Soc.Common.Unity.PlatformResOpt;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using Object = UnityEngine.Object;

namespace PackTool
{
    //unity编辑器的菜单功能，主要包含preview的相关功能
    public class LocalPackOptMenu
    {
        public static SocLogger logger = LogHelper.GetLogger<LocalPackOptMenu>();
        
        [MenuItem("Assets/平台资产/外部功能/收集prefab内的override信息", false, 100)]
        public static void CollectPrefabOverrides()
        {
            List<string> allAssetPath = new ();
            var selectObjects = Selection.assetGUIDs;
            foreach (var assetGuid in selectObjects)
            {
                var assetPath = AssetDatabase.GUIDToAssetPath(assetGuid);

                if (AssetDatabase.IsValidFolder(assetPath))
                {
                    var allguids = AssetDatabase.FindAssets("t:GameObject", new[] { assetPath });
                    foreach (var guid in allguids)
                    {
                        var path = AssetDatabase.GUIDToAssetPath(guid);
                        allAssetPath.Add(path);
                    }
                }
                else
                {
                    allAssetPath.Add(assetPath);
                }
            }

            new PrefabModificationValidator().ValidateAllPrefabs(allAssetPath);
        }
        
        [MenuItem("Assets/平台资产/外部功能/统计动画内存", false, 100)]
        public static void PrintAnimStats()
        {
            if (Selection.assetGUIDs.Length == 0)
            {
                logger.ErrorFormat("没有选中任何资源");
                return;
            }
            
            HashSet<string> allAssetPath = new HashSet<string>();
            var selectObjects = Selection.assetGUIDs;
            foreach (var assetGuid in selectObjects)
            {
                var assetPath = AssetDatabase.GUIDToAssetPath(assetGuid);

                if (AssetDatabase.IsValidFolder(assetPath))
                {
                    var allguids = AssetDatabase.FindAssets("t:AnimationClip", new[] { assetPath });
                    foreach (var guid in allguids)
                    {
                        var path = AssetDatabase.GUIDToAssetPath(guid);
                        allAssetPath.Add(path);
                    }
                }
                else
                {
                    allAssetPath.Add(assetPath);
                }
            }
            
            
            Assembly asm = Assembly.GetAssembly(typeof(Editor));
            MethodInfo getAnimationClipStats = typeof(AnimationUtility).GetMethod("GetAnimationClipStats", BindingFlags.Static | BindingFlags.NonPublic);
            Type aniclipstats = asm.GetType("UnityEditor.AnimationClipStats");
            FieldInfo sizeInfo = aniclipstats.GetField ("size", BindingFlags.Public | BindingFlags.Instance);


            int allSize = 0;
            foreach (var assetPath in allAssetPath)
            {
                var clip = AssetDatabase.LoadAssetAtPath<AnimationClip>(assetPath);
                if (clip == null)
                {
                    logger.ErrorFormat("选中资源:{0}不是AnimationClip", assetPath);
                    continue;
                }
                
                var clipStats = getAnimationClipStats.Invoke(null, new object[] { clip });
                var size = (int)sizeInfo.GetValue(clipStats);
                
                logger.InfoFormat("选中资源:{0}的动画内存大小:{1}KB", assetPath,EditorUtility.FormatBytes(size ));
                allSize+= size;
            }
            

            logger.InfoFormat("收集到的所有资产大小:{0}", EditorUtility.FormatBytes(allSize ));
        }
        
        [MenuItem("Assets/平台资产/外部功能/收集依赖类型", false, 100)]
        public static void CollectDepTypes()
        {
            if (Selection.assetGUIDs.Length == 0)
            {
                logger.ErrorFormat("没有选中任何资源");
                return;
            }
            
            HashSet<string> allAssetPath = new HashSet<string>();
            var selectObjects = Selection.assetGUIDs;
            foreach (var assetGuid in selectObjects)
            {
                var assetPath = AssetDatabase.GUIDToAssetPath(assetGuid);

                if (AssetDatabase.IsValidFolder(assetPath))
                {
                    var allguids = AssetDatabase.FindAssets("t:GameObject", new[] { assetPath });
                    foreach (var guid in allguids)
                    {
                        var path = AssetDatabase.GUIDToAssetPath(guid);
                        allAssetPath.Add(path);
                    }
                }
                else
                {
                    allAssetPath.Add(assetPath);
                }
            }
            
            Dictionary<string,string> typeMaps = new Dictionary<string, string>();

            foreach (var assetPath in allAssetPath)
            {
                var dependencies = EditorUtility.CollectDependencies(new Object[] { AssetDatabase.LoadAssetAtPath<Object>(assetPath) });

                foreach (var dep in dependencies)
                {
                    var depPath = AssetDatabase.GetAssetPath(dep);
                    var ext = Path.GetExtension(depPath);
                    if (string.IsNullOrEmpty(ext))
                    {
                        continue;
                    }
                    typeMaps[ext] =  depPath;
                }
            }
            
            StringBuilder sb = new StringBuilder();
            foreach (var kv in typeMaps)
            {
                sb.AppendLine($"{kv.Key} 比如: {kv.Value}");
            }
            
            logger.InfoFormat("收集到的依赖类型:\n{0}", sb.ToString());
        }

        [MenuItem("Assets/平台资产/外部功能/对选中prefab进行空对象检查", false, 100)]
        public static void PrintCheckPrefabNull()
        {
            if (Selection.assetGUIDs.Length == 0)
            {
                logger.ErrorFormat("没有选中任何资源");
                return;
            }
            
            HashSet<string> allAssetPath = new HashSet<string>();
            var selectObjects = Selection.assetGUIDs;
            foreach (var assetGuid in selectObjects)
            {
                var assetPath = AssetDatabase.GUIDToAssetPath(assetGuid);

                if (AssetDatabase.IsValidFolder(assetPath))
                {
                    var files = Directory.GetFiles(assetPath, "*.prefab", SearchOption.AllDirectories);
                    foreach (var file in files)
                    {
                        var unityPath = PlatformPackPath.GetUnityPath(file);
                        allAssetPath.Add(unityPath);
                    }
                }
                else
                {
                    allAssetPath.Add(assetPath);
                }
            }

            foreach (var assetPath in allAssetPath)
            {
                var go = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
                if (go == null)
                {
                    var devResPath = PlatformPackPath.PathCombine(PlatformPackConst.svnRootDirPath, PlatformPackPath.DevRes);
                    var relativePath = PlatformPackPath.GetGameRelativePath( assetPath);
                    var testPath = PlatformPackPath.PathCombine(devResPath, relativePath);
                    var dir = Path.GetDirectoryName(testPath);
                    
                    // Debug.LogError(testPath+"  exist "+File.Exists(testPath)); 
                    var info = PlatformPackVCTools.RunSvnCommand("list -vR "+Path.GetFileName(assetPath), dir);
                    StringBuilder sb = new();
                    foreach (var str in info)
                    {
                        sb.AppendLine(str);
                    }
                    
                    logger.ErrorFormat("选中资源:{0}不是GameObject 可能guid丢失 svn信息:{1} ",assetPath, sb.ToString());
                }
            }
        }
        
        [MenuItem("Assets/平台资产/外部功能/对选中prefab输出依赖", false, 100)]
        public static void PrintDependency()
        {
            if (Selection.assetGUIDs.Length == 0)
            {
                logger.ErrorFormat("没有选中任何资源");
                return;
            }
            
            HashSet<string> allAssetPath = new HashSet<string>();
            var selectObjects = Selection.assetGUIDs;
            foreach (var assetGuid in selectObjects)
            {
                var assetPath = AssetDatabase.GUIDToAssetPath(assetGuid);

                if (AssetDatabase.IsValidFolder(assetPath))
                {
                    var allguids = AssetDatabase.FindAssets("t:GameObject", new[] { assetPath });
                    foreach (var guid in allguids)
                    {
                        var path = AssetDatabase.GUIDToAssetPath(guid);
                        allAssetPath.Add(path);
                    }
                }
                else
                {
                    allAssetPath.Add(assetPath);
                }
            }

            foreach (var assetPath in allAssetPath)
            {
                var dependency = AssetDatabase.GetDependencies(assetPath);
                foreach (var dep in dependency)
                {
                    if(dep == assetPath || dep.EndsWith(".cs"))
                    {
                        continue;
                    }
                    
                    if (dep.EndsWith(".fbx"))
                    {
                        logger.ErrorFormat("prefab:{0} 依赖fbx:{1}", assetPath, dep);
                    }
                    
                    logger.InfoFormat("prefab:{0} 依赖:{1}", assetPath, dep);
                }
            }
        }
        
        [MenuItem("Assets/平台资产/外部功能/检查变体丢失引用情况", false, 100)]
        public static void CheckVariant()
        {
            if (Selection.assetGUIDs.Length == 0)
            {
                logger.ErrorFormat("没有选中任何资源");
                return;
            }
            
            HashSet<string> allAssetPath = new HashSet<string>();
            var selectObjects = Selection.assetGUIDs;
            foreach (var assetGuid in selectObjects)
            {
                var assetPath = AssetDatabase.GUIDToAssetPath(assetGuid);

                if (AssetDatabase.IsValidFolder(assetPath))
                {
                    var allguids = AssetDatabase.FindAssets("t:GameObject", new[] { assetPath });
                    foreach (var guid in allguids)
                    {
                        var path = AssetDatabase.GUIDToAssetPath(guid);
                        allAssetPath.Add(path);
                    }
                }
                else
                {
                    allAssetPath.Add(assetPath);
                }
            }
            
            HashSet<string> errorAssetPaths = new HashSet<string>();
            HashSet<string> activeMissingAssetPaths = new HashSet<string>();
            
            foreach (var assetPath in allAssetPath)
            {
                if(assetPath.EndsWith("_Lb.prefab", StringComparison.OrdinalIgnoreCase) == false)
                {
                    continue;
                }

                
                var targetGo = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);

                var type = PrefabUtility.GetPrefabAssetType(targetGo);
                if (type != PrefabAssetType.Variant)
                {
                    continue;
                }
                
                var sourceInst = PrefabUtility.InstantiatePrefab(targetGo) as GameObject;
                
                var modifylist = PrefabUtility.GetPropertyModifications(targetGo);
                var overrides = PrefabUtility.GetObjectOverrides(targetGo);
                var addGos = PrefabUtility.GetAddedGameObjects(targetGo);
                var rmGos = PrefabUtility.GetRemovedGameObjects(targetGo);
                var addedComponents = PrefabUtility.GetAddedComponents(targetGo);
                var removedComponents = PrefabUtility.GetRemovedComponents(targetGo);
                
               foreach (var property in overrides)
                {
                    if (property.instanceObject is GameObject)
                    {
                        var go = property.instanceObject as GameObject;
                        //尝试在source中找到对应的物体
                        var sourceTrans = TryFindFromSource(go.transform, sourceInst.transform);
                        if (sourceTrans == null)
                        {
                            logger.InfoFormat("没有找到对应的物体,则不进行修改,:{0} ", property.instanceObject);
                            
                            errorAssetPaths.Add(assetPath);
                            continue;
                        }
                    }
                    else if (property.instanceObject is Component)
                    {
                        var sourceComp = property.instanceObject as Component;

                        //尝试在source中找到到对应的物体
                        var sourceTrans = TryFindFromSource(sourceComp.transform, sourceInst.transform);
                        if (sourceTrans == null)
                        {
                            logger.InfoFormat("没有找到对应的物体,则不进行修改,:{0} ", property.instanceObject);
                            errorAssetPaths.Add(assetPath);
                            continue;
                        }
                        else
                        {
                            var targetcomp = sourceTrans.gameObject.GetComponent(sourceComp.GetType());
                            if (targetcomp == null)
                            {
                                logger.InfoFormat("没有找到对应的组件,则不进行修改,:{0} ", sourceComp);
                                errorAssetPaths.Add(assetPath);
                                continue;
                            }
                        }
                    }
                    else
                    {
                        logger.InfoFormat("不支持的类型,则不进行修改,instanceObject:{0} ", property.instanceObject);
                        errorAssetPaths.Add(assetPath);
                        continue;
                    }
                }

                foreach (var addGo in addGos)
                {
                    var trans = addGo.instanceGameObject.transform;
                    //尝试在source中找到对应的物体
                    var sourceTrans = TryFindFromSource(trans.parent, sourceInst.transform);
                    if (sourceTrans == null)
                    {
                        logger.InfoFormat("没有找到对应的物体,则不进行添加,:{0} ", trans);
                        
                        errorAssetPaths.Add(assetPath);
                    }
                }

                foreach (var rmGo in rmGos)
                {
                    var trans = rmGo.assetGameObject.transform;
                    var sourceTrans = TryFindFromSource(trans, sourceInst.transform);
                    if (sourceTrans == null)
                    {
                        logger.InfoFormat("没有找到对应的物体,则不进行删除,:{0} ", trans);
                        errorAssetPaths.Add(assetPath);
                    }
                }

                foreach (var rmComp in removedComponents)
                {
                    var trans = rmComp.containingInstanceGameObject.transform;
                    //尝试在source中找到对应的物体
                    var sourceTrans = TryFindFromSource(trans, sourceInst.transform);
                    if (sourceTrans == null)
                    {
                        logger.InfoFormat("没有找到对应的物体,则不进行删除Component,:{0} ", rmComp.containingInstanceGameObject);
                        errorAssetPaths.Add(assetPath);
                    }
                }

                foreach (var addComp in addedComponents)
                {
                    var trans = addComp.instanceComponent.transform;
                    //尝试在source中找到对应的物体
                    var sourceTrans = TryFindFromSource(trans, sourceInst.transform);
                    if (sourceTrans == null)
                    {
                        logger.InfoFormat("没有找到对应的物体,则不进行删除Component,:{0} ", addComp.instanceComponent);
                        errorAssetPaths.Add(assetPath);
                    }
                }

                //更新modifylist
                foreach (var modification in modifylist)
                {
                    //修正target到source上
                    if (modification.target == null)
                    {
                        logger.InfoFormat("modifylist中target为null,则不进行modify更新,propertyPath:{0} value:{1}", modification.propertyPath,modification.value);
                        errorAssetPaths.Add(assetPath);
                        
                        if (modification.propertyPath.Contains("m_IsActive"))
                        {
                            activeMissingAssetPaths.Add(assetPath);
                        }
                        continue;
                    }

                    if (modification.target is GameObject go)
                    {
                        var sourceTrans = TryFindFromSource(go.transform, sourceInst.transform);
                        if (sourceTrans == null)
                        {
                            logger.InfoFormat("没有找到对应的物体,则不进行modify更新,:{0} ", modification.target);
                            errorAssetPaths.Add(assetPath);
                            
                            if (modification.propertyPath.Contains("m_IsActive"))
                            {
                                activeMissingAssetPaths.Add(assetPath);
                            }
                            continue;
                        }
                    }
                    else if (modification.target is Component comp)
                    {
                        var sourceTrans = TryFindFromSource(comp.transform, sourceInst.transform);
                        if (sourceTrans == null)
                        {
                            logger.InfoFormat("没有找到对应的物体,则不进行modify更新,:{0} ", modification.target);
                            errorAssetPaths.Add(assetPath);
                            if (modification.propertyPath.Contains("m_IsActive"))
                            {
                                activeMissingAssetPaths.Add(assetPath);
                            }
                            continue;
                        }

                        var sourceComp = sourceTrans.GetComponent(comp.GetType());
                        if (sourceComp == null)
                        {
                            logger.InfoFormat("没有找到对应的组件,则不进行modify更新:{0} ", comp);
                            errorAssetPaths.Add(assetPath);
                            if (modification.propertyPath.Contains("m_IsActive"))
                            {
                                activeMissingAssetPaths.Add(assetPath);
                            }
                            continue;
                        }
                    }
                    else
                    {
                        logger.InfoFormat("不支持的类型,则不进行修改,instanceObject:{0} propertyPath :{1}",
                            modification.target,
                            modification.propertyPath);
                        
                        errorAssetPaths.Add(assetPath);
                    }
                }
                
                GameObject.DestroyImmediate(sourceInst);
            }
            
            if (errorAssetPaths.Count > 0)
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine("以下变体丢失引用:");
                foreach (var path in errorAssetPaths)
                {
                    sb.AppendLine(path);
                }
                logger.ErrorFormat(sb.ToString());
                
                if (activeMissingAssetPaths.Count > 0)
                {
                    StringBuilder sb2 = new StringBuilder();
                    sb2.AppendLine("以下变体丢失激活引用:");
                    foreach (var path in activeMissingAssetPaths)
                    {
                        sb2.AppendLine(path);
                    }
                    logger.ErrorFormat(sb2.ToString());
                }
            }
            else
            {
                logger.InfoFormat("没有发现变体丢失引用");
            }
        }
        
        private static Transform TryFindFromSource(Transform target, Transform source)
        {
            if (target == target.root)
            {
                return source;
            }

            var scenePath = PlatformPackUtils.GetTransformPathSkipRoot(target);

            //尝试在source中找到到对应的物体
            var sourceTrans = source.Find(scenePath);
            return sourceTrans;
        }
        

        [MenuItem("Assets/平台资产/对选中资产执行本地优化(当前平台)", false, 100)]
        [MenuItem("美术工具/平台资产处理/对选中资产执行本地优化(当前平台)")]
        public static void PackPlatformResForTarget()
        {
            if (Selection.assetGUIDs.Length == 0)
            {
                logger.ErrorFormat("没有选中任何资源");
                return;
            }
            PackPlatformRes( PackToolSettings.ToPlatformPackPlatform( EditorUserBuildSettings.activeBuildTarget));
        }
        
        [MenuItem("Assets/平台资产/对选中资产执行本地优化(pc)", false, 100)]
        [MenuItem("美术工具/平台资产处理/对选中资产执行本地优化(pc)")]
        public static void PackPlatformResForPc()
        {
            if (Selection.assetGUIDs.Length == 0)
            {
                logger.ErrorFormat("没有选中任何资源");
                return;
            }
            PackPlatformRes(PackToolSettings.ToPlatformPackPlatform( BuildTarget.StandaloneWindows64));
        }
        [MenuItem("Assets/平台资产/对选中资产执行本地优化(mobile)", false, 100)]
        [MenuItem("美术工具/平台资产处理/对选中资产执行本地优化(mobile)")]
        public static void PackPlatformResForMobile()
        {
            if (Selection.assetGUIDs.Length == 0)
            {
                logger.ErrorFormat("没有选中任何资源");
                return;
            }
            PackPlatformRes(PackToolSettings.ToPlatformPackPlatform( BuildTarget.Android));
        }
        
        [MenuItem("Assets/平台资产/外部功能/收集git信息", false, 100)]
        public static void CollectGitInfo()
        {
            PlatformPackGitCache.Current = null;
            PlatformPackVCTools.BuildGitInfo();
            
            //校验
            if (PlatformPackGitCache.Current != null)
            {
                foreach (var (k,dirInfo) in  PlatformPackGitCache.Current.gitCacheDirInfo)
                {
                    if (isInValidPath(dirInfo.gitPath))
                    {
                        logger.ErrorFormat("git路径:{0}不合法", dirInfo.gitPath);
                    }
                    
                    if (isInValidPath(dirInfo.unityPath))
                    {
                        logger.ErrorFormat("git路径:{0}不合法", dirInfo.unityPath);
                    }

                    foreach (var file in dirInfo.gitFiles)
                    {
                        if (isInValidPath(file.filePath))
                        {
                            logger.ErrorFormat("git路径:{0}不合法", file.filePath);
                        }
                        
                        if (isInValidPath(file.unityPath))
                        {
                            logger.ErrorFormat("git路径:{0}不合法", file.unityPath);
                        }
                    }
                }
                
                foreach (var (k, fileInfo) in PlatformPackGitCache.Current.gitCacheFileInfo)
                {
                    if (isInValidPath(fileInfo.filePath))
                    {
                        logger.ErrorFormat("git路径:{0}不合法", fileInfo.filePath);
                    }
                    
                    if (isInValidPath(fileInfo.unityPath))
                    {
                        logger.ErrorFormat("git路径:{0}不合法", fileInfo.unityPath);
                    }
                }
            }
        }
        
        [MenuItem("Assets/平台资产/外部功能/收集svn信息", false, 100)]
        public static void CollectSvnInfo()
        {
            PlatformPackSvnCache.Current = null;
            PlatformPackVCTools.BuildSvnInfo();
            
            //校验
            if (PlatformPackSvnCache.Current != null)
            {
                foreach (var (k,dirInfo) in  PlatformPackSvnCache.Current.dirs)
                {
                    if (isInValidPath(dirInfo.path))
                    {
                        logger.ErrorFormat("svn路径:{0}不合法", dirInfo.path);
                    }
                    
                    if (isInValidPath(dirInfo.unityPath))
                    {
                        logger.ErrorFormat("svn路径:{0}不合法", dirInfo.unityPath);
                    }

                    foreach (var file in dirInfo.files)
                    {
                        if (isInValidPath(file.path))
                        {
                            logger.ErrorFormat("svn路径:{0}不合法", file.path);
                        }
                        
                        if (isInValidPath(file.unityPath))
                        {
                            logger.ErrorFormat("svn路径:{0}不合法", file.unityPath);
                        }
                    }
                }
                
                foreach (var (k, fileInfo) in PlatformPackSvnCache.Current.files)
                {
                    if (isInValidPath(fileInfo.path))
                    {
                        logger.ErrorFormat("svn路径:{0}不合法", fileInfo.path);
                    }
                    
                    if (isInValidPath(fileInfo.unityPath))
                    {
                        logger.ErrorFormat("svn路径:{0}不合法", fileInfo.unityPath);
                    }

                }
            }
        }
        
        public static bool isInValidPath(string path)
        {
            if (path.Contains(":"))
            {
                return true;
            }
            if (path.Contains("\\"))
            {
                return true;
            }
            return false;
        }

        public static void PackPlatformRes(PlatformPackPlatform targetPlatform)
        {
            //看是否开启了资产处理
            PlatformPackToolSettings currentPlatformSetting =PackToolSettings.TryFindSettings(targetPlatform);
            if (null == currentPlatformSetting)
            {
                logger.ErrorFormat("当前平台:{0}未配置资产处理", targetPlatform);
                return ;
            }

            if (currentPlatformSetting.Enable == false)
            {
                logger.ErrorFormat("当前平台:{0}未开启资产处理", targetPlatform);
                return ;
            }
            
            var ismain = StageUtility.GetMainStageHandle() == StageUtility.GetCurrentStageHandle();
            if (!ismain)
            {
                logger.ErrorFormat("请在主场景下执行");
                StageUtility.GoToMainStage();
                return;
            }

            //伪造数据
            PlatformPackContext context = new PlatformPackContext();
            context.targetPlatform =  targetPlatform;
            context.cacheRootPath = PlatformPackEditorPath.GetPreviewPath(targetPlatform); //PlatformPackConst.GetCacheRootPath(targetPlatform);
            context.gitRootPath = Application.dataPath + "/../../";
            context.svnRootPath = Application.dataPath + "/../../../SocRes/";
            context.processStage = EPlatformProcessStage.EditorPreview;
            
            if(!Directory.Exists(context.cacheRootPath))
            {
                Directory.CreateDirectory(context.cacheRootPath);
            }
            
            HashSet<string> allAssetPath = new HashSet<string>();
            var selectObjects = Selection.assetGUIDs;
            foreach (var assetGuid in selectObjects)
            {
                var assetPath = AssetDatabase.GUIDToAssetPath(assetGuid);

                if (AssetDatabase.IsValidFolder(assetPath))
                {
                    var allguids = AssetDatabase.FindAssets("t:GameObject", new[] { assetPath });
                    foreach (var guid in allguids)
                    {
                        var path = AssetDatabase.GUIDToAssetPath(guid);
                        allAssetPath.Add(path);
                    }
                }
                else
                {
                    allAssetPath.Add(assetPath);
                }
            }
            var Step = new StepOptimizePlatformRes();

            HashSet<Object> allAsset = new HashSet<Object>();

            foreach (var assetPath in allAssetPath)
            {
                if (!PlatformPackPath.ShouldPlatformPack(assetPath))
                {
                    continue;
                }
                
                //在这里是为了性能
                if ( StepOptimizePlatformRes.IsIgnoreDir(assetPath, currentPlatformSetting))
                {
                    logger.InfoFormat("跳过处理资产：{0}，因为在ignoreDirs列表中 ", assetPath);
                    continue;
                }
                //previw部分
                {
                    //获取文件的时间戳
                    var fileInfo = new FileInfo(assetPath);
                    if (fileInfo.Exists == false)
                    {
                        logger.ErrorFormat("选中资源:{0}不存在", assetPath);
                        continue;
                    }
                
                    var lastWriteTime = fileInfo.LastWriteTime;
                    //追加后缀
                    var newFileName = PlatformPackUtils.AppendTimestampAdvanced(assetPath, lastWriteTime);
                    var relativePath = PlatformPackPath.GetGameRelativePath(newFileName);
                    //拼接到preview路径下
                    newFileName = PlatformPackPath.PathCombine(context.cacheRootPath, relativePath);
                
                    logger.InfoFormat("选中资源路径:{0} preview路径{1}", assetPath, newFileName);
                
                    var directoryName = Path.GetDirectoryName(newFileName);
                    if (!Directory.Exists(directoryName))
                    {
                        Directory.CreateDirectory(directoryName);
                    }
                
                    //将资产复制到新的路径,每次都覆盖
                    bool suc = PlatformPackUtils.CopyAsset(assetPath, newFileName);
                    if (!suc)
                    {
                        logger.ErrorFormat("选中资源:{0}的时间戳文件:{1}复制失败", assetPath, newFileName);
                        continue;
                    }
                
                    var finalObj = AssetDatabase.LoadAssetAtPath<Object>(newFileName);
                
                    if (finalObj == null)
                    {
                        throw new Exception(string.Format("选中资源:{0}的时间戳文件:{1}加载异常", assetPath, newFileName));
                    }
                
                    context.assetList[assetPath] = finalObj;
                    allAsset.Add(finalObj);
                }
            }
            
            Step.Execute(context);

            List<Object> objects = new List<Object>();
            foreach (var go in allAsset)
            {
                AssetDatabase.SaveAssetIfDirty(go);
                objects.Add(go);
            }

            AssetDatabase.Refresh();

            if (objects.Count == 1)
            {
                var previewPath = AssetDatabase.GetAssetPath(objects[0]);
                PrefabStageUtility.OpenPrefab(previewPath);
            }
            Selection.objects = objects.ToArray();
        }
        
 
    }
}