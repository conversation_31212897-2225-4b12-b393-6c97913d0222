﻿using System.Collections.Generic;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Config;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocWorld;

namespace WizardGames.Soc.Common.Entity
{
    [HotfixClass]
    public static partial class ReputationUtil
    {
        public struct AwardBattleRewardInfo
        {
            public List<int> ToBeUnlocked;
            public int ReputationLevel;
            public List<int> DropIds;
            public List<DropParam> Rewards;
        }

        public static int CalcReputationCommitExp(int reputationLevel, int itemCount)
        {
            var exp = itemCount;
            var storyStage = ServerInstanceEntity.Instance.StoryStage;
            var curStoryStageConfig = McCommon.Tables.TbStoryStage.GetOrDefault(storyStage);
            if (curStoryStageConfig == null)
            {
                return exp;
            }
            var curWorldLevel = curStoryStageConfig.IntelWorldLevel;
            var lastStoryStageConfig = McCommon.Tables.TbStoryStage.GetOrDefault(storyStage - 1);
            var lastWorldLevel = 0;
            if (lastStoryStageConfig != null)
            {
                lastWorldLevel = lastStoryStageConfig.IntelWorldLevel;
            }

            if (reputationLevel >= curWorldLevel)
                exp = (int)((float)itemCount * (float)McCommon.Tables.TbGlobalConfig.ReputationExpCoverDebuff / 100f);
            else if (reputationLevel < lastWorldLevel)
                exp = (int)((float)itemCount * (float)McCommon.Tables.TbGlobalConfig.ReputationExpCoverBuff / 100f);
            return exp;
        }

        public static int CalcReputationConversionEfficiency(int reputationLevel, bool storageFull)
        {
            if (storageFull)
            {
                return 50;
            }

            var gameModeConfig = McCommon.Tables.TBGameMode.GetOrDefault(ServerConfig.Instance.GameMode);
            if (gameModeConfig == null)
            {
                return 100;
            }

            return gameModeConfig.IncEfficiencyPerLevel * reputationLevel + 100;
        }

        [Hotfix(PropertyChangeCallback = true)]
        private static void OnStoryStageChange(ServerInstanceEntity self, int oldStoryStage, int storyStage)
        {
            if (storyStage == oldStoryStage)
                return;

            self.Logger.Info($"[OnStoryStageChange] storyStage:{oldStoryStage} -> {storyStage}");

            var allReputationCabinetIds = TerritoryManagerEntity.Instance.reputationCabinetIds;
            foreach (var reputationCabinetId in allReputationCabinetIds)
            {
                var partEntity = EntityManager.Instance.GetEntity(reputationCabinetId) as PartEntity;
                if (partEntity != null)
                {
                    var territoryCabinetComp = partEntity.GetComponent<TerritoryCabinetComponent>(EComponentIdEnum.TerritoryCabinet);
                    territoryCabinetComp.OnEfficiencyChange();
                }
            }
        }
    }
}
