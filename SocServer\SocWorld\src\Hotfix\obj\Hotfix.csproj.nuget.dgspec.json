{"format": 1, "restore": {"E:\\Development\\soc\\SocServer\\SocWorld\\src\\Hotfix\\Hotfix.csproj": {}}, "projects": {"E:\\Development\\soc\\SocCommon\\MessagePack\\MessagePack.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Development\\soc\\SocCommon\\MessagePack\\MessagePack.csproj", "projectName": "MessagePack", "projectPath": "E:\\Development\\soc\\SocCommon\\MessagePack\\MessagePack.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Development\\soc\\SocCommon\\MessagePack\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Development\\soc\\SocCommon\\Soc.Common\\Soc.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Development\\soc\\SocCommon\\Soc.Common\\Soc.Common.csproj", "projectName": "Soc.Common", "projectPath": "E:\\Development\\soc\\SocCommon\\Soc.Common\\Soc.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Development\\soc\\SocCommon\\Soc.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Development\\soc\\SocCommon\\MessagePack\\MessagePack.csproj": {"projectPath": "E:\\Development\\soc\\SocCommon\\MessagePack\\MessagePack.csproj"}, "E:\\Development\\soc\\SocCommon\\SocLog\\SocLog.csproj": {"projectPath": "E:\\Development\\soc\\SocCommon\\SocLog\\SocLog.csproj"}, "E:\\Development\\soc\\SocConst\\SocConst.csproj": {"projectPath": "E:\\Development\\soc\\SocConst\\SocConst.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"log4net": {"target": "Package", "version": "[2.0.15, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Development\\soc\\SocCommon\\SocLog\\SocLog.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Development\\soc\\SocCommon\\SocLog\\SocLog.csproj", "projectName": "SocLog", "projectPath": "E:\\Development\\soc\\SocCommon\\SocLog\\SocLog.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Development\\soc\\SocCommon\\SocLog\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"log4net": {"target": "Package", "version": "[2.0.15, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Development\\soc\\SocConst\\SocConst.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Development\\soc\\SocConst\\SocConst.csproj", "projectName": "SocConst", "projectPath": "E:\\Development\\soc\\SocConst\\SocConst.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Development\\soc\\SocConst\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0", "net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}, "net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}, "net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Development\\soc\\SocServer\\SocWorld\\src\\Hotfix\\Hotfix.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Development\\soc\\SocServer\\SocWorld\\src\\Hotfix\\Hotfix.csproj", "projectName": "Hotfix", "projectPath": "E:\\Development\\soc\\SocServer\\SocWorld\\src\\Hotfix\\Hotfix.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Development\\soc\\SocServer\\SocWorld\\src\\Hotfix\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Development\\soc\\SocServer\\SocWorld\\src\\WorldEssentials\\WorldEssentials.csproj": {"projectPath": "E:\\Development\\soc\\SocServer\\SocWorld\\src\\WorldEssentials\\WorldEssentials.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "false", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Development\\soc\\SocServer\\SocWorld\\src\\WorldEssentials\\WorldEssentials.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Development\\soc\\SocServer\\SocWorld\\src\\WorldEssentials\\WorldEssentials.csproj", "projectName": "World", "projectPath": "E:\\Development\\soc\\SocServer\\SocWorld\\src\\WorldEssentials\\WorldEssentials.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Development\\soc\\SocServer\\SocWorld\\src\\WorldEssentials\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Development\\soc\\SocCommon\\MessagePack\\MessagePack.csproj": {"projectPath": "E:\\Development\\soc\\SocCommon\\MessagePack\\MessagePack.csproj"}, "E:\\Development\\soc\\SocCommon\\Soc.Common\\Soc.Common.csproj": {"projectPath": "E:\\Development\\soc\\SocCommon\\Soc.Common\\Soc.Common.csproj"}, "E:\\Development\\soc\\SocConst\\SocConst.csproj": {"projectPath": "E:\\Development\\soc\\SocConst\\SocConst.csproj"}}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "false", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AgonesSDK": {"target": "Package", "version": "[1.49.0, )"}, "CommandLineParser": {"target": "Package", "version": "[2.9.1, )"}, "Google.Api.CommonProtos": {"target": "Package", "version": "[2.16.0, )"}, "Google.Protobuf": {"target": "Package", "version": "[3.30.2, )"}, "Grpc.Core": {"target": "Package", "version": "[2.46.6, )"}, "Grpc.Net.Client": {"target": "Package", "version": "[2.71.0, )"}, "Grpc.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.72.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.4, )"}, "MongoDB.Driver": {"target": "Package", "version": "[3.4.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Data.HashFunction.MurmurHash": {"target": "Package", "version": "[2.0.0, )"}, "TencentCloudSDK": {"target": "Package", "version": "[3.0.1294, )"}, "log4net": {"target": "Package", "version": "[2.0.15, )"}, "prometheus-net": {"target": "Package", "version": "[8.2.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}