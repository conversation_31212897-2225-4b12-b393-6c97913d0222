using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using UnityEngine;

public static class DelayedInstantiateStats
{
    public static int MaxEntityDicCount = 0;
    public static int MaxEmbeddedEntityDicCount = 0;
    public static int MaxInstantiatedEntityCount = 0;
    public static int MaxLastNearByList = 0;
    public static int MaxCurNearbyList = 0;
    public static int ToRemoveList = 0;
    public static int ToCallbackList = 0;
    public static int ToRemoveEntityList = 0;
    public static int Entity2CellIdx = 0;
    public static int DelayedRemoveEntityList = 0;
    public static int SortedCellResult = 0;
    public static int TempSortedCellResult = 0;
    public static int SortedEntityInCellResult = 0;

    [Conditional("UNITY_EDITOR")]
    public static void UpdateCompContainer(DelayedInstantiateComp comp)
    {
        if(comp.LastNearbyList.Count() > MaxLastNearByList)
        {
            MaxLastNearByList = comp.LastNearbyList.Count();
        }
        
        if (comp.CurNearbyList.Count() > MaxCurNearbyList)
        {
            MaxCurNearbyList = comp.CurNearbyList.Count();
        }
        
        if (comp.Entity2CellIdx.Count() > Entity2CellIdx)
        {
            Entity2CellIdx = comp.Entity2CellIdx.Count();
        }
        if (comp.DelayedRemoveEntityList.Count() > DelayedRemoveEntityList)
        {
            DelayedRemoveEntityList = comp.DelayedRemoveEntityList.Count();
        }
        if (comp.SortedCellResult.Count > SortedCellResult)
        {
            SortedCellResult = comp.SortedCellResult.Count;
        }
        if (comp.TempSortedCellResult.Count > TempSortedCellResult)
        {
            TempSortedCellResult = comp.TempSortedCellResult.Count;
        }
        if (comp.SortedEntityInCellResult.Count > SortedEntityInCellResult)
        {
            SortedEntityInCellResult = comp.SortedEntityInCellResult.Count;
        }
    }

    [Conditional("UNITY_EDITOR")]
    public static void UpdateStats(DelayedInstantiateCell cell)
    {
        int entityCount = cell.EntityCount;
        int embeddedCount = cell.EmbeddedEntityCount;
        int instantiatedCount = cell.InstantiatedEntityCount;

        if (entityCount > MaxEntityDicCount)
        {
            MaxEntityDicCount = entityCount;
        }
        if (embeddedCount > MaxEmbeddedEntityDicCount)
        {
            MaxEmbeddedEntityDicCount = embeddedCount;
        }
        if (instantiatedCount > MaxInstantiatedEntityCount)
        {
            MaxInstantiatedEntityCount = instantiatedCount;
        }
       
    }

    [Conditional("UNITY_EDITOR")]
    public static void DbgPrint()
    {
        DelayedInstantiateComp.Logger.InfoFormat("EntityDic: {0}, EmbeddedEntityDic: {1}, InstantiatedEntity: {2}"
            , MaxEntityDicCount
            , MaxEmbeddedEntityDicCount
            , MaxInstantiatedEntityCount);
        
        DelayedInstantiateComp.Logger.InfoFormat("DelayedInstantiateComp: LastNearbyList: {0}, CurNearbyList: {1}, ToRemoveList: {2}, ToCallbackList: {3}, ToRemoveEntityList: {4}, Entity2CellIdx:{5} DelayedRemoveEntityList: {6}, SortedCellResult: {7}, TempSortedCellResult: {8}, SortedEntityInCellResult: {9}"
            , MaxLastNearByList
            , MaxCurNearbyList
            , ToRemoveList
            , ToCallbackList
            , ToRemoveEntityList
            , Entity2CellIdx
            , DelayedRemoveEntityList
            , SortedCellResult
            , TempSortedCellResult
            , SortedEntityInCellResult);
    }

    [Conditional("UNITY_EDITOR")]
    public static void Reset()
    {
        MaxEntityDicCount = 0;
        MaxEmbeddedEntityDicCount = 0;
        MaxInstantiatedEntityCount = 0;
        MaxLastNearByList = 0;
        MaxCurNearbyList = 0;
        ToRemoveList = 0;
        ToCallbackList = 0;
        ToRemoveEntityList = 0;
        Entity2CellIdx = 0;
        DelayedRemoveEntityList = 0;
        SortedCellResult = 0;
        TempSortedCellResult = 0;
        SortedEntityInCellResult = 0;
    }
}
