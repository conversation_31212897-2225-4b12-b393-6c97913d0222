using Cysharp.Text;
using FairyGUI;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Combat;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Data.mall;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.SocClient.Audio;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Ui.Binder.GameConsole;
using WizardGames.Soc.SocClient.Ui.Utils;

namespace WizardGames.Soc.SocClient.Ui
{
    /// <summary>
    /// 中控台相关功能
    /// </summary>
    public partial class UiCenterConsole : WindowComBase
    {
        #region Compents
        private RootCenterConsoleBinder rootBinder; 
        private ListItemBanner_Com2Binder informationCom;
        private ListItemBannerBinder storyBanner;
        private ListItemBannerBinder shopItemBanner;
        #endregion

        private List<int> groupListData = new();

        // 播放进场动效
        private long playAniTimerId;

        // 倒计时
        private long updateTimerId = 0;

        // 商城滚动
        private long autoRollTimerId = 0;
        // 热销商品列表
        private List<OBJHotSale> allSales;
        // 当前商城滚动到第几个
        private int curScrollIndex = 0;
        // 商城滚动间隔
        private int autoScrollCDMs = Mc.Tables.TbGlobalConfig.MallHotSaleAutoScrollCDMs;

        /// <summary>
        /// 缓存控件，用于解锁和引导
        /// </summary>
        private Dictionary<int, GObject> allFunctionBtns = new();

        /// <summary>
        /// 控件解锁的key的前缀
        /// </summary>
        private const string UNLOCK_KEY_PREFIX = "center_console_{0}";

        protected override void OnInit()
        {
            base.OnInit();
            Mc.Ui.AddPackage("LobbyMall");
            InitUIComponent();
            InitGetDyanmicIconFunc();
            InitFuncClickAction();
            InitCheckRedDotAction();
            RegisterEvents();

            var showAnim = rootBinder.TransExitSelectionCom_show_anim;
            if (showAnim != null)
            {
                showAnim.invalidateBatchingEveryFrame = true;
            }
            var hideAnim = rootBinder.TransExitSelectionCom_hide_anim;
            if (hideAnim != null)
            {
                hideAnim.invalidateBatchingEveryFrame = true;
            }
        }

        public override void OnLayerVisibleChanged(bool layerVisible)
        {
            if (layerVisible)
            {
                OnEnable();
                //打开其他界面触发层级隐藏，之后再显示的时候不会走openWindow逻辑，需要主动触发一下UI解锁逻辑
                HandleUnlockConditionChange();
            }
            else
            {
                OnDisable();
            }
        }

        protected override void OnEnable()
        {
            isHalfScreenWin = true;
            base.OnEnable();
            InitShopSales();
            RefreshTopList();
            RefreshBottomList();
            RefreshConsoleGroupList();
            PlayEffect();
            AddUpdateTimer();
            AddShopScorllTimer();
            TryTriggerGuide();
            GenerateAllUnlockKey();

            // 预先设置Hud遮挡隐藏
            Mc.Hud.SetHideArea(GetHalfScreenHideArea());
        }

        public override Rect GetHalfScreenHideArea()
        {
            return new Rect(rootBinder.HideArea.x, 0, rootBinder.HideArea.width, rootBinder.HideArea.height);
        }

        public override void OnDestroy()
        {
            base.OnDestroy();
            Mc.Ui.ReleasePackage("LobbyMall");
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            ClearPlayAniTimer();
            ClearUpdateTimer();
            ClearShopBannerScorllTimer();
            informationCom = null;
            shopItemBanner = null;
            curScrollIndex = 0;
            allSales.Clear();
            allFunctionBtns.Clear();
            if (rootBinder != null)
            {
                rootBinder.CtrlShowExitSelectionCom.selectedIndex = 0;
            }
            
            TryTriggerGuide();
        }

        private void InitUIComponent()
        {
            rootBinder = new RootCenterConsoleBinder(ComRoot);
            rootBinder.CloseBtn.onClick.Set(OnCloseBtnClick);
            rootBinder.BgClose.onClick.Set(OnBgCloseClick);
            
            rootBinder.TopList.itemRenderer = OnTopListItemRender;
            rootBinder.BottomList.itemRenderer = OnBottomListItemRender;
            rootBinder.ConsoleGroupList.itemRenderer = OnConsoleGroupListItemRender;
            rootBinder.OptCom.BinderRoot.visible = false;

#if POCO
            rootBinder.CloseBtn.PocoRegister("ConsoleCloseBtn");
#endif
        }

        private void RegisterEvents()
        {
            RegisterEvent(EventDefine.UpdateSingleMission, TryTriggerGuide);
            RegisterEvent(EventDefine.UpdateTrackMissionId, TryTriggerGuide);
            RegisterEvent(EventDefine.RefreshPandoraEntryState, CheckPandoraEntry);
            RegisterEvent<int>(EventDefine.PlayerStateChange, PlayerStateAction);
            RegisterEvent<DamageDataEvent>(EventDefine.Damage, OnHandleDamage);
            RegisterEvent(EventDefine.TribeTipChanged, RefreshConsoleGroupList);
        }

        private void RefreshTopList()
        {
            var topList = Mc.CenterConsole.GetGroupDataByGroupType(GameConsoleGroupType.BasicTop);
            if (topList == null)
            {
                rootBinder.TopList.numItems = 0;
                return;
            }
            rootBinder.TopList.numItems = topList.Count;
        }

        private void RefreshBottomList()
        {
            var bottomList = Mc.CenterConsole.GetGroupDataByGroupType(GameConsoleGroupType.BasicBottom);
            if (bottomList == null)
            {
                rootBinder.BottomList.numItems = 0;
                return;
            }
            rootBinder.BottomList.numItems = bottomList.Count;
        }

        private void RefreshConsoleGroupList()
        {
            //新手关屏蔽左侧显示
            if (PlayHelper.IsNewbie)
            {
                rootBinder.ConsoleGroupList.visible = false;
                return;
            }
            var sortedGroupList = Mc.CenterConsole.SortedGroup;
            groupListData.Clear();
            for (int i = 0; i < sortedGroupList.Count; i++)
            {
                var groupId = sortedGroupList[i];
                var groupType = Mc.Tables.TbGameCenterConsoleGroup.GetOrDefault(groupId)?.GroupType;
                if (groupType != GameConsoleGroupType.BasicTop && groupType != GameConsoleGroupType.BasicBottom)
                {
                    groupListData.Add(groupId);
                }
            }
            rootBinder.ConsoleGroupList.numItems = groupListData.Count;
            rootBinder.ConsoleGroupList.ScrollToView(0);
            bool enbaleScroll = rootBinder.ConsoleGroupList.scrollPane.contentHeight > rootBinder.ConsoleGroupList.viewHeight;
            FairyGuiUtil.SetListScrollEnable(rootBinder.ConsoleGroupList, enbaleScroll);
        }

        /// <summary>
        /// 播放特效
        /// </summary>
        private void PlayEffect()
        {
            ClearPlayAniTimer();
            playAniTimerId = FairyGuiUtil.PlayListAnimation(rootBinder.ConsoleGroupList, 30);
        }

        private void ClearPlayAniTimer()
        {
            if (playAniTimerId > 0)
            {
                Mc.TimerWheel.CancelTimer(playAniTimerId);
                playAniTimerId = 0;
            }
        }

        private void AddUpdateTimer()
        {
            ClearUpdateTimer();
            if (updateTimerId > 0) return;
            updateTimerId = Mc.TimerWheel.AddTimerRepeat(0, 1000, UpdateTimer, "UiCenterConsoleTimer");
        }

        private void ClearUpdateTimer()
        {
            if (updateTimerId > 0)
            {
                Mc.TimerWheel.CancelTimer(updateTimerId);
                updateTimerId = 0;
            }
        }

        private void InitShopSales()
        {
            allSales = Mc.Mall?.GetAllActiveHotSales();
        }

        /// <summary>
        /// 商城滚动定时器
        /// </summary>
        private void AddShopScorllTimer()
        {
            if (PlayHelper.IsNewbie) return; // 新手关不需要滚动
            ClearShopBannerScorllTimer();
            if (autoRollTimerId > 0) return;
            autoRollTimerId = Mc.TimerWheel.AddTimerRepeat(autoScrollCDMs, autoScrollCDMs, OnShopAutoScrollTimer);
            RefreshShopCom();
        }

        private void ClearShopBannerScorllTimer()
        {
            if (autoRollTimerId > 0)
            {
                Mc.TimerWheel.CancelTimer(autoRollTimerId);
                autoRollTimerId = 0;
            }
        }

        private void OnShopAutoScrollTimer(long timerId, object data = null, bool delete = false)
        {
            if (allSales.Count <= 1) return;
            int preIndex = curScrollIndex;
            ++curScrollIndex;
            if (curScrollIndex >= allSales.Count)
            {
                curScrollIndex = 0;
            }

            if (shopItemBanner.Icon != null)
            {
                shopItemBanner.Icon.Icon.url = allSales[preIndex].Thumbnail;
                shopItemBanner.Icon.Icon.url = allSales[curScrollIndex].Thumbnail;
            }
            shopItemBanner.TransScrollRTL.Play();
        }

        private void UpdateTimer(long timerId, object data, bool delete)
        {
            RefreshStoryCom();
        }

        /// <summary>
        /// 缓存所有的功能按钮
        /// </summary>
        /// <param name="id"></param>
        /// <param name="btn"></param>
        private void TryAddToAllFunctionBtns(int id, GObject btn)
        {
            if (btn == null) return;
            if (allFunctionBtns.ContainsKey(id))
            {
                allFunctionBtns[id] = btn;
                return;
            }
            allFunctionBtns.Add(id, btn);
        }

        private void HandleCanShowInCurrentMode(GObject btn, Common.Data.Ui.GameCenterConsole config)
        {
            if (btn == null) return;
            var show = config.CurModeCanShow;
            if (show) return;

            // 不需要解锁逻辑了
            RemoveNeedCheckUnlockCtrl(btn);

            var style = config.NotShowStyle;
            if(style == 1)
            {
                btn.visible = false;
                return;
            }
            else
            {
                var ctrlLock = btn.asButton.GetController("lock");
                if (ctrlLock != null)
                {
                    ctrlLock.selectedIndex = 1;
                }

                btn.onClick.AddLockCallback((context) => {
                    MgrAudio.PlayAudioEventAsync("UI_Click_Fail", null, null);
                    Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.CanNotUseInThisMode);
                    context.StopPropagation();
                });
            }
        }

        /// <summary>
        /// 根据FunctionID获取对应的按钮
        /// </summary>
        /// <param name="funcId"></param>
        /// <returns></returns>
        private GObject GetObjByFunctionId(GameConsoleFunction funcId)
        {
            foreach (var (id, obj) in allFunctionBtns)
            {
                if (obj == null) continue;
                var config = Mc.Tables.TbGameCenterConsole.GetOrDefault(id);
                if (config == null) continue;
                if (config.FunctionID == funcId)
                {
                    return obj;
                }
            }
            return null;
        }

        /// <summary>
        /// 给所有功能按钮加UnlockKey
        /// </summary>
        private void GenerateAllUnlockKey()
        {
            foreach (var (id, obj) in allFunctionBtns)
            {
                var config = Mc.Tables.TbGameCenterConsole.GetOrDefault(id);
                if (config != null && !config.CurModeCanShow) continue;
                if (obj == null) continue;
                var unlockKey = ZString.Format(UNLOCK_KEY_PREFIX, id);
                obj.UnlockKey = unlockKey;
                AddNeedCheckUnlockCtrl(obj);
            }
        }

        /// <summary>
        /// 刷新剧本的Banner信息
        /// </summary>
        private void RefreshStoryCom()
        {
            if (storyBanner == null) return;
            var storyCom = storyBanner.StoryCom;
            var tempStageList = Mc.Story.GetStageList();
            if (tempStageList == null || tempStageList.Count == 0)
            {
                SafeUtil.SafeSetText(storyCom.Title, string.Empty);
                SafeUtil.SafeSetText(storyCom.Num, string.Empty);
                SafeUtil.SafeSetText(storyCom.Time, string.Empty);
                storyBanner.BinderRoot.grayed = true;
                return;
            }

            var curStage = Mc.Story.GetCurrStage();
            if (curStage == null)
            {
                SafeUtil.SafeSetText(storyCom.Title, string.Empty);
                SafeUtil.SafeSetText(storyCom.Num, string.Empty);
                SafeUtil.SafeSetText(storyCom.Time, string.Empty);
                storyBanner.BinderRoot.grayed = true;
                return;
            }

            storyBanner.BinderRoot.grayed = false;

            var num = ZString.Format("0{0}", curStage.Id);

            var config = Mc.Tables.TbStoryStage.GetOrDefault(curStage.Id);
            if (config == null)
            {
                SafeUtil.SafeSetText(storyCom.Title, string.Empty);
                SafeUtil.SafeSetText(storyCom.Num, string.Empty);
                return;
            }
            
            // 阶段名称
            SafeUtil.SafeSetText(storyCom.Title, config.StageName);
            SafeUtil.SafeSetText(storyCom.Num, num);
            // 时间
            var tempEndTime = Mc.Story.GetStageEndTime();
            var time = tempEndTime - TimeStampUtil.GetNowTimeStampMSec();
            if (time < 0)
            {
                SafeUtil.SafeSetText(storyCom.Time, string.Empty);
            }
            else
            {
                time = Unity.Mathematics.math.max(0, time);
                SafeUtil.SafeSetText(storyCom.Time, FairyGuiUtil.FormatTime(time));
            }
        }

        /// <summary>
        /// 刷新情报的Banner信息
        /// </summary>
        private void RefreshInformationCom()
        {
            if (informationCom == null) return;
            var level = Mc.Reputation.Level;
            var levelStr = ZString.Format(LanguageManager.GetTextConst(LanguageConst.Level), level);
            SafeUtil.SafeSetText(informationCom.Level, levelStr);
        }

        /// <summary>
        /// 刷新商城的Banner信息
        /// </summary>
        private void RefreshShopCom()
        {
            if (shopItemBanner == null) return;
            var shopCom = shopItemBanner.ShopCom;
            if (shopCom == null) return;
            if (allSales == null || allSales.Count == 0) return;
            var sale = allSales[curScrollIndex];
            SafeUtil.SafeSetText(shopCom.Title, sale.ShopName);
            var iconUrl = GetShopIconUrl();
            if (!iconUrl.Equals(shopItemBanner.Icon.Icon.url))
            {
                shopItemBanner.Icon.Icon.url = iconUrl;
            }
        }

        private void OnRefreshText()
        {
            if (null == allSales || curScrollIndex < 0 || curScrollIndex >= allSales.Count)
            {
                shopItemBanner.ShopCom.Title.visible = false;
                return;
            }
            var curSale = allSales[curScrollIndex];
            shopItemBanner.ShopCom.Title.text = curSale.ShopName;
            shopItemBanner.ShopCom.Title.visible = true;
        }

        private void CheckPandoraEntry()
        {
            RefreshTopList();
            RefreshBottomList();
            RefreshConsoleGroupList();
        }

        private void PlayerStateAction(int stateId)
        {
            // 受击
            if (stateId == KillType.None)
            {
                HideSelf();
            }
        }

        private void OnHandleDamage(DamageDataEvent data)
        {
            if (data.DamageRelation == (int)DamageRelation.Teammate || data.SourcePlayerId == Mc.MyPlayer.MyEntityLocal.EntityId) return;
            if (data.SourceType != EntityTypeId.MonsterEntity && data.SourceType != EntityTypeId.PlayerEntity &&
                data.SourceType != EntityTypeId.PartEntity && data.SourceType != EntityTypeId.TrapEntity) return;
            HideSelf();
        }

        #region 列表渲染
        /// <summary>
        /// 右侧上方列表
        /// </summary>
        /// <param name="index"></param>
        /// <param name="obj"></param>
        private void OnTopListItemRender(int index, GObject obj)
        {
            var topList = Mc.CenterConsole.GetGroupDataByGroupType(GameConsoleGroupType.BasicTop);
            if (topList == null || index >= topList.Count) return;
            RenderRightListItem(obj.asCom, topList[index]);
        }

        /// <summary>
        /// 右侧下方列表
        /// </summary>
        /// <param name="index"></param>
        /// <param name="obj"></param>
        private void OnBottomListItemRender(int index, GObject obj)
        {
            var bottomList = Mc.CenterConsole.GetGroupDataByGroupType(GameConsoleGroupType.BasicBottom);
            if (bottomList == null || index >= bottomList.Count) return;
            RenderRightListItem(obj.asCom, bottomList[index]);
        }

        /// <summary>
        /// 渲染上下两个列表的item
        /// </summary>
        /// <param name="com"></param>
        /// <param name="id"></param>
        private void RenderRightListItem(GComponent com, int id)
        {
            var btnConsoleCom = new BtnConsoleBinder(com);

            var config = Mc.Tables.TbGameCenterConsole.GetOrDefault(id);
            if (config == null) return;

            // 图标
            var iconUrl = GetIconById(id);
            btnConsoleCom.Icon.url = iconUrl;

            // 标题
            SafeUtil.SafeSetText(btnConsoleCom.Title, config.IconName);

            //特殊处理一下，新手关的返回大厅文字改成返回登录
            if (PlayHelper.IsNewbie && config.FunctionID == GameConsoleFunction.ReturnLobby)
            {
                btnConsoleCom.Title.SafeSetText(LanguageManager.GetTextConst(LanguageConst.ReturnLogin));
            }
            
            // 是否显示时间
            //bool showTime = config.FunctionID == GameConsoleFunction.CampingTent;
            bool showTime = false;
            btnConsoleCom.CtrlShowTime.selectedIndex = showTime ? 1 : 0;
            if (showTime)
            {
                // 目前没有倒计时的
            }

            // 绑定红点
            BindRedDot(btnConsoleCom.RedDot, config.FunctionID);

            // 按钮点击事件
            btnConsoleCom.BinderRoot.onClick.Set(() =>
            {
                OnFunctionBtnClick(id);
            });

#if POCO
            if (config.FunctionID == GameConsoleFunction.Setting)
            {
                btnConsoleCom.BinderRoot.asButton.PocoRegister("OpenSettingButton");
            }

            if (config.FunctionID == GameConsoleFunction.ReturnLobby)
            {
                btnConsoleCom.BinderRoot.asButton.PocoRegister("ConsoleReturnLobby");
            }

            if(config.FunctionID == GameConsoleFunction.Escape)
            {
                btnConsoleCom.BinderRoot.asButton.PocoRegister("ConsoleEscape");
            }
#endif

            TryAddToAllFunctionBtns(id, btnConsoleCom.BinderRoot);
            HandleCanShowInCurrentMode(btnConsoleCom.BinderRoot, config);
        }

        private void OnConsoleGroupListItemRender(int index, GObject obj)
        {
            if (index >= groupListData.Count) return;
            var groupId = groupListData[index];
            var groupData = Mc.Tables.TbGameCenterConsoleGroup.GetOrDefault(groupId);
            if (groupData == null) return;
            var groupType = groupData.GroupType;
            var groupCom = new ListItemConsoleGroupBinder(obj.asCom);

            // 标题
            SafeUtil.SafeSetText(groupCom.GroupTitle, groupData.GroupName);
            var groupDataList = Mc.CenterConsole.GetGroupDataByGroupType(groupType);
            if (groupDataList == null) return;
            RenderNormalList(groupCom.ConsoleBtnList, groupType);
        }

        private void RenderNormalList(GList list, GameConsoleGroupType groupType)
        {
            list.RemoveChildrenToPool();
            var listData = Mc.CenterConsole.GetGroupDataByGroupType(groupType);
            if (listData == null) return;
            // list.numItems = listData.Count;
            for (int i = 0; i < listData.Count; i++)
            {
                var id = listData[i];
                var config = Mc.Tables.TbGameCenterConsole.GetOrDefault(id);
                if (config == null) continue;
                bool isBanner = config.BtnStyle == 1;
                var url = FGUIResPathDef.LIST_ITEM_NORMAL_BTN;
                if (isBanner)
                {
                    url = FGUIResPathDef.LIST_ITEM_BANNER;
                    var item = list.AddItemFromPool(url);
                    if (item == null) continue;
                    var itemBanner = new ListItemBannerBinder(item.asCom);
                    // 标题
                    SafeUtil.SafeSetText(itemBanner.Title, config.IconName);
                    // 图标
                    var iconUrl = GetIconById(id);

                    if (!iconUrl.Equals(itemBanner.Icon.Icon.url))
                    {
                        itemBanner.Icon.Icon.url = iconUrl;
                    }

                    var style = string.Empty;
                    if (config.FunctionID == GameConsoleFunction.Story)
                    {
                        style = "storyCom";
                        storyBanner = itemBanner;
                        RefreshStoryCom();
                    }
                    else if (config.FunctionID == GameConsoleFunction.Information)
                    {
                        style = "intelCom";
                        informationCom = itemBanner.IntelCom;
                        RefreshInformationCom();
                    }
                    else if (config.FunctionID == GameConsoleFunction.Shop)
                    {
                        style = "shopCom";
                        shopItemBanner = itemBanner;
                        RefreshShopCom();
                        shopItemBanner.TransScrollRTL.SetHook("RefreshText", OnRefreshText);
                    }
                    // 哪种样式
                    if(!string.IsNullOrEmpty(style))
                    {
                        itemBanner.CtrlComStyle.SetSelectedPage(style);
                    }

                    BindRedDot(itemBanner.RedDot, config.FunctionID);

                    // 按钮点击事件
                    itemBanner.BinderRoot.onClick.Set(() =>
                    {
                        OnFunctionBtnClick(id);
                    });
                    TryAddToAllFunctionBtns(id, item);

                    // 当前模式下是否显示（优先级比解锁逻辑高）
                    HandleCanShowInCurrentMode(item, config);
                }
                else
                {
                    var item = list.AddItemFromPool(url);
                    if (item == null) continue;
                    RenderNormalBtn(item.asCom, id);
                    TryAddToAllFunctionBtns(id, item);
                    HandleCanShowInCurrentMode(item, config);
                }
            }

            list.ResizeToFit();
        }

        private void RenderNormalBtn(GComponent com, int id)
        {
            var config = Mc.Tables.TbGameCenterConsole.GetOrDefault(id);
            if (config == null) return;
#if POCO
            if (config.FunctionID == GameConsoleFunction.SurvivalManual)
            {
                com.PocoRegister("ConsoleSurvivalBtn");
            }
            else if (config.FunctionID == GameConsoleFunction.ActionList)
            {
                com.PocoRegister("ConsoleActionListBtn");
            }     
#endif

            var itemNormal = new ListItemNormalBtnBinder(com);
            // 标题
            SafeUtil.SafeSetText(itemNormal.Title, config.IconName);
            // 图标
            var iconUrl = GetIconById(id);
            itemNormal.Icon.url = iconUrl;
            // 红点
            BindRedDot(itemNormal.RedDot, config.FunctionID);
            // 是否显示背景特效
            bool showEffect = config.BgEffect;
            if (showEffect)
            {
                itemNormal.TransLoop.Play(-1, 0.1f, null);
            }
            else
            {
                itemNormal.TransLoop.Stop();
            }

            itemNormal.CtrlImgStyle.selectedIndex = id % 2;

            // 是否显示Tip
            var showTip = false;
            if (config.FunctionID == GameConsoleFunction.Mail)
            {
                showTip = Mc.RedDot?.GetRedDotBool(RedDotType.GameMail) ?? false;

                // 监听红点变化
                itemNormal.RedDot.AddEventListener("onVisibleChanged", () => 
                { 
                    bool reddotVisible = itemNormal.RedDot.visible;
                    itemNormal.CtrlShowTip.selectedIndex = reddotVisible ? 1 : 0;
                });

                itemNormal.CtrlShowTip.selectedIndex = showTip ? 1 : 0;
                if (showTip)
                {
                    SafeUtil.SafeSetText(itemNormal.Tip, LanguageManager.GetTextConst(LanguageConst.HasMailToRead));
                }
            }

            if (config.FunctionID == GameConsoleFunction.Tribe)
            {
                showTip = Mc.Tribe.CheckNeedShowGraduatedTip();
                itemNormal.CtrlShowTip.selectedIndex = showTip ? 1 : 0;
                if (showTip)
                {
                    SafeUtil.SafeSetText(itemNormal.Tip, LanguageManager.GetTextConst(LanguageConst.TribeGraduate));
                }
            }

            // 按钮点击事件
            itemNormal.BinderRoot.onClick.Set(() =>
            {
                OnFunctionBtnClick(id);
            });

#if POCO
            if (config.FunctionID == GameConsoleFunction.Technology)
            {
                itemNormal.BinderRoot.asButton.PocoRegister("OpenTechTreeButton");
            }
#endif
        }

        #endregion
    }
}