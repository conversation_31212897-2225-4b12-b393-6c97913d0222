using FairyGUI;
using System.Diagnostics;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.SocClient.Ui;

namespace WizardGames.Soc.SocClient
{
    /// <summary>
    /// Hud通用API聚集地
    /// </summary>
    public class MgrHudCommon : MgrBase
    {
        public static SocLogger Logger { get; private set; } = LogHelper.GetLogger(typeof(MgrHudCommon));

        public override void Init()
        {
            base.Init();
        }
        /// <summary>
        /// 显示通用倒计时
        /// </summary>
        /// <param name="msCountdown"></param>
        /// <param name="tips"></param>
        /// <param name="onFinish">正常结束时触发</param>
        /// <param name="onClickCancel">点击取消按钮时触发</param>
        /// <param name="onAbort">调用终止时触发</param>
        /// <param name="iconUrl">需要显示的ICON</param>
        public void ShowCountDown(int msCountdown, string tips, EventCallback0 onFinish = null, EventCallback0 onClickCancel = null, EventCallback0 onAbort = null, string iconUrl = null, bool autoHideWhenCancel = true)
        {
            var elem = UiHud.GetElem<UiHudElemCountDown>(374);
            if (elem == null)
            {
                Logger.Info("[HUD] UiHudElemCountDown not found, please check the UI element ID.");
                return;
            }
            Logger.InfoFormat("[HUD] UiHudElemCountDown SetCountdown  ms:{0}  tips:{1}  iconUrl:{2}", msCountdown, tips, iconUrl);
            elem.Show();
            elem.SetCountdown(msCountdown, tips, () =>
            {
                Logger.Info("[HUD] UiHudElemCountDown Finished");
                onFinish?.Invoke();
                elem.Hide();
            }, () =>
            {
                Logger.Info("[HUD] UiHudElemCountDown Canceled");
                onClickCancel?.Invoke();
                if (autoHideWhenCancel) elem.Hide();
            }, () =>
            {
                Logger.Info("[HUD] UiHudElemCountDown Aborted");
                onAbort?.Invoke();
                elem.Hide();
            }, iconUrl, autoHideWhenCancel);
        }
        /// <summary>
        /// 终止倒计时
        /// </summary>
        public void AbortCountDown()
        {
            var elem = UiHud.GetElem<UiHudElemCountDown>(374);
            if (elem == null)
            {
                Logger.Info("[HUD] UiHudElemCountDown not found, please check the UI element ID.");
                return;
            }
            elem.AbortCountDown();
        }
    }
}