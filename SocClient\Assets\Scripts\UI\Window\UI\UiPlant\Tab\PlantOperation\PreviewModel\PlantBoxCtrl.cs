using com.pixui;
using Effect;
using Facepunch;
using System;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.ObjPool;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.SocClient.GoLoader;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Plant;

namespace WizardGames.Soc.SocClient.Ui
{
    public partial class PlantBoxCtrl : MonoBehaviour
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(PlantBoxCtrl));

        public Transform[] slots;
        public Transform[] hightlights;

        private PlantBoxData curPlantBox;
        private Dictionary<int, PlantCtrl> curPlantDic = new Dictionary<int, PlantCtrl>();

        private string layer = string.Empty;

        private Dictionary<int, long> prePlantDic = new Dictionary<int, long>();
        private bool IsInUIMode => this.layer == "UI";

        private void OnEnable()
        {
            Mc.Msg.AddListener<long>(EventDefine.UpdatePlantsInfo, OnUpdatePlantsInfo);
            Mc.Msg.AddListener<long, int>(EventDefine.HarvestSuccess, OnHarvestSuccess);
        }

        private void OnDisable()
        {
            UnHighLightAllSlots();
            DisableAllOutline();
            foreach (var item in curPlantDic.Values)
            {
                Pool.Release(item);
            }
            layer = string.Empty;
            curPlantDic.Clear();
            prePlantDic.Clear();
            Mc.Msg.RemoveListener<long>(EventDefine.UpdatePlantsInfo, OnUpdatePlantsInfo);
            Mc.Msg.RemoveListener<long, int>(EventDefine.HarvestSuccess, OnHarvestSuccess);
        }

        private void OnDestroy()
        {
            ClearPlants();
            OnDestroyFx();
        }

        public void ClearPlants()
        {
            foreach (var item in curPlantDic.Values)
            {
                Pool.Release(item);
            }
            layer = string.Empty;
            curPlantDic.Clear();
        }
        
        public void SetLayer(string layer)
        {
            this.layer = layer;
        }

        public void RefreshPlants(PlantBoxData plantBox = null)
        {
            if (plantBox != null)
            {
                curPlantBox = plantBox;
            }
            if (slots == null || curPlantBox == null) return;
            //已播种
            for (var i = 0; i < slots.Length; ++i)
            {
                var plant = curPlantBox.GetPlantData(i);
                if (plant != null)
                {
                    if (curPlantDic.TryGetValue(i, out var plantCtrl))
                    {
                        plantCtrl.RefreshModel(plant, slots[i], layer, curPlantBox.Water > 0);
                    }
                    else
                    {
                        plantCtrl = Pool.Get<PlantCtrl>();
                        curPlantDic[i] = plantCtrl;
                        plantCtrl.RefreshModel(plant, slots[i], layer, curPlantBox.Water > 0);
                    }
                }
                else
                {
                    if (curPlantDic.TryGetValue(i, out var plantCtrl))
                    {
                        Pool.Release(plantCtrl);
                        curPlantDic.Remove(i);
                    }
                }
            }

            //预种植
            foreach(var item in prePlantDic)
            {
                if (curPlantDic.ContainsKey(item.Key))
                {
                    logger.InfoFormat("pre plant is invalid, slodsdt:{0}", item.Key);
                    continue;
                }
                else
                {
                    var plantCtrl = Pool.Get<PlantCtrl>();
                    curPlantDic[item.Key] = plantCtrl;
                    plantCtrl?.RefreshModelPrePlant(item.Value, PlantStage.Seed, slots[item.Key], layer);
                }
            }
        }

        public void ToggleHighLight(int index)
        {
            if (index < 0 || index >= hightlights.Length)
            {
                return;
            }

            bool isActive = hightlights[index].gameObject.activeSelf;
            hightlights[index].SetActive(!isActive);
        }

        public void RefreshHighLight(int selectedIndex)
        {
            UnHighLightAllSlots();

            HighLightOneSlot(selectedIndex);
        }

        public void RefreshHighLight(List<int> allIndexes)
        {
            UnHighLightAllSlots();

            HighLightMultiple(allIndexes);
        }

        private void OnUpdatePlantsInfo(long collectionId)
        {
            if (curPlantBox != null && curPlantBox.EntityId == collectionId)
            {
                //浇水后引用会变，重新获取一遍
                curPlantBox = Mc.Plant.GetPlantBoxData(curPlantBox.EntityId);
                RefreshPlants();
            }
        }

        public void PrePlant(int slot, long seedId)
        {
            if (curPlantBox == null || slots == null) return;
            if (slot >= 0 && slot < slots.Length)
            {
                if (prePlantDic.TryGetValue(slot, out var oldSeedId) && oldSeedId == seedId) return;
                prePlantDic[slot] = seedId;
                RefreshPlants();
            }
        }

        public void CancelPrePlant(int slot, long seedId)
        {
            if (curPlantBox == null || slots == null) return;
            if (slot >= 0 && slot < slots.Length)
            {
                prePlantDic.Remove(slot);
                RefreshPlants();
            }
        }

        public void UnHighLightAllSlots()
        {
            for (int i = 0; i < slots.Length; i++)
            {
                if (slots[i] != null)
                {
                    UnHighLightOneSlot(i);
                }
            }
        }

        public void ClearAllPrePlantContext()
        {
            UnHighLightAllSlots();

            for (int i = 0; i < slots.Length; ++i)
            {
                if (prePlantDic.ContainsKey(i))
                {
                    prePlantDic.Remove(i);
                    if (curPlantDic.TryGetValue(i, out var plantCtrl))
                    {
                        Pool.Release(plantCtrl);
                    }
                    curPlantDic.Remove(i);
                }
            }

            prePlantDic.Clear();
        }

        private void HighLightOneSlot(int slotIndex)
        {
            if (slotIndex < 0 || slotIndex >= hightlights.Length || (curPlantBox != null && curPlantBox.GetPlantData(slotIndex) != null))
            {
                return;
            }

            hightlights[slotIndex].SetActive(true);
        }

        private void UnHighLightOneSlot(int slotIndex)
        {
            if (slotIndex < 0 || slotIndex >= hightlights.Length)
            {
                return;
            }

            hightlights[slotIndex]?.SetActive(false);
        }

        public void EnableOutline(int slot)
        {
            if (curPlantDic.TryGetValue(slot, out var plantCtrl))
            {
                plantCtrl.EnableOutline();
            }
        }

        public void DisableAllOutline()
        {
            for (var i = 0; i < slots.Length; ++i)
            {
                if (slots[i] != null)
                {
                    if (curPlantDic.TryGetValue(i, out var plantCtrl))
                    {
                        plantCtrl.DisableOutline();
                    }
                }
            }
        }

        private void HighLightMultiple(List<int> indexes)
        {
            foreach (int index in indexes)
            {
                HighLightOneSlot(index);
            }
        }

    }
}
