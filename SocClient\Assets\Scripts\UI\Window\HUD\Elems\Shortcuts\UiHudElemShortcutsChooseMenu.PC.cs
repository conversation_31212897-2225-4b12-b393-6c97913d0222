#if STANDALONE_REAL_PC
using FairyGUI;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.SocClient.Manager;

namespace WizardGames.Soc.SocClient.Ui
{
    public partial class UiHudElemShortcutsChooseMenu
    {
        private Controller dragTitleCtrl;

        private void UiHudElemShortcutsChooseMenu_Plaform(GComponent comRoot)
        {
            dragTitleCtrl = comRoot.GetController("dragTitle");
            comDropbar.onRollOver.Add(OnDropbarRollOver);
            comDropbar.onRollOut.Add(OnDropbarRollOut);
            comDropbar.AddEventListener("onItemDrop", OnDropbarItemDrop);
        }

        public void SetShowDragTitle(bool show)
        {
            comDropbar.RelockUpdateAfterFrameIfLock();
            dragTitleCtrl.selectedIndex = show ? 0 : 1;
        }

        private void OnDropbarRollOver()
        {
            comDropbar.RelockUpdateAfterFrameIfLock();
            dragTitleCtrl.selectedIndex = 1;
        }

        private void OnDropbarRollOut()
        {
            comDropbar.RelockUpdateAfterFrameIfLock();
            dragTitleCtrl.selectedIndex = 0;
        }

        private void OnDropbarItemDrop(EventContext ctx)
        {
            comDropbar.RelockUpdateAfterFrameIfLock();
            dragTitleCtrl.selectedIndex = 0;
        }

        private bool OnIconAcceptDrag_Platform(ComBaseIcon myIcon, ItemDragInfo dragInfo)
        {
            if (null == dragInfo || null == dragInfo.item || myIcon == UiItemIconDragDrop.CurDragIcon)
            {
                return true;
            }

            var dropItem = dragInfo.item;
            bool isWeaponItemNode = dropItem is WeaponItemNode;
            // 如果是远程武器,给个提示
            if (isWeaponItemNode)
            {
                Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.WeaponCannotPutInItem);
                return true;
            }
            return false;
        }
    }
}
#endif