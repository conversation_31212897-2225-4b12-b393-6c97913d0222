#if !STANDALONE_REAL_PC
using System;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.SocClient.Manager;

namespace WizardGames.Soc.SocClient.Ui
{
    public partial class UiHudWeaponChoose : WindowComBase, IUiFps30Update
    {
        private void RefreshBoardState()
        {
            if (null == elemWeapon || null == elemWeapon.statusTarget) return;
            Vector2 globalPos = elemWeapon.GetWeaponChooseBoardGlobalPos();
            comBoard.position = comRoot.GlobalToLocal(globalPos);
            ctrlDropBarStyle.SetSelectedIndex(elemWeapon.IsTabLeft? 0: 1);
            if (elemWeapon.IsTabLeft) triggerAutoChooseX = comDragTip.LocalToStage(Vector2.right * comDragTip.width).x;
            else triggerAutoChooseX = comDragTip.LocalToStage(Vector2.zero).x;
        }

        private void ItemsReSort()
        {

        }

        private void OnHudWeaponIconDragStart_Plaform()
        {
            
        }

        private void OnHudWeaponIconDragEnd_Plaform()
        {
            
        }

        public static UiHudWeaponChoose Open(bool isDrag = false)
        {
            openWithDragState = isDrag;
            var win = Mc.Ui.OpenWindowT<UiHudWeaponChoose>("UiHudWeaponChoose");
            if (null != win) win.IsInDragState = isDrag;
            return win;
        }

        public static void Hide()
        {
            var win = Mc.Ui.GetWindow("UiHudWeaponChoose");
            if (null != win) win.HideSelf();
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            if (!UiHud.IsInEdit)
            {
                elemWeapon = UiHudElemWeapons.GetElem();
                if (null != elemWeapon)
                {
                    elemWeapon.SetChooseStyle(true);
                    elemWeapon.SetWeaponChooseState(true, openWithDragState ? -1 : 0);
                }
            }
            RefreshBoardState();
            RefreshList();
            IsChooseVisible = true;
            Mc.Msg.AddListener(EventDefine.OnHudMakeFullScreen, OnMakeFullScreen);
        }

        protected override void OnDisable()
        {
            openWithDragState = false;
            IsChooseVisible = false;
            CurChooseIndex = -1;
            base.OnDisable();
            curItems.Clear();
            if (!UiHud.IsInEdit && null != elemWeapon)
            {
                elemWeapon.SetWeaponChooseState(false);
                elemWeapon.SetChooseStyle(false);
                elemWeapon.Refresh();
            }
            for (int i = 0; i < listIcons.numChildren; ++i)
            {
                var comChild = listIcons.GetChildAt(i)?.asCom;
                if (null == comChild) continue;
                var icon = comChild.GetChild("icon") as ComItemIcon;
                if (null == icon) continue;
                icon.SetHighlight(false);
            }
            comDropbar.visible = false;
            listIcons.numItems = 0;
            Mc.Msg.RemoveListener(EventDefine.OnHudMakeFullScreen, OnMakeFullScreen);
        }

        private List<BaseItemNode> GetRenderData()
        {
            return curItems;
        }

        private void OnIconAcceptDrag_Palform(ComBaseIcon icon, ItemDragInfo info)
        {

        }
    }
}
#endif