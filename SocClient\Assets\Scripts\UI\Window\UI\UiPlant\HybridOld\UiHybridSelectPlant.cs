using Assets.Scripts.Plant;
using Cysharp.Text;
using FairyGUI;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Framework;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.Unity.Construction;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Plant;
using WizardGames.Soc.SocClient.Ui.Utils;

namespace WizardGames.Soc.SocClient.Ui
{

    /// <summary>
    /// 杂交植物选择阶段
    /// </summary>
    public enum SelectStep
    {
        Target,
        Material
    }

    public class UiHybridSelectPlant
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(UiHybridSelectPlant));

        private GComponent rootNode;
        private GComponent comPlantBox;
        private GButton btnNextStep;
        private GTextField textPlantNum;
        private GList listMaterialPlants;
        private GLoader loader3D;
        private Controller selectControl;


        private ComShowItemModel comShowItemModel;

        private Dictionary<GObject, Dictionary<string, GObject>> listItemGoDic = new();

        private Vector2 touchStartPos = Vector2.zero;

        private UiHybridMain uiHybridMain;

        private UiPlantBox2D uiPlantBox2D;

        private PlantBoxCtrl plantBoxCtrl;


        /// <summary>
        /// 当前选择阶段
        /// </summary>
        public SelectStep CurSelectStep => selectControl == null ? SelectStep.Target : (SelectStep)selectControl.selectedIndex;

        /// <summary>
        /// 当前所选的植株
        /// </summary>
        public List<PlantData> CurSelectPlants => uiHybridMain == null ? new() : uiHybridMain.CurSelectPlants;

        private PlantBoxData CurPlantBox => (uiHybridMain.UiPlantMain.TabPanelDic[ETabType.PlantOperation] as UiPlantOperationSubPanel).CurPlantBox;

        public static UiHybridSelectPlant Create(GComponent com, UiHybridMain hybridMain)
        {
            var rt = new UiHybridSelectPlant();
            rt.Init(com, hybridMain);
            return rt;
        }

        private void Init(GComponent root, UiHybridMain hybridMain)
        {
            uiHybridMain = hybridMain;
            rootNode = root;
            comPlantBox = rootNode.GetChild("com_plantBox").asCom;
            btnNextStep = rootNode.GetChild("btn_nextStep").asButton;
            textPlantNum = rootNode.GetChild("text_plantNum").asTextField;
            listMaterialPlants = rootNode.GetChild("list_materialPlants").asList;
            loader3D = rootNode.GetChild("loader_3d").asLoader;
            selectControl = rootNode.GetController("select");

            comShowItemModel = new ComShowItemModel(loader3D);
            comShowItemModel.TryInitScene(modelType: MgrUiModel.ModelType.Plant);
            uiPlantBox2D = UiPlantBox2D.Create(comPlantBox, null,EPageType.Hybrid, RefreshSelectedPlant, this);

            RefreshNextStepBtn();

            if (listMaterialPlants != null)
            {
                listMaterialPlants.itemRenderer = OnRenderMaterialList;
            }

            btnNextStep.onClick.Add(DoNextStep);
        }

        private void DoNextStep()
        {

            if (CurSelectStep == SelectStep.Target)
            {
                EnterSelectPlantPage(SelectStep.Material);
                return;
            }
            var plantBoxEntity = EntityManager.Instance.GetEntity(CurPlantBox.EntityId);
            if (plantBoxEntity != null)
            {
                var plantBox = plantBoxEntity.GetComponent<PlantBoxComponent>(EComponentIdEnum.PlantBox);
                BasicTypeList<int> selectIndexs = new();
                for (var i = 1; i < CurSelectPlants.Count; i++)
                {
                    selectIndexs.Add(CurSelectPlants[i].NodeIndex);
                }
                plantBox.RemoteCallManualHybridizeStart(ERpcTarget.World, CurSelectPlants[0].NodeIndex, selectIndexs);
                logger.InfoFormat("杂交||RemoteCallManualHybridizeStart NodeIndex：{0} selectIndexsCount :{1}", CurSelectPlants[0].NodeIndex, selectIndexs.Count);
            }

            uiHybridMain.RefreshHybridData();
            uiHybridMain.SelectPage(HybridStage.GeneShow);
        }

        /// <summary>
        /// 进入植物选择阶段
        /// </summary>
        /// <param name="selectStep"></param>
        public void EnterSelectPlantPage(SelectStep selectStep)
        {
            selectControl.selectedIndex = (int)selectStep;
            uiPlantBox2D.Refresh(CurPlantBox);
            RefreshNextStepBtn();
            RefreshPlantList();
        }

        public void Show()
        {
            Refresh();
            EnterSelectPlantPage(SelectStep.Target);
        }

        public void Refresh()
        {
            comShowItemModel?.Show();
            uiPlantBox2D.Refresh(CurPlantBox);
            RefreshSelectedPlant();
            InitPlantBoxModel();
            RefreshNextStepBtn();
        }

        public void Hide()
        {
            for (var i = CurSelectPlants.Count - 1; i >= 0; i--)
            {
                RemovePlant(i);
            }
            comShowItemModel?.Hide();

        }

        /// <summary>
        /// 清除杂交植株锁定
        /// </summary>
        public void ClearPlantLock()
        {
            var plantBoxEntity = EntityManager.Instance.GetEntity(CurPlantBox.EntityId);
            if (plantBoxEntity != null)
            {
                var plantBox = plantBoxEntity.GetComponent<PlantBoxComponent>(EComponentIdEnum.PlantBox);
                var num = Common.Plant.PlantUtils.DecryptMagicNum(Mc.Plant.MagicNum, McCommon.Tables.TbPlantConstConfig.HybridizationNorNum);
                plantBox.RemoteCallManualHybridizeClear(ERpcTarget.World, num);
                logger.Info("杂交||RemoteCallManualHybridizeClear");
            }
        }

        public void OnDestory()
        {
            Hide();
            comShowItemModel?.Clear();
            comShowItemModel = null;
        }
        /// <summary>
        /// 刷新下一步按钮的状态
        /// </summary>
        private void RefreshNextStepBtn()
        {
            var plantCount = uiPlantBox2D.SelectedIndexSort.Count;
            btnNextStep.visible = (plantCount >= 1 && CurSelectStep == SelectStep.Target) ||
                                  CurSelectStep == SelectStep.Material && plantCount >= McCommon.Tables.TbPlantConstConfig.HybridizationNeedMaxCount;
        }

        /// <summary>
        /// 杂交材料List刷新函数
        /// </summary>
        /// <param name="index"></param>
        /// <param name="obj"></param>
        private void OnRenderMaterialList(int index, GObject obj)
        {
            if (CurSelectPlants == null || index >= CurSelectPlants.Count || obj == null) return;

            var comMat = obj.asCom;
            if (comMat == null) return;
            var plantData = CurSelectPlants[index];
            var isTarget = index == 0;
            var isLock = (CurSelectStep == SelectStep.Target && !isTarget) || (CurSelectStep == SelectStep.Material && isTarget);
            comMat.touchable = !isLock;
            comMat.grayed = isLock;
            comMat.GetController("type").selectedIndex = isTarget ? 0 : 1;
            if (!listItemGoDic.TryGetValue(obj, out var itemDic))
            {
                itemDic = new();
                var item = obj.asCom;
                var textPlantTarget = item.GetChild("text_plantTarget").asTextField;
                var textPlantName = item.GetChild("text_plantName").asTextField;
                var labelTag = item.GetChild("label_tag").asLabel;
                var iconPlant = item.GetChild("icon_plant").asLoader;
                var listGene = item.GetChild("list_gene").asList;
                var btnCancel = item.GetChild("btn_cancel").asButton;
                itemDic.Add("text_plantTarget", textPlantTarget);
                itemDic.Add("text_plantName", textPlantName);
                itemDic.Add("label_tag", labelTag);
                itemDic.Add("icon_plant", iconPlant);
                itemDic.Add("listGene", listGene);
                itemDic.Add("btn_cancel", btnCancel);
                listItemGoDic.Add(obj, itemDic);
            }

            if (!listItemGoDic.TryGetValue(obj, out var items)) return;
            if (items.TryGetValue("text_plantTarget", out var textTitle))
            {
                var title = ZString.Format("{0}{1}", isTarget ? "Target Plant" : "Material Plant", index);
                //var title = isTarget ? "Target Plant" : "Material Plant" + index;
                SafeUtil.SafeSetText(textTitle, title);
            }
            if (items.TryGetValue("text_plantName", out var textName))
            {
                SafeUtil.SafeSetText(textName, plantData.plantSeedCfg.Name);
            }
            if (items.TryGetValue("label_tag", out var textTag))
            {
                SafeUtil.SafeSetText(textTag, plantData.Stage.ToString());
            }
            if (items.TryGetValue("icon_plant", out var icon))
            {
                SafeUtil.SafeSetLoaderUrl(icon as GLoader, plantData.plantSeedCfg.Icon);
            }
            if (items.TryGetValue("listGene", out var geneObj))
            {
                var geneList = geneObj.asList;
                if (geneList != null)
                {
                    uiHybridMain.OnRenderGeneList(geneList, index);
                }
            }
            if (items.TryGetValue("btn_cancel", out var cancelObj))
            {
                var btnCancel = cancelObj.asButton;
                if (btnCancel != null)
                {
                    btnCancel.onClick.Clear();
                    btnCancel.onClick.Add(() => { RemovePlant(index); });
                }
            }
        }

        /// <summary>
        /// 刷新杂交植株列表
        /// </summary>
        private void RefreshPlantList()
        {
            listMaterialPlants.numItems = CurSelectPlants.Count;
            for (int i = 0; i < listMaterialPlants.numChildren; i++)
            {
                var item = listMaterialPlants.GetChildAt(i);
                OnRenderMaterialList(i, item);
            }
        }

        /// <summary>
        /// 移除杂交植株方法
        /// </summary>
        /// <param name="index"></param>
        private void RemovePlant(int index)
        {
            if (CurSelectPlants == null || index >= CurSelectPlants.Count) return;
            var plantData = CurSelectPlants[index];
            var plantSlot = CurPlantBox.GetPlantDataIndex(plantData);
            uiPlantBox2D.RemoveIndex(plantSlot);
            RefreshSelectedPlant();
        }

        private void RefreshSelectedPlant()
        {
            CurSelectPlants.Clear();
            var indexList = uiPlantBox2D.SelectedIndexSort;
            foreach (var i in indexList)
            {
                var plant = CurPlantBox.GetPlantData(i);
                if (plant != null)
                {
                    CurSelectPlants.Add(plant);
                }
            }
            RefreshPlantList();
            uiHybridMain.RefreshHybridData();
            var maxCount = Mc.Tables.TbPlantConstConfig.HybridizationNeedMaxCount;
            RefreshNextStepBtn();
            var content = ZString.Format("{0}/{1}", indexList.Count, maxCount);
            SafeUtil.SafeSetText(textPlantNum, content);
        }

        #region 模型

        private void InitPlantBoxModel()
        {
            if (CurPlantBox != null && CurPlantBox.plantBoxCfg != null)
            {
                string modelPath = CommonConstructionUtils.GetSeparatePrefabPathByType(CurPlantBox.plantBoxCfg.Id, EConstructionPrefabType.SourceMesh, 0);
                if (!string.IsNullOrEmpty(modelPath))
                {
                    if (comShowItemModel != null)
                    {
                        comShowItemModel.RefreshModel(modelPath, (info) =>
                        {
                            if (info == null || info.objModelHolder == null) return;
                            Quaternion rotation = Quaternion.Euler(CurPlantBox.plantBoxCfg.ModelOriAngle[0], CurPlantBox.plantBoxCfg.ModelOriAngle[1], CurPlantBox.plantBoxCfg.ModelOriAngle[2]);
                            info.objModelHolder.transform.rotation = rotation;
                            info.objModelHolder.transform.localScale = Vector3.one * CurPlantBox.plantBoxCfg.ModelZoom;
                            info.objModelHolder.transform.localPosition = new Vector3(CurPlantBox.plantBoxCfg.ModelOffset[0], CurPlantBox.plantBoxCfg.ModelOffset[1], CurPlantBox.plantBoxCfg.ModelOffset[2]);
                            if (comShowItemModel != null)
                            {
                                comShowItemModel.EnableTouch(OnItemTouchBegin, OnItemTouchMove);
                                comShowItemModel.EnableClick(OnClickLoader);
                                plantBoxCtrl = comShowItemModel?.modelGO?.GetComponent<PlantBoxCtrl>();
                                plantBoxCtrl?.SetLayer("UI");
                                plantBoxCtrl?.RefreshPlants(CurPlantBox);
                            }
                        });
                    }
                }
            }
        }

        public void OnItemTouchBegin(EventContext context)
        {
            touchStartPos = context.inputEvent.position;
            context.CaptureTouch();
        }

        public void OnItemTouchMove(EventContext context)
        {
            var deltaX = context.inputEvent.x - touchStartPos.x;
            var deltaY = context.inputEvent.y - touchStartPos.y;
            comShowItemModel?.Rotate(new Vector3(-deltaY, -deltaX, 0), Space.World);
            touchStartPos = context.inputEvent.position;
        }

        public void OnClickLoader(EventContext context)
        {
            if (context == null || comShowItemModel == null) return;
            comShowItemModel.RayCastPlant(context.inputEvent.position, out var hit);
            if (hit.transform != null)
            {
                var index = hit.transform.name.LastIndexOf("_");
                if (int.TryParse(hit.transform.name.Substring(index + 1), out var slot))
                {
                    Mc.Msg.FireMsg(EventDefine.PlantBoxModelSelectedSlot, slot, (int)EPageType.Hybrid);
                }
            }
        }

        #endregion

    }

}