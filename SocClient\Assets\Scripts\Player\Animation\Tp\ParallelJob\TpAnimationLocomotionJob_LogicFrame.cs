﻿using Unity.Mathematics;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.Defines;
using WizardGames.Soc.Common.Unity.Parachute;
using WizardGames.Soc.Common.Unity.Systems;


namespace WizardGames.Soc.SocClient.Player.Animation
{
    public partial struct TpAnimationJob
    {
        private void UpdateLocomotionLayerLogicFrame_ParamsBefore(ref TpAnimationJobData jobData,
            ref TpAnimationResultJobData resultJobData, ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            UpdateLerpStandCrouch(ref jobData, ref resultJobData, ref animParams, in constData);
        }
        
        private void UpdateLocomotionLayerLogicFrame_ParamsAfter(ref TpAnimationJobData jobData,
            ref TpAnimationResultJobData resultJobData, ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            UpdateLocomotionLayerCommonParams(ref jobData, ref resultJobData, ref animParams, in constData);
            UpdateMoveDirectionInCharacterCoordinate(ref jobData, ref resultJobData, constData, ref animParams);
            UpdateNormalizeSpeed(ref jobData, ref resultJobData, ref animParams, in constData, ref resultJobData.PlayerLocalData);
        }

        private void UpdateLocomotionLayerLogicFrame_Tick(ref TpAnimationJobData jobData,
            ref TpAnimationResultJobData resultJobData, ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            playerLocalData.PoseNeedTransition = true;
            
            var inState = false;
            var goToState = false;
            
            goToState = GoToLocomotionEmpty_Sleep(ref jobData, ref resultJobData, ref animParams, constData, ref inState);
            if (!goToState && !inState)
            {
                goToState = GoToInjuredState(ref jobData, ref resultJobData, ref animParams, constData, ref inState);
            }
            if (!goToState && !inState)
            {
                goToState = GoToZiplineState(ref jobData, ref resultJobData, ref animParams, constData, ref inState);
            }
            if (!goToState && !inState)
            {
                goToState = GoToRiderState(ref jobData, ref resultJobData, ref animParams, constData, ref inState);
            }
            if (!goToState && !inState)
            {
                goToState = GotoParachuteState(ref jobData, ref resultJobData, ref animParams, constData, ref inState);
            }
            if (!goToState && !inState)
            {
                goToState =  GoToDriveState(ref jobData, ref resultJobData, ref animParams, constData, ref inState);
            }
            if (!goToState && !inState)
            {
                goToState =  GoToMountState(ref jobData, ref resultJobData, ref animParams, constData, ref inState);
            }
            if (!goToState && !inState)
            {
                goToState =  GoToSwimState(ref jobData, ref resultJobData, ref animParams, constData, ref inState);
            }
            if (!goToState && !inState)
            {
                goToState =  GoToLadderState(ref jobData, ref resultJobData, ref animParams, constData, ref inState);
            }
            if (!goToState && !inState)
            {
                goToState =  GoToMantleState(ref jobData, ref resultJobData, ref animParams, constData, ref inState);
            }
            if (!goToState && !inState)
            {
                goToState =  GoToJumpState(ref jobData, ref resultJobData, ref animParams, constData, ref inState);
            }
            if (!goToState && !inState)
            {
                goToState =  GoToJogState(ref jobData, ref resultJobData, ref animParams, constData, ref inState);
            }
            if (!goToState && !inState)
            {
                GoToIdleState(ref jobData, ref resultJobData, ref animParams, constData);
            }
            
            if (!goToState)
            {
                //空的，所有gotostate的instate要特殊化
                UpdateLocomotionEmpty_Sleep(ref jobData, ref resultJobData, ref animParams, constData);
                UpdateInjuredState(ref jobData, ref resultJobData, ref animParams, constData);
                UpdateZiplineState(ref jobData, ref resultJobData, ref animParams, constData);
                UpdateRiderState(ref jobData, ref resultJobData, ref animParams, constData);
                UpdateParachuteState(ref jobData, ref resultJobData, ref animParams, constData);
                UpdateDriveState(ref jobData, ref resultJobData, ref animParams, constData);
                //空的，所有gotostate的instate要特殊化
                UpdateMountState(ref jobData, ref resultJobData, ref animParams, constData);
                UpdateSwimState(ref jobData, ref resultJobData, ref animParams, constData);
                UpdateLadderState(ref jobData, ref resultJobData, ref animParams, constData);
                UpdateMantleState(ref jobData, ref resultJobData, ref animParams, constData);
                UpdateJumpState(ref jobData, ref resultJobData, ref animParams, constData);
                UpdateJogState(ref jobData, ref resultJobData, ref animParams, constData);
                //sprint的goto不需要，只能通过jog进入
                UpdateSprintState(ref jobData, ref resultJobData, ref animParams, constData);
                UpdateIdleState(ref jobData, ref resultJobData, ref animParams, constData);
            }
        }
        
        private bool GoToLocomotionEmpty_Sleep(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData, ref bool inState)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            var isSleep = playerStateData.UnAliveState == PlayerUnAliveStateEnum.GoSleep ||
                          playerStateData.UnAliveState == PlayerUnAliveStateEnum.Sleep;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var inSleep = playerLocalData.ELocomotionLayer == AnimParametersTp.ELocomotionLayer.LocomotionEmpty;
            if (!inSleep && isSleep)
            {
                ToLocomotionLayerState(ref jobData, ref resultJobData,  AnimParametersTp.LocomotionLayer_LocomotionEmpty_NameId, AnimParametersTp.ELocomotionLayer.LocomotionEmpty,
                    0f, 0f, true, true);
                return true;
            }
            if (inSleep && isSleep)
            {
                inState = true;
            }
            //!sleep通过其他逻辑跳转
            return false;
        }
        
        private void UpdateLocomotionEmpty_Sleep(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
        }
        
        private bool GoToInjuredState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData, ref bool inState)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var isInjured = playerStateData.PoseDyingState == PlayerPoseDyingStateEnum.Crawl ||
                            playerStateData.PoseDyingState == PlayerPoseDyingStateEnum.Incapacitated;
            var inInjured = InLocomotionInjured(playerLocalData.ELocomotionLayer) ||
                            InLocomotionInCap(playerLocalData.ELocomotionLayer);
            if (!inInjured && isInjured)
            {
                if (playerStateData.PoseDyingState == PlayerPoseDyingStateEnum.Crawl)
                {
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_Injured_ToInjured_NameId,
                        AnimParametersTp.ELocomotionLayer.Injured_ToInjured,
                        0.1f, 0, true, true);
                }
                else if (playerStateData.PoseDyingState == PlayerPoseDyingStateEnum.Incapacitated)
                {
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_InCap_ToLieDown_NameId,
                        AnimParametersTp.ELocomotionLayer.InCap_ToLieDown,
                        0.1f, 0, true, true);
                }
                return true;
            }
            inState = inInjured;
            return false;
        }

        private void UpdateInjuredState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var inInjured = InLocomotionInjured(playerLocalData.ELocomotionLayer) ||
                            InLocomotionInCap(playerLocalData.ELocomotionLayer);
            if (!inInjured)
                return;
            UpdateCrawlState(ref jobData, ref resultJobData, ref animParams, constData);
            UpdateLieDownState(ref jobData, ref resultJobData, ref animParams, constData);
        }
        
        private bool GoToZiplineState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData, ref bool inState)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var isZipline = playerStateData.MoveZiplineState == PlayerMoveZiplineStateEnum.ZiplineIn ||
                            playerStateData.MoveZiplineState == PlayerMoveZiplineStateEnum.ZiplineSlow;
            var inZipline = InLocomotionZipline(playerLocalData.ELocomotionLayer);
            if (!inZipline && isZipline)
            {
                switch (playerStateData.MoveZiplineState)
                {
                    case PlayerMoveZiplineStateEnum.ZiplineIn:
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Zipline_ZiplineStandIn_NameId,
                            AnimParametersTp.ELocomotionLayer.Zipline_ZiplineStandIn,
                            0.25f, 0, true, true);
                        break;
                    case PlayerMoveZiplineStateEnum.ZiplineSlow:
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Zipline_ZiplineSlow_NameId,
                            AnimParametersTp.ELocomotionLayer.Zipline_ZiplineSlow,
                            0.25f, 0, true, true);
                        break;
                }
                return true;
            }
            inState = inZipline;
            return false;
        }

        private void UpdateZiplineState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var inZipline = InLocomotionZipline(playerLocalData.ELocomotionLayer);
            if (!inZipline)
                return;
            var zipline = playerLocalData.ELocomotionLayer == AnimParametersTp.ELocomotionLayer.Zipline_ZiplineSlow ||
                          playerLocalData.ELocomotionLayer ==
                          AnimParametersTp.ELocomotionLayer.Zipline_ZiplineStandIn ||
                          playerLocalData.ELocomotionLayer == AnimParametersTp.ELocomotionLayer.Zipline_ZiplineFast ||
                          playerLocalData.ELocomotionLayer == AnimParametersTp.ELocomotionLayer.Zipline_ZiplineSwitch ||
                          playerLocalData.ELocomotionLayer == AnimParametersTp.ELocomotionLayer.Zipline_ZiplineStandOut;
            if (!zipline)
                return;
            switch (playerLocalData.ELocomotionLayer)
            {
                case AnimParametersTp.ELocomotionLayer.Zipline_ZiplineStandIn:
                    if (playerStateData.MoveZiplineState == PlayerMoveZiplineStateEnum.ZiplineSlow)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Zipline_ZiplineSlow_NameId,
                            AnimParametersTp.ELocomotionLayer.Zipline_ZiplineSlow,
                            0.1f, 0, true, true);
                        return;
                    }
                    if (playerStateData.PoseState != PlayerPoseStateEnum.Zipline)
                    {
                        var over = playerLocalData.TpClipCollect.NowLocState.Percent >= 0.5f;
                        if (over)
                        {
                            ToLocomotionLayerState(ref jobData, ref resultJobData,  AnimParametersTp.LocomotionLayer_LocomotionEmpty_NameId, AnimParametersTp.ELocomotionLayer.LocomotionEmpty,
                                0f, 0f, true, true);
                        }
                    }
                    break;
                case AnimParametersTp.ELocomotionLayer.Zipline_ZiplineSlow:
                    if (playerStateData.PoseState != PlayerPoseStateEnum.Zipline)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Zipline_ZiplineStandOut_NameId,
                            AnimParametersTp.ELocomotionLayer.Zipline_ZiplineStandOut,
                            0.113293f, 0, true, true);
                        return;
                    }
                    if (playerStateData.MoveZiplineState == PlayerMoveZiplineStateEnum.ZiplineFast)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Zipline_ZiplineSwitch_NameId,
                            AnimParametersTp.ELocomotionLayer.Zipline_ZiplineSwitch,
                            0.1148193f, 0, true, true);
                    }
                    break;
                case AnimParametersTp.ELocomotionLayer.Zipline_ZiplineSwitch:
                    if (playerStateData.PoseState != PlayerPoseStateEnum.Zipline)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Zipline_ZiplineStandOut_NameId,
                            AnimParametersTp.ELocomotionLayer.Zipline_ZiplineStandOut,
                            0.1094377f, 0, true, true);
                        return;
                    }
                    if (playerStateData.MoveState == PlayerMoveStateEnum.MoveSwim)
                    {
                        var over = playerLocalData.TpClipCollect.NowLocState.Percent >= 0.815481f;
                        if (over)
                        {
                            ToLocomotionLayerState(ref jobData, ref resultJobData,
                                AnimParametersTp.LocomotionLayer_Zipline_ZiplineSlow_NameId,
                                AnimParametersTp.ELocomotionLayer.Zipline_ZiplineSlow,
                                0.1230121f, 0, true, true);
                            return;
                        }
                    }
                    var overSwitch = playerLocalData.TpClipCollect.NowLocState.Percent >= 0.8687034;
                    if (overSwitch)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Zipline_ZiplineFast_NameId,
                            AnimParametersTp.ELocomotionLayer.Zipline_ZiplineFast,
                            0.08753109f, 0, true, true);
                    }
                    break;
                case AnimParametersTp.ELocomotionLayer.Zipline_ZiplineFast:
                    if (playerStateData.PoseState != PlayerPoseStateEnum.Zipline)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Zipline_ZiplineStandOut_NameId,
                            AnimParametersTp.ELocomotionLayer.Zipline_ZiplineStandOut,
                            0.1437039f, 0, true, true);
                        return;
                    }
                    if (playerStateData.MoveZiplineState == PlayerMoveZiplineStateEnum.ZiplineSlow)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Zipline_ZiplineSlow_NameId,
                            AnimParametersTp.ELocomotionLayer.Zipline_ZiplineSlow,
                            0.4375405f, 0, true, true);
                    }
                    break;
                case AnimParametersTp.ELocomotionLayer.Zipline_ZiplineStandOut:
                    var standOutOver = playerLocalData.TpClipCollect.NowLocState.Percent >= 0.8117162f;
                    if (standOutOver)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,  AnimParametersTp.LocomotionLayer_LocomotionEmpty_NameId, AnimParametersTp.ELocomotionLayer.LocomotionEmpty,
                            0f, 0f, true, true);
                    }
                    break;
            }
        }

        private bool GoToRiderState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData, ref bool inState)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            ref var vehicleData = ref jobData.VehicleData;
            var inRider = InLocomotionRiderLocomotion(playerLocalData.ELocomotionLayer) ||
                          InLocomotionRiderHalter(playerLocalData.ELocomotionLayer) ||
                          InLocomotionRiderJump(playerLocalData.ELocomotionLayer);
            var isOnMount = playerStateData.MountableId > 0;
            /* 是否在驾驶载具，驾驶的载具是否是马匹*/
            var isRide = false;
            if (isOnMount)
            {
                if (playerStateData.MountableType == EntityTypeId.HorseEntity &&
                    playerStateData.MoveState == PlayerMoveStateEnum.Mountable)
                {
                    isRide = true;
                }
            }
            if (!inRider && isRide)
            {
                if (vehicleData.HorseAniType == (int)EHorseAnimType.Jump)
                {
                    ToRiderJumpState(ref jobData, ref resultJobData, 0f, 0, true);
                    return true;
                }
                else if (vehicleData.HorseAniType == (int)EHorseAnimType.Halter)
                {
                    ToLocomotionLayerState(ref jobData, ref resultJobData, AnimParametersTp.LocomotionLayer_RiderJump_Neigh_NameId, AnimParametersTp.ELocomotionLayer.RiderJump_Neigh,
                        0, 0, true);
                    return true;
                }
                else
                {
                    ToLocomotionLayerState(ref jobData, ref resultJobData, AnimParametersTp.LocomotionLayer_RiderLocomotion_RiderLocomotionNode_NameId, AnimParametersTp.ELocomotionLayer.RiderLocomotion_RiderLocomotionNode,
                        0, 0, true);
                    return true;
                }
            }
            inState = inRider;
            return false;
        }

        private void UpdateRiderState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            ref var playerStateData = ref jobData.PlayerStateData;
            var inRider = InLocomotionRiderLocomotion(playerLocalData.ELocomotionLayer) ||
                          InLocomotionRiderHalter(playerLocalData.ELocomotionLayer) ||
                          InLocomotionRiderJump(playerLocalData.ELocomotionLayer);
            var isOnMount = playerStateData.MountableId > 0;
            var isRide = false;
            if (isOnMount)
            {
                if (playerStateData.MountableType == EntityTypeId.HorseEntity &&
                    playerStateData.MoveState == PlayerMoveStateEnum.Mountable)
                {
                    isRide = true;
                }
            }
            if (!inRider)
                return;
            UpdateRiderLocomotion(ref jobData, ref resultJobData, ref animParams, constData, isRide);
            UpdateRiderHalter(ref jobData, ref resultJobData, ref animParams, constData, isRide);
            UpdateRiderJump(ref jobData, ref resultJobData, ref animParams, constData, isRide);
        }

        private bool GotoParachuteState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData, ref bool inState)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            ref var heldItemData =ref jobData.HeldItemData;
            var isParachute = false;
            var isOnMount = playerStateData.MountableId > 0;
            if (isOnMount && HeldItemSystemUtils.IsParachute(heldItemData.CurrentWeaponTableId))
            {
                isParachute = true;
            }
            var inParachute = InLocomotionParachute(playerLocalData.ELocomotionLayer);
            if (!inParachute && isParachute)
            {
                ToParachuteLocomotion(ref jobData, ref resultJobData, 0f, 0, true);
                return true;
            }
            inState = inParachute;
            return false;
        }

        private void UpdateParachuteState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            ref var heldItemData =ref jobData.HeldItemData;
            var parachute = playerLocalData.ELocomotionLayer ==
                            AnimParametersTp.ELocomotionLayer.Parachute_ParachuteIdle ||
                            playerLocalData.ELocomotionLayer == AnimParametersTp.ELocomotionLayer.Parachute_Start ||
                            playerLocalData.ELocomotionLayer == AnimParametersTp.ELocomotionLayer.Parachute_Cut;
            if (!parachute)
                return;
            if (!HeldItemSystemUtils.IsParachute(heldItemData.CurrentWeaponTableId))
            {
                ToLocomotionLayerState(ref jobData, ref resultJobData,  AnimParametersTp.LocomotionLayer_LocomotionEmpty_NameId, AnimParametersTp.ELocomotionLayer.LocomotionEmpty,
                    0f, 0f, true, true);
                return;
            }
            switch ( jobData.VehicleData.ParachuteState)
            {
                case (int)EParachuteMoveState.Start:
                    if (playerLocalData.ELocomotionLayer != AnimParametersTp.ELocomotionLayer.Parachute_Start)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Parachute_Start_NameId,
                            AnimParametersTp.ELocomotionLayer.Parachute_Start,
                            0f, 0, true);
                    }
                    break;
                case (int)EParachuteMoveState.Cut:
                    if (playerLocalData.ELocomotionLayer != AnimParametersTp.ELocomotionLayer.Parachute_Cut)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Parachute_Cut_NameId,
                            AnimParametersTp.ELocomotionLayer.Parachute_Cut,
                            0f, 0, true);
                    }
                    break;
                case (int)EParachuteMoveState.Fall:
                    if (playerLocalData.ELocomotionLayer != AnimParametersTp.ELocomotionLayer.Parachute_ParachuteIdle)
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Parachute_ParachuteIdle_NameId,
                            AnimParametersTp.ELocomotionLayer.Parachute_ParachuteIdle,
                            0f, 0, true);
                    }
                    break;
            }
        }
        
        private bool GoToDriveState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData, ref bool inState)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var interval = playerLocalData.LogicInterval / 1000.0f;
            var vehicleType = GetVehicleType(ref jobData, constData, out var isDriver, out var isPassenger);
            ref var heldItemData =ref jobData.HeldItemData;
            if ((VehicleType)vehicleType == VehicleType.Horse || (VehicleType)vehicleType == VehicleType.Parachute)
                return false;
            if (InLocomotionDrive(playerLocalData.ELocomotionLayer))
            {
                inState = true;
                return false;
            }
            if ((VehicleType)vehicleType == VehicleType.Kayak)
            {
                var weaponIsKayak = (TableItemEnum)heldItemData.CurrentTableItemEnum == TableItemEnum.Melee_paddle;
                if (weaponIsKayak)
                {
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_Drive_KayakRaise_NameId,
                        AnimParametersTp.ELocomotionLayer.Drive_KayakRaise,
                        0f, 0, true, true);
                    return true;
                }
            }
            if (isDriver)
            {
                ref var vehicleData = ref jobData.VehicleData;
                //开车
                if ((VehicleType)vehicleType == VehicleType.ModularCar)
                {
                    var wantSteerAngle = vehicleData.SteerAngle / 35;
                    wantSteerAngle *= 0.3f;
                    var lastAnimSteerAngleTp = playerLocalData.LastAnimSteerAngleTp;
                    var nowAnimSteerAngleTp = Lerp(lastAnimSteerAngleTp, wantSteerAngle,
                        interval * TpAniConstData.LerpSteerAngleSpeed);
                    playerLocalData.LastAnimSteerAngleTp = nowAnimSteerAngleTp;
                    if (math.abs(nowAnimSteerAngleTp) <= 0.1f)
                    {
                        playerLocalData.EnableInterpolation = false;
                        playerLocalData.AlwaysUpdate = true;
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Drive_Idle_NameId,
                            AnimParametersTp.ELocomotionLayer.Drive_Idle,
                            0f, 0, true, true);
                        return true;
                    }
                    else
                    {
                        if (nowAnimSteerAngleTp > 0)
                        {
                            ToLocomotionLayerState(ref jobData, ref resultJobData,
                                AnimParametersTp.LocomotionLayer_Drive_ModularDriveR_NameId,
                                AnimParametersTp.ELocomotionLayer.Drive_ModularDriveR,
                                0f, nowAnimSteerAngleTp, false, true);
                            return true;
                        }
                        else
                        {
                            ToLocomotionLayerState(ref jobData, ref resultJobData,
                                AnimParametersTp.LocomotionLayer_Drive_ModularDriveR_NameId,
                                AnimParametersTp.ELocomotionLayer.Drive_ModularDriveL,
                                0f, -nowAnimSteerAngleTp, false, true);
                            return true;
                        }
                    }
                }
                else
                {
                    playerLocalData.EnableInterpolation = false;
                    playerLocalData.AlwaysUpdate = true;
                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                        AnimParametersTp.LocomotionLayer_Drive_Idle_NameId,
                        AnimParametersTp.ELocomotionLayer.Drive_Idle,
                        0f, 0, true, true);
                    return true;
                }
            }
            inState = InLocomotionDrive(playerLocalData.ELocomotionLayer);
            return false;
        }

        //TODO 这里还是放在logicframe里， 未来如果表现卡顿，则放在渲染帧里Tick
        private void UpdateDriveState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var drive = playerLocalData.ELocomotionLayer == AnimParametersTp.ELocomotionLayer.Drive_KayakRaise ||
                        playerLocalData.ELocomotionLayer == AnimParametersTp.ELocomotionLayer.Drive_KayakDrive ||
                        playerLocalData.ELocomotionLayer == AnimParametersTp.ELocomotionLayer.Drive_Idle ||
                        playerLocalData.ELocomotionLayer == AnimParametersTp.ELocomotionLayer.Drive_ModularDriveL ||
                        playerLocalData.ELocomotionLayer == AnimParametersTp.ELocomotionLayer.Drive_ModularDriveR;
            if (!drive)
                return;
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var heldItemData =ref jobData.HeldItemData;
            ref var vehicleData = ref jobData.VehicleData;
            var interval = playerLocalData.LogicInterval / 1000.0f;
            var vehicleType = GetVehicleType(ref jobData, constData, out var isDriver, out var isPassenger);
            //马的驾驶不走这里
            if ((VehicleType)vehicleType == VehicleType.Horse)
            {
                return;
            }
            var weaponIsKayak = (TableItemEnum)heldItemData.CurrentTableItemEnum ==
                                TableItemEnum.Melee_paddle;
            if (!weaponIsKayak && (VehicleType)vehicleType == VehicleType.Kayak)
            {
                ToLocomotionLayerState(ref jobData, ref resultJobData,  AnimParametersTp.LocomotionLayer_LocomotionEmpty_NameId, AnimParametersTp.ELocomotionLayer.LocomotionEmpty,
                    0f, 0f, true, true);
                return;
            }
            switch (playerLocalData.ELocomotionLayer)
            {
                case AnimParametersTp.ELocomotionLayer.Drive_KayakRaise:
                    
                    if ((VehicleType)vehicleType == VehicleType.Kayak)
                    {
                        var kayakRaiseOver = playerLocalData.TpClipCollect.NowLocState.Percent >= 0.75f;
                        if (kayakRaiseOver)
                        {
                            var kayakNoInput = math.abs(playerLocalData.MoveForward)<0.0001f&&
                                               math.abs(playerStateData.Movement8Direction) <0.0001f &&
                                               math.abs(playerLocalData.MoveRight) < 0.0001f;
                            if (kayakNoInput)
                            {
                                playerLocalData.EnableInterpolation = false;
                                playerLocalData.AlwaysUpdate = true;
                                ToLocomotionLayerState(ref jobData, ref resultJobData,
                                    AnimParametersTp.LocomotionLayer_Drive_Idle_NameId,
                                    AnimParametersTp.ELocomotionLayer.Drive_Idle,
                                    0f, 0, true, true);
                            }
                            else
                            {
                                ToLocomotionLayerState(ref jobData, ref resultJobData,
                                    AnimParametersTp.LocomotionLayer_Drive_KayakDrive_NameId,
                                    AnimParametersTp.ELocomotionLayer.Drive_KayakDrive,
                                    0.25f, 0, true, true);
                            }
                        }
                    }
                    else
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,  AnimParametersTp.LocomotionLayer_LocomotionEmpty_NameId, AnimParametersTp.ELocomotionLayer.LocomotionEmpty,
                            0f, 0f, true, true);
                    }

                    break;
                case AnimParametersTp.ELocomotionLayer.Drive_KayakDrive:
                    if ((VehicleType)vehicleType == VehicleType.Kayak)
                    {
                        var kayakNoInput = math.abs(playerLocalData.MoveForward)<0.0001f&&
                                           math.abs(playerStateData.Movement8Direction) <0.0001f &&
                                           math.abs(playerLocalData.MoveRight) < 0.0001f;
                        //var kayakDriveOver = playerLocalData.TpClipCollect.NowLocState.Percent >= 0.75f;
                        if (kayakNoInput)//&& kayakDriveOver)
                        {
                            playerLocalData.EnableInterpolation = false;
                            playerLocalData.AlwaysUpdate = true;
                            ToLocomotionLayerState(ref jobData, ref resultJobData,
                                AnimParametersTp.LocomotionLayer_Drive_Idle_NameId,
                                AnimParametersTp.ELocomotionLayer.Drive_Idle,
                                0.25f, 0, true, true);
                        }
                    }
                    else if (isDriver)
                    {
                        playerLocalData.EnableInterpolation = false;
                        playerLocalData.AlwaysUpdate = true;
                        ToLocomotionLayerState(ref jobData, ref resultJobData,
                            AnimParametersTp.LocomotionLayer_Drive_Idle_NameId,
                            AnimParametersTp.ELocomotionLayer.Drive_Idle,
                            0f, 0, true, true);
                    }
                    else
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,  AnimParametersTp.LocomotionLayer_LocomotionEmpty_NameId, AnimParametersTp.ELocomotionLayer.LocomotionEmpty,
                            0f, 0f, true, true);
                    }
                    break;
                case AnimParametersTp.ELocomotionLayer.Drive_Idle:
                    if ((VehicleType)vehicleType == VehicleType.Kayak)
                    {
                        var kayakNoInput = math.abs(playerLocalData.MoveForward)<0.0001f&&
                                           math.abs(playerStateData.Movement8Direction) <0.0001f &&
                                           math.abs(playerLocalData.MoveRight) < 0.0001f;
                        if (!kayakNoInput)
                        {
                            ToLocomotionLayerState(ref jobData, ref resultJobData,
                                AnimParametersTp.LocomotionLayer_Drive_KayakDrive_NameId,
                                AnimParametersTp.ELocomotionLayer.Drive_KayakDrive,
                                0.25f, 0, true, true);
                        }
                    }
                    else if (isDriver)
                    {
                        //开车
                        if ((VehicleType)vehicleType == VehicleType.ModularCar)
                        {
                            var wantSteerAngle = vehicleData.SteerAngle / 35;
                            wantSteerAngle *= 0.3f;
                            var lastAnimSteerAngleTp = playerLocalData.LastAnimSteerAngleTp;
                            var nowAnimSteerAngleTp = Lerp(lastAnimSteerAngleTp, wantSteerAngle,
                                interval * TpAniConstData.LerpSteerAngleSpeed);
                            playerLocalData.LastAnimSteerAngleTp = nowAnimSteerAngleTp;
                            if (math.abs(nowAnimSteerAngleTp) <= 0.1f)
                            {
                            }
                            else
                            {
                                if (nowAnimSteerAngleTp > 0)
                                {
                                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                                        AnimParametersTp.LocomotionLayer_Drive_ModularDriveR_NameId,
                                        AnimParametersTp.ELocomotionLayer.Drive_ModularDriveR,
                                        0f, nowAnimSteerAngleTp, false, true);
                                }
                                else
                                {
                                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                                        AnimParametersTp.LocomotionLayer_Drive_ModularDriveR_NameId,
                                        AnimParametersTp.ELocomotionLayer.Drive_ModularDriveL,
                                        0f, -nowAnimSteerAngleTp, false, true);
                                }
                            }
                        }
                    }
                    else
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,  AnimParametersTp.LocomotionLayer_LocomotionEmpty_NameId, AnimParametersTp.ELocomotionLayer.LocomotionEmpty,
                            0f, 0f, true, true);
                    }
                    break;
                case AnimParametersTp.ELocomotionLayer.Drive_ModularDriveL:
                    if ((VehicleType)vehicleType == VehicleType.Kayak)
                    {
                        if (weaponIsKayak)
                        {
                            ToLocomotionLayerState(ref jobData, ref resultJobData,
                                AnimParametersTp.LocomotionLayer_Drive_KayakRaise_NameId,
                                AnimParametersTp.ELocomotionLayer.Drive_KayakRaise,
                                0f, 0, true, true);
                        }
                    }
                    else if (isDriver)
                    {
                        //开车
                        if ((VehicleType)vehicleType == VehicleType.ModularCar)
                        {
                            var wantSteerAngle = vehicleData.SteerAngle / 35;
                            wantSteerAngle *= 0.3f;
                            var lastAnimSteerAngleTp = playerLocalData.LastAnimSteerAngleTp;
                            var nowAnimSteerAngleTp = Lerp(lastAnimSteerAngleTp, wantSteerAngle,
                                interval * TpAniConstData.LerpSteerAngleSpeed);
                            playerLocalData.LastAnimSteerAngleTp = nowAnimSteerAngleTp;
                            if (math.abs(nowAnimSteerAngleTp) <= 0.1f)
                            {
                                playerLocalData.EnableInterpolation = false;
                                playerLocalData.AlwaysUpdate = true;
                                ToLocomotionLayerState(ref jobData, ref resultJobData,
                                    AnimParametersTp.LocomotionLayer_Drive_Idle_NameId,
                                    AnimParametersTp.ELocomotionLayer.Drive_Idle,
                                    0f, 0, true, true);
                            }
                            else
                            {
                                if (nowAnimSteerAngleTp > 0)
                                {
                                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                                        AnimParametersTp.LocomotionLayer_Drive_ModularDriveR_NameId,
                                        AnimParametersTp.ELocomotionLayer.Drive_ModularDriveR,
                                        0f, nowAnimSteerAngleTp, false, true);
                                }
                                else
                                {
                                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                                        AnimParametersTp.LocomotionLayer_Drive_ModularDriveR_NameId,
                                        AnimParametersTp.ELocomotionLayer.Drive_ModularDriveL,
                                        0f, -nowAnimSteerAngleTp, false, true);
                                }
                            }
                        }
                        else
                        {
                            playerLocalData.EnableInterpolation = false;
                            playerLocalData.AlwaysUpdate = true;
                            ToLocomotionLayerState(ref jobData, ref resultJobData,
                                AnimParametersTp.LocomotionLayer_Drive_Idle_NameId,
                                AnimParametersTp.ELocomotionLayer.Drive_Idle,
                                0f, 0, true, true);
                        }
                    }
                    else
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,  AnimParametersTp.LocomotionLayer_LocomotionEmpty_NameId, AnimParametersTp.ELocomotionLayer.LocomotionEmpty,
                            0f, 0f, true, true);
                    }
                    break;
                case AnimParametersTp.ELocomotionLayer.Drive_ModularDriveR:
                    if ((VehicleType)vehicleType == VehicleType.Kayak)
                    {
                        if (weaponIsKayak)
                        {
                            ToLocomotionLayerState(ref jobData, ref resultJobData,
                                AnimParametersTp.LocomotionLayer_Drive_KayakRaise_NameId,
                                AnimParametersTp.ELocomotionLayer.Drive_KayakRaise,
                                0f, 0, true, true);
                            return;
                        }
                    }
                    else if (isDriver)
                    {
                        //开车
                        if ((VehicleType)vehicleType == VehicleType.ModularCar)
                        {
                            var wantSteerAngle = vehicleData.SteerAngle / 35;
                            wantSteerAngle *= 0.3f;
                            var lastAnimSteerAngleTp = playerLocalData.LastAnimSteerAngleTp;
                            var nowAnimSteerAngleTp = Lerp(lastAnimSteerAngleTp, wantSteerAngle,
                                interval * TpAniConstData.LerpSteerAngleSpeed);
                            playerLocalData.LastAnimSteerAngleTp = nowAnimSteerAngleTp;
                            if (math.abs(nowAnimSteerAngleTp) <= 0.1f)
                            {
                                playerLocalData.EnableInterpolation = false;
                                playerLocalData.AlwaysUpdate = true;
                                ToLocomotionLayerState(ref jobData, ref resultJobData,
                                    AnimParametersTp.LocomotionLayer_Drive_Idle_NameId,
                                    AnimParametersTp.ELocomotionLayer.Drive_Idle,
                                    0f, 0, true, true);
                            }
                            else
                            {
                                if (nowAnimSteerAngleTp > 0)
                                {
                                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                                        AnimParametersTp.LocomotionLayer_Drive_ModularDriveR_NameId,
                                        AnimParametersTp.ELocomotionLayer.Drive_ModularDriveR,
                                        0f, nowAnimSteerAngleTp, false, true);
                                }
                                else
                                {
                                    ToLocomotionLayerState(ref jobData, ref resultJobData,
                                        AnimParametersTp.LocomotionLayer_Drive_ModularDriveR_NameId,
                                        AnimParametersTp.ELocomotionLayer.Drive_ModularDriveL,
                                        0f, -nowAnimSteerAngleTp, false, true);
                                }
                            }
                        }
                        else
                        {
                            playerLocalData.EnableInterpolation = false;
                            playerLocalData.AlwaysUpdate = true;
                            ToLocomotionLayerState(ref jobData, ref resultJobData,
                                AnimParametersTp.LocomotionLayer_Drive_Idle_NameId,
                                AnimParametersTp.ELocomotionLayer.Drive_Idle,
                                0f, 0, true, true);
                        }
                    }
                    else
                    {
                        ToLocomotionLayerState(ref jobData, ref resultJobData,  AnimParametersTp.LocomotionLayer_LocomotionEmpty_NameId, AnimParametersTp.ELocomotionLayer.LocomotionEmpty,
                            0f, 0f, true, true);
                    }
                    break;
            }
        }

        private bool GoToMountState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData, ref bool inState)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var isOnMount = playerStateData.MountableId > 0;
            var inMount = InLocomotionIdle(playerLocalData.ELocomotionLayer) && playerLocalData.ELocomotionLayer == AnimParametersTp.ELocomotionLayer.HipStance_HipStandIdle;
            if (!inMount && isOnMount)
            {
                LogicToStandDetailState(ref jobData, ref resultJobData, ref animParams, constData, 0f, true);
                return true;
            }
            inState = inMount && isOnMount;
            return false;
        }

        private void UpdateMountState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            
        }

        private bool GoToSwimState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData, ref bool inState)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            //是否在游泳状态
            var isSwim = playerStateData.PoseState == PlayerPoseStateEnum.Dive ||
                         playerStateData.PoseState == PlayerPoseStateEnum.Swim;
            var inSwim = InLocomotionSwimIdle(playerLocalData.ELocomotionLayer)||
                         InLocomotionSwimJog(playerLocalData.ELocomotionLayer) ||
                         InLocomotionSwimSprint(playerLocalData.ELocomotionLayer);
            if (!inSwim && isSwim)
            {
                if (playerStateData.Movement8Direction == 0)
                {
                    ToLocomotionLayerState(ref jobData, ref resultJobData, AnimParametersTp.LocomotionLayer_Swim_SwimIdle_NameId,
                        AnimParametersTp.ELocomotionLayer.Swim_SwimIdle, 0f, 0, true,
                        transitFromSnapshotPose:transitFromSnapshotPose);
                    return true;
                }
                else if (playerStateData.MoveSwimState == PlayerMoveSwimStateEnum.SwimSprint)
                {
                    //疾跑
                    ToLocomotionLayerState(ref jobData, ref resultJobData, AnimParametersTp.LocomotionLayer_Swim_Swim_SprintF_NameId,
                        AnimParametersTp.ELocomotionLayer.Swim_Swim_SprintF, 0.15f, 0, true,
                        transitFromSnapshotPose:transitFromSnapshotPose);
                    return true;
                }
                else
                {
                    SwitchSwimLocomotion(ref jobData, ref resultJobData, constData, 0.15f, 0f, true);
                    return true;
                }
            }
            inState = inSwim;
            return false;
        }

        //TODO 速度可能得放在rednerfarame，暂时放在logicframe
        private void UpdateSwimState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var inSwim = InLocomotionSwimIdle(playerLocalData.ELocomotionLayer)||
                         InLocomotionSwimJog(playerLocalData.ELocomotionLayer) ||
                         InLocomotionSwimSprint(playerLocalData.ELocomotionLayer);
            if (!inSwim)
                return;
            UpdateSwimIdleState(ref jobData, ref resultJobData, ref animParams, constData);
            UpdateSwimJogState(ref jobData, ref resultJobData, ref animParams, constData);
            UpdateSwimSprintState(ref jobData, ref resultJobData, ref animParams, constData);
        }
        
        private bool GoToLadderState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData, ref bool inState)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var isLadder = playerStateData.MoveState == PlayerMoveStateEnum.MoveLadder;
            var inLadder = InLocomotionLadder(playerLocalData.ELocomotionLayer);
            if (!inLadder && isLadder)
            {
                UpdateNewLadderState(ref jobData, ref resultJobData,ref animParams, in constData);
                return true;
            }
            inState = inLadder;
            return false;
        }
        
        private void UpdateLadderState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var inLadder = InLocomotionLadder(playerLocalData.ELocomotionLayer);
            if (!inLadder)
                return;
            UpdateNewLadderState(ref jobData, ref resultJobData, ref animParams,in constData);
        }
        
        private bool GoToMantleState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData, ref bool inState)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var isMantle = playerStateData.MoveState == PlayerMoveStateEnum.MoveMantle;
            var inMantle = InLocomotionMantle(playerLocalData.ELocomotionLayer);
            if (!inMantle && isMantle)
            {
                ToRootMotionWarpingState(ref jobData, ref resultJobData);
                return true;
            }
            inState = inMantle;
            return false;
        }
        
        private void UpdateMantleState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var inMantle =  InLocomotionMantle(playerLocalData.ELocomotionLayer);
            if (!inMantle)
                return;
            if (playerStateData.PoseState == PlayerPoseStateEnum.Mantle)
                return;
            if (playerStateData.MoveState == PlayerMoveStateEnum.Fall ||
                playerStateData.MoveState == PlayerMoveStateEnum.Jump)
            {
                playerLocalData.PoseNeedTransition = false;
                ToJumpState(ref jobData, ref resultJobData, 0.2f, 0, true);
            }
            else if (playerStateData.Movement8Direction != 0)
            {
                playerLocalData.PoseNeedTransition = false;
                SwitchHipLocomotion(ref jobData, ref resultJobData,ref animParams, in constData,0.2f, 0, true);
            }
            else
            {
                playerLocalData.PoseNeedTransition = false;
                OverrideLerpStandCrouch(ref jobData, ref resultJobData, ref animParams, constData);
                var transition = 0f;
                if( jobData.PlayerStateData.PoseState == PlayerPoseStateEnum.Crouch)
                {
                    transition = 0.2f;
                }
                LogicToStandDetailState(ref jobData, ref resultJobData,ref animParams, constData, transition, true);
            }
        }
        
        private bool GoToJumpState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData, ref bool inState)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            ref var playerStateData = ref jobData.PlayerStateData;
            var isJump = playerStateData.MoveState == PlayerMoveStateEnum.Fall ||
                         (playerStateData.MoveState == PlayerMoveStateEnum.Jump &&
                          playerStateData.MoveJumpState != PlayerMoveJumpStateEnum.LeaveJump);
            var inJump = InLocomotionAir(playerLocalData.ELocomotionLayer);
            if (!inJump && isJump)
            {
                ToJumpState(ref jobData, ref resultJobData, 0.1f, 0f, true);
                return true;
            }
            inState = inJump;
            return false;
        }
        
        private void UpdateJumpState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var inJump = InLocomotionAir(playerLocalData.ELocomotionLayer);
            if (!inJump)
                return;
            ref var playerStateData = ref jobData.PlayerStateData;
            var inMantle = playerStateData.MoveState == PlayerMoveStateEnum.MoveMantle;
            var inAir = playerStateData.MoveState == PlayerMoveStateEnum.Jump || playerStateData.MoveState == PlayerMoveStateEnum.Fall;
            var specialOverJumpStart = inMantle;
            var transitionTime = 0.15f;

            switch (playerLocalData.ELocomotionLayer)
            {
                case AnimParametersTp.ELocomotionLayer.Jump_JumpStart:
                    //startJump阶段
                    var jumpStartPercent = playerLocalData.TpClipCollect.JumpStart.Percent;
                    if (specialOverJumpStart)
                    {
                        if (inMantle)
                        {
                            ToRootMotionWarpingState(ref jobData, ref resultJobData);
                        }
                    }
                    else if (jumpStartPercent >= 1f)
                    {
                        transitionTime = 0.15f;
                        if (inAir)
                        {
                            if (playerStateData.MoveJumpState == PlayerMoveJumpStateEnum.LeaveJump)
                            {
                                //end
                                UpdateJumpDir(ref animParams, playerStateData);
                                ToLocomotionLayerState(ref jobData, ref resultJobData, AnimParametersTp.LocomotionLayer_Jump_JumpEnd_NameId, AnimParametersTp.ELocomotionLayer.Jump_JumpEnd,
                                    transitionTime, 0, true, needComparse:true);
                                playerLocalData.InJumpEndInput = jobData.PlayerStateData.Movement8Direction != 0;
                            }
                            else
                            {
                                transitionTime = 0f;
                                //跳转到loop
                                if (!jobData.PlayerStateData.Disable2JumpLoop)
                                {
                                    ToLocomotionLayerState(ref jobData, ref resultJobData, AnimParametersTp.LocomotionLayer_Jump_JumpLoop_NameId, AnimParametersTp.ELocomotionLayer.Jump_JumpLoop,
                                        transitionTime, 0, true,  needComparse:true);
                                }
                            }
                        }
                        else
                        {
                            //end
                            UpdateJumpDir(ref animParams, playerStateData);
                            ToLocomotionLayerState(ref jobData, ref resultJobData, AnimParametersTp.LocomotionLayer_Jump_JumpEnd_NameId, AnimParametersTp.ELocomotionLayer.Jump_JumpEnd,
                                transitionTime, 0, true, needComparse:true);
                            playerLocalData.InJumpEndInput = jobData.PlayerStateData.Movement8Direction != 0;
                        }
                    }
                    else
                    {
                        transitionTime = 0.15f;
                        if (inAir)
                        {
                            if (playerStateData.MoveJumpState == PlayerMoveJumpStateEnum.LeaveJump)
                            {
                                //end
                                UpdateJumpDir(ref animParams, playerStateData);
                                ToLocomotionLayerState(ref jobData, ref resultJobData, AnimParametersTp.LocomotionLayer_Jump_JumpEnd_NameId, AnimParametersTp.ELocomotionLayer.Jump_JumpEnd,
                                    transitionTime, 0, true,  needComparse:true);
                                playerLocalData.InJumpEndInput = jobData.PlayerStateData.Movement8Direction != 0;
                            }
                        }
                        else
                        {
                            //end
                            UpdateJumpDir(ref animParams, playerStateData);
                            ToLocomotionLayerState(ref jobData, ref resultJobData, AnimParametersTp.LocomotionLayer_Jump_JumpEnd_NameId, AnimParametersTp.ELocomotionLayer.Jump_JumpEnd,
                                transitionTime, 0, true, needComparse:true);
                            playerLocalData.InJumpEndInput = jobData.PlayerStateData.Movement8Direction != 0;
                        }
                    }
                    break;
                case AnimParametersTp.ELocomotionLayer.Jump_JumpLoop:
                    //loop阶段
                    if (specialOverJumpStart)
                    {
                        if (inMantle)
                        {
                            ToRootMotionWarpingState(ref jobData, ref resultJobData);
                        }
                    }
                    else if (inAir)
                    {
                        if (playerStateData.MoveJumpState == PlayerMoveJumpStateEnum.EnterJump)
                        {
                            transitionTime = 0f;
                            ToLocomotionLayerState(ref jobData, ref resultJobData, AnimParametersTp.LocomotionLayer_Jump_JumpStart_NameId, AnimParametersTp.ELocomotionLayer.Jump_JumpStart,
                                transitionTime, 0, true,  needComparse:true);
                        }
                        else if (playerStateData.MoveJumpState == PlayerMoveJumpStateEnum.LeaveJump)
                        {
                            transitionTime = 0.15f;
                            //end
                            UpdateJumpDir(ref animParams, playerStateData);
                            ToLocomotionLayerState(ref jobData, ref resultJobData, AnimParametersTp.LocomotionLayer_Jump_JumpEnd_NameId, AnimParametersTp.ELocomotionLayer.Jump_JumpEnd,
                                transitionTime, 0, true,  needComparse:true);
                            playerLocalData.InJumpEndInput = jobData.PlayerStateData.Movement8Direction != 0;
                        }
                    }
                    else if (!inAir)
                    {
                        transitionTime = 0.15f;
                        //end
                        UpdateJumpDir(ref animParams, playerStateData);
                        ToLocomotionLayerState(ref jobData, ref resultJobData, AnimParametersTp.LocomotionLayer_Jump_JumpEnd_NameId, AnimParametersTp.ELocomotionLayer.Jump_JumpEnd,
                            transitionTime, 0, true,  needComparse:true);
                        playerLocalData.InJumpEndInput = jobData.PlayerStateData.Movement8Direction != 0;
                    }
                    break;
                case AnimParametersTp.ELocomotionLayer.Jump_JumpEnd:
                    //leaveJump阶段
                    var jumpEndPercent = playerLocalData.TpClipCollect.JumpEnd.Percent;
                    switch (playerStateData.MoveState)
                    {
                        case PlayerMoveStateEnum.MoveIdle:

                            if (playerLocalData.InJumpEndInput)
                            {
                                transitionTime = 0.15f;
                                //进入前有输入的
                                //结束
                                LogicToStandDetailState(ref jobData, ref resultJobData, ref animParams, constData, transitionTime, true);
                            }
                            else
                            {
                                transitionTime = 0.15f;
                                if (jumpEndPercent >= 1)
                                {
                                    LogicToStandDetailState(ref jobData, ref resultJobData, ref animParams, constData, transitionTime, true);
                                }
                            }
                            break;
                        case PlayerMoveStateEnum.Run:
                            if (playerLocalData.InJumpEndInput)
                            {
                                transitionTime = 0.15f;
                                if (jumpEndPercent >= 1)
                                {
                                    SwitchHipLocomotion(ref jobData, ref resultJobData,ref animParams, in constData, transitionTime, 0f, true);
                                }
                            }
                            else
                            {
                                transitionTime = 0.15f;
                                SwitchHipLocomotion(ref jobData, ref resultJobData,ref animParams, in constData,transitionTime, 0f, true);
                            }
                            break;
                        case PlayerMoveStateEnum.Sprint:
                            transitionTime = 0.15f;
                            ToLocomotionLayerState(ref jobData, ref resultJobData, AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_SprintF_NameId,
                                AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_SprintF, transitionTime, 0, true, 
                                transitFromSnapshotPose:transitFromSnapshotPose);
                            break;
                        case PlayerMoveStateEnum.Jump:
                            transitionTime = 0f;
                            if (playerStateData.MoveJumpState == PlayerMoveJumpStateEnum.EnterJump)
                            {
                                ToLocomotionLayerState(ref jobData, ref resultJobData, AnimParametersTp.LocomotionLayer_Jump_JumpStart_NameId, AnimParametersTp.ELocomotionLayer.Jump_JumpStart,
                                    transitionTime, 0, true,  needComparse:true);
                            }
                            break;
                        case PlayerMoveStateEnum.Fall:
                            transitionTime = 0.15f;
                            if (!jobData.PlayerStateData.Disable2JumpLoop)
                            {
                                ToLocomotionLayerState(ref jobData, ref resultJobData, AnimParametersTp.LocomotionLayer_Jump_JumpLoop_NameId, AnimParametersTp.ELocomotionLayer.Jump_JumpLoop,
                                    transitionTime, 0, true,  needComparse:true);
                            }
                            break;
                        case PlayerMoveStateEnum.MoveMantle:
                            ToRootMotionWarpingState(ref jobData, ref resultJobData);
                            break;
                        case PlayerMoveStateEnum.MoveLadder:
                            UpdateNewLadderState(ref jobData, ref resultJobData,ref animParams, in constData);
                            break;
                        case PlayerMoveStateEnum.MoveSwim:
                            transitionTime = 0f;
                            ToLocomotionLayerState(ref jobData, ref resultJobData, AnimParametersTp.LocomotionLayer_Swim_SwimIdle_NameId,
                                AnimParametersTp.ELocomotionLayer.Swim_SwimIdle, transitionTime, 0, true, 
                                transitFromSnapshotPose:transitFromSnapshotPose);
                            break;
                        case PlayerMoveStateEnum.Mountable:
                            transitionTime = 0f;
                            LogicToStandDetailState(ref jobData, ref resultJobData, ref animParams, constData, transitionTime, true);
                            break;
                        case PlayerMoveStateEnum.MoveZipline:
                            transitionTime = 0f;
                            LogicToStandDetailState(ref jobData, ref resultJobData, ref animParams, constData, transitionTime, true);
                            break;
                        default:
                            transitionTime = 0f;
                            if (jumpEndPercent >= 1f)
                            {
                                //结束
                                LogicToStandDetailState(ref jobData, ref resultJobData, ref animParams, constData, transitionTime, true);
                            }
                            break;
                    }
                    break;
            }
        }
        
        private bool GoToJogState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData, ref bool inState)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var isJog = playerStateData.Movement8Direction!=0;
            var inJog = InLocomotionJog(playerLocalData.ELocomotionLayer) || InLocomotionSprint(playerLocalData.ELocomotionLayer);
            if (!inJog && isJog)
            {
                SwitchHipLocomotion(ref jobData, ref resultJobData,ref animParams, in constData, 0.15f, 0f, true);
                return true;
            }
            inState = inJog;
            return false;
        }
        
        //TODO 暂时用logic ，后面看是否改为renderframe
        private void UpdateJogState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var inJog = InLocomotionJog(playerLocalData.ELocomotionLayer);
            if(!inJog)
                return;
            var tpLerpSpeed = new float3(playerLocalData.TpLerpAniSpeedX, 0, playerLocalData.TpLerpAniSpeedZ);
            var lerpSpeedValue = math.length(tpLerpSpeed);
            if (playerStateData.Movement8Direction == 0 && lerpSpeedValue < TpAniConstData.MoveToIdleSpeedThreshold) //new Vector3(entity.SpeedX,0,entity.SpeedZ).magnitude<0.1f)
            {
                LogicToStandDetailState(ref jobData, ref resultJobData, ref animParams, constData, 0.1f, true);
            }
            else if (playerStateData.MoveState == PlayerMoveStateEnum.Sprint)
            {
                //疾跑
                ToLocomotionLayerState(ref jobData, ref resultJobData, AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_SprintF_NameId,
                    AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_SprintF, 0.15f, 0, true, 
                    transitFromSnapshotPose:transitFromSnapshotPose);
            }
            else
            {
                SwitchHipLocomotion(ref jobData, ref resultJobData,ref animParams, in constData,0.1f, 0f, true);
            }
        }
        
        //TODO 暂时用logic ，后面看是否改为renderframe
        private void UpdateSprintState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var inSprint = InLocomotionSprint(playerLocalData.ELocomotionLayer);
            if(!inSprint)
                return;
            var tpLerpSpeed = new float3(playerLocalData.TpLerpAniSpeedX, 0, playerLocalData.TpLerpAniSpeedZ);
            var lerpSpeedValue = math.length(tpLerpSpeed);
            if (playerStateData.Movement8Direction == 0 && lerpSpeedValue < TpAniConstData.MoveToIdleSpeedThreshold) //new Vector3(entity.SpeedX,0,entity.SpeedZ).magnitude<0.1f)
            {
                LogicToStandDetailState(ref jobData, ref resultJobData, ref animParams, constData, 0.15f, true);
            }
            else if (playerStateData.MoveState != PlayerMoveStateEnum.Sprint && playerStateData.Movement8Direction != 0)
            {
                SwitchHipLocomotion(ref jobData, ref resultJobData,ref animParams, in constData,0.1f, 0f, true);
            }
        }
        
        private bool GoToIdleState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var isIdle = playerStateData.Movement8Direction==0;
            var inIdle = InLocomotionIdle(playerLocalData.ELocomotionLayer);
            if (!inIdle && isIdle)
            {
                LogicToStandDetailState(ref jobData, ref resultJobData, ref animParams, constData, 0f, true);
                return true;
            }
            return false;
        }

        private void UpdateIdleState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            var inIdle = InLocomotionIdle(playerLocalData.ELocomotionLayer);
            if (!inIdle)
                return;
            LogicToStandDetailState(ref jobData, ref resultJobData, ref animParams, constData, 0f, true);
        }

        private void UpdateLocomotionLayerLogicFrame_Late(ref TpAnimationJobData jobData,
            ref TpAnimationResultJobData resultJobData, ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            //重置原地转身参数
            ResetTurnInPlaceData(ref jobData, ref resultJobData);
        }

        /// <summary>
        /// 重置原地转身的数据
        /// </summary>
        /// <param name="resultJobData"></param>
        private void ResetTurnInPlaceData(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData)
        {
            ref var aniPlayerLocal = ref resultJobData.PlayerLocalData;
            ref var playerStateData = ref jobData.PlayerStateData;
            if(InLocomotionIdle(aniPlayerLocal.ELocomotionLayer))
                return;
            if (aniPlayerLocal.TurnInPlaceDirection != 0)
            {
                aniPlayerLocal.TipOver = true;
            }
            aniPlayerLocal.TurnInPlaceDirection = 0;
            aniPlayerLocal.AnimTurnInPlaceDeltaAngle = 0;
            aniPlayerLocal.AnimRotation = playerStateData.AimYaw;
            aniPlayerLocal.LastAimYawLocal = DeltaAngle(aniPlayerLocal.AnimRotation, playerStateData.AimYaw);
        }
        
        private void UpdateLocomotionLayerLogicFrame_ToRender(ref TpAnimationJobData jobData,
            ref TpAnimationResultJobData resultJobData, ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            switch (playerLocalData.ELocomotionLayer)
            {
                case AnimParametersTp.ELocomotionLayer.Injured_ToInjured:
                case AnimParametersTp.ELocomotionLayer.InCap_ToLieDown:
                case AnimParametersTp.ELocomotionLayer.Zipline_ZiplineStandIn:
                case AnimParametersTp.ELocomotionLayer.Zipline_ZiplineSwitch:
                case AnimParametersTp.ELocomotionLayer.Zipline_ZiplineStandOut:
                case AnimParametersTp.ELocomotionLayer.Drive_KayakRaise:
                case AnimParametersTp.ELocomotionLayer.Jump_JumpStart:
                case AnimParametersTp.ELocomotionLayer.Jump_JumpEnd:
                    playerLocalData.SetWantLogicToRenderFrame(true);
                    break;
                default:
                    playerLocalData.SetWantLogicToRenderFrame(false);
                    break;
            }
        }
        
    }
}