﻿using Effect;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.Character.State.Event;
using WizardGames.Soc.Common.Unity.Construction;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.ObjPool;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocClient.ClientItem;
using WizardGames.Soc.SocClient.GpuInstance.Utils;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Plant;
using WizardGames.Soc.SocClient.Player.HeldItem;
using WizardGames.Soc.SocClient.Systems;
using WizardGames.Soc.SocClient.Ui;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.Common.Component
{
    //此主要功能由WangSongtao实现，@xql在ECS改造中移植
    public partial class PlantBoxComponent : StorageComponent, IUiBtnHandler,ISkinGoHandler
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(PlantBoxComponent));
        static PlantBoxComponent()
        {
            PropertyStaticCallback.SubscribePropertyChange<int>(CLASS_HASH, PropertyIds.WATER, WaterUpdated);
        }

        private bool isWatering = false;
        private PlantBoxData curPlantBox;
        private BaseItemNode waterHolder;
        private PlantBoxCtrl plantBoxCtrl;
        private UIPlantWaterEffect effect;
        private readonly GpuInstToggleGroup instanceToggleGroup = new();
        private Transform mainTransform;
        private long updateWaterTimerId;

        public PartEntity ParentPartEntity => ParentEntity as PartEntity;
        
        private EffectItemHandle<EffectItem> splashWaterHandle;
        private EffectItemHandle<EffectItem> fallWaterHandle;

        private ConstructionSkinNodeConfig constructionSkinNodeConfig;

        public override void PostInit()
        {
            base.PostInit();

            Mc.Msg.FireMsgAtOnce(EventDefine.SubscribeAck, ParentId, true);
            OnLodChange();
        }

        private static void WaterUpdated(CustomTypeBase comp, int oldVal, int newVal)
        {
            var self = comp as PlantBoxComponent;
            if (self.updateWaterTimerId > 0) return;
            self.updateWaterTimerId = self.AddTimerOnce(1, (_) =>
            {
                self.updateWaterTimerId = 0;
                var plantBoxData = Mc.Plant.GetPlantBoxData(self.ParentId);
                if (plantBoxData != null)
                {
                    plantBoxData.UpdateWater(self.Water);
                }
            });

            Mc.Msg.FireMsgAtOnce(EventDefine.SubscribeAck, self.ParentId, true);

            self.OnLodChange(self.ParentPartEntity.LodLevel);
        }

        public override void OnLodChange(int level)
        {
            base.OnLodChange(level);

            if (level == CustomLod.LOD_FULL || level == CustomLod.LOD_10M)
            {
                OnFullSubscribed();
            }
        }

        public override void OnFullSubscribed()
        {
            base.OnFullSubscribed();
            var plantsNode = Root.GetChildNode(StorageContainerConst.PlantBoxSlot) as ItemContainerNode;
            if (plantsNode != null)
            {
                plantsNode.SubscribeAnyUpdateCallback(() =>
                {
                    var plantBoxData = Mc.Plant.GetPlantBoxData(ParentId);
                    if (plantBoxData != null)
                    {
                        plantBoxData.UpdatePlants(this);
                    }
                });
            }
        }

        public override void OnGoLoaded()
        {
            var obj = ParentPartEntity?.MainGo;
            mainTransform = obj.transform;
            plantBoxCtrl = mainTransform.GetComponent<PlantBoxCtrl>();

            Mc.Msg.AddListener<long, int>(EventDefine.WaterSuccess, OnWaterSuccess);

            InitInstanceToggleGroup();

            RefreshPlants(ParentId);
        }

        public override void Cleanup()
        {
            Mc.Msg.RemoveListener<long, int>(EventDefine.WaterSuccess, OnWaterSuccess);
            base.Cleanup();
        }
        public void OnSkinGoLoaded(GameObject skinGo)
        {
            constructionSkinNodeConfig = skinGo.GetComponent<ConstructionSkinNodeConfig>();
        }

        public void OnSkinGoRemove()
        {
        }

        public bool HasAnyPlant()
        {
            if (curPlantBox != null && curPlantBox.HasAnyPlant())
            {
                return true;
            }
            return false;
        }

        private void RefreshPlants(long entityId)
        {
            if (ParentId == entityId)
            {
                curPlantBox = Mc.Plant.GetPlantBoxData(entityId);
                plantBoxCtrl?.RefreshPlants(curPlantBox);
                instanceToggleGroup.Update();
            }
        }
        
        private void InitInstanceToggleGroup()
        {
            instanceToggleGroup.Clear();
            var container = ParentPartEntity.GoComponent.GpuInstContainer;
            if (container != null)
            {
                var tmpAddList = Pool.GetColl<List<GameObject>>();
                tmpAddList.Clear();
                foreach (var mesh in plantBoxCtrl.hightlights)
                {
                    tmpAddList.Add(mesh.gameObject);
                }
                instanceToggleGroup.InitToggle(tmpAddList, container, RenderType.Construction);
                Pool.ReleaseColl(tmpAddList);
            }
        }

        private void OnWaterSuccess(long id, int waterVal)
        {
            if (id != ParentId)
            {
                return;
            }

            Mc.Audio.Add2DAudio(PlantEffectEmitter.WaterAudioKey);
            
            //生成全屏弹窗式特效
            if (effect != null)
            {
                effect.CloseWindow();
                effect = null;
            }
            if (!Mc.Ui.IsWindowOpen("UiPlantMain"))
            {
                effect = Mc.Ui.OpenWindowT<UIPlantWaterEffect>("UIPlantWaterEffect");
            }
            
            if (constructionSkinNodeConfig != null)
            {
                ReleaseEffectGo();
                //生成水花特效
                var splashFxCfg = Mc.Tables.TbPlantEffect.GetOrDefault(PlantEffectType.WaterBottle_SplashWater);
                var waterFallCfg = Mc.Tables.TbPlantEffect.GetOrDefault(PlantEffectType.WaterBottle_WaterFall);
                splashWaterHandle = Mc.Effect.PlayEffect<EffectItem>(splashFxCfg.FxId, -1, constructionSkinNodeConfig.EffectSocketRootIdle.transform);
                splashWaterHandle.AddOrCallLoadCompletedCallback((bool success, EffectItem effectItem, object o) =>
                {
                    if (success)
                    {
                        effectItem.EffectGo.SetActive(true);
                    }
                });
                fallWaterHandle = Mc.Effect.PlayEffect<EffectItem>(waterFallCfg.FxId, -1, constructionSkinNodeConfig.EffectSocketRootIdle.transform);
                fallWaterHandle.AddOrCallLoadCompletedCallback((bool success, EffectItem effectItem, object o) =>
                {
                    if (success)
                    {
                        effectItem.EffectGo.SetActive(true);
                    }
                });
                Mc.TimerWheel.AddTimerOnce((int)Mc.Tables.TbPlantConstConfig.WaterEffTime * 1000, ((id, data, delete) => { ReleaseEffectGo(); }), "PlantModuleClient.Init");
            }
        }

        private void ReleaseEffectGo()
        {
            Mc.Effect.Release(splashWaterHandle,true);
            Mc.Effect.Release(fallWaterHandle,true);
        }

        public void GetUiBtnList(List<int> retList)
        {
            retList.Add(MgrPlant.PLANT_MGR_ID);

            var plantBoxData = Mc.Plant.GetPlantBoxData(ParentId);
            if (plantBoxData != null)
            {
                if (plantBoxData.CanSeed())
                {
                    retList.Add(MgrPlant.SEED_ID);
                }

                if (plantBoxData.CanManure())
                {
                    retList.Add(MgrPlant.MANURE_ID);
                }

                if (plantBoxData.CanWater())
                {
                    retList.Add(MgrPlant.WATER_ID);
                }

                if (plantBoxData.CanHarvest())
                {
                    retList.Add(MgrPlant.HARVEST_ID);
                }
            }
        }

        public void GetUiBtnOverlayList(List<int> retList)
        {
        }


        public void GetUiBtnListOutOfPerm(List<int> retList)
        {

        }

        public void OnUiBtnClicked(int btnId)
        {
            // var data = Mc.Tables.TbInteraction.GetOrDefault(btnId);

            switch (btnId)
            {
                case MgrPlant.SEED_ID:
                    // UiPlantMain.OpenWindow(ParentId, win => { win.InitPlantBox(ParentId, data.Type); });
                    break;
                case MgrPlant.MANURE_ID:
                    // UiPlantMain.OpenWindow(ParentId, win => { win.InitPlantBox(ParentId, data.Type); });
                    break;
                case MgrPlant.WATER_ID:
                    isWatering = true;
                    // Mc.MyPlayer.InteractiveTargetId = curPlantBox.EntityId;
                    // var curHiddenUse = Mc.MyPlayer.MyEntityLocal.CustomItemHiddenUse as IItemEntity;
                    // if (waterHolder != null && curHiddenUse != null && curHiddenUse.ItemUid == waterHolder.Id)
                    // {
                    //     ClientUseBagItemSystem.SetUseCmd(curHiddenUse as IHeldItemEntity, BagUsePurposes.PlantBoxAddWater);
                    //     // Mc.MyPlayer.MyEntityLocal.bagUseAction = AddWater2PlantBox;
                    //     Mc.MyPlayer.MyEntityLocal.bagUsePurposes = BagUsePurposes.PlantBoxAddWater;
                    //    
                    //     return;
                    // }
                    //
                    //在快捷栏种找水瓶或水桶
                    waterHolder =
                        Mc.CollectionItem.InventoryCom.Root.GetNodeById(Mc.MyPlayer.MyEntityLocal.CurrentWeaponId) as
                            WaterBottleItemNode;
                    if (waterHolder == null)
                    {//手上没拿 从快捷栏找
                        MgrCollectionItem.IterOneContainer(Mc.CollectionItem.InventoryCom.ContainerBeltNode, (node) =>
                        {
                            var waterBottle = node as WaterBottleItemNode;
                            if (waterBottle != null && waterBottle.GetWaterAmount() > 0)
                            {
                                waterHolder = waterBottle;
                                return false;
                            }

                            return true;
                        });
                    }

                    //没找到的话，继续在背包里找
                    if (waterHolder == null)
                    {
                        var player = Mc.MyPlayer.MyEntityServer;
                        var rootClient = player.RootNode;
                        var containerRoot = rootClient.GetNodeByPath(NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Main) as ItemContainerNode;
                        foreach (var pair in containerRoot)
                        {
                            var waterBottleItemNode = pair.Value as WaterBottleItemNode;
                            if (waterBottleItemNode == null)
                            {
                                continue;
                            }
                    
                            if (waterBottleItemNode.GetWaterAmount() > 0)
                            {
                                waterHolder = waterBottleItemNode;
                                break;
                            }
                        }
                    }
                    
                    //没找到的话，显示tips
                    if (waterHolder == null)
                    {
                        Mc.MsgTips.ShowRealtimeWeakTip(23002);
                        return;
                    }
                    Mc.MyPlayer.TryBagUseHeldItem(waterHolder, curPlantBox.EntityId, BagUsePurposes.PlantBoxAddWater);
                    // if (waterHolder != null)
                    //     UseWaterHolder();
                    break;
                case MgrPlant.HARVEST_ID:
                    if (curPlantBox != null)
                    {
                        curPlantBox.RequestHarvest(-1);
                    }
                    break;
                default:
                    break;
            }
        }

        // public bool UseWaterHolder()
        // {
        //     //Tips!!! 本次修改只是转移逻辑代码位置，blame逻辑内容时请咨询wjx
        //     if (null == waterHolder) return false;
        //     // 快捷栏直接使用，不发rpc
        //     if (waterHolder.ParentNode.Id == NodeConst.BeltItemContainerNodeId)
        //     {
        //         var currentEntity = Mc.MyPlayer.MyEntityLocal.GetItemEntity((int)waterHolder.Index);
        //         if (currentEntity != null)
        //         {
        //             if (currentEntity.EntityId == Mc.MyPlayer.MyEntityLocal.CurrentWeaponId)
        //             {
        //                 ClientUseBagItemSystem.SetUseCmd(currentEntity as IHeldItemEntity, BagUsePurposes.PlantBoxAddWater);
        //             }
        //             else
        //             {
        //                 Mc.MyPlayer.MyEntityLocal.DrawComponent.Equip((HoldItemIndex)waterHolder.Index);
        //                 Mc.MyPlayer.MyEntityLocal.bagUseEntityId = currentEntity.EntityId;
        //                 Mc.MyPlayer.MyEntityLocal.bagUseStep = BagUseStep.Switching;
        //             }
        //         }
        //     }
        //     else
        //     {
        //         var entityId = waterHolder.GetBelongComponent()?.ParentEntity.EntityId ?? 0;
        //         if (entityId > 0) Mc.CollectionItem.RemoteCallUseItem(entityId, waterHolder.NodeId, 1);
        //     }
        //     // SwitchHoldItem会重置背包使用信息，所以设置背包信息要放在SwitchHoldItem之后
        //     Mc.MyPlayer.MyEntityLocal.bagUseAction = AddWater2PlantBox;
        //     Mc.MyPlayer.MyEntityLocal.bagUsePurposes = BagUsePurposes.PlantBoxAddWater;
        //     return true;
        // }
    }
}