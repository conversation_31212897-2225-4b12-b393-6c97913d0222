using FairyGUI;
using System;
using System.Collections.Generic;
using WizardGames.Soc.Common.Data.mall;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Ui.Binder.LobbyMall;

namespace WizardGames.Soc.SocClient.Ui
{
    public class UiMall : WindowComBase, IJumpToUIWindow
    {
        private Dictionary<MallPageType, Func<MallPageBase>> pageRegister = new()
        {
            { MallPageType.HotSale,     ()=> new MallPageHotSale()},
            { MallPageType.Gacha,       ()=> new MallPageGacha()},
            { MallPageType.Bundle,      ()=> new MallPageBundles()},
            { MallPageType.ShopItems,   ()=> new MallPageShopItems()},
        };

        public ComMallBg ComBg { get; private set; }
        private Controller ctrlPage;
        private ComCommonNavBar comNavBar;
        private Dictionary<MallPageType, ComLazyLoader> pageLazyLoaders = new();
        private Dictionary<MallPageType, MallPageBase> pages = new();
        private Dictionary<int, string> pageBgs = new();
        private Dictionary<int, List<OBJMallNavTab>> tabGroupRec = new();
        private Dictionary<MallShelfType, int> shelfItemCount = new();
        private MallPageType curPageType = MallPageType.None;
        private int curNavID = -1;
        private int autoJumpId = -1;

        public bool JumpFromHotSale = false;
        private ComTopBar topBar;
        private List<NavBarData> navBarDataList = new();
        List<int> mallGachaList = new();

        protected override void OnInit()
        {
            base.OnInit();
            var panelBinder = new UiMallBinder(ContentPane);
            var rootBinder = panelBinder.Root;
            ComBg = new ComMallBg(panelBinder.Bg);

            topBar = rootBinder.TopBar as ComTopBar;
            topBar.InitCurrencyList(uiName);
            topBar.SetBackButtonClicked(OnClickClose);

            var contentBinder = rootBinder.Content;
            ctrlPage = contentBinder.CtrlPage;
            pageLazyLoaders[MallPageType.HotSale] = contentBinder.HotPage as ComLazyLoader;
            pageLazyLoaders[MallPageType.Gacha] = contentBinder.LucklyDrawPage as ComLazyLoader;
            pageLazyLoaders[MallPageType.Bundle] = contentBinder.BundlePage as ComLazyLoader;
            pageLazyLoaders[MallPageType.ShopItems] = contentBinder.ShopPage as ComLazyLoader;

            var list = Mc.Gacha.GetAllActiveGachas();
            mallGachaList.Clear();
            foreach (var info in list)
            {
                mallGachaList.Add(info.RaffleID);
            }
            comNavBar = new ComCommonNavBar();
            comNavBar.Init(rootBinder.NavBar, true);
            comNavBar.LinkTopBar(topBar, true);
            //comNavBar.IsCallbackEveryClick = true;
            comNavBar.SetClickBack(OnClickTab);
            comNavBar.IsHideFirstTabRedDot = true;
            bool autoChooseTab = !HasCustomFlag(LayerWinFlag.SKIP_DEFAULT_UI_SHOW);
            RefreshNavTabs(autoChooseTab);
        }

        private NavBarData AppendBavBarData(OBJMallNavTab tabCfg, int parentID = -1)
        {
            var tabData = new NavBarData(tabCfg.Name, tabCfg.ID, tabCfg.Icon, ENavIconType.NoIcon);
            // tabData.RedDotType = RedDotType.MallGacha;    
            comNavBar.AddTabData(tabData, parentID);
            navBarDataList.Add(tabData);
            comNavBar.SetRedType(ENavTabRedDotType.Dot);
            tabData.IsShowRedDot = IsShowRedDot(tabCfg.ID);

            if (!string.IsNullOrEmpty(tabCfg.Bg)) pageBgs[tabCfg.ID] = tabCfg.Bg;
            return tabData;
        }

        public bool IsShowRedDot(int pageId)
        {
            if (pageId == 2)
            {
                return IsGachaShowRedDot();
            }
            return false;
        }
        private bool IsGachaShowRedDot()
        {
            foreach (var gachaId in mallGachaList)
            {
                if (!Mc.Gacha.IsGachaPoolRead(gachaId)) return true;
            }
            return false;
        }
         /// <summary>
         /// 刷新页签红点
         /// </summary>
        public void RefreshRedDot()
        {
            if (navBarDataList == null || navBarDataList.Count == 0) return;
            for (int i = 0; i < navBarDataList.Count; i++)
            {
                navBarDataList[i].IsShowRedDot = IsShowRedDot(navBarDataList[i].TabId);
            }
            comNavBar.Refresh();
        }

        /// <summary>
        /// 刷新界面Tab
        /// </summary>
        /// <param name="autoChoose">是否执行tab自动选中的行为</param>
        /// <param name="curChooseFirst">优先自动选中当前的tab</param>
        private void RefreshNavTabs(bool autoChoose = true, bool curChooseFirst = false)
        {
            shelfItemCount.Clear();
            // 记录tab之间的父子关系
            tabGroupRec.Clear();
            foreach (var tab in Mc.Tables.TBMallNavTab.DataList)
            {
                // 这里只遍历所有的子tab
                if (tab.Parent <= 0) continue;
                if (!tabGroupRec.TryGetValue(tab.Parent, out var subs))
                {
                    subs = new List<OBJMallNavTab>();
                    tabGroupRec[tab.Parent] = subs;
                }
                var shelfType = tab.ShelfType;
                if (!tab.Enable || shelfType == MallShelfType.None) continue;
                // 之前的需求是一个子页签如果没有可售物品就隐藏，但是会存在第一次进商城界面没有请求商品数据时页签还在的情况
                // 当前需求修改为即使没有可售物品，页签也存在，只不过没有商品，后续会补充一个空状态 @cs wtz
                //if (!shelfItemCount.TryGetValue(shelfType, out int itemNum))
                //{
                //    itemNum = Mc.Mall.GetAllActiveMallItems(shelfType).Count;
                //    shelfItemCount[shelfType] = itemNum;
                //}
                //if (itemNum <= 0) continue;
                subs.Add(tab);
            }
            OBJMallNavTab firstTab = null;
            OBJMallNavTab curChooseTab = null;
            pageBgs.Clear();
            comNavBar.ClearTabData();
            navBarDataList.Clear();
            // 因为需要按调表顺序增加tab，因此需要再过一遍表
            foreach (var tab in Mc.Tables.TBMallNavTab.DataList)
            {
                // 上面已经收集过有效的子页签，这里不再便遍历子页签
                if (!tab.Enable || tab.Parent > 0) continue;
                bool hasSubTabs = tabGroupRec.TryGetValue(tab.ID, out var subs);
                // 如果一个tab是组的父tab（即有任意tab的parent指向此tab），则在没有有效子tab的情况下，这个tab也不显示
                // 如果父tab填了PageType，会导致父子刷两遍，因此是要求不填的，这里对父tab填了的情况也跳过
                if (hasSubTabs && (subs.Count <= 0 || tab.PageType != MallPageType.None)) continue;
                // 如果没有子tab，则需要其pageType有效
                if (!hasSubTabs && tab.PageType == MallPageType.None) continue;
                // 添加当前tab
                AppendBavBarData(tab);

                if (!hasSubTabs)
                {
                    if (null == firstTab) firstTab = tab;
                    if (curNavID == tab.ID) curChooseTab = tab;
                }
                // 添加子tab
                if (hasSubTabs)
                {
                    foreach (var subTab in subs)
                    {
                        AppendBavBarData(subTab, tab.ID);
                        if (null == firstTab) firstTab = subTab;
                        if (curNavID == subTab.ID) curChooseTab = subTab;
                    }
                }
            }
            if (!autoChoose) return;
            if (curChooseFirst && null != curChooseTab) ChooseNavByCfg(curChooseTab);
            else if (null != firstTab) ChooseNavByCfg(firstTab);
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            topBar.OnEnable();
            RefreshRedDot();
        }
        protected override void OnDisable()
        {
            base.OnDisable();
            topBar.OnDisable();
        }
        public override void OnShowByLayerStack()
        {
            base.OnShowByLayerStack();
            if (pages.TryGetValue(curPageType, out var tarPage))
            {
                tarPage.OnShowByLayerStack();
            }
        }

        public override void OnHideByLayerStack(string coverUiName)
        {
            base.OnHideByLayerStack(coverUiName);
            // 从界面堆栈中回来，刷新一下当前页面
            if (pages.TryGetValue(curPageType, out var tarPage))
            {
                tarPage.OnHideByLayerStack(coverUiName);
            }
        }

        private void ChooseNavByCfg(OBJMallNavTab navCfg)
        {
            if (null == navCfg || !navCfg.Enable) return;
            if (navCfg.Parent <= 0) comNavBar.ScrollAndSelect(navCfg.ID);
            else comNavBar.ScrollAndSelect(navCfg.Parent, navCfg.ID);
            RefreshRedDot();
        }

        private void OnClickTab(NavBarData tab, bool isBtnClick)
        {
            var tabCfg = Mc.Tables.TBMallNavTab.GetOrDefault(tab.TabId);
            if (null == tabCfg || !tabCfg.Enable) return;
            var pageType = tabCfg.PageType;
            if (pageType == MallPageType.None) return;
            int jumpId = -1;
            if (!isBtnClick) jumpId = autoJumpId;
            autoJumpId = -1;
            JumpToInternal(tabCfg, jumpId);
            JumpFromHotSale = false;
            RefreshRedDot();
        }

        /// <summary>
        /// 跳转到指定的页面
        /// </summary>
        private void JumpToInternal(OBJMallNavTab tabCfg, int jumpId = -1)
        {
            if (null == tabCfg || !tabCfg.Enable) return;
            if (curNavID != tabCfg.ID)
            {
                bool hasNextBg = pageBgs.TryGetValue(tabCfg.ID, out string bg);
                ComBg.ClearAllContent(!hasNextBg);
                if (hasNextBg) ComBg.SetKV(bg);
            }
            curNavID = tabCfg.ID;

            var tarPageType = tabCfg.PageType;
            bool hasTargetPage = pages.TryGetValue(tarPageType, out var tarPage);
            // 刷新当前界面
            if (curPageType == tarPageType && hasTargetPage)
            {
                tarPage.OnRefresh(jumpId, tabCfg);
                return;
            }
            // 要切换下个界面之前，先检查一下目标界面的合法性，如果不合法，就不切换了
            if (!pageLazyLoaders.TryGetValue(tarPageType, out var tarLoader) || !pageRegister.ContainsKey(tarPageType))
            {
                return;
            }
            int pageIndex = (int)tarPageType - 1;
            if (pageIndex < 0 || pageIndex >= ctrlPage.pageCount)
            {
                return;
            }
            // 隐藏上一个界面
            if (pages.TryGetValue(curPageType, out var prePage))
            {
                prePage.Hide();
            }
            // 显示下一个界面
            curPageType = tarPageType;
            ctrlPage.SetSelectedIndex(pageIndex);
            if (!hasTargetPage)
            {
                tarPage = pageRegister[tarPageType].Invoke().BindLoader(this, tarLoader);
                pages[tarPageType] = tarPage;
            }
            tarPage.Show(jumpId, tabCfg);
        }

        public void JumpTo(int mallNavID, int jumpId = -1)
        {
            var navTab = Mc.Tables.TBMallNavTab.GetOrDefault(mallNavID);
            if (null == navTab || navTab.PageType == MallPageType.None || !navTab.Enable) return;
            autoJumpId = jumpId;
            ChooseNavByCfg(navTab);
        }

        public void JumpToUIWindow(JumpData jumpData)
        {
            if (null == jumpData) return;
            JumpTo(jumpData.firstTabId, jumpData.selectNodeId);
        }

        private void OnClickClose(EventContext ctx)
        {
            //Mc.Ui.PlaySocAudio("UI_Click_02");
            RemoveSelf();
        }

        public override void OnDestroy()
        {
            base.OnDestroy();
            ComBg.ClearAllContent();
            if (pages.TryGetValue(curPageType, out var page)) page.Hide();
            curPageType = MallPageType.None;
        }
    }
}
