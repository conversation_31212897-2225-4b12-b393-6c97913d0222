using Sirenix.Utilities;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using Unity.Collections;
using UnityEngine;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Algorithm;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.Profile;
using WizardGames.Soc.Common.Unity.ObjPool;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocClient.GoLoader;
using WizardGames.Soc.SocClient.Manager;
using Mathf = UnityEngine.Mathf;
using Vector2 = UnityEngine.Vector2;

public class DelayedInstConf
{
    //默认一帧最多4ms实例化时间, 重连时最多20s
    public const float InGameDuration_Ms = 4.0f;
    public const float ReconnectDuration_Ms = 20.0f;

    public const int MaxRefreshDistance = 300;
    public const int CellEntityMaxCount = 100;
    public const int CellCapcity = 200;
    public const int CellSize = 50;
    public const int AfterLoadPrefabCallbackMaxCount = 512;
    
    //DelayedInstantiateCell容器定义
    public const int MaxEntityDicCount = 1400;
    public const int MaxEmbeddedEntityDicCount = 20;
    public const int MaxInstantiatedEntityCount = 600;
    
    //DelayedInstantiate内容易定义
    public const int NearbyListMaxCount = 144;
    public const int ToRemoveEntityListMax = 250;
    public const int Entity2CellIdxMax = 4000;
    public const int DelayedRemoveEntityListMax = 250;
    public const int SortedCellInCellResultMax = 800;

}

public enum E_EntityInstState
{
    EIS_Waiting = 0,    //等待实例化
    EIS_PfbLoading = 1, //申请prefab资源加载中
    EIS_Ready = 2,      //prefab资源已经完成
    EIS_Finished = 3,   //实例化已经完成
}

//延迟实例化
// 1. 根据玩家位置生成grid与其周围cell
// 2. 输入entity, 根据位置放入cell
// 3. 根据距离与视野优先级, 对cell排序
// 4. cell内实例根据excel配置进行排序
// 5. TODO 检查资源是否加载完成, 加载完成后实例化
// 6. 按照固定阀值时间实例化, 超过时间延迟到后续帧
public class DelayedInstantiateComp
{
    public static SocLogger Logger = LogHelper.GetLogger(typeof(DelayedInstantiateComp));

    public bool IsInit = false;
    public bool IsValid = false;

    //====================计算玩家位置与周围Grid得cell更新====================
    public int LastPlayerIdx = 0;
    public Vector2i PlayerGridIdx = new Vector2i();
    public Recti NearbyRect = new Recti();
    public SignifianceGrid<DelayedInstantiateCell> Grid = null;
    public NativeParallelHashSet<int> LastNearbyList;
    public NativeParallelHashSet<int> CurNearbyList;
    public NativeParallelHashMap<long, int> Entity2CellIdx; 
    //key是entity id, value是cell idx, 方便embeded entity更新
    public NativeParallelHashSet<long> DelayedRemoveEntityList; // 延迟删除的entity列表

    //====================计算cell优先级====================
    //通过excel配置, key是类型, value是优先级分数
    public PriorityQueue<int> SortedCellResult = new PriorityQueue<int>(DelayedInstConf.NearbyListMaxCount);
    public PriorityQueue<int> TempSortedCellResult = new PriorityQueue<int>(DelayedInstConf.NearbyListMaxCount);
    public PriorityQueue<long> SortedEntityInCellResult = new PriorityQueue<long>(DelayedInstConf.SortedCellInCellResultMax);

    //记录已经申请加载prefab的资源
    public Stopwatch Timer = new Stopwatch();
    public float CurDuration = 0.0f;
    public float CurTimeLeft = 0.0f;
    public float GameDuration = DelayedInstConf.InGameDuration_Ms;
    public int NumInstantiateEntity = 0;
    public int NumInstantiateEmbeddedEntity = 0;
    public int NumInstantiatedEntity = 0;
    public int NumInstantiatedEmbeddedEntity = 0;

    public void Init()
    {
        Pool.Init(new PoolConfig<DelayedInstantiateCell>(maxSize: DelayedInstConf.CellCapcity
            , capacity: DelayedInstConf.CellEntityMaxCount
            , createFunc: () => new DelayedInstantiateCell()
            , actionOnRelease: cell => cell.Clear()));
        
        SetCurrentDuration(GameDuration);
        IsInit = true;
        IsValid = true;
        
        LastNearbyList = new NativeParallelHashSet<int>(DelayedInstConf.NearbyListMaxCount, Allocator.Persistent);
        CurNearbyList = new NativeParallelHashSet<int>(DelayedInstConf.NearbyListMaxCount, Allocator.Persistent);
        Entity2CellIdx = new NativeParallelHashMap<long, int>(DelayedInstConf.Entity2CellIdxMax, Allocator.Persistent);
        DelayedRemoveEntityList = new NativeParallelHashSet<long>(DelayedInstConf.DelayedRemoveEntityListMax, Allocator.Persistent);
    }

    public void Clear()
    {
        IsInit = false;
        IsValid = false;
        SetCurrentDuration(DelayedInstConf.ReconnectDuration_Ms);
        Timer.Stop();

        SortedEntityInCellResult.Clear();
        TempSortedCellResult.Clear();
        SortedCellResult.Clear();
        if (DelayedRemoveEntityList.IsCreated)
            DelayedRemoveEntityList.Dispose();
        if (Entity2CellIdx.IsCreated)
            Entity2CellIdx.Dispose();
        if (CurNearbyList.IsCreated)
            CurNearbyList.Dispose();
        if (LastNearbyList.IsCreated)
            LastNearbyList.Dispose();

        if (Grid != null)
        {
            // 在Pool的actionOnRelease统一调用Cell的Clear
            Grid.Clear();
            Grid = null;
        }
        // 确保在Grid清理之后销毁Pool
        Pool.Clear<DelayedInstantiateCell>();
        
        DelayedInstantiateStats.Reset();
    }

    public void SetGameDuration(float duration)
    {
        GameDuration = duration;
        if (IsInit)
        {
            CurDuration = duration;
        }
    }

    public void SetCurrentDuration(float duration)
    {
        CurDuration = duration;
        CurTimeLeft = duration; // 当前没有剩余时间，用于判断是否还有等待异步实例化的Entity
#if ENABLE_DELAYED_DESTROY_OBJECT_DURATION
        // 设置每帧延迟销毁对象的默认时长
        UnityEngine.Object.delayedDestroyDefaultDurationMs = CurDuration;
#endif
    }

    public void Update()
    {
        //0. 没初始化就不更新了
        if (IsInit == false)
            return;

        if (Grid == null)
        {
            IsValid = TryInitGrid(Mc.Terrain.TerrainRect
                                , Mc.Terrain.TerrainSize
                                , DelayedInstConf.CellSize
                                , out Grid);
        }

        if (IsValid == false)
            return;


        CurTimeLeft = 0.0f;
        Timer.Restart();

        //1. 立即删除entity
        if (!RemoveEntitiesImmediate(Timer, CurDuration, ref CurTimeLeft))
        {
#if ENABLE_DELAYED_DESTROY_OBJECT_DURATION
            UnityEngine.Object.delayedDestroyDurationMs = 0.0f;
#endif
            return;
        }

        //2. 调用Prefab加载完成后处理
        if (!GoPool.LoadTaskMgr.UpdateDelayedTasks(Timer, CurDuration, ref CurTimeLeft))
        {
#if ENABLE_DELAYED_DESTROY_OBJECT_DURATION
            UnityEngine.Object.delayedDestroyDurationMs = 0.0f;
#endif
            return;
        }

        //3. 更新玩家位置
        int player_idx = 0;
        Vector2 player_pos = new Vector2(Mc.MyPlayer.MyEntityLocal.PosX, Mc.MyPlayer.MyEntityLocal.PosZ);
        Grid.CalcGridCoords(player_pos.x, player_pos.y, ref player_idx, ref PlayerGridIdx);
        bool is_grid_change = player_idx != LastPlayerIdx;

        //4. 根据玩家grid坐标更新临近cell
        if (is_grid_change)
        {
            //3.1 计算出NearbyList
            Grid.CalcNearbyCellRect(player_idx
                                    , DelayedInstConf.MaxRefreshDistance
                                    , ref NearbyRect);

            CurNearbyList.Clear();
            for (int i = NearbyRect.X; i < NearbyRect.xMax; ++i)
                for (int j = NearbyRect.Y; j < NearbyRect.yMax; ++j)
                {
                    int idx = j * Grid.CellCount + i;
                    CurNearbyList.Add(idx);
                }

            
            //3.2 使用LastNearByList与CurNearByList求补, 得出需要移除的cell
            var ToRemoveList = new NativeParallelHashSet<int>(DelayedInstConf.ToRemoveEntityListMax, Allocator.Temp);
            ToRemoveList.Clear();
            foreach (var cell_id in LastNearbyList)
                ToRemoveList.Add(cell_id);
            ToRemoveList.ExceptWith(CurNearbyList);
            
            // Logger.DebugFormat("DelayedInstantiate Player Pos Changed: Cell Idx: {0}, old idx: {1}, RemoveList: {2}"
            //     , player_idx
            //     , LastPlayerIdx
            //     , ToRemoveList.Count());

            //3.3 更新当前数据
            LastNearbyList.Clear();
            foreach (var cur_list in CurNearbyList)
                LastNearbyList.Add(cur_list);
            LastPlayerIdx = player_idx;

            //3.4. 计算CurNearByList内格子的优先级, 存入SortedResult进行排序
            //Vector2 player_forward = Mc.Camera.Forward2D;
            SortedCellResult.Clear();
            foreach (var idx in CurNearbyList)
            {
                //3.4.1 将玩家自己的cell最大
                if (idx == player_idx)
                {
                    SortedCellResult.Enqueue(player_idx, int.MaxValue - 1);
                    continue;
                }

                //3.4.2 获取cell, 计算与玩家的距离, 与玩家的角度
                var cell = Grid.GetCell(idx);
                Vector2 target_vec = cell.Pos - player_pos;
                float dist = target_vec.magnitude;
                int priority = (int)Mathf.Clamp(DelayedInstConf.MaxRefreshDistance - dist, 0.0f, 1000.0f);

                //3.4.3 存入最大堆
                SortedCellResult.Enqueue(idx, priority);
            }

            //3.5 处理移除的cell, 需要把cell内的entity对应的Entity2Cell全局数据清除
            var ToRemoveEntityList = new NativeParallelHashSet<long>(DelayedInstConf.ToRemoveEntityListMax, Allocator.Temp);
            foreach (var cell_idx in ToRemoveList)
            {
                var cell = Grid.GetCell(cell_idx);
                ToRemoveEntityList.Clear();
                cell.FillAllEntity(ref ToRemoveEntityList);
                
                foreach (var entity_id in ToRemoveEntityList)
                {
                    Entity2CellIdx.Remove(entity_id);
                }

                Grid.RemoveCell(cell_idx);
            }

            ToRemoveEntityList.Dispose();
            ToRemoveList.Dispose();
        }

        ProfilerApi.BeginSample(EProfileFunc.OnFps30Update_DelayInst_InstEntity);
        //5. 从SortedResult中取出cell, 额外加入全局格子,开始计时
        TempSortedCellResult.Clear();
        TempSortedCellResult.CopyFrom(SortedCellResult);
        TempSortedCellResult.Enqueue(-1, int.MaxValue);

        bool has_time_left = CurTimeLeft < CurDuration;
        while (has_time_left)
        {
            //5.1 获取当前cell, 并将其内部的entity排好序
            bool IsSortedListEmpty = TempSortedCellResult.TryDequeue(out int idx, out int priority);
            if (IsSortedListEmpty == false)
                break;

            DelayedInstantiateCell cell = Grid.GetCell(idx);
            if (cell.IsInit == false || cell.IsEmpty())
            {
                CurTimeLeft = Timer.ElapsedMilliseconds;
                has_time_left = CurTimeLeft < CurDuration;
                continue;
            }

            // 5.2 遍历获取排好序的entity id, 检查prefab是否创建, 然后开始实例化
            if (cell.EntityCount > 0)
            {
                SortedEntityInCellResult.Clear();
                cell.FillEntityInQueue(SortedEntityInCellResult);
                has_time_left = InstantiateEntity(SortedEntityInCellResult
                    , cell
                    , Timer
                    , CurDuration
                    , ref CurTimeLeft);

                if (has_time_left == false)
                    break;
            }

            //5.3 再使用相同步骤处理embeded entity
            if (cell.EmbeddedEntityCount > 0)
            {
                SortedEntityInCellResult.Clear();
                cell.FillEmbeddedInQueue(SortedEntityInCellResult);
                has_time_left = InstantiateEmbeddedEntity(SortedEntityInCellResult
                    , cell
                    , Timer
                    , CurDuration
                    , ref CurTimeLeft);
            }
        }
        
        Timer.Stop();
        ProfilerApi.EndSample(EProfileFunc.OnFps30Update_DelayInst_InstEntity);

#if ENABLE_DELAYED_DESTROY_OBJECT_DURATION
        // 设置当前帧延迟销毁对象时长
        UnityEngine.Object.delayedDestroyDurationMs = CurDuration - CurTimeLeft;
#endif
        
    }

    public bool InstantiateEntity(PriorityQueue<long> InQueue
                                    , DelayedInstantiateCell InCell
                                    , Stopwatch InWatch
                                    , float InCurDuration
                                    , ref float OutInterval)
    {
        //1. 遍历获取排好序的entity id, 检查prefab是否创建, 然后开始实例化
        EntityBase entity = null;
        float lastInterval = 0;
        bool has_time_left = OutInterval < InCurDuration;
        while (has_time_left)
        {
            bool is_suc = InQueue.TryDequeue(out long entity_id, out int type_priority);
            if (is_suc == false)
                break;

            // TODO 5.3 检查GoPool是否有资源, 目前prefab路径必须创建GO, 根据每个业务逻辑进行拼装, 后续再修改
            // TODO 5.4 PrefabLoading是否已经申请, 没有则申请加载并跳过
            // TODO 5.5 否则跳过

            //5.6 根据id获取entity
            bool is_entity_exist = Mc.Entity.TryGetEntity(entity_id, out entity);
            if (is_entity_exist == false)
            {
                InCell.Remove(entity_id);
                Logger.InfoFormat("DelayedInstantiate EntityId:{0} cell:{1} not exist in Mc.Entity "
                    , entity_id, InCell.Idx);
                continue;
            }

            if ((entity.LodLevel & ~CustomLod.INNER_LOD_BRIEF) == 0)
            {
                continue;
            }
            
            try
            {
                Mc.Go.CreateOrUpdateGo(entity, true);
            }
            catch (Exception e)
            {
                Logger.Error(e);
            }
            finally
            {
                //5.7 从cell中移除，成功则添加到已实例化队列，失败则删除，给embedded entity进行查询
                InCell.InstantiateEntity(entity_id);
                lastInterval = OutInterval;
                OutInterval = InWatch.ElapsedMilliseconds;
                lastInterval = OutInterval - lastInterval;
                has_time_left = OutInterval < InCurDuration;
            }

            Logger.DebugFormat("DelayedInstantiate Cell id: {1}, EntityId:{0}, left:{2}, embedded left:{3}, time interval:{4}"
                                , entity_id
                                , InCell.Idx
                                , InCell.EntityCount
                                , InCell.EmbeddedEntityCount
                                , OutInterval);
        }
        if (lastInterval > InCurDuration && entity != null)
        {
            Logger.DebugFormat("DelayedInstantiate.InstantiateEntity id: {0}, type:{1}, time:{2}", entity.EntityId, entity.EntityType, lastInterval);
        }
        return has_time_left;
    }

    public bool InstantiateEmbeddedEntity(PriorityQueue<long> InQueue
                                        , DelayedInstantiateCell InCell
                                        , Stopwatch InWatch
                                        , float InCurDuration
                                        , ref float OutInterval)
    {
        //1. 遍历获取排好序的entity id, 检查prefab是否创建, 然后开始实例化
        EmbeddedCustomBase entity = null;
        float lastInterval = 0;
        bool has_time_left = OutInterval < InCurDuration;
        while (has_time_left)
        {
            bool is_suc = InQueue.TryDequeue(out long entity_id, out int type_priority);
            if (is_suc == false)
                break;

            // TODO 5.3 检查GoPool是否有资源, 目前prefab路径必须创建GO, 根据每个业务逻辑进行拼装, 后续再修改
            // TODO 5.4 PrefabLoading是否已经申请, 没有则申请加载并跳过
            // TODO 5.5 否则跳过

            //5.6 根据id获取entity
            entity = EmbeddedCustomManager.Instance.GetEmbedded(entity_id);
            if (entity == null)
            {
                InCell.Remove(entity_id);
                Logger.InfoFormat("DelayedInstantiate Embedded EntityId:{0} Cell:{1} not exist in Mc.Entity"
                    , entity_id, InCell.Idx);
                continue;
            }

            //5.7 如果是embeded entity, 需要额外检查父类是否已经实例化
            var parent = entity.RootParent;
            if (parent == null)
            {
                InCell.Remove(entity_id);
                Logger.InfoFormat("DelayedInstantiate Embedded entityId:{0} entityType:{1} parent is null"
                    , entity_id, entity.EntityType);
                continue;
            }

            //5.8 当前玩家不在cell内, 如果发现父类是当前玩家则直接实例化,如果不是则等待父类加载完成
            if (parent.EntityId != Mc.MyPlayer.MyEntityId && InCell.Idx != -1)
            {
                if (!InCell.InstantiatedEntity.Contains(parent.EntityId))
                {
                    InCell.Remove(entity_id);
                    Logger.ErrorFormat("DelayedInstantiate Embedded entityId:{0} entityType:{1} parent:{2} is not instantiated"
                        , entity_id, entity.EntityType, parent.EntityId);
                    continue;
                }
            }

            try
            {
                Mc.Go.CreateOrUpdateGo(entity);
                Mc.Entity.tryTriggerShortcutsEventNextFrame.Add(entity);
            }
            catch (Exception e)
            {
                Logger.Error(e);
            }
            finally
            {
                InCell.Remove(entity_id); // Embedded entity更新后，不再记录entity id，直接删除
                lastInterval = OutInterval;
                OutInterval = InWatch.ElapsedMilliseconds;
                lastInterval = OutInterval - lastInterval;
                has_time_left = OutInterval < InCurDuration;
            }

            Logger.DebugFormat("DelayedInstantiate Cell id: {1}, EntityId:{0}, left:{2}, embedded left:{3}, time interval:{4}"
                                , entity_id
                                , InCell.Idx
                                , InCell.EntityCount
                                , InCell.EmbeddedEntityCount
                                , OutInterval);
        }
        if (lastInterval > InCurDuration && entity != null)
        {
            Logger.DebugFormat("DelayedInstantiate.InstantiateEmbeddedEntity id: {0}, type:{1}, time:{2}", entity.EntityId, entity.EntityType, lastInterval);
        }
        return has_time_left;
    }

    public void UpdateEntities(IReadOnlyDictionary<long, EntityBase> inNewEntities,
                                 Dictionary<long, IEmbeddedCustom> newEmbedded)
    {
        //1. 防御, 由于地形初始化在MgrEntityGo之后, 因此grid创建也只能延后
        if (IsInit == false)
            return;

        if (Grid == null)
        {
            IsValid = TryInitGrid(Mc.Terrain.TerrainRect
                                , Mc.Terrain.TerrainSize
                                , DelayedInstConf.CellSize
                                , out Grid);
        }

        if (IsValid == false)
            return;
        
        //3. 更新entity到cell, 如果cell不存在则创建
        foreach (var pair in inNewEntities)
        {
            //3.1 如果是全局entity, 则加入全局cell
            //    不能是玩家当前的格子, 因为网络时序问题, 玩家的位置可能还没有更新
            var entityId = pair.Key;
            var entity = pair.Value;
            var entityType = entity.EntityType;
#if SOC_CLIENT
            if (Mc.Go.LinkClientPredictEntity(entity))
            {
                // ThrowEntity 在预测时已经创建
                continue;
            }
#endif

            int cellIdx = -1;
            // 建筑的优先级需要根据玩家位置来调整
            int priority = DelayedInstPriorityMap.GetDelayedInstPriority(entity);
            if (IsGlobalEntity(entityType) == false)
            {
                Vector2 pos = GetEntityPos(entity);
                cellIdx = Grid.CalcGridSingleIdx(pos.x, pos.y);
            }
            
            DelayedInstantiateCell cell = Grid.GetCell(cellIdx);
            if (cell.IsInit == false)
                cell.Init(cellIdx, Grid.CalcCellPos(cellIdx));
            
            cell.Add(entityId, priority);
            Entity2CellIdx[entityId] = cellIdx;
            //DelayedRemoveEntityList.Remove(entityId); // 从延迟卸载中移除entity
            Logger.InfoFormat("DelayedInstantiate.UpdateEntities entityId:{0} type:{1} cell_idx:{2}", entityId, entityType, cellIdx);
        }

        //4. 更新EmbeddedEntitye到cell, 根据父类id获取位置, 并且找到相应cell进行更新
        foreach (var (_, embedded) in newEmbedded)
        {
            //4.1 防御空父类
            if (embedded == null)
                continue;

            var entityId = embedded.EntityId;
            var entityType = embedded.EntityType;
            var parent = embedded.RootParent;
            if (parent == null)
            {
                Logger.ErrorFormat("DelayedInstantiate UpdateEntities: embeddedEntity:{0}, type:{1} has no parent"
                                    , entityId, entityType);
                continue;
            }

            //4.2 防御父类不在cell内
            var ownerEntityId = parent.EntityId;
            bool is_found = Entity2CellIdx.TryGetValue(ownerEntityId, out var cell_idx);
            if (is_found == false)
            {
                Logger.InfoFormat("DelayedInstantiate UpdateEntities: embeddedEntity:{0}, type:{1} parent:{2} type:{3} has no cell, use cell -1"
                    , entityId, entityType
                    , ownerEntityId, parent.EntityType);
                cell_idx = -1;
            }

            //4.3 更新cell
            int priority = DelayedInstPriorityMap.GetDelayedInstPriority(entityType, ownerEntityId);
            var Cell = Grid.GetCell(cell_idx);
            Cell.AddEmbedded(entityId, priority);
            Entity2CellIdx[entityId] = cell_idx;
            Logger.InfoFormat("DelayedInstantiate.UpdateEntities embedded entityId:{0} type:{1} cell_idx:{2}", entityId, entityType, cell_idx);
            //DelayedRemoveEntityList.Remove(embedded.EntityId); // 从延迟卸载中移除entity
        }

        if (inNewEntities.Count > 0 || newEmbedded.Count > 0)
            Logger.DebugFormat("DelayedInstantiate UpdateEntities: New Entity Count: {0}, New Embedded Entity Count: {1}", inNewEntities.Count, newEmbedded.Count);

        
        //5. 遍历UpdateEntity列表, 因为部分entity会移动, 比如其他玩家, 比如火车, 在玩家移动后, 可能会导致cell不正确而无法出现
        if (ProcessEntity.Instance.ChangedEntities.Count == 0)
            return;

        int update_count = 0;
        var ToRemoveEntityList = new NativeParallelHashSet<long>(DelayedInstConf.ToRemoveEntityListMax, Allocator.Temp);
        foreach (var pair in ProcessEntity.Instance.ChangedEntities)
        {
            var entityId = pair.Key;
            //5.1 找到对应的cell, 假设embeded与parent绑定, 忽略其cell信息, 也跟着一起挪动
            bool is_2cell_found = Entity2CellIdx.TryGetValue(entityId, out int last_idx);
            if (is_2cell_found == false)
                continue;

            //5.2 获取最新的pos, 找到对应的cell_idx, 和之前的cell_idx比较, 如果不一样则移动
            bool is_found = Mc.Entity.TryGetEntity(entityId, out IEntity entity);
            if (is_found == false)
            {
                Logger.ErrorFormat("DelayedInstantiate UpdateEntities: EntityId:{0} not exist in Mc.Entity", entityId);
                continue;
            }

            if (IsGlobalEntity(entity.EntityType))
                continue;

            var pos_2d = GetEntityPos(entity);
            int new_cell_idx = Grid.CalcGridSingleIdx(pos_2d.x, pos_2d.y);
            if (new_cell_idx == last_idx)
                continue;

            //5.3 移除旧的cell, 更新新的cell,
            var last_cell = Grid.GetCell(last_idx);
            var new_cell = Grid.GetCell(new_cell_idx);
            if (new_cell.IsInit == false)
                new_cell.Init(new_cell_idx, Grid.CalcCellPos(new_cell_idx));

            if (last_cell.TryRemoveEntity(entityId, out var priority))
                new_cell.Add(entityId, priority);
            else if (last_cell.InstantiatedEntity.Remove(entityId))
            {
                new_cell.InstantiatedEntity.Add(entityId);
                ++new_cell.InstantiatedEntityCount;
                --last_cell.InstantiatedEntityCount;
            }

            //5.4 遍历移动embedded entity, 并在旧cell中移除, 在新cell中添加
            
            if (last_cell.EmbeddedEntityCount > 0)
            {
                bool hasRemoveEntity = false;
                ToRemoveEntityList.Clear();
                foreach (var embeded_pair in last_cell.EmbeddedEntityDic)
                {
                    var embeddedId = embeded_pair.Key;
                    var embeddedPriority = embeded_pair.Value;
                    var embeddedEntity = EmbeddedCustomManager.Instance.GetEmbedded(embeddedId);
                    var embeddedParent = embeddedEntity?.RootParent;
                    if (embeddedParent == null)
                    {
                        hasRemoveEntity = true;
                        ToRemoveEntityList.Add(embeddedId);
                        Entity2CellIdx.Remove(embeddedId);
                        Logger.ErrorFormat("DelayedInstantiate UpdateEntities:Embedded EntityId:{0} is null:{1} or parent is null, type:{2}"
                            , embeddedId, embeddedEntity == null, entity.EntityType);
                        continue;
                    }

                    if (embeddedParent.EntityId == entityId)
                    {
                        hasRemoveEntity = true;
                        ToRemoveEntityList.Add(embeddedId);
                        new_cell.AddEmbedded(embeddedId, embeddedPriority);
                        Entity2CellIdx[embeddedId] = new_cell_idx;
                    }
                }

                if (hasRemoveEntity)
                {
                    foreach (var id in ToRemoveEntityList)
                        last_cell.Remove(id);
                
                    Logger.DebugFormat("DelayedInstantiate EntityId:{0}'s embedded entity Move to Cell:{1}, Count:{2}"
                        , entityId
                        , new_cell_idx
                        , ToRemoveEntityList.Count());
                }
            }

            update_count += 1;

            //5.4 更新全局数据
            Entity2CellIdx[entityId] = new_cell_idx;
        }

        if (update_count > 0)
        {
            //RemoveNullEntities();
            Logger.DebugFormat("DelayedInstantiate UpdateEntities: Update Entity Count: {0}", update_count);
        }

        ToRemoveEntityList.Dispose();
    }


    public bool TryInitGrid(Rect InTerrainRect
                            , int InGridSize
                            , int CellSize
                            , out SignifianceGrid<DelayedInstantiateCell> OutGrid)
    {
        OutGrid = null;
        if (InTerrainRect.x != 0 || InTerrainRect.y != 0)
        {
            OutGrid = new SignifianceGrid<DelayedInstantiateCell>(InTerrainRect
                                                                    , InGridSize
                                                                    , CellSize);

            //初始化全局cell
            var cell = OutGrid.GetCell(-1);
            cell.Init(-1, Vector2.zero);

            return true;
        }

        Logger.WarnFormat("DelayedInstantiate TerrainRect is empty, disable delayed instantiate!");
        return false;
    }

    public void RemoveEntities(Dictionary<long, int> removeEntities,
        HashSet<IEmbeddedCustom> removeEmbedded)
    {
        //1. 为了和entity顺序一致， 必须先删除， 再添加。
        // 先销毁embedded entity
        foreach (var embeddedId in removeEmbedded)
        {
            RemoveEntityDelayed(embeddedId.EntityId);
        }

        // 后销毁entity
        foreach (var id in removeEntities.Keys)
        {
            RemoveEntityDelayed(id);
        }

        /*if (removeEmbedded.Count > 0 || removeEntities.Count > 0)
        {
            RemoveNullEntities();
        }*/
    }

    public bool RemoveEntityFromCell(long entityId)
    {
        if (Entity2CellIdx.TryGetValue(entityId, out var cellIdx) == true)
        {
            Entity2CellIdx.Remove(entityId);
            var cell = Grid.GetCell(cellIdx);
            return cell.Remove(entityId);    
        }

        return false;
    }

    // 检测不存在的实体，并删除，只在编辑器起效，避免影响性能
    [Conditional("UNITY_EDITOR")]
    public void RemoveNullEntities()
    {
        var ToRemoveEntityList = new NativeParallelHashSet<long>(DelayedInstConf.ToRemoveEntityListMax, Allocator.Temp);
        foreach (var pari in Entity2CellIdx)
        {
            if (Mc.Entity.Entities.ContainsKey(pari.Key) == false
                && EmbeddedCustomManager.Instance.GetEmbedded(pari.Key) == null)
                ToRemoveEntityList.Add(pari.Key);
        }

        foreach(var id in ToRemoveEntityList)
        {
            bool hasGo = Mc.Go.Gos.ContainsKey(id);
            bool hasLoadingGo = Mc.Go.GoFactory.IsAsyncLoading(id);
            RemoveEntityDelayed(id);
            Logger.ErrorFormat("DelayedInstantiate.RemoveNullEntities entityId:{0} not exist in Mc.Entity, in Gos:{1}, in LoadingGos:{2}"
                , id, hasGo, hasLoadingGo);
        }

        ToRemoveEntityList.Dispose();
    }

    public void RemoveEntityDelayed(long entityId)
    {
        Logger.InfoFormat("DelayedInstantiate.RemoveEntityDelayed entityId:{0}", entityId);

        if (Mc.Go.GoFactory.CancelAsync(entityId))
        {
            // 异步加载中，取消即可
            return;
        }

        // 判断Entity不在延迟实例化队列中或在已经实例化队列中
        if (!RemoveEntityFromCell(entityId) || Mc.Go.Gos.ContainsKey(entityId))
        {
            // 添加到延迟删除队列
            DelayedRemoveEntityList.Add(entityId);
        }
    }

    public bool RemoveEntitiesImmediate(Stopwatch InWatch, float InCurDuration, ref float OutInterval)
    {
        if (DelayedRemoveEntityList.IsEmpty)
            return OutInterval < InCurDuration;

        ProfilerApi.BeginSample(EProfileFunc.OnFps30Update_DelayInst_RemoveEntity);
        float lastInterval = 0;

        var ToRemoveEntityList = new NativeParallelHashSet<long>(DelayedInstConf.ToRemoveEntityListMax, Allocator.Temp);
        foreach(var entityId in DelayedRemoveEntityList)
        {
            ToRemoveEntityList.Add(entityId);
            
            try
            {
                // 不需要再次删除延迟实例化Entity，已在RemoveEntityDelayed删除，防止同一帧后添加的延迟实例化Entity被删除
                Mc.Go.GoFactory.Remove(entityId);
            }
            catch (Exception e)
            {
                Logger.Error(e);
            }
            finally
            {
                lastInterval = OutInterval;
                OutInterval = InWatch.ElapsedMilliseconds;
                lastInterval = OutInterval - lastInterval;
            }
        }

        foreach (var id in ToRemoveEntityList)
            DelayedRemoveEntityList.Remove(id);
        
        ToRemoveEntityList.Dispose();
        ProfilerApi.EndSample(EProfileFunc.OnFps30Update_DelayInst_RemoveEntity);

        if (lastInterval > InCurDuration)
        {
            Logger.InfoFormat("DelayedInstantiate.RemoveEntitiesImmediate cost {0}ms", lastInterval);
        }
        return OutInterval < InCurDuration;
    }

    public Vector2 GetEntityPos(IEntity InEntity)
    {
        //1. 在代码Generator中影响保障了所有entity都继承了IPosition2Entity
        var pos = InEntity is IPosition2Entity posEntity ? new Vector2(posEntity.PosX, posEntity.PosZ) : Vector2.zero;
        return pos;
    }
    
    public bool IsGlobalEntity(int InEntityTypeId)
    {
        bool isGlobalEntity = InEntityTypeId == EntityTypeId.AirdropEntity
                              || InEntityTypeId == EntityTypeId.AirDropPlaneEntity
                              || InEntityTypeId == EntityTypeId.MissileEntity
                              || InEntityTypeId == EntityTypeId.PlayerEntity; // 玩家优先加载，防止载具上的玩家位置不更新导致一直不创建
        return isGlobalEntity;
    }
    
    // 延迟实例化是否全部完成
    public bool IsDelayedInstantiateDone()
    {
        return CurDuration > CurTimeLeft
               && DelayedRemoveEntityList.IsEmpty
               && !GoPool.LoadTaskMgr.HasTasks;
    }
}
