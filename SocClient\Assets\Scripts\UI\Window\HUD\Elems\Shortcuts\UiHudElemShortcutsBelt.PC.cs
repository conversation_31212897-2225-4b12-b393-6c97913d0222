#if STANDALONE_REAL_PC
using System.Drawing;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.SocClient.GameDebug;
using WizardGames.Soc.SocClient.Manager;

namespace WizardGames.Soc.SocClient.Ui
{
    /// <summary>
    /// 物品快捷栏作为物品使用的分流中心, 点击或选中物品后, 将物品使用的逻辑分流给 系统/战斗/建造 侧逻辑进行处理
    /// </summary>
    public partial class UiHudElemShortcutsBelt
    {
        private void DoClickEmptyItem(ComItemIcon clickIcon)
        {
            // 点击空的图标
            // 如果是远程武器图标，唤出武器替换
            if (clickIcon is ComHUDWeaponItemIcon)
            {
                Mc.UploadLog.RecordClick(EBattleButtonId.ChooseWeapon, EBattleButtonGroup.Weapon);
                //UiHudWeaponChoose.Open(false);
            }
            // 否则呼出快捷栏道具替换(新手关除外)
            else if (!PlayHelper.IsNewbie)
            {
               // DoClickItemChooseMenu(clickIcon);
            }

            // 新需求是切空手
            Mc.MyPlayer.MyEntityLocal.DrawComponent.UnEquip();
        }

        /// <summary>
        /// HUD点击到的Elem发生变化
        /// </summary>
        public void OnHudBlockElemChange(UiHudElem curElem, bool force)
        {
            //pc端无初始的block和摇杆，过滤特殊情况
            if (!force &&( curElem == null || curElem == this.root))
                return;
            if (inMenuChoose) SetChooseMenuState(false);
        }

        /// <summary>
        /// 点击物品图标设置物品选单
        /// </summary>
        protected bool DoClickItemChooseMenu(ComItemIcon clickIcon)
        {
            int iconIndex = clickIcon.Position;
            // 点击物品图标时，如果选单已经打开, 且已经选择了一个格子
            if (inMenuChoose && curMenuChooseIndex >= 0)
            {
                // 点击的是当前选中的图标, 关闭选单
                if (curMenuChooseIndex == iconIndex)
                {
                    SetChooseMenuState(false);
                }

                return true;
            }
            // 如果物品选单还没有打开，那么先打开
            if (!inMenuChoose)
            {
                SetChooseMenuState(true);
            }

            if (iconIndex >= ctrlMenuChooseSign.pageCount)
            {
                return false;
            }
            return true;
        }

        /// <summary>
        /// 执行快捷键
        /// </summary>
        public void ExecuteShortcut(int index)
        {
            OnClickItemByIndex(index);
        }

        public int GetChooseMenuRowCount()
        {
            return 3;
        }

        /// <summary>
        /// 执行点击图标的逻辑
        /// </summary>
        /// <returns>是否成功执行图标选中态切换, 当为false时, 会打断战斗侧状态机的切换</returns>
        public bool DoClickItem(ComItemIcon clickIcon, bool ignoreRootVisible = false)
        {
            if (null == clickIcon || (!ignoreRootVisible && !root.IsElemEnable))
            {
                ShortcutsBeltDebug.Info(logger, $"ClickItem fail, clickIcon is null? {null == clickIcon} or root.IsVisible is false {!root.IsElemEnable}");
                return false;
            }

            // 玩家状态机不在激活状态， 不允许切换快捷栏
            if (Mc.MyPlayer.MyEntityLocal.CharacterState == PlayerCharacterStateEnum.UnActive)
            {
                return false;
            }

            // 打开了设置面板, 不响应快捷栏切换
            if (UiDebug.IsShowCheat)
            {
                ShortcutsBeltDebug.Info(logger, $"ClickItem {clickIcon.TbItem.Name} fail, open ShowCheat");
                return false;
            }

            // 由于建筑模式会隐藏快捷栏, 所以在建筑模式下不允许执行图标点击, 并且需要对战斗的状态进行打断
            if (Mc.Construction.IsBuildCoreMode || Mc.Construction.IsInBuildEditMode)
            {
                ShortcutsBeltDebug.Info(logger, $"ClickItem {clickIcon.TbItem.Name} fail, In ConstructionMode");
                return false;
            }

            // PC 直接执行物品点击逻辑
            return DoClickItemInternal(clickIcon);
        }

        private bool TryClickItem(ComBaseIcon icon)
        {
            if (icon is not ComItemIcon clickIcon)
                return false;

            ShortcutsBeltDebug.Info(logger, "ShortcutsBelt click item");

            // PC版不判断CD

            clickCDRemainT = ClickCDT;
            if (!clickExceptIcons.Contains(clickIcon))
            {
                if (!DoClickItem(clickIcon))
                {
                    // 执行不成功, 需要打断战斗侧状态机执行
                    ShortcutsBeltDebug.Info(logger, "ShortcutsBelt click item, DoClickItem fail");
                    return false;
                }
                return true;
            }

            return false;
        }

        private void OnBottomBeltIconAcceptDrag_Platform(ComBaseIcon myIcon, ItemDragInfo dragInfo)
        {
            if (null == dragInfo || null == dragInfo.item || myIcon == UiItemIconDragDrop.CurDragIcon)
            {
                return;
            }

            var dropItem = dragInfo.item;
            bool isWeaponItemNode = dropItem is WeaponItemNode;
            // 如果是远程武器,给个提示
            if (isWeaponItemNode)
            {
                Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.WeaponCannotPutInItem);
            }
        }

        public void SetShowDragTitle(bool show)
        {
            if (inMenuChoose)
            {
                chooseMenu.SetShowDragTitle(show);
            }
        }
    }
}
#endif