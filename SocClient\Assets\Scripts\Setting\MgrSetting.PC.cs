﻿using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.SocClient.Manager;

namespace WizardGames.Soc.SocClient.Setting
{
    /// <summary>
    /// PC独有的逻辑
    /// </summary>
    public partial class MgrSetting
    {
        /// <summary>
        /// 配置表的数据
        /// </summary>
        private Dictionary<ESettingTab_PC, Dictionary<ESettingItemGroup, List<int>>> configDataPC = new();

        /// <summary>
        /// 手游的Tab数据
        /// </summary>
        private List<ESettingTab_PC> tabListPC = new();
        private Dictionary<ESettingTab_PC, int> tabToIdPC = new();

        private List<ESettingTab_PC> canShowTabsPC = new();
        /// <summary>
        /// 新手关需要隐藏的Tab
        /// </summary>
        private List<ESettingTab_PC> newbieHideTabsPC = new()
        {
            ESettingTab_PC.PickUp,
#if PUBLISH
            ESettingTab_PC.GmCommand,
            ESettingTab_PC.GmProperty,
            ESettingTab_PC.GmSkin,
            ESettingTab_PC.GmMonster,
            ESettingTab_PC.Debug,
            ESettingTab_PC.Observer,
#endif
        };
        
        /// <summary>
        ///  publish发布版本需要隐藏的Tab
        /// </summary>
        private List<ESettingTab_PC> publishHideTabsPC = new()
        {
            ESettingTab_PC.GmMonster,
            ESettingTab_PC.Debug,
        };
        
        /// <summary>
        /// 从表格里读取Tab数据
        /// </summary>
        public void LoadTabDataFromTb_PC()
        {
            if (!IsUsePCUi()) return;
            if (null == Mc.Tables.TbSettingTab_PC) return;

            var tempList = new List<Common.Data.Item.SettingTab_PC>();
            tempList.AddRange(Mc.Tables.TbSettingTab_PC.DataList);
            tempList.Sort((a, b) => a.Sort.CompareTo(b.Sort));
            foreach (var data in tempList)
            {
                if (data.IsShow)
                {
                    tabListPC.Add(data.TabId);
                    tabToIdPC.Add(data.TabId, data.Id);
                }
            }
        }

        /// <summary>
        /// 从配置表里读取数据
        /// </summary>
        public void LoadConfigFromTb_PC()
        {
            if (!IsUsePCUi()) return;
            if (null == Mc.Tables.TbSetting_PC) return;
            foreach (var data in Mc.Tables.TbSetting_PC.DataList)
            {
                if (!configDataPC.ContainsKey(data.TabId))
                {
                    configDataPC[data.TabId] = new Dictionary<ESettingItemGroup, List<int>>();
                }

                if (!configDataPC[data.TabId].ContainsKey(data.Group))
                {
                    configDataPC[data.TabId][data.Group] = new List<int>();
                }

                if (data.NotShow == 0)
                {
                    configDataPC[data.TabId][data.Group].Add(data.Id);
                }
            }

            // 对List<int>的ID按sort字段进行排序
            foreach (var tab in configDataPC)
            {
                foreach (var group in tab.Value)
                {
                    group.Value.Sort((a, b) =>
                    {
                        var aSort = Mc.Tables.TbSetting_PC.GetOrDefault(a).Sort;
                        var bSort = Mc.Tables.TbSetting_PC.GetOrDefault(b).Sort;
                        return aSort.CompareTo(bSort);
                    });
                }
            }
        }

        /// <summary>
        /// 标签数据
        /// </summary>
        /// <returns></returns>
        public List<ESettingTab_PC> GetTabList_PC()
        {
            return tabListPC;
        }

        /// <summary>
        /// 获取可显示的标签数据
        /// </summary>
        /// <returns></returns>
        public List<ESettingTab_PC> GetCanShowTabs_PC()
        {
            bool isPublish = false;
#if PUBLISH
            isPublish = true;
#endif
            canShowTabsPC.Clear();
            foreach (var tab in tabListPC)
            {
                if (PlayHelper.IsNewbie && newbieHideTabsPC.Contains(tab)) continue;
                if (isPublish && publishHideTabsPC.Contains(tab)) continue;
                canShowTabsPC.Add(tab);
               
            }

            
            logger.InfoFormat("GetCanShowTabs_PC: {0} tabs can be shown newbieHideTabsPC Count {1} isPublish {2}", canShowTabsPC.Count,
                newbieHideTabsPC.Count, isPublish);
            return canShowTabsPC;
        }
        
        public int GetTabId_PC(ESettingTab_PC tab)
        {
            if (tabToIdPC.ContainsKey(tab))
            {
                return tabToIdPC[tab];
            }
            return 0;
        }

        /// <summary>
        /// 获取标签里的数据
        /// </summary>
        /// <param name="tab"></param>
        /// <returns></returns>
        public Dictionary<ESettingItemGroup, List<int>> GetConfigDataByTab_PC(ESettingTab_PC tab)
        {
            if (configDataPC.ContainsKey(tab))
            {
                return configDataPC[tab];
            }
            return null;
        }


        private void LoadBasicsConfigPc()
        {
            //切换冲刺按住
            AddGameConfigFromTable("mbtnRunningStyle", true, selected =>
            {
                if (!bool.TryParse(selected, out var b)) return;
                Mc.UserCmd.SetupSprintPressed = b;
            });
        }
        
#if STANDALONE_REAL_PC
        /// <summary>
        /// 打开设置界面
        /// </summary>
        /// <summary>
        /// 是否使用PC的UI
        public bool IsUsePCUi()
        {
            return true;
        }
#endif
    }
}