﻿using Construction.Util;
using System;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Const;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Common.Unity.Construction;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.SocClient;
using WizardGames.Soc.SocClient.Construction;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Framework;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Ui;

namespace WizardGames.Soc.Common.Component
{
    /// <summary>
    /// 编辑模式交互Id
    /// </summary>
    public enum EBuildEditInteractiveType
    {
        RepairAll = 307,
        BuildUpgrade = 400,
        BuildRepair,
        BuildRotate,
        BuildPickup,
        BuildDestroy,
        ElectricClearWire,
        DeployUpgrade,
        DeployMove,
        SwitchSocket = 514, //切换吸附点
        BuildComboUpgrade = 516, //组合升级
        BuildModify = 517, //建筑改造
        Electric = 161, //电力接线模式
        Water = 162, //水利接线模式
    }
    public class InteractiveComponent: BaseComponentLocal
    {
        private UiHud UiHud => Mc.Ui.GetWindow("UiHud") as UiHud;
        private float lastBtnListTime;
        private PartEntity ParentPartEntity => ParentEntity as PartEntity;
        public override int Id => (int)EComponentIdEnum.Interactive;
        private readonly List<int> uiBtnList = new();
        public List<int> GetUiBtnList()
        {
            //短时间内不重新计算
            if (Time.time - lastBtnListTime > 0.3f)
            {
                lastBtnListTime = Time.time;
                uiBtnList.Clear();

                if (UiHud == null)
                {
                    return null;
                }

                var list = GetUiBtnList(Mc.Hud.CurrentActiveStates());
                if (list != null && list.Count > 0)
                {
                    uiBtnList.AddRange(list);
                }

            }

            var checkRst = BuildingPrivilegeChecker.Instance.CheckPermissionByPart(EBuildingPrivilegeMask.BuildA_CheckTerritory, Mc.MyPlayer.MyEntityLocal, ParentPartEntity, true);

            if (checkRst != EResultType.Pass)
            {
                uiBtnList.Remove((int)EBuildEditInteractiveType.Electric);
                uiBtnList.Remove((int)EBuildEditInteractiveType.Water);
            }
            return uiBtnList;
        }
        
        
        /// <summary>
        /// 是否需要维修
        /// </summary>
        public bool NeedRepair => ParentPartEntity != null && ParentPartEntity.Hp < ParentPartEntity.MaxHp;

        private List<int> GetUiBtnListAtBeta1BuildMode()
        {
            var idList = new List<int>();
            var buildCoreConfig = Mc.Tables.TbBuildingCore.GetOrDefault(ParentPartEntity.TemplateId);
            if (buildCoreConfig == null)
            {
                return null;
            }
            //如果是组合建筑的父建筑并且子建筑已损毁，
            if (Mc.Construction.CanShowInteractiveId(ParentPartEntity))
            {
                uiBtnList.Add(EntityInteractiveIdHandle.RecoverSinglePartInteractiveId);
            }
            
            if (NeedRepair)
            {
                var repairResult =
                    ClientConstructionUtils.CheckInteractivePermission(ParentPartEntity, EPrivilegeType.Repair, true);
                if (repairResult && buildCoreConfig.IsRepairable == 1)
                {
                    idList.Add((int)EBuildEditInteractiveType.BuildRepair);
                }
            }
            return idList;
        }
        
         /// <summary>
        /// 根据建筑的状态来获取交互ID
        /// </summary>
        /// <returns></returns>
        private List<int> GetCommonUiBtnList()
        {
            var data = McCommon.Tables.TbBuildingCore.GetOrDefault(ParentPartEntity.TemplateId);
            if (data == null)
                return null;


            var ret = new List<int>();

            //用于记录handler，方便后续使用
            var handlers = Pool.GetColl<List<IUiBtnHandler>>();
            handlers.Clear();
            
            
            var lst = Pool.GetList<IUiBtnHandler>();
            ParentPartEntity.FindComponents(lst);
            handlers.AddRange(lst);
            lst.Clear();
            Pool.ReleaseList(lst);

            var config = Mc.Tables.TbBuildingCore.GetOrDefault(ParentPartEntity.TemplateId);
            if (config.CanBeUpgrade == 1 && ClientConstructionUtils.CheckInteractivePermission(ParentPartEntity,EPrivilegeType.Upgrade, true))
            {
                ret.Add((int)EBuildEditInteractiveType.DeployUpgrade);
            }
            
            //0.检查密码权
            if (!ClientConstructionUtils.CheckInteractivePermission(ParentPartEntity,EPrivilegeType.Password, true))
            {
                if (config.CanTakeLock == 1)
                {
                    ret.Add(MgrConstructionUnity.NoPasswordPermInteractId);
                }
                
                //获取突破权限限制的按钮
                foreach (var btnHandler in handlers)
                {
                    btnHandler.GetUiBtnListOutOfPerm(ret);
                }

                handlers.Clear();
                Pool.Release(handlers);

                return ret;
            }

            //1.添加默认通用配置的菜单
            if (data.InteractiveId != null)
            {
                ret.AddRange(data.InteractiveId);
            }

            //2.各个模块根据情况进行修正和补充(需要注意：可能存在清空原先的按钮list的情况)
            foreach (var btnHandler in handlers)
            {
                btnHandler.GetUiBtnList(ret);
            }

            //3.添加需要叠加的交互id(需要注意：这里绝对不允许对原先的id进行操作，只允许叠加)
            foreach (var btnHandler in handlers)
            {
                btnHandler.GetUiBtnOverlayList(ret);
            }

            handlers.Clear();
            Pool.Release(handlers);

            return ret;
        }

        
        private List<int> GetUiBtnList(List<EHudState> curHudStates)
        {
            if (!BuildingPrivilegeChecker.Instance.CheckPermissionByPartEx(EPrivilegeType.Interact, ParentPartEntity, true))
                return null;

            var listBeta1 = GetUiBtnListAtBeta1BuildMode();
            var commonUiBtnList = GetCommonUiBtnList();
            listBeta1.AddRange(commonUiBtnList);
            return listBeta1;
        }
        
        private PermissionHubComponent GetPermissionHubComp()
        {
            var permissionHubComp = ParentPartEntity?.GetComponent(EComponentIdEnum.PermissionHub) as PermissionHubComponent ?? null;
            if (permissionHubComp != null)
            {
                return permissionHubComp;
            }
            return null;
        }
        
        private void BuildEditOnClick(int btnId)
        {
            if (!Enum.IsDefined(typeof(EBuildEditInteractiveType), btnId)) return;

            Vector3 playerPos = Mc.MyPlayer.MyPlayerCc.transform.position;
            var dis = Vector3.Distance(playerPos, ParentPartEntity.Position.CV3ToUV3());
            if (dis > McCommon.Tables.TbConstructionConstantConfig.BuildInterDis)
            {
               logger.WarnFormat("InteractiveComponent: BuildEditOnClick - Distance too far: {0} > {1}", dis, Mc.Tables.TbConstructionConstantConfig.BuildInterDis); 
               return;
            }
            if (Mc.Construction.CurrentMode != null)
            {
                switch ((EBuildEditInteractiveType)btnId)
                {
                    case EBuildEditInteractiveType.BuildRepair:
                        Mc.Construction.TryRepair(ParentId);
                        break;
                }
            }
            switch ((EBuildEditInteractiveType)btnId)
            {
                case EBuildEditInteractiveType.Electric:
                case EBuildEditInteractiveType.Water:
                    Mc.Construction.EnterBuildMode(EBuildThirdModeType.DeviceConnection);
                    break;
                case EBuildEditInteractiveType.DeployUpgrade:
                    UiDeployUpgrade.OpenUpgradeView(ParentPartEntity);
                    break;
            }
            
            /*
            if (Mc.Construction.CurrentBuildMode != null)
            {
                switch ((BuildEditInteractiveType)btnId)
                {
                    case BuildEditInteractiveType.BuildUpgrade:
                    case BuildEditInteractiveType.BuildComboUpgrade:
                        Mc.Construction.EnterMode(BuildModeType.Edit);
                        UiEditConstruction?.RecordTargetEditMode(UiHudElemEditConstruction.EEditMode.Upgrade);
                        break;
                    case BuildEditInteractiveType.BuildRepair:
                        if (Mc.Construction.IsInRepairMode)
                        {
                            
                        }
                        Mc.Construction.EnterMode(BuildModeType.Edit);
                        UiEditConstruction?.RecordTargetEditMode(UiHudElemEditConstruction.EEditMode.Repair);
                        break;
                    case BuildEditInteractiveType.BuildRotate:
                        //Mc.Construction.EnterMode(BuildModeType.Edit);
                        if (Mc.MyPlayer.MyEntityLocal.IsWounded || Mc.MyPlayer.MyEntityLocal.IsDead) return;
                        CommonConstructionUtils.RequestRotatePart(ParentId);
                        //不要直接发送请求！！！
                        //Mc.LocalClient.SendRpcPacket(Endpoint.World, "EditRotatePart", EntityId);
                        break;
                    case BuildEditInteractiveType.SwitchSocket:
                        Mc.Construction.TryChangeCurAimPartDirection(ParentId);
                        break;
                    case BuildEditInteractiveType.BuildPickup:
                        Mc.Construction.EnterMode(BuildModeType.Edit);
                        UiEditConstruction?.RecordTargetEditMode(UiHudElemEditConstruction.EEditMode.PickUp);
                        break;
                    case BuildEditInteractiveType.BuildDestroy:
                        Mc.Construction.EnterMode(BuildModeType.Edit);
                        UiEditConstruction?.RecordTargetEditMode(UiHudElemEditConstruction.EEditMode.Destroy);
                        break;
                    case BuildEditInteractiveType.ElectricClearWire:
                        Mc.Construction.EnterMode(BuildModeType.Edit);
                        UiEditConstruction?.RecordTargetEditMode(UiHudElemEditConstruction.EEditMode.ClearWire);
                        break;
                    case BuildEditInteractiveType.DeployMove:
                        if (ParentPartEntity.TemplateId == (long)Common.Construction.PartType.ToolCupboard)
                        {
                            var terrCol = TerritoryManagerEntity.Instance.GetTerritoryEntity(ParentPartEntity.TerritoryId);
                            if (terrCol != null && terrCol.BaseComp.MovedTimes >= McCommon.Tables.TbConstructionConstantConfig.MaxTCMove)
                            {
                                Mc.MsgTips.ShowRealtimeWeakTip(1206);
                                return;
                            }
                        }

                        var partConfig = McCommon.Tables.TbBuildingCore.GetOrDefault(ParentPartEntity.TemplateId);
                        var nowTime = TimeStampUtil.GetNowTimeStampSec();
                        var deltaTime = ParentPartEntity.LastMovedSec + (int)partConfig.MoveCD - nowTime;
                        if (deltaTime > 0)
                        {
                            Mc.MsgTips.ShowRealtimeWeakTip(1204, (deltaTime + 1).ToString());
                        }
                        else
                        {
                            Mc.Construction.DoLocalDeployMove(ParentPartEntity);
                        }
                        Mc.Msg.FireMsgAtOnce(EventDefine.BuildItemCancelSelect, false);
                        break;
                    case BuildEditInteractiveType.DeployUpgrade:
                        UiDeployUpgrade.OpenUpgradeView(ParentPartEntity);
                        break;
                    case BuildEditInteractiveType.BuildModify:
                        Mc.Construction.EnterMode(BuildModeType.Modify);
                        break;
                }
            }
            else
            {
                switch ((BuildEditInteractiveType)btnId)
                {
                    case BuildEditInteractiveType.DeployUpgrade:
                        UiDeployUpgrade.OpenUpgradeView(ParentPartEntity);
                        break;
                    case BuildEditInteractiveType.BuildRepair:
                        Mc.Construction.EnterMode(BuildModeType.Edit);
                        UiEditConstruction?.RecordTargetEditMode(UiHudElemEditConstruction.EEditMode.Repair);
                        break;
                }
            }
            */
        }

        
        /// <summary>
        /// 点击响应
        /// </summary>
        /// <param name="btnId">交互id</param>
        /// <returns>权限检测结果是否通过(可考虑废弃)</returns>
        public void OnUiBtnClicked(int btnId, out bool permissionCheckRst)
        {
            permissionCheckRst = true;
            var configData = Mc.Tables.TbBuildingCore.GetOrDefault(ParentPartEntity.TemplateId);
            if (configData == null)
            {
                permissionCheckRst = false;
                return;
            }

            if (btnId == MgrConstructionUnity.NoPasswordPermInteractId)
            {
                permissionCheckRst = false;

                //todo begin @zjyuan @sfl
                ////领地柜中的"无权限"按钮点击
                //if (ParentPartEntity.TemplateId == (long)Common.Construction.PartType.ToolCupboard)
                //{
                //    var hubModule = GetPermissionHubComp();
                //    if (hubModule != null && hubModule.TerritoryEnt != null)
                //    {
                //        //占领期，防守者提示
                //        if (hubModule.TerritoryEnt.PlayComp.IsCapturing())
                //        {
                //            Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.NoPermInCapturingDefender);
                //            return;
                //        }
                //    }
                //}
                //todo end @zjyuan @sfl

                //通用无权提示
                Mc.MsgTips.ShowRealtimeWeakTip(23020);

                return;
            }


            var lst = Pool.GetList<IUiBtnHandler>();
            ParentPartEntity.FindComponents(lst);
            foreach (var item in lst)
            {
                item.OnUiBtnClicked(btnId);
            }
            lst.Clear();
            Pool.ReleaseList(lst);

            if (btnId == (int)EBuildEditInteractiveType.BuildPickup)
            {
                if (ParentPartEntity.TemplateId == (long)Common.Construction.PartType.LargePlanterBox || ParentPartEntity.TemplateId == (long)Common.Construction.PartType.SmallPlanterBox)
                {
                    var plantBoxComp = ParentPartEntity.PlantBoxComponent;
                    if (plantBoxComp != null && plantBoxComp.HasAnyPlant())
                    {
                        //无法回收，该种植箱内有未收割的植物
                        Mc.MsgTips.ShowRealtimeWeakTip(22096);
                        return;
                    }
                }
            }

            //进入接线模式
            if (btnId == (int)EBuildEditInteractiveType.Electric || btnId == (int)EBuildEditInteractiveType.Water)
            {
                Mc.Construction.EnterBuildMode(EBuildThirdModeType.DeviceConnection);
            }

            BuildEditOnClick(btnId);
        }

    }
}