using Newtonsoft.Json;
using SimpleJSON;
using System.Collections.Generic;
using System.Net;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Config;
using WizardGames.Soc.Common.Data.Play;
using WizardGames.Soc.Common.Data.task;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Event;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.ClassImpl.Task;
using WizardGames.Soc.SocWorld.Event;
using WizardGames.Soc.SocWorld.Framework.Event;
using WizardGames.Soc.SocWorld.Lobby;
using WizardGames.SocConst.Soc.Const;
using pbc = Google.Protobuf.Collections;

namespace WizardGames.Soc.Common.CustomType
{
    public enum ETaskState
    {
        UNLOCK = 0,
        IN_PROGRESS = 1,
        COMPLETED = 2,
        CLAIMED = 3
    }

    public enum ETaskGroupState
    {
        UNFINISHED = 0,
        FINISHED = 1,
        RECEIVED = 2
    }

    [HotfixClass]
    public static partial class LobbyMainTaskContainerHotfix
    {
        private const int WEEK_FRESH_HOUR = 5;
        private const int WEEK_FRESH_DAY = 1;

        /// <summary>
        /// 检查任务是否需要在当前游戏模式下跳过接取
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>true表示需要跳过接取</returns>
        private static bool ShouldSkipTaskAccept(long taskId)
        {
            return McCommon.Tables.TBTaskData.GetOrDefault((int)taskId)?.SkipAcceptGameMode
                ?.ContainsWithoutLinq(ServerConfig.Instance.GameMode) == true;
        }
        static LobbyMainTaskContainerHotfix()
        {
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<PlayerEntityLoginEvent>((self, _) =>
            {
                self.ComponentTask?.GetTaskContainer<LobbyMainTaskContainer>(PlayerTaskContainerIndex.Lobby).SyncTaskFromLobby();
            });
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<KickPlayerEvent>((self, _) =>
            {
                self.ComponentTask?.GetTaskContainer<LobbyMainTaskContainer>(PlayerTaskContainerIndex.Lobby).SyncTaskToLobby();
            });
        }

        [Hotfix]
        public static void InitVirtual(this LobbyMainTaskContainer self)
        {
            BaseInit.Invoke(self);
            self.TaskComp.AddSchedule(WEEK_FRESH_HOUR, WEEK_FRESH_DAY, true, WeekResetLobbyTaskHotfix);
        }

        [Hotfix]
        public static void PostInitVirtual(this LobbyMainTaskContainer self, bool isLoadFromDb)
        {
            BasePostInit.Invoke(self, isLoadFromDb);
        }

        [Hotfix(ScheduleCallback = true)]
        public static void WeekResetLobbyTask(this PlayerTaskComponent self, long _)
        {
            var lobbyTaskContainer = self.GetTaskContainer<LobbyMainTaskContainer>(PlayerTaskContainerIndex.Lobby);
            lobbyTaskContainer?.FlushWeekLobbyTask();

        }

        private static long WeeklyZeroTimeSecs()
        {
            var today = TimeStampUtil.MSec2LocalDateTime(ProcessEntity.Instance.NowTs);
            int daysToMonday = (int)today.DayOfWeek;

            if (daysToMonday == 0)
                daysToMonday = 6;
            else
                daysToMonday--;

            var monday = today.AddDays(-daysToMonday);
            return TimeStampUtil.DateTimeToSec(monday);
        }

        private static void FlushWeekLobbyTask(this LobbyMainTaskContainer self)
        {
            var currVersion = WeeklyZeroTimeSecs();
            foreach (var (taskId, _) in self.TaskId2Version)
            {
                self.FlushOneLobbyTask(taskId, currVersion);
            }
            self.Logger.Info($"[FlushWeekLobbyTask] currVersion:{currVersion}");
        }

        private static bool FlushOneLobbyTask(this LobbyMainTaskContainer self, long taskId, long currVersion)
        {
            if(self.TaskId2Version.TryGetValue(taskId, out var taskVersion))
            {
                if (currVersion <= taskVersion) return false;
                self.RemoveTask(taskId);
            }
            else
            {
                var groupId = (int)FindTaskGroupByTaskId(taskId);
                var taskGroupConfig = McCommon.Tables.TBTaskGroupData.GetOrDefault(groupId);
                if (taskGroupConfig == null)
                {
                    self.Logger.Error($"[FlushOneLobbyTask] task group Id {groupId} not found in TBTaskGroupData config");
                    return false;
                }
                if(taskGroupConfig.Reset != Data.task.ENUMResetType.WEEK)
                {
                    return false;
                }
            }
            self.TaskId2Version[taskId] = currVersion;
            self.AcceptTaskInternal(taskId);
            self.Logger.Info($"[FlushOneLobbyTask] taskId:{taskId},taskVersion:{taskVersion},currVersion:{currVersion}");
            return true;
        }

        private static void ClearTaskNode(this LobbyMainTaskContainer self)
        {
            self.RemoveContainerTask(TaskNodeIndex.InProgress);
            self.RemoveContainerTask(TaskNodeIndex.CompletedAndNotGetReward);
            self.CompletedTaskIds.Clear();

            self.GroupDic.Clear();
            self.TaskId2Version.Clear();
        }


        [Hotfix]
        public static EOpCode CompleteTaskVirtual(this LobbyMainTaskContainer self, TaskNode taskNode)
        {
            self.CompleteLobbyTask(taskNode);
            // 移除追踪
            self.TaskComp.CancelTrack(taskNode.BizId);
            return EOpCode.Success;
        }

        public static void SyncTaskFromLobby(this LobbyMainTaskContainer self)
        {
            LobbyServiceManager.Instance.lobbyService.SendSyncTaskFromLobby(self.Player.RoleId, self.OnSyncTaskFromLobby);
        }

        [Hotfix]
        public static TaskNode CreateTaskNodeVirtual(this LobbyMainTaskContainer self, QuestPhase taskConfig)
        {
            return new LobbyMainTaskNode(taskConfig.Id);
        }

        public static void OnSyncTaskFromLobby(this LobbyMainTaskContainer self, JSONNode content, HttpStatusCode statusCode,
            int errorCode)
        {
            if (statusCode != HttpStatusCode.OK || errorCode != 0)
            {
                self.Logger.Error($"[OnSyncTaskFromLobby] failed {statusCode} {errorCode} {content}");
                return;
            }
            self.ClearTaskNode();
            self.Logger.Info($"[OnSyncTaskFromLobby] success {content}");

            var taskData = JsonConvert.DeserializeObject<TaskServiceInnerTaskGroup>(content.ToString());
            if (taskData?.DsTaskGroups?.TaskGroups == null || taskData.DsTaskGroups.TaskGroups.Count == 0)
            {
                self.Logger.Info("[OnSyncTaskFromLobby] taskGroups is empty");
                return;
            }

            var lastTrackTaskId = self.TaskComp.TrackTaskId;
            foreach (var taskGroup in taskData.DsTaskGroups.TaskGroups)
            {
                var groupId = taskGroup.GroupId;
                self.GroupDic[groupId] = taskGroup;
                self.Logger.Info($"[OnSyncTaskFromLobby] add groupId {groupId}");

                if (McCommon.Tables.TBTaskGroupData.GetOrDefault(groupId) is not OBJTaskGroup taskGroupConfig)
                {
                    self.Logger.Error($"[OnSyncTaskFromLobby] task group Id {groupId} not found in TBTaskGroupData config");
                    continue;
                }

                foreach (var task in taskGroup.Tasks)
                {
                    if (McCommon.Tables.TbQuestPhase.GetOrDefault(task.TaskId) is not QuestPhase taskConfig)
                    {
                        self.Logger.Error($"[OnSyncTaskFromLobby] taskId {task.TaskId} not exist in task config");
                        continue;
                    }
                    //周刷新版本号
                    if (taskGroupConfig.Reset == Data.task.ENUMResetType.WEEK)
                    {
                        self.TaskId2Version[task.TaskId] = taskGroup.Version;
                    }
                    HandleTaskByState(self, task, taskConfig);
                }
            }
            self.Player.Trigger(LobbyTaskLoadSuccessEvent.Instance);

            if (lastTrackTaskId != 0 && self.TaskComp.TrackTaskId == 0)
            {
                self.TaskComp.CheckAutoTrackTask(lastTrackTaskId);
            }
        }

        private static void HandleTaskByState(LobbyMainTaskContainer self, CommonTask task, QuestPhase taskConfig)
        {
            switch ((ETaskState)task.State)
            {
                case ETaskState.COMPLETED:
                    self.SetupCompletedTaskNode(task, taskConfig);
                    break;
                case ETaskState.CLAIMED:
                    self.SetupClaimedTaskNode(task, taskConfig);
                    break;
                case ETaskState.IN_PROGRESS:
                    self.SetupInProgressTaskNode(task, taskConfig);
                    break;
                case ETaskState.UNLOCK:
                    //未解锁的先不管,除非有需求
                    break;
                default:
                    self.Logger.Error($"[OnSyncTaskFromLobby] taskId {task.TaskId} state {task.State} not support");
                    break;
            }
        }

        private static void SetupInProgressTaskNode(this LobbyMainTaskContainer self, CommonTask task, QuestPhase taskConfig)
        {
            // 检查主线任务是否需要跳过接取
            if (ShouldSkipTaskAccept(task.TaskId))
            {
                self.Logger.Info($"[SetupInProgressTaskNode] taskId {task.TaskId} skip accept in game mode {ServerConfig.Instance.GameMode}");
                return;
            }

            if (self.AddProcessTaskNode(taskConfig) is not LobbyMainTaskNode taskNode)
            {
                self.Logger.Error($"[OnSyncTaskFromLobby] taskId {task.TaskId} add process task node failed");
                return;
            }

            self.AcceptTaskCommon(taskNode, taskConfig, false);

            if (taskNode.ChildCountWithoutReset != task.Counters.Count)
            {
                self.Logger.Error($"[OnSyncTaskFromLobby] taskId {task.TaskId} counters count mismatch with children");
            }

            self.UpdateTaskCounters(taskNode, task.Counters);
        }

        private static void SetupCompletedTaskNode(this LobbyMainTaskContainer self, CommonTask task, QuestPhase taskConfig)
        {
            if (self.AddProcessTaskNode(taskConfig) is not LobbyMainTaskNode taskNode)
            {
                self.Logger.Error($"[SetupCompletedTaskNode] taskId {task.TaskId} add process task node failed");
                return;
            }

            self.completedNotRewardTask.AddChildNode(taskNode);
            foreach (var subTaskId in taskConfig.SubTasks.AppendWithoutLinq((int)taskConfig.Id))
            {
                if (taskNode.GetChild(subTaskId) is not SubTaskNode subTaskNode)
                {
                    continue;
                }

                if (McCommon.Tables.TbQuestPhase.GetOrDefault(subTaskId) is not QuestPhase subTaskConfig)
                {
                    self.Logger.Error($"[SetupCompletedTaskNode] subTaskId {subTaskId} not exist in task config");
                    continue;
                }

                if (subTaskConfig.EndConditionParameter.Length > 0)
                {
                    subTaskNode.Count = (int)subTaskConfig.EndConditionParameter[0];
                }

                subTaskNode.IsComplete = true;
            }

            if (self.TaskComp.TrackTaskId == taskNode.BizId)
            {
                self.TaskComp.TrackTaskId = 0;
            }
        }

        private static void SetupClaimedTaskNode(this LobbyMainTaskContainer self, CommonTask task, QuestPhase taskConfig)
        {
            if (self.AddProcessTaskNode(taskConfig) is not LobbyMainTaskNode taskNode)
            {
                self.Logger.Error($"[SetupClaimedTaskNode] taskId {task.TaskId} add process task node failed");
                return;
            }

            self.AddCompletedTask(taskNode);

            if (self.TaskComp.TrackTaskId == taskNode.BizId)
            {
                self.TaskComp.TrackTaskId = 0;
            }
        }

        private static void UpdateTaskCounters(this LobbyMainTaskContainer self, LobbyMainTaskNode taskNode,
            List<CommonTaskCounter> counters)
        {
            using var counterEnumerator = counters.GetEnumerator();

            using var subTaskEnumerator = taskNode.GetEnumeratorWithoutReset();
            while (subTaskEnumerator.MoveNext())
            {
                var (_, subTaskNode) = subTaskEnumerator.Current;
                if (subTaskNode is not SubTaskNode subTask)
                {
                    continue;
                }

                if (!counterEnumerator.MoveNext())
                {
                    self.Logger.Error($"[UpdateTaskCounters] taskId {taskNode.Id} counters count less than children count");
                    break;
                }

                if (counterEnumerator.Current is not CommonTaskCounter counter)
                {
                    self.Logger.Error($"[UpdateTaskCounters] counter is not CommonTaskCounter for subTask {subTask.BizId}");
                    continue;
                }

                subTask.Count = (int)counter.CountValue;

                if (McCommon.Tables.TbQuestPhase.GetOrDefault(subTask.BizId) is QuestPhase taskConfig)
                {
                    if (McCommon.Tables.TbQuestCondition.GetOrDefault(taskConfig.TaskPhaseEndCondition) is QuestCondition
                        conditionConfig)
                    {
                        subTask.IsComplete = ConditionUtil.Compare(conditionConfig.CompareType, subTask.Count,
                            (int)taskConfig.EndConditionParameter[0]);
                    }
                    else
                    {
                        self.Logger.Error($"[UpdateTaskCounters] conditionConfig null for subTask {subTask.BizId}");
                        subTask.IsComplete = false;
                    }
                }
            }
        }

        private static bool AcceptTaskInternal(this LobbyMainTaskContainer self, long taskId)
        {
            if (McCommon.Tables.TbQuestPhase.GetOrDefault(taskId) is not QuestPhase taskConfig)
            {
                self.Logger.Error($"[acceptTaskInternal] taskId {taskId} not exist in task config");
                return false;
            }

            // 检查主线任务是否需要跳过接取
            if (ShouldSkipTaskAccept(taskId))
            {
                self.Logger.Info($"[acceptTaskInternal] taskId {taskId} skip accept in game mode {ServerConfig.Instance.GameMode}");
                return false; // 跳过接取
            }

            if (self.AddProcessTaskNode(taskConfig) is not LobbyMainTaskNode taskNode)
            {
                self.Logger.Error($"[acceptTaskInternal] taskId {taskId} add process task node failed");
                return false;
            }

            self.AcceptTaskCommon(taskNode, taskConfig, false);
            return true;
        }

        [Hotfix]
        public static bool AcceptTaskVirtual(this LobbyMainTaskContainer self, long taskId)
        {
            return false;
        }

        public static void AcceptNewTaskFromLobby(this LobbyMainTaskContainer self, pbc::RepeatedField<Gameserver.TaskGroupInfo> dsNewTaskGroups)
        {
            foreach (var taskGroup in dsNewTaskGroups)
            {
                var taskGroupConfig = McCommon.Tables.TBTaskGroupData.GetOrDefault(taskGroup.GroupID);
                if (taskGroupConfig == null)
                {
                    self.Logger.Error($"[AcceptNewTaskFromLobby] task group Id {taskGroup.GroupID} not found in TBTaskGroupData config");
                    continue;
                }

                var lobbyVersion = taskGroup.Version;
                foreach (var taskId in taskGroup.TaskIDs)
                {
                    if(!self.FlushOneLobbyTask(taskId, lobbyVersion))
                    {
                        self.AcceptTaskInternal(taskId);
                    }
                }
            }
        }

        public static void CompleteLobbyTask(this LobbyMainTaskContainer self, TaskNode taskNode)
        {
            var groupId = FindTaskGroupByTaskId(taskNode.BizId);
            self.Logger.Info($"Complete lobby task, taskId: {taskNode.BizId}, groupId: {groupId}");
            // 两个任务 ID 不一定一样，所以需要判断
            if (McCommon.Tables.TbQuestConst.UnlockTeamTaskId == taskNode.BizId)
            {
                self.Player.ComponentTeam.NewBieUnlockTeam = true;
            }
            if (McCommon.Tables.TbQuestConst.FinishNewbiePartTaskId == taskNode.BizId)
            {
                self.Player.ComponentTask.NewbieTaskFlag = true;
            }
            var taskVersion = self.TaskId2Version.GetValueOrDefault(taskNode.BizId, 0);
            LobbyServiceManager.Instance.lobbyService.SendFinishTaskToLobby(self.Player.RoleId, (int)groupId, taskVersion,
                taskNode, self.OnCompleteTaskCallback);
        }

        public static void OnCompleteTaskCallback(this LobbyMainTaskContainer self, JSONNode content, HttpStatusCode statusCode,
            int errorCode, TaskNode taskNode)
        {
            if (statusCode != HttpStatusCode.OK || errorCode != 0)
            {
                self.Logger.Error($"complete task failed {statusCode} {errorCode} {content}");
                return;
            }
            self.Logger.Info($"[OnCompleteTaskCallback] content:{content}");

            self.RemoveTask(TaskNodeIndex.InProgress, taskNode.BizId);
            self.completedNotRewardTask.AddChildNode(taskNode);

            self.Player.ProcessEvent(ConditionInnerClassId.TASK_TYPE_COMPLETE_EVENT_ID, new TaskTypeFinishInfo(taskNode.TaskType));
            EntityStaticCallback<PlayerEntity>.InvokeStaticCallback(self.Player, new TaskFinishEvent(taskNode.BizId));

            var taskServiceInnerTaskFinishedRsp = JsonConvert.DeserializeObject<TaskServiceInnerTaskFinishedRsp>(content.ToString());
            if (taskServiceInnerTaskFinishedRsp == null)
            {
                self.Logger.Error($"complete task success, but new task groups is null {content}");
                return;
            }

            //版本不一致刷新任务
            if (taskServiceInnerTaskFinishedRsp.DsNeedResendTaskGroups != null)
            {
                foreach (var taskGroup in taskServiceInnerTaskFinishedRsp.DsNeedResendTaskGroups)
                {
                    foreach (var taskId in taskGroup.TaskIds)
                    {
                        self.FlushOneLobbyTask(taskId, taskGroup.Version);
                    }
                }
            }

            if (taskServiceInnerTaskFinishedRsp.DsNewTaskGroups != null)
            {
                foreach (var taskGroup in taskServiceInnerTaskFinishedRsp.DsNewTaskGroups)
                {
                    foreach (var newTaskId in taskGroup.TaskIds)
                    {
                        self.AcceptTaskInternal(newTaskId);
                    }
                }
            }
        }

        public static void SyncTaskToLobby(this LobbyMainTaskContainer self)
        {
            var data = new TaskServiceInnerTaskGroup
            {
                RoleId = (long)self.Player.RoleId,
                DsTaskGroups = new TaskServiceRoleTaskGroup
                {
                    RoleId = (long)self.Player.RoleId,
                    TaskGroups = new List<CommonTaskGroup>()
                }
            };

            var taskGroupDic = CollectInProgressTasksForSync(self);

            foreach (var taskGroup in taskGroupDic.Values)
            {
                if (self.GroupDic.TryGetValue(taskGroup.GroupId, out var oldTaskGroup))
                {
                    taskGroup.Enable = oldTaskGroup.Enable;
                    taskGroup.State = oldTaskGroup.State;
                    taskGroup.ResourceLimit = oldTaskGroup.ResourceLimit;
                }

                if (taskGroup.State != (int)ETaskGroupState.FINISHED)
                {
                    data.DsTaskGroups.TaskGroups.Add(taskGroup);
                }
            }

            LobbyServiceManager.Instance.lobbyService.SendSyncTaskToLobby(data,
                (content, statusCode, errorCode) =>
                {
                    if (statusCode != HttpStatusCode.OK || errorCode != 0)
                    {
                        self.Logger.Error($"[SyncTaskToLobby] failed {statusCode} {errorCode} {content}");
                        return;
                    }

                    self.Logger.Info("[SyncTaskToLobby] success");
                    self.ClearTaskNode();
                });
        }

        private static Dictionary<long, CommonTaskGroup> CollectInProgressTasksForSync(LobbyMainTaskContainer self)
        {
            var taskGroupDic = new Dictionary<long, CommonTaskGroup>();
            if (self.GetChildNode((int)TaskNodeIndex.InProgress) is not DirectoryNode directoryNode ||
                directoryNode.GetChildren().Count == 0)
            {
                return taskGroupDic;
            }

            foreach (var (_, node) in directoryNode)
            {
                if (node is LobbyMainTaskNode taskNode)
                {
                    self.ProcessInProgressTaskNode(taskNode, ref taskGroupDic);
                }
            }

            return taskGroupDic;
        }

        private static void ProcessInProgressTaskNode(this LobbyMainTaskContainer self, LobbyMainTaskNode taskNode,
            ref Dictionary<long, CommonTaskGroup> taskGroupDic)
        {
            var taskGroupId = FindTaskGroupByTaskId(taskNode.BizId);
            if (taskGroupId == -1)
            {
                self.Logger.Error($"[ProcessInProgressTaskNode] taskId {taskNode.BizId} not found in task group config");
                return;
            }

            if (!self.GroupDic.TryGetValue(taskGroupId, out var taskGroup))
            {
                self.Logger.Error(
                    $"[ProcessInProgressTaskNode] groupId {taskGroupId} not found in GroupDic for taskId {taskNode.BizId}");
                return;
            }

            var existingTask = taskGroup.Tasks.FirstOrDefaultWithoutLinq(t => t.TaskId == (int)taskNode.BizId);
            if (!ShouldUpdateTask(existingTask, taskNode))
            {
                return;
            }

            if (!taskGroupDic.TryGetValue(taskGroupId, out var outerTaskGroup))
            {
                outerTaskGroup = new CommonTaskGroup
                {
                    GroupId = (int)taskGroupId,
                    Enable = taskGroup.Enable,
                    State = taskGroup.State,
                    ResourceLimit = taskGroup.ResourceLimit,
                    Tasks = new List<CommonTask>(),
                    Version = self.TaskId2Version.GetValueOrDefault(taskNode.BizId, 0)
                };
                taskGroupDic[taskGroupId] = outerTaskGroup;
            }

            if (self.CreateCommonTask(taskNode, existingTask) is CommonTask commonTask)
            {
                outerTaskGroup.Tasks.Add(commonTask);
            }
        }


        /// <summary>
        /// 创建 CommonTask
        /// 如果subTask不合法直接返回null, 放弃同步到 Lobby
        /// </summary>
        /// <param name="self"></param>
        /// <param name="taskNode"></param>
        /// <param name="existingTask"></param>
        /// <returns>CommonTask</returns>
        private static CommonTask CreateCommonTask(this LobbyMainTaskContainer self, LobbyMainTaskNode taskNode,
            CommonTask existingTask)
        {
            var commonTask = new CommonTask
            {
                TaskId = (int)taskNode.BizId,
                State = (int)ETaskState.IN_PROGRESS,
                RewardCount = 0,
                Counters = new List<CommonTaskCounter>()
            };

            if (existingTask != null)
            {
                commonTask.RewardCount = existingTask.RewardCount;
            }

            using var subTaskEnumerator = taskNode.GetEnumeratorWithoutReset();
            while (subTaskEnumerator.MoveNext())
            {
                var (taskId, node) = subTaskEnumerator.Current;
                if (node is not SubTaskNode subTaskNode)
                {
                    self.Logger.Error($"[createCommonTask] subTask {taskId} is null or invalid type");
                    return null;
                }

                var (eventId, targetValue) = self.GetEventIdAndTargetValueFromTaskId(subTaskNode.BizId);
                if (eventId < 0 || targetValue < 0)
                {
                    return null;
                }
                var taskCounter = new CommonTaskCounter
                {
                    CounterId = eventId,
                    CountValue = subTaskNode.Count,
                    TargetValue = targetValue
                };

                commonTask.Counters.Add(taskCounter);
            }

            return commonTask;
        }

        private static (int eventId, long targetValue) GetEventIdAndTargetValueFromTaskId(this LobbyMainTaskContainer self, long taskId)
        {
            if (McCommon.Tables.TbQuestPhase.GetOrDefault(taskId) is not QuestPhase taskConfig)
            {
                self.Logger.Error($"[getEventIdFromTaskId] taskId {taskId} not exist in task config");
                return (-1, -1);
            }

            return (taskConfig.TaskPhaseEndCondition, taskConfig.EndConditionParameter[0]);
        }

        private static bool ShouldUpdateTask(CommonTask existingTask, LobbyMainTaskNode taskNode)
        {
            if (existingTask == null) return true;
            if (existingTask.State != (int)ETaskState.IN_PROGRESS) return true;
            // 遍历子节点，同时检查计数器数量和值
            int counterIndex = 0;
            using var subTaskEnumerator = taskNode.GetEnumeratorWithoutReset();
            while (subTaskEnumerator.MoveNext())
            {
                var (_, node) = subTaskEnumerator.Current;
                if (node is not SubTaskNode subTaskNode) continue;
                if (counterIndex >= existingTask.Counters.Count) return true;
                if (existingTask.Counters[counterIndex].CountValue != subTaskNode.Count) return true;
                counterIndex++;
            }

            return counterIndex != existingTask.Counters.Count;
        }

        /// <summary>
        /// 根据任务ID查找对应的任务组ID
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务组ID，如果找不到返回-1</returns>
        public static long FindTaskGroupByTaskId(long taskId)
        {
            foreach (var taskGroup in McCommon.Tables.TBTaskGroupData.DataList)
            {
                if (taskGroup.Tasklist != null && taskGroup.Tasklist.ContainsWithoutLinq((int)taskId))
                {
                    return taskGroup.Id;
                }
            }
            return -1;
        }

        public static void GetLobbyMainTaskReward(this LobbyMainTaskContainer self, long taskId)
        {
            if (self.completedNotRewardTask.GetChildNode(taskId) is not LobbyMainTaskNode taskNode)
            {
                self.Logger.Info($"[GetLobbyMainTaskReward] taskId {taskId} already claimed or not completed");
                return;
            }

            var groupId = FindTaskGroupByTaskId(taskId);
            if (groupId == -1)
            {
                self.Logger.Error($"[GetLobbyMainTaskReward] taskId {taskId} not found in task group config");
                return;
            }
            var taskVersion = self.TaskId2Version.GetValueOrDefault(taskId, 0);
            LobbyServiceManager.Instance.lobbyService.SendGetLobbyMainTaskRewardReq(self.Player.RoleId, (int)groupId, taskId, taskVersion,
                (content, statusCode, errorCode) =>
                {
                    if (statusCode != HttpStatusCode.OK || errorCode != 0)
                    {
                        self.Logger.Error($"[GetLobbyMainTaskReward] failed {statusCode} {errorCode} {content}");
                        return;
                    }
                    bool isVersionLow = bool.Parse(content["isVersionLow"]);
                    self.Logger.Info($"[GetLobbyMainTaskReward] success {content}");
                    if (isVersionLow)
                    {
                        self.FlushOneLobbyTask(taskId, WeeklyZeroTimeSecs());
                    }
                    else
                    {
                        self.completedNotRewardTask.RemoveChildNode(taskNode);
                        self.AddCompletedTask(taskNode);
                    }
                }
            );
        }
    }
}
