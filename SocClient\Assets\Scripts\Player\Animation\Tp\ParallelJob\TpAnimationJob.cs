using CommonUnity.Runtime.Animation;
using System;
using Unity.Burst;
using Unity.Collections;
using Unity.Collections.LowLevel.Unsafe;
using Unity.Jobs;
using Unity.Mathematics;
using UnityEngine;
using WizardGames.Editor;
using WizardGames.Soc.Common.Character;
using WizardGames.Soc.Common.Unity.Character;

namespace WizardGames.Soc.SocClient.Player.Animation
{

#if !DISABLE_CODEUPDATE
    [BurstCompile]
#endif
    public partial struct TpAnimationJob: IJobParallelFor
    {
        [ReadOnly]
        public NativeArray<TpAnimationJobData> tpAnimationJobDataArray;
        [ReadOnly]
        public TpAniConstData tpConstData;
                
        [NativeDisableParallelForRestriction]
        [ReadOnly]
        public NativeArray<AnimProcedureCtrlJobConfGroup> MatchRules;
        
        [NativeDisableParallelForRestriction]
        [ReadOnly]
        public NativeArray<TpMaskWeightConfGroup> MaskArray;
        
        [NativeDisableParallelForRestriction]
        [ReadOnly]
        public NativeArray<TpMaskWeightConfGroup> AoMaskArray;

        [ReadOnly, NativeDisableParallelForRestriction]
        public NativeArray<SnapEventData> snapEventDataArray;

        public NativeArray<TpAnimationResultJobData> tpAnimationResultJobDataArray;
        
        [NativeDisableParallelForRestriction]
        [ReadOnly]
        public NativeArray<float> OverrideLayerTimeArray;
        
        [NativeDisableParallelForRestriction]
        [ReadOnly]
        public NativeArray<float> LocomotionLayerTimeArray;
        
        [NativeDisableParallelForRestriction]
        [ReadOnly]
        public NativeArray<bool> OverrideLayerStateLoopArray;
        
        [NativeDisableParallelForRestriction]
        [ReadOnly]
        public NativeArray<bool> LocomotionLayerSpecialArray;
        
        [NativeDisableParallelForRestriction]
        [ReadOnly]
        public NativeArray<TpAniBoneMask> LocomotionLayerWeightArray;
        
        [NativeDisableUnsafePtrRestriction]
        public IntPtr OverrideWeightAnimationCurves;
        
        [NativeDisableUnsafePtrRestriction]
        public IntPtr TurnInPlaceYawnCurves;
#if !PUBLISH && !POCO     
        [ReadOnly]
        [NativeDisableContainerSafetyRestriction]
        [NativeDisableParallelForRestriction]
        public NativeArray<TpAniPlayerDebugBone> DebugBones;
#endif
        public bool transitFromSnapshotPose;

        public int FrameNumber;

        public float WalkSpeed;
        
        public float StandCCHeight;

        public long SelfEntityId;

        public int SnapEventCount;
        
        //和TpLadderEnum一一对应
        public static readonly int[] LadderAnimIds = new[]
        {
            0 ,//TpLadderEnum.Empty
            AnimParametersTp.LocomotionLayer_Ladder_UpEnter_NameId, //TpLadderEnum.UpEnter
            AnimParametersTp.LocomotionLayer_Ladder_UpLeave_NameId, //TpLadderEnum.UpLeave
            AnimParametersTp.LocomotionLayer_Ladder_DownEnter_NameId, //TpLadderEnum.DownEnter
            AnimParametersTp.LocomotionLayer_Ladder_DownLeave_NameId, //TpLadderEnum.DownLeave
            AnimParametersTp.LocomotionLayer_Ladder_LadderMove_NameId, //TpLadderEnum.LadderMove
            AnimParametersTp.LocomotionLayer_Ladder_LadderFastDown_NameId, //TpLadderEnum.FastDown
            AnimParametersTp.LocomotionLayer_Ladder_LadderFastUp_NameId, //TpLadderEnum.FastUp
        };
        
        //  输入 0无或者抵消 1f 2b 3l 4r 5lf 6rf 7lb 8rb 八方向
        public static readonly AnimParametersTp.ELocomotionLayer[] MoveJogEnumIds = new[]
        {
            AnimParametersTp.ELocomotionLayer.LocomotionEmpty,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogFStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogBStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogLStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogRStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogFLStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogFRStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogBLStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogBRStand,
        };

        public static readonly AnimParametersTp.ELocomotionLayer[] MoveWalkEnumIds = new[]
        {
            AnimParametersTp.ELocomotionLayer.LocomotionEmpty,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkFStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkBStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkLStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkRStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkFLStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkFRStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkBLStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkBRStand
        };
        
        //  输入 0无或者抵消 1f 2b 3l 4r 5lf 6rf 7lb 8rb 八方向 
        public static readonly AnimParametersTp.ELocomotionLayer[] MoveCrouchEnumIds = new[]
        {
            AnimParametersTp.ELocomotionLayer.LocomotionEmpty,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchFStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchBStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchLStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchRStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchFLStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchFRStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchBLStand,
            AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchBRStand,
  
        };
        
        //
        public static readonly (AnimParametersTp.ELocomotionLayer,int)[] MoveJogEnumCache = new[]
        {
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogFStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_JogFStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogLStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_JogLStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogRStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_JogRStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogBStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_JogBStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogFLStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_JogFLStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogFRStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_JogFRStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogBLStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_JogBLStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogBRStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_JogBRStand_NameId),
        };
        
        public static readonly (AnimParametersTp.ELocomotionLayer,int)[] MoveWalkEnumCache = new[]
        {
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkFStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_WalkFStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkLStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_WalkLStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkRStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_WalkRStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkBStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_WalkBStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkFLStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_WalkFLStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkFRStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_WalkFRStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkBLStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_WalkBLStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkBRStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_WalkBRStand_NameId),
        };
        
        public static readonly (AnimParametersTp.ELocomotionLayer,int)[] MoveCrouchEnumCache = new[]
        {
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchFStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_CrouchFStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchLStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_CrouchLStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchRStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_CrouchRStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchBStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_CrouchBStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchFLStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_CrouchFLStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchFRStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_CrouchFRStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchBLStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_CrouchBLStand_NameId),
            (AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchBRStand,
                AnimParametersTp.LocomotionLayer_HipLocomotion_Locomotion_CrouchBRStand_NameId),
        };
        
        public static readonly AnimParametersTp.ELocomotionLayer[] LadderAnimEnumIds = new[]
        {
            AnimParametersTp.ELocomotionLayer.LocomotionEmpty,//TpLadderEnum.Empty
            AnimParametersTp.ELocomotionLayer.Ladder_UpEnter, //TpLadderEnum.UpEnter
            AnimParametersTp.ELocomotionLayer.Ladder_UpLeave, //TpLadderEnum.UpLeave
            AnimParametersTp.ELocomotionLayer.Ladder_DownEnter, //TpLadderEnum.DownEnter
            AnimParametersTp.ELocomotionLayer.Ladder_DownLeave, //TpLadderEnum.DownLeave
            AnimParametersTp.ELocomotionLayer.Ladder_LadderMove, //TpLadderEnum.LadderMove
            AnimParametersTp.ELocomotionLayer.Ladder_LadderFastDown, //TpLadderEnum.FastDown
            AnimParametersTp.ELocomotionLayer.Ladder_LadderFastUp, //TpLadderEnum.FastUp
        };
#if DEVELOPMENT_BUILD
        [NotBurstCompatible]
#endif
        public void Execute(int index)
        {
            var jobData =  tpAnimationJobDataArray[index];
            var resultData = tpAnimationResultJobDataArray[index];
            BeforeUpdate(ref resultData);
            UpdateLocomotionLayer(ref jobData, ref resultData, ref resultData.AnimParams, in tpConstData);
            UpdatePoseLayer(ref jobData, ref resultData, ref resultData.AnimParams, in tpConstData);
            UpdateAdditiveLayer(ref jobData, ref resultData, ref resultData.AnimParams, in tpConstData, in snapEventDataArray);
            UpdateOverrideLayer(ref jobData, ref resultData, ref resultData.AnimParams, in tpConstData, in snapEventDataArray);
            UpdateAOLayer(ref jobData, ref resultData, ref resultData.AnimParams, in tpConstData);
            
            UpdateLocomotionWeightLayer(ref jobData, ref resultData, ref resultData.AnimParams, in tpConstData);
            UpdatePoseWeightLayer(ref jobData, ref resultData, ref resultData.AnimParams,in tpConstData);
            UpdateOverrideLayerWeightLayer(ref jobData, ref resultData, ref resultData.AnimParams, in tpConstData, OverrideWeightAnimationCurves);
            UpdateAoWeightLayer(ref jobData, ref resultData, ref resultData.AnimParams,in tpConstData);
            AfterUpdate(ref jobData, ref resultData, ref resultData.AnimParams,in tpConstData);
            
            var flush_mem = Animator.GetFlushMemUnSafe(jobData.AnimatorInstanceId);
            resultData.AnimParams.ApplyToAnimator(ref flush_mem);
            tpAnimationResultJobDataArray[index] = resultData;
        }

        private void BeforeUpdate(ref TpAnimationResultJobData resultJobData)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            if (playerLocalData.LogicFrame)
            {
                playerLocalData.SetWantLogicToRenderFrame(false, true);
            }
            if(!resultJobData.PlayerLocalData.RenderFrame)
                return;
            playerLocalData.EnableInterpolation = true;
            playerLocalData.AlwaysUpdate = false;
        }

        private bool Approximately(float a, float b, float epsilon = 0.001f)
        {
            return math.abs(a - b) < epsilon;
        }

        private float Angle(float3 from, float3 to)
        {
            // sqrt(a) * sqrt(b) = sqrt(a * b) -- valid for real numbers
            var denominator = (float)math.sqrt( math.lengthsq(from) * math.lengthsq(to));
            if (denominator < TpAniConstData.KEpsilonNormalSqrt)
                return 0f;
            var dot = math.clamp(math.dot(from, to) / denominator, -1F, 1F);
            return math.degrees(math.acos(dot));
        }
        
        private float SignedAngle(float3 from, float3 to, float3 axis)
        {
            var unsignedAngle = Angle(from, to);
            var cross = math.cross(from, to);
            var sign = math.sign(axis.x * cross.x + axis.y * cross.y + axis.z * cross.z);
            return unsignedAngle * sign;
        }
        
        private float InverseLerp(float a, float b, float value)
        {
            if (math.abs(a - b) < TpAniConstData.KEpsilonNormalSqrt)
                return 0f;
            return math.clamp((value - a) / (b - a), 0f, 1f);
        }
        
        private float SmoothStep(float from, float to, float t)
        {
            t = math.clamp(t, 0.0f,1.0f);
            t = -2.0f * t * t * t + 3.0f * t * t;
            return to * t + from * (1f - t);
        }
        
        private float Repeat(float t, float length)
        {
            return math.clamp(t - math.floor(t / length) * length, 0.0f, length);
        }
        
        private float DeltaAngle(float current, float target)
        {
            float delta = Repeat((target - current), 360.0f);
            if (delta > 180.0f)
                delta -= 360.0f;
            return delta;
        }
        
        private float Clamp01(float value)
        {
            return math.clamp(value, 0.0f, 1.0f);
        }

        private float Lerp(float a, float b, float t)
        {
            return a + (b - a) * Clamp01(t);
        }
        
        /// <summary>
        /// 将角度限制在-180到180
        /// </summary>
        /// <returns></returns>
        private float ClampAngleTo180(float angle)
        {
            //转到0-360
            angle = (angle % 360 + 360) % 360;
            //转到-180 ~180
            angle = angle > 180 ? angle - 360 : angle;
            return angle;
        }
        
        private void AfterUpdate(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            ref AnimParametersTp animParams, in TpAniConstData constData)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            if (playerLocalData.LogicFrame)
            {
                playerLocalData.SetLogicToRenderFrame(playerLocalData.WantLogicToRenderFrame);
            }

            if(!playerLocalData.RenderFrame)
                return;
            ref var heldItemData =ref jobData.HeldItemData;
            playerLocalData.TpAniWeaponId = heldItemData.CurrentWeaponTableId;
            playerLocalData.ApplyInstanceId = jobData.NowInstanceId;
            playerLocalData.UpdateIndex ++;
            playerLocalData.UpdateIndex = math.min(playerLocalData.UpdateIndex, 10);
            playerLocalData.SwitchTpAnimator = false;
            playerLocalData.OverrideLayerStateChange = false;
            playerLocalData.BowStateChange = false;
            playerLocalData.TipOver = false;
            playerLocalData.LogicWantToIdleState = false;
            playerLocalData.LogicWantToIdleStateTransitionTime = 0f;
        }

        /// <summary>
        /// 获取状态的时长
        /// </summary>
        /// <param name="jobData"></param>
        /// <returns></returns>
        private float GetStateNormalizedTime(ref TpAnimationJobData jobData, int layerIndex, int stateIndex)
        {
            var animatorInstanceId = jobData.AnimatorInstanceId; 
            Animator.GetAnimatorStateInfoUnSafe(animatorInstanceId, layerIndex, StateInfoIndex.CurrentState, out var stateInfo);
            if(stateInfo.shortNameHash == stateIndex)
                return stateInfo.normalizedTime;
            return 0f;
        }
    }
}