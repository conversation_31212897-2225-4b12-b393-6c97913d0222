#if UNITY_EDITOR&& SOC_CLIENT
using Animation.Event;
using System;
using System.Collections.Generic;
#if SOC_CLIENT
using Sirenix.OdinInspector;
#endif
using UnityEditor;
using UnityEngine;
using UnityEngine.Serialization;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Unity;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.Manager;
using WizardGames.Soc.SocClient.Manager;

namespace Character.Resource.AnimRule.Data
{
    /// <summary>
    /// 关键字的匹配规则
    /// </summary>
    public enum Anim_KeyWordMatchMode
    {
        None,
        FullMatch,
        Contains,
        EndsWidth,
    }

    [System.Serializable]
    public class Anim_EventDataInfo
    {
        /// <summary>
        /// 事件帧数
        /// </summary>
        public int Frame = 0;

        /// 事件函数
        public string FunctionName = "FireAnimEvent";

        /// 事件参数
        public string StringParameter = "";

        /// 事件float参数
        public float floatParameter = 0;

        /// 事件int参数
        [CustomValueDrawer("MyEventGui")]
        public int intParameter = 0;

        /// 是否是音频事件
        [ReadOnly]
        public bool isAudio = false;
        
        /// 是否是武器动画事件
        public bool isWeapon = false;

        /// <summary>
        /// 拷贝时是否清除
        /// </summary>
        public bool ClearWhenCopy = false;
        
        private float MyEventGui(int value, GUIContent label)
        {
            var newval = EditorGUILayout.IntField(label,value);
            
            if (Mc.Tables == null || Mc.Tables.TbChracterParameter == null)
            {
                //var configDir = Application.dataPath.Replace("SocClient/Assets",
                //    "SocCommon/Soc.Common/Soc.Common/Resources/Data/Client");
                Mc.Tables = new(PathMgr.ClientDataPath);
                McCommonUnity.Res = new MgrRes();
            }

            var config = Mc.Tables.TbAnimEventConfig.GetOrDefault(newval);
            if (config == null)
            {
                EditorGUILayout.LabelField($"未从表中找到匹配的事件Id:{newval} (请检查表格，或者重新执行导表和刷新后确认)",EditorStyles.boldLabel);
               
                if( GUILayout.Button("刷新表格"))
                {
                    Mc.Tables = new(PathMgr.ClientDataPath);
                    McCommonUnity.Res = new MgrRes();
                }
                
                return newval;
            }

            var animnotify = AnimNotifyModule.BuildAnimNotify(newval);
            if (animnotify == null)
            {
                EditorGUILayout.LabelField($"表id找到了,但是未找到匹配的动画事件 Id:{newval} (请检查表格，或者重新执行导表和刷新，或者和程序确认是否支持该类型) ",EditorStyles.boldLabel);
                if( GUILayout.Button("刷新表格"))
                {
                    Mc.Tables = new(PathMgr.ClientDataPath);
                    McCommonUnity.Res = new MgrRes();
                }
                return newval;
            }

            if (animnotify is AnimNotify_VisibleBehaviour)
            {
                EditorGUILayout.LabelField($"{animnotify.GetType().Name} 显隐事件 : {config.EventType} target:{config.VisibleTarget}",EditorStyles.boldLabel);
            }
            else if (animnotify is AnimNotify_WeaponVisBehaviour)
            {
                EditorGUILayout.LabelField($"{animnotify.GetType().Name} 武器显隐事件 : {config.EventType}",EditorStyles.boldLabel);
            }
            else if (animnotify is AnimNotify_AudioBehaviour)
            {
                EditorGUILayout.LabelField($"{animnotify.GetType().Name} 音频事件 : {config.EventType} 音频:{config.Audio}",EditorStyles.boldLabel);
            }
            else if (animnotify is AnimNotify_FootStep)
            {
                EditorGUILayout.LabelField($"{animnotify.GetType().Name} 脚步事件 : {config.EventType} ",EditorStyles.boldLabel);
            }
            else if (animnotify is AnimNotify_IKChange)
            {
                EditorGUILayout.LabelField($"{animnotify.GetType().Name} IK切换事件 : {config.EventType} target:{config.SwitchIKTarget}",EditorStyles.boldLabel);
            }
            else if (animnotify is AnimNotify_EffectBehaviour)
            {
                EditorGUILayout.LabelField($"{animnotify.GetType().Name} 特效事件 : {config.EventType} 特效Id:{config.EffectId} 特效类型:{config.EffectType} 特效挂点:{config.EffectAttach}",EditorStyles.boldLabel);
            }
            else
            {
                EditorGUILayout.LabelField($"{animnotify.GetType().Name} 事件 : {config.EventType}",EditorStyles.boldLabel);
            }
   
            return newval;
        }
    }

    [System.Serializable]
    public class Anim_CurveInfo
    {
        /// <summary>
        /// 配置曲线名称
        /// </summary>
        [ValueDropdown("GetCurveOptions")]
        public string CurveName;

        public bool Export = true;

        [Tooltip("拷贝时是否清除")]
        public bool ClearWhenCopy = false;

        /// <summary>
        /// 曲线
        /// </summary>
        public AnimationCurve Curve = AnimationCurve.Linear(0, 0, 1, 1);

        private string[] GetCurveOptions()
        {
            return new string[] {"LeftIKAutoCurve", "RightIKAutoCurve", "HorseSpeacial_WeightCurve"};
        }
    }
    
    [System.Serializable]
    public class Anim_CopySameClipInfo
    {
            /// <summary>
            /// 同源片段名
            /// </summary>
            public string sourceClip;
            /// <summary>
            /// 拷贝片段名
            /// </summary>
            public List<string> CopyClips = new List<string>();
    }

    [System.Serializable]
    public class Anim_CurveExtensionInfo
    {
        /// <summary>
        /// 配置曲线名称
        /// </summary>
        public string CurveExtension;

        /// <summary>
        /// 曲线
        /// </summary>
        public AnimationCurve Curve = AnimationCurve.Linear(0, 0, 1, 1);
    }

    [System.Serializable]
    public class Anim_MaskInfo
    {
        /// <summary>
        /// 动画关键字
        /// </summary>
        public string AnimKeyWord;

        /// <summary>
        /// 动画遮罩
        /// </summary>
        public AvatarMask Mask;

        //只是设置诶默认值，而不是移除
        public bool JustClean = false;

        /// <summary>
        /// 是否是tplocomotion层，需要对多个分层的进行配置mask
        /// </summary>
        public bool TpLocomotion;

        /// <summary>
        /// 按照locomotion层的顺序一次配置mask，没配就用默认的，配了就Override
        /// </summary>
        public SerializableDictionary<string, AvatarMask> TpLocomotionMasks = new SerializableDictionary<string, AvatarMask>()
        {
            {"LocomotionLayer",null},{"LocomotionSpineLayer",null},{"LocomotionWeaponLayer",null},{"LocomotionLeftArmLayer",null},{"LocomotionRightArmLayer",null},
        };
    }

    [System.Serializable]
    public class Anim_DataInfo : Anim_AudioEventDataInfo
    {
        /// <summary>
        /// Fp false  Tp true
        /// </summary>
        public bool PersonType;

        /// <summary>
        /// 曲线信息列表
        /// </summary>
        public List<Anim_CurveInfo> Curves = new List<Anim_CurveInfo>();

        /// <summary>
        /// 是否需要行为表现时移动权重曲线
        /// </summary>
        public bool NeedActionLocomotionWeight = false;
        
        /// <summary>
        /// 是否需要行为表现时Ao权重曲线
        /// </summary>
        public bool NeedActionAoWeight = false;
        
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jog")]
#endif
        public AnimationCurve JogDynamic_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jog")]
#endif
        public AnimationCurve JogSpine_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jog")]
#endif
        public AnimationCurve JogSpine1_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jog")]
#endif
        public AnimationCurve JogSpine2_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jog")]
#endif
        public AnimationCurve JogLClavicle_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jog")]
#endif
        public AnimationCurve JogHead_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jog")]
#endif
        public AnimationCurve JogRClavicle_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jog")]
#endif
        public AnimationCurve JogWeapon_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jog")]
#endif
        public AnimationCurve JogLeftArm_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jog")]
#endif
        public AnimationCurve JogRightArm_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Sprint")]
#endif
        public AnimationCurve SprintDynamic_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Sprint")]
#endif
        public AnimationCurve SprintSpine_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Sprint")]
#endif
        public AnimationCurve SprintSpine1_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Sprint")]
#endif
        public AnimationCurve SprintSpine2_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Sprint")]
#endif
        public AnimationCurve SprintLClavicle_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Sprint")]
#endif
        public AnimationCurve SprintHead_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Sprint")]
#endif
        public AnimationCurve SprintRClavicle_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Sprint")]
#endif
        public AnimationCurve SprintWeapon_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Sprint")]
#endif
        public AnimationCurve SprintLeftArm_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Sprint")]
#endif
        public AnimationCurve SprintRightArm_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jump")]
#endif
        public AnimationCurve JumpDynamic_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jump")]
#endif
        public AnimationCurve JumpSpine_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jump")]
#endif
        public AnimationCurve JumpSpine1_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jump")]
#endif
        public AnimationCurve JumpSpine2_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jump")]
#endif
        public AnimationCurve JumpLClavicle_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jump")]
#endif
        public AnimationCurve JumpHead_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jump")]
#endif
        public AnimationCurve JumpRClavicle_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jump")]
#endif
        public AnimationCurve JumpWeapon_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jump")]
#endif
        public AnimationCurve JumpLeftArm_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Jump")]
#endif
        public AnimationCurve JumpRightArm_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Swim")]
#endif
        public AnimationCurve SwimDynamic_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Swim")]
#endif
        public AnimationCurve SwimSpine_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Swim")]
#endif
        public AnimationCurve SwimSpine1_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Swim")]
#endif
        public AnimationCurve SwimSpine2_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Swim")]
#endif
        public AnimationCurve SwimLClavicle_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Swim")]
#endif
        public AnimationCurve SwimHead_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Swim")]
#endif
        public AnimationCurve SwimRClavicle_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Swim")]
#endif
        public AnimationCurve SwimWeapon_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Swim")]
#endif
        public AnimationCurve SwimLeftArm_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Swim")]
#endif
        public AnimationCurve SwimRightArm_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_SwimIdle"), ShowIf("PersonType")]
#endif
        public AnimationCurve SwimIdleDynamic_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_SwimIdle"), ShowIf("PersonType")]
#endif
        public AnimationCurve SwimIdleSpine_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_SwimIdle"), ShowIf("PersonType")]
#endif
        public AnimationCurve SwimIdleSpine1_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_SwimIdle"), ShowIf("PersonType")]
#endif
        public AnimationCurve SwimIdleSpine2_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_SwimIdle"), ShowIf("PersonType")]
#endif
        public AnimationCurve SwimIdleLClavicle_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_SwimIdle"), ShowIf("PersonType")]
#endif
        public AnimationCurve SwimIdleHead_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_SwimIdle"), ShowIf("PersonType")]
#endif
        public AnimationCurve SwimIdleRClavicle_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_SwimIdle"), ShowIf("PersonType")]
#endif
        public AnimationCurve SwimIdleWeapon_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_SwimIdle"), ShowIf("PersonType")]
#endif
        public AnimationCurve SwimIdleLeftArm_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_SwimIdle"), ShowIf("PersonType")]
#endif
        public AnimationCurve SwimIdleRightArm_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Crouch"), ShowIf("PersonType")]
#endif
        public AnimationCurve CrouchDynamic_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Crouch"), ShowIf("PersonType")]
#endif
        public AnimationCurve CrouchSpine_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Crouch"), ShowIf("PersonType")]
#endif
        public AnimationCurve CrouchSpine1_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Crouch"), ShowIf("PersonType")]
#endif
        public AnimationCurve CrouchSpine2_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Crouch"), ShowIf("PersonType")]
#endif
        public AnimationCurve CrouchLClavicle_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Crouch"), ShowIf("PersonType")]
#endif
        public AnimationCurve CrouchHead_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Crouch"), ShowIf("PersonType")]
#endif
        public AnimationCurve CrouchRClavicle_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Crouch"), ShowIf("PersonType")]
#endif
        public AnimationCurve CrouchWeapon_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Crouch"), ShowIf("PersonType")]
#endif
        public AnimationCurve CrouchLeftArm_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Crouch"), ShowIf("PersonType")]
#endif
        public AnimationCurve CrouchRightArm_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Ladder"), ShowIf("PersonType")]
#endif
        public AnimationCurve LadderDynamic_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Ladder"), ShowIf("PersonType")]
#endif
        public AnimationCurve LadderSpine_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Ladder"), ShowIf("PersonType")]
#endif
        public AnimationCurve LadderSpine1_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Ladder"), ShowIf("PersonType")]
#endif
        public AnimationCurve LadderSpine2_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Ladder"), ShowIf("PersonType")]
#endif
        public AnimationCurve LadderLClavicle_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Ladder"), ShowIf("PersonType")]
#endif
        public AnimationCurve LadderHead_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Ladder"), ShowIf("PersonType")]
#endif
        public AnimationCurve LadderRClavicle_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Ladder"), ShowIf("PersonType")]
#endif
        public AnimationCurve LadderWeapon_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Ladder"), ShowIf("PersonType")]
#endif
        public AnimationCurve LadderLeftArm_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight_Ladder"), ShowIf("PersonType")]
#endif
        public AnimationCurve LadderRightArm_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionLocomotionWeight"), ShowIf("PersonType")]
#endif
        public AnimationCurve ActionState_ActionWeightCurve = AnimationCurve.Linear(0, 1, 1, 1);
        
#if SOC_CLIENT
        [FoldoutGroup("ActionAoWeight"), ShowIf("PersonType")]
#endif
        public AnimationCurve AoDynamic_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionAoWeight"), ShowIf("PersonType")]
#endif
        public AnimationCurve AoSpine_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionAoWeight"), ShowIf("PersonType")]
#endif
        public AnimationCurve AoSpine1_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionAoWeight"), ShowIf("PersonType")]
#endif
        public AnimationCurve AoSpine2_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionAoWeight"), ShowIf("PersonType")]
#endif
        public AnimationCurve AoHead_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
#if SOC_CLIENT
        [FoldoutGroup("ActionAoWeight"), ShowIf("PersonType")]
#endif
        public AnimationCurve AoHand_ActionWeightCurve = AnimationCurve.Linear(0, 0, 1, 0);
    }



    [System.Serializable]
    public class Anim_EditorRuleDefine
    {
        /// <summary>
        /// 叠加动画Base关键字
        /// </summary>
        public string BaseKeyWord;

        /// <summary>
        /// 需要叠加的动画关键字
        /// </summary> 
        public SerializableDictionary<string, Anim_AdditiveRuleDefine> AnimKeyWords =
            new SerializableDictionary<string, Anim_AdditiveRuleDefine>();
    }

    [System.Serializable]
    public class Anim_AdditiveRuleDefine
    {
        /// <summary>
        /// 叠加动画Base时间
        /// </summary>
        public float BaseTime = 0;
        /// <summary>
        /// 是否是tp移动层，自动拆分叠加目标
        /// </summary>
        public bool TpLocomotion;
    }
    
    /// <summary>
    /// meshspace空间
    /// </summary>
    [System.Serializable]
    public class Anim_MeshSpaceDefine
    {
            /// <summary>
            /// 叠加动画Base关键字
            /// </summary>
            [SerializeField]
            public string BaseKeyWord;

            /// <summary>
            /// basekey的clip的md5
            /// </summary>
            [SerializeField, HideInInspector]
            public string ApplyBaseKeyWordMd5;
            
            /// <summary>
            /// 需要叠加的动画关键字
            /// </summary> 
            public SerializableDictionary<string, Anim_MeshSpaceSubDefine> AnimKeyWords =
                    new SerializableDictionary<string, Anim_MeshSpaceSubDefine>();

            [SerializeField]
            public List<Anim_MeshSpaceChangeDefine> ChangeLists = new List<Anim_MeshSpaceChangeDefine>();
    }
    
    /// <summary>
    /// meshspace空间
    /// </summary>
    [System.Serializable]
    public class Anim_MeshSpaceOverrideDefine
    {
            /// <summary>
            /// 叠加动画Base关键字
            /// </summary>
            public string BaseKeyWord;
            
            /// <summary>
            /// 需要叠加的动画关键字
            /// </summary>
            public SerializableDictionary<string, Anim_MeshSpaceOverrideSubDefine> AnimKeyWords =
                    new SerializableDictionary<string, Anim_MeshSpaceOverrideSubDefine>();
    }
    
    /// <summary>
    /// meshspace空间子对象
    /// </summary>
    [System.Serializable]
    public class Anim_MeshSpaceSubDefine
    {
            [SerializeField, HideInInspector]
            public string ApplyAnimMd5;
        
            /// <summary>
            /// 新的叠加源
            /// </summary>
            /// <returns></returns>
            [SerializeField]
            public string AddPose;
            [SerializeField, HideInInspector]
            public string ApplyAddPoseMd5;

            /// <summary>
            /// 叠加源的帧数
            /// </summary>
            [SerializeField]
            public float AddPoseTime;

            /// <summary>
            /// 要开始覆盖的骨骼名，覆盖叠加只能配一种
            /// </summary>
            public List<string> OverrideBranchFiltersBoneName;
            [HideInInspector]
            public List<string> ApplyOverrideBranchFiltersBoneName;
            
            /// <summary>
            /// 要开始叠加的骨骼名，覆盖叠加只能配一种
            /// </summary>
            public List<string> AdditiveBranchFiltersBoneName;
            [HideInInspector]
            public List<string> ApplyAdditiveBranchFiltersBoneName;
    }
    
    /// <summary>
    /// meshspace空间子对象
    /// </summary>
    [System.Serializable]
    public class Anim_MeshSpaceOverrideSubDefine
    {
            /// <summary>
            /// 开始控制覆盖的骨骼名
            /// </summary>
            public string BranchFiltersBoneName;
    }
    
    /// <summary>
    /// meshspace空间子对象
    /// </summary>
    [System.Serializable]
    public class Anim_MeshSpaceChangeDefine
    {
            /// <summary>
            /// tp要替换掉基础pose
            /// </summary>
            /// <returns></returns>
            public string ChangePoseWord;

            /// <summary>
            /// tp替换的源数据
            /// </summary>
            public string ChangePoseSource;
    }
    
    
    
    [System.Serializable]
    public class Anim_CopyDefine
    {
        /// <summary>
        /// 拷贝源名字
        /// </summary>
        public string SourceClipName;

        /// <summary>
        /// 叠加动画Base时间
        /// </summary>
        public string DestinationClipName ;

        /// <summary>
        /// 如果不存在，则创建，如果已经有了就不需要创了
        /// </summary>
        public bool CreateIfNotExist = false;

        /// <summary>
        /// tp 移动层，1个得拷贝出4个
        /// </summary>
        public bool TpLocomotion;
    }
    
    [System.Serializable]
    public class Anim_AnimPoseData
    {
        /// <summary>
        /// 拷贝源名字
        /// </summary>
        public string SourceClipName;

        /// <summary>
        /// 叠加动画Base名字
        /// </summary>
        public string DestinationClipName ;

        /// <summary>
        /// 提取的帧数
        /// </summary>
        public int ExtractFrame;

        /// <summary>
        /// 是否是生成源片段同等时长的片段，默认是一帧的时长
        /// </summary>
        public bool UseSourceClipTime;

        /// <summary>
        /// 主动设置时长，-1代表用一帧或者是UseSourceClipTime的
        /// </summary>
        public float SetLength = -1f;
    }

    /// <summary>
    /// 武器动画曲线的数据信息
    /// </summary>
    public struct WpnCurveData
    {
        public WpnCurveDataKey Key;
        public bool Constant;
        public float ConstantValue;
        public bool Diff;
        public EWpnCurveType curveType;
    }

    public enum EWpnCurveType
    {
        Pos,
        Rot,
        Scale,
    }

    public struct WpnCurveDataKey : IEquatable<WpnCurveDataKey>
    {
        public string BoneName;
        public string PropertyName;
        
        public bool Equals(WpnCurveDataKey other)
        {
            return BoneName == other.BoneName && PropertyName == other.PropertyName;
        }
    }
    
    public struct WpnCurveDataKeyByType : IEquatable<WpnCurveDataKeyByType>
    {
        public string BoneName;
        public EWpnCurveType BoneType;
        
        public bool Equals(WpnCurveDataKeyByType other)
        {
            return BoneName == other.BoneName && BoneType == other.BoneType;
        }
    }
    
    
    public enum NameMatchType
    {
        Exact = 0,
        Regex,
    }
    
    [Serializable]
    public class Anim_CurveCheckInfo
    {
        public string name;
        public bool isPath;
        public NameMatchType matchType;
        public bool exist;
        public bool showException;
        
        protected bool Equals(Anim_CurveCheckInfo other)
        {
            return name == other.name && isPath == other.isPath && matchType == other.matchType && exist == other.exist;
        }

        public override bool Equals(object obj)
        {
            if (ReferenceEquals(null, obj))
            {
                return false;
            }
            if (ReferenceEquals(this, obj))
            {
                return true;
            }
            if (obj.GetType() != this.GetType())
            {
                return false;
            }
            return Equals((Anim_CurveCheckInfo)obj);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(name, isPath, (int)matchType, exist);
        }
    }
}
#endif