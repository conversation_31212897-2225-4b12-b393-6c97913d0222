using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using System.Text;
using UnityEngine;
using UnityEngine.Scripting;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.Utility;

namespace WizardGames.Soc.Common.Unity.Event
{
    /// <summary>
    /// 事件系统, 强类型/全局事件 实现<br/>
    /// 使用 FireMsgAtOnce 立即触发事件<br/>
    /// 使用 FireMsg 经过一帧事件合并后使用最新的参数触发事件
    /// </summary>
    public class MgrMsg : MgrBase
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(MgrMsg));

#if DEBUG
        /// <summary>
        /// 事件数量监控
        /// </summary>
        private Dictionary<EventDefine, int> msgCount = null;
        private string msgLastKey = null;
#endif

        private EventTag eventTag = EventTag.Lobby;
        private Dictionary<EventDefine, int> clearEventCount = new();
        private Dictionary<EventDefine, List<InvokableActionBase>> registerEvents = new();
        private Dictionary<EventDefine, EventParamBase> eventParams = new();
        private Queue<EventParamBase> fireQueue = new();

        public void RecordMsgCount(string key)
        {
#if DEBUG
            var curCount = new Dictionary<EventDefine, int>();
            if (null != registerEvents && registerEvents.Count > 0)
            {
                foreach (var pair in registerEvents)
                {
                    int count = 0;
                    if (null != pair.Value) count = pair.Value.Count;
                    if (!curCount.ContainsKey(pair.Key)) curCount[pair.Key] = 0;
                    curCount[pair.Key] += count;
                }
            }
            StringBuilder countInfo = null;
            bool hasPreRec = null != msgCount;
            int hasDiff = 0;

            if (hasPreRec)
            {
                foreach (var pair in curCount)
                {
                    int count = msgCount.ContainsKey(pair.Key) ? msgCount[pair.Key] : 0;
                    int sub = pair.Value - count;
                    if (0 == sub) continue;
                    hasDiff += sub;
                    if (null == countInfo) countInfo = new StringBuilder();
                    countInfo.Append($"{pair.Key}={(sub > 0 ? "+" : string.Empty)}{sub},");
                }

                foreach (var pair in msgCount)
                {
                    if (!curCount.ContainsKey(pair.Key))
                    {
                        if (null == countInfo) countInfo = new StringBuilder();
                        countInfo.Append($"{pair.Key}=-{pair.Value},");
                        hasDiff -= pair.Value;
                    }
                }
            }
            if (null != countInfo)
            {
                if (hasDiff > 0)
                {
                    logger.ErrorFormat("[MgrMsg] 事件系统在进出局内前后计数不同:{0}->{1}, 请检查是否有局内Manager注册的事件在OnExitWorld中没有正确移除, 详细信息: \n{2}", msgLastKey ?? "unknow", key, countInfo);
                }
                else if (hasDiff < 0)
                {
                    logger.ErrorFormat("[MgrMsg] 事件系统在进出局内前后计数不同:{0}->{1}, 当前事件数量比之前事件数量减少, 详细信息: \n{2}", msgLastKey ?? "unknow", key, countInfo);
                }
            }
            if (hasPreRec)
            {
                msgLastKey = null;
                msgCount = null;
            }
            else
            {
                msgLastKey = key;
                msgCount = curCount;
            }
#endif
        }

        /// <summary>
        /// 设置当前事件系统的标记
        /// </summary>
        public void MarkTag(EventTag tag)
        {
            var oldTag = eventTag;
            eventTag = tag;
            logger.InfoFormat("[MgrMsg] 事件系统标记: {0} -> {1}", oldTag, tag);
        }

        /// <summary>
        /// 清理带有执行标记的所有事件
        /// </summary>
        public void ClearEventsOfTag(EventTag tag)
        {
            clearEventCount.Clear();
            var bridgeList = GetActionListFromPool();
            foreach (var pair in registerEvents)
            {
                if (0 == pair.Value.Count) continue;
                bridgeList.Clear();
                foreach (var invokable in pair.Value)
                {
                    if (tag == invokable.eventTag)
                    {
                        invokable.Clear();
                        continue;
                    }
                    bridgeList.Add(invokable);
                }
                int subCount = pair.Value.Count - bridgeList.Count;
                if (subCount > 0) clearEventCount[pair.Key] = subCount;
                pair.Value.Clear();
                pair.Value.AddRange(bridgeList);
            }
            ReturnActionListToPool(bridgeList);
            StringBuilder logInfo = null;
            foreach (var pair in clearEventCount)
            {
                CheckForEventRemoval(pair.Key);
                if (null == logInfo) logInfo = new StringBuilder();
                logInfo.Append($"{pair.Key} x{pair.Value}, ");
            }
            if (null != logInfo) logger.InfoFormat("[MgrMsg] 清除带有{0}标记的残留事件: {1}", tag, logInfo);
        }

        private List<InvokableActionBase> GetRegisteredActionList(EventDefine eventName)
        {
            if (!registerEvents.TryGetValue(eventName, out var list)) return null;
            return list;
        }

        private List<InvokableActionBase> GetActionListFromPool()
        {
            var list = Pool.GetList<InvokableActionBase>();
            return list;
        }

        private void ReturnActionListToPool(List<InvokableActionBase> list)
        {
            Pool.ReleaseList(list);
        }

        private List<InvokableActionBase> CloneThenGetActionList(EventDefine eventName)
        {
            List<InvokableActionBase> invokableActionBaseList = GetRegisteredActionList(eventName);
            if (null == invokableActionBaseList) return null;
            var toInvokeList = GetActionListFromPool();
            toInvokeList.AddRange(invokableActionBaseList);
            return toInvokeList;
        }

        private void CheckForEventRemoval(EventDefine eventName, List<InvokableActionBase> actionList = null)
        {
            if (null == actionList) actionList = GetRegisteredActionList(eventName);
            if (null != actionList && actionList.Count > 0) return;
            ReturnActionListToPool(actionList);
            registerEvents.Remove(eventName);
        }

        public override void CleanUp()
        {
            base.CleanUp();
            fireQueue.Clear();
            eventParams.Clear();
        }

        /// <summary>
        /// 事件系统每帧收集非立即触发的事件, 并始终采用最新的参数,
        /// 事件系统收集的间隔取决于外部调用此函数的频率
        /// </summary>
        public void DoUpdate()
        {
            if (0 == eventParams.Count) return;
            foreach (var pair in eventParams)
            {
                fireQueue.Enqueue(pair.Value);
            }
            eventParams.Clear();
            while (fireQueue.Count > 0)
            {
                var eventParam = fireQueue.Dequeue();
                try
                {
                    eventParam.Fire(this);
                    eventParam.ReturnToPool();
                }
                catch (Exception e)
                {
                    logger.ErrorFormat("[MgrMsg] 事件触发异常: {0}, {1}", eventParam.Msg, e);
                }
            }
        }

        #region AddListener

        private void AddListener(EventDefine eventName, InvokableActionBase invokableAction)
        {
            if (!registerEvents.TryGetValue(eventName, out var invokableActionBaseList))
            {
                invokableActionBaseList = GetActionListFromPool();
                registerEvents[eventName] = invokableActionBaseList;
            }
            invokableActionBaseList.Add(invokableAction);
        }

        public void AddListener(EventDefine eventName, Action action)
        {
            InvokableAction invokableAction = Pool.Get<InvokableAction>();
            invokableAction.Initialize(eventTag, action);
            AddListener(eventName, invokableAction);
        }

        public void AddListener<T1>(EventDefine eventName, Action<T1> action)
        {
            InvokableAction<T1> invokableAction = Pool.Get<InvokableAction<T1>>();
            invokableAction.Initialize(eventTag, action);
            AddListener(eventName, invokableAction);
        }

        public void AddListener<T1, T2>(EventDefine eventName, Action<T1, T2> action)
        {
            InvokableAction<T1, T2> invokableAction = Pool.Get<InvokableAction<T1, T2>>();
            invokableAction.Initialize(eventTag, action);
            AddListener(eventName, invokableAction);
        }

        public void AddListener<T1, T2, T3>(EventDefine eventName, Action<T1, T2, T3> action)
        {
            InvokableAction<T1, T2, T3> invokableAction = Pool.Get<InvokableAction<T1, T2, T3>>();
            invokableAction.Initialize(eventTag, action);
            AddListener(eventName, invokableAction);
        }

        public bool ContainsStaticListener<T1, T2, T3>(EventDefine eventName, Action<T1, T2, T3> action)
        {
            if (registerEvents.TryGetValue(eventName, out var invokableActionBaseList))
            {
                for (int index = 0; index < invokableActionBaseList.Count; ++index)
                {
                    InvokableAction<T1, T2, T3> invokableAction =
                        invokableActionBaseList[index] as InvokableAction<T1, T2, T3>;
                    if (invokableAction != null && invokableAction.IsAction(action))
                    {
                        return true;
                    }
                }

                return false;
            }
            else
            {
                return false;
            }
        }

        public void AddListener<T1, T2, T3, T4>(EventDefine eventName, Action<T1, T2, T3, T4> action)
        {
            InvokableAction<T1, T2, T3, T4> invokableAction = Pool.Get<InvokableAction<T1, T2, T3, T4>>();
            invokableAction.Initialize(eventTag, action);
            AddListener(eventName, invokableAction);
        }

        public void AddListener<T1, T2, T3, T4, T5>(EventDefine eventName, Action<T1, T2, T3, T4, T5> action)
        {
            InvokableAction<T1, T2, T3, T4, T5> invokableAction = Pool.Get<InvokableAction<T1, T2, T3, T4, T5>>();
            invokableAction.Initialize(eventTag, action);
            AddListener(eventName, invokableAction);
        }
        #endregion

        #region FireMsgAtOnce 立即触发事件

        public void FireMsgAtOnce(EventDefine eventName)
        {
            List<InvokableActionBase> actionList = CloneThenGetActionList(eventName);
            if (actionList == null)
                return;
            for (int index = actionList.Count - 1; index >= 0; --index)
                (actionList[index] as InvokableAction).Invoke();
            ReturnActionListToPool(actionList);
        }

        public void FireMsgAtOnce<T1>(EventDefine eventName, T1 arg1)
        {
            List<InvokableActionBase> actionList = CloneThenGetActionList(eventName);
            if (actionList == null)
                return;
            for (int index = actionList.Count - 1; index >= 0; --index)
                (actionList[index] as InvokableAction<T1>).Invoke(arg1);
            ReturnActionListToPool(actionList);
        }

        public void FireMsgAtOnce<T1, T2>(EventDefine eventName, T1 arg1, T2 arg2)
        {
            List<InvokableActionBase> actionList = CloneThenGetActionList(eventName);
            if (actionList == null)
                return;
            for (int index = actionList.Count - 1; index >= 0; --index)
                (actionList[index] as InvokableAction<T1, T2>).Invoke(arg1, arg2);
            ReturnActionListToPool(actionList);
        }

        public void FireMsgAtOnce<T1, T2, T3>(EventDefine eventName, T1 arg1, T2 arg2, T3 arg3)
        {
            List<InvokableActionBase> actionList = CloneThenGetActionList(eventName);
            if (actionList == null)
                return;
            for (int index = actionList.Count - 1; index >= 0; --index)
                (actionList[index] as InvokableAction<T1, T2, T3>).Invoke(arg1, arg2, arg3);
            ReturnActionListToPool(actionList);
        }

        public void FireMsgAtOnce<T1, T2, T3, T4>(EventDefine eventName, T1 arg1, T2 arg2, T3 arg3, T4 arg4)
        {
            List<InvokableActionBase> actionList = CloneThenGetActionList(eventName);
            if (actionList == null)
                return;
            for (int index = actionList.Count - 1; index >= 0; --index)
                (actionList[index] as InvokableAction<T1, T2, T3, T4>).Invoke(arg1, arg2, arg3, arg4);
            ReturnActionListToPool(actionList);
        }

        public void FireMsgAtOnce<T1, T2, T3, T4, T5>(EventDefine eventName, T1 arg1, T2 arg2, T3 arg3, T4 arg4, T5 arg5)
        {
            List<InvokableActionBase> actionList = CloneThenGetActionList(eventName);
            if (actionList == null)
                return;
            for (int index = actionList.Count - 1; index >= 0; --index)
                (actionList[index] as InvokableAction<T1, T2, T3, T4, T5>).Invoke(arg1, arg2, arg3, arg4, arg5);
            ReturnActionListToPool(actionList);
        }

        #endregion

        #region FireMsg 经过一帧事件合并后使用最新的参数触发事件
        public void FireMsg(EventDefine eventName)
        {
            if (!eventParams.TryGetValue(eventName, out EventParamBase eventParam))
            {
                eventParam = Pool.Get<EventParam>();
                eventParams.Add(eventName, eventParam);
            }
            (eventParam as EventParam)?.Set(eventName);
        }

        public void FireMsg<T1>(EventDefine eventName, T1 p1)
        {
            if (!eventParams.TryGetValue(eventName, out EventParamBase eventParam))
            {
                eventParam = Pool.Get<EventParam<T1>>();
                eventParams.Add(eventName, eventParam);
            }
            (eventParam as EventParam<T1>)?.Set(eventName, p1);
        }

        public void FireMsg<T1, T2>(EventDefine eventName, T1 p1, T2 p2)
        {
            if (!eventParams.TryGetValue(eventName, out EventParamBase eventParam))
            {
                eventParam = Pool.Get<EventParam<T1, T2>>();
                eventParams.Add(eventName, eventParam);
            }
            (eventParam as EventParam<T1, T2>)?.Set(eventName, p1, p2);
        }

        public void FireMsg<T1, T2, T3>(EventDefine eventName, T1 p1, T2 p2, T3 p3)
        {
            if (!eventParams.TryGetValue(eventName, out EventParamBase eventParam))
            {
                eventParam = Pool.Get<EventParam<T1, T2, T3>>();
                eventParams.Add(eventName, eventParam);
            }
            (eventParam as EventParam<T1, T2, T3>)?.Set(eventName, p1, p2, p3);
        }

        public void FireMsg<T1, T2, T3, T4>(EventDefine eventName, T1 p1, T2 p2, T3 p3, T4 p4)
        {
            if (!eventParams.TryGetValue(eventName, out EventParamBase eventParam))
            {
                eventParam = Pool.Get<EventParam<T1, T2, T3, T4>>();
                eventParams.Add(eventName, eventParam);
            }
            (eventParam as EventParam<T1, T2, T3, T4>)?.Set(eventName, p1, p2, p3, p4);
        }
        public void FireMsg<T1, T2, T3, T4, T5>(EventDefine eventName, T1 p1, T2 p2, T3 p3, T4 p4, T5 p5)
        {
            if (!eventParams.TryGetValue(eventName, out EventParamBase eventParam))
            {
                eventParam = Pool.Get<EventParam<T1, T2, T3, T4, T5>>();
                eventParams.Add(eventName, eventParam);
            }
            (eventParam as EventParam<T1, T2, T3, T4, T5>)?.Set(eventName, p1, p2, p3, p4, p5);
        }
        #endregion

        #region RemoveListener

        public void RemoveListener(EventDefine eventName, Action action)
        {
            List<InvokableActionBase> actionList = GetRegisteredActionList(eventName);
            if (actionList == null)
                return;
            for (int index = 0; index < actionList.Count; ++index)
            {
                InvokableAction invokableAction = actionList[index] as InvokableAction;
                if (invokableAction.IsAction(action))
                {
                    invokableAction.Clear();
                    actionList.RemoveAt(index);
                    break;
                }
            }

            CheckForEventRemoval(eventName, actionList);
        }

        public void RemoveListener<T1>(EventDefine eventName, Action<T1> action)
        {
            List<InvokableActionBase> actionList = GetRegisteredActionList(eventName);
            if (actionList == null)
                return;
            for (int index = 0; index < actionList.Count; ++index)
            {
                InvokableAction<T1> invokableAction = actionList[index] as InvokableAction<T1>;
                if (invokableAction.IsAction(action))
                {
                    invokableAction.Clear();
                    actionList.RemoveAt(index);
                    break;
                }
            }

            CheckForEventRemoval(eventName, actionList);
        }

        public void RemoveListener<T1, T2>(EventDefine eventName, Action<T1, T2> action)
        {
            List<InvokableActionBase> actionList = GetRegisteredActionList(eventName);
            if (actionList == null)
                return;
            for (int index = 0; index < actionList.Count; ++index)
            {
                InvokableAction<T1, T2> invokableAction = actionList[index] as InvokableAction<T1, T2>;
                if (invokableAction.IsAction(action))
                {
                    invokableAction.Clear();
                    actionList.RemoveAt(index);
                    break;
                }
            }

            CheckForEventRemoval(eventName, actionList);
        }

        public void RemoveListener<T1, T2, T3>(EventDefine eventName, Action<T1, T2, T3> action)
        {
            List<InvokableActionBase> actionList = GetRegisteredActionList(eventName);
            if (actionList == null)
                return;
            for (int index = 0; index < actionList.Count; ++index)
            {
                InvokableAction<T1, T2, T3> invokableAction = actionList[index] as InvokableAction<T1, T2, T3>;
                if (invokableAction.IsAction(action))
                {
                    invokableAction.Clear();
                    actionList.RemoveAt(index);
                    break;
                }
            }

            CheckForEventRemoval(eventName, actionList);
        }

        public void RemoveListener<T1, T2, T3, T4>(
            EventDefine eventName,
            Action<T1, T2, T3, T4> action)
        {
            List<InvokableActionBase> actionList = GetRegisteredActionList(eventName);
            if (actionList == null)
                return;
            for (int index = 0; index < actionList.Count; ++index)
            {
                InvokableAction<T1, T2, T3, T4> invokableAction = actionList[index] as InvokableAction<T1, T2, T3, T4>;
                if (invokableAction.IsAction(action))
                {
                    invokableAction.Clear();
                    actionList.RemoveAt(index);
                    break;
                }
            }

            CheckForEventRemoval(eventName, actionList);
        }

        public void RemoveListener<T1, T2, T3, T4, T5>(
            EventDefine eventName,
            Action<T1, T2, T3, T4, T5> action)
        {
            List<InvokableActionBase> actionList = GetRegisteredActionList(eventName);
            if (actionList == null)
                return;
            for (int index = 0; index < actionList.Count; ++index)
            {
                InvokableAction<T1, T2, T3, T4, T5> invokableAction = actionList[index] as InvokableAction<T1, T2, T3, T4, T5>;
                if (invokableAction.IsAction(action))
                {
                    invokableAction.Clear();
                    actionList.RemoveAt(index);
                    break;
                }
            }

            CheckForEventRemoval(eventName, actionList);
        }
        #endregion
    }

    #region InvokableAction定义
    internal abstract class InvokableActionBase
    {
        public EventTag eventTag;

        public abstract void Clear();
    }

    [Preserve]
    internal class InvokableAction : InvokableActionBase
    {
        private event Action m_Action;

        public void Initialize(EventTag tag, Action action)
        {
            eventTag = tag;
            this.m_Action = action;
        }

        public void Invoke() => this.m_Action();

        public bool IsAction(Action action) => this.m_Action == action;

        public override void Clear()
        {
            Pool.Release(this);
            m_Action = null;
        }
    }

    [Preserve]
    internal class InvokableAction<T1> : InvokableActionBase
    {
        private event Action<T1> m_Action;

        public void Initialize(EventTag tag, Action<T1> action)
        {
            eventTag = tag;
            this.m_Action = action;
        }

        public void Invoke(T1 arg1) => this.m_Action(arg1);

        public bool IsAction(Action<T1> action) => this.m_Action == action;

        public override void Clear()
        {
            Pool.Release(this);
            m_Action = null;
        }
    }

    [Preserve]
    internal class InvokableAction<T1, T2> : InvokableActionBase
    {
        private event Action<T1, T2> m_Action;

        public void Initialize(EventTag tag, Action<T1, T2> action)
        {
            eventTag = tag;
            this.m_Action = action;
        }

        public void Invoke(T1 arg1, T2 arg2) => this.m_Action(arg1, arg2);

        public bool IsAction(Action<T1, T2> action) => this.m_Action == action;

        public override void Clear()
        {
            Pool.Release(this);
            m_Action = null;
        }
    }

    [Preserve]
    internal class InvokableAction<T1, T2, T3> : InvokableActionBase
    {
        private Action<T1, T2, T3> m_Action;

        public void Initialize(EventTag tag, Action<T1, T2, T3> action)
        {
            eventTag = tag;
            this.m_Action = action;
        }

        public void Invoke(T1 arg1, T2 arg2, T3 arg3) => this.m_Action(arg1, arg2, arg3);

        public bool IsAction(Action<T1, T2, T3> action) => this.m_Action == action;

        public override void Clear()
        {
            Pool.Release(this);
            m_Action = null;
        }
    }

    [Preserve]
    internal class InvokableAction<T1, T2, T3, T4> : InvokableActionBase
    {
        private event Action<T1, T2, T3, T4> m_Action;

        public void Initialize(EventTag tag, Action<T1, T2, T3, T4> action)
        {
            eventTag = tag;
            this.m_Action = action;
        }

        public void Invoke(T1 arg1, T2 arg2, T3 arg3, T4 arg4) => this.m_Action(arg1, arg2, arg3, arg4);

        public bool IsAction(Action<T1, T2, T3, T4> action) => this.m_Action == action;

        public override void Clear()
        {
            Pool.Release(this);
            m_Action = null;
        }
    }

    [Preserve]
    internal class InvokableAction<T1, T2, T3, T4, T5> : InvokableActionBase
    {
        private event Action<T1, T2, T3, T4, T5> m_Action;

        public void Initialize(EventTag tag, Action<T1, T2, T3, T4, T5> action)
        {
            eventTag = tag;
            this.m_Action = action;
        }

        public void Invoke(T1 arg1, T2 arg2, T3 arg3, T4 arg4, T5 arg5) => this.m_Action(arg1, arg2, arg3, arg4, arg5);

        public bool IsAction(Action<T1, T2, T3, T4, T5> action) => this.m_Action == action;

        public override void Clear()
        {
            Pool.Release(this);
            m_Action = null;
        }
    }
    #endregion

    #region EventParam定义

    internal abstract class EventParamBase
    {
        protected EventDefine msg;

        public EventDefine Msg => msg;
        public abstract void Fire(MgrMsg mgr);

        public abstract void ReturnToPool();
    }

    [Preserve]
    internal class EventParam : EventParamBase
    {
        public void Set(EventDefine msg)
        {
            this.msg = msg;
        }

        public override void Fire(MgrMsg mgr)
        {
            mgr.FireMsgAtOnce(msg);
        }

        public override void ReturnToPool()
        {
            Pool.Release(this);
        }
    }

    [Preserve]
    internal class EventParam<T1> : EventParamBase
    {
        public T1 p1;

        public void Set(EventDefine msg, T1 p1)
        {
            this.msg = msg;
            this.p1 = p1;
        }

        public override void Fire(MgrMsg mgr)
        {
            mgr.FireMsgAtOnce(msg, p1);
        }

        public override void ReturnToPool()
        {
            Pool.Release(this);
        }
    }

    [Preserve]
    internal class EventParam<T1, T2> : EventParamBase
    {
        public T1 p1;
        public T2 p2;

        public void Set(EventDefine msg, T1 p1, T2 p2)
        {
            this.msg = msg;
            this.p1 = p1;
            this.p2 = p2;
        }

        public override void Fire(MgrMsg mgr)
        {
            mgr.FireMsgAtOnce(msg, p1, p2);
        }

        public override void ReturnToPool()
        {
            Pool.Release(this);
        }
    }

    [Preserve]
    internal class EventParam<T1, T2, T3> : EventParamBase
    {
        public T1 p1;
        public T2 p2;
        public T3 p3;

        public void Set(EventDefine msg, T1 p1, T2 p2, T3 p3)
        {
            this.msg = msg;
            this.p1 = p1;
            this.p2 = p2;
            this.p3 = p3;
        }

        public override void Fire(MgrMsg mgr)
        {
            mgr.FireMsgAtOnce(msg, p1, p2, p3);
        }

        public override void ReturnToPool()
        {
            Pool.Release(this);
        }
    }

    [Preserve]
    internal class EventParam<T1, T2, T3, T4> : EventParamBase
    {
        public T1 p1;
        public T2 p2;
        public T3 p3;
        public T4 p4;

        public void Set(EventDefine msg, T1 p1, T2 p2, T3 p3, T4 p4)
        {
            this.msg = msg;
            this.p1 = p1;
            this.p2 = p2;
            this.p3 = p3;
            this.p4 = p4;
        }

        public override void Fire(MgrMsg mgr)
        {
            mgr.FireMsgAtOnce(msg, p1, p2, p3, p4);
        }

        public override void ReturnToPool()
        {
            Pool.Release(this);
        }
    }

    [Preserve]
    internal class EventParam<T1, T2, T3, T4, T5> : EventParamBase
    {
        public T1 p1;
        public T2 p2;
        public T3 p3;
        public T4 p4;
        public T5 p5;

        public void Set(EventDefine msg, T1 p1, T2 p2, T3 p3, T4 p4, T5 p5)
        {
            this.msg = msg;
            this.p1 = p1;
            this.p2 = p2;
            this.p3 = p3;
            this.p4 = p4;
            this.p5 = p5;
        }

        public override void Fire(MgrMsg mgr)
        {
            mgr.FireMsgAtOnce(msg, p1, p2, p3, p4, p5);
        }

        public override void ReturnToPool()
        {
            Pool.Release(this);
        }
    }

    #endregion
}