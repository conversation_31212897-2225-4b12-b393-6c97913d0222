using Cysharp.Text;
using Facepunch;
using FairyGUI;
using SimpleJSON;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using WizardGames.Soc.Client.Lobby;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data.Play;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Profile;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Http;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Ui.Utils;

namespace WizardGames.Soc.SocClient.Ui
{
    /// <summary>
    /// 历史战局界面
    /// </summary>
    public class UiHistoryBattle : WindowComBase, IUiFps10Update
    {
        private static SocLogger Log = LogHelper.GetLogger(typeof(UiHistoryBattle));

        private GButton btnPlay;
        private Controller playController;
        private Controller rankPointCtrl;
        private ComTopBar topBar;
        private GLoader RTLoader;

        private GTextField serverStartTime;
        private GTextField serverEndTime;
        // private GTextField serverResetTime;
        private GList historyPlayGList;
        private GComponent[] playerComs;

        private GButton btnDelete;
        private GTextField deleteTime;
        private Controller deleteController;
        private GImage deleteImg;

        private GTextField impeachTip;


        private const int TEAM_MAX_NUM = 4;
        private int selectModeId = int.MinValue;
        private int selectIndex = 0;
        private RenderTexture rtForModel;
        private NTexture ntForModel = null;
        private bool isJoinBattle = false;

        // private float playPPVTime = 0f;

        // private HistoryBattleControlAnim historyBattleControlAnim;

        private List<string> roleIdList = new List<string>();

        private bool fromTamUI = false;

        // private long timerId = -1;

        private GComponent content;
        private bool isMedalPoint;
        //是否开启积分结算
        private ComRankMedalCheckBox medalCheckBox;
        private GComponent comrankMedal;
        
        private List<OBJGameMode> curGameModeList = new();
        private Dictionary<int,long> timerDic = new();

        private GComponent stageCom;
        private GTextField stageTxt;

        private string battleId;

        private OBJGameMode oBJGameMode;
        private Transition clickTrans;
        private Transition bgClickTrans;

        public override EProfileFunc GetProfileFuncEnum(ELoopType loopType)
        {
            switch (loopType)
            {
                case ELoopType.Fps10:
                    return EProfileFunc.OnFps10Update_UI_UiHistoryBattle;
                default:
                    return EProfileFunc.None;
            }
        }

        protected override void OnInit()
        {
            base.OnInit();

            content = ComRoot.GetChild("content").asCom;
            btnPlay = content.GetChild("btnPlay").asButton;
            playController = btnPlay.GetController("status");
            var bgCom = ContentPane.GetChild("bg").asCom;
            RTLoader = bgCom.GetChild("RTLoader").asLoader;
            bgClickTrans = bgCom.GetTransition("click");

            topBar = ComRoot.GetChild("topBar") as ComTopBar;
            topBar.Title = LanguageManager.GetTextConst(LanguageConst.HistoryBattle);
            topBar.SetBackButtonClicked(OnEscClose);

            btnDelete = content.GetChild("btnDelete").asButton;
            historyPlayGList = content.GetChild("historyList").asList;
            serverStartTime = content.GetChild("openServerTime").asTextField;
            serverEndTime = content.GetChild("serverEndTime").asTextField;
            // serverResetTime = content.GetChild("serverResetTime").asTextField;
            deleteTime = content.GetChild("deleteTime").asTextField;
            deleteController = content.GetController("deleteTime");
            rankPointCtrl = content.GetController("point");
            deleteImg = btnDelete.GetChild("n2").asImage;
            deleteImg.color = Color.black;

            playerComs = new GComponent[TEAM_MAX_NUM];
            for (int i = 0; i < TEAM_MAX_NUM; i++)
            {
                playerComs[i] = ContentPane.GetChild("bg").asCom.GetChild(ZString.Concat("player", i)).asCom;
                playerComs[i].visible = false;
            }

            btnPlay.SetSoundClick(Mc.Tables.TbUiAudio.UniversalButtonClick, ctx => OnClickConfirmPlay());
            btnDelete.SetSoundClick(Mc.Tables.TbUiAudio.UniversalButtonClick, ctx => OnClickDelete());

            historyPlayGList.itemRenderer = RenderListItem;

            InitRT();

            impeachTip = content.GetChild("impeachTip").asTextField;
            
            comrankMedal = content.GetChild("rank").asCom;
            medalCheckBox = new ComRankMedalCheckBox(comrankMedal);
            medalCheckBox.SetOnClick(OnClickRankMedalCheckBox);
            
            stageCom = content.GetChild("stage").asCom;
            stageTxt = stageCom.GetChild("title").asTextField;
            clickTrans = content.GetTransition("click");
            clickTrans.invalidateBatchingEveryFrame = true;

        }

        private void OnClickRankMedalCheckBox()
        {
            if (!medalCheckBox.hasPointRank)
            {
                medalCheckBox.checkBox.selected = false;
                if (string.IsNullOrEmpty(Mc.Medal.openBattleServerID))
                {
                    string strText = ZString.Format(LanguageManager.GetTextConst(LanguageConst.DeleteMedalRankScoreTip), Mc.Tables.TBConsts.ChangeRankMatchCd / 60);
                    List<ModalBtnInfo> btns = new()
                        {
                            new(LanguageManager.GetTextConst(LanguageConst.No), null),
                            new(LanguageManager.GetTextConst(LanguageConst.Yes), ((() =>
                            {
                                Mc.Medal.SetSettleStyleRankPointsSwitch(battleId);
                            })))
                        };
                    UiMsgBox.ShowMsgBox(new() { Msg = strText, BtnList = btns, SetCenter = true });
                }
                else
                {
                    if (Mc.Medal.IsRankScoreSwitchValid(out long cdTime))
                    {
                        string strText = ZString.Format(LanguageManager.GetTextConst(LanguageConst.DeleteMedalRankScoreTip), Mc.Tables.TBConsts.ChangeRankMatchCd / 60);
                        List<ModalBtnInfo> btns = new()
                        {
                            new(LanguageManager.GetTextConst(LanguageConst.No), null),
                            new(LanguageManager.GetTextConst(LanguageConst.Yes), ((() =>
                            {
                                Mc.Medal.SetSettleStyleRankPointsSwitch(battleId);
                            })))
                        };
                        UiMsgBox.ShowMsgBox(new() { Msg = strText, BtnList = btns, SetCenter = true });
                    }
                    else
                    {
                        // cd中，无法切换
                        Mc.MsgTips.ShowRealtimeWeakTip(24385);
                        return;
                    }
                }
            }
            else
            {
                medalCheckBox.checkBox.selected = true;
                //if (Mc.Medal.IsRankScoreSwitchValid(out long cdTime))
                {
                    if (battleId == Mc.Medal.openBattleServerID)
                    {
                        //弹窗删除结算                    
                        string strText = LanguageManager.GetTextConst(LanguageConst.SwitchMedalRankScoreTip);
                        List<ModalBtnInfo> btns = new()
                        {
                            new(LanguageManager.GetTextConst(LanguageConst.No), null),
                            new(LanguageManager.GetTextConst(LanguageConst.Yes), ((() =>
                            {
                                Mc.Medal.DelSettleStyleRankPointsSwitch(battleId);
                            })))
                        };
                        UiMsgBox.ShowMsgBox(new() { Msg = strText, BtnList = btns, SetCenter = true });
                    }
                }
            }
        }
        public override void OnEscClose(EventContext context)
        {
            //MgrAudio.PlayAudioEventAsync(Mc.Tables.TbUiAudio.UniversalMainMenuTab);
            base.OnEscClose(context);
        }


        public void OnJoinOldBattleNotice(JSONNode node)
        {
            if (isJoinBattle)
            {
                HideSelf();
                isJoinBattle = false;
            }
        }

        // public void SetGlitchRGBSplitVisible(bool show)
        // {
        //     // if (historyBattleControlAnim == null || historyBattleControlAnim.GetGlitchRGBSplitActive() == show) return;
        //     // playPPVTime = historyBattleControlAnim.playTime;
        //     // historyBattleControlAnim.SetGlitchRGBSplitVisible(show);
        // }

        public void OnClickItem(int index, GComponent obj, int modeId)
        {
            Mc.Audio.PlayAudioEvent(null, Mc.Tables.TbUiAudio.VoiceButtonClick);
            var dataList = Mc.Lobby.GetLobbyBattleServerDataByModeId(modeId);
            OnRefreshItemRedDot(dataList[index].BattleServerId, obj.GetChild("redDot") as ComRedDot);
            if (selectIndex == index && modeId == selectModeId)
            {
                return;
            }
            // SetGlitchRGBSplitVisible(true);
            clickTrans.Play();
            bgClickTrans.Play();
            selectIndex = index;
            selectModeId = modeId;
            RefreshSelectBattleInfo();
            
        }

        public void OnRefreshItemRedDot(string battleServerId,GComponent redDot)
        {
            if (Mc.Lobby.ShowRedByBattleId(battleServerId))
            {
                ClientPlayerPrefs.SetString(battleServerId,"");
                if (redDot != null)
                {
                    redDot.visible = false;
                }
            }
            else if (Mc.Lobby.IsShowCaptainImpeachmentTip(battleServerId) && !Mc.Lobby.CaptainImpeachmentTipIsInClientPrefs(battleServerId))
            {
                Mc.Lobby.SetCaptainImpeachmentTipClientPrefs(battleServerId);
                if (redDot != null)
                {
                    redDot.visible = false;
                }
            }
            Mc.RedDot?.RefreshRedDot(RedDotType.LobbyHistoryBattle);
        }

        public void OnFps10Update(float dt)
        {
            // if (playPPVTime > 0)
            // {
            //     playPPVTime = playPPVTime - dt / 1000;
            // }
            // else
            // {
            //     SetGlitchRGBSplitVisible(false);
            // }
            UpdatePlayerNamePos();//这里的位置更新跟着人物进场走，现在拿不到人物进场结束后的回调，所以这里在update更新下
            UpdateDeleteTime();
            UpdateSwitchCdTime();
        }

        private void UpdateDeleteTime()
        {
            if (deleteController.selectedIndex == 0)
            {
                var isGet = Mc.LobbyTime.TryGetCurSvrUtc0TimeStamp(out var curSvrUtc0TimeStamp);
                if (isGet == false)
                {
                    return;
                }
                var time = Mc.Lobby.NextCanRemoveBattleTime - curSvrUtc0TimeStamp;
                if (time > 0)
                {
                    deleteTime.text = ZString.Format(LanguageManager.GetTextConst(LanguageConst.DeleteTimeCD), ConvertCountDown(time));
                    deleteImg.fillAmount = 1;//(float)time / Mc.Tables.TBConsts.DeletePlayerServerCD;
                }
                else
                {
                    RefreshDeleteTimeController();
                }
            }
        }
        private void UpdateSwitchCdTime()
        { 
            if (oBJGameMode != null && oBJGameMode.IsRank)
            {
                medalCheckBox?.SetTitle(true);
                if (!string.IsNullOrEmpty(Mc.Medal.openBattleServerID))
                {
                    if (battleId != Mc.Medal.openBattleServerID)
                    {
                        if (!Mc.Medal.IsRankScoreSwitchValid(out long cdTime))
                        {
                            string leftTime = CommonNumberFormatUtils.FormatTime(cdTime);
                            string cdTimeStr = ZString.Format(LanguageManager.GetTextConst(LanguageConst.MedalRankScoreCDTime), leftTime);
                            medalCheckBox.SetTitle(false, cdTimeStr);
                        }       
                    }
                }
            }
        }
        private string ConvertCountDown(long countDown)
        {
            var hour = countDown / 3600;
            var minute = (countDown % 3600) / 60;
            var second = countDown % 60;
            return ZString.Format("{0:D2}:{1:D2}:{2:D2}", hour, minute, second);
        }

        public void UpdatePlayerNamePos()
        {
            var camera = LobbyStepController.Instance?.GetStageCamera();
            if (playerComs == null || camera == null) return;
            for (int i = 0; i < playerComs.Length; i++)
            {
                if (playerComs[i] != null && playerComs[i].visible)
                {
                    var worldPos = Mc.LobbyTeam.lobbyTeamPlayerModel.GetPlayerParentPos(i);
                    
                    Vector3 screenPos = camera.WorldToScreenPoint(worldPos);
                    screenPos.y = RTLoader.height - screenPos.y; //unity坐标在左上角，转换下y坐标

                    var pt = RTLoader.LocalToRoot(screenPos, RTLoader.parent.root);
                    var pos = RTLoader.parent.RootToLocal(pt, RTLoader.parent.root);//受适配影响，需要再转成RTLoader.parent下的本地坐标

                    playerComs[i].SetXY(pos.x, pos.y);
                }
                
            }
        }

        public void RenderListItem(int index, GObject obj)
        {
            GComponent item = obj.asCom;
            if (item == null || index >= curGameModeList.Count || index < 0)
            {
                Log.ErrorFormat("RenderListItem index:{0} curGameModeList.Count:{1}", index, curGameModeList.Count);
                return;
            }

            var config = curGameModeList[index];
            var serverDataByModeId = Mc.Lobby.GetLobbyBattleServerDataByModeId(config.GameModeID);
            var title = item.GetChild("title").asLabel;
            
            var curModeNum = serverDataByModeId.Count;
            var playModeMain = Mc.Tables.TBPlayModeMain.GetOrDefault(config.GameModeID);//战局模式表
            var modeMaxNum = playModeMain.BattleLimit;
            title.title = ZString.Format("{0}({1}/{2})",config.GameModeName,curModeNum,modeMaxNum) ;
            
            var list = item.GetChild("list").asList;
            list.itemRenderer = (_index,_obj) =>
            {
                OnBattleItemRenderer(_index, _obj, config.GameModeID);
            };
            list.onClickItem.Set((context) =>
            {
                var itemIndex = list.GetChildIndex((GObject)context.data);
                var itemObj = context.data as GComponent;
                if (itemObj == null)
                {
                    return;
                }
                ResetSelectControllerByModeId(config.GameModeID);
                OnClickItem(itemIndex,itemObj,config.GameModeID);
                list.numItems = serverDataByModeId.Count;
                list.ResizeToFit();
            });
            list.numItems = serverDataByModeId.Count;
            list.ResizeToFit();
            if (timerDic.TryGetValue(index,out var timerID))
            {
                Mc.TimerWheel.CancelTimer(timerID);
                timerID = 0;
            }
            timerDic[index] = FairyGuiUtil.PlayListAnimation(list);
        }

        public void ResetSelectControllerByModeId(int modeId)
        {
            if (modeId != selectModeId)
            {
                var index = curGameModeList.FindIndex(x => x.GameModeID == selectModeId);
                var item = historyPlayGList.GetChildAt(index) as GComponent;
                var itemList = item.GetChild("list").asList;
                for (int i = 0; i < itemList.numChildren; i++)
                {
                    var selectItem = itemList.GetChildAt(i) as GComponent;
                    var control = selectItem.GetController("control");
                    if (control != null)
                    {
                        control.SetSelectedIndex(1); // 1是未选中状态
                    }
                }
            }
        }

        public void OnBattleItemRenderer(int index, GObject obj, int modeId)
        {
            var item = obj.asCom;
            if (item == null)
            {
                Log.ErrorFormat("OnBattleItemRenderer index:{0} item is null", index);
                return;
            }
            
            Transition clickTrans = item.GetTransition("click");
            clickTrans.invalidateBatchingEveryFrame = true;
            
            bool isSelect = selectIndex == index && selectModeId == modeId;
            Controller control = item.GetController("control");
            control.SetSelectedIndex(isSelect ? 0 : 1); // 0是选中状态，1是未选中状态
            var dataList = Mc.Lobby.GetLobbyBattleServerDataByModeId(modeId);
            LobbyBattleServerData serverData = dataList[index];
            if (serverData == null || dataList.Count == 0)
            {
                return;
            }
            Controller settlementStatusCtrl = item.GetController("settlementStatus");
            if (IsBattleServerEnd(serverData.BattleEndTime))
            {
                settlementStatusCtrl.selectedPage = serverData.Settlement ? "settlementCanreward" : "settlementNoreward";
            }
            else
            {
                settlementStatusCtrl.selectedPage = "inProgress";
            }
            
            var config = Mc.Tables.TBGameMode.GetOrDefault(modeId);
            if (config == null)
            {
                return;
            }
            
            GLoader bgIcon = item.GetChild("bg").asLoader;
            bgIcon.url = config.GameModeHistoryBattleBtnIcon;
            bgIcon.visible = isSelect;
            
            GTextField indexTxt = item.GetChild("index").asTextField;
            indexTxt.text = index < 10 ? ZString.Concat("0", index + 1) : (index + 1).ToString();
            
            GTextField serverNameTxt = item.GetChild("serverName").asTextField;
            serverNameTxt.text = config.GameModeName; //现在没有服务器名称，先显示玩法模式名

            var plantTxt = item.GetChild("plantTxt").asRichTextField;
            var iconUrl = FGUIResPathDef.LOBBY_TEAM_ICON_07;
            var plantformName = TeamStaticUtil.PlanFormNameDic[serverData.Platform];
            plantTxt.text = ZString.Format("{0}{1}/{2}", iconUrl,serverData.TeamSize,plantformName);
            var redDot = item.GetChild("redDot") as ComRedDot;
            RefreshRedDot(serverData.BattleServerId, redDot, item.GetController("redDotPos"));
            
        }
        
        public void RefreshRedDot(string battleServerId, ComRedDot redDot, Controller redDotPosCtrl)
        {
            //新增>队长弹劾>奖励提示
            bool isShow = false;
            if (Mc.Lobby.ShowRedByBattleId(battleServerId)) //是否有新增战局
            {
                redDot.SetStyle(ERedDotStyle.Text);
                redDotPosCtrl.selectedPage = "text";
                isShow = true;
            }                               
            else if (Mc.Lobby.IsShowCaptainImpeachmentTip(battleServerId) && !Mc.Lobby.CaptainImpeachmentTipIsInClientPrefs(battleServerId)) //队长弹劾
            {
                redDotPosCtrl.selectedPage = "custom";
                redDot.SetCustomValue(FGUIResPathDef.RED_DOT_IMPEACH);
                redDot.SetStyle(ERedDotStyle.Custom);
                isShow = true;
            }
            else if (Mc.Lobby.ShowLobbyHistoryBattleRewardRedDotByBattleServerId(battleServerId)) //奖励提示
            {
                redDotPosCtrl.selectedPage = "dot";
                redDot.SetStyle(ERedDotStyle.Dot);
                isShow = true;
            }
            redDot.visible = isShow;
        }

        public bool IsBattleServerEnd(ulong battleEndTime)
        {
            var isGet = Mc.LobbyTime.TryGetCurSvrUtc0TimeStamp(out var curSvrUtc0TimeStamp);
            if (isGet == false)
            {
                return false;
            }
            Log.InfoFormat("curSvrUtc0TimeStamp:{0} battleEndTime:{1}", curSvrUtc0TimeStamp, battleEndTime);
            return curSvrUtc0TimeStamp - (long)battleEndTime > 0;
        }

        public string FormatTimeFromSeconds(int seconds)
        {
            // 计算天、小时、分钟和秒
            long days = seconds / 86400; // 一天有86400秒
            // 格式化输出
            if(days >= 1)
            {
                var dayStr = ZString.Format(LanguageManager.GetTextConst(LanguageConst.UiMailValidFormat), days);
                var timeStr = TimeUtil.FormatTime(seconds - days * 86400);
                return ZString.Concat(dayStr, timeStr);
            }
            return TimeUtil.FormatTime(seconds);
        }

        private string GetTimeStr(string lastLeaveTime, string lastJoinTime)
        {
            long playTime = long.Parse(lastLeaveTime) - long.Parse(lastJoinTime);
            long hour = playTime / 3600;
            string timeStr = "";
            if (hour > 24)
            {
                timeStr = ZString.Concat(timeStr, hour / 24, LanguageManager.GetTextConst(LanguageConst.Day));
            }

            string time1 = TimeUtil.FormatTime(playTime - hour * 3600);
            timeStr = ZString.Concat(timeStr, ' ', time1);
            return timeStr;
        }
        private void OnClickConfirmPlay()
        {
            var dataList = Mc.Lobby.GetLobbyBattleServerDataByModeId(selectModeId);
            if (dataList == null || dataList.Count <= selectIndex) return;
            var data = dataList[selectIndex];
            if (data == null || data.BattleServerId == null)
            {
                Log.InfoFormat("OnClickOldPlay join battle failed selectIndex:{0}", selectIndex);
                return;
            }
            
            bool isServerEnd = IsBattleServerEnd(data.BattleEndTime);
            if (!isServerEnd && oBJGameMode != null && oBJGameMode.IsRank && !medalCheckBox.hasPointRank)
            {
                string strText = LanguageManager.GetTextConst(LanguageConst.NoMedalRankScoreTip);
                List<ModalBtnInfo> btns = new()
                        {
                            new(LanguageManager.GetTextConst(LanguageConst.No), null),
                            new(LanguageManager.GetTextConst(LanguageConst.Yes), ((() =>
                            {
                                OnClickOldPlay(data);
                            })))
                        };
                UiMsgBox.ShowMsgBox(new() { Msg = strText, BtnList = btns, SetCenter = true });
            }
            else
            {
                OnClickOldPlay(data);
            }
             
        }
        /// <summary>
        /// 打开历史战局界面
        /// </summary>
        public void OnClickOldPlay(LobbyBattleServerData data)
        {
            // var dataList = Mc.Lobby.GetLobbyBattleServerDataByModeId(selectModeId);
            // if (dataList == null || dataList.Count <= selectIndex) return;
            // var data = dataList[selectIndex];
            if (data == null || data.BattleServerId == null)
            {
                Log.InfoFormat("OnClickOldPlay join battle failed selectIndex:{0}", selectIndex);
                return;
            }
            if (IsBattleServerEnd(data.BattleEndTime)) // 战局结算
            {
                if (!data.Settlement) //奖励未过来，给个提示
                {
                    Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.BattleDataProcessing);
                    return;
                }
                HideSelf();

                int modeId = data.ModeId;
                OBJPlayModeMain gameMode = McCommon.Tables.TBPlayModeMain.GetOrDefault(modeId);
                bool isNewbie = gameMode is { IsNewbie: true }; // 新手教学服

                string key = ZString.Format("TheBattleResult_{0}", data.BattleServerId);
                bool hasKey = ClientPlayerPrefs.HasKey(key);
                Log.InfoFormat ("OnClickOldPlay join battle hasKey:{0}, isNewbie:{1}, BattleServerId:{2}",
                    hasKey, isNewbie, data.BattleServerId);
                if (hasKey)
                {
                    if (isNewbie)
                        Log.InfoFormat("OnClickOldPlay join battle newbie mode needDelete, BattleServerId:{0}",
                            data.BattleServerId);
                    else
                        UiWarSituation.ShowWarSituation(data.BattleServerId); // 显示战况窗口
                }
                else
                {
                    if (isNewbie) // 播放CG
                    {
                        UiGameNewbieMissionCG.TrainingFinished = true;
                        UiGameNewbieMissionCG.OpenWindow();
                    }
                    else
                        UiStorySettlement.ShowStorySettlement(data.BattleServerId, 0);
                }
            }
            else
            {
                Mc.LobbyTeam.IsJoinBattleByHistory = true;
                Mc.LoginStep.StepLog($"[HistoryJoinBattle]历史战局进服, 服务器ID {data.BattleServerId}");
                Mc.Lobby.JoinBattle(EHttpReqModule.HistoryBattle, data.BattleServerId);
                isJoinBattle = true;
            }
        }

        private void OnClickDelete()
        {
            var isGet = Mc.LobbyTime.TryGetCurSvrUtc0TimeStamp(out var curSvrUtc0TimeStamp);
            if (isGet == false)
            {
                return;
            }
            var time = Mc.Lobby.NextCanRemoveBattleTime - curSvrUtc0TimeStamp;
            if (time > 0)
            {
                Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.DeleteBattleServerInCD);
                return;
            }

            var dataList = Mc.Lobby.GetLobbyBattleServerDataByModeId(selectModeId);
            if(dataList == null || dataList.Count <= selectIndex) return;
            var data = dataList[selectIndex];   
            if (data == null || data.BattleServerId == null)
            {
                Log.InfoFormat("OnClickDelete battle failed selectIndex:{0}", selectIndex);
                return;
            }

            Mc.Ui.OpenWindow("UiDeleteHistoryPop", win =>
            {
                var deletePop = win as UiDeleteHistoryPop;
                if (deletePop != null)
                {
                    deletePop.Show(data.ModeId,data.BattleServerId);
                }
            });
        }

        public void OnBattleEndSummaryNotice()
        {
            RefreshInfo(selectModeId);
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            topBar.OnEnable();
            Mc.Medal.GetSettleStyleRankPointsSwitch();
            RMQualityManager.CloseUpScaling();
            LobbyStepController.Instance?.SetScenePrefabActive((int)ELobbyStep.Team);//历史战局和组队界面使用同一个场景
            // if (historyBattleControlAnim == null)
            // {
            //     historyBattleControlAnim = Object.FindObjectOfType<HistoryBattleControlAnim>();
            // }
            Mc.Msg.AddListener<JSONNode>(EventDefine.JoinOldBattleNotice, OnJoinOldBattleNotice);
            Mc.Msg.AddListener(EventDefine.LobbyBattleServerDataUpdate, OnBattleEndSummaryNotice);
            Mc.Msg.AddListener(EventDefine.DeleteBattleServerSuccessNotice, RefreshInfoFromDeleteServer);
            Mc.Msg.AddListener(EventDefine.DeleteSettleSwitchSuccess, OnDeleteSettleSwitchSuccess);
            Mc.Msg.AddListener(EventDefine.SetSettleSwitchSuccess, OnSetSettleSwitchSuccess);
            Mc.Msg.AddListener(EventDefine.GetSettleStyleRankPointsSwitch, OnGetSettleStyleRankPointsSwitch);
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            Mc.Msg.RemoveListener<JSONNode>(EventDefine.JoinOldBattleNotice, OnJoinOldBattleNotice);
            Mc.Msg.RemoveListener(EventDefine.LobbyBattleServerDataUpdate, OnBattleEndSummaryNotice);
            Mc.Msg.RemoveListener(EventDefine.DeleteBattleServerSuccessNotice, RefreshInfoFromDeleteServer);
            Mc.Msg.RemoveListener(EventDefine.DeleteSettleSwitchSuccess, OnDeleteSettleSwitchSuccess);
            Mc.Msg.RemoveListener(EventDefine.SetSettleSwitchSuccess, OnSetSettleSwitchSuccess);
            Mc.Msg.RemoveListener(EventDefine.SetSettleSwitchSuccess, OnSetSettleSwitchSuccess);
            Mc.Msg.RemoveListener(EventDefine.GetSettleStyleRankPointsSwitch, OnGetSettleStyleRankPointsSwitch);
            topBar.OnDisable();
            Mc.LobbyTeam.lobbyTeamPlayerModel.ReleaseDisplayPlayers(ELobbyModelType.HistoryModel);
            
            selectIndex = 0;
            roleIdList.Clear();
            ResetSceneCamera();
            RMQualityManager.OpenUpScaling();
            // if (timerId != -1)
            // {
            //     Mc.TimerWheel.CancelTimer(timerId);
            // }
            foreach (var VARIABLE in timerDic)
            {
                if (VARIABLE.Value != 0)
                {
                    Mc.TimerWheel.CancelTimer(VARIABLE.Value);
                }
            }
            timerDic.Clear();
        }

        public override void OnDestroy()
        {
            base.OnDestroy();
            DisposeRT();
            ResetSceneCamera();
            
            // historyBattleControlAnim = null;
        }

        public void ResetSceneCamera()
        {
            if (Mc.LoginStep == null || Mc.LoginStep.ObjSceneModel == null) return;
            var obj = Mc.LoginStep.ObjSceneModel.transform.Find("VirtualCamHistoryBattle");
            if (obj != null)
            {
                obj.transform.SetActive(false);
            }
            SetStageCamRenderType(CameraRenderType.Overlay,false);
            Camera.main.targetTexture = null;
        }

        public static void Open(int modeId = int.MinValue)
        {
            if (Mc.Ui.IsWindowOpen("UiHistoryBattle"))
            {
                var win = Mc.Ui.GetWindow("UiHistoryBattle") as UiHistoryBattle;
                win.RefreshInfo(modeId);
                return;
            }
            Mc.Ui.OpenWindowT<UiHistoryBattle>("UiHistoryBattle", win => win.RefreshInfo(modeId));
        }

        public void InitRT()
        {
            if (null != rtForModel) return;
            rtForModel = new RenderTexture((int)RTLoader.width, (int)RTLoader.height, 0,RenderTextureFormat.ARGB32);
            // rtForModel = new RenderTexture((int)RTLoader.width, (int)RTLoader.height, GraphicsFormat.R8G8B8A8_SRGB,
            //     GraphicsFormat.D24_UNorm_S8_UInt);//这里使用GraphicsFormat.R8G8B8A8_SRGB是为了保证颜色的正确性，不知道为啥会出现找不到深度和颜色的格式，找然神定位了下，先手动传入
            rtForModel.name = "HistoryBattleRT";
            rtForModel.dimension = TextureDimension.Tex2D;
            rtForModel.antiAliasing = 1;
            rtForModel.filterMode = FilterMode.Bilinear;
            rtForModel.anisoLevel = 0;
            rtForModel.Create();
            ntForModel = new NTexture(rtForModel) { destroyMethod = DestroyMethod.None };
            
        }

        public void DisposeRT()
        {
            if (null != rtForModel) rtForModel.Release();
            rtForModel = null;
            if (null != ntForModel) ntForModel.Dispose();
            ntForModel = null;
        }

        public void SetStageCamRenderType(CameraRenderType renderType, bool show = false)
        {
            var stageCamera = Object.FindObjectOfType<StageCamera>();
            //这里需要设置stageCamera的渲染模式为Base，关闭界面的时候记得还原为Overlay
            if (stageCamera != null)
            {
                var stageCamData = stageCamera.cachedCamera.GetUniversalAdditionalCameraData();
                if (stageCamData != null)
                {
                    stageCamData.renderType = renderType;
                }
            }
            var mainCamData = Camera.main.GetUniversalAdditionalCameraData();
            if (show)
            {
                if (null != mainCamData && mainCamData.cameraStack.Contains(stageCamera.cachedCamera))
                {
                    mainCamData.cameraStack.Remove(stageCamera.cachedCamera);
                }
            }
            else
            {
                if (null != mainCamData && !mainCamData.cameraStack.Contains(stageCamera.cachedCamera))
                {
                    mainCamData.cameraStack.Add(stageCamera.cachedCamera);
                }
            }

            // historyBattleControlAnim?.SetTargetObjVisible(show);
        }

        public void RefreshRT()
        {
            var obj = Mc.LoginStep.ObjSceneModel.transform.Find("VirtualCamHistoryBattle");
            if(obj != null)
            {
                obj.transform.SetActive(true);
            }
            
            //这里需要设置stageCamera的渲染模式为Base，关闭界面的时候记得还原为Overlay
            SetStageCamRenderType(CameraRenderType.Base,true);
            RTLoader.texture = ntForModel;
            Camera.main.targetTexture = rtForModel;
        }

        public void RefreshInfo(int modeId)
        {
            selectModeId = modeId;
            RefreshBattleModeIdList();
            RefreshSelectBattleInfo();
            RefreshRT();
        }

        /// <summary>
        /// 删除服务器后刷新
        /// </summary>
        public void RefreshInfoFromDeleteServer()
        {
            var dataList = Mc.Lobby.GetLobbyBattleServerDataByModeId(selectModeId);
            if (dataList == null || dataList.Count == 0)
            {
                Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.DeleteAllBattleServer);
                HideSelf(false);
                return;
            }

            // SetGlitchRGBSplitVisible(true);
            selectIndex = 0;
            selectModeId = dataList[selectIndex].ModeId;
            RefreshSelectBattleInfo();
        }

        private void OnDeleteSettleSwitchSuccess()
        {
            RefreshRankMedalState(selectModeId, battleId);
        }
        private void OnSetSettleSwitchSuccess()
        { 
            RefreshRankMedalState(selectModeId, battleId);
        }
        private void OnGetSettleStyleRankPointsSwitch()
        { 
            RefreshRankMedalState(selectModeId, battleId);
        }

        //拿GTBGameMode配置的id去GetSelectBattleServerData筛选出id，再根据TBGameMode的Sort字段排序，得到一个List<int> 的当前战局模式id集合
        public void RefreshBattleModeIdList()
        {
            if (Mc.Lobby == null || Mc.Lobby.lobbyBattleServerDatas == null || Mc.Lobby.lobbyBattleServerDatas.Count == 0)
            {
                Log.Info("RefreshBattleModeIdList lobbyBattleServerDatas is empty");
                return;
            }
            var config = Mc.Tables.TBGameMode.DataList;
            for (int i = 0; i < Mc.Lobby.lobbyBattleServerDatas.Count; i++)
            {
                var serverData = Mc.Lobby.lobbyBattleServerDatas[i];
                for (int j = 0; j < config.Count; j++)
                {
                    if (serverData.ModeId == config[j].GameModeID && !curGameModeList.Contains(config[j]))
                    {
                        curGameModeList.Add(config[j]);
                    }
                }
            }
            curGameModeList.Sort((a, b) => a.Sort - b.Sort);
            if (selectModeId == int.MinValue)
            {
                selectModeId = curGameModeList.Count > 0 ? curGameModeList[0].GameModeID : int.MinValue;
            }
            historyPlayGList.numItems = curGameModeList.Count;
        }

        public void RefreshSelectBattleInfo()
        {
            List<LobbyBattleServerData> dataList = Mc.Lobby.GetLobbyBattleServerDataByModeId(selectModeId);
            // 如果拿到的历史战局为空，就主动获取一次
            if (dataList == null || dataList.Count == 0)
            {
                return;
            }
            LobbyBattleServerData battleData = dataList[selectIndex];
            // serverResetTime.text = GetTimeStr(battleData.LastLeaveTime, battleData.LastJoinTime);
            string playTimeLabel = LanguageManager.GetTextConst(LanguageConst.Survived);
            string playTimeStr = FormatTimeFromSeconds(battleData.TotalPlayedSeconds);
            serverStartTime.text = ZString.Concat(playTimeLabel, ":", playTimeStr);
            //GetTransTime(battleData.ServerOpenedTime,LanguageManager.GetTextConst(LanguageConst.ServerStartTime));
            serverEndTime.text = GetTransTime(battleData.BattleEndTime, LanguageManager.GetTextConst(LanguageConst.ServerEndTime));
            impeachTip.visible = Mc.Lobby.IsShowCaptainImpeachmentTip(battleData.BattleServerId);

            playController.selectedPage = IsBattleServerEnd(battleData.BattleEndTime) ? "replay" : "play";
            if (IsBattleServerEnd(battleData.BattleEndTime)) //结算期间，只有奖励领了才能删
            {
                btnDelete.visible = false;
            }
            else
            {
                btnDelete.visible = true;
            }
            for (int i = 0; i < playerComs.Length; i++)
            {
                playerComs[i].visible = false;
            }

            // var stageNum = Mc.Lobby.GetStageIdByEndTime((long)battleData.BattleEndTime, out var stageName);
            stageCom.visible = false;//(battleData.BattleEndTime == 0 || stageNum == -1) ? false : true; B2版本和新手服设计冲突，先干掉，rls再开发
            // stageTxt.text = ZString.Format("0{0}{1}", stageNum, stageName);
            SetPlayInfo();
            RefreshDeleteTimeController();
            battleId = battleData.BattleServerId;
            RefreshRankMedalState(selectModeId, battleData.BattleServerId);
        }

        private void RefreshRankMedalState(int modelId, string battleId)
        {
            var config = Mc.Tables.TBGameMode.GetOrDefault(modelId);
            if (config == null) return;
            oBJGameMode = config;
            if (config.IsRank && Mc.Medal.ConditionUnlock)
            {
                rankPointCtrl.SetSelectedPage("no");
                var dataList = Mc.Lobby.GetLobbyBattleServerDataByModeId(selectModeId);
                if (dataList == null || dataList.Count <= selectIndex) return;
                var data = dataList[selectIndex];
                if (data == null || data.BattleServerId == null)
                {
                    Log.InfoFormat("OnClickOldPlay join battle failed selectIndex:{0}", selectIndex);
                    return;
                }
                bool isServerEnd = IsBattleServerEnd(data.BattleEndTime);
                if (isServerEnd)
                {
                    rankPointCtrl.SetSelectedPage("no");
                    return;
                }
                rankPointCtrl.SetSelectedPage("yes");
                medalCheckBox?.SetTitle(true);
                if (!string.IsNullOrEmpty(Mc.Medal.openBattleServerID))
                {
                    if (battleId == Mc.Medal.openBattleServerID)
                    {
                        medalCheckBox?.SetCheckBoxSelected(true);
                    }
                    else
                    {
                        if (!Mc.Medal.IsRankScoreSwitchValid(out long cdTime))
                        {
                            string leftTime = CommonNumberFormatUtils.FormatTime((long)cdTime);
                            string cdTimeStr = ZString.Format(LanguageManager.GetTextConst(LanguageConst.MedalRankScoreCDTime), leftTime);
                            medalCheckBox.SetTitle(false, cdTimeStr);
                        }
                        else
                        { 
                            medalCheckBox?.SetTitle(true);
                        }              
                        medalCheckBox?.SetCheckBoxSelected(false);
                    }
                }
                else
                {
                    medalCheckBox?.SetCheckBoxSelected(false);
                }
            }
            else
            {
                rankPointCtrl.SetSelectedPage("no");
            }
        }

        private void RefreshDeleteTimeController()
        {
            var isGet = Mc.LobbyTime.TryGetCurSvrUtc0TimeStamp(out var curSvrUtc0TimeStamp);
            if (isGet == false)
            {
                return;
            }
            var time = Mc.Lobby.NextCanRemoveBattleTime - curSvrUtc0TimeStamp;
            if (time > 0)
            {
                deleteController.SetSelectedIndex(0);
            }
            else
            {
                deleteController.SetSelectedIndex(1);
                deleteImg.fillAmount = 0;
            }
        }

        private string GetTransTime(ulong time, string serverStartTimeTitleStr)
        {
            if (time == 0) return "";
            string serverStartTimeStr = TimeUtil.FormatTimeDateTime(time.ToString());
            return ZString.Concat(serverStartTimeTitleStr, serverStartTimeStr);
        }


        /// <summary>
        /// 设置玩家信息（名字、状态）
        /// </summary>
        /// <param name="index"></param>
        public void SetPlayInfo()
        {
            var dataList = Mc.Lobby.GetLobbyBattleServerDataByModeId(selectModeId);
            var data = dataList[selectIndex];
            if (playerComs.Length == 0 || data == null)
            {
                Log.InfoFormat("SetPlayInfo playerComs or data is not exist index:{0} data:{1}", selectIndex, data);
                return;
            }

            Mc.Lobby.GetBattleTeamData(EHttpReqModule.HistoryBattle, data.TeamId, data.BattleServerId, (battleTeamData) =>
            {
                if (battleTeamData == null || battleTeamData.TeamMemberDataList.Count == 0)
                {
                    Log.InfoFormat("SetPlayInfo GetBattleTeamDataByTeamID res is null index:{0} data:{1}", selectIndex, battleTeamData);
                    return;
                }
                OnHandleTeamInfo(battleTeamData);
            });
        }
        
        public void OnHandleTeamInfo(BattleTeamData battleTeamData)
        {
            Mc.LobbyTeam.lobbyTeamPlayerModel.ReleaseDisplayPlayers(ELobbyModelType.HistoryModel);
            Mc.LobbyTeam.lobbyTeamPlayerModel.SetModelActive(ELobbyModelType.LobbyTeamModel, false);//隐藏组队界面的模型
            bool teamNoSelf = false;//处理保底情况，如果队伍中没有自己，确保显示的只有自己的模型以及数据（ps：这种情况下因为拿不到队伍数据，所以只会显示裸模）
            for (int i = 0; i < battleTeamData.TeamMemberDataList.Count; i++)
            {
                var info = battleTeamData.TeamMemberDataList[i];
                var roleID = info.roleID;
                if (roleID.Equals(Mc.Config.roleId))
                {
                    teamNoSelf = true;
                }
            }

            roleIdList.Clear();
            if (!teamNoSelf)
            {
                roleIdList.Add(Mc.Config.roleId);
                playerComs[0].visible = true;
                Mc.LobbyTeam.lobbyTeamPlayerModel.hasMaterials = false;
                    
                var displayData = new PlayerDisplayData(false, "");
                Mc.LobbyTeam.lobbyTeamPlayerModel.CreatePlayModel(ELobbyModelType.HistoryModel,0, displayData, false);
                    
                var worldPos = Mc.LobbyTeam.lobbyTeamPlayerModel.GetPlayerParentPos(0);
                var camera = LobbyStepController.Instance?.GetStageCamera();
                    
                Vector3 pos = camera.WorldToScreenPoint(worldPos);
                pos.y = RTLoader.height - pos.y;

                var pt = RTLoader.LocalToRoot(pos, content.root);
                playerComs[0].SetXY(pt.x, pt.y);
            }
            else
            {
                // 排序
                battleTeamData.TeamMemberDataList.Sort((x, y) =>
                {
                    // 首先按 JoinTime 从小到大排序
                    var xJoinTime = long.Parse(x.joinTime);
                    var yJoinTime = long.Parse(y.joinTime);
                    int result = xJoinTime.CompareTo(yJoinTime);
                    if (result == 0) // 如果 JoinTime 相等，按 RoleID 排序
                    {
                        var xRoleId = long.Parse(x.roleID);
                        var yRoleId = long.Parse(y.roleID);
                        result = xRoleId.CompareTo(yRoleId);
                    }
                    return result;
                });
                for (int i = 0; i < battleTeamData.TeamMemberDataList.Count; i++)
                {
                    var info = battleTeamData.TeamMemberDataList[i];
                    var roleID = info.roleID;
                    roleIdList.Add(roleID);
                    playerComs[i].visible = true;
                    Mc.LobbyTeam.lobbyTeamPlayerModel.hasMaterials = false;
                    
                    string battlePlayerDataStr = info.battlePlayerData;
                    var displayData = new PlayerDisplayData(false, JSONNode.Parse(battlePlayerDataStr));
                    Mc.LobbyTeam.lobbyTeamPlayerModel.CreatePlayModel(ELobbyModelType.HistoryModel,i, displayData,false);
                    
                    var worldPos = Mc.LobbyTeam.lobbyTeamPlayerModel.GetPlayerParentPos(i);
                    var camera = LobbyStepController.Instance?.GetStageCamera();
                    
                    Vector3 pos = camera.WorldToScreenPoint(worldPos);
                    pos.y = RTLoader.height - pos.y;

                    var pt = RTLoader.LocalToRoot(pos, content.root);
                    playerComs[i].SetXY(pt.x, pt.y);
                }
            }
            ReqGetPlayerInfo();
        }
        
        public void ReqGetPlayerInfo()
        {
            Mc.Config.RequestUserInfoList(EHttpReqModule.HistoryBattle, (jsonArray) =>
            {
                for (int i = 0;i < jsonArray.Count;i++)
                {
                    var player = playerComs[i];
                    var jsonNode = jsonArray[i];
                    var roleId = jsonNode["roleid"].AsULong;
                    var onlineStatus = jsonNode["onlineStatus"].AsInt;
                    var nickname = jsonNode["nickname"];

                    var selfName = player.GetChild("selfName").asTextField;
                    var otherOnlineName = player.GetChild("otherOnlineName").asTextField;
                    var otherOfflineName = player.GetChild("otherOfflineName").asTextField;
                    var otherGamingName = player.GetChild("otherGamingName").asTextField;
                    var control = player.GetController("status");
                    
                    if (roleId == Mc.Config.RoleId)
                    {
                        control.SetSelectedIndex(0);
                        selfName.text = nickname;
                    }
                    else
                    {
                        //在线状态.1 空闲  2 离线 3 房间中 4 匹配中 5 队伍中 6 游戏中 
                        if (onlineStatus == (int)EOnlineStatusType.Room)
                        {
                            control.SetSelectedIndex(3);
                            otherGamingName.text = nickname;
                        }
                        else if (onlineStatus == (int)EOnlineStatusType.Offline)
                        {
                            control.SetSelectedIndex(2);
                            otherOfflineName.text = nickname;
                        }
                        else
                        {
                            control.SetSelectedIndex(1);
                            otherOnlineName.text = nickname;
                        }
                    }
                }
            }, roleIdList);
        }
    

    }
}