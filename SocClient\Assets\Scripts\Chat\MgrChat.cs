using Assets.Scripts.MicroServiceClient;
using Cysharp.Text;
using LitJson;
using SimpleJSON;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using UnityEngine;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.SDK;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Http;
using WizardGames.Soc.SocClient.Manager;

namespace WizardGames.Soc.SocClient
{
    public class MgrChat : MgrBase
    {
        public static readonly SocLogger logger = LogHelper.GetLogger(typeof(MgrChat));
        public readonly Dictionary<string, ClientChatRoom> chatRooms = new();
        public readonly Dictionary<ulong, Action> UpdateRoleInfoCallback = new();

        private long worldChatChannel = 0;// 世界频道分线ID

        //Chat用过的RoleInfo，会保存到本地
        private readonly Dictionary<ulong, RoleInfo> roleIdDic = new();
        //系统角色信息，目前只有一个系统消息
        private readonly Dictionary<ulong, RoleInfo> systemRoleDic = new();
        //禁言
        private readonly BanInfo chatBan = new();
        private List<HashSet<string>> channelCategoryUnseenLst = new();
        private List<HashSet<string>> channelCategoryUnseenInviteLst = new();
        public List<List<string>> roomCategoryLst = new();
        public Deque_RandomAccess<ClientChatMessage> totalMessageQueue;
        public string serverBattleId;
        public ulong roleId;
        public bool isInLobby = true;
        private HashSet<ulong> personalBriefRoleIds = new();

        private string privateCacheFileName => $"private-{roleId}-Chat.json";
        private string lobbyTeamCacheFileName => $"lobby-team-{roleId}-Chat.json";
        [Obsolete("局内组队聊天信息，不需要缓存")]
        private string battleTeamCacheFileName => $"battle-team-{serverBattleId}-{roleId}-Chat.json";

        public override void OnLogined()
        {
            base.OnLogined();
        }
        public override void OnLogout()
        {
            base.OnLogout();
        }
        public override void OnAccountLogined()
        {
            base.OnAccountLogined();
            systemRoleDic.Clear();
            systemRoleDic.Add(1, new()
            {
                roleId = 1,
                roleName = LanguageManager.GetTextConst(LanguageConst.ChatSystemMessage),
            });
            ///聊天数据处理流程：
            ///1.读取本地缓存的私聊数据，创建频道，显示上限20个
            ///2.拉取最新的简要聊天信息，与本地缓存合并，如果已有房间，将房间置顶；如果没有房间，创建新房间。
            EnterLobbyInit(true);
            //拉取简要信息接口
            MicroServiceClient.Instance.CallLobbyGet("chatservice/personalmessage/brieflist", EHttpReqModule.Chat, callback: OnGetPersonalBrieflistMessage);
            //注册队伍信息变动事件
            Mc.Msg.AddListener(EventDefine.LobbyTeamTeamNotice, RefreshTeamChannel);
            Mc.Msg.AddListener(EventDefine.LobbyTeamSelfLeaveNotice, RefreshTeamChannel);
            Mc.Msg.AddListener<string, bool>(EventDefine.LobbyTeamMemberLeaveNotice, OnLobbyTeamMemberLeaveNotice);
            Mc.Msg.AddListener(EventDefine.MyTribeInfosChange, RefreshTribeChannel);
            //测试用
            // Mc.Config.CloseUserPrivacy(Config.EUserPrivacy.CloseStrangerPrivateChat);
            // Mc.Config.SaveUserPrivacy(EHttpReqModule.Lobby);
        }
        private void OnLobbyTeamMemberLeaveNotice(string roleId, bool isAppointment)
        {
            RefreshTeamChannel();
        }
        /// <summary>
        /// 队伍信息变动事件
        /// </summary>
        public void RefreshTeamChannel()
        {
            //普通组队
            bool isTeamExist = isInLobby ?
                !string.IsNullOrEmpty(Mc.LobbyTeam.GetCurTeamInfo(false).TeamID)
                && Mc.LobbyTeam.GetCurTeamInfo(false).TeamID != "0"
                : TeamEntity.Instance?.MemberDic?.Count > 0;
            if (!isTeamExist)
            {
                if (Mc.Chat.chatRooms.ContainsKey(ChatChannelData.channelTeamId.ToString()))
                {
                    Mc.Chat.RemoveChannel(ChatChannelData.channelTeamId.ToString());
                }
            }
            else
            {
                if (!Mc.Chat.chatRooms.ContainsKey(ChatChannelData.channelTeamId))
                {
                    Mc.Chat.CreateRoom(new TeamChatSession(ulong.Parse(ChatChannelData.channelTeamId)));
                }
            }
            //预约队伍 - 局内不显示预约队伍
            var appointmentTeamData = Mc.LobbyTeam.GetCurTeamInfo(true);
            if (!isInLobby || string.IsNullOrEmpty(appointmentTeamData.TeamID) || appointmentTeamData.TeamID.Equals("0"))
            {
                if (Mc.Chat.chatRooms.ContainsKey(ChatChannelData.channelAppointment))
                {
                    Mc.Chat.RemoveChannel(ChatChannelData.channelAppointment);
                }
            }
            else
            {
                if (!Mc.Chat.chatRooms.ContainsKey(ChatChannelData.channelAppointment))
                {
                    Mc.Chat.CreateRoom(new TeamChatSession(ulong.Parse(ChatChannelData.channelAppointment)));
                }
            }
        }
        /// <summary>
        /// 刷新部落频道
        /// </summary>
        public void RefreshTribeChannel()
        {
            var myTribeInfos = Mc.Tribe.GetMyTribeInfos();
            foreach (var tribe in myTribeInfos)
            {
                if (!Mc.Chat.chatRooms.ContainsKey(tribe.InGameGroupID))
                {
                    Mc.Chat.CreateRoom(tribe.InGameGroupID);
                }
            }
            // 清理不存在的部落频道
            var removeChannel = new List<string>();
            foreach (var channel in Mc.Chat.chatRooms)
            {
                if (channel.Value.ChannelType == EChatChannelType.Tribe && !myTribeInfos.Exists(t => t.InGameGroupID == channel.Key))
                {
                    removeChannel.Add(channel.Key);
                }
            }
            foreach (var channelId in removeChannel)
            {
                Mc.Chat.RemoveChannel(channelId);
            }
        }
        public override void OnAccountLogout()
        {
            ExitClear(false);
            base.OnAccountLogout();
            //注册队伍信息变动事件
            Mc.Msg.RemoveListener(EventDefine.LobbyTeamTeamNotice, RefreshTeamChannel);
            Mc.Msg.RemoveListener(EventDefine.LobbyTeamSelfLeaveNotice, RefreshTeamChannel);
            Mc.Msg.RemoveListener<string, bool>(EventDefine.LobbyTeamMemberLeaveNotice, OnLobbyTeamMemberLeaveNotice);
            Mc.Msg.RemoveListener(EventDefine.MyTribeInfosChange, RefreshTribeChannel);
        }
        private void Initialize()
        {
            totalMessageQueue = new(ChatChannelData.GlobalMessageCacheMax, 1);
            ResetNewRoomLst();
            // 手动加世界频道
            CreateRoom(new TeamChatSession(ulong.Parse(ChatChannelData.channelWorldId)));
            RecvTotalMessage(new(LanguageManager.GetTextConst(isInLobby ? LanguageConst.ChatLobbyWelcome : LanguageConst.ChatWorldWelcome), 1)
            {
                channelId = ChatChannelData.channelWorldId,
                msgType = EClientChatMessageType.SystemMessage,
            });
        }
        /// <summary>
        /// 聊天是否被禁言
        /// </summary>
        /// <returns></returns>
        public bool IsChatBand()
        {
            if (chatBan.UpdateTime > 0 && chatBan.EndTime <= 0) return false;
            var now = TimeStampUtil.GetNowTimeStampMSec() * 0.001;
            return chatBan.EndTime > now;
        }
        /// <summary>
        /// 查找聊天房间
        /// </summary>
        /// <param name="channelId"></param>
        /// <param name="createIfNotExist"></param>
        /// <returns></returns>
        public ClientChatRoom GetRoom(string channelId, bool createIfNotExist = true)
        {
            if (chatRooms.TryGetValue(channelId, out var room))
            {
                return room;
            }
            else if (createIfNotExist)
            {
                return CreateRoom(channelId);
            }
            return null;
        }
        public void ShowBandTips()
        {
            // 需要有和大厅禁言一致的表现
            string lobbyFormat = "{{\"HasError\":true,\"LogicError\":14007,\"Content\":{{\"banInfo\":{{\"banReason\":{0},\"banEndTime\":{1}}}}}}}";
            string errorMsg = ZString.Format(lobbyFormat, chatBan.Reason, chatBan.EndTime);
            JSONNode errorInfo = JSON.Parse(errorMsg);
            MicroServiceClient.PopupResultError(errorInfo);
        }
        /// <summary>
        /// 请求禁言信息
        /// </summary>
        public void RequestMuteStatus()
        {
            var now = TimeStampUtil.GetNowTimeStampMSec() * 0.001;
            if (now >= chatBan.UpdateTime)
            {
                //下次主动请求禁言时间
                chatBan.UpdateTime = (long)now + Mc.Tables.TbGlobalConfig.ChatBanUpdateInterval;

                MicroServiceClient.Instance.CallLobbyGet("chatservice/baninfo", EHttpReqModule.Chat, callback: OnMuteStatusChange);
            }
        }
        /// <summary>
        /// 获取好友私聊频道列表，排除陌生人
        /// </summary>
        /// <returns></returns>
        public List<string> GetFriendPersonalChannels()
        {
            var lst = new List<string>();
            var personal = roomCategoryLst[(int)EChatCategory.Personal];
            foreach (var channelId in personal)
            {
                if (Mc.LobbyFriend.IsFriend(channelId.ToString()))
                {
                    lst.Add(channelId);
                }
            }
            return lst;
        }
        /// <summary>
        /// 排除陌生人
        /// </summary>
        /// <returns></returns>
        public HashSet<string> GetFriendPersonalUnseen()
        {
            var lst = new HashSet<string>();
            var personal = channelCategoryUnseenLst[(int)EChatCategory.Personal];
            foreach (var channelId in personal)
            {
                if (Mc.LobbyFriend.IsFriend(channelId.ToString()))
                {
                    lst.Add(channelId);
                }
            }
            return lst;
        }
        /// <summary>
        /// 处理封禁信息
        /// </summary>
        /// <param name="node"></param>
        public void OnMuteStatusChange(JSONNode node)
        {
            var chatEndTime = node?["muteInfo"]?["banEndTime"].AsLong ?? 0;
            var chatBanReason = node?["muteInfo"]?["banReason"].AsInt ?? 0;
            //voice封禁
            var voiceEndTime = node?["voiceBanInfo"]?["banEndTime"].AsLong ?? 0;
            var voiceBanReason = node?["voiceBanInfo"]?["banReason"].AsInt ?? 0;
            var now = TimeStampUtil.GetNowTimeStampMSec() * 0.001;
            var endTime = chatBan.UpdateTime;
            //禁言结束时更新一次，保证UpdateTime比now大，防止无限刷新
            if (chatEndTime > now && chatEndTime < endTime)
            {
                endTime = chatEndTime;
            }
            if (voiceEndTime > now && voiceEndTime < endTime)
            {
                endTime = voiceEndTime;
            }
            chatBan.UpdateTime = endTime;
            SetBanInfo(chatEndTime, chatBanReason);
            Mc.Voice.SetBanInfo(voiceEndTime, voiceBanReason);
        }
        /// <summary>
        /// 更新禁言信息
        /// </summary>
        /// <param name="endTime"></param>
        /// <param name="reason"></param>
        public void SetBanInfo(long endTime, int reason)
        {
            chatBan.EndTime = endTime;
            chatBan.Reason = reason;
        }
        public override Task OnEnterWorld()
        {
            EnterBattleInit();
            //组队消息
            Mc.Msg.AddListener(EventDefine.TeamMemberDictChange, this.OnTeamMemberChange);
            return base.OnEnterWorld();
        }
        public override Task OnExitWorld()
        {
            EnterLobbyInit();
            //组队消息
            Mc.Msg.RemoveListener(EventDefine.TeamMemberDictChange, this.OnTeamMemberChange);
            return base.OnExitWorld();
        }
        private void EnterLobbyInit(bool isFirstInit = false)
        {
            isInLobby = true;
            ulong.TryParse(Mc.Config.roleId, out roleId);
            //进入大厅时，保存局内聊天数据
            if (!isFirstInit) Save(true);
            //清理内存数据
            ExitClear(true);
            Initialize();
            Load(false, isFirstInit);
        }
        private void EnterBattleInit()
        {
            isInLobby = false;
            serverBattleId = Mc.Config.serverId;
            ulong.TryParse(Mc.Config.roleId, out roleId);
            //进入战斗时，保存大厅聊天数据
            Save(false);
            //清理内存数据
            ExitClear(true);
            Initialize();
            Load(true);
        }

        private void OnTeamMemberChange()
        {
            if (TeamEntity.Instance?.MemberDic?.Count > 0)
            {
                //队伍组件TeamComponentClient会处理创建频道
            }
            else
            {
                this.RemoveChannel(ChatChannelData.channelTeamId.ToString());
            }
        }

        public void OnWorldChatChannelIdChanged(long newChannel)
        {
            this.worldChatChannel = newChannel;
            logger.InfoFormat("[Chat][OnWorldChatChannelIdChanged] 世界频道分线ID变更为 {0}", newChannel);
        }

        public void ExitClear(bool stilInGame)
        {
            if (totalMessageQueue == null) { return; }
            if (!stilInGame)
            {
                Save(!isInLobby);//强退时，保存数据，局内存局内、大厅存大厅
                roleIdDic.Clear();
                chatRooms.Clear();
                roomCategoryLst.Clear();
                channelCategoryUnseenLst.Clear();
                channelCategoryUnseenInviteLst.Clear();
            }
            UpdateRoleInfoCallback.Clear();
            totalMessageQueue = null;
        }
        public static Comparison<ClientChatMessage> CompareByTime = (x, y) => x.TimeInfo.CompareTo(y.TimeInfo);
        private void OnGetPersonalBrieflistMessage(JSONNode node)
        {
            try
            {
                var array = node["chats"].AsArray;
                if (array == null) { return; }
                List<ClientChatMessage> messages = new();
                foreach (var (_, chatroom) in array)
                {
                    //只有最后一条信息
                    var lastMessage = chatroom["lastMessage"];
                    var lastReadMsgId = chatroom["lastReadMsgId"];
                    var roleId = chatroom["roleID"].AsLong;
                    messages.Add(new(lastMessage["msg"], lastMessage["sender"].AsULong, (long)lastMessage["sendTime"].AsULong)
                    {
                        Id = lastMessage["msgID"].AsUInt,
                        RoleId = roleId,
                        channelId = roleId.ToString()
                    });
                }
                messages.Sort(CompareByTime);
                personalBriefRoleIds = new();
                var maxNum = ChatChannelData.GetChannelTypeCapacity(EChatChannelType.Personal);
                for (var i = messages.Count - 1; i >= 0; i--)
                {
                    if (ChatChannelData.GetChannelType(messages[i].channelId) == EChatChannelType.Personal)
                    {
                        personalBriefRoleIds.Add((ulong)messages[i].RoleId);
                        if (personalBriefRoleIds.Count == maxNum) { break; }
                    }
                }
                foreach (var (_, chatroom) in array)
                {
                    if (personalBriefRoleIds.Contains(chatroom["roleID"].AsULong))
                    {
                        CreateRoom(chatroom);
                    }
                }
                foreach (var msg in messages)
                {
                    if (!chatRooms.ContainsKey(msg.channelId)) { continue; }
                    var room = chatRooms[msg.channelId];
                    if (msg.Id > room.CurrentSeq)
                    {
                        room.IsMessageBrief = true;
                        ClearChannel(msg.channelId, msg.Id);
                        this.RecvTotalMessage(msg, true);
                        room.ReceiveMsg(msg);
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                logger.Warn(node.ToString());
            }
        }
        private void OnGetPersonalDetailMessage(JSONNode chatroom)
        {
            try
            {
                List<ClientChatMessage> messages = new();
                var selfBubbleId = chatroom["senderChatBubbleID"].AsInt;
                var otherBubbleId = chatroom["receiverChatBubbleID"].AsInt;
                var msgArray = chatroom["messages"]?.AsArray;
                var roleId = chatroom["roleID"].AsLong;
                var channelId = roleId.ToString();
                var lastReadMsgId = chatroom["lastReadMsgId"].AsUInt;
                var remoteCurrent = msgArray[^1]["msgID"].AsUInt;
                foreach (var (_, msg) in msgArray)
                {
                    messages.Add(new(msg["msg"], msg["sender"].AsULong)
                    {
                        Id = msg["msgID"].AsUInt,
                        channelId = channelId,
                        TimeInfo = (long)msg["sendTime"].AsULong,
                        ChatBubbleId = msg["sender"].Equals(Mc.Config.roleId) ? selfBubbleId : otherBubbleId,
                    });
                }
                messages.Sort(CompareByTime);
                var room = chatRooms[channelId];
                //最多150条信息，用首条信息的MsgId作为offset
                room.chatMessages.ResetOffset(msgArray[0]["msgID"].AsUInt);
                foreach (var msg in messages)
                {
                    this.RecvTotalMessage(msg, true, false);
                    room.ReceiveMsg(msg);
                }
                room.LastReadSeq = lastReadMsgId;
                room.RemoteSeq = remoteCurrent;
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                logger.Warn(chatroom.ToString());
            }
        }
        private void ResetNewRoomLst()
        {
            var lastRoomLst = roomCategoryLst;
            var lastUnseenLst = channelCategoryUnseenLst;
            var lastUnseenInviteLst = channelCategoryUnseenInviteLst;
            roomCategoryLst = new();
            channelCategoryUnseenLst = new();
            channelCategoryUnseenInviteLst = new();

            foreach (var value in Enum.GetValues(typeof(EChatCategory)))
            {
                roomCategoryLst.Add(new());
                channelCategoryUnseenLst.Add(new());
                channelCategoryUnseenInviteLst.Add(new());
            }

            foreach (var lst in lastRoomLst)
            {
                foreach (var id in lst)
                {
                    if (chatRooms.ContainsKey(id))
                    {
                        roomCategoryLst[(int)ChatChannelData.GetChannelCategory(id)].Add(id);
                    }
                }
            }
            foreach (var set in lastUnseenLst)
            {
                foreach (var id in set)
                {
                    if (chatRooms.ContainsKey(id))
                    {
                        channelCategoryUnseenLst[(int)ChatChannelData.GetChannelCategory(id)].Add(id);
                    }
                }
            }
            foreach (var set in lastUnseenInviteLst)
            {
                foreach (var id in set)
                {
                    if (chatRooms.ContainsKey(id))
                    {
                        channelCategoryUnseenInviteLst[(int)ChatChannelData.GetChannelCategory(id)].Add(id);
                    }
                }
            }
        }
        public void CreateRoom(ChatSession session)
        {
            if (session == null) { return; }
            var channelId = session.ChannelId.ToString();
            logger.InfoFormat("[Chat][CreateRoom] ChatSession 创建频道 {0}   session.CurrentSeq:{1}  session.LastReadSeq:{2}", channelId, session.CurrentSeq, session.LastReadSeq.ToString());
            if (chatRooms.TryGetValue(channelId, out var room))
            {
                room.RemoteSeq = session.CurrentSeq > room.CurrentSeq ? session.CurrentSeq : room.CurrentSeq;
                room.LastReadSeq = session.LastReadSeq > room.LastReadSeq ? session.LastReadSeq : room.LastReadSeq;
                if (room.LastReadSeq > room.RemoteSeq) { room.LastReadSeq = room.RemoteSeq; }
            }
            else
            {
                room = new ClientChatRoom()
                {
                    ChannelId = channelId,
                    LastReadSeq = session.LastReadSeq,
                    RemoteSeq = session.CurrentSeq
                };
                chatRooms.Add(channelId, room);
                if (room.LastReadSeq > room.RemoteSeq) { room.LastReadSeq = room.RemoteSeq; }
            }
            CreateRoomOver(room);
            //如果队伍频道，请求全量数据
            if (ChatChannelData.GetChannelType(channelId) == EChatChannelType.Team) Mc.Chat.RemoteCallGetTeamChatMessage();
            logger.InfoFormat("[Chat][CreateRoom] 创建完成 room {0}   room.RemoteSeq:{1}  room.LastReadSeq:{2}  room.UnseenCount:{3}", room.ChannelId.ToString(), room.RemoteSeq, room.LastReadSeq.ToString(), room.UnseenCount);
        }

        public void CreateRoom(JSONNode roomNode)
        {
            if (roomNode == null) { return; }
            var roleId = roomNode["roleID"].AsULong;
            var channelId = roleId.ToString();
            var lastReadSeq = roomNode["lastReadMsgId"].AsUInt;
            var level = roomNode["level"].AsInt;
            uint currentSeq;
            if (roomNode["messages"] != null)//全量消息
            {
                var msgs = roomNode["messages"].AsArray;
                currentSeq = msgs[^1]["msgID"].AsUInt;
            }
            else//简量消息
            {
                currentSeq = roomNode["lastMessage"]["msgID"].AsUInt;
            }

            if (chatRooms.TryGetValue(channelId, out var room))
            {
                //将房间移动至顶部，防止覆盖
                MoveChannelToTop(channelId);
                room.RemoteSeq = currentSeq;
                room.LastReadSeq = lastReadSeq;
                if (room.LastReadSeq > room.RemoteSeq) { room.LastReadSeq = room.RemoteSeq; }
            }
            else
            {
                room = new ClientChatRoom()
                {
                    ChannelId = channelId,
                    LastReadSeq = lastReadSeq,
                    RemoteSeq = currentSeq
                };
                chatRooms.Add(channelId, room);
                if (room.LastReadSeq > room.RemoteSeq) { room.LastReadSeq = room.RemoteSeq; }
            }
            CreateRoomOver(room);
        }

        public ClientChatRoom CreateRoom(string channelId)
        {
            if (chatRooms.ContainsKey(channelId)) { return null; }
            ClientChatRoom room = new() { ChannelId = channelId };
            chatRooms.Add(channelId, room);
            CreateRoomOver(room);
            return room;
        }

        private void CreateRoomOver(ClientChatRoom room)
        {
            if (room.UnseenCount > 0) { AddRedUnSeen(room); }
            OnCreateChannel(room.ChannelId);
        }

        public void ClearChannel(string channelId, long offset)
        {
            if (!chatRooms.TryGetValue(channelId, out var room)) { return; }
            room.ClearRoom();
            room.chatMessages.ResetOffset(offset);
            RemoveRedUnSeen(channelId);
        }
        /// <summary>
        /// TODO：重构时优化，目前添加频道用的CreateRoom，删除频道用的RemoveChannel，逻辑上不一致
        /// </summary>
        /// <param name="channelId"></param>
        public void RemoveChannel(string channelId)
        {
            if (!chatRooms.ContainsKey(channelId)) { return; }
            if (ChatChannelData.GetChannelType(channelId) == EChatChannelType.World)
            {
                return;
            }
            roomCategoryLst[(int)ChatChannelData.GetChannelCategory(channelId)].Remove(channelId);
            chatRooms.Remove(channelId);
            RemoveRedUnSeen(channelId);
            //删除频道，同时清理掉此频道的消息
            this.totalMessageQueue.Remove(msg => msg.channelId.Equals(channelId));
            Mc.Msg.FireMsg(EventDefine.OnChatChannelModify);
        }
        public void MoveChannelToTop(string channelId)
        {
            if (!chatRooms.ContainsKey(channelId)) { return; }
            if (ChatChannelData.GetChannelType(channelId) == EChatChannelType.World)
            {
                return;
            }
            roomCategoryLst[(int)ChatChannelData.GetChannelCategory(channelId)].Remove(channelId);
            roomCategoryLst[(int)ChatChannelData.GetChannelCategory(channelId)].Insert(0, channelId);
            Mc.Msg.FireMsg(EventDefine.OnChatChannelModify);
        }

        public void RequestPersonalMessageDetail(ClientChatRoom room, Action getCallback)
        {
            //私聊房间、有未接收数据时请求
            if (room != null && room.IsMessageBrief && EChatChannelType.Personal == ChatChannelData.GetChannelType(room.ChannelId))
            {
                MicroServiceClient.Instance.CallLobbyGet("chatservice/personalmessage/detail?receiver=" + room.ChannelId, EHttpReqModule.Chat, null, callback: (json) =>
                {
                    OnGetPersonalDetailMessage(json);
                    room.IsMessageBrief = false;
                    getCallback();
                }, false, onError: (json) =>
                {
                    logger.Error(json.ToString());
                });
            }
            else
            {
                getCallback();
            }
        }

        #region save&load
        private struct SaveStruct
        {
            public List<ClientChatMessage> chatList;
            public List<RoleInfo> roleList;
        }
        /// <summary>
        /// 保存数据到缓存
        /// </summary>
        /// <param name="isSaveBattle">是否保存局内，false为保存大厅数据</param>
        private void Save(bool isSaveBattle)
        {
            if (chatRooms.Count == 0 && roleIdDic.Count == 0) { return; }
            List<ClientChatMessage> teamChatList = new();//局内、局外组队数据分开保存
            List<ClientChatMessage> privateChatList = new();//私聊数据共用一份
            List<string> removeChannel = new();
            foreach (var (channelId, room) in chatRooms)
            {
                // 不保存，直接删除 - 世界频道
                if (ChatChannelData.GetClearOnSwitch(channelId))
                {
                    removeChannel.Add(channelId);
                    continue;
                }
                // 保存后，删除 - 组队、预约频道
                if (ChatChannelData.GetClearOnSwitchNeedSave(channelId))
                {
                    removeChannel.Add(channelId);
                    for (var i = 0; i < room.chatMessages.Count; i++)
                    {
                        teamChatList.Add(room.chatMessages[i]);
                    }
                }
                //私聊，保存数据，保留频道
                else if (!room.IsMessageBrief)
                {
                    for (var i = 0; i < room.chatMessages.Count; i++)
                    {
                        privateChatList.Add(room.chatMessages[i]);
                    }
                }
            }
            //删除频道
            foreach (var i in removeChannel)
            {
                chatRooms.Remove(i);
            }
            //缓存角色信息
            //由于局内局外都可以私聊，为防止私聊缓存丢失，大厅、局内切换时都会保存。
            List<RoleInfo> roleList = new();
            if (!isSaveBattle)
            {
                foreach (var item in roleIdDic) { roleList.Add(item.Value); }
                SaveChatData(teamChatList, roleList, this.lobbyTeamCacheFileName);
                SaveChatData(privateChatList, roleList, this.privateCacheFileName);
            }
            else
            {
                //局内组队聊天数据不需要缓存
                //SaveChatData(teamChatList, new(), this.battleTeamCacheFileName);
                SaveChatData(privateChatList, roleList, this.privateCacheFileName);
            }
        }
        private void SaveChatData(List<ClientChatMessage> chatList, List<RoleInfo> roleList, string fileName)
        {
            var obj = new SaveStruct()
            {
                chatList = chatList,
                roleList = roleList
            };
            var savePath = Path.Combine(Application.persistentDataPath, "Res/Config/");
            if (!Directory.Exists(savePath)) Directory.CreateDirectory(savePath);
            var filePath = Path.Combine(savePath, fileName);
            if (!File.Exists(filePath)) File.Create(filePath).Dispose();
            File.WriteAllText(filePath, JsonMapper.ToJson(obj));
        }
        /// <summary>
        /// 加载缓存数据
        /// </summary>
        /// <param name="isLoadBattle"></param>
        /// <returns></returns>
        private void Load(bool isLoadBattle, bool isFirstLoad = false)
        {
            try
            {
                List<ClientChatMessage> chatList = new List<ClientChatMessage>();
                List<RoleInfo> roleList = new List<RoleInfo>();
                //大厅组队聊天数据 - 局内队伍数据暂不需要
                if (!isLoadBattle)
                {
                    SaveStruct teamObj = LoadChatData(this.lobbyTeamCacheFileName);
                    if (teamObj.chatList != null) chatList.AddRange(teamObj.chatList);
                    if (teamObj.roleList != null) roleList.AddRange(teamObj.roleList);
                }
                if (isFirstLoad)//仅首次加载时加载私聊，运行期间私聊频道不会被关闭
                {
                    SaveStruct privateObj = LoadChatData(this.privateCacheFileName);
                    if (privateObj.chatList != null) chatList.AddRange(privateObj.chatList);
                    if (privateObj.roleList != null) roleList.AddRange(privateObj.roleList);
                }
                foreach (var msg in chatList)
                {
                    if (!chatRooms.TryGetValue(msg.channelId, out var room))
                    {
                        room = this.CreateRoom(msg.channelId);
                    }
                    room.chatMessages.PushBack(msg);
                }
                //清理未读消息，对齐RemoteSeq，LastReadSeq。
                foreach (var room in chatRooms) { room.Value.ClearUnseen(); }
                foreach (var item in roleList)
                {
                    if (!roleIdDic.ContainsKey(item.GetRoleId()))
                    {
                        roleIdDic[item.GetRoleId()] = item;
                        item.lastUpd = 0;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.WarnFormat("JsonLoad报错，可能是代码更改导致 ex:{0}", ex);
            }
        }
        private SaveStruct LoadChatData(string fileName)
        {
            var filePath = Path.Combine(Application.persistentDataPath, "Res/Config/", fileName);
            if (!File.Exists(filePath)) return default;
            try
            {
                var str = File.ReadAllText(filePath);
                var obj = JsonMapper.ToObject<SaveStruct>(str);
                return obj;
            }
            catch (Exception ex)
            {
                logger.WarnFormat("LoadChatData报错，可能是代码更改导致 ex:{0}", ex);
            }
            return default;
        }

        #endregion

        #region Role
        /// <summary>
        /// 
        /// </summary>
        /// <param name="newInfo"></param>
        [Obsolete("待优化，角色缓存全部使用Mc.Config")]
        public void AddOrUpdateRoleInfo(RoleInfo newInfo)
        {
            if ((ulong)newInfo.roleId == Mc.Config.RoleId) return;
            if (!roleIdDic.TryGetValue((ulong)newInfo.roleId, out var roleInfo))
            {
                roleInfo = new RoleInfo();
                roleIdDic[(ulong)newInfo.roleId] = roleInfo;
            }
            roleInfo.roleId = newInfo.roleId;
            roleInfo.roleName = newInfo.roleName;
            roleInfo.avatarFrameId = newInfo.avatarFrameId;
            roleInfo.isRoleOnline = newInfo.isRoleOnline;
            roleInfo.roleAvatarId = newInfo.roleAvatarId;
            roleInfo.lastUpd = newInfo.roleAvatarId == "" ? 0 : Time.time;
            roleInfo.reputationLevel = newInfo.reputationLevel;
            roleInfo.level = newInfo.level;
            roleInfo.userPrivacy = newInfo.userPrivacy;
            roleInfo.styleID = newInfo.styleID;
            roleInfo.rankPoint = newInfo.rankPoint;
            Mc.Msg.FireMsg(EventDefine.RefreshChatUi);
        }
        /// <summary>
        /// 只有简要信息
        /// </summary>
        /// <param name="playerBriefInfo"></param>
        [Obsolete("待优化，角色缓存全部使用Mc.Config")]
        public void AddOrUpdateRoleBriefInfo(PlayerBriefInfo playerBriefInfo, int reputationLevel = -3)
        {
            if (!roleIdDic.TryGetValue(playerBriefInfo.RoleId, out var roleInfo))
            {
                roleInfo = new RoleInfo();
                roleIdDic[playerBriefInfo.RoleId] = roleInfo;
            }
            if (!string.IsNullOrEmpty(playerBriefInfo.Name)) roleInfo.roleName = playerBriefInfo.Name;
            roleInfo.roleId = (long)playerBriefInfo.RoleId;
            if (!string.IsNullOrEmpty(playerBriefInfo.Portrait)) roleInfo.roleAvatarId = playerBriefInfo.Portrait;
            roleInfo.avatarFrameId = (int)playerBriefInfo.PortraitFrame;
            roleInfo.isRoleOnline = playerBriefInfo.IsOnline;
            if (playerBriefInfo.Level != 0) roleInfo.level = playerBriefInfo.Level;
            roleInfo.lastUpd = roleInfo.roleAvatarId == "" ? 0 : Time.time;
            if (reputationLevel != -3) roleInfo.reputationLevel = reputationLevel;
        }
        /// <summary>
        /// 根据JSONNode更新角色信息
        /// </summary>
        /// <param name="jsonNode"></param>
        [Obsolete("待优化，角色缓存全部使用Mc.Config")]
        public void AddOrUpdateRoleInfo(JSONNode jsonNode)
        {
            var styleId = 0;
            var rankPoint = 0;
            var styleIDList = jsonNode["gameStyleIDs"].AsArray;
            if (styleIDList != null && styleIDList.Count > 0)
            {
                styleId = styleIDList[0].AsInt;
            }
            else
            {
                styleId = 1001;
            }
            var styleRankPoint = jsonNode["styleRankPoints"];
            if (styleRankPoint && styleRankPoint.Count > 0)
            {
                if (styleRankPoint.TryGetValue(styleId.ToString(), out JSONNode rankPointNode))
                {
                    rankPoint = rankPointNode.AsInt;
                }
            }
            Mc.Chat.AddOrUpdateRoleInfo(new RoleInfo()
            {
                roleId = jsonNode["roleid"].AsLong,
                roleName = jsonNode["nickname"].Value,
                roleAvatarId = jsonNode["portrait"].Value,
                avatarFrameId = jsonNode["portraitFrame"].AsInt,
                isRoleOnline = jsonNode["onlineStatus"].AsInt != 2,
                level = jsonNode["level"].AsInt,
                userPrivacy = (Config.EUserPrivacy)jsonNode["privacyFlags"].AsInt,
                styleID = styleId,
                rankPoint = rankPoint,
            });
        }
        public void OnGetRoleInfo(PlayerBriefInfo info)
        {
            if (string.IsNullOrEmpty(info.Name))
            {
                //角色名有可能为空，使用空字符串赋值，防止重复请求角色信息
                info.Name = "   ";
                logger.WarnFormat("RoleName is empty: {0} {1}", info.RoleId, info.Portrait);
            }
            AddOrUpdateRoleBriefInfo(info);
            UpdateRoleInfoCallback.TryGetValue(info.RoleId, out var action);
            action?.Invoke();
            UpdateRoleInfoCallback.Remove(info.RoleId);
        }

        public RoleInfo GetRoleInfo(ulong roleId, Action callback = null)
        {
            var ret = roleIdDic.GetValueOrDefault(roleId);
            if (roleId == this.roleId) { UpdateSelfInfo(); }
            else if (systemRoleDic.TryGetValue(roleId, out var roleInfo)) { return roleInfo; }
            //未缓存或上次更新在1分钟内或角色名为空(偶现hud第一条消息没有名字)
            else if (roleId != 0 && (ret == null /*Time.time - ret.lastUpd > 60f ||*/))
            {
                if (callback != null)
                {
                    if (UpdateRoleInfoCallback.ContainsKey(roleId))
                    {
                        UpdateRoleInfoCallback[roleId] -= callback;
                        UpdateRoleInfoCallback[roleId] += callback;
                    }
                    else
                    {
                        UpdateRoleInfoCallback.Add(roleId, callback);
                    }
                }
                Mc.Config.DeprecateLocalCache(roleId);
                RemoteCallGetPlayerBriefInfo(roleId);
            }
            return ret;
        }

        public RoleInfo GetRoleInfoByHudChat(ulong roleId, Action callback = null)
        {
            var ret = roleIdDic.GetValueOrDefault(roleId);
            if (roleId == this.roleId) { UpdateSelfInfo(); }
            else if (systemRoleDic.TryGetValue(roleId, out var roleInfo)) { return roleInfo; }
            //未缓存或上次更新在1分钟内或角色名为空(偶现hud第一条消息没有名字)
            else if (roleId != 0 && (ret == null || string.IsNullOrEmpty(ret.roleName)))
            {
                if (callback != null)
                {
                    if (UpdateRoleInfoCallback.ContainsKey(roleId))
                    {
                        UpdateRoleInfoCallback[roleId] -= callback;
                        UpdateRoleInfoCallback[roleId] += callback;
                    }
                    else
                    {
                        UpdateRoleInfoCallback.Add(roleId, callback);
                        RemoteCallGetPlayerBriefInfo(roleId);
                    }
                }
            }
            return ret;
        }

        public void OnGetRolesInfo(List<PlayerBriefInfo> infoLst)
        {
            foreach (var info in infoLst) { OnGetRoleInfo(info); }
            Mc.Msg.FireMsg(EventDefine.OnGetMultiPlayerBriefInfo);
        }

        public void GetRolesInfo(List<ulong> lst)
        {
            UpdateSelfInfo();
            RemoteCallGetMultiPlayerInfo((lst));
        }

        public void UpdateSelfInfo()
        {
            this.AddOrUpdateRoleBriefInfo(new PlayerBriefInfo(roleId, Mc.Config.nickName, Mc.Config.AvatarId, true, (uint)Mc.Config.lobbyRoleInfo.portraitFrame, Mc.Config.level));

        }
        #endregion

        #region Main
        public void AddRedUnSeen(ClientChatRoom room)
        {
            var channelId = room.ChannelId;
            if (ChatChannelData.GetChannelType(channelId) != EChatChannelType.World)
            {
                var channelType = ChatChannelData.GetChannelType(channelId);
                //私聊频道判断下读取位置
                if (channelType == EChatChannelType.Personal && room.RemoteSeq <= room.LastReadSeq)
                {
                    return;
                }
                var channelCategory = (int)ChatChannelData.GetChannelCategory(channelType);
                channelCategoryUnseenLst[channelCategory].Add(channelId);
                if (room.haveNotSeenTeamInvite) { channelCategoryUnseenInviteLst[channelCategory].Add(channelId); }
                this.MoveChannelToTop(channelId);
            }
        }
        public void RemoveRedUnSeen(string channelId)
        {
            if (ChatChannelData.GetChannelType(channelId) != EChatChannelType.World)
            {
                var channelCategory = (int)ChatChannelData.GetChannelCategory(channelId);
                channelCategoryUnseenLst[channelCategory].Remove(channelId);
                channelCategoryUnseenInviteLst[channelCategory].Remove(channelId);
                Mc.Msg.FireMsg(EventDefine.OnChatChannelModify);
                Mc.Msg.FireMsg(EventDefine.OnChatHudChangeEvent);
            }
        }
        /// <summary>
        /// 将已读索引更新至最新，用于聊天界面关闭的情况下清除未读红点
        /// </summary>
        /// <param name="channelId"></param>
        public void UpdateReadSeqToLast(string channelId)
        {
            if (chatRooms.TryGetValue(channelId, out var room))
            {
                room.MessageUpdate();
            }
        }

        public int CheckUnseen(EChatCategory channelCategory, string blockId = "0")
        {
            //if (channelCategoryUnseenInviteLst.Count == 0) return 0;
            //var count = channelCategoryUnseenInviteLst[(int)channelCategory].Count;
            //var hasBlock = channelCategoryUnseenInviteLst[(int)channelCategory].Contains(blockId) ? 1 : 0;
            //if (count - hasBlock > 0) { return 2; }
            var list = channelCategoryUnseenLst[(int)channelCategory];
            if (Mc.Config.lobbyRoleInfo.userPrivacy.HasFlag(Config.EUserPrivacy.StrangerPrivateChat) && channelCategory == EChatCategory.Personal)
            {
                list = GetFriendPersonalUnseen();
            }
            int unreadCount = 0;
            foreach (var roomId in list)
            {
                if (roomId == blockId) continue;
                var room = chatRooms.GetValueOrDefault(roomId);
                unreadCount += (int)room.UnseenCount;
            }
            return unreadCount;
        }
        /// <summary>
        /// 局内聊天按钮红点检测
        /// </summary>
        /// <returns></returns>
        public bool CheckRedDot()
        {
            foreach (var room in this.chatRooms.Values)
            {
                if (Mc.Config.lobbyRoleInfo.userPrivacy.HasFlag(Config.EUserPrivacy.StrangerPrivateChat)
                && ChatChannelData.GetChannelType(room.ChannelId) == EChatChannelType.Personal)
                {
                    if (Mc.LobbyFriend.IsFriend(room.ChannelId.ToString()) == false) { continue; }
                }
                if (room.ChannelId != ChatChannelData.channelWorldId && room.RemoteSeq > room.LastReadSeq)
                {
                    return true;
                }
            }
            return false;
        }
        public bool HasUnseenTeamInvite
        {
            get
            {
                foreach (var set in channelCategoryUnseenInviteLst)
                {
                    if (set.Count > 0) { return true; }
                }
                return false;
            }
        }

        private void OnCreateChannel(string channelId)
        {
            var lst = roomCategoryLst[(int)ChatChannelData.GetChannelCategory(channelId)];
            if (lst.Contains(channelId)) { return; }
            var cnt = lst.Count;
            switch (ChatChannelData.GetChannelTypeOrderType(ChatChannelData.GetChannelType(channelId)))
            {
                case EChannelTypeOrderType.Inplace:
                    int i;
                    for (i = 0; i < cnt; i++)
                    {
                        if (ChatChannelData.GetChannelOrder(lst[i]) <= ChatChannelData.GetChannelOrder(channelId)) { continue; }
                        break;
                    }

                    lst.Insert(i, channelId);
                    break;
                case EChannelTypeOrderType.Insert:
                    var channelType = ChatChannelData.GetChannelType(channelId);
                    if (lst.Count == ChatChannelData.GetChannelTypeCapacity(channelType))
                    {
                        var removeIdx = lst.Count - (chatRooms[lst[^1]].visLock ? 2 : 1);
                        var removeChannel = lst[removeIdx];
                        RemoveChannel(removeChannel);
                    }
                    lst.Insert(0, channelId);
                    if (lst.Count > Mc.Tables.TbGlobalConfig.MaximumNumberOfDisplays) { RemoveRedUnSeen(channelId); }
                    break;
                default:
                    break;
            }
            Mc.Msg.FireMsg(EventDefine.OnChatChannelModify);
        }

        public void OnRecvChannelMessage(string channelId)
        {
            var lst = roomCategoryLst[(int)ChatChannelData.GetChannelCategory(channelId)];
            switch (ChatChannelData.GetChannelTypeOrderType(ChatChannelData.GetChannelType(channelId)))
            {
                case EChannelTypeOrderType.Insert:
                    for (var i = 0; i < lst.Count; i++)
                    {
                        var room = lst[i];
                        if (i != 0)
                        {
                            // 如果当前所在频道为最后一个，且不是需要更新的界面，则跳过替换位置
                            if (room != channelId && i == Mc.Tables.TbGlobalConfig.MaximumNumberOfDisplays - 1 && chatRooms[lst[i]].visLock)
                            {
                                RemoveRedUnSeen(lst[i - 1]);
                                continue;
                            }
                            (lst[0], lst[i]) = (lst[i], lst[0]);
                            if (i >= Mc.Tables.TbGlobalConfig.MaximumNumberOfDisplays)
                            {
                                RemoveRedUnSeen(lst[i]);
                            }
                        }
                        if (room == channelId) { break; }
                    }
                    break;
                case EChannelTypeOrderType.Inplace:
                default:
                    break;
            }
            Mc.Msg.FireMsg(EventDefine.OnChatChannelModify);
        }
        #endregion

        #region 网络接口封装
        private bool isEntityReady => Mc.MyPlayer.MyEntityServer != null;
        #region NeedRemoteImplement

        //TODO: 聊天迁移
        private static void ChatLobbyCallBack(JSONNode result, Action callbackSuccess)
        {
            if (result == null) { return; }
            var retNode = result["result"];
            if (retNode == null) { return; }
            var errcode = retNode.AsInt;
            switch (errcode)
            {
                case 0:
                    callbackSuccess?.Invoke();
                    break;
                case 1:
                    //Mc.MsgTips.ShowRealtimeWeakTip(23179);  // 敏感词   聊天现在不做提示了，因为直接有***的替换了
                    break;
                case 2:
                    logger.Warn("聊天，大厅回包报错过于频繁提示");
                    //LanguageConst.
                    //Mc.MsgTips.ShowRealtimeWeakTip(LanguageManager.GetTextConst TableHelper.GetText("/ui/chat/过于频繁提示"));
                    break;
                default:
                    break;
            }
        }
        private void GetRoleInfoLobbyCallBack(JSONArray retArray)
        {
            if (retArray == null) { return; }
            foreach (var node in retArray)
            {
                //这里有全量数据，改为RoleInfo
                this.AddOrUpdateRoleInfo(node.Value);
                var roleId = node.Value["roleid"].AsULong;
                UpdateRoleInfoCallback.TryGetValue(roleId, out var action);
                action?.Invoke();
                UpdateRoleInfoCallback.Remove(roleId);
            }
            Mc.Msg?.FireMsg(EventDefine.OnGetMultiPlayerBriefInfo);
        }
        public void RemoteCallSendPrivateChat(ulong toRoleId, string content)
        {
            var roleInfo = GetRoleInfo(toRoleId);
            if (roleInfo != null
            && !Mc.Config.IsUserPrivacyOpen(roleInfo.userPrivacy, Config.EUserPrivacy.StrangerPrivateChat, true)
            && Mc.LobbyFriend.IsFriend(toRoleId.ToString()) == false)
            {
                //如果对方关闭了隐私设置，且不是好友关系，则不允许发送私聊
                return;
            }
            ulong.TryParse(Mc.Config.roleId, out var iRoleId);
            JSONObject body = new();
            body.Add("msg", content); body.Add("receiver", toRoleId.ToString());
            MicroServiceClient.Instance.CallLobbyPost("chatservice/personalmessage", EHttpReqModule.Chat, (result) => ChatLobbyCallBack(result, () =>
            {
                Mc.Chat.GetRoleInfo(toRoleId);
            }), body);
            //if (isInLobby) { }
            //else if(IsCollectionReady) { Mc.MyPlayer.MyEntityServer.RemoteCallSendPrivateChat(toRoleId, content); }
        }
        public void RemoteCallSendWorldChat(string content)
        {
            if (isInLobby)
            {
                JSONObject body = new();
                JSONObject group = new();
                group.Add("channelId", new JSONNumber(0));
                group.Add("id", new JSONString("0"));
                body.Add("group", group); body.Add("msg", content);
                body.Add("worldChatChannel", this.worldChatChannel);//分线Id
                MicroServiceClient.Instance.CallLobbyPost("chatservice/message", EHttpReqModule.Chat, InnerChatLobbyCallBackWithoutDeal, body, true, null, true);
            }
            else if (isEntityReady) { Mc.MyPlayer.MyEntityServer.MyChatComponent.RemoteCallSendWorldChat(ERpcTarget.World, content); }
        }
        public void RemoteCallGetPlayerBriefInfo(ulong targetRoleId)
        {
            RemoteCallGetMultiPlayerInfo(new() { targetRoleId });
            //if (isInLobby) {  }
            //else if (IsCollectionReady) { Mc.MyPlayer.MyEntityServer.RemoteCallGetPlayerBriefInfo(targetRoleId); }
        }
        public void RemoteCallGetMultiPlayerInfo(List<ulong> roleIds)
        {
            if (roleIds == null || roleIds.Count == 0) { return; }
            List<string> idList = new();
            foreach (var id in roleIds) { idList.Add(id.ToString()); }
            Mc.Config.RequestUserInfoList(EHttpReqModule.Chat, GetRoleInfoLobbyCallBack, idList);
        }
        #endregion
        public void RemoteCallSendTeamChat(string content)
        {
            if (isInLobby)
            {
                JSONObject body = new();
                JSONObject group = new();
                group.Add("channelId", new JSONNumber(1));
                group.Add("id", new JSONString(Mc.LobbyTeam?.GetCurTeamInfo()?.TeamID ?? "0"));
                body.Add("group", group);
                body.Add("msg", content);
                MicroServiceClient.Instance.CallLobbyPost("chatservice/message", EHttpReqModule.Chat, InnerChatLobbyCallBackWithoutDeal, body);
            }
            else if (isEntityReady) { Mc.MyPlayer.MyEntityServer.MyChatComponent.RemoteCallSendTeamChat(ERpcTarget.World, content); }
        }
        public void RemoteCallSendAppointmentChat(string content)
        {
            if (isInLobby)
            {
                JSONObject body = new();
                JSONObject group = new();
                group.Add("channelId", new JSONNumber(1));
                group.Add("id", new JSONString(Mc.LobbyTeam?.GetCurTeamInfo(true)?.TeamID ?? "0"));
                body.Add("group", group);
                body.Add("msg", content);
                MicroServiceClient.Instance.CallLobbyPost("chatservice/message", EHttpReqModule.Chat, InnerChatLobbyCallBackWithoutDeal, body);
            }
            else if (isEntityReady) { Mc.MyPlayer.MyEntityServer.MyChatComponent.RemoteCallSendTeamChat(ERpcTarget.World, content); }
        }

        public void RemoteCallGetTeamChatMessage()
        {
            if (!isInLobby && isEntityReady) { Mc.MyPlayer.MyEntityServer.MyChatComponent.RemoteCallGetTeamChatMessage(ERpcTarget.World); }
        }
        public void RemoteCallSetMessageRead(string channelId, uint sequence)
        {
            if (ChatChannelData.GetChannelType(channelId) != EChatChannelType.Personal)
            {
                return;
            }
            if (isInLobby)
            {
                JSONObject body = new();
                body.Add("msgID", sequence); body.Add("receiver", channelId.ToString());
                MicroServiceClient.Instance.CallLobbyPost("chatservice/personalmessage/read", EHttpReqModule.Chat, body: body);
            }
            else if (!isInLobby && isEntityReady)
            {
                Mc.MyPlayer.MyEntityServer.MyChatComponent.RemoteCallSetMessageRead(ERpcTarget.World, ulong.Parse(channelId), sequence);
            }
        }

        private static ulong removeWorldChatMsgRoleId;
        private static readonly Func<ClientChatMessage, bool> InnerRemoveWorldChatMsgPending = (msg) => msg.RoleId == (long)removeWorldChatMsgRoleId && msg.channelId.Equals(ChatChannelData.channelWorldId);
        public void RemoveWorldChatMsg(ulong roleId)
        {
            removeWorldChatMsgRoleId = roleId;
            var room = chatRooms[ChatChannelData.channelWorldId];
            totalMessageQueue?.Remove(InnerRemoveWorldChatMsgPending);

            if (room?.chatMessages != null)
            {
                room.chatMessages.Remove(InnerRemoveWorldChatMsgPending);
                Mc.Msg.FireMsg(EventDefine.OnRecvTotalMessage, room?.chatMessages.Back);
                Mc.Msg.FireMsg(EventDefine.OnChatChannelModify);
            }
        }
        #endregion

        #region Hud 放Ui层的话会需要遍历

        public void RecvTotalMessage(ClientChatMessage msg, bool unreadLstSync = false, bool triggerChannelMessage = true)
        {
            logger.InfoFormat("[Chat] RecvTotalMessage,channel:{0} role:{1} msg:{2}", msg.channelId, msg.RoleId, msg.MessageContent);
            if (Mc.LobbyFriend.blackIdInfos.Contains(msg.RoleId.ToString())) return;
            if (msg.channelId == ChatChannelData.channelTeamId && Mc.LobbyTeam.GetCurTeamInfo(true).TeamID != null && Mc.LobbyTeam.GetCurTeamInfo(true).TeamID.Equals(msg.GroupId))
            {
                //队伍频道，大厅那共用一个channelId=1，客户端自己区分普通队伍、预约队伍
                //改为预约频道
                msg.channelId = ChatChannelData.channelAppointment;
            }
            CreateRoom(msg.channelId);
            if (triggerChannelMessage) OnRecvChannelMessage(msg.channelId);
            if (!chatRooms.TryGetValue(msg.channelId, out var room)) { return; }
            if (room.UnRecvCount == 0)
            {
                CreateRoom(msg.channelId);
                room.ReceiveMsg(msg);
                Mc.Msg.FireMsg(EventDefine.OnChatRecvMessage, msg);
            }
            if (!unreadLstSync)
            {
                room.RemoteSeq++;
            }
            AddRedUnSeen(room);

            if (totalMessageQueue.Count == totalMessageQueue.Capacity) { totalMessageQueue.PopFront(); }
            //如果是未读消息，直接添加到总消息队列尾部
            if (room.UnseenCount > 0)
            {
                totalMessageQueue.PushBack(msg);
            }
            //如果不是新消息，往前插，保留最后一条消息
            else
            {
                var lastMsg = totalMessageQueue.PopBack();
                totalMessageQueue.PushBack(msg);
                totalMessageQueue.PushBack(lastMsg);
            }
            Mc.Msg.FireMsg(EventDefine.OnRecvTotalMessage, msg);
        }
        /// <summary>
        /// 补充信息数据
        /// 用于加载本地缓存、初始化ChatRoom、补充ChatRoom消息
        /// 非正常接收消息流程，不会触发接收消息的各事件
        /// </summary>
        /// <param name="msg"></param>
        public void CompensateMessage(ClientChatMessage msg)
        {
            var room = this.GetRoom(msg.channelId);
            room.ReceiveMsg(msg);
        }
        /// <summary>
        /// 收取部落聊天消息
        /// </summary>
        /// <param name="tribeId"></param>
        /// <param name="msg"></param>
        public void RecvTribeMessage(string tribeId, InGameMsgContent msg)
        {
            var tribeInfo = Mc.Tribe.GetMyTribeInfoById(tribeId);
            var room = this.GetRoom(tribeInfo.InGameGroupID);
            var newMsg = new ClientChatMessage(msg.Content, ulong.Parse(msg.SenderId), msg.SendTime, msg.Meta)
            {
                channelId = tribeInfo.InGameGroupID,
                GroupId = tribeId,
            };
            room.ReceiveMsg(newMsg);
            room.RemoteSeq++;
            AddRedUnSeen(room);
            logger.InfoFormat("[Chat] RecvTribeMessage,channel:{0} role:{1} msg:{2} meta:{3}", tribeId, msg.SenderId, msg.Content, msg.Meta);
            if (totalMessageQueue.Count == totalMessageQueue.Capacity) { totalMessageQueue.PopFront(); }
            totalMessageQueue.PushBack(newMsg);
            Mc.Msg.FireMsg(EventDefine.OnChatChannelModify);
            Mc.Msg.FireMsg(EventDefine.OnRecvTotalMessage, newMsg);
        }

        #endregion

        #region lambda
        private readonly Action<JSONNode> InnerChatLobbyCallBackWithoutDeal;

        public MgrChat() : base()
        {
            InnerChatLobbyCallBackWithoutDeal = (result) => ChatLobbyCallBack(result, null);
        }
        #endregion
    }
}
