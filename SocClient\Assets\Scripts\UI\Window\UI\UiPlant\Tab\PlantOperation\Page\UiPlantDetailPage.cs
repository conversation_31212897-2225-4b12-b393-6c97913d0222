using Assets.Scripts.Plant;
using FairyGUI;
using System;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Data.Plant;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Plant;
using WizardGames.Soc.SocClient.Ui.Utils;

namespace WizardGames.Soc.SocClient.Ui
{
    public sealed class UiPlantDetailPage : UiPlantBasePage
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(UiPlantDetailPage));
        public override EPageType PageType => EPageType.PlantDetail;
        
        private GButton closeBtn;
        private ComItemIcon itemIconCom;
        private GTextField plantNameTxt;
        private GTextField plantProgressTxt;
        private GTextField plantDescTxt;
        private GTextField plantStageText;
        private GButton removeBtn;
        private GButton manualBtn;
        private Controller manualBtnCtrl;
        private GButton harvestBtn;
        private Controller harvestBtnCtrl;
        private GButton detailBtn;
        private GTextField waterIntakeTxt;
        private GTextField harvestTxt;
        private GProgressBar manualProgressBar;
        private GProgressBar waterProgressBar;
        private GProgressBar lightProgressBar;
        private GProgressBar tempuratureProgressBar;
        private GProgressBar overallProgressBar;
        private GList geneLst;

        private Controller removeBtnCtrl;

        private PlantBoxData curPlantBox;
        private PlantData curPlant;

        private int slotIndex;

        private GObject waterBuff;
        private GObject manualBuff;

        public static UiPlantBasePage Create(GComponent com,UiPlantOperationSubPanel uiPlantOperationSubPanel)
        {
            UiPlantBasePage rt = new UiPlantDetailPage();
            rt.Init(com,uiPlantOperationSubPanel);
            return rt;
        }

        protected override void OnInit(GComponent com, UiPlantOperationSubPanel uiPlantOperationSubPanel)
        {
            base.OnInit(com, uiPlantOperationSubPanel);
            itemIconCom = com.GetChild("icon") as ComItemIcon;
            plantNameTxt = com.GetChild("plant_name_txt").asTextField;
            plantProgressTxt = com.GetChild("plant_progress_txt").asTextField;
            plantDescTxt = com.GetChild("plant_desc_txt").asTextField;
            plantStageText = com.GetChild("plantStageText").asTextField;

            var plantDesc = com.GetChild("plant_scroll_view").asCom;
            if (plantDesc != null)
            {
                waterIntakeTxt = plantDesc.GetChild("water_intake_content_txt").asTextField;
                harvestTxt = plantDesc.GetChild("harvest_content_txt").asTextField;
                manualProgressBar = plantDesc.GetChild("manual_progress").asProgress;
                waterProgressBar = plantDesc.GetChild("water_progress").asProgress;
                lightProgressBar = plantDesc.GetChild("light_progress").asProgress;
                tempuratureProgressBar = plantDesc.GetChild("tempurature_progress").asProgress;
                overallProgressBar = plantDesc.GetChild("overall_progress").asProgress;
                geneLst = plantDesc.GetChild("gene_list").asList;
            }

            detailBtn = com.GetChild("detail_btn").asButton;
            detailBtn.onClick.Set(OnClickDetail);
            
            removeBtn = com.GetChild("remove_btn").asButton;
            removeBtn.onClick.Set(OnClickRemove);
            removeBtnCtrl = removeBtn.GetController("enable");
            removeBtnCtrl.SetSelectedIndex(1);

            manualBtn = com.GetChild("manual_btn").asButton;
            manualBtn.onClick.Set(OnClickManual);
            manualBtnCtrl = manualBtn.GetController("enable");

            harvestBtn = com.GetChild("harvest_btn").asButton;
            harvestBtn.onClick.Set(OnClickHarvest);
            harvestBtnCtrl = harvestBtn.GetController("enable");
            harvestBtnCtrl.SetSelectedIndex(1);

            closeBtn = com.GetChild("close_btn").asButton;
            closeBtn.onClick.Set(OnClickClose);
            
            var buffRoot = com.GetChild("buffIcon");
            SafeUtil.SafeSetVisible(buffRoot, true);
            waterBuff = com.GetChild("n50");
            manualBuff = com.GetChild("n51");
        }

        protected override void OnEnable(object plantBox)
        {
            base.OnEnable(plantBox);
            curPlantBox = plantBox as PlantBoxData;
            Mc.Msg.AddListener<int>(EventDefine.DetailPageSelectedSlot, OnDetailPageSelectedSlot);
            Mc.Msg.AddListener<long>(EventDefine.UpdatePlantsInfo, OnUpdatePlantsInfo);
            Mc.Msg.AddListener<long, int>(EventDefine.RemovePlantSuccess, OnRemovePlantSuccess);
            Mc.Msg.AddListener<long, int>(EventDefine.HarvestSuccess, OnHarvestSuccess);
            manualBtnCtrl?.SetSelectedIndex(Mc.Plant.TotalManure > 0 ? 1 : 0);
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            Mc.Msg.RemoveListener<int>(EventDefine.DetailPageSelectedSlot, OnDetailPageSelectedSlot);
            Mc.Msg.RemoveListener<long, int>(EventDefine.RemovePlantSuccess, OnRemovePlantSuccess);
            Mc.Msg.RemoveListener<long>(EventDefine.UpdatePlantsInfo, OnUpdatePlantsInfo);
            Mc.Msg.RemoveListener<long, int>(EventDefine.HarvestSuccess, OnHarvestSuccess);
        }

        private void OnDetailPageSelectedSlot(int slot)
        {
            RefreshPlant(slot);
        }

        private void RefreshPlant(int slot)
        {
            slotIndex = slot;
            if (curPlantBox != null)
            {
                curPlant = curPlantBox.GetPlantData(slot);
                if (curPlant != null)
                {
                    if (itemIconCom != null && curPlant.plantSeedCfg != null)
                    {
                        try
                        {
                            itemIconCom.SetTepmlateData(curPlant.plantSeedCfg);
                            SafeUtil.SafeSetVisible(itemIconCom, true);
                            itemIconCom.CanDrag = false;
                        }
                        catch { }
                        SafeUtil.SafeSetText(plantNameTxt, curPlant.plantSeedCfg.Name);
                        SafeUtil.SafeSetText(plantDescTxt, curPlant.plantSeedCfg.DescriptionOfUse);
                    }

                    var seedCfg = Mc.Tables.TbPlantSeedConfig.GetOrDefault(curPlant.SeedId);
                    if (seedCfg != null)
                    {
                        SafeUtil.SafeSetText(waterIntakeTxt, GetRealWaterIntake(seedCfg, curPlant.GeneList).ToString());
                        SafeUtil.SafeSetText(harvestTxt, GetRealHarvest(seedCfg, curPlant.GeneList).ToString());
                    }

                    SafeUtil.SafeSetText(plantStageText, PlantUtils.GetStageDesc(curPlant.Stage));
                    if (curPlant.Stage == PlantStage.Harvest)
                    {
                        SafeUtil.SafeSetText(plantProgressTxt, string.Empty);
                    }
                    else
                    {
                        SafeUtil.SafeSetText(plantProgressTxt, SafeUtil.SafeStringFormat("{0}%", curPlant.GrowRate));
                    }

                    var minRate = float.MaxValue;
                    SafeUtil.SafeSetProgress(manualProgressBar, curPlant.GroudRate * 100);
                    if (manualProgressBar != null)
                    {
                        minRate = Mathf.Min(minRate, (float)manualProgressBar.value);
                    }
                    SafeUtil.SafeSetProgress(waterProgressBar, curPlant.WaterRate * 100);
                    if (waterProgressBar != null)
                    {
                        minRate = Mathf.Min(minRate, (float)waterProgressBar.value);
                    }
                    SafeUtil.SafeSetProgress(lightProgressBar, curPlant.LightRate * 100);
                    if (lightProgressBar != null)
                    {
                        minRate = Mathf.Min(minRate, (float)lightProgressBar.value);
                    }
                    SafeUtil.SafeSetProgress(tempuratureProgressBar, curPlant.TemperatureRate * 100);
                    if (tempuratureProgressBar != null)
                    {
                        minRate = Mathf.Min(minRate, (float)tempuratureProgressBar.value);
                    }
                    minRate = Mathf.Max(minRate, Mc.Tables.TbPlantConstConfig.MinOverallQuality * 100);
                    SafeUtil.SafeSetProgress(overallProgressBar, minRate);

                    SafeUtil.SafeSetVisible(removeBtn, curPlant.Stage != PlantStage.Harvest);
                    SafeUtil.SafeSetVisible(manualBtn, curPlant.CanManure());
                    SafeUtil.SafeSetVisible(harvestBtn, curPlant.Stage == PlantStage.Harvest);
                    SafeUtil.SafeSetVisible(waterBuff, curPlantBox.Water > 0);
                    SafeUtil.SafeSetVisible(manualBuff, !curPlant.CanManure());

                    if (geneLst != null && curPlant.GeneList != null)
                    {
                        geneLst.numItems = curPlant.GeneList.Count;
                        for (var i = 0; i < geneLst.numChildren; i++)
                        {
                            var button = geneLst.GetChildAt(i).asButton;
                            if (button == null) continue;
                            var geneConfig = Mc.Tables.TbPlantGeneConfig.GetOrDefault(curPlant.GeneList[i]);
                            if (geneConfig == null) continue;
                            var loader = button.GetChild("n7").asLoader;

                            loader.icon = string.Format("ui://GamePlant/{0}", geneConfig.Gene);
                            button.selected = geneConfig.IsPositive;
                        }
                    }
                }
            }
        }

        private void OnClickClose()
        {
            uiPlantOperationSubPanel.ChangePage(EPageType.Default);
            // Mc.Msg.FireMsg(EventDefine.OnChangePlantMainPage, EPageType.Default);
        }

        private void OnClickDetail()
        {
            if (curPlant != null && curPlant.plantSeedCfg != null && detailBtn != null)
            {
                UiItemTips.ShowTips(curPlant.plantSeedCfg, detailBtn.position, false, true);
            }
        }

        private void OnClickRemove()
        {
            UiMsgBox.ShowMsgBox(new()
            {
                //是否确认移除植株？
                Msg = LanguageManager.GetTextConst(LanguageConst.PlantDelConfirm),
                BtnList = new() { new(LanguageManager.GetTextConst(LanguageConst.Cancel), null), new(LanguageManager.GetTextConst(LanguageConst.Confirm), () =>
                    {
                        curPlantBox.RequestRemove((int)curPlant.Slot);
                        uiPlantOperationSubPanel.ChangePage(EPageType.Default);
                        // Mc.Msg.FireMsg(EventDefine.OnChangePlantMainPage, EPageType.Default);
                    })}
            });
        }

        private void OnClickManual()
        {
            curPlantBox?.RequestManure(new List<int> { (int)curPlant.Slot });
        }

        private void OnClickHarvest()
        {
            if (curPlantBox != null && curPlant != null)
                curPlantBox.RequestHarvest((int)curPlant.Slot);
        }

        private void OnHarvestSuccess(long collectionId, int idx)
        {
            if (collectionId == curPlantBox.EntityId)
            {
                OnClickClose();
            }
        }

        private void OnRemovePlantSuccess(long collectionId, int slot)
        {
            if (curPlantBox != null && curPlant != null)
            {
                if (curPlantBox.EntityId == collectionId && curPlant.Slot == slot)
                {
                    OnClickClose();
                }
            }
        }

        private void OnUpdatePlantsInfo(long collectionId)
        {
            if (curPlantBox != null)
            {
            }
            if (curPlantBox != null && curPlantBox.EntityId == collectionId && curPlant != null)
            {
                RefreshPlant(slotIndex);
            }
        }

        private int GetRealHarvest(PlantSeedConfig cfg, BasicTypeList<int> geneLst)
        {
            if (cfg == null || geneLst == null) return 0;
            var yNum = 0;
            foreach (var item in geneLst)
            {
                if (item == 2) ++yNum;
            }
            return Mathf.RoundToInt((1 + yNum * Mc.Tables.TbPlantConstConfig.GeneYYieldIndex) * Mc.Tables.TbPlantConstConfig.BaseYieldIncreaseMultiplier * cfg.PickupMultiplier);
        }

        private int GetRealWaterIntake(PlantSeedConfig cfg, BasicTypeList<int> geneLst)
        {
            if (cfg == null || geneLst == null) return 0;
            var wNum = 0;
            foreach (var item in geneLst)
            {
                if (item == 5) ++wNum;
            }
            return Mathf.RoundToInt(cfg.BasicWaterIntake * (1 + wNum * Mc.Tables.TbPlantConstConfig.GeneWIndex));
        }
    }
}
