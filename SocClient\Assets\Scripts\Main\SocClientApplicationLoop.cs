using Animation;
using Assets.Scripts.MicroServiceClient;
using Cysharp.Text;
using Effect;
using Framework.Replay;
using log4net;
using Share.Common.ObjPool;
using System;
using UnityEngine;

using UnityEngine.Rendering.Universal;
using UnityEngine.SceneManagement;
using Utilities;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Framework;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.MonumentPath;
using WizardGames.Soc.Common.Network;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.Profile;
using WizardGames.Soc.Common.Runtime;
using WizardGames.Soc.Common.Syncronization;
using WizardGames.Soc.Common.Unity;
using WizardGames.Soc.Common.Unity.Animation;
using WizardGames.Soc.Common.Unity.Animation.PhysicsBone;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.Construction;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Loader.Logger;
using WizardGames.Soc.Common.Unity.Main;
using WizardGames.Soc.Common.Unity.Manager;
using WizardGames.Soc.Common.Unity.ObjPool;
using WizardGames.Soc.Common.Unity.Pandora;
using WizardGames.Soc.Common.Unity.Runtime.AreaBox;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.PlatformAsset;
using WizardGames.Soc.SDK;
using WizardGames.Soc.ShaderVariantManager;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocClient.ActionExecutor;
using WizardGames.Soc.SocClient.Animation;
using WizardGames.Soc.SocClient.Audio;
using WizardGames.Soc.SocClient.Audio.WWise;
using WizardGames.Soc.SocClient.Collection;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Log;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Network;
using WizardGames.Soc.SocClient.Ui;
using WizardGames.Soc.SocClient.Utility;
#if !(UNITY_EDITOR || UNITY_STANDALONE_WIN)
using WizardGames.Soc.SocClient.Utility;
#endif
#if MSDK
using GCloud.MSDK;
#endif
#if POCO
using SocRpcMsg;
using SocRpcPoco;
#endif
#if UNIT_TEST
using WizardGames.Soc.SocClient.Test.Automation;
#endif


namespace WizardGames.Soc.SocClient.Main
{
    /// <summary>
    /// 客户端主循环
    /// </summary>
    public class SocClientApplicationLoop : UnityMainLoop
    {
        public static SocLogger logger = LogHelper.GetLogger(typeof(SocClientApplicationLoop));
        /// <summary>
        /// 服务器ip
        /// </summary>
        [Tooltip("（废弃功能）服务器ip")]
        public string ServerIP = "127.0.0.1";

        /// <summary>
        /// 场景是否已经加载
        /// </summary>
        [Tooltip("（废弃功能）是否已经在游戏场景中了， 用于测试时期快速进入场景, 勾选之后将不再执行场景加载")]
        public bool isSceneLoaded = true;

        /// <summary>
        /// 是否是在线场景
        /// </summary>
        [Tooltip("是否是在线场景")] public static bool isOnline = true;

        private static SocClientLogService ClientLogService;


        protected override LoopType _loopType => LoopType.Application;

        /// <summary>
        /// 日志记录器
        /// </summary>
        public ILog Log;

        /// <summary>
        /// 进入大厅场景时的调用
        /// </summary>
        public static Action OnEnterLobbyScene;
        
        public static Action LateUpdateAction;

#if UNITY_EDITOR
        private static Action unregisterUtilsLoad;
        static void UnregisterUtilsLoad()
        {
            UtilsLoad.OnLoadObjectCbk -= RuntimeAssetPostProcess.LoadAssetComplete;
            UtilsLoad.ConvertPathHooker -= RuntimeAssetPostProcess.ConvertLoadPath;
            Application.quitting -= unregisterUtilsLoad;
        }
#endif
        
        /// <summary>
        /// Unity脚本苏醒
        /// </summary>
        public override void Awake()
        {
            base.Awake();

            //绑定后处理
            UtilsLoad.OnLoadObjectCbk -= RuntimeAssetPostProcess.LoadAssetComplete;
            UtilsLoad.OnLoadObjectCbk += RuntimeAssetPostProcess.LoadAssetComplete;
            //绑定转换路径，覆盖编辑器的转换路径逻辑
            UtilsLoad.ConvertPathHooker -= RuntimeAssetPostProcess.ConvertLoadPath;
            UtilsLoad.ConvertPathHooker += RuntimeAssetPostProcess.ConvertLoadPath;
            
#if UNITY_EDITOR
            unregisterUtilsLoad = UnregisterUtilsLoad;
            
            Application.quitting -= unregisterUtilsLoad;
            Application.quitting += unregisterUtilsLoad;
#endif


            //默认开启transform dispatch 优化
            GlobalOptimizationParameters.EnableCustomDispatchChange();
            GlobalOptimizationParameters.SetScanMinimalThreshold(2);

#if !FIXED_UPDATE_SKM_OPT_CRASH
            // 该优化存在Crash风险，暂时关闭，到引擎修复后放出
            GlobalOptimizationParameters.DisableOptSkinPoseMatrixCalcJob();
#endif
            // //目前static binding有问题，暂时关闭
            // Animator.SetGlobalEnableStaticBindings(false);

#if ENABLE_SOC_PROFILER
            ProfilerApi.Instance.InitProfilerFunc = InitProfiler;
#endif

            //注册动画回调
            if (SocAnimationManager.CreateSocAnimatorRuntime == null)
            {
                SocAnimationManager.CreateSocAnimatorRuntime = () => new SocAnimatorRuntime();
            }

            // 绑定场景加载+卸载事件
            SceneManager.sceneLoaded += OnSceneLoaded;
            SceneManager.sceneUnloaded += OnSceneUnloaded;
        }

        public static void InitProfiler()
        {
            //第一次启用的时候才会进
            if (!ProfilerApi.Instance.HasProfiler(EPROFILER_TYPE.UnityFunc))
            {
                UnityProfileFuncReg.Reg();
            }
        }

        public static void RestartApp()
        {
#if UNITY_ANDROID
            AndroidJavaClass unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
            AndroidJavaObject currentActivity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");
            AndroidJavaObject pm = currentActivity.Call<AndroidJavaObject>("getPackageManager");
            AndroidJavaObject intent = pm.Call<AndroidJavaObject>("getLaunchIntentForPackage", currentActivity.Call<string>("getPackageName"));
            intent.Call<AndroidJavaObject>("addFlags", 0x20000000); //Intent.FLAG_ACTIVITY_SINGLE_TOP
            currentActivity.Call("startActivity", intent);
            currentActivity.Call("finish");
            System.Diagnostics.Process.GetCurrentProcess().Kill();
#elif UNITY_IOS
            // Terminate the application
            UnityEngine.iOS.Device.RequestStoreReview(); // This is a workaround to exit the app
            Application.Quit();
    
            // Relaunch the application
            var url = Application.absoluteURL;
            Application.OpenURL(url);
#endif
        }


        /// <summary>
        /// 是否可以执行本地移动预判
        /// </summary>
        // private bool canPredictMove = false;

#pragma warning disable 1998

        /// <summary>
        /// 脚本启动
        /// </summary>
        public override async void Start()
        {
            //TGPA相关,游戏启动
            TGPAWrapper.UpdateGameInfo(EWrapperGameDataKey.Scene, 1);
            //移动端SDK登录回调
            MSDKWrapper.SetLoginCB((method, resultJson) =>
            {
                logger.InfoFormat(">>>>LoginCB: method {0} resultJson:{1}", method, resultJson);
                try
                {
#if MSDK
                    //登录超时，我们自己设置的玩家登录超时，关闭loading
                    if (method == MSDKWrapper.MSDK_LOGIN_TIMEOUT_CODE) {
                        UiLiteLoading.HideLoading();
                        //弹窗提示
                        MsgBoxInfo boxInfo = new MsgBoxInfo(MsgBoxConst.LoginFailed, new() { ()=> { } });
                        boxInfo.SetCenter = true;
                        UiMsgBox.ShowMsgBox(boxInfo);
                        return; 
                    }
                    MSDKWebViewHandle.ParseResultJson(resultJson);
                    ClientMSDKProcessor.LoginProcess(method, resultJson);
                    //TPGA相关，上报openid
                    TGPAWrapper.UpdateGameInfo("OpenID", Mc.Config.openId);
                    TGPAWrapper.UpdateGameInfo(EWrapperGameDataKey.MainVersion, Common.Unity.Loader.SocAppContext.Client_Version);
                    TGPAWrapper.UpdateGameInfo(EWrapperGameDataKey.MainVersion, Common.Unity.Loader.SocAppContext.Res_Version);

                    PandoraWrapper.Instance.SetGCloudMSDKData(resultJson);
#endif
                }
                catch (Exception e)
                {
                    logger.ErrorFormat(">>>> LoginCB Error: {0}", e.ToString());
                    throw e;
                }
            });
            //Windows端SDK登录回调
            WeGameSDKWrapper.SetLoginCB((method, resultJson) => {
                WeGameSDKProcessor.LoginProcess(method, resultJson);
                PandoraWrapper.Instance.SetGCloudMSDKData(resultJson);
#if STANDALONE_REAL_PC && ENABLE_ANTICHEAT
                // WeGame登录成功后设置ACE SDK
                Mc.Ace.SetupAceSdkUserWeGame();
#endif
            });
            //安卓主动申请权限
#if !UNITY_EDITOR && (UNITY_ANDROID || UNITY_IOS)
            DevicePermissionMgr.GetInstance().InitPermissionCallbacks();
#if !POCO
            if (!Common.Unity.Loader.ApplicationConfig.Instance.IsFirstRequestPermission())
            {
                DevicePermissionMgr.GetInstance().RequestUserPermissions();
            }
#endif
#endif

            RuntimeEnvironment.CurrentEndPoint = Endpoint.Client;

            WizardGames.Soc.Common.Unity.Synchronization.MessagePack.Initialize();

            ClientLogService = new SocClientLogService();
            ClientLogService.InitService("SocClient", "Config/log4net_client", "log");

            ApplicationConfig.Instance.Init(true);

            //在begin之后注册事件
            AnimBeginEventRegister.Register();

#if ENABLE_SOC_PROFILER && UNITY_5_6_OR_NEWER
            Common.Profile.ProfilePlayerLoopSystemRegister.Register();
    #if ENABLE_MALLOC_AUTOHOOK //内存工具使用
            ProfilerApi.Instance.SetFrameEndOp(EProfileFrameEndOp.Enable);
    #endif
#endif
            PropArrayPoolInitializer.Init();
            PoolElementRegister.Init();
            //Debug.Log("SocClientMainLoop.Start()");
            Entity.EntTypeCacheRegister.Init();

            //Debug.Log("SocClientMainLoop Save UnityMainLoop Instance");

            // if (Application.platform == RuntimePlatform.Android || Application.platform == RuntimePlatform.IPhonePlayer)
            //     Application.targetFrameRate = 60;


            /*todo 客户端也需要生成一些本地实体,需要实例化实体id生成器*/
            IdGenerator.InitSnowFlake(30);
            //注册table加载函数
            Common.Data.MgrTables.TextGetter = LanguageManager.GetTextConst;
            Common.Data.MgrTables.UnityConfigLoader = ResProviderTable.LoadTable;
            Common.Data.MgrTables.UnityConfigUnloader = ResProviderTable.UnLoadTableResource;

            await CustomTypeLoader.LoadCustomData();
            RpcParamsAutoGenerate.Init();

            //初始化管理器
            Mc.Init();
            // 初始化Hotfix中心，注册Hotfix执行函数到一帧的结尾，因为表格Hotfix的原因，需要在表格初始化后初始化
            HotfixCenter.Instance.Init();
            
            // PlayerLogicStateAutoUtility.InitData();
            PlayerLogicJobModel.InitData();
            //McCommonUnity.AssetBundleRes = BundleRuntimeContext.AssetBundleRes;
            //McCommonUnity.AssetBundleMap = BundleRuntimeContext.AssetBundleMap;

            //创建客户端网络服务
            Mc.LocalService = new SocClientService();

            CustomTypeHelper.InitCustomTypeHelper();
            CustomTypeRegister.Register();
            EmbeddedCustomManager.Instance.Init();

            //更新资源
            //await UpdateResBundles();

            McCommonUnity.Scene.OnGameSceneReadyFunc += CreateGameMainLoop;
            McCommonUnity.Scene.BeforeExitGameSceneFunc += BeforeExitGameScene;
            McCommonUnity.Scene.AfterExitGameSceneFunc += AfterExitGameScene;
            McCommonUnity.Scene.OnLobbySceneReadyFunc += OnLobbySceneReady;

            EnterApplicationSpace();

            if (!isSceneLoaded)
            {
                //UiFullScreenVideo.PlayVideo("Video/socm_lobby", true);
                //登入入口
                // Mc.LoginStep.OpenLogin();
                // 热更完毕后第一屏展示游戏忠告, 展示完毕后打开登录界面
                Mc.LoginStep.OpenGameAdvice();
            }
            else
            {
                //本地测试的时候， 已经打开了对应的游戏场景
                OnSceneHasLoaded();
            }
            //
            //if (Common.Unity.Loader.SocAppContext.IsArgContains("poco"))
            //{
#if POCO
            logger.Info("poco 启动");
            var pocoGo = new GameObject("Poco"); // 修改变量名避免冲突
            var pocoManagerInstance = pocoGo.AddComponent<PocoManager>(); // 获取 PocoManager 实例
            pocoGo.AddComponent<InterfaceInfoClass>();
            //pocoGo.AddComponent<PocoSnippingTools>();
            Mc.PocoMa = pocoManagerInstance; // 将实例赋值给 Mc.PocoMa
            Mc.TimerWheel.AddTimerExplicit(1000, -1, 5000, GrabRpc.SendPocoSignin, "PocoSignin");
            DontDestroyOnLoad(pocoGo);
#endif
#if UNIT_TEST
                // 内置脚本用来处理cmd消息的，如果不调用，人物不会移动
                Mc.AutoUnit = new AutoUnitTest();
                AutoUnitTest.AutoRpcQueue = new();
                // QA冒烟测试
#if AWAIT_TEST//需要进行一些操作才会开始自动测试
                logger.Info("启动操作监听");
                gameObject.AddComponent<PocoSnippingTools>();
#else
                //#if UNITY_EDITOR
                //if (MainTest.StartAutomationOrNot())// 这里的逻辑后面去掉
                //{
                //#endif
                logger.Info("QA专用logtitle,进入自动化跑测拉");
                gameObject.AddComponent(typeof(MainTest));
                //#if UNITY_EDITOR
                //}
                //#endif
#endif
#endif
            //}
            
            //await TestResLoader();
            InitErrorLogFormatter();

            // 必须要先获取一次设备信息, 因为登录流程依赖于缓存的设备信息, 且设备信息相关接口无法在子线程上使用
            var di = await SystemUtil.PrepareDeviceInfo();
            //Debug.Log($">>>>DeviceInfo:{di.ToJson()}");

            // 解析Replay命令行参数
            ReplayCommandLineParser.ParseCommandLine();

            Mc.Msg.AddListener(EventDefine.BeforeSkin, FireBeforeSkinSystem);
            
            // 重新加载Reload属性标记的Shader
            RuntimeResourceReloader.ReloadAllShadersInAssetBundle();
            
            // 初始化潘多拉
            PandoraWrapper.Instance.Init();
        }


        public override void OnDestroy()
        {
            if (IsLoopActive) MicroServiceClient.Instance?.Dispose();
            Mc.Msg.RemoveListener(EventDefine.BeforeSkin, FireBeforeSkinSystem);
            base.OnDestroy();
        }

        /// <summary>
        /// 处理客户端直接在游戏场景中启动调试的情况
        /// </summary>
        private async void OnSceneHasLoaded()
        {
            if (!isSceneLoaded)
                return;

            McCommonUnity.Scene.ChangeSceneDummy();
        }

        public static void CreateEffectManager()
        {
            Mc.LoginStep.StepLog("创建EffectManager");
            var go = new GameObject("EffectManager");
            go.AddComponent<EffectManager>();
        }
        /// <summary>
        /// 进入游戏场景的时候创建场景内的GameMainLoop
        /// </summary>
        public static void CreateGameMainLoop()
        {
            Mc.LoginStep.StepLog("创建GameMainLoop");
            // 即使断线， 也继续创建GameMainLoop, 在重新连接成功之前， GameMainLoop的Start在连上World之前不会执行完毕
            //if (isOnline)
            //{
            //    if (!Mc.LocalService.IsConnected)
            //    {
            //        Mc.LoginStep.StepLogError("创建GameMainLoop时断线, 返回大厅");
            //        Mc.LoginStep.StartLogoutFlow();
            //        //Mc.LocalService.StopService();
            //        //Mc.Ui.HideWindow("UiLoading");
            //        //McCommonUnity.Scene.ChangeSceneAsync(SceneName.Login);
            //        return;
            //    }
            //}
            var go = new GameObject("GameMainLoop");
            if (isOnline)
                go.AddComponent<SocClientMainLoop>();
            else
            {
                go.AddComponent<SocClientMainLoopOffline>();
            }
            //DontDestroyOnLoad(go);
        }

        /// <summary>
        /// 时机为Game场景卸载，MC调用OnExitWorld之前
        /// </summary>
        private void BeforeExitGameScene()
        {
            UiLoading.ShowMsg($"{LanguageManager.GetTextConst(LanguageConst.StartCleanGameData)}(1/3)...", 60);
            try
            {
                // 清理Entity和Go
                Mc.LoginStep.StepLog("[UILoading] [CleanStep1] MgrEntity.ClearOnDisconnect...");
                Mc.Ui.ClearAllWindow();
                Mc.Entity.ClearOnDisconnect(true);

                Mc.LoginStep.StepLog("[UILoading] [CleanStep1] EmbeddedCustomManager.Cleanup...");
                EmbeddedCustomManager.Instance.Cleanup();

                Mc.LoginStep.StepLog("[UILoading] [CleanStep1] Mc.CleanUp...");
                Mc.Instance.CleanUp(false);
            }
            catch (Exception e)
            {
                Mc.LoginStep.StepLogError($"[UILoading] error in BeforeExitGameScene [CleanStep1]: {e}");
            }
        }

        /// <summary>
        /// 时机为Game场景卸载，MC调用OnExitWorld之后
        /// </summary>
        private void AfterExitGameScene()
        {
            UiLoading.ShowMsg($"{LanguageManager.GetTextConst(LanguageConst.StartCleanGameData)}(2/3)...", 60);
            try
            {
                // 清理Controller
                //Mc.Instance.ClearAllControllers();
                ClientSnapshotReceiverDebugger.Release();
                SocAnimationManager.Clear();

                SocAnimCache.Clear();

                // 清理Manager
                Mc.Instance.AfterExitWorld();
                // 清理对象池
                GoPool.ClearAll();
                Pool.ClearAllPools();
                // 清除游戏内的定时器
                Mc.Instance.ResetTimer();
                // 表格初始化
                Mc.Tables.Reset();
                // 清理TemplateGoConfig
                CommonConstructionUtils.ClearTemplateGoConfigData();
                //Mc.Predict.Clear();
                MgrPathPoint.Clear();
                MgrAreaBox.Clear();
            }
            catch (Exception e)
            {
                Mc.LoginStep.StepLogError($"[UILoading] error in AfterExitGameScene [CleanStep2]: {e}");
            }
            // 主动垃圾回收
            UiLoading.ShowMsg($"{LanguageManager.GetTextConst(LanguageConst.StartCleanGameData)}(3/3)...", 80);
            GC.Collect();
        }

        public static void OnLobbySceneReady()
        {
            var mgrLogin = Mc.LoginStep;
            // 如果玩家的Token失效了, 直接进入登录界面
            if (string.IsNullOrEmpty(Mc.Config.token))
            {
                mgrLogin.StepLog("[OnLobbySceneReady] user token is null or empty, go to login");
                mgrLogin.OpenLogin();
                OnEnterLobbyScene?.Invoke();
                OnEnterLobbyScene = null;

                return;
            }
            // 如果玩家返回大厅后需要立即重连上一个服务器, 则执行重连
            if (mgrLogin.ReconnectCurSvrWhenReturnToLobby)
            {
                mgrLogin.ReconnectCurSvrWhenReturnToLobby = false;
                mgrLogin.IsReconnectCurSvrWhenReturnToLobby = true;
                if (null != mgrLogin.CurSvrInfo)
                {
                    mgrLogin.StepLog("[OnLobbySceneReady] reconnect to last server as soon as reurn to the lobby");
                    mgrLogin.StartLoginFlow(mgrLogin.CurSvrInfo);
                    return;
                }
            }
            // 否则, 加载大厅场景, 并打开大厅界面
            Action onModelLoad = () =>
            {
                LoginStepHelper.HandleOnlineStatus();
                OnEnterLobbyScene?.Invoke();
                OnEnterLobbyScene = null;
            };
            mgrLogin.StepLog("[OnLobbySceneReady] prepare lobby scene");
            Mc.LoginStep.PrepareLobbySceneModel(onModelLoad);
        }

        public override void OnFpsUnlimitedUpdate(int timeSinceLastUpdate, int timeToNextUpdate, int targetFps)
        {
            base.OnFpsUnlimitedUpdate(timeSinceLastUpdate, timeToNextUpdate, targetFps);

            // 检查主线程回调
            McCommonUnity.ThreadTask.Update();
            McCommonUnity.WebSocket.Update();
            Mc.SettingClient.Update();
            // 更新输入
            if (Mc.ControlLobby != null) Mc.ControlLobby.CollectInput();
            //更新UI
            Mc.Ui.OnFpsUnlimitedUpdateUI(timeSinceLastUpdate);
            //更新平台头像
            Mc.PlatformPlayerInfo.OnFpsUnlimitedUpdate(timeSinceLastUpdate);
            //服务更新
            Mc.LocalService.UpdateService(false);

            if (ProcessEntity.LastTimeStamp == 0)
            {
                Mc.TimerWheel.Update(1);
                Mc.TimerWheelPersistent.Update(1);
                ProcessEntity.LastTimeStamp = TimeStampUtil.GetRawTimestampMsec();
            }
            else
            {
                var now = TimeStampUtil.GetRawTimestampMsec();
                var delta = now - ProcessEntity.LastTimeStamp;
                if (delta < 0)
                {
                    Mc.TimerWheel.Update(10);
                    Mc.TimerWheelPersistent.Update(10);
                }
                else
                {
                    if (delta > 5000)
                    {
                        logger.InfoFormat("TimerWheel delta {0}", delta);
                    }
                    uint deltaTime = (uint)delta;
                    Mc.TimerWheel.Update(deltaTime);
                    Mc.TimerWheelPersistent.Update(deltaTime);
                    ProcessEntity.LastTimeStamp = now;
                }
            }
        }


        public override void OnFps30Update(int timeSinceLastUpdate, int timeToNextUpdate, int targetFps)
        {
            base.OnFps30Update(timeSinceLastUpdate, timeToNextUpdate, targetFps);
            //更新合批事件
            Mc.Msg.DoUpdate();

            //更新UI
            Mc.Ui.OnFps30UpdateUI(timeSinceLastUpdate);

            // 语音大厅刷新
            ProfilerApi.BeginSample(EProfileFunc.OnFps30Update_Voice);
            if (Mc.Voice != null)
            {
                Mc.Voice.Update(timeSinceLastUpdate);
            }
            ProfilerApi.EndSample(EProfileFunc.OnFps30Update_Voice);

            // 处理音频逻辑
            SoundBankManager.ProcessEvents();
        }


        public override void OnFps10Update(int timeSinceLastUpdate, int timeToNextUpdate, int targetFps)
        {
            base.OnFps10Update(timeSinceLastUpdate, timeToNextUpdate, targetFps);

            //更新UI
            Mc.Ui.OnFps10UpdateUI(timeSinceLastUpdate);
            Mc.PowerOptimization.OnFps10Update();
        }

        public override void OnFps2Update(int timeSinceLastUpdate, int timeToNextUpdate, int targetFps)
        {
            base.OnFps2Update(timeSinceLastUpdate, timeToNextUpdate, targetFps);

            Mc.Ui.OnFps2UpdateUI(timeSinceLastUpdate);
        }

        public override void OnFps1Update(int timeSinceLastUpdate, int timeToNextUpdate, int targetFps)
        {
            base.OnFps1Update(timeSinceLastUpdate, timeToNextUpdate, targetFps);

            McCommonUnity.Res.Update();

            //更新UI
            Mc.Ui.OnFps1UpdateUI(timeSinceLastUpdate);

            // 省电更新
            Mc.PowerOptimization.OnFps1Update();
            if (Mc.Report != null)
            {
                Mc.Report.UpdateOn1Fps(timeSinceLastUpdate);
            }
            //SDK update
            Mc.SDK.OnFps1UpdateUI(timeSinceLastUpdate);

            Mc.SettingClient.OnFps1Update(timeSinceLastUpdate);

            Mc.Thermal.OnFps1Update();

            DeviceMemoryMonitor();
        }

        private void DeviceMemoryMonitor()
        {
            GoPool.Update();
        }

        private void InitErrorLogFormatter()
        {
            ErrorLogFormatter.GetPlayerPosition = () =>
            {
                try
                {
                    if (Mc.MyPlayer != null && Mc.MyPlayer.MyEntityLocal != null)
                    {
                        return new Vector3(
                            Mc.MyPlayer.MyEntityLocal.PosX,
                            Mc.MyPlayer.MyEntityLocal.PosY,
                            Mc.MyPlayer.MyEntityLocal.PosZ
                        ).ToString();
                    }
                    return "unknown";
                }
                catch (Exception)
                {
                    // ignored
                }
                return "unknown";
            };
            ErrorLogFormatter.GetWeaponName = () =>
            {
                try
                {
                    if (Mc.Tables != null && Mc.MyPlayer != null && Mc.MyPlayer.MyEntityLocal != null)
                    {
                        var itemConfig = Mc.Tables.TbItemConfig.GetOrDefault(Mc.MyPlayer.MyEntityLocal.CurrentWeaponTableId);
                        if (itemConfig != null)
                        {
                            return itemConfig.Name;
                        }
                    }

                    return "unknown";
                }
                catch (Exception)
                {
                    // ignored
                }

                return "unknown";
            };
        }

        #region Scene

        private void OnSceneLoaded(Scene iScene, LoadSceneMode iMode) { }

        private void OnSceneUnloaded(Scene iScene) { }

        #endregion


        private void OnApplicationPause(bool pauseStatus)
        {
            Mc.LoginStep.StepLog(ZString.Format("[SocClientApplicationLoop] OnApplicationPause, status = {0}", pauseStatus));

            if (Mc.Voice != null)
            {
                Mc.Voice.OnApplicationPause(pauseStatus);
            }

            Mc.PowerOptimization?.OnApplicationPause(pauseStatus);

            // 通知网络服务处理应用暂停
            Mc.LocalService?.OnApplicationPause(pauseStatus);
            
            if (LogCollector.Instance != null)
                LogCollector.Instance.Flush();

            LogHelper.Flush(true);

#if !STANDALONE_REAL_PC && ENABLE_ANTICHEAT
            AntiCheatSDKWrapper.Pause(pauseStatus);
#endif

            SocShaderVariantManager.SaveAndUploadCompiledVariants();
#if !PUBLISH
            AudioEventUsageTracker.SaveAndUploadUsedEvents();
#endif
            
#if UNITY_IOS
            // iOS高刷屏存在BUG，在50帧模式下切换后台/锁屏后再切回游戏，会导致屏幕刷新率异常处于48Hz/30Hz，进而导致游戏帧率异常
            // 可以通过设置帧率为60并延迟一帧设回原帧率来解决这个问题
            if (!pauseStatus)
            {
                Mc.SettingClient.DelayRefreshFrameRate();
            }
#endif

#if POCO // Poco 相关逻辑用宏包裹
            // 在应用进入后台时停止 Poco 服务
            if (pauseStatus)
            {
                if (Mc.PocoMa != null)
                {
                    logger.Info("PocoManager: Stopping listening due to application pause.");
                    try
                    {
                        Mc.PocoMa.StopListening();
                    }
                    catch (Exception ex)
                    {
                        logger.ErrorFormat("PocoManager: Error stopping listening: {0}", ex.Message);
                    }
                }
            }
            // 在应用从后台恢复时启动 Poco 服务
            else
            {
                if (Mc.PocoMa != null)
                {
                    logger.Info("PocoManager: Starting listening due to application resume.");
                    try
                    {
                        Mc.PocoMa.StartListening();
                    }
                    catch (Exception ex)
                    {
                        logger.ErrorFormat("PocoManager: Error starting listening: {0}", ex.Message);
                    }
                }
            }
#endif
        }

        private void FireBeforeSkinSystem()
        {
            //依赖动画begin的得在这里跑，在update和lateupdate之间
            ProfilerApi.BeginSample(EProfileFunc.LateUpdate_System_ClientPhysicsBoneSystem);
            PhysicsBoneManager.physicsBoneManagerInstance.LateUpdate();
            ProfilerApi.EndSample(EProfileFunc.LateUpdate_System_ClientPhysicsBoneSystem);
        }

        private void OnApplicationQuit()
        {
            // 撤销绑定场景加载+卸载事件
            SceneManager.sceneLoaded -= OnSceneLoaded;
            SceneManager.sceneUnloaded -= OnSceneUnloaded;

            PlayerLogicStateMachine.Instance.ClearData();
            PlayerLogicJobModel.ClearData();

#if UNITY_EDITOR
            if (Mc.Voice != null)
            {
                Mc.Voice.OnApplicationQuit();
            }
#endif
            Mc.LoginStep.StepLog("[SocClientApplicationLoop] OnApplicationQuit");

            if (LogCollector.Instance != null)
                LogCollector.Instance.Flush();

            LogHelper.Flush();
            LogoutWhenApplicationQuit();
            ClientLogService?.Dispose();
            LogCollector.Instance?.Complete();

            MicroServiceClient.Instance?.Dispose();
            Mc.Chat?.ExitClear(false);
            Mc.Ui.ClearAllWindow();
            Mc.LocalService.StopService();
            Mc.Effect?.DisposeJob();
            if (null != Mc.UiModel) Mc.UiModel.DisposeAllRT();
            SocShaderVariantManager.SaveAndUploadCompiledVariants();
            PandoraWrapper.Instance.Destroy();
            PhysicsBoneManager.physicsBoneManagerInstance.OnDispose();
#if !PUBLISH
            AudioEventUsageTracker.SaveAndUploadUsedEvents();
#endif
        }

        private void LogoutWhenApplicationQuit()
        {
            //删除这里的logout调用。这里调用logout的话，不会触发房间服的重连
        }
        
        protected override void OnLateUpdate()
        {
            base.OnLateUpdate();
            LateUpdateAction?.Invoke();
        }
    }
}