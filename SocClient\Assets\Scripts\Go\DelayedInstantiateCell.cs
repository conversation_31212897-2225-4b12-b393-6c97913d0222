using System.Collections.Generic;
using Unity.Collections;
using UnityEngine;

public class DelayedInstantiateCell
{
    public int Idx = 0;
    public bool IsInit = false;
    public Vector2 Pos = Vector2.zero;
    
    //key是Entity id, value是按照EntityTypeId对应的优先级
    public int EntityCount;
    public int EmbeddedEntityCount;
    public int InstantiatedEntityCount;
    public NativeParallelHashMap<long, int> EntityDic;
    public NativeParallelHashMap<long, int> EmbeddedEntityDic;
    public NativeParallelHashSet<long> InstantiatedEntity;
    
    public bool IsEmpty() { return EntityCount <= 0 && EmbeddedEntityCount <= 0; }

    public void Init(int InIdx, Vector2 InPos)
    {
        Idx = InIdx;
        Pos = InPos;
        IsInit = true;
        EntityCount = 0;
        EmbeddedEntityCount = 0;
        InstantiatedEntityCount = 0;
        
        EntityDic =
            new NativeParallelHashMap<long, int>(DelayedInstConf.MaxEntityDicCount, Allocator.Persistent);
        EmbeddedEntityDic = new NativeParallelHashMap<long, int>(DelayedInstConf.MaxEmbeddedEntityDicCount
            , Allocator.Persistent);
        InstantiatedEntity =
            new NativeParallelHashSet<long>(DelayedInstConf.MaxInstantiatedEntityCount
                , Allocator.Persistent);
    }

    public void FillEntityInQueue(PriorityQueue<long> InQueue)
    {
        if (EntityCount <= 0)
            return;
        
        foreach (var pair in EntityDic)
            InQueue.Enqueue(pair.Key, pair.Value);
    }
    
    public void FillEmbeddedInQueue(PriorityQueue<long> InQueue)
    {
        if (EmbeddedEntityCount <= 0)
            return;
        
        foreach (var pair in EmbeddedEntityDic)
            InQueue.Enqueue(pair.Key, pair.Value);
    }

    public void FillAllEntity(ref NativeParallelHashSet<long> InList)
    {
        if (EntityCount > 0)
        {
            foreach(var pair in EntityDic)
                InList.Add(pair.Key);
        }

        if (EmbeddedEntityCount > 0)
        {
            foreach(var pair in EmbeddedEntityDic)
                InList.Add(pair.Key);
        }

        if (InstantiatedEntityCount > 0)
        {
            foreach(var entity in InstantiatedEntity)
                InList.Add(entity);
        }
    }
    
    public void Clear()
    {
        Idx = 0;
        IsInit = false;
        Pos = Vector2.zero;
        EntityCount = 0;
        EmbeddedEntityCount = 0;
        InstantiatedEntityCount = 0;
        
        if(EntityDic.IsCreated)
            EntityDic.Dispose();
        
        if(EmbeddedEntityDic.IsCreated)
            EmbeddedEntityDic.Dispose();
        
        if(InstantiatedEntity.IsCreated)
            InstantiatedEntity.Dispose();
    }

    public void Add(long InId, int InTypeId)
    {
        if (EntityDic.TryAdd(InId, InTypeId))
            ++EntityCount;
    }
    
    public void AddEmbedded(long InId, int InTypeId)
    {
        if (EmbeddedEntityDic.TryAdd(InId, InTypeId))
            ++EmbeddedEntityCount;
    }

    public void InstantiateEntity(long InId)
    {
        if (EntityCount > 0 && EntityDic.Remove(InId))
            --EntityCount;
        else if (EmbeddedEntityCount > 0 && EmbeddedEntityDic.Remove(InId))
            --EmbeddedEntityCount;
        
        if (InstantiatedEntity.Add(InId))
            ++InstantiatedEntityCount;
    }
    
    public bool TryRemoveEntity(long InId, out int OutPriority)
    {
        if (EntityDic.TryGetValue(InId, out OutPriority))
        {
            EntityDic.Remove(InId);
            --EntityCount;
            return true;
        }
        
        OutPriority = 0;
        return false;
    }
    
    public bool Remove(long InId)
    {
        bool hasDelayed = false;
        // 是否在延迟实例化队列中
        if (EntityCount > 0 && EntityDic.Remove(InId))
        {
            --EntityCount;
            hasDelayed = true;
        }
        else if (EmbeddedEntityCount > 0 && EmbeddedEntityDic.Remove(InId))
        {
            --EmbeddedEntityCount;
            hasDelayed = true;
        }
        else if (InstantiatedEntityCount > 0 && InstantiatedEntity.Remove(InId))
        {
            --InstantiatedEntityCount;
            hasDelayed = true;
        }

        return hasDelayed;
    }
}