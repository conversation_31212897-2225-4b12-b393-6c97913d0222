using System.Collections.Generic;
using Unity.Collections;
using UnityEngine;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Unity.Utility;
using WizardGames.Soc.SocAI.Go;
using WizardGames.Soc.SocAI.Sense;
using WizardGames.Soc.SocSimulator.CommonUnity.Runtime.Utility;

namespace WizardGames.Soc.SocSimulator.AI.Perception
{
    public class VisionRaycastRequestMulti : IPhysicsMultipleRayCastWithJob
    {
        public IEntity targetEntity;
        public Vector3 eyePos;
        public List<Vector3> targetPoints = new List<Vector3>();
        public bool[] results; // 存储每个射线检测的结果
        public int raycastStartIndex { get; set; }
        public int raycastCount { get; set; }

        public void CollectMultipleCastCommandData(NativeList<CastPhysicsCommandData> castPhysicsCommandDataList)
        {
            raycastStartIndex = castPhysicsCommandDataList.Length;
            raycastCount = targetPoints.Count;
            
            // 为每个目标点创建射线检测命令
            for (int i = 0; i < targetPoints.Count; i++)
            {
                var dirVector = targetPoints[i] - eyePos;
                
                CastPhysicsCommandData commandData = new CastPhysicsCommandData()
                {
                    position = eyePos,
                    direction = dirVector.normalized,
                    distance = dirVector.magnitude,
                    layerMask = MonsterGo.SightMask,
                    hitTriggers = QueryTriggerInteraction.Ignore,
                    shapeType = PhysicsShapeType.Ray
                };
                
                castPhysicsCommandDataList.Add(commandData);
            }
        }
        
        public void MultipleCastJobFinished(NativeArray<RaycastHit> raycastResults, NativeArray<RaycastHit> sortedResults)
        {
            // 确保结果数组大小正确
            if (results == null || results.Length != targetPoints.Count)
            {
                results = new bool[targetPoints.Count];
            }
            
            // 处理每个射线检测结果
            for (int i = 0; i < targetPoints.Count; i++)
            {
                int resultIndex = raycastStartIndex + i;
                if (resultIndex < sortedResults.Length)
                {
                    var hit = sortedResults[resultIndex];
                    results[i] = hit.collider == null || UnityUtility.ColliderIgnoreHurt(hit.collider);
                }
                else
                {
                    results[i] = true; // 默认可见
                }
            }
        }
    }
}
