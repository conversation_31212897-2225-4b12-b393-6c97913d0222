using System.Collections.Generic;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Data.task;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.ClassImpl.Task;

namespace WizardGames.Soc.Common.CustomType
{
    [HotfixClass]
    public static partial class SubTaskNodeHotfix
    {
        /// <summary>
        /// 返回监听事件，是否监听组队，以及是否增量
        /// </summary>
        [Hotfix]
        public static IEnumerable<(int, bool, bool)> GetInterestEventIdVirtual(this SubTaskNode self, PlayerTaskComponent comp)
        {
            var taskConfig = McCommon.Tables.TbQuestPhase.GetOrDefault(self.BizId);
            if (taskConfig == null)
            {
                yield break;
            }

            var conditionConfig = McCommon.Tables.TbQuestCondition.GetOrDefault(taskConfig.TaskPhaseEndCondition);
            if (conditionConfig == null)
            {
                yield break;
            }

            var taskCondition = ConditionUtil.GetCondition(conditionConfig.TargetData);
            if (taskCondition == null)
            {
                yield break;
            }

            foreach (var eventId in taskCondition.GetInterestEventId(comp.Player, taskConfig.TaskPhaseEndCondition))
            {
                yield return (eventId, taskConfig.Isteam, conditionConfig.CounterChange == CounterChange.Increment);
            }
        }

        public static void InitRefreshProcess(this SubTaskNode self, PlayerTaskComponent comp, long taskId, int taskType)
        {
            if (self.IsComplete) return;

            var taskConfig = McCommon.Tables.TbQuestPhase.GetOrDefault(self.BizId);
            if (taskConfig == null)
            {
                self.Logger.Error($"[RefreshTaskNodeCount] taskConfig null {self.BizId}");
                return;
            }
            
            var conditionConfig = McCommon.Tables.TbQuestCondition.GetOrDefault(taskConfig.TaskPhaseEndCondition);
            if (conditionConfig == null)
            {
                self.Logger.Error($"[RefreshTaskNodeCount] conditionConfig null {self.BizId} {taskConfig.TaskPhaseEndCondition}");
                return;
            }

            if (conditionConfig.CounterChange == CounterChange.Increment)
            {
                comp.SendTLog(taskId, self.BizId, 0, 0, 0, 0, TimeStampUtil.GetNowTimeStampSec(), 0, 0, taskType);
                return;
            }

            var cnt = ConditionUtil.GetCurProcess(comp.Player, taskConfig.TaskPhaseEndCondition, taskConfig.EndConditionParameter);
            self.Count = cnt;
            if (cnt > 0)
            {
                comp.SendTLog(taskId, self.BizId, cnt, 0, cnt, 0, TimeStampUtil.GetNowTimeStampSec(), 0, 0, taskType);
            }

            self.IsComplete = ConditionUtil.Compare(conditionConfig.CompareType, cnt, (int)taskConfig.EndConditionParameter[0]);
        }

        public static bool IsCompleted(this SubTaskNode self)
        {
            return self.IsComplete;
        }

        [Hotfix]
        public static long[] GetEndParameters(this SubTaskNode self)
        {
            var taskConfig = McCommon.Tables.TbQuestPhase.GetOrDefault(self.BizId);
            if (taskConfig == null)
            {
                return null;
            }

            return taskConfig.EndConditionParameter;
        }

        [Hotfix]
        public static bool TryUpdateProcessVirtual(this SubTaskNode self, ConditionInnerInfo info)
        {
            var taskConfig = McCommon.Tables.TbQuestPhase.GetOrDefault(self.BizId);
            if (taskConfig == null) return false;

            var conditionConfig = McCommon.Tables.TbQuestCondition.GetOrDefault(taskConfig.TaskPhaseEndCondition);
            if (conditionConfig == null) return false;

            var condition = ConditionUtil.GetCondition(conditionConfig.TargetData);
            if (condition == null) return false;

            if (!condition.NeedUpdate(self.TaskComp.Player, ref info, self.BizId, taskConfig.TaskPhaseEndCondition, taskConfig.EndConditionParameter))
            {
                return false;
            }

            switch (conditionConfig.CounterChange)
            {
                case CounterChange.GetValue:
                    {
                        self.Count = condition.GetValue(self.TaskComp.Player, taskConfig.EndConditionParameter);
                        break;
                    }
                case CounterChange.Increment:
                    {
                        if (info.Count <= 0) return false;
                        self.Count += info.Count;
                        break;
                    }
            }

            self.IsComplete = ConditionUtil.Compare(conditionConfig.CompareType, self.Count, (int)taskConfig.EndConditionParameter[0]);

            if (self.IsComplete)
            {
                var comp = self.TaskComp;
                foreach (var (eventId, isTeam, _) in self.GetInterestEventId(comp))
                {
                    self.TaskContainer.RemoveTaskEvent(eventId, self.Id, isTeam);
                }
            }
            return true;
        }
    }
}
