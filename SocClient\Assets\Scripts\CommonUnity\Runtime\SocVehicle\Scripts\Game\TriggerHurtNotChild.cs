﻿using System;
using UnityEngine;
using System.Collections.Generic;
using Soc.Vehicle;
using Soc.Vehicle.Util.Extension;
using SocVehicle.Scripts.Entity.Vehicle;
using UnityEngine.Serialization;
using WizardGames.Soc.Common.Combat;
#if SOC_SIMULATOR
using WizardGames.Soc.Common.Config;
#endif
using WizardGames.Soc.Common.Data.Attr;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Unity.Audio;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.DebugLog;
using WizardGames.Soc.Common.Unity.Extension;
using WizardGames.Soc.Common.Unity.Go;
using WizardGames.Soc.Common.Unity.Manager;
using DamageType = WizardGames.Soc.Common.SimpleCustom.DamageType;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.Unity.Monster;
using WizardGames.Soc.Common.Utility;

public class TriggerHurtNotChild : TriggerBase //, IServerComponent, IHurtTrigger
{
    enum ActiveSpeedType
    {
        /// 矢量，带方向
        Vector,
        /// 标量，不带方向
        Scalar
    }
    
    [SerializeField]private ActiveSpeedType activeSpeedType = ActiveSpeedType.Vector;
    
    /// <summary>
    /// 值为0则表示速度不影响组件激活
    /// 如果值大于0表示速度需要大于ActiveSpeed组件才激活
    /// 如果值小于0表示速度需要小于ActiveSpeed组件才激活
    /// </summary>
    public float HurtTriggerMinSpeed = 0;

    public bool IgnoreSleep = false;

    public float DamagePerSecond = 1.0f; // Base damage to deal per second. May be further modified by a multiplier
    public float DamageTickRate = 4.0f; // How many times to damage, per second

    public float
        DamageDelay =
            0.0f; // Delay on damage starting when an object enters the trigger. Can work around issues where something's in a trigger for 1 or 2 frames and occasionally takes damage due to it

    public DamageType damageType = DamageType.Generic;

    public bool ignoreNPC = true;
    public float npcMultiplier = 1f;

    public bool ignoreCorpse = true;

    public float resourceMultiplier = 1f;

    public bool triggerHitImpacts = true;
    public bool RequireUpAxis = false;

    public BaseRustEntity SourceEntity;

    // cyj默认使用的sourceEntity为Rust带的，适配一下Soc自己的Entiyt
    public IEntity SocSourceEntity;

    // If the source entity is an IHurtTriggerUser, use its damage multiplier method to modify the damage amount
    public bool UseSourceEntityDamageMultiplier = true;

    // Ignore entities that are mounted to vehicles
    public bool ignoreAllVehicleMounted = false;

    // Optional delay frmo activation until the trigger starts functioning
    public float activationDelay;

    public long SourceEntityId;

    public interface IHurtTriggerUser
    {
        // Who should be considered responsible for any damage caused by this trigger
        BasePlayer GetPlayerDamageInitiator();

        // Custom damage multiplier. 1.0 is no multiplier
        float GetPlayerDamageMultiplier();

        // Callback in case the trigger user wants to do something when the trigger causes damage
        void OnHurtTriggerOccupant(BaseEntityGo hurtEntity, DamageType damageType, float damageTotal);
    }

    public interface IBeHurtTrigger
    {
        float GetDamageMultiplier();
    }

    // #if SERVER
    private Dictionary<BaseEntityGo, float> entryTimes;
    private TimeSince timeSinceAcivation = new TimeSince();

    public void Init(IEntity entity, BaseEntityGo entityGo)
    {
        SocSourceEntity = entity;
        SourceEntityId = entity.EntityId;
        _entityGo = entityGo;
    }

    private BaseEntityGo _entityGo;

    public BaseEntityGo EntityGo
    {
        get
        {
            if (_entityGo == null)
            {
                var cc = gameObject.GetColliderConfig();
                if (cc == null)
                {
                    return null;
                }

                _entityGo = (BaseEntityGo)cc.OwnerEntityGo;
            }

            return _entityGo;
        }
    }

    public void CheckActive(bool isSleep, float speed)
    {
        if (!gameObject)
        {
            return;
        }

        if (isSleep)
        {
            if (gameObject.activeSelf)
            {
                gameObject.SetActive(false);
            }

            return;
        }

        if (HurtTriggerMinSpeed == 0)
        {
            return;
        }

        if (activeSpeedType == ActiveSpeedType.Vector)
        {
            if (HurtTriggerMinSpeed > 0 && speed >= HurtTriggerMinSpeed)
            {
                if (!gameObject.activeSelf)
                    gameObject.SetActive(true);
            }
            else if (HurtTriggerMinSpeed < 0 && speed <= HurtTriggerMinSpeed)
            {
                if (!gameObject.activeSelf)
                    gameObject.SetActive(true);
            }
            else
            {
                if (gameObject.activeSelf)
                    gameObject.SetActive(false);
            }
        }
        else if (activeSpeedType == ActiveSpeedType.Scalar)
        {
            var active = Mathf.Abs(speed) >= Mathf.Abs(HurtTriggerMinSpeed);
            if (active != gameObject.activeSelf)
                gameObject.SetActive(active);
        }
    }

    internal override GameObject InterestedInObject(Collider obj)
    {
        var go = base.InterestedInObject(obj);
        if (go == null)
        {
            return null;
        }
        var ent = obj.GetBaseEntityGo();
        if (ent == null || ent.Entity == null)
        {
            return null;
        }

        if (go == null && ent.Entity.EntityType != EntityTypeId.HorseEntity)
        {

            if (ent.Entity.EntityType != EntityTypeId.HorseEntity)
            {
                return null;
            }

            /* 马匹虽然是载具，但可以收到伤害，但不能是触发器*/
            if (obj.isTrigger)
            {
                /* 触发器不应该激活伤害*/
                return null;
            }
        }

        if (ignoreCorpse && ent.Entity.EntityType == EntityTypeId.CorpseEntity)
        {
            return null;
        }

        if (ignoreNPC && ent.Entity.EntityType == EntityTypeId.MonsterEntity)
        {
            return null;
        }

        return ent.MainGo;
    }

    private long _timerId;

    internal override void OnObjects()
    {
        //InvokeRepeating( OnTick, 0f, 1.0f / DamageTickRate );
        SocDebug.VehicleLog($"{gameObject.name} 添加计时器 {_timerId}");
        OnTick();
        var tick = (int)(1.0f / DamageTickRate * 1000);

        CancelInvoke();

        if (_timerId == 0)
        {
            _timerId = McCommonUnity.TimerWheel.AddTimerRepeat(tick, tick,
                (id, data, delete) => { OnTick(); }, "TriggerHurtNotChild.OnObjects");
        }
    }

    internal override void OnEntityEnter(BaseEntityGo ent, Collider col)
    {
        base.OnEntityEnter(ent, col);

        if (ent != null && DamageDelay > 0)
        {
            if (entryTimes == null)
            {
                entryTimes = new Dictionary<BaseEntityGo, float>();
            }

            entryTimes.TryAdd(ent, Time.time);
        }
    }

    internal override void OnEntityLeave(BaseEntityGo ent)
    {
        if (ent != null && entryTimes != null)
        {
            entryTimes.Remove(ent);
        }

        base.OnEntityLeave(ent);
    }

    internal override void OnEmpty()
    {
        //CancelInvoke( OnTick );
        CancelInvoke();
    }

    protected void OnEnable()
    {
        timeSinceAcivation = 0f;
    }

    protected override void OnDisable()
    {
        //CancelInvoke( OnTick );
        //SocDebug.VehicleLog($"{gameObject.name} OnDisable 计时器 {_timerId}");
        CancelInvoke();
        base.OnDisable();
    }

    void CancelInvoke()
    {
        if (_timerId == 0)
        {
            return;
        }

        //SocDebug.VehicleLog($"{gameObject.name} 取消计时器 {_timerId}");
        // 取消的时候结算一遍，否则如果Disable了伤害就永远不会结算了
        OnTick();
        McCommonUnity.TimerWheel.CancelTimer(_timerId);
        _timerId = 0;
    }

    // Interested check for things that can potentially change WHILE the thing is in the trigger
    bool IsInterested(BaseEntityGo ent)
    {
        if (timeSinceAcivation < activationDelay)
        {
            return false;
        }

        if (ent is BasePlayerGo ply)
        {
            if (ply.PlayerEntity.IsOnMount)
            {
                // Always ignore players who are mounted to THIS vehicle
                if (ply.PlayerEntity.MountableId == GetEntityId())
                {
                    return false;
                }

                // Optionally ignore players who are mounted to ANY vehicle
                // (helps this trigger not damage players who are inside vehicle cockpits etc)
                if (ignoreAllVehicleMounted && ply.PlayerEntity.MountableId > 0)
                {
                    return false;
                }
            }
        }
        else if (ent is BaseMonsterGo monsterGo)
        {
            if (monsterGo.MonsterEntity.MountableId > 0 && monsterGo.MonsterEntity.MountableId == GetEntityId())
            {
                return false;
            }
            else if (SourceEntity is CH47HelicopterAIController heli)
            {
                return heli.CanHurtMonster; //对于ai控制的ch47运输机，为了避免起飞时刮死平台上的怪物，增加了这个字段
            }
        }

        // Prevents things like players on a vehicle flatbed getting hurt by a hurt trigger on a vehicle
        // (which can happen even when the triggers don't overlap due to timing between server and client)
        /*if (SourceEntity != null && ply.HasEntityInParents(SourceEntity))
        {
            return false;
        }*/
        return true;
    }


    [System.NonSerialized] private Dictionary<long, Collider> copiedContents = new ();

    private void HandleDamage(long entityId, Collider col, IHurtTriggerUser hurtTriggerUser)
    {
        if (transform == null)
        {
            return;
        }
        
        var entityGo = ColliderExtension.GetEntityGo(entityId) as BaseEntityGo;
        if (entityGo == null || col == null || entityGo.Entity == null || entityGo.MainGo == null)
        {
            RemoveContentedEntity(entityId);
            return;
        }
        
        // 怪物间伤害，如果配置了无法造成伤害，则跳过
        if (SocSourceEntity is MonsterEntity srcEntity && entityGo.Entity is MonsterEntity dstEntity)
        {
            if (MonsterUtility.MonsterCanGenerateHitRequest(srcEntity, dstEntity) == false)
            {
                return;
            }
        }

        if ( /*!ent.IsValid() || */!IsInterested(entityGo))
        {
            return;
        }

        /* 只有可攻击物才能受到伤害*/
        if (!entityGo.Entity.TryGetIHitable(out IHitableEntity hitableEntity))
        {
            return;
        }

        if (hitableEntity.Hp <= 0)
        {
            //角色死亡后，碰撞会被隐藏，然后不会触发triggerexit移除
            RemoveContentedEntity(entityGo.EntityId);
            return;
        }

#if SOC_SIMULATOR
        if (DamageDelay > 0 && entryTimes != null && entryTimes.TryGetValue(entityGo, out float addTime))
        {
            if (addTime + DamageDelay > Time.time)
            {
                return;
            }
        }

        if (RequireUpAxis && Vector3.Dot(entityGo.MainTransform.up, transform.up) < 0f)
            return;

        float damageTotal = DamagePerSecond * 1.0f / DamageTickRate;
        if (UseSourceEntityDamageMultiplier && hurtTriggerUser != null)
        {
            damageTotal *= hurtTriggerUser.GetPlayerDamageMultiplier();
        }

        if (entityGo.Entity is MonsterEntity or HorseEntity) // 对于怪物（npc）的伤害加成
            damageTotal *= npcMultiplier;

        if (entityGo.Entity is TreeEntity) // 对于树（资源）的伤害加成
            damageTotal *= resourceMultiplier;

        var hitPos = entityGo.MainTransform.position + (Vector3.up * 1f);

        // 获取受击方的特殊倍率修改
        if (col.GetBaseEntityGo() is IBeHurtTrigger hurt)
            damageTotal *= hurt.GetDamageMultiplier();

        /* tip 对碰到的实体发起伤害请求*/
        logger.InfoFormat("载具{0}对目标：{1} 造成了类型为{2}，数值为{3}的伤害", GetEntityId(), entityGo.MainGo.name, damageType, damageTotal);
        var hitRequest = HitRequest.Generate(GetEntityId(), entityGo.Entity);
        hitRequest.HitPart = HitPart.Everything;
        hitRequest.Point = hitPos.ToVector3();
        hitRequest.HitObjectPoint = entityGo.MainTransform.InverseTransformPoint(hitPos).ToVector3();
        hitRequest.Normal = System.Numerics.Vector3.UnitY;
        hitRequest.AddDamage(damageType, damageTotal);
        hitRequest.MaterialType = McCommonUnity.Material.GetMaterial(col, hitPos);
        hitRequest.WaysToDieID = (int)WaysToDie.IndirectDamageDead;
        if (SocSourceEntity != null)
        {
            BaseMountableGo mountableGo = _entityGo as BaseMountableGo;
#if SOC_SIMULATOR
            var tableId = mountableGo?.VehicleInfo?.Id ?? 0;
            hitRequest.AddSource(SourceEntityId, SocSourceEntity.EntityType, tableId);
            if (mountableGo != null)
            {
                var driverId = mountableGo.GetDriverId();
                if (driverId > 0)
                {
                    if (WizardGames.Soc.SocSimulator.Manager.Mc.Entity.TryGetEntity(driverId,
                            out IEntity driver))
                    {
                        if (driver is PlayerEntity player)
                        {
                            hitRequest.AddSource(driverId,player.EntityType, 0);
                        }
                
                        if (driver is MonsterEntity monster)
                        {
                            hitRequest.AddSource(driverId,monster.EntityType, monster.TemplateId);
                        }
                    }
                }
            }
#endif
            hitRequest.AddSource(SourceEntityId, SocSourceEntity.EntityType, tableId);
            hitRequest.SourceId = SourceEntityId;//判断死亡方式用到
        }

        McCommon.SystemRequestMgr.AddRequest(ESystemRequest.HitRequestSet, hitRequest);

        if (hurtTriggerUser != null)
        {
            hurtTriggerUser.OnHurtTriggerOccupant(entityGo, damageType, damageTotal);
        }
#elif SOC_CLIENT
        if (hurtTriggerUser != null)
        {
            hurtTriggerUser.OnHurtTriggerOccupant(entityGo, default, default); // 后两个参数实际没用上
        }
#endif
    }
    
    void OnTick()
    {
        if (entityContents.IsNullOrEmpty())
            return;

        /*var copiedContents = Facepunch.Pool.GetList<BaseEntity>();
        copiedContents.AddRange(entityContents);*/
        // var copiedContents = entityContents;
        copiedContents.Clear();
        foreach (var pair in entityContents)
        {
            copiedContents.Add(pair.Key, pair.Value);
        }

        IHurtTriggerUser hurtTriggerUser = GetHurtTriggerUser();
        foreach (var (entityId, col) in copiedContents)
        {
            try
            {
                HandleDamage(entityId, col, hurtTriggerUser);
            }
            catch (Exception e)
            {
                logger.ErrorFormat("{0}\n{1}", e.Message, e.StackTrace);
            }
        }

        // Facepunch.Pool.FreeList(ref copiedContents);

        // We had some occasional trouble with trigger exit not firing when the trigger was on a car.
        // To make sure it's totally foolproof, check each tick for invalid entities that have moved outside the trigger.
        // Do it last though, as otherwise it can immediately remove things that just entered, preventing them taking any hurt damage at all.
        /*
         * 通过collider添加entity但是该方法通过判断maingo的位置移除entity的做法有问题
         * 在熊的测试中会导致熊的collider还在碰撞范围但是熊的maingo已经在到碰撞范围外导致移除entity，所以不用该方法
         * RemoveInvalidEntities(); 
         */
    }

    private long GetEntityId()
    {
        if (SourceEntity != null && SourceEntity.Entity != null)
        {
            return SourceEntity.Entity.EntityId;
        }

        return SourceEntityId;
    }

    private IHurtTriggerUser GetHurtTriggerUser()
    {
        if (SourceEntity != null)
        {
            return SourceEntity as IHurtTriggerUser;
        }

        if (EntityGo != null)
        {
            return EntityGo as IHurtTriggerUser;
        }

        return null;
    }

    // #endif
}