using Cysharp.Text;
using FairyGUI;
using System;
using UnityEngine;
using WizardGames.Soc.SocClient.Manager;

namespace WizardGames.Soc.SocClient.Ui
{
    public partial class UiHudElemPlayerLifeBar : UiHudElemBar
    {
        private GTextField maxHpText;


        /// <summary>
        /// 上次缓存玩家虚血
        /// </summary>
        private int lastPlayerVirtualHp = 0;
        /// <summary>
        /// 上次缓存玩家实际血量
        /// </summary>
        private int lastPlayerHp = 0;
        /// <summary>
        /// 实血红色闪动动效
        /// </summary>
        private Transition redFlashing;
        /// <summary>
        /// 实血黄色闪动动效
        /// </summary>
        private Transition orangeFlashing;
        /// <summary>
        /// 实血绿色闪动动效
        /// </summary>
        private Transition greenFlashing;
        /// <summary>
        /// 扣血残留
        /// </summary>
        private GImage bgResidual;
        /// <summary>
        /// 残留血量消失动画
        /// </summary>
        private Transition residualTransition;
        private bool isResidualPlaying = false;
        private long timerId = 0;


        public override void Init(UiPlayerStatusData uiData, GComponent root, int maxValue = 100)
        {
            base.Init(uiData, root, maxValue);

            mainBar = root.GetChild("hp_bar").asProgress;
            barImg = mainBar.GetChild("bar").asImage;
            baseBarImg = mainBar.GetChild("bgBar").asImage;
            barTextField = mainBar.GetChild("title").asTextField;
            maxHpText = mainBar.GetChild("maxHp").asTextField;
            barLessTrans = mainBar.GetTransition("RedFlashing");
            redFlashing = mainBar.GetTransition("RedFlashing");
            orangeFlashing = mainBar.GetTransition("OrangeFlashing");
            greenFlashing = mainBar.GetTransition("GreenFlashing");
            bgResidual = mainBar.GetChild("bgResidual")?.asImage;
            residualTransition = mainBar.GetTransition("residual");
            ctrIsBuildMode = mainBar.GetController("IsBuildMode");

            if (mainBar != null)
            {
                maxHpText.text = ZString.Format("/{0}", uiData.PlayerHpMax);
                SetHPBarValue(uiData.PlayerHp, uiData.PlayerHpMax);
                SetBaseBarValue(uiData.PlayerVirtualHp);
            }

            lastPlayerHp = uiData.PlayerHp;
            lastPlayerVirtualHp = uiData.PlayerVirtualHp;

            if (baseBarImg != null)
            {
                //SetBaseBarColor(uiData.Config.GreenLifeBarVirtualColor);
            }

            iconLoader = mainBar.GetChild("icon").asLoader;

            SetBarColor();

            uiData.InjuredDownAction += this.OnInjuredDown;
        }

        private void OnInjuredDown()
        {
            //倒地时，先把血条改成红色
            // TODO：需要优化？现在特效加了个warn条，会挡住原本的血条
            SetBarColor();
            if (uiData.MyPlayer.IsDead)
            {
                StopTransition(orangeFlashing);
                StopTransition(greenFlashing);
                StopTransition(redFlashing);
                return;
            }
            PlayTransition(redFlashing);
            StopTransition(redFlashing);
        }

        public override void OnDestroy()
        {
            uiData.InjuredDownAction -= this.OnInjuredDown;
        }

        public override void OnEnable()
        {
            base.OnEnable();
            CheckTransition();
        }

        public override void Refresh()
        {
            int playerHpMax = uiData.PlayerHpMax;
            if ((int)mainBar.max != playerHpMax)
            {
                maxHpText.text = ZString.Format("/{0}", playerHpMax);
            }

            SetHPBarValue(uiData.PlayerHp, playerHpMax);
            SetBarColor();
            if (lastPlayerVirtualHp != uiData.PlayerVirtualHp)
            {
                SetBaseBarValue(uiData.PlayerVirtualHp);
                lastPlayerVirtualHp = uiData.PlayerVirtualHp;
            }
            if (uiData.PlayerVirtualHp - uiData.PlayerHp > 0 && !uiData.IsInjuredDown) //血条增加
            {
                CheckTransition();
            }
            else if ((uiData.PlayerVirtualHp - uiData.PlayerHp < 0 || lastPlayerHp > uiData.PlayerHp) && !uiData.IsInjuredDown) //血条减少
            {
                if (!isResidualPlaying)
                {
                    if (this.bgResidual != null) this.bgResidual.fillAmount = lastPlayerHp / (float)playerHpMax;
                    residualTransition.SetValue("ScaleEnd", uiData.PlayerHp / (float)playerHpMax, 1f);
                    residualTransition.Stop();
                    residualTransition.Play();

                    isResidualPlaying = true;
                    this.timerId = Mc.TimerWheel.AddTimerOnce(500, TimerCallback);
                }
                else
                {
                    residualTransition.Stop();
                    residualTransition.SetValue("ScaleEnd", uiData.PlayerHp / (float)playerHpMax, 1f);
                    residualTransition.Play();

                    Mc.TimerWheel.CancelTimer(this.timerId);
                    isResidualPlaying = true;
                    this.timerId = Mc.TimerWheel.AddTimerOnce(500, TimerCallback);
                }
                SetHPBarValue(uiData.PlayerVirtualHp, playerHpMax);
                SetBarColor();
            }
            else
            {
                StopTransition(redFlashing);
                StopTransition(orangeFlashing);
                StopTransition(greenFlashing);
            }
            // 更新上次血量
            if (lastPlayerHp != uiData.PlayerHp)
            {
                lastPlayerHp = uiData.PlayerHp;
            }
        }

        private void CheckTransition()
        {
            var curValue = mainBar.value / mainBar.max;

            if (curValue < redFlashingValue)
            {
                StopTransition(orangeFlashing);
                StopTransition(greenFlashing);
                PlayTransition(redFlashing);
            }
            else if (curValue >= redFlashingValue && curValue < orangeFlashingValue)
            {
                StopTransition(redFlashing);
                StopTransition(greenFlashing);
                PlayTransition(orangeFlashing);
            }
            else
            {
                StopTransition(redFlashing);
                StopTransition(orangeFlashing);
                PlayTransition(greenFlashing);
            }
        }

        private void TimerCallback(long id, object data, bool del)
        {
            isResidualPlaying = false;
        }
        public void StopTransition(Transition transition)
        {
            if (null == transition)
            {
                return;
            }
            if (transition.playing)
            {
                transition.Stop();
            }
        }
        public void PlayTransition(Transition transition)
        {
            if (null == transition)
            {
                return;
            }
            if (!transition.playing)
            {
                transition.Play();
            }

        }

        public override void SetBarLessTrans()
        {
            if (mainBar.value / mainBar.max < redFlashingValue)
            {
                if (barLessTrans != null && !barLessTrans.playing)
                {
                    barLessTrans.Play();
                }
            }
            else
            {
                if (barLessTrans != null && barLessTrans.playing)
                {
                    barLessTrans.Stop();
                }
            }
        }

        /// <summary>
        /// 设置生命条的颜色，增加血量百分比判断，和动效改颜色逻辑一致
        /// </summary>
        private void SetBarColor()
        {
            var curValue = mainBar.value / mainBar.max;
            Color barColor = uiData.IsInjuredDown || curValue < redFlashingValue ? uiData.Config.RedLifeBarColor : uiData.Config.GreenLifeBarColor;
            if (uiData.MyPlayer.IsDead)
            {
                barColor = Color.clear;
            }
            if (baseBarImg != null)
            {
                baseBarImg.visible = !uiData.IsInjuredDown;
            }
            CheckTransition();
            //SetBarColor(barColor);
            if (mainBar != null)
            {
                Color baseBarColor = curValue < redFlashingValue ? uiData.Config.RedLifeBarColor : uiData.Config.GreenLifeBarColor;
                SetBaseBarColor(baseBarColor);
            }
        }
    }
}
