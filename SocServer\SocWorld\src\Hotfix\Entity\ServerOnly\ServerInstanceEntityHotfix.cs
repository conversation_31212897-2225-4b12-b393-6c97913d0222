using SimpleJSON;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Config;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.TimerWheel;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Event;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.Event;
using WizardGames.Soc.SocWorld.Framework;
using WizardGames.Soc.SocWorld.Framework.Event;
using WizardGames.Soc.SocWorld.Framework.Threads;
using WizardGames.Soc.SocWorld.Lobby;
using WizardGames.Soc.SocWorld.RuleGraph.Function;
using WizardGames.Soc.SocWorld.Share.Utility;
using WizardGames.Soc.SocWorld.WorldCommon;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.Common.Entity
{
    [HotfixClass]
    public static partial class ServerInstanceEntityHotfix
    {
        [Hotfix(NeedWrapper = true)]
        public static void InitListenAndTimer(this ServerInstanceEntity self)
        {
            self.ListenTargetEvent(EntityConstId.PROCESSS_ENTITY_ID, OnClientExtraPayloadHotfix);
            self.ListenTargetEvent(EntityConstId.PROCESSS_ENTITY_ID, OnReloadHotfix);
            self.SubscribePropertyChange(ServerInstanceEntity.PropertyIds.STORY_STAGE, ReputationUtil.OnStoryStageChangeHotfix);
            self.DailyDropLimitClearScheduleId = TimerWheelConst.INVALID_TIMER_ID;
            self.DelayExecute(() => self.TickDayAndNight(0));
            self.AddTimerRepeat(5000, 5000, TickDayAndNightHotfix);
            // 每 24 小时同步一次勋章风格分数
            var intervalMs = (uint)(McCommon.Tables.TBConsts.MedalSyncIntervalMinutes * 60 * 1000);
            self.AddTimerRepeat(intervalMs, intervalMs, SyncAllPlayersDeltaMedalScoresHotfix);
        }

        [Hotfix(EventCallback = true)]
        private static void OnClientExtraPayload(this ServerInstanceEntity self, RpcPacketExtraPayloadEvent e)
        {
            //self.Logger.Info($"OnClientExtraPayload {e.NetPeerId}");
            if (e.IsFromClient)
            {
                if (ServerConfig.Instance.DisableAntiCheatCheck == 0)
                {
                    if (e.Payload.Count < 1 || e.Payload.Count > 1024)
                    {
                        self.Logger.Warn($"AntiCheatCheck unexpected payload length {e.Payload.Count}");
                        return;
                    }
                    var source = e.Payload[^1];
                    if (source != 1 && source != 2)
                    {
                        self.Logger.Warn($"AntiCheatCheck unexpected source {source}");
                        return;
                    }

                    // 反作弊
                    var player = UserManagerEntity.Instance.GetPlayerEntityByNetPeerId(e.NetPeerId);
                    if (player == null)
                    {
                        self.Logger.Error($"OnClientExtraPayload but player not found, {e.NetPeerId}");
                        return;
                    }
                    player.lastReportAntiCheatTime = TimeStampUtil.GetRawTimestampMsec();
                    if (e.Payload.Count > 2)
                    {
                        // 记录tlog
                        var antiCheatPayload = TLogUtil.ByteArrayToHexString(e.Payload[..^1]);
                        // 里面有个0表示正常
                        var logString = $"{player.uniqueBattleId}|{antiCheatPayload}|0|{source}";
                        TLogUtil.PlayerLog(player, "SecAntiDataFlow", logString);
                    }
                    else
                    {
                        player.Logger.Info($"AntiCheatCheck source {source} report empty message");
                    }
                }
            }
        }

        [Hotfix(EventCallback = true)]
        public static void OnReload(this ServerInstanceEntity self, ReloadExcelEvent evt)
        {
            var isChinaVersion = McCommon.Tables.TbGlobalConfig.IsChinaVersion;
            if (self.IsChinaVersion != isChinaVersion)
            {
                self.Logger.Info($"SetIsServerChinaVersion {isChinaVersion}");
                self.IsChinaVersion = isChinaVersion;
                RpcEntity.Instance.RemoteCallSwitchChinaVersionForSimulator(ERpcTarget.Simulator, isChinaVersion);
            }
        }

        /// <summary>
        /// 逻辑开服
        /// 如果不调用，就从开服开始计算逻辑开始时间
        /// </summary>
        public static void LogicStart(this ServerInstanceEntity self)
        {
            self.Logger.Info($"LogicStart");

            ProcessEntity.Instance.SetLogicStart();
            ProcessEntity.Instance.SetGameState(ProcessEntity.GS_RUNNING);
            GlobalInfoSyncEntity.Instance.LogicStartTime = ProcessEntity.Instance.TimeSinceStartup;
            GlobalInfoSyncEntity.Instance.LogicStartTs = ProcessEntity.Instance.NowTs;

            self.AddTimerOnce((uint)RandomUtil.Instance.Next(1, 600000), DelayStartAutoSaveHotfix);
            // 同步给 simulator
            ProcessEntity.Instance.RemoteCallSyncWorldLogicStart(ERpcTarget.Simulator, ProcessEntity.Instance.TimeSinceStartup);

            //RpcEntity.Instance.StartDisableFreeRepairTimer();
            // todo 还要通知已经连接的客户端

            EntityStaticCallback<ServerControlEntity>.InvokeAllEventCallback(ServerControlEntity.Instance, new GameStartEvent());

            MainThread.SetGameLogicStarted();
            EntityBase.EntityTimerWheel.EnableSingleTimerExecuteTimeWarning(50);
        }

        [Hotfix(TimerCallback = true)]
        private static void DelayStartAutoSave(this ServerInstanceEntity self, long _)
        {
            EStartAutoSaveResult ret;
            if ((ret = ProcessEntity.Instance.StartAutoSave()) != EStartAutoSaveResult.Success)
            {
                self.Logger.Warn($"DelayStartAutoSave failed with code {ret}");
            }
        }

        public static void AddServerDropCount(this ServerInstanceEntity self, int dropLimitId)
        {
            self.Logger.Info($"[AddServerDropCount] {dropLimitId}");
            if (self.ServerDropLimit.TryGetValue(dropLimitId, out var count))
            {
                self.ServerDropLimit[dropLimitId] = count + 1;
            }
            else
            {
                self.ServerDropLimit[dropLimitId] = 1;
            }

            if (self.DailyDropLimitClearScheduleId == TimerWheelConst.INVALID_TIMER_ID)
            {
                var targetHour = McCommon.Tables.TbGlobalConfig.DailyRewardLimitResetTime;
                self.DailyDropLimitClearScheduleId = self.AddSchedule(targetHour, 0, true, ResetDailyDropCountHotfix);
            }
        }

        [Hotfix(ScheduleCallback = true)]
        public static void ResetDailyDropCount(this ServerInstanceEntity self, long scheduleId)
        {
            self.Logger.Info($"ResetDailyDropCount");
            self.ServerDropLimit.Clear();
            self.DailyDropLimitClearScheduleId = TimerWheelConst.INVALID_TIMER_ID;
        }

        public static int GetServerDropCount(this ServerInstanceEntity self, int dropLimitId)
        {
            if (self.ServerDropLimit.TryGetValue(dropLimitId, out var count))
            {
                return count;
            }
            return 0;
        }

        [Hotfix(TimerCallback = true)]
        public static void PushBattleEndTimeTips(this ServerInstanceEntity self, long timerId)
        {
            GlobalInfoSyncEntity.Instance.RemoteCallShowCountDown(ERpcTarget.AllClients, self.BattleEndTimeTipsDuration, 0,
                self.BattleEndTimeTipsTimeColor,
                self.BattleEndTimeTipsTitle,
                self.BattleEndTimeTipsTitleColor);
        }

        [Hotfix(TimerCallback = true)]
        private static void TickDayAndNight(this ServerInstanceEntity self, long _)
        {
            var inGameElapsedSeconds = GameTimeUtil.GetDaySecondsFromLogicMs(ProcessEntity.Instance.TimeSinceLogicStart, self.gameTimeParamHolder);
            var stage = GameTimeUtil.GetGameTimeStage(self.gameTimeParamHolder, inGameElapsedSeconds);
            var isDay = stage == GameTimeStage.Day;
            var isNight = stage == GameTimeStage.Night;
            if (self.IsDaytime != isDay)
            {
                if (!self.IsDaytime)
                {
                    self.IsDaytime = true;
                    self.Trigger(EnterDaytimeEvent.Instance);
                }
                else
                {
                    self.IsDaytime = false;
                    self.Trigger(LeaveDayEvent.Instance);
                }
            }
            if (self.IsNight != isNight)
            {
                if (!self.IsNight)
                {
                    self.IsNight = true;
                    self.Trigger(EnterNightEvent.Instance);
                }
                else
                {
                    self.IsNight = false;
                    self.Trigger(LeaveNightEvent.Instance);
                }
            }
        }

        /// <summary>
        /// 批量同步所有玩家的勋章风格分数到大厅（定时增量同步）
        /// </summary>
        /// <param name="self">服务器实例</param>
        /// <param name="timerId">定时器ID</param>
        [Hotfix(TimerCallback = true)]
        public static void SyncAllPlayersDeltaMedalScores(this ServerInstanceEntity self, long timerId)
        {
            var allPlayersInfo = new List<PlayerGrowthServiceSettleStyleRankPointsInfo>();

            // 遍历所有玩家，收集增量勋章数据（仅包含未上报过的勋章等级）
            foreach (var roleId in UserManagerEntity.Instance.TotalRoleIds)
            {
                if (UserManagerEntity.Instance.GetPlayerEntity(roleId) is not PlayerEntity player) continue;

                if (player.ComponentTask.GetTaskContainer<MedalTaskContainer>(PlayerTaskContainerIndex.Medal) is MedalTaskContainer medalTaskContainer)
                {
                    if (medalTaskContainer.GetIncrementalMedalData() is PlayerGrowthServiceSettleStyleRankPointsInfo info)
                    {
                        allPlayersInfo.Add(info);
                    }
                }
            }

            if (allPlayersInfo.Count <= 0)
            {
                self.Logger.Info("SyncAllPlayersDeltaMedalScores: No incremental medal data to report");
                return;
            }

            self.Logger.InfoFormat("SyncAllPlayersDeltaMedalScores: Syncing incremental medal data for {0} players", allPlayersInfo.Count);

            LobbyServiceManager.Instance.lobbyService.SendSettleStyleRankPointsToLobby(allPlayersInfo, self.OnSendSettleStyleRankPointsToLobbyCallback);
        }

        public static void OnSendSettleStyleRankPointsToLobbyCallback(this ServerInstanceEntity self, JSONNode content, HttpStatusCode statusCode, int errorCode)
        {
            if (errorCode == 0 && statusCode == System.Net.HttpStatusCode.OK)
            {
                // 同步成功后，将 CompletedNotRewardTask 中的任务移动到 CompletedTask 中，并生成 MedalSyncRecord
                foreach (var roleId in UserManagerEntity.Instance.TotalRoleIds)
                {
                    if (UserManagerEntity.Instance.GetPlayerEntity(roleId) is not PlayerEntity player) continue;
                    if (player.ComponentTask.GetTaskContainer<MedalTaskContainer>(PlayerTaskContainerIndex.Medal) is
                        MedalTaskContainer medalTaskContainer)
                    {
                        // 收集本次同步的勋章信息用于生成同步记录
                        var syncedMedals = new List<(int styleId, int medalId, int level, int points)>();

                        foreach (var (_, taskNode) in medalTaskContainer.completedNotRewardTask)
                        {
                            var medalInfo = (taskNode as MedalTaskNode).GetMedalInfo();
                            syncedMedals.Add((medalInfo.StyleId, medalInfo.MedalId, medalInfo.Level, medalInfo.Point));

                            medalTaskContainer.completedNotRewardTask.RemoveChildTask(taskNode);
                            medalTaskContainer.AddCompletedTask(taskNode);
                        }

                        medalTaskContainer.GenerateSyncRecord(syncedMedals);
                    }
                }

                self.Logger.InfoFormat("SyncAllPlayersDeltaMedalScores: Successfully synced incremental medal data");
            }
            else
            {
                self.Logger.ErrorFormat(
                    "SyncAllPlayersDeltaMedalScores: Failed to sync incremental medal data, error: {0}, status: {1}",
                    errorCode, statusCode);
            }
        }

        /// <summary>
        /// 上报所有玩家的勋章积分信息（对局结束全量同步）
        /// </summary>
        /// <param name="self">服务器实例</param>
        [Hotfix]
        public static void SyncAllPlayersMedalScores(this ServerInstanceEntity self)
        {
            var allPlayerInfos = new List<PlayerGrowthServiceSettleStyleRankPointsInfo>();

            // 遍历所有玩家，收集全量勋章信息
            foreach (var roleId in UserManagerEntity.Instance.TotalRoleIds)
            {
                if (UserManagerEntity.Instance.GetPlayerEntity(roleId) is not PlayerEntity player) continue;
                if (player.ComponentTask.GetTaskContainer<MedalTaskContainer>(PlayerTaskContainerIndex.Medal) is MedalTaskContainer medalTaskContainer)
                {
                    allPlayerInfos.Add(medalTaskContainer.GetFullMedalData());
                }
            }

            // 如果没有数据需要上报，直接返回成功
            if (allPlayerInfos.Count == 0)
            {
                self.Logger.Info("SyncAllPlayersMedalScores: No medal data to report");
                return;
            }

            self.Logger.InfoFormat("SyncAllPlayersMedalScores: Reporting full medal scores for {0} players", allPlayerInfos.Count);

            LobbyServiceManager.Instance.lobbyService.SendFullStyleRankPointsToLobby(allPlayerInfos,
                (content, statusCode, errorCode) =>
                {
                    var success = errorCode == 0 && statusCode == System.Net.HttpStatusCode.OK;
                    if (success)
                    {
                        self.Logger.InfoFormat("SyncAllPlayersMedalScores: Successfully reported full medal scores for {0} players", allPlayerInfos.Count);
                    }
                    else
                    {
                        self.Logger.ErrorFormat("SyncAllPlayersMedalScores: Failed to report full medal scores, error: {0}, status: {1}", errorCode, statusCode);
                    }
                });
        }
    }
}
