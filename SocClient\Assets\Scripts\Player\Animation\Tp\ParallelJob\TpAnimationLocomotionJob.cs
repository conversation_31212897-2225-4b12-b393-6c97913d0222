using Unity.Collections.LowLevel.Unsafe;
using Unity.Mathematics;
using UnityEngine;
using WizardGames.Soc.Common.Character;
using WizardGames.Soc.Common.Unity.Character;

namespace WizardGames.Soc.SocClient.Player.Animation
{
    /// <summary>
    /// 控制locomotionLayer层行为
    /// </summary>
    public partial struct TpAnimationJob
    {
        private void UpdateLocomotionLayer(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData, ref AnimParametersTp animParams,in TpAniConstData constData)
        {
            if (resultJobData.PlayerLocalData.RenderFrame)
            {
                UpdateLocomotionLayerRenderFrame_StatePercent(ref jobData, ref resultJobData, ref animParams, constData);
            }

            if (resultJobData.PlayerLocalData.LogicFrame)
            {
                UpdateLocomotionLayerLogicFrame_ParamsBefore(ref jobData, ref resultJobData, ref animParams, constData);
                UpdateLocomotionLayerLogicFrame_Tick(ref jobData, ref resultJobData, ref animParams, constData);
            }
            
            if (resultJobData.PlayerLocalData.RenderFrame)
            {
                UpdateLocomotionLayerRenderFrame_Tick(ref jobData, ref resultJobData, ref animParams, constData);
            }
            
            if (resultJobData.PlayerLocalData.LogicFrame)
            {
                UpdateLocomotionLayerLogicFrame_ParamsAfter(ref jobData, ref resultJobData, ref animParams, constData);
                UpdateLocomotionLayerLogicFrame_Late(ref jobData, ref resultJobData, ref animParams, constData);
                UpdateLocomotionLayerLogicFrame_ToRender(ref jobData, ref resultJobData, ref animParams, constData);
            }
        }

        /// <summary>
        /// 按照state进行设置权重
        /// </summary>
        private unsafe void SetLocomotionLayerWeightByState(ref TpAnimationJobData jobData,
            ref TpAnimationResultJobData resultJobData, TpAniBoneMask boneMask, float time, bool defaultValue = false,
            bool maxTime = false)
        {
            int bonesize = sizeof(TpAniBoneMask) / sizeof(float);
            for (int i = 0; i < bonesize; ++i)
            {
                ref var weightRuntimeData = ref resultJobData.PlayerLocalData.LocoBoneWeightGroup.Get(i);
                weightRuntimeData.MaskTargetWeightTp = 1;
                if (!defaultValue)
                {
                    var maskPtr = (float*)UnsafeUtility.AddressOf(ref boneMask);
                    weightRuntimeData.MaskTargetWeightTp = maskPtr[i];
                }
                var finaltime = time;
                if (maxTime)
                {
                    finaltime = math.max(0, weightRuntimeData.MaskWeightTimeTp);
                }
                weightRuntimeData.MaskWeightSpeedTp = Approximately(finaltime, 0f)
                    ? (weightRuntimeData.MaskTargetWeightTp - weightRuntimeData.MaskNowWeightTp) * 1000
                    : (weightRuntimeData.MaskTargetWeightTp - weightRuntimeData.MaskNowWeightTp) / finaltime;
                weightRuntimeData.MaskWeightTimeTp = finaltime;
            }
        }
        
        private void ToLocomotionLayerState(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            int stateInt, AnimParametersTp.ELocomotionLayer eLocomotionLayer, float transitionTime, float normalizeTime,
            bool fixTime, bool defaultValue = false, bool ocBoneFlag =false, TpAniBoneMask ocAniBoneMask =default, bool transitFromSnapshotPose = false,float playRate = 1.0f, bool skipWeight = false,bool changeState = true, bool needComparse=false)
        {
            var animatorInstanceId = jobData.AnimatorInstanceId;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            if (needComparse && playerLocalData.ELocomotionLayer == eLocomotionLayer)
            {
                return;
            }
            if (!skipWeight)
            {
                if (ocBoneFlag)
                {
                    SetLocomotionLayerWeightByState(ref jobData, ref resultJobData,
                        ocAniBoneMask, transitionTime);
                }
                else
                {
                    if (defaultValue)
                    {
                        SetLocomotionLayerWeightByState(ref jobData, ref resultJobData,
                            default, transitionTime, true);
                    }
                    else
                    {
                        var boneMask = GetLocomotionLayerBone(ref jobData, ref resultJobData, eLocomotionLayer);
                        SetLocomotionLayerWeightByState(ref jobData, ref resultJobData,
                            boneMask, transitionTime);
                    }   
                }
            }
            
            if (fixTime)
            {
                Animator.CrossFadeInFixedTimeUnSafe(animatorInstanceId, stateInt, transitionTime,
                    AnimParametersTp.LocomotionLayer_LayerIndex, normalizeTime, 0, transitFromSnapshotPose: transitFromSnapshotPose);
            }
            else
            {
                Animator.CrossFadeUnSafe(animatorInstanceId, stateInt, transitionTime,
                    AnimParametersTp.LocomotionLayer_LayerIndex, normalizeTime, 0, transitFromSnapshotPose: transitFromSnapshotPose);
            }

            if (changeState)
            {
                playerLocalData.LastELocomotionLayer = playerLocalData.ELocomotionLayer;
                playerLocalData.ELocomotionLayer = eLocomotionLayer;
                ChangeLocomotionLayerTime(ref jobData, ref resultJobData,  eLocomotionLayer, playRate);
            }

            var lastIsIdle = InLocomotionIdle(playerLocalData.LastELocomotionLayer);
            var lastIsLoco = InLocomotionJog(playerLocalData.LastELocomotionLayer);
            var nowIsIdle = InLocomotionIdle(playerLocalData.ELocomotionLayer);
            var nowIsLoco = InLocomotionJog(playerLocalData.ELocomotionLayer);
            if (lastIsIdle && nowIsLoco)
            {
                playerLocalData.StartStance2LocoLerp = playerLocalData.TpToLocomotionTime;
                playerLocalData.LeftStance2LocoLerp = playerLocalData.TpToLocomotionTime;
            }else if (lastIsLoco && nowIsIdle)
            {
                playerLocalData.StartStance2LocoLerp = playerLocalData.TpToStanceTime;
                playerLocalData.LeftStance2LocoLerp = playerLocalData.TpToStanceTime;
            }
        }
        
        private void ChangeLocomotionLayerTime(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData,
            AnimParametersTp.ELocomotionLayer nowStateType, float playRate = 1.0f)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            ref var heldData =ref jobData.HeldItemData;
            var heldDataTimeIndex = heldData.LocTimeIndex;
            ref var tpClipCollect = ref playerLocalData.TpClipCollect;
            ref var lastState = ref tpClipCollect.LastLocState;
            ref var nowState = ref tpClipCollect.NowLocState;
            
            //更新last
            lastState = nowState;
            lastState.Start = false;
            //更新now
            nowState.LocState = nowStateType;
            nowState.NowTime = 0f;
            nowState.Start = true;
            nowState.Percent = 0f;
            nowState.PlayRate = playRate;
            var index = heldDataTimeIndex+ (int)nowStateType;
            nowState.AniTime = LocomotionLayerTimeArray[index];
        }
        
        private bool InLocomotionLadder(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Ladder_UpLeave ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Ladder_DownEnter ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Ladder_DownLeave ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Ladder_LadderFastDown ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Ladder_LadderFastUp ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Ladder_LadderMove ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Ladder_LadderProcedureCrouch ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Ladder_LadderProcedureCrouchWait ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Ladder_LadderProcedureJog ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Ladder_LadderProcedureWait ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Ladder_UpEnter;
        }
        
        private bool InLocomotionMantle(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Mantle_MantleOnFar ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Mantle_MantleOnNear;
        }
        
        private bool InLocomotionIdle(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipStance_HipStandIdle ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipStance_HipCrouchIdle ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceLCrouch ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipStance_TurnInPlaceRCrouch ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipStance_Stand2Crouch ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipStance_Stand2CrouchReady ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipStance_Crouch2Stand||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipStance_Crouch2StandReady;
        }
        
        private bool InLocomotionJog(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchBLStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchBRStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchBStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchFLStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchFRStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchFStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchLStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchRStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogBLStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogBRStand||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogBStand||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogFLStand||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogFRStand||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogFStand||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogLStand||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogRStand||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkBLStand||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkBRStand||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkBStand||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkFLStand||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkFRStand||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkFStand||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkLStand||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkRStand;
        }

        private bool InLocomotionSprint(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_SprintF;
        }

        private bool InLocomotionAir(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Jump_JumpStart ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Jump_JumpLoop ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Jump_JumpEnd;
        }
        
        private bool InLocomotionSwimIdle(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Swim_SwimIdle;
        }
        
        private bool InLocomotionSwimJog(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Swim_Swim_JogF||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Swim_Swim_JogB||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Swim_Swim_JogL||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Swim_Swim_JogR;
        }
        
        private bool InLocomotionSwimSprint(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Swim_Swim_SprintF;
        }
        
        private bool InLocomotionRiderLocomotion(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.RiderLocomotion_RiderLocomotionNode;
        }
        
        private bool InLocomotionRiderJump(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.RiderJump_JumpForward_JumpForwardStart||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.RiderJump_JumpCanter_JumpCanterStart||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.RiderJump_JumpGallop_JumpGallopStart||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.RiderJump_JumpSprint_JumpSprintStart||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.RiderJump_JumpForward_JumpForwardLoop||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.RiderJump_JumpCanter_JumpCanterLoop||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.RiderJump_JumpGallop_JumpGallopLoop||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.RiderJump_JumpSprint_JumpSprintLoop||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.RiderJump_JumpForward_JumpForwardEnd||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.RiderJump_JumpCanter_JumpCanterEnd||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.RiderJump_JumpGallop_JumpGallopEnd||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.RiderJump_JumpSprint_JumpSprintEnd;
        }
        
        private bool InLocomotionRiderHalter(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.RiderJump_Neigh;
        }
        
        private bool InLocomotionParachute(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Parachute_Start||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Parachute_Cut||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Parachute_ParachuteIdle;
        }
        
        private bool InLocomotionInjured(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Injured_ToInjured||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Injured_InjuredIdle||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Injured_InjuredMove;
        }
        
        private bool InLocomotionInCap(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.InCap_ToLieDown||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.InCap_LieDownLoop;
        }
        
        private bool InLocomotionDrive(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Drive_Idle ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Drive_KayakDrive ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Drive_KayakRaise ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Drive_ModularDriveL ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Drive_ModularDriveR;
        }
        
        private bool InLocomotionZipline(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Zipline_ZiplineFast ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Zipline_ZiplineSlow ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Zipline_ZiplineStandIn ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Zipline_ZiplineStandOut ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.Zipline_ZiplineSwitch;
        }
        
        private bool InLocomotionEmpty(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.LocomotionEmpty;
        }
        
        private bool InLocomotionStandWalk(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkBLStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkBRStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkBStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkFLStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkFRStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkFStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkLStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_WalkRStand;
        }
        
        private bool InLocomotionCrouchWalk(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchBLStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchBRStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchBStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchFLStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchFRStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchFStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchLStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_CrouchRStand;
        }
        
        private bool InLocomotionStandJog(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            return eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogBLStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogBRStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogBStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogFLStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogFRStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogFStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogLStand ||
                   eLocomotionLayer == AnimParametersTp.ELocomotionLayer.HipLocomotion_Locomotion_JogRStand;
        }

        private int GetTpHorseJumpType(AnimParametersTp.ELocomotionLayer  eLocomotionLayer)
        {
            switch (eLocomotionLayer)
            {
                case AnimParametersTp.ELocomotionLayer.RiderJump_JumpCanter_JumpCanterStart:
                case AnimParametersTp.ELocomotionLayer.RiderJump_JumpForward_JumpForwardStart:
                case AnimParametersTp.ELocomotionLayer.RiderJump_JumpGallop_JumpGallopStart:
                case AnimParametersTp.ELocomotionLayer.RiderJump_JumpSprint_JumpSprintStart:
                    return 0;
                case AnimParametersTp.ELocomotionLayer.RiderJump_JumpCanter_JumpCanterLoop:
                case AnimParametersTp.ELocomotionLayer.RiderJump_JumpForward_JumpForwardLoop:
                case AnimParametersTp.ELocomotionLayer.RiderJump_JumpGallop_JumpGallopLoop:
                case AnimParametersTp.ELocomotionLayer.RiderJump_JumpSprint_JumpSprintLoop:
                    return 1;
                case AnimParametersTp.ELocomotionLayer.RiderJump_JumpCanter_JumpCanterEnd:
                case AnimParametersTp.ELocomotionLayer.RiderJump_JumpForward_JumpForwardEnd:
                case AnimParametersTp.ELocomotionLayer.RiderJump_JumpGallop_JumpGallopEnd:
                case AnimParametersTp.ELocomotionLayer.RiderJump_JumpSprint_JumpSprintEnd:
                    return 2;
            }
            return -1;
        }

        private float GetStateNormalizedTimeLocomotionLayer(ref TpAnimationJobData jobData, int stateIndex)
        {
            return GetStateNormalizedTime(ref jobData, AnimParametersTp.LocomotionLayer_LayerIndex, stateIndex);
        }
    }
}