using CommonUnity.Runtime.Animation;
using System;
using System.Collections.Generic;
using Unity.Collections;
using Unity.Collections.LowLevel.Unsafe;
using Unity.Mathematics;
using WizardGames.Soc.Common.Const;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Data.DataItem;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.State.Character;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Weapon;
#if SOC_CLIENT
using WizardGames.Soc.SocClient.Player.Animation;
#endif

namespace WizardGames.Soc.Common.Character
{
    public enum ETpDebugBoneGroup
    {
        None,
        Group,
        Spine,
        LeftClav,
        RightClav,
        Head,
    }
    public struct TpAniPlayerDebugBone
    {
        public FixedString64Bytes BoneName;
        // public FixedString512Bytes BonePath;
        public int BonePathHash;
        public float Weight;
        public ETpDebugBoneGroup Group;
    }
    

    /// tp动画用到的结构体
    /// 角色tp用于动画的同步状态数据
    public struct TpAniPlayerStateData :IAnimSpeedData
    {
        /// poseState状态
        public PlayerPoseStateEnum PoseState { get; set; }
        /// moveState状态
        public PlayerMoveStateEnum MoveState{ get; set; }
        /// 非激活状态
        public PlayerUnAliveStateEnum UnAliveState{ get; set; }
        /// 爬梯状态
        public PlayerMoveLadderStateEnum MoveLadderState{ get; set; }
        /// 行为
        public PlayerActionStateEnum ActionState{ get; set; }
        /// jump类型
        public PlayerMoveJumpStateEnum MoveJumpState;
        /// swim类型
        public PlayerMoveSwimStateEnum MoveSwimState;
        /// 换弹
        public PlayerActionStateReloadEnum ReloadState;
        /// 开枪
        public PlayerAttackStateEnum AttackSubState;
        /// 投掷状态
        public PlayerThrowState ThrowState;

        public PlayerBowStateEnum BowState;
        public PlayerAdsStateEnum AdsState;
        public PlayerActionHoldStateEnum ActionHoldState;
        public PlayerPoseDyingStateEnum PoseDyingState;
        public PlayerMoveZiplineStateEnum MoveZiplineState;

        public int LadderMoveFlag;
        public LadderTestDir LadderEnterDir;
        public LadderTestDir LadderExitDir;

        public int AnimMoveTime;
        public int AnimTurnTime;
        public int AnimRMTotalTime;
        public int AnimSpeed;
        
        public quaternion LadderMoveQuat;
        public quaternion LadderQuat;
        public float3 LadderNormal;
        public float3 LadderForward;
        public float3 LadderRight;
        
        //速度配置缓存
        public AnimMoveSpeedConfGroup MoveSpeedConfList;

        //因为落地宽容，不能直接进入jumploop
        public bool Disable2JumpLoop;

        // 表情动作
        public float GestureIndex;
        
        /// 载具Id
        public long MountableId;

        /// <summary>
        /// 载具类型
        /// </summary>
        public int VehicleType;

        /// <summary>
        /// 座位类型
        /// </summary>
        public int MountableType;

        /// <summary>
        /// 是否是司机
        /// </summary>
        public bool IsDriver;

        /// <summary>
        /// 角色的旋转
        /// </summary>
        public float RotateY{ get; set; }

        /// <summary>
        /// 角色的cmd yaw偏航角
        /// </summary>
        public float ViewYaw;

        public float CCHeight;

        /// <summary>
        /// 角色的cmd yaw偏航角上一逻辑帧
        /// </summary>
        public float LastViewYaw;

        /// <summary>
        /// 开关镜进度
        /// </summary>
        public float AdsOffsetProgress;

        public long CurrentWeaponId;

        /// <summary>
        /// 移动八方向
        /// </summary>
        public int Movement8Direction;

        /// <summary>
        /// 移动4方向
        /// </summary>
        public int Movement4Direction;

        /// <summary>
        /// rt的index
        /// </summary>
        public int RootMotionWarpingIndex;

        public bool RootMotionRefresh;

        /// <summary>
        /// 疾跑
        /// </summary>
        public bool InputSprint;

        /// <summary>
        /// 移速
        /// </summary>
        public float SpeedX{ get; set; }

        public float SpeedZ{ get; set; }
        public float SpeedY{ get; set; }

        /// <summary>
        /// 玩家id
        /// </summary>
        public long PlayerEntityId;

        /// <summary>
        /// 打断
        /// </summary>
        public int StateRecoveryReason;

        /// <summary>
        /// 尝试开枪
        /// </summary>
        public bool TryFire;

        // public bool UsingForOther;
        public float CmdPitch;

        public int AttackHitMat;
        public int UseHitIndex;//下次近战命中检测的索引，>=3不播命中动画

        public float MantleOnPlayRate;

        public int rmCurTime;

        public float warmupProgress;
        public int warmupAnimType;
        public bool InSkyDiving;
        public bool IsDead;        

        //turn in place
        /// <summary>
        /// ao的朝向角度
        /// </summary>
        public float AimYaw;
        /// <summary>
        /// 交互的逻辑时间
        /// </summary>
        public float InteractiveStateTime;

        public bool IsTp;
    }

    /// <summary>
    /// 手持物的数据
    /// </summary>
    public struct TpAniHeldItemData
    {
        /// <summary>
        /// 武器tableId
        /// </summary>
        public long CurrentWeaponTableId;

        public int CurrentTableItemEnum;
        public ItemEntityType CurrentTableItemType;

        /// <summary>
        /// 能否在梯子上持有
        /// </summary>
        public bool DisplayOnLadder;

        /// <summary>
        /// 左手EntityId
        /// </summary>
        public long ItemLeftEntityId;

        /// <summary>
        /// 装备类型
        /// </summary>
        public EquipType EquipType;

        public int SlingSort;
        public float MeleeAttackTime;

        /// <summary>
        /// 攻击下标
        /// </summary>
        public int MeleeAttackIndex;

        public bool FarThrow;
        public bool CanPlayHitAnim;

        public bool IsWarmupWeapon;
        
        /// <summary>
        /// 装备类型
        /// </summary>
        public EHeldSocketType HoldType;
        /// <summary>
        /// fp 的配置
        /// </summary>
        public FpMetaData FpMetaData;

        public int TpAniOcWeightIndex;
        
        public int OcTimeIndex;

        public int LocTimeIndex;
        
        public int LocTimSpecialIndex;
        
        public int TpAniTipCurveIndex;

        public int TpAniLocomotionWeightIndex;
    }

    /// <summary>
    /// 手持的权重
    /// </summary>
    public struct TpAniHeldItemWeight
    {
        public TpAniBoneMask IdleWeight;
        public TpAniBoneMask JogWeight;
        public TpAniBoneMask SprintWeight;
        public TpAniBoneMask JumpWeight;
        public TpAniBoneMask SwimJogWeight;
        public TpAniBoneMask SwimSprintWeight;
        public TpAniBoneMask SwimIdleWeight;
        public TpAniBoneMask MantleWeight;
        public TpAniBoneMask LadderWeight;
        public TpAniBoneMask Stand2CrouchWeight;
        public TpAniBoneMask Crouch2StandWeight;
        public TpAniBoneMask HorseLocomotionWeight;
        public TpAniBoneMask ParachuteLocomotionWeight;
    }

    public struct TpAniBoneMask
    {
        //按顺序
        //dunamic
        public float Bip01;
        public float Bip01Pelvis;
        public float Bip01LThigh;
        public float Bip01LCalf;
        public float Bip01RThigh;
        public float Bip01RCalf;
        //spine
        public float Bip01Spine;
        public float Bip01Spine1;
        public float Bip01Spine2;
        public float Bip01LClavicle;
        public float Bip01Neck;
        public float Bip01Head;
        public float Bip01RClavicle;
        //weapon
        public float BaseWeaponLocator;
        //leftarm
        public float Bip01LUpperArm;
        public float Bip01LForearm;
        public float Bip01LHand;
        //rightarm
        public float Bip01RUpperArm;
        public float Bip01RForearm;
        public float Bip01RHand;

        public void InitDefaultValue()
        {
            Bip01 = 1;
            Bip01Pelvis = 1;
            Bip01LThigh = 1;
            Bip01LCalf = 1;
            Bip01RThigh = 1;
            Bip01RCalf = 1;
            Bip01Spine = 1;
            Bip01Spine1 = 1;
            Bip01Spine2 = 1;
            Bip01LClavicle = 1;
            Bip01Neck = 1;
            Bip01Head = 1;
            Bip01RClavicle = 1;
            BaseWeaponLocator = 1;
            Bip01LUpperArm = 1;
            Bip01LForearm = 1;
            Bip01LHand = 1;
            Bip01RUpperArm = 1;
            Bip01RForearm = 1;
            Bip01RHand = 1;
        }
    }
    
#if SOC_CLIENT
    public struct TpOcAniBoneCurveMask :IDisposable
    {
         //按顺序
         //dunamic
         public SocJobAnimationCurve Bip01;
         public SocJobAnimationCurve Bip01Pelvis;
         public SocJobAnimationCurve Bip01LThigh;
         public SocJobAnimationCurve Bip01LCalf;
         public SocJobAnimationCurve Bip01RThigh;
         public SocJobAnimationCurve Bip01RCalf;
         //spine
         public SocJobAnimationCurve Bip01Spine;
         public SocJobAnimationCurve Bip01Spine1;
         public SocJobAnimationCurve Bip01Spine2;
         public SocJobAnimationCurve Bip01LClavicle;
         public SocJobAnimationCurve Bip01Neck;
         public SocJobAnimationCurve Bip01Head;
         public SocJobAnimationCurve Bip01RClavicle;
         //weapon
         public SocJobAnimationCurve BaseWeaponLocator;
         //leftarm
         public SocJobAnimationCurve Bip01LUpperArm;
         public SocJobAnimationCurve Bip01LForearm;
         public SocJobAnimationCurve Bip01LHand;
         //rightarm
         public SocJobAnimationCurve Bip01RUpperArm;
         public SocJobAnimationCurve Bip01RForearm;
         public SocJobAnimationCurve Bip01RHand;

        public static unsafe int Capacity => sizeof(TpOcAniBoneCurveMask)/ sizeof(SocJobAnimationCurve);

        public void Dispose()
        {
            Bip01.Dispose();
            Bip01Pelvis.Dispose();
            Bip01LThigh.Dispose();
            Bip01LCalf.Dispose();
            Bip01RThigh.Dispose();
            Bip01RCalf.Dispose();
            Bip01Spine.Dispose();
            Bip01Spine1.Dispose();
            Bip01Spine2.Dispose();
            Bip01LClavicle.Dispose();
            Bip01Neck.Dispose();
            Bip01Head.Dispose();
            Bip01RClavicle.Dispose();
            BaseWeaponLocator.Dispose();
            Bip01LUpperArm.Dispose();
            Bip01LForearm.Dispose();
            Bip01LHand.Dispose();
            Bip01RUpperArm.Dispose();
            Bip01RForearm.Dispose();
            Bip01RHand.Dispose();
        }
    }
#endif
    
    public struct TpAoAniBoneMask
    {
        //按顺序
        //dunamic
        public float Bip01;
        public float Bip01Pelvis;
        public float Bip01LThigh;
        public float Bip01LCalf;
        public float Bip01RThigh;
        public float Bip01RCalf;
        //spine
        public float Bip01Spine;
        //spine1
        public float Bip01Spine1;
        //spine2
        public float Bip01Spine2;
        //head
        public float Bip01Neck;
        public float Bip01Head;
        //hand
        public float Bip01LClavicle;
        public float Bip01LUpperArm;
        public float Bip01LForearm;
        public float Bip01LHand;
        public float Bip01RClavicle;
        public float BaseWeaponLocator;
        public float Bip01RUpperArm;
        public float Bip01RForearm;
        public float Bip01RHand;
    }


    /// <summary>
    /// 片段数据
    /// </summary>
    public struct ClipMetaData
    {
        public int ClipNameHash;
        public float AnimationTime;
        public float AnimatorStateTime;
        public float RecoveryPercentage;
    }

    /// <summary>
    /// 所依赖的第一人称的相关数据
    /// </summary>
    public struct FpMetaData
    {
        public ClipMetaData DrawClipMetaData;
        public ClipMetaData AttackUClipMetaData;
        public ClipMetaData AttackRClipMetaData;
        public ClipMetaData AttackR2ClipMetaData;
        public ClipMetaData AttackCClipMetaData;
        public ClipMetaData AttackLClipMetaData;
        public ClipMetaData AttackL2ClipMetaData;
    }
    
    /// <summary>
    /// 动画时长
    /// </summary>
    public struct TpClipLengthCollect
    {
        public TpClipLength JumpStart;
        public TpClipLength JumpEnd;
        
        public TpClipLength NowState;
        public TpClipLength LastState;

        public TpClipLength NowLocState;
        public TpClipLength LastLocState;

        public TpClipLength Bolt;
    }
    
    /// <summary>
    /// 动画时长
    /// </summary>
    public struct TpClipLength
    {
        public AnimParametersTp.EOverrideLayer State;
        public AnimParametersTp.ELocomotionLayer LocState;
        public float AniTime;
        public float NowTime;
        public bool Start;
        public float Percent;
        public float PlayRate;
        public bool IsLoop;
    }
    
    /// <summary>
    /// 载具相关数值
    /// </summary>
    public struct TpVehicleSeatTbData
    {
        public bool SeatTbIsOmnidirectional;
        /// <summary>
        /// 相关ao载具的数值
        /// </summary>
        public float SeatTbFrontOverlapLeft;
        public float SeatTbFrontOverlapRight;
        public float SeatTbBackOverlapLeft;
        public float SeatTbBackOverlapRight;
        public float SeatTbFrontAnimOverlapLeft;
        public float SeatTbFrontAnimOverlapRight;
        public float YawLeft;
        public float YawRight;
        public float AnimYawLeft;
        public float AnimYawRight;
        public LowerSeatPoseType LowerSeatPoseType;
    }

    /// <summary>
    /// 载具数据
    /// </summary>
    public struct TpAniVehicleData
    {
        /// <summary>
        /// 座位类型,-1代表没坐
        /// </summary>
        public int SeatStyleType;

        public TpVehicleSeatTbData VehicleSeatTbData;
      
        /// <summary>
        /// 载具移动数值
        /// </summary>
        public float HorseVertical;
        public float HorseHorizontal;
        public float HorseLocomotionMultiplier;
        public float Slope;
        /// <summary>
        /// 是否当前是骑马
        /// </summary>
        public bool HaveHorseData;
        /// <summary>
        /// 马的移动节点进度
        /// </summary>
        public float HorseLocomotionNormalizedTime;

        public float SteerAngle;
        public int HorseAniType;
        public int HorseJumpIndex;
        public int HorseJumpType;
        
        // 降落伞状态
        public int ParachuteState;
        public float ParachuteMoveSpeed;
        public float ParachuteRotateSpeed;
    }

    /// <summary>
    /// tp运行时得mask权重参数
    /// </summary>
    public struct TpMaskWeightRuntimeData
    {
        public float MaskTargetWeightTp;
        /// <summary>
        /// tp 移动层过渡速度
        /// </summary>
        public float MaskWeightSpeedTp;
        /// <summary>
        /// tp 移动层当前权重 初始化1
        /// </summary>
        public float MaskNowWeightTp;
        /// <summary>
        /// tp 移动层当前过渡时长
        /// </summary>
        public float MaskWeightTimeTp;
        /// <summary>
        /// tp行为覆盖的权重
        /// </summary>
        public float MaskOverrideTargetWeightTp;

        public TpMaskWeightRuntimeData(float w)
        {
            MaskTargetWeightTp = w;
            MaskWeightSpeedTp = w;
            MaskNowWeightTp = w;
            MaskWeightTimeTp = w;
            MaskOverrideTargetWeightTp = w;
        }

        public void Init(float w)
        {
            MaskNowWeightTp = w;
            MaskTargetWeightTp = w;
        }
    }

    public unsafe struct TpMaskWeightFloatGroup
    {
        public float mask0;
        public float mask1;
        public float mask2;
        public float mask3;
        public float mask4;
        public float mask5;
        public float mask6;
        public float mask7;
        public float mask8;
        public float mask9;
        public float mask10;
        public float mask11;
        public float mask12;
        public float mask13;
        public float mask14;
        public float mask15;
        public float mask16;
        public float mask17;
        public float mask18;
        public float mask19;
        public float mask20;
        public float mask21;
        public float mask22;
        public float mask23;
        public float mask24;
        public float mask25;
        public float mask26;
        public float mask27;
        public float mask28;
        public float mask29;
        public float mask30;
        public float mask31;
        public float mask32;
        public float mask33;
        public float mask34;
        public float mask35;
        public float mask36;
        public float mask37;
        
        public static unsafe int Capacity => sizeof(TpMaskWeightFloatGroup) / sizeof(float);
        
        public unsafe void Set(int idx, float value)
        {
            if (idx < 0 || idx >= Capacity)
            {
                throw new Exception(string.Format("TpMaskWeightFloatGroup Index out of range: {0}", idx));
            }
            UnsafeUtility.WriteArrayElement(UnsafeUtility.AddressOf(ref this), idx, value);
        }
    }

    public unsafe struct TpMaskWeightConfGroup
    {
        public int mask0;
        public int mask1;
        public int mask2;
        public int mask3;
        public int mask4;
        public int mask5;
        public int mask6;
        public int mask7;
        public int mask8;
        public int mask9;
        public int mask10;
        public int mask11;
        public int mask12;
        public int mask13;
        public int mask14;
        public int mask15;
        public int mask16;
        public int mask17;
        public int mask18;
        public int mask19;
        public int mask20;
        public int mask21;
        public int mask22;
        public int mask23;
        public int mask24;
        public int mask25;
        public int mask26;
        public int mask27;
        public int mask28;
        public int mask29;
        public int mask30;
        public int mask31;
        public int mask32;
        public int mask33;
        public int mask34;
        public int mask35;
        public int mask36;
        public int mask37;
        
        public static unsafe int Capacity => sizeof(TpMaskWeightConfGroup) / sizeof(int);
        
        public unsafe void Set(int idx, int value)
        {
            if (idx < 0 || idx >= Capacity)
            {
                throw new Exception(string.Format("TpMaskWeightConfGroup Index out of range: {0}", idx));
            }
            UnsafeUtility.WriteArrayElement(UnsafeUtility.AddressOf(ref this), idx, value);
        }
        
        public unsafe int Get(int idx)
        {
            if (idx < 0 || idx >= Capacity)
            {
                throw new Exception(string.Format("TpMaskWeightConfGroup Index out of range: {0}", idx));
            }
            return UnsafeUtility.ReadArrayElement<int>(UnsafeUtility.AddressOf(ref this), idx);
        }
    }

    public unsafe struct TpMaskWeightRuntimeDataGroup
    {
        public unsafe struct DefaultGroup
        {
            public TpMaskWeightRuntimeData bip01;
            public TpMaskWeightRuntimeData bip01Pelvis;
            public TpMaskWeightRuntimeData lThigh;
            public TpMaskWeightRuntimeData lCalf;
            public TpMaskWeightRuntimeData rThigh;
            public TpMaskWeightRuntimeData rCalf;
            
            public static unsafe int Capacity => sizeof(DefaultGroup)  / sizeof(TpMaskWeightRuntimeData);
            public unsafe ref TpMaskWeightRuntimeData Get(int idx)
            {
                if (idx < 0 || idx >= Capacity)
                {
                    throw new Exception(string.Format("TpMaskWeightRuntimeDataGroup Index out of range: {0}", idx));
                }
                return ref UnsafeUtility.ArrayElementAsRef<TpMaskWeightRuntimeData>(UnsafeUtility.AddressOf(ref this), idx);
            }
        }
        
        public unsafe struct SpineGroup
        {
            public TpMaskWeightRuntimeData spine;
            
            public static  unsafe int Capacity => sizeof(SpineGroup)  / sizeof(TpMaskWeightRuntimeData);
            public unsafe ref TpMaskWeightRuntimeData Get(int idx)
            {
                if (idx < 0 || idx >= Capacity)
                {
                    throw new Exception(string.Format("TpMaskWeightRuntimeDataGroup Index out of range: {0}", idx));
                }
                return ref UnsafeUtility.ArrayElementAsRef<TpMaskWeightRuntimeData>(UnsafeUtility.AddressOf(ref this), idx);
            }
        }
        
        public unsafe struct Spine1Group
        {
            public TpMaskWeightRuntimeData spine1;
            
            public static  unsafe int Capacity => sizeof(Spine1Group)  / sizeof(TpMaskWeightRuntimeData);
            public unsafe ref TpMaskWeightRuntimeData Get(int idx)
            {
                if (idx < 0 || idx >= Capacity)
                {
                    throw new Exception(string.Format("TpMaskWeightRuntimeDataGroup Index out of range: {0}", idx));
                }
                return ref UnsafeUtility.ArrayElementAsRef<TpMaskWeightRuntimeData>(UnsafeUtility.AddressOf(ref this), idx);
            }
        }
        
        public unsafe struct Spine2Group
        {
            public TpMaskWeightRuntimeData spine2;
            
            public static  unsafe int Capacity => sizeof(Spine2Group)  / sizeof(TpMaskWeightRuntimeData);
            public unsafe ref TpMaskWeightRuntimeData Get(int idx)
            {
                if (idx < 0 || idx >= Capacity)
                {
                    throw new Exception(string.Format("TpMaskWeightRuntimeDataGroup Index out of range: {0}", idx));
                }
                return ref UnsafeUtility.ArrayElementAsRef<TpMaskWeightRuntimeData>(UnsafeUtility.AddressOf(ref this), idx);
            }
        }
        
        public unsafe struct LCaviGroup
        {
            public TpMaskWeightRuntimeData lClavicle;
            
            public static  unsafe int Capacity => sizeof(LCaviGroup)  / sizeof(TpMaskWeightRuntimeData);
            public unsafe ref TpMaskWeightRuntimeData Get(int idx)
            {
                if (idx < 0 || idx >= Capacity)
                {
                    throw new Exception(string.Format("TpMaskWeightRuntimeDataGroup Index out of range: {0}", idx));
                }
                return ref UnsafeUtility.ArrayElementAsRef<TpMaskWeightRuntimeData>(UnsafeUtility.AddressOf(ref this), idx);
            }
        }
        
        public unsafe struct HeadGroup
        {
            public TpMaskWeightRuntimeData neck;
            public TpMaskWeightRuntimeData head;
            
            public static  unsafe int Capacity => sizeof(HeadGroup)  / sizeof(TpMaskWeightRuntimeData);
            public unsafe ref TpMaskWeightRuntimeData Get(int idx)
            {
                if (idx < 0 || idx >= Capacity)
                {
                    throw new Exception(string.Format("TpMaskWeightRuntimeDataGroup Index out of range: {0}", idx));
                }
                return ref UnsafeUtility.ArrayElementAsRef<TpMaskWeightRuntimeData>(UnsafeUtility.AddressOf(ref this), idx);
            }
        }
        
        public unsafe struct RCaviGroup
        {
            public TpMaskWeightRuntimeData rClavicle;
            
            public static  unsafe int Capacity => sizeof(RCaviGroup)  / sizeof(TpMaskWeightRuntimeData);
            public unsafe ref TpMaskWeightRuntimeData Get(int idx)
            {
                if (idx < 0 || idx >= Capacity)
                {
                    throw new Exception(string.Format("TpMaskWeightRuntimeDataGroup Index out of range: {0}", idx));
                }
                return ref UnsafeUtility.ArrayElementAsRef<TpMaskWeightRuntimeData>(UnsafeUtility.AddressOf(ref this), idx);
            }
        }
        
        public unsafe struct WeaponGroup
        {
            public TpMaskWeightRuntimeData baseWeaponLocator;
            
            public static unsafe int Capacity => sizeof(WeaponGroup)  / sizeof(TpMaskWeightRuntimeData);
            public unsafe ref TpMaskWeightRuntimeData Get(int idx)
            {
                if (idx < 0 || idx >= Capacity)
                {
                    throw new Exception(string.Format("TpMaskWeightRuntimeDataGroup Index out of range: {0}", idx));
                }
                return ref UnsafeUtility.ArrayElementAsRef<TpMaskWeightRuntimeData>(UnsafeUtility.AddressOf(ref this), idx);
            }
        }
        
        public unsafe struct LeftArmGroup
        {
            public TpMaskWeightRuntimeData lUpperArm;
            public TpMaskWeightRuntimeData lForearm;
            public TpMaskWeightRuntimeData lHand;
            public static  unsafe int Capacity => sizeof(LeftArmGroup)  / sizeof(TpMaskWeightRuntimeData);
            public unsafe ref TpMaskWeightRuntimeData Get(int idx)
            {
                if (idx < 0 || idx >= Capacity)
                {
                    throw new Exception(string.Format("TpMaskWeightRuntimeDataGroup Index out of range: {0}", idx));
                }
                return ref UnsafeUtility.ArrayElementAsRef<TpMaskWeightRuntimeData>(UnsafeUtility.AddressOf(ref this), idx);
            }
        }
        
        public unsafe struct RightArmGroup
        {
            public TpMaskWeightRuntimeData rUpperArm;
            public TpMaskWeightRuntimeData rForearm;
            public TpMaskWeightRuntimeData rHand;
            
            public static  unsafe int Capacity => sizeof(RightArmGroup)  / sizeof(TpMaskWeightRuntimeData);
            public unsafe ref TpMaskWeightRuntimeData Get(int idx)
            {
                if (idx < 0 || idx >= Capacity)
                {
                    throw new Exception(string.Format("TpMaskWeightRuntimeDataGroup Index out of range: {0}", idx));
                }
                return ref UnsafeUtility.ArrayElementAsRef<TpMaskWeightRuntimeData>(UnsafeUtility.AddressOf(ref this), idx);
            }
        }
        
        
        public DefaultGroup defaultGroup;
        public SpineGroup spineGroup;
        public Spine1Group spine1Group;
        public Spine2Group spine2Group;
        public LCaviGroup lcaviGroup;
        public HeadGroup headGroup;
        public RCaviGroup rcaviGroup;
        public WeaponGroup weaponGroup;
        public LeftArmGroup leftArmGroup;
        public RightArmGroup rightArmGroup;

        public static unsafe int FullCapacity => sizeof(TpMaskWeightRuntimeDataGroup) / sizeof(TpMaskWeightRuntimeData);

        public unsafe ref TpMaskWeightRuntimeData Get(int idx)
        {
            if (idx < 0 || idx >= FullCapacity)
            {
                throw new Exception(string.Format("TpMaskWeightRuntimeDataGroup Index out of range: {0}", idx));
            }
            return ref UnsafeUtility.ArrayElementAsRef<TpMaskWeightRuntimeData>(UnsafeUtility.AddressOf(ref this), idx);
        }

        public TpMaskWeightRuntimeDataGroup(float w)
        {
            defaultGroup.bip01 = new TpMaskWeightRuntimeData(w);
            defaultGroup.bip01Pelvis = new TpMaskWeightRuntimeData(w);
            defaultGroup.lThigh = new TpMaskWeightRuntimeData(w);
            defaultGroup.lCalf = new TpMaskWeightRuntimeData(w);
            defaultGroup.rThigh = new TpMaskWeightRuntimeData(w);
            defaultGroup.rCalf = new TpMaskWeightRuntimeData(w);
            spineGroup.spine = new TpMaskWeightRuntimeData(w);
            spine1Group.spine1 = new TpMaskWeightRuntimeData(w);
            spine2Group.spine2 = new TpMaskWeightRuntimeData(w);
            lcaviGroup.lClavicle = new TpMaskWeightRuntimeData(w);
            headGroup.neck = new TpMaskWeightRuntimeData(w);
            headGroup.head = new TpMaskWeightRuntimeData(w);
            rcaviGroup.rClavicle = new TpMaskWeightRuntimeData(w);
            weaponGroup.baseWeaponLocator = new TpMaskWeightRuntimeData(w);
            leftArmGroup.lUpperArm = new TpMaskWeightRuntimeData(w);
            leftArmGroup.lForearm = new TpMaskWeightRuntimeData(w);
            leftArmGroup.lHand = new TpMaskWeightRuntimeData(w);
            rightArmGroup.rUpperArm = new TpMaskWeightRuntimeData(w);
            rightArmGroup.rForearm = new TpMaskWeightRuntimeData(w);
            rightArmGroup.rHand = new TpMaskWeightRuntimeData(w);
        }

        public void Init(float w)
        {
            defaultGroup.bip01.Init(w);
            defaultGroup.bip01Pelvis.Init(w);
            defaultGroup.lThigh.Init(w);
            defaultGroup.lCalf.Init(w);
            defaultGroup.rThigh.Init(w);
            defaultGroup.rCalf.Init(w);
            spineGroup.spine.Init(w);
            spine1Group.spine1.Init(w);
            spine2Group.spine2.Init(w);
            lcaviGroup.lClavicle.Init(w);
            headGroup.neck.Init(w);
            headGroup.head.Init(w);
            rcaviGroup.rClavicle.Init(w);
            weaponGroup.baseWeaponLocator.Init(w);
            leftArmGroup.lUpperArm.Init(w);
            leftArmGroup.lForearm.Init(w);
            leftArmGroup.lHand.Init(w);
            rightArmGroup.rUpperArm.Init(w);
            rightArmGroup.rForearm.Init(w);
            rightArmGroup.rHand.Init(w);
            
        }
    }
    
    public unsafe struct TpAoMaskWeightRuntimeDataGroup
    {
        public unsafe struct DefaultGroup
        {
            public TpMaskWeightRuntimeData bip01;
            public TpMaskWeightRuntimeData bip01Pelvis;
            public TpMaskWeightRuntimeData lThigh;
            public TpMaskWeightRuntimeData lCalf;
            public TpMaskWeightRuntimeData rThigh;
            public TpMaskWeightRuntimeData rCalf;
            
            public static unsafe int Capacity => sizeof(DefaultGroup)  / sizeof(TpMaskWeightRuntimeData);
            public unsafe ref TpMaskWeightRuntimeData Get(int idx)
            {
                if (idx < 0 || idx >= Capacity)
                {
                    throw new Exception(string.Format("TpMaskWeightRuntimeDataGroup Index out of range: {0}", idx));
                }
                return ref UnsafeUtility.ArrayElementAsRef<TpMaskWeightRuntimeData>(UnsafeUtility.AddressOf(ref this), idx);
            }
        }
        
        public unsafe struct SpineGroup
        {
            public TpMaskWeightRuntimeData spine;
            
            public static  unsafe int Capacity => sizeof(SpineGroup)  / sizeof(TpMaskWeightRuntimeData);
            public unsafe ref TpMaskWeightRuntimeData Get(int idx)
            {
                if (idx < 0 || idx >= Capacity)
                {
                    throw new Exception(string.Format("TpMaskWeightRuntimeDataGroup Index out of range: {0}", idx));
                }
                return ref UnsafeUtility.ArrayElementAsRef<TpMaskWeightRuntimeData>(UnsafeUtility.AddressOf(ref this), idx);
            }
        }
        
        public unsafe struct Spine1Group
        {
            public TpMaskWeightRuntimeData spine1;
            
            public static  unsafe int Capacity => sizeof(Spine1Group)  / sizeof(TpMaskWeightRuntimeData);
            public unsafe ref TpMaskWeightRuntimeData Get(int idx)
            {
                if (idx < 0 || idx >= Capacity)
                {
                    throw new Exception(string.Format("TpMaskWeightRuntimeDataGroup Index out of range: {0}", idx));
                }
                return ref UnsafeUtility.ArrayElementAsRef<TpMaskWeightRuntimeData>(UnsafeUtility.AddressOf(ref this), idx);
            }
        }
        
        public unsafe struct Spine2Group
        {
            public TpMaskWeightRuntimeData spine2;
            
            public static  unsafe int Capacity => sizeof(Spine2Group)  / sizeof(TpMaskWeightRuntimeData);
            public unsafe ref TpMaskWeightRuntimeData Get(int idx)
            {
                if (idx < 0 || idx >= Capacity)
                {
                    throw new Exception(string.Format("TpMaskWeightRuntimeDataGroup Index out of range: {0}", idx));
                }
                return ref UnsafeUtility.ArrayElementAsRef<TpMaskWeightRuntimeData>(UnsafeUtility.AddressOf(ref this), idx);
            }
        }
        
        public unsafe struct HeadGroup
        {
            public TpMaskWeightRuntimeData neck;
            public TpMaskWeightRuntimeData head;
            
            public static  unsafe int Capacity => sizeof(HeadGroup)  / sizeof(TpMaskWeightRuntimeData);
            public unsafe ref TpMaskWeightRuntimeData Get(int idx)
            {
                if (idx < 0 || idx >= Capacity)
                {
                    throw new Exception(string.Format("TpMaskWeightRuntimeDataGroup Index out of range: {0}", idx));
                }
                return ref UnsafeUtility.ArrayElementAsRef<TpMaskWeightRuntimeData>(UnsafeUtility.AddressOf(ref this), idx);
            }
        }
        
        public unsafe struct Hand
        {
            public TpMaskWeightRuntimeData lClavicle;
            public TpMaskWeightRuntimeData lUpperArm;
            public TpMaskWeightRuntimeData lForearm;
            public TpMaskWeightRuntimeData lHand;
            public TpMaskWeightRuntimeData rClavicle;
            public TpMaskWeightRuntimeData baseWeaponLocator;
            public TpMaskWeightRuntimeData rUpperArm;
            public TpMaskWeightRuntimeData rForearm;
            public TpMaskWeightRuntimeData rHand;
            
            public static  unsafe int Capacity => sizeof(Hand)  / sizeof(TpMaskWeightRuntimeData);
            public unsafe ref TpMaskWeightRuntimeData Get(int idx)
            {
                if (idx < 0 || idx >= Capacity)
                {
                    throw new Exception(string.Format("TpMaskWeightRuntimeDataGroup Index out of range: {0}", idx));
                }
                return ref UnsafeUtility.ArrayElementAsRef<TpMaskWeightRuntimeData>(UnsafeUtility.AddressOf(ref this), idx);
            }
        }
        
        
        public DefaultGroup defaultGroup;
        public SpineGroup spineGroup;
        public Spine1Group spine1Group;
        public Spine2Group spine2Group;
        public HeadGroup headGroup;
        public Hand handGroup;

        public static unsafe int FullCapacity => sizeof(TpAoMaskWeightRuntimeDataGroup) / sizeof(TpMaskWeightRuntimeData);

        public unsafe ref TpMaskWeightRuntimeData Get(int idx)
        {
            if (idx < 0 || idx >= FullCapacity)
            {
                throw new Exception(string.Format("TpAoMaskWeightRuntimeDataGroup Index out of range: {0}", idx));
            }
            return ref UnsafeUtility.ArrayElementAsRef<TpMaskWeightRuntimeData>(UnsafeUtility.AddressOf(ref this), idx);
        }
    }
    
    #if SOC_CLIENT
    /// <summary>
    /// 存储配置的序列化数据
    /// </summary>
    public struct TpAniOverrideLayerWeight :IDisposable
    {
        public TpOcAniBoneCurveMask IdleStandWeightCurve;
        public TpOcAniBoneCurveMask IdleCrouchWeightCurve;
        public TpOcAniBoneCurveMask JogStandWeightCurve;
        public TpOcAniBoneCurveMask JogCrouchWeightCurve;
        public TpOcAniBoneCurveMask SprintWeightCurve;
        public TpOcAniBoneCurveMask JumpWeightCurve;
        public TpOcAniBoneCurveMask SwimIdleWeightCurve;
        public TpOcAniBoneCurveMask SwimJogWeightCurve;
        public TpOcAniBoneCurveMask SwimSprintWeightCurve;
        public TpOcAniBoneCurveMask LadderWeightCurve;
        public TpOcAniBoneCurveMask HorseWeightCurve;
        public TpOcAniBoneCurveMask VehicleWeightCurve;

        public void Dispose()
        {
            IdleStandWeightCurve.Dispose();
            IdleCrouchWeightCurve.Dispose();
            JogStandWeightCurve.Dispose();
            JogCrouchWeightCurve.Dispose();
            SprintWeightCurve.Dispose();
            JumpWeightCurve.Dispose();
            SwimIdleWeightCurve.Dispose();
            SwimJogWeightCurve.Dispose();
            SwimSprintWeightCurve.Dispose();
            LadderWeightCurve.Dispose();
            HorseWeightCurve.Dispose();
            VehicleWeightCurve.Dispose();
        }
    }
#endif
    
    
}