using Config;
using Effect.Manager;
using Go.PureGo;
using LobbyFriend;
using System;
using Systems;
using UnityEngine;
using Vehicle;
using WizardGames.Soc.AdaptivePerformance;
using WizardGames.Soc.Bridge;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network.Client;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Profile;
using WizardGames.Soc.Common.Syncronization;
using WizardGames.Soc.Common.TimerWheel;
using WizardGames.Soc.Common.Unity;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Manager;
using WizardGames.Soc.Common.Unity.Monster.State.AimationState.Mgr;
using WizardGames.Soc.SDK;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocClient.ActionExecutor;
using WizardGames.Soc.SocClient.Announcement;
using WizardGames.Soc.SocClient.Audio;
using WizardGames.Soc.SocClient.Auxiliary.AimRange;
using WizardGames.Soc.SocClient.Auxiliary.AimSnap;
using WizardGames.Soc.SocClient.ClientItem;
using WizardGames.Soc.SocClient.ClientLight;
using WizardGames.Soc.SocClient.Control;
using WizardGames.Soc.SocClient.Corpse;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Entity;
using WizardGames.Soc.SocClient.Gathering;
using WizardGames.Soc.SocClient.Go;
using WizardGames.Soc.SocClient.Guide;
using WizardGames.Soc.SocClient.Indicator;
using WizardGames.Soc.SocClient.Map;
using WizardGames.Soc.SocClient.Marker;
using WizardGames.Soc.SocClient.Network;
using WizardGames.Soc.SocClient.PersistentData;
using WizardGames.Soc.SocClient.Plant;
using WizardGames.Soc.SocClient.PlatformPlayerInfo;
using WizardGames.Soc.SocClient.Reputation;
using WizardGames.Soc.SocClient.ServerList;
using WizardGames.Soc.SocClient.Setting;
using WizardGames.Soc.SocClient.Shatter;
using WizardGames.Soc.SocClient.Story;
using WizardGames.Soc.SocClient.Talent;
using WizardGames.Soc.SocClient.Team;
using WizardGames.Soc.SocClient.Technology;
using WizardGames.Soc.SocClient.Terrain;
using WizardGames.Soc.SocClient.Timeline;
using WizardGames.Soc.SocClient.TPNSPush;
using WizardGames.Soc.SocClient.Trigger;
using WizardGames.Soc.SocClient.Ui;
using WizardGames.Soc.SocClient.Ui.FocusInfo;
using WizardGames.Soc.SocClient.Ui.KillDataEvent;
using WizardGames.Soc.SocClient.Ui.UiDecal.Mgr;
using WizardGames.Soc.SocClient.Ui.VehicleInfo;
using WizardGames.Soc.SocClient.Vehcle;
using WizardGames.Soc.SocClient.Voice;
using WizardGames.Soc.SocClient.Water;
using WizardGames.Soc.SocClient.Weapon;
using WizardGames.Soc.SocClient.WorldResource;
using WizardGames.Soc.SocSimulator.Buff;

namespace WizardGames.Soc.SocClient.Manager
{
    /// <summary>
    /// SocWorld中的Mgr管理类， 关联Common中的Mgr管理类
    /// </summary>
    public partial class Mc : McCommon
    {
        /// <summary>
        /// 日志记录器
        /// </summary>
        protected static readonly SocLogger Log = LogHelper.GetLogger(typeof(Mc));

        private static string strLoadTableException = null;
#if POCO
        public static PocoManager PocoMa = null;
#endif
#if UNIT_TEST
        public static AutoUnitTest AutoUnit = null;
#endif

        public static bool ObserverMode
        {
            get;
            private set;
        }
        public static bool HasObserverTarget => ObserverMode && ObserverEntity.Instance.PossessionPlayerId > 0;

        private bool re_RegMgr = false;

        /// <summary>
        /// 是否已经执行了 BeforeEnterWorld
        /// </summary>
        public bool HasDoneBeforeEnterWorld { get; private set; } = false;

        /// <summary>
        /// 获取单例对象
        /// </summary>
        public new static Mc Instance
        {
            get { return (Mc)McCommon.Instance; }
        }

        /// <summary>
        /// 初始化入口
        /// </summary>
        public static void Init()
        {
            var ins = new Mc();
            McCommonUnity.InitAddonManager();
            ins.RegisterAllMgrs();
            ins.CreateMgrInsts();
            ins.CheckTableInitError();
            InitShadowConfig();
        }

        private static void InitShadowConfig()
        {
            if (PlayerPrefs.HasKey(nameof(McCommon.OpenShadow)))
            {
                McCommon.OpenShadow = PlayerPrefs.GetInt(nameof(McCommon.OpenShadow)) == 1;
            }
            else
            {
                McCommon.OpenShadow = false;
                PlayerPrefs.SetInt(nameof(McCommon.OpenShadow), 0);
            }

            Log.InfoFormat("OpenShadow:{0}", McCommon.OpenShadow);
        }
        
        public static void SetObserverMode(bool isOpen)
        {
            if (ObserverMode != isOpen)
            {
                ObserverMode = isOpen;
                Instance.re_RegMgr = true;
                Instance.RegisterAllMgrs();
                Instance.Re_RegisterMgrs();
                Instance.re_RegMgr = false;
            }
           
        }
        
        public void CheckTableInitError()
        {
            // Ui管理器初始化完毕时， 如果有导表错误， 已经可以弹窗提示了
            if (!string.IsNullOrEmpty(strLoadTableException) || MgrTables.SerializeExceptionList.Count > 0)
            {
                string strCombineError = $"{strLoadTableException}\n" ?? "";
                strCombineError += string.Join("\n", MgrTables.SerializeExceptionList);
                MgrTables.SerializeExceptionList.Clear();
                strLoadTableException = null;
                Log.Error(strCombineError);
                UiMsgBox.ShowMsgBox(new()
                {
                    Title = "Tables Load Error",
                    Msg = strCombineError,
                    SetCenter = true,
                    BtnList = new() { new(LanguageManager.GetTextConst(LanguageConst.OK), null) }
                });
            }
        }

        #region all_mgr_variables

        public static MgrEntity Entity;

        // 防止iOS调用泛型接口时报错（可能是查询虚函数表报错），将接口改为实例
        public static INetworkEndpointForClient LocalClient => LocalService.NetworkEndpoint;
        public static SocClientService LocalService;

        public static MgrGamePyhsicsSystem GamePyhsicsSystemMgr;

        public static MgrInputSystem InputSystem;
        public static MgrControlLobby ControlLobby;
        public static MgrControl Control;
        public static MgrRebindKey RebindKey;

        public static MgrMyPlayer MyPlayer;

        public static MgrEntityGo Go;

        // public static MgrMonster Monster;

        public static MgrConfig Config;

        public static MgrDebugConfig DebugConfig;

        public static MgrPredict Predict;

        public static MgrUserCmd UserCmd;

        public static MgrCamera Camera;

        public static MgrConstruction Construction => McCommonUnity.Construction as MgrConstruction;

        public static MgrUi Ui;

        public static MgrPowerOptimization PowerOptimization;
        
        public static MgrHud Hud;

        public static MgrHudCommon HudCommon;

        /// <summary>
        /// 局内/局外实例
        /// 在局内大厅切换时会被清理掉
        /// </summary>
        public static TimerWheel TimerWheel
        {
            get => McCommonUnity.TimerWheel;
            private set => McCommonUnity.TimerWheel = value;
        }

        private static TimerWheel persistentTimerWheel;
        /// <summary>
        /// 持久化TimerWheel
        /// </summary>
        public static TimerWheel TimerWheelPersistent
        {
            get => persistentTimerWheel;
            private set => persistentTimerWheel = value;
        }

        public static MgrAudio Audio;

        public static MgrEffect Effect;

        public static MgrTOD TOD;

        public static MgrPPV PPV;

        public static MgrGathering Gathering;

        public static MgrItem Item;

        public static MgrCollectionItem CollectionItem;

        public static MgrWater Water;

        public static MgrAimSnap AimSnap;

        public static MgrToEntityCalcCache ToEntityCalcCache;

        public static MgrAimRange AimRange;
        // public static MgrWeaponAccessoryGos WeaponAccessoryGos;
        public static MgrShopEntity shopEntityMgr;

        public static MgrRaycast Raycast;

        public static MgrGatherItemPickable GatherItemPickable;
        public static MgrWorldResource WorldResource;

        public static MgrHudMsg HudMsg;
        public static MgrTips MsgTips;
        public static MgrAntiAddiction AntiAddiction;

        public static MgrMsg Msg;

        public static MgrBlueprint Blueprint;
        public static MgrWorldFlag WorldFlag;
        public static MgrQuickLoot QuickLoot;
        public static MgrTechnology Technology;
        public static MgrMap Map;
        public static MgrSocShaderBridge SocShaderBridge;
        public static MgrNetworkMonitor NetWorkMonitor;
        public static MgrBoxShatter BoxShatter;
        public static MgrTerrain Terrain;
        public static MgrGameTeam GameTeam;
        public static MgrMarker Marker;
        public static MgrLootCollection LootCollection;
        public static MgrIOEntity ioEntityMgr;
        public static MgrElevator Elevator;
        public static MgrMonumentEvent MonumentEvent;
        public static MgrKatyusha Katyusha;
        public static MgrGuideLine GuideLine;
        public static MgrDig Dig;
        public static MgrEnhancedDisplayTip EnhancedDisplayTip;
        public static MgrVoice Voice;
        public static MgrIndicator Indicator;
        public static ClientSnapshotReceiver SnapshotReceiver;
        public static MgrChat Chat;
        public static MgrLoginStep LoginStep;
        public static MgrMonsterAnimation MonsterAnimation;
        public static MgrSetting SettingClient;
        public static MgrServerList ServerListClient;
        public static MgrRedDot RedDot;
        public static MgrFullScreenEffect FullScreenEffect;
        public static MgrObjectRenderer ObjectRenderer;
        public static MgrUiModel UiModel;
        public static MgrCorpse Corpse;
        public static MgrLight Light;
        public static MgrPersistentData PersistentData;
        public static MgrPureGo PureGo;
        public static MgrMission Mission;
        public static MgrDeviceLevel DeviceLevel;
        public static MgrWeaponFxController WeaponFxController;
        public static MgrPlant Plant;
        public static MgrElectricLocal ElectricLocal;
        public static MgrMail Mail;
        public static MgrInterestTribe Tribe;
        public static MgrMall Mall;
        public static MgrBattlePass BattlePass;
        // public static MgrDynamicLight DynamicLight;
        public static MgrBuff Buff;
        public static MgrGuide Guide;
        public static MgrClientActionExecutor ActionExecutor;
        public static MgrProfiler Profiler;
        public static MgrAnnouncement Announcement;
        public static MgrVehicle Vehicle;
        public static MgrPermissionCenter PermCenter;
        public static MgrLobby Lobby;
        public static MgrMarquee Marquee;
        public static MgrLobbyTeam LobbyTeam;
        public static MgrRecruit Recruit;
        public static MgrTrigger Trigger;
        public static MgrTrain Train;
        public static MgrLobbyFriend LobbyFriend;
        public static MgrLobbyTempFriend LobbyTempFriend;
        public static MgrLobbyStash LobbyStash;
        public static MgrLobbySkin LobbySkin;
        public static MgrSkinPreview SkinPreview;
        public static MgrUploadLog UploadLog;
        public static MgrSearch Search;
        public static MgrMonumentTask MonumentTask;
        public static MgrRaid Raid;
        public static MgrTreeControl TreeControl;
        public static MgrMonumentPreventBuildings MonumentPreventBuildings;
        public static MgrReputation Reputation;
        public static MgrSignifianceDbgDraw SignifianceDbgDraw;
        public static MgrSignifianceRes SignifianceRes;
        public static MgrPopQueue PopQueue;
        public static MgrTalent Talent;
        public static MgrPlayerBlueprintData PlayerBlueprintData;
        public static MgrDecal Decal;
#if ENABLE_ANTICHEAT
#if STANDALONE_REAL_PC
        public static PCMgrAce Ace;
#else
        public static MgrAce Ace;
#endif
#endif
#if ENABLE_PERF_SIGHT
        public static MgrPerfSight PerfSight;
#endif
        public static MgrSocTpAnim SocTpAnim;
        public static MgrSignifianceReq SignifianceReq;
        //public static MyTriggerSystem LightTrigger;
        public static MgrEntityCareStates EntityCareStates;
        public static MgrReport Report;
        public static MgrSDK SDK;
        public static MgrPay Pay;
        public static MgrAccessoryManagers AccessoryManagers;
        // public static MgrPlayersVisibleCheck PlayersVisibleCheck;
        public static MgrAoiGrid AoiGrid;
        // public static MgrPlayerRes PlayerRes;
        // public static MgrHeldItemAsset HeldItemAsset;
        public static MgrFocusInfo FocusInfo;
        public static MgrKillDataEvent KillDataEvent;
        public static MgrClientTimeline Timeline;
        public static MgrNewbieLevel NewbieLevel;
        public static MgrVehicleInfo VehicleInfo;
        public static MgrPhysicsScene PhysicsScene;
        public static MgrPushNotice PushNotice;
        public static MgrStory Story;
        public static MgrLocalLog LocalLog;
        public static MgrUIConstructionDetailTips ConstructionDetailTips;
        public static MgrWireControl WireControl;
        public static MgrQuickActBubble QuickActBubble;
        public static MgrConstructionPreviewRT ConstructionPreviewRT;
        public static MgrSocMemoryProfiler SocMemory;
        public static MgrConstructionPreview ConstructionPreview;
        public static MgrPreviewSceneManager PreviewSceneManager;
        public static MgrLobbyTime LobbyTime;
        public static MgrClientCondition ConditionMgr;
        public static MgrPlatformPlayerInfo PlatformPlayerInfo;
        public static MgrPhoto Photo;
        public static MgrAdmin Admin;
        public static MgrCenterConsole CenterConsole;
        public static MgrThermal Thermal;
        public static MgrBattleReport BattleReport;
        public static MgrConstructionReport ConstructionReport;
        public static MgrMedal Medal;
        public static MgrActivity Activity;
        public static MgrGacha Gacha;
        #endregion

        /// <summary>
        /// 实例初始化，在这里注册所有的Entity
        /// 需要在上面all_mgr_variables的区域中定义一个静态成员， 成员名字必须是MgrXXX去掉Mgr后的XXX
        /// </summary>
        protected override void RegisterAllMgrs()
        {
            if (!re_RegMgr)
            {
                try
                {
                    Tables = new();
                }
                catch (Exception e)
                {
                    strLoadTableException = e.ToString();
                    Log.Error(strLoadTableException);
                }

                TimerWheel = new TimerWheel("Mc");
                TimerWheelPersistent = new TimerWheel("McPersistent");
                EntityBase.EntityTimerWheel = TimerWheel;
                
                base.RegisterAllMgrs();
                
            }
           


            RegMgr<MgrProfiler>(m => Profiler = m as MgrProfiler, MgrRange.Persistent, re_RegMgr);
#if ENABLE_PERF_SIGHT
            RegMgr<MgrPerfSight>(m => PerfSight = m as MgrPerfSight, MgrRange.Persistent, re_RegMgr);
#endif
            RegMgr<MgrDeviceLevel>(m => DeviceLevel = m as MgrDeviceLevel, MgrRange.Persistent, re_RegMgr);

            // 初始化McCommonUnity中的Mgr
            RegMgr<MgrClientCondition>(m => ConditionMgr = m as MgrClientCondition, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrGamePyhsicsSystem>(m => GamePyhsicsSystemMgr = m as MgrGamePyhsicsSystem, MgrRange.GameOnly, re_RegMgr);
            RegMgr<ClientSnapshotReceiver>(m => SnapshotReceiver = m as ClientSnapshotReceiver, MgrRange.GameOnly, re_RegMgr);
            //McCommonUnity.SnapshotReceiver = SnapshotReceiver;
            RegMgr<MgrSocTpAnim>(m => SocTpAnim = m as MgrSocTpAnim, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrSocShaderBridge>(m => SocShaderBridge = m as MgrSocShaderBridge, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrInputSystem>(m => InputSystem = m as MgrInputSystem, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrControlLobby>(m => ControlLobby = m as MgrControlLobby, MgrRange.LobbyOnly, re_RegMgr);
            RegMgr<MgrControl>(m => Control = m as MgrControl, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrRebindKey>(m => RebindKey = m as MgrRebindKey, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrMyPlayer>(m => MyPlayer = m as MgrMyPlayer, MgrRange.GameOnly, re_RegMgr);
            //RegMgr<MgrMonster>(m => Monster = m as MgrMonster, MgrRange.GameOnly, reReg);
            RegMgr<MgrConfig>(m => Config = m as MgrConfig, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrDebugConfig>(m => DebugConfig = m as MgrDebugConfig, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrPredict>(m => Predict = m as MgrPredict, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrEntity>(m => Entity = m as MgrEntity, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrEntityGo>(m => Go = m as MgrEntityGo, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrMonsterAnimation>(m => MonsterAnimation = m as MgrMonsterAnimation, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrUserCmd>(m => UserCmd = m as MgrUserCmd, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrCamera>(m => Camera = m as MgrCamera, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrConstruction>(m => McCommonUnity.Construction = m as MgrConstruction, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrPlayerBlueprintData>(m => PlayerBlueprintData = m as MgrPlayerBlueprintData, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrRedDot>(m => RedDot = m as MgrRedDot, MgrRange.Persistent, re_RegMgr);   // 红点管理类需要在MgrUI之前
            RegMgr<MgrUi>(m => Ui = m as MgrUi, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrHud>(m => Hud  = m as MgrHud, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrHudCommon>(m => HudCommon  = m as MgrHudCommon, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrFullScreenEffect>(m => FullScreenEffect = m as MgrFullScreenEffect, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrAudio>(m => Audio = m as MgrAudio, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrTOD>(m => TOD = m as MgrTOD, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrPPV>(m => PPV = m as MgrPPV, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrWater>(m => Water = m as MgrWater, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrRaycast>(m => Raycast = m as MgrRaycast, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrAimSnap>(m => AimSnap = m as MgrAimSnap, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrAimRange>(m => AimRange = m as MgrAimRange, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrHudMsg>(m => HudMsg = m as MgrHudMsg, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrTips>(m => MsgTips = m as MgrTips, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrAntiAddiction>(m => AntiAddiction = m as MgrAntiAddiction, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrGathering>(m => Gathering = m as MgrGathering, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrGatherItemPickable>(m => GatherItemPickable = m as MgrGatherItemPickable, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrWorldResource>(m => WorldResource = m as MgrWorldResource, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrMsg>(m => Msg = m as MgrMsg, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrBlueprint>(m => Blueprint = m as MgrBlueprint, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrWorldFlag>(m => WorldFlag = m as MgrWorldFlag, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrQuickLoot>(m => QuickLoot = m as MgrQuickLoot, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrTechnology>(m => Technology = m as MgrTechnology, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrItem>(m => Item = m as MgrItem, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrCollider>(m => Collider = m as MgrCollider, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrMap>(m => Map = m as MgrMap, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrNetworkMonitor>(m => NetWorkMonitor = m as MgrNetworkMonitor, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrBoxShatter>(m => BoxShatter = m as MgrBoxShatter, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrTerrain>(m => Terrain = m as MgrTerrain, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrMarker>(m => Marker = m as MgrMarker, MgrRange.GameOnly, re_RegMgr);
            // RegMgr<MgrWeaponAccessoryGos>(m => WeaponAccessoryGos = m as MgrWeaponAccessoryGos, MgrRange.GameOnly, reReg);
            RegMgr<MgrShopEntity>(m => shopEntityMgr = m as MgrShopEntity, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrLootCollection>(m => LootCollection = m as MgrLootCollection, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrIOEntity>(m => ioEntityMgr = m as MgrIOEntity, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrElevator>(m => Elevator = m as MgrElevator, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrMonumentEvent>(m => MonumentEvent = m as MgrMonumentEvent, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrKatyusha>(m => Katyusha = m as MgrKatyusha, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrGuideLine>(m => GuideLine = m as MgrGuideLine, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrDig>(m => Dig = m as MgrDig, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrEnhancedDisplayTip>(m => EnhancedDisplayTip = m as MgrEnhancedDisplayTip, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrVoice>(m => Voice = m as MgrVoice, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrIndicator>(m => Indicator = m as MgrIndicator, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrChat>(m => Chat = m as MgrChat, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrLoginStep>(m => LoginStep = m as MgrLoginStep, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrObjectRenderer>(m => ObjectRenderer = m as MgrObjectRenderer, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrUiModel>(m => UiModel = m as MgrUiModel, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrCorpse>(m => Corpse = m as MgrCorpse, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrLight>(m => Light = m as MgrLight, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrServerList>(m => ServerListClient = m as MgrServerList, MgrRange.LobbyOnly, re_RegMgr);
            RegMgr<MgrMail>(m => Mail = m as MgrMail, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrInterestTribe>(m => Tribe = m as MgrInterestTribe, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrLobbySkin>(m => LobbySkin = m as MgrLobbySkin, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrSkinPreview>(m => SkinPreview = m as MgrSkinPreview, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrPersistentData>(m => PersistentData = m as MgrPersistentData, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrPlant>(m => Plant = m as MgrPlant, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrMall>(m => Mall = m as MgrMall, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrBattlePass>(m => BattlePass = m as MgrBattlePass, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrPureGo>(m => PureGo = m as MgrPureGo, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrWeaponFxController>(m => WeaponFxController = m as MgrWeaponFxController, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrAccessoryManagers>(m => AccessoryManagers = m as MgrAccessoryManagers, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrElectricLocal>(m => ElectricLocal = m as MgrElectricLocal, MgrRange.GameOnly, re_RegMgr);
            // RegMgr<MgrDynamicLight>(m => DynamicLight = m as MgrDynamicLight, MgrRange.GameOnly, reReg);
            RegMgr<MgrBuff>(m => Buff = m as MgrBuff, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrClientActionExecutor>(m => ActionExecutor = m as MgrClientActionExecutor, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrSetting>(m => SettingClient = m as MgrSetting, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrGuide>(m => Guide = m as MgrGuide, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrAnnouncement>(m => Announcement = m as MgrAnnouncement, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrPermissionCenter>(m => PermCenter = m as MgrPermissionCenter, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrLobby>(m => Lobby = m as MgrLobby, MgrRange.LobbyOnly, re_RegMgr);
            RegMgr<MgrMarquee>(m => Marquee = m as MgrMarquee, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrLobbyTeam>(m => LobbyTeam = m as MgrLobbyTeam, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrRecruit>(m => Recruit = m as MgrRecruit, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrLobbyStash>(m => LobbyStash = m as MgrLobbyStash, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrTrigger>(m => Trigger = m as MgrTrigger, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrTrain>(m => Train = m as MgrTrain, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrLobbyFriend>(m => LobbyFriend = m as MgrLobbyFriend, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrLobbyTempFriend>(m => LobbyTempFriend = m as MgrLobbyTempFriend, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrUploadLog>(m => UploadLog = m as MgrUploadLog, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrPushNotice>(m=> PushNotice = m as MgrPushNotice, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrSearch>(m => Search = m as MgrSearch, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrVehicle>(m => Vehicle = m as MgrVehicle, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrToEntityCalcCache>(m => ToEntityCalcCache = m as MgrToEntityCalcCache, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrMonumentTask>(m => MonumentTask = m as MgrMonumentTask, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrRaid>(m => Raid = m as MgrRaid, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrTreeControl>(m => TreeControl = m as MgrTreeControl, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrMonumentPreventBuildings>(m => MonumentPreventBuildings = m as MgrMonumentPreventBuildings, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrReputation>(m => Reputation = m as MgrReputation, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrSignifianceDbgDraw>(m => SignifianceDbgDraw = m as MgrSignifianceDbgDraw, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrSignifianceRes>(m => SignifianceRes = m as MgrSignifianceRes, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrSignifianceReq>(m => SignifianceReq = m as MgrSignifianceReq, MgrRange.GameOnly, re_RegMgr);
            //RegMgr<MyTriggerSystem>(m => LightTrigger = m as MyTriggerSystem, MgrRange.Persistent, reReg);
            RegMgr<MgrEntityCareStates>(m => EntityCareStates = m as MgrEntityCareStates, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrReport>(m => Report = m as MgrReport, MgrRange.Persistent, re_RegMgr);
            // RegMgr<MgrPlayersVisibleCheck>(m => PlayersVisibleCheck = m as MgrPlayersVisibleCheck, MgrRange.GameOnly, reReg);
            RegMgr<MgrAoiGrid>(m => AoiGrid = m as MgrAoiGrid, MgrRange.GameOnly, re_RegMgr);
            //RegMgr<MgrPlayerRes>(m=>PlayerRes = m as MgrPlayerRes, MgrRange.GameOnly, reReg);
            RegMgr<MgrFocusInfo>(m => FocusInfo = m as MgrFocusInfo, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrKillDataEvent>(m => KillDataEvent = m as MgrKillDataEvent, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrVehicleInfo>(m => VehicleInfo = m as MgrVehicleInfo, MgrRange.GameOnly, re_RegMgr);
            RegMgr<LocationBasedEventManager>(m => LocationBasedEvent = m as LocationBasedEventManager, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrClientTimeline>(m => Timeline = m as MgrClientTimeline, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrNewbieLevel>(m => NewbieLevel = m as MgrNewbieLevel, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrPhysicsScene>(m=> PhysicsScene = m as MgrPhysicsScene, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrLocalLog>(m=> LocalLog = m as MgrLocalLog, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrUIConstructionDetailTips>(m=> ConstructionDetailTips = m as MgrUIConstructionDetailTips, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrPopQueue>(m=> PopQueue = m as MgrPopQueue, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrWireControl>(m=> WireControl = m as MgrWireControl, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrConstructionPreviewRT>(m=> ConstructionPreviewRT = m as MgrConstructionPreviewRT, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrConstructionPreview>(m=> ConstructionPreview = m as MgrConstructionPreview, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrPreviewSceneManager>(m=> PreviewSceneManager = m as MgrPreviewSceneManager, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrLobbyTime>(m=> LobbyTime = m as MgrLobbyTime, MgrRange.Persistent, re_RegMgr);
            // RegMgr<MgrHeldItemAsset>(m=>HeldItemAsset = m as MgrHeldItemAsset, MgrRange.GameOnly, reReg);
            RegMgr<MgrQuickActBubble>(m=> QuickActBubble = m as MgrQuickActBubble, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrSDK>(m => SDK = m as MgrSDK, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrPay>(m => Pay = m as MgrPay, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrMission>(m => Mission = m as MgrMission, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrStory>(m=> Story = m as MgrStory, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrCollectionItem>(m => CollectionItem = m as MgrCollectionItem, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrGameTeam>(m => GameTeam = m as MgrGameTeam, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrEffect>(m => Effect = m as MgrEffect, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrPowerOptimization>(m => PowerOptimization = m as MgrPowerOptimization, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrBattleReport>(m => BattleReport = m as MgrBattleReport, MgrRange.GameOnly, re_RegMgr);
#if ENABLE_ANTICHEAT            
#if STANDALONE_REAL_PC
            // PC平台使用PC特定的实现
            RegMgr<PCMgrAce>(m=> Ace = m as PCMgrAce, MgrRange.Persistent, re_RegMgr);
#else
            // 移动平台使用默认实现
            RegMgr<MgrAce>(m=> Ace = m as MgrAce, MgrRange.Persistent, re_RegMgr);
#endif
#endif
            RegMgr<MgrSocMemoryProfiler>(m => SocMemory = m as MgrSocMemoryProfiler, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrPlatformPlayerInfo>(m => PlatformPlayerInfo = m as MgrPlatformPlayerInfo, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrPhoto>(m => Photo = m as MgrPhoto, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrAdmin>(m => Admin = m as MgrAdmin, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrCenterConsole>(m => CenterConsole = m as MgrCenterConsole, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrThermal>(m => Thermal = m as MgrThermal, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrTalent>(m => Talent = m as MgrTalent, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrDecal>(m => Decal = m as MgrDecal, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrConstructionReport>(m => ConstructionReport = m as MgrConstructionReport, MgrRange.GameOnly, re_RegMgr);
            RegMgr<MgrMedal>(m => Medal = m as MgrMedal, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrActivity>(m => Activity = m as MgrActivity, MgrRange.Persistent, re_RegMgr);
            RegMgr<MgrGacha>(m => Gacha = m as MgrGacha, MgrRange.Persistent, re_RegMgr);
            

            RegisterPlatformMgrs();
        }

        protected void ObserverRegisterAllMgrs()
        {
            RegMgr<MgrProfiler>(m => Profiler = m as MgrProfiler, MgrRange.Persistent, true);
            RegMgr<MgrConfig>(m => Config = m as MgrConfig, MgrRange.Persistent, true);
            RegMgr<MgrRedDot>(m => RedDot = m as MgrRedDot, MgrRange.Persistent, true);   // 红点管理类需要在MgrUI之前
            RegMgr<MgrUi>(m => Ui = m as MgrUi, MgrRange.Persistent, true);
            RegMgr<MgrHud>(m => Hud = m as MgrHud, MgrRange.GameOnly, true);
            RegMgr<MgrHudCommon>(m => HudCommon = m as MgrHudCommon, MgrRange.GameOnly, true);
            RegMgr<MgrNetworkMonitor>(m => NetWorkMonitor = m as MgrNetworkMonitor, MgrRange.Persistent, true);
            RegMgr<MgrLoginStep>(m => LoginStep = m as MgrLoginStep, MgrRange.Persistent, true);
            RegMgr<MgrObjectRenderer>(m => ObjectRenderer = m as MgrObjectRenderer, MgrRange.Persistent, true);
            RegMgr<MgrUiModel>(m => UiModel = m as MgrUiModel, MgrRange.Persistent, true);
            RegMgr<MgrServerList>(m => ServerListClient = m as MgrServerList, MgrRange.LobbyOnly, true);
            RegMgr<MgrLobbySkin>(m => LobbySkin = m as MgrLobbySkin, MgrRange.Persistent, true);
            RegMgr<MgrSkinPreview>(m => SkinPreview = m as MgrSkinPreview, MgrRange.Persistent, true);
            RegMgr<MgrPersistentData>(m => PersistentData = m as MgrPersistentData, MgrRange.Persistent, true);
            RegMgr<MgrDeviceLevel>(m => DeviceLevel = m as MgrDeviceLevel, MgrRange.Persistent, true);
            RegMgr<MgrSetting>(m => SettingClient = m as MgrSetting, MgrRange.Persistent, true);
            RegMgr<MgrAnnouncement>(m => Announcement = m as MgrAnnouncement, MgrRange.Persistent, true);
            RegMgr<MgrLobby>(m => Lobby = m as MgrLobby, MgrRange.LobbyOnly, true);
            RegMgr<MgrSDK>(m => SDK = m as MgrSDK, MgrRange.Persistent, true);
            RegMgr<MgrPay>(m => Pay = m as MgrPay, MgrRange.Persistent, true);
            RegMgr<MgrMsg>(m => Msg = m as MgrMsg, MgrRange.Persistent, true);
            RegMgr<MgrAudio>(m => Audio = m as MgrAudio, MgrRange.Persistent, true);
            RegMgr<MgrLobbyTeam>(m => LobbyTeam = m as MgrLobbyTeam, MgrRange.Persistent, true);
            RegMgr<MgrRecruit>(m => Recruit = m as MgrRecruit, MgrRange.Persistent, true);
            RegMgr<MgrLobbyFriend>(m => LobbyFriend = m as MgrLobbyFriend, MgrRange.Persistent, true);
            RegMgr<MgrLobbyTempFriend>(m => LobbyTempFriend = m as MgrLobbyTempFriend, MgrRange.Persistent, true);
            RegMgr<MgrLobbyStash>(m => LobbyStash = m as MgrLobbyStash, MgrRange.Persistent, true);
            RegMgr<MgrMail>(m => Mail = m as MgrMail, MgrRange.Persistent, true);
            RegMgr<MgrInterestTribe>(m => Tribe = m as MgrInterestTribe, MgrRange.Persistent, true);
            RegMgr<MgrChat>(m => Chat = m as MgrChat, MgrRange.Persistent, true);
            RegMgr<MgrGuide>(m => Guide = m as MgrGuide, MgrRange.Persistent, true);
            RegMgr<MgrVoice>(m => Voice = m as MgrVoice, MgrRange.Persistent, true);
            RegMgr<MgrTips>(m => MsgTips = m as MgrTips, MgrRange.Persistent, true);
            RegMgr<MgrFullScreenEffect>(m => FullScreenEffect = m as MgrFullScreenEffect, MgrRange.GameOnly, true);
            RegMgr<MgrTrigger>(m => Trigger = m as MgrTrigger, MgrRange.Persistent, true);
            RegMgr<MgrReport>(m => Report = m as MgrReport, MgrRange.Persistent, true);

            RegMgr<ClientSnapshotReceiver>(m => SnapshotReceiver = m as ClientSnapshotReceiver, MgrRange.GameOnly, true);
            RegMgr<MgrSocShaderBridge>(m => SocShaderBridge = m as MgrSocShaderBridge, MgrRange.GameOnly, true);
            RegMgr<MgrInputSystem>(m => InputSystem = m as MgrInputSystem, MgrRange.LobbyOnly, true);
            RegMgr<MgrControlLobby>(m => ControlLobby = m as MgrControlLobby, MgrRange.Persistent, true);
            RegMgr<MgrControl>(m => Control = m as MgrControl, MgrRange.GameOnly, true);
            RegMgr<MgrMyPlayer>(m => MyPlayer = m as MgrMyPlayer, MgrRange.GameOnly, true);
            RegMgr<MgrDebugConfig>(m => DebugConfig = m as MgrDebugConfig, MgrRange.GameOnly, true);
            RegMgr<MgrEntity>(m => Entity = m as MgrEntity, MgrRange.GameOnly, true);
            RegMgr<MgrEntityGo>(m => Go = m as MgrEntityGo, MgrRange.GameOnly, true);
            RegMgr<MgrUserCmd>(m => UserCmd = m as MgrUserCmd, MgrRange.GameOnly, true);
            RegMgr<MgrCamera>(m => Camera = m as MgrCamera, MgrRange.GameOnly, true);
            RegMgr<MgrConstruction>(m => McCommonUnity.Construction = m as MgrConstruction, MgrRange.GameOnly, true);
            RegMgr<MgrWater>(m => Water = m as MgrWater, MgrRange.GameOnly, true);
            RegMgr<MgrRaycast>(m => Raycast = m as MgrRaycast, MgrRange.GameOnly, true);
            RegMgr<MgrMap>(m => Map = m as MgrMap, MgrRange.GameOnly, true);
            RegMgr<MgrTerrain>(m => Terrain = m as MgrTerrain, MgrRange.GameOnly, true);
            RegMgr<MgrIndicator>(m => Indicator = m as MgrIndicator, MgrRange.GameOnly, true);
            RegMgr<MgrPermissionCenter>(m => PermCenter = m as MgrPermissionCenter, MgrRange.GameOnly, true);
            RegMgr<MgrAoiGrid>(m => AoiGrid = m as MgrAoiGrid, MgrRange.GameOnly, true);
            RegMgr<MgrMarker>(m => Marker = m as MgrMarker, MgrRange.GameOnly, true);
            RegMgr<MgrPhysicsScene>(m => PhysicsScene = m as MgrPhysicsScene, MgrRange.GameOnly, true);
            RegMgr<MgrCollider>(m => Collider = m as MgrCollider, MgrRange.GameOnly, true);
            RegMgr<MgrHudMsg>(m => HudMsg = m as MgrHudMsg, MgrRange.GameOnly, true);
            RegMgr<MgrSignifianceDbgDraw>(m => SignifianceDbgDraw = m as MgrSignifianceDbgDraw, MgrRange.GameOnly, true);
            RegMgr<MgrSignifianceRes>(m => SignifianceRes = m as MgrSignifianceRes, MgrRange.GameOnly, true);
            RegMgr<MgrSignifianceReq>(m => SignifianceReq = m as MgrSignifianceReq, MgrRange.GameOnly, true);
            RegMgr<MgrTreeControl>(m => TreeControl = m as MgrTreeControl, MgrRange.GameOnly, true);
            RegMgr<MgrEffect>(m => Effect = m as MgrEffect, MgrRange.GameOnly, true);
            RegMgr<MgrLight>(m => Light = m as MgrLight, MgrRange.GameOnly, true);
            RegMgr<MgrEntityCareStates>(m => EntityCareStates = m as MgrEntityCareStates, MgrRange.GameOnly, true);
            RegMgr<MgrFocusInfo>(m => FocusInfo = m as MgrFocusInfo, MgrRange.GameOnly, true);
            RegMgr<MgrWireControl>(m => WireControl = m as MgrWireControl, MgrRange.GameOnly, true);
            RegMgr<MgrConstructionPreviewRT>(m => ConstructionPreviewRT = m as MgrConstructionPreviewRT, MgrRange.GameOnly, true);
            RegMgr<MgrConstructionPreview>(m => ConstructionPreview = m as MgrConstructionPreview, MgrRange.GameOnly, true);
            RegMgr<MgrPreviewSceneManager>(m => PreviewSceneManager = m as MgrPreviewSceneManager, MgrRange.Persistent, true);
            RegMgr<MgrPlant>(m => Plant = m as MgrPlant, MgrRange.GameOnly, true);
            RegMgr<MgrGamePyhsicsSystem>(m => GamePyhsicsSystemMgr = m as MgrGamePyhsicsSystem, MgrRange.GameOnly, true);
            RegMgr<MgrPlatformPlayerInfo>(m => PlatformPlayerInfo = m as MgrPlatformPlayerInfo, MgrRange.Persistent, true);
            RegMgr<MgrPhoto>(m => Photo = m as MgrPhoto, MgrRange.GameOnly, true);
            RegMgr<MgrAdmin>(m => Admin = m as MgrAdmin, MgrRange.GameOnly, true);
            RegMgr<MgrThermal>(m => Thermal = m as MgrThermal, MgrRange.Persistent, true);
            RegMgr<MgrPowerOptimization>(m => PowerOptimization = m as MgrPowerOptimization, MgrRange.Persistent, true);
            RegMgr<MgrBattleReport>(m => BattleReport = m as MgrBattleReport, MgrRange.GameOnly, true);
            RegMgr<MgrConstructionReport>(m => ConstructionReport = m as MgrConstructionReport, MgrRange.GameOnly, true);
            RegMgr<MgrMedal>(m => Medal = m as MgrMedal, MgrRange.Persistent, true);
        
            RegMgr<MgrActivity>(m => Activity = m as MgrActivity, MgrRange.Persistent, true);
        }

        /// <summary>
        /// 创建Manager实例
        /// </summary>
        private void CreateMgrInsts()
        {
            TimerWheel.Start();
            TimerWheelPersistent.Start();
            InitMgrByRange(MgrRange.Persistent | MgrRange.LobbyOnly);
        }

        /// <summary>
        /// 进入世界前, 清理大厅的Manager, 创建局内的Manager
        /// </summary>
        /// <returns></returns>
        public override void BeforeEnterWorld()
        {
            if (HasDoneBeforeEnterWorld) return;
            try
            {
                // 标记从现在开始增加的事件都算局内事件, 出局内时会统一清理
                Mc.Msg.MarkTag(EventTag.Game);
                Mc.Msg.RecordMsgCount("BeforeEnterWorld");
                Mc.LoginStep.StepLog("[Mc] Init PlayerLogicBlockData");
                // 销毁仅大厅的Manager, 创建局内的Manager
                Mc.LoginStep.StepLog("[Mc] Clear Lobby Mgrs And Create Game Mgrs");
                ClearMgrByRange(MgrRange.LobbyOnly, true);
                Mc.Tables.ClearSceneOnlyDataUpdateEvent();
                ResetTimer();
                InitMgrByRange(MgrRange.GameOnly);
                
                // 创建局内红点
                Mc.RedDot?.SwitchRedDotToGame();
            }
            catch (Exception e)
            {
                Log.ErrorFormat("Error in BeforeEnterWorld: {0}", e);
            }
            finally
            {
                HasDoneBeforeEnterWorld = true;
            }
        }

        /// <summary>
        /// 退出游戏时, 清理局内的Manager, 创建Lobby的Manager
        /// </summary>
        public override void AfterExitWorld()
        {
            if (!HasDoneBeforeEnterWorld) return;
            try
            {
                // 清理局内的事件
                Mc.Msg.ClearEventsOfTag(EventTag.Game);
                Mc.Msg.MarkTag(EventTag.Lobby);
                
                //清理MgrMyPlayer外部引用
                PlayerLogicJobModel.ClearCmdStateList();
                
                // 销毁局内的Manager， 创建大厅的Manager
                Mc.LoginStep.StepLog("[Mc] Clear Game Mgrs And Create Lobby Mgrs");
                // 局内Manager在退出场景流程中执行了CleanUp, 这里不需要再执行
                ClearMgrByRange(MgrRange.GameOnly, false);
                Mc.Tables.ClearSceneOnlyDataUpdateEvent();
                InitMgrByRange(MgrRange.LobbyOnly);

                // 创建大厅红点
                Mc.RedDot?.SwitchRedDotToLobby();
                Msg.RecordMsgCount("AfterExitGame");

                // 保存Shader变体
                ShaderVariantManager.SocShaderVariantManager.SaveAndUploadCompiledVariants();
#if !PUBLISH
                AudioEventUsageTracker.SaveAndUploadUsedEvents();
#endif
            }
            catch (Exception e)
            {
                Log.ErrorFormat("Error in AfterExitWorld: {0}", e);
            }
            finally
            {
                HasDoneBeforeEnterWorld = false;
            }
        }

        public void ClearAllControllers()
        {
            ActionStateController.Instance.Clear();
            //ActionFireStateController.Instance.Clear();
            //ActionStateReloadController.Instance.Clear();
            AdsStateController.Instance.Clear();
            CharacterStateController.Instance.Clear();
            MoveJumpStateController.Instance.Clear();
            MoveLadderStateController.Instance.Clear();
            MoveStateController.Instance.Clear();
            PassiveStateController.Instance.Clear();
            PoseDyingStateController.Instance.Clear();
            PoseStateController.Instance.Clear();
            UnAliveStateController.Instance.Clear();
            // ActionHoldStateController.Instance.Clear();
        }
        /// <summary>
        /// 大厅/局内切换时,清理非持久化Timer
        /// </summary>
        public void ResetTimer()
        {
            Log.Info("Mc ClearGameTimer");
            Mc.TimerWheel.CancelAllTimer();
            Mc.TimerWheel = new TimerWheel("Mc");
            Mc.TimerWheel.Start();
            EntityBase.EntityTimerWheel = Mc.TimerWheel;

            Mc.Msg.FireMsgAtOnce(EventDefine.TimerReset); //临时做法，等release后再改正式做法
        }
    }
}