﻿using System.Collections.Generic;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.RuleGraph;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.SocWorld.Event;

namespace WizardGames.Soc.SocWorld.RuleGraph
{
    [HotfixClass]
    public static partial class RuleGraphHotfix
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(RuleGraph));

        private static void InitEventNode(this RuleGraph self)
        {
            List<int> events = self.Context.GraphCfg.EventList;
            foreach (int i in events)
            {
                //
                if (self.eventNodeDic.ContainsKey(i))
                {
                    continue;
                }
                
                EventCfg e = self.Context.GraphCfg.GetNodeCfg<EventCfg>(i);
                EventNode eventNode = MgrGraphNode.Instance.GetNode(e.GetClassHash()) as EventNode;

                if (eventNode == null)
                {
                    logger.Error($"[RuleGraph] Event node not found, hash={e.GetClassHash()}");
                    continue;
                }
                
                self.eventNodeDic.Add(e.Id, eventNode);
                int eventId = eventNode.GetEventId();
                if (!self.eventIds.Contains(eventId))
                {
                    eventNode.OnRegisterEventCallback(self.Context);
                    self.eventIds.Add(eventId);
                }
            }
        }
        
        #region life
        [Hotfix]
        public static void OnStart(this RuleGraph self)
        {
            BaseOnStart.Invoke(self);
            
            self.InitEventNode();
        }

        [Hotfix]
        public static void OnStop(this RuleGraph self)
        {
            foreach (KeyValuePair<int, EventNode> kv in self.eventNodeDic)
            {
                kv.Value.UnListenEvent(self.Context);
            }
            
            // 回收
            foreach (KeyValuePair<int, EventNode> kv in self.eventNodeDic)
            {
                MgrGraphNode.Instance.Recycle(kv.Value.GetClassHash(), kv.Value);
            }
            self.eventNodeDic.Clear();

            self.Context.Clear();    
            self.Context = null;
#if DEBUG   
            self.DebugInfo = null;
#endif

            BaseOnStop.Invoke(self);
        }
        #endregion
        
        [Hotfix]
        public static void OnEvent(this RuleGraph self, int eventId, object e)
        {
            foreach (KeyValuePair<int, EventNode> kv in self.eventNodeDic)
            {
                if (kv.Value.GetEventId() == eventId)
                {
                    if (kv.Value is GraphCustomEventNode a)
                    {
                        EventCfg cfg = self.Context.GraphCfg.GetNodeCfg<EventCfg>(kv.Key);
                        GraphCustomEventCfg nodeCfg = cfg as GraphCustomEventCfg;
                        GraphCustomEvent eventData = e as GraphCustomEvent;
                        if (nodeCfg.EventName != eventData.eventName)
                        {
                            continue;
                        }
                    }
                    
                    logger.Info($"[RuleGraph] OnEvent : {kv.Value.GetType().Name}");

                    int id = GraphUtils.GenId($"EventNode_{kv.Value.Id}_FlowContext");
                    FlowContext context = new(id, self.Context);
                    context.EventData = e;
                    context.EventId = eventId;
                    // 开始
                    EventFlow.StartExecute(context, kv.Key, 0);
                    
                    logger.Info($"[RuleGraph] OnEvent FlowFinish : {kv.Value.GetType().Name}");
                }
            }
        }

        public static void Hotfix(this RuleGraph self, RuleGraphCfg cfg)
        {
            self.Context.GraphCfg = cfg;
            
            // 更新事件节点 TODO　后续要改，还在其他分支
            self.InitEventNode();
        }
    }
}