﻿using System.Collections.Generic;
using System.Linq;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Const;
using WizardGames.Soc.Common.Data.blueprintData;
using WizardGames.Soc.Common.Data.constraction;
using WizardGames.Soc.Common.Data.DataItem;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Game.NodeSystem;

#if SOC_WORLD
using WizardGames.Soc.SocWorld.Framework;
using WizardGames.Soc.SocWorld.NodeSystem;

#endif
using WizardGames.SocConst.Soc.Const;
using Mathf = WizardGames.Soc.Common.Algorithm.Mathf;

namespace WizardGames.Soc.Common.Construction
{
    public class ConstructionConsumeUtils
    {
        private static readonly SocLogger logger = LogHelper.GetLogger(typeof(ConstructionConsumeUtils));
        public static void MergeConsume(IDictionary<long, int> costTotal, IDictionary<long, int> consume)
        {
            foreach (var (itemId, itemCount) in consume)
            {
                if (itemCount <= 0)
                    continue;
                if (costTotal.ContainsKey(itemId))
                {
                    costTotal[itemId] += itemCount;
                }
                else
                {
                    costTotal[itemId] = itemCount;
                }
            }
        }

        public static void MergeConsume(IDictionary<long, int> costTotal, ItemCostAmount consume)
        {
            if (consume.amount <= 0)
            {
                return;
            }

            if (costTotal.ContainsKey(consume.templateId))
            {
                costTotal[consume.templateId] += (int)consume.amount;
            }
            else
            {
                costTotal[consume.templateId] = (int)consume.amount;
            }
        }

        public static void MergeConsume(IDictionary<long, float> costTotal, IDictionary<long, float> consume)
        {
            foreach (var (itemId, itemCount) in consume)
            {
                if (itemCount <= 0)
                    continue;
                if (costTotal.ContainsKey(itemId))
                {
                    costTotal[itemId] += itemCount;
                }
                else
                {
                    costTotal[itemId] = itemCount;
                }
            }
        }

        public static void RemoveConsume(IDictionary<long, int> costTotal, IDictionary<long, int> consume)
        {
            foreach (var (itemId, itemCount) in consume)
            {
                if (itemCount <= 0)
                    continue;
                if (costTotal.ContainsKey(itemId))
                {
                    if (costTotal[itemId] <= itemCount)
                    {
                        costTotal[itemId] = 0;
                        costTotal.Remove(itemId);
                    }
                    else
                    {
                        costTotal[itemId] -= itemCount;
                    }
                }
            }
        }

        public static void CopyConsume(IDictionary<long, int> origin, IDictionary<long, int> target)
        {
            target.Clear();
            if (origin == null) { return; }
            foreach (var (itemId, itemCount) in origin)
            {
                target[itemId] = itemCount;
            }
        }

        /// <summary>
        /// 计算单次修理血量
        /// 最大单次修理HP=max(50, 建筑最大HP * repairPerTime)
        /// </summary>
        /// <param name="partEntity"></param>
        /// <returns></returns>
        public static bool CalcRepairHP(PartEntity partEntity, out float healthMissing, out float healthMissingFraction)
        {
            healthMissing = 0f;
            healthMissingFraction = 0f;
            if (!partEntity.TryGetIHitable(out IHitableEntity hitable)) return false;

            if (!FloatUtil.IsGreater(hitable.MaxHp, 0f))
            {
                logger.ErrorFormat("CalcRepairHP error, max hp error. {0} {1} {2}", partEntity.EntityId, partEntity.TemplateId, hitable.MaxHp);
                return false;
            }

            // 单次血量上限
            var maxRepairHp = MaxHealthHp(partEntity);
            // 本次可修理血量
            healthMissing = Mathf.Min(hitable.MaxHp - hitable.Hp, maxRepairHp);
            healthMissingFraction = healthMissing / hitable.MaxHp;
            return true;
        }

        /// <summary>
        /// 单次最大修复血量
        /// </summary>
        /// <param name="partEntity"></param>
        /// <returns></returns>
        public static float MaxHealthHp(PartEntity partEntity)
        {
            var buildingCore = McCommon.Tables.TbBuildingCore.GetOrDefault(partEntity.TemplateId);
            if (buildingCore == null)
            {
                logger.ErrorFormat("MaxHealthHp error, no config {0}", partEntity.TemplateId);
                return ConstructionConst.MAX_HEALTH_REPAIR;
            }
            if (!partEntity.TryGetIHitable(out IHitableEntity hitable)) return 0;
            var maxRepairHp = Mathf.Max(ConstructionConst.MAX_HEALTH_REPAIR, hitable.MaxHp * buildingCore.RepairPerTime);
            return maxRepairHp;
        }



        public static Dictionary<long, int> GetRepairConsume(float maxHp, float hp, long partType, long grade)
        {
            if (maxHp <= 0.0f)
                return null;
            if (Mathf.Abs(maxHp - hp) <= 1e-6)
            {
                return null;
            }
            var healthMissing = maxHp - hp;
            var healthMissingFraction = healthMissing / maxHp;

            var consume = GetRepairConsume(healthMissingFraction, partType, grade);
            return consume;
        }

        /// <summary>
        /// 计算残血修理消耗
        /// </summary>
        /// <param name="healthMissingFraction"></param>
        /// <param name="partType"></param>
        /// <param name="grade"></param>
        /// <returns></returns>
        public static Dictionary<long, int> GetRepairConsume(float healthMissingFraction, long partType, long grade)
        {
            Dictionary<long, int> costData = new();
            if (!FloatUtil.IsGreater(healthMissingFraction, 0f))
            {
                return costData;
            }
            var repairFactor = McCommon.Tables.TbConstructionConstantConfig.RepairCoeff;
            var material = GetRepairMaterial(partType, grade);
            foreach (var itemId in material.Keys)
            {
                var itemCount = material[itemId];
                var amount = Mathf.RoundToInt(itemCount * healthMissingFraction * repairFactor);
                if (amount <= 0)
                    continue;
                costData[itemId] = amount;
            }
            return costData;
        }

        /// <summary>
        /// 获得修复材料，没有任何折扣
        /// </summary>
        /// <param name="partType"></param>
        /// <param name="grade"></param>
        /// <returns></returns>
        public static Dictionary<long, float> GetRepairMaterial(long partType, long grade)
        {
            Dictionary<long, float> costData = new();
            var itemCost = GetConstructionBuildCost(partType, (int)grade);
            for (int i = 0; i < itemCost.Count; i++)
            {
                var resource = itemCost[i];

                var itemConfig = McCommon.Tables.TbItemConfig.GetOrDefault(resource.templateId);
                if (itemConfig == null)
                {
                    logger.ErrorFormat("GetRepairConsume metrial item {0} for {1} has no item config", resource.templateId, partType);
                    continue;
                }
                var materialsTempalteId = resource.templateId;
                var materialsTempalteIdAmount = resource.amount;
                if (itemConfig.Itemflags.Contains(ItemFlags.IsComponents))
                {
                    // 若该材料的道具有 111(Rust组件)标签，则取该组件的蓝图的第一个材料，并乘以组件数量加入列表
                    var blueprint = ItemHelper.GetBlueprintConfigByItemId(resource.templateId);
                    if (blueprint == null)
                    {
                        logger.ErrorFormat("GetRepairConsume metrial item {0} for {1} has no blueprint config", resource.templateId, partType);
                        continue;
                    }
                    if (blueprint.Ingredients == null || blueprint.IngredientsNum == null)
                    {
                        logger.ErrorFormat("GetRepairConsume metrial item {0} blueprint config error", resource.templateId);
                        continue;
                    }
                    if (blueprint.Ingredients.Length == 0 || blueprint.IngredientsNum.Length == 0)
                    {
                        logger.ErrorFormat("GetRepairConsume metrial item {0} blueprint config no materials", resource.templateId);
                        continue;
                    }
                    materialsTempalteId = blueprint.Ingredients[0];
                    materialsTempalteIdAmount = resource.amount * blueprint.IngredientsNum[0];
                }
                if (materialsTempalteIdAmount <= 0)
                    continue;
                if (costData.ContainsKey(materialsTempalteId))
                {
                    costData[materialsTempalteId] += materialsTempalteIdAmount;
                }
                else
                {
                    costData[materialsTempalteId] = materialsTempalteIdAmount;
                }
            }

            return costData;
        }
        public static List<ItemCostAmount> RepairCost(float healthMissingFraction, long partType, int grade)
        {
            var costData = GetRepairConsume(healthMissingFraction, partType, grade);
            var itemsToTake = new List<ItemCostAmount>();
            foreach (var (templateId, count) in costData)
            {
                itemsToTake.Add(new(templateId, count));
            }
            return itemsToTake;
        }
        /// <summary>
        /// 获取建造消耗的材料列表， 仅基础造物
        /// </summary>
        /// <param name="partId"></param>
        /// <param name="grade"></param>
        /// <returns></returns>
        public static Dictionary<long, int> GetBuildConsume(long partType, long grade)
        {
            var itemCost = new Dictionary<long, int>();
            GetBuildConsume(partType, grade, itemCost);
            return itemCost;
        }

        public static void GetBuildConsume(long partType, long grade, Dictionary<long, int> itemCost)
        {
            if (itemCost == null)
            {
                logger.Error("GetBuildConsume itemCost is null");
                return;
            }
            var cClass = McCommon.Tables.TbConstractionGrade.GetOrDefault(grade);
            if (cClass == null) return;
            var itemId = cClass.ItemId;
            var itemCount = cClass.Count;

            var buildCore = McCommon.Tables.TbBuildingCore.GetOrDefault(partType);
            if (buildCore == null) return;
            var factor = buildCore.CostMultipler;
            int finalCost = Mathf.CeilToInt(itemCount * factor);
            if (itemCost.ContainsKey(itemId))
            {
                finalCost += itemCost[itemId];
                itemCost[itemId] = finalCost;
            }
            else
            {
                itemCost.Add(itemId, finalCost);
            }
        }
#if SOC_WORLD
        public static bool TryConsumeBlueprint(ulong roleId, long blueprintId, Dictionary<long, int[]> constructionSummaryData)
        {
            var totalCost = GetSummaryConstructionCost(constructionSummaryData);
            logger.InfoFormat("TryConsumeBlueprint roleId {0} totalCost: {1}", roleId, totalCost.Count);
            //检查当前玩家是否拥有足够的材料
            if (!IsCostEnough(roleId, totalCost))
            {
                return false;
            }

            //消耗材料
            var playerEntity = EntityManager.Instance.GetPlayerEntityByRoleId(roleId);
            if (playerEntity == null)
            {
                logger.ErrorFormat("TryConsumeBlueprint failed, can't find player entity by role id {0}", roleId);
                return false;
            }
            //
            if (!TryConsumeItems(playerEntity, totalCost, OpContextConst.CONSTRUCTION_SET, blueprintId, out var failItemId))
            {
                logger.ErrorFormat("TryConsumeBlueprint failed, consume items failed, roleId {0}, failItemId {1}", roleId, failItemId);
                return false;
            }
            return true;
        }

        public static bool TryConsumeItems(PlayerEntity player, IDictionary<long, int> data, string source, long partType, out long failItemId)
        {
            failItemId = 0;
            if (data == null) return false;
            var root = player.Root;
            using var ctx = NodeOpContext.GetNew(source, partType).SetOpRoleId(player.RoleId);
            using var transaction = EntityTransaction.Start($"ConstructionConsumeItems-{player.RoleId}-{data.Print()}");

            foreach (var (itemId, count) in data)
            {
                var ret = root.RequireNode(new NodeOpByBizId(itemId, count));
                if (ret != EOpCode.Success)
                {
                    failItemId = itemId;
                    transaction.Rollback("construction consume fail", false);
                    return false;
                }
            }

            transaction.Commit();
            return true;
        }
#endif



        public static Dictionary<long, int> GetSummaryConstructionCost(Dictionary<long, int[]> constructionSummaryData)
        {
            var totalCost = new Dictionary<long, int>();
            foreach (var kvp in constructionSummaryData)
            {
                for (int i = 1; i < kvp.Value.Length; i++)
                {
                    var grade = i;
                    var num = kvp.Value[i];
                    if (num > 0)
                    {
                        var cost = GetConstructionBuildCost(kvp.Key, grade);
                        foreach (var itemCostAmount in cost)
                        {
                            itemCostAmount.amount *= num;
                            MergeConsume(totalCost, itemCostAmount);
                        }
                    }
                }
            }

            return totalCost;
        }
#if SOC_WORLD
        public static bool CheckConsumeEnough(ulong roleId, Dictionary<long, int[]> constructionSummaryData)
        {
            var totalCost = GetSummaryConstructionCost(constructionSummaryData);
            logger.InfoFormat("CheckConsumeEnough roleId {0} totalCost: {1}", roleId, totalCost.Count);
            //检查当前玩家是否拥有足够的材料
            return IsCostEnough(roleId, totalCost);
        }
        public static bool IsCostEnough(ulong roleId, Dictionary<long, int> cost)
        {
            var playerEntity = EntityManager.Instance.GetPlayerEntityByRoleId(roleId);
            if (playerEntity == null)
            {
                return false;
            }

            foreach (var (id, count) in cost)
            {
                var hasCount = playerEntity.Root.GetNodeValByPath(NodePathConst.VPlayerInventory, id);
                if (hasCount < count)
                {
                    return false;
                }
            }

            return true;
        }

#endif



        public static void GetBuildConsume(List<ConstructionBlueprintSaveData> blueprintSaveData, Dictionary<long, int> itemCost)
        {
            if (itemCost == null)
            {
                logger.Error("GetBuildConsume itemCost is null");
                return;
            }
            itemCost.Clear();
            foreach (ConstructionBlueprintSaveData constructionBlueprintSaveData in blueprintSaveData)
            {
                var itemBuildCost = GetConstructionBuildCost(constructionBlueprintSaveData.TemplateId, constructionBlueprintSaveData.Grade);
                //GetBuildConsume(constructionBlueprintSaveData.TemplateId, constructionBlueprintSaveData.Grade, itemCost);
                foreach (ItemCostAmount itemCostAmount in itemBuildCost)
                {
                    if (itemCost.ContainsKey(itemCostAmount.templateId))
                    {
                        itemCost[itemCostAmount.templateId] += Mathf.CeilToInt(itemCostAmount.amount);
                    }
                    else
                    {
                        itemCost.Add(itemCostAmount.templateId, Mathf.CeilToInt(itemCostAmount.amount));
                    }
                }
            }

        }

        /// <summary>
        /// 获取建造消耗的内容
        /// </summary>
        /// <param name="partTemplateId"></param>
        /// <param name="grade"></param>
        /// <returns></returns>
        public static List<ItemCostAmount> GetConstructionBuildCost(long partTemplateId, int grade = 0)
        {   //如果是核心建筑，则直接获取最低等级所需要的材料
            var list = new List<ItemCostAmount>();
            var config = McCommon.Tables.TbBuildingCore.GetOrDefault(partTemplateId);
            if (config == null)
            {
                logger.ErrorFormat("get construction build cost failed, can't find config of {0}", partTemplateId);
                return list;
            }


            if (config.Type == (int)ConstructionPartType.Core)
            {
                var cost = GetBuildConsume(partTemplateId, grade);
                foreach (var (itemId, count) in cost)
                {
                    var itemCost = new ItemCostAmount(itemId, count);
                    list.Add(itemCost);
                }
                return list;
            }

            Blueprint blueprint = null;
            //如果是非核心建筑，则直接读蓝图表
            //需要判断是不是组合建筑的子建筑，如果是,需要读另一张表
            if (config.IsComboPart == 1)
            {
                blueprint = ItemHelper.GetBlueprintConfigByBuildCoreId(partTemplateId);
            }
            else
            {
                blueprint = ItemHelper.GetBlueprintConfigByItemId(partTemplateId);
            }

            if (blueprint == null)
            {
                logger.ErrorFormat("get construction build cost failed, can't find blueprint of {0}", partTemplateId);
                return list;
            }

            var normalBuildingConfig = McCommon.Tables.TbBlueprint.GetOrDefault(blueprint.Id);
            if (normalBuildingConfig == null)
            {
                logger.ErrorFormat("get construction build cost failed, can't find blueprint config of {0}", blueprint.Id);
                return null;
            }

            var ingredients = normalBuildingConfig.Ingredients;
            var ingredientsNum = normalBuildingConfig.IngredientsNum;
            for (var i = 0; i < ingredients.Length; i++)
            {
                var itemCost = new ItemCostAmount(ingredients[i], ingredientsNum[i]);
                list.Add(itemCost);
            }

            return list;

        }

        /// <summary>
        /// 获取组合建筑的所有消耗资源
        /// </summary>
        /// <param name="comboId"></param>
        /// <param name="grade"></param>
        /// <param name="isUpgrade"></param>
        /// <returns></returns>
        public static Dictionary<long, int> GetComboBuildConsume(long comboId, int grade, bool isUpgrade = false)
        {
            var comboConfig = McCommon.Tables.TbConstructionComboConfig.GetOrDefault(comboId);
            if (comboConfig == null)
            {
                logger.WarnFormat("can not find  config of combo id {0}", comboId);
                return null;
            }
            var consumeItem = GetBuildConsume(comboConfig.ParentPartType, grade);

            var childPartGroupType = comboConfig.ChildPartTypeBeta;
            var childGroupConfig = McCommon.Tables.TbConstructionComboChildGroupConfig.GetOrDefault(childPartGroupType);
            if (childGroupConfig == null)
            {
                logger.WarnFormat("can not find  config of combo id {0}", childPartGroupType);
                return null;
            }
            var consumeItemCombo = GetComboChildBuildConsume(childPartGroupType);

            if (consumeItemCombo != null)
            {
                foreach (var kvp in consumeItemCombo)
                {
                    if (consumeItem.ContainsKey(kvp.Key))
                    {
                        consumeItem[kvp.Key] = kvp.Value + consumeItem[kvp.Key];
                    }
                    else
                    {
                        consumeItem[kvp.Key] = kvp.Value;
                    }
                }
            }
            return consumeItem;
        }

        /// <summary>
        /// 获取组合建筑子建筑的建筑材料消耗
        /// </summary>
        /// <param name="childComboPartType"></param>
        /// <returns></returns>
        public static Dictionary<long, int> GetComboChildBuildConsume(long childComboPartType)
        {
            Dictionary<long, int> ingredients = new();
            //子建筑变成了子组合建筑
            var childComboConfig = McCommon.Tables.TbConstructionComboChildGroupConfig.GetOrDefault(childComboPartType);
            if (childComboConfig == null)
            {
                logger.ErrorFormat("can not find  config of combo id {0}", childComboPartType);
                return null;
            }

            foreach (var childId in childComboConfig.ComboId)
            {
                var itemCost = GetConstructionBuildCost(childId);
                for (int i = 0; i < itemCost.Count; i++)
                {
                    var resource = itemCost[i];
                    var amount = Mathf.CeilToInt(resource.amount);
                    if (amount <= 0)
                        continue;
                    if (ingredients.ContainsKey(resource.templateId))
                    {
                        ingredients[resource.templateId] += amount;
                    }
                    else
                    {
                        ingredients[resource.templateId] = amount;
                    }
                }
            }


            return ingredients;
        }

        public static Dictionary<long, int> GetConsumeDictFromList(List<ItemCostAmount> list, int count = 1)
        {
            Dictionary<long, int> consume = new();
            GetConsumeDictFromList(list, consume, count);
            return consume;
        }

        public static void GetConsumeDictFromList(List<ItemCostAmount> list, Dictionary<long, int> consume, int count = 1)
        {
            foreach (var itemCostAmount in list)
            {
                if (consume.ContainsKey(itemCostAmount.templateId))
                {
                    consume[itemCostAmount.templateId] += Mathf.CeilToInt(itemCostAmount.amount) * count;
                }
                else
                {
                    consume[itemCostAmount.templateId] = Mathf.CeilToInt(itemCostAmount.amount) * count;
                }
            }
        }

        /// <summary>
        /// 非核心建筑的升级消耗（工作台...）
        /// </summary>
        /// <param name="templateId"></param>
        /// <returns></returns>
        public static Dictionary<long, int> GetNoneCorePartUpgradeCost(long templateId)
        {
            var cost = new Dictionary<long, int>();
            var partConfig = McCommon.Tables.TbBuildingCore.GetOrDefault(templateId);
            if (partConfig == null)
            {
                return cost;
            }

            for (var i = 0; i < partConfig.Ingredients.Length; i++)
            {
                cost.Add(partConfig.Ingredients[i], Mathf.CeilToInt((float)partConfig.IngredientsNum[i] * partConfig.UpdateMultipler));
            }

            return cost;

        }

        public static int GetWorkBenchLevel(long templateId)
        {
            return templateId switch
            {
                (int)PartType.Tier1WorkBench => 1,
                (int)PartType.Tier1WorkBench_v1 => 1,
                (int)PartType.Tier2WorkBench => 2,
                (int)PartType.Tier2WorkBench_v1 => 2,
                (int)PartType.Tier3WorkBench => 3,
                (int)PartType.Tier3WorkBench_v1 => 3,
                _ => -1
            };
        }

        public static ConstructionComboChildGroupConfig GetComboChildPartGroupConfig(long childPartType)
        {
            return McCommon.Tables.TbConstructionComboChildGroupConfig.GetOrDefault(childPartType);
        }

        public static Dictionary<long, int> GetComboBuildConsumeWithGrade(long parentType, int parentGrade, List<long> childTypeList)
        {
            var parent = GetBuildConsume(parentType, parentGrade);
            if (childTypeList != null && childTypeList.Count > 0)
            {
                foreach (var childType in childTypeList)
                {
                    var child = GetComboChildBuildConsume(childType);
                    MergeConsume(parent, child);
                }

            }
            return parent;
        }

        public static Dictionary<long, int> GetTransformationConsume(long curParentEntityId,
            long targetParentType, int targetParentGrade, long targetComboChildGroupId, uint usedTimes)
        {
            var cost = GetPartChangeConsume(curParentEntityId, targetParentType, targetParentGrade, targetComboChildGroupId);
            float costTimes = 1;
            if (usedTimes < McCommon.Tables.TbConstructionConstantConfig.AlterCosts.Length)
            {
                costTimes = McCommon.Tables.TbConstructionConstantConfig.AlterCosts[usedTimes];
            }
            else
            {
                costTimes = McCommon.Tables.TbConstructionConstantConfig.AlterCosts.Last();
            }
            Dictionary<long, int> ret = new();
            foreach (var (id, amount) in cost)
            {
                var count = Mathf.CeilToInt(amount * costTimes);
                if (count > 0)
                {
                    ret.Add(id, count);
                }
            }
            return ret;
        }
        /// <summary>
        /// 计算转换消耗
        /// </summary>
        /// <param name="curParentEntityId">当前父亲建筑EntityId</param>
        /// <param name="targetParentType">目标父亲建筑类型</param>
        /// <param name="targetParentGrade">目标父亲建筑等级</param>
        /// <param name="targetComboChildGroupId">目标子建筑组id</param>
        /// <returns></returns>
        public static Dictionary<long, int> GetPartChangeConsume(long curParentEntityId,
            long targetParentType, int targetParentGrade, long targetComboChildGroupId)
        {
            Dictionary<long, int> totalCost = new();
            var parentEntity = EntityManager.Instance.GetEntity(curParentEntityId) as PartEntity;
            if (parentEntity == null)
            {
                logger.InfoFormat("GetPartChangeConsume failed, can't find parent part entity {0}", curParentEntityId);
                return totalCost;
            }
            var curParentType = parentEntity.TemplateId;
            var curParentGrade = parentEntity.Grade;

            //当前父建筑的子建筑类型列表
            List<long> curAliveChildTypeList = new();
            //获取组合建筑信息
            if (parentEntity.ComboChildDataDict != null && parentEntity.ComboChildDataDict.Count > 0)
            {
                foreach (var kvp in parentEntity.ComboChildDataDict)
                {
                    var comboChildPartData = kvp.Value;
                    if (comboChildPartData == null || comboChildPartData.State == 0)
                    {
                        continue;
                    }
                    curAliveChildTypeList.Add(comboChildPartData.PartTemplateId);
                }
            }
            return GetPartChangeConsume(curParentType, curParentGrade, targetParentType, targetParentGrade, curAliveChildTypeList, targetComboChildGroupId);
        }

        /// <summary>
        /// 计算材料消耗
        /// </summary>
        /// <param name="curParentType">当前父建筑类型</param>
        /// <param name="curParentGrade">当前父建筑等级</param>
        /// <param name="targetParentType">目标父建筑类型</param>
        /// <param name="targetParentGrade">目标父建筑等级</param>
        /// <param name="curAliveChildTypeList">当前存活的子建筑</param>
        /// <param name="targetComboChildGroupId">目标子建筑组合</param>
        /// <returns></returns>
        public static Dictionary<long, int> GetPartChangeConsume(long curParentType, int curParentGrade,
            long targetParentType, int targetParentGrade, List<long> curAliveChildTypeList, long targetComboChildGroupId)
        {
            Dictionary<long, int> totalCost = new();

            //分析目标子建筑信息
            List<long> targetChildTypeList = null;
            if (targetComboChildGroupId != 0)
            {
                targetChildTypeList = new();
                var childComboGroupConfig = McCommon.Tables.TbConstructionComboChildGroupConfig.GetOrDefault(targetComboChildGroupId);
                if (childComboGroupConfig == null)
                {
                    logger.ErrorFormat("GetPartChangeConsume error,calculate child consume failed, can't find combo child group config {0}", targetComboChildGroupId);
                }
                else
                {
                    foreach (var childType in childComboGroupConfig.ComboId)
                    {
                        targetChildTypeList.Add(childType);
                    }
                }
            }

            //分析父建筑变化情况
            if (targetParentGrade < curParentGrade)
            {
                logger.ErrorFormat("GetPartChangeConsume failed, target grade {0} is lower than current grade {1}", targetParentGrade, curParentGrade);
                return totalCost;
            }
            var needReduceNum = Pool.Get<Dictionary<long, int>>();
            needReduceNum.Clear();
            if (targetParentType != curParentType)
            {
                //父建筑改造，父建筑的消耗等于目标建筑的消耗
                var targetParentCost = GetBuildConsume(targetParentType, targetParentGrade);
                /*        var curParentCost = GetBuildConsume(curParentType, curParentGrade);
                        //计算消耗差
                        foreach (var (id, amount) in targetParentCost)
                        {
                            if (curParentCost.ContainsKey(id))
                            {
                                if (!needReduceNum.ContainsKey(id))
                                {
                                    needReduceNum[id] = curParentCost[id];
                                }
                                else
                                {
                                    needReduceNum[id] += curParentCost[id];
                                }
                            }
                        }

                        if (needReduceNum.Count > 0)
                        {
                            foreach (var kvp in needReduceNum)
                            {
                                if (targetParentCost.ContainsKey(kvp.Key))
                                {
                                    targetParentCost[kvp.Key] -= kvp.Value;
                                    if (targetParentCost[kvp.Key] <= 0)
                                    {
                                        targetParentCost.Remove(kvp.Key);
                                    }
                                }
                            }
                        }*/

                MergeConsume(totalCost, targetParentCost);
            }
            else if (targetParentGrade == curParentGrade)
            {
                //完全不变
            }
            else
            {
                //父建筑升级，消耗等于目标建筑
                var targetParentCost = GetBuildConsume(targetParentType, targetParentGrade);
                MergeConsume(totalCost, targetParentCost);
            }


            //子建筑可以非常粗暴处理：目标类型建筑的消耗减去当前活着的建筑消耗就行
            var curChildCost = new Dictionary<long, int>();
            var targetChildCost = new Dictionary<long, int>();
            if (targetChildTypeList != null && targetChildTypeList.Count > 0)
            {
                foreach (var targetChildType in targetChildTypeList)
                {
                    var cost = GetConsumeDictFromList(GetConstructionBuildCost(targetChildType));
                    MergeConsume(targetChildCost, cost);
                }
            }
            //当前活着的子建筑价值
            if (curAliveChildTypeList != null && curAliveChildTypeList.Count > 0)
            {
                foreach (var childType in curAliveChildTypeList)
                {
                    var cost = GetConsumeDictFromList(GetConstructionBuildCost(childType));
                    MergeConsume(curChildCost, cost);
                }
            }
            needReduceNum.Clear();
            //计算差值
            foreach (var (id, amount) in targetChildCost)
            {
                if (curChildCost.ContainsKey(id))
                {
                    if (!needReduceNum.ContainsKey(id))
                    {
                        needReduceNum[id] = curChildCost[id];
                    }
                    else
                    {
                        needReduceNum[id] += curChildCost[id];
                    }
                }
            }

            if (needReduceNum.Count > 0)
            {
                foreach (var kvp in needReduceNum)
                {
                    if (targetChildCost.ContainsKey(kvp.Key))
                    {
                        targetChildCost[kvp.Key] -= kvp.Value;
                        if (targetChildCost[kvp.Key] <= 0)
                        {
                            targetChildCost.Remove(kvp.Key);
                        }
                    }
                }
            }
            Pool.Release(needReduceNum);

            MergeConsume(totalCost, targetChildCost);
            return totalCost;
        }

        public static Dictionary<long, int> GetDebrisRepairCost(long partType)
        {
            var config = McCommon.Tables.TbBuildingDebris.GetOrDefault(partType);
            if (config == null)
            {
                logger.ErrorFormat("GetDebrisRepairCost no config {0}", partType);
                return null;
            }
            if (config.RepairListID == null || config.RepairListamount == null)
            {
                logger.ErrorFormat("GetDebrisRepairCost repair item null");
                return null;
            }
            if (config.RepairListID.Length != config.RepairListamount.Length)
            {
                logger.ErrorFormat("GetDebrisRepairCost repair item len error {0} {1}", config.RepairListID.Length, config.RepairListamount.Length);
                return null;
            }
            var ret = new Dictionary<long, int>();
            for (int i = 0; i < config.RepairListID.Length; i++)
            {
                ret.TryGetValue(config.RepairListID[i], out var preCount);
                ret[config.RepairListID[i]] = config.RepairListamount[i] + preCount;
            }
            return ret;
        }
    }
}