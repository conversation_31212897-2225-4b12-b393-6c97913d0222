
using FairyGUI;
using WizardGames.Soc.SocClient.Ui.Binder.GameHud;

namespace WizardGames.Soc.SocClient.Ui
{
    /// <summary>
    /// 以 ComCountdown 为基础的HUD元素
    /// 局内战斗相关倒计时通用
    /// </summary>
    public class UiHudElemCountDown : UiHudElem
    {
        private ComCountdown comCountdown;
        private HudCountDownBinder binder;
        protected override void OnCreate(GComponent node)
        {
            base.OnCreate(node);
            this.binder = new HudCountDownBinder(node);
            this.comCountdown = this.binder.Countdown.BinderRoot as ComCountdown;
            this.CanUpdateWhenHide = true;//支持隐藏时继续更新
        }
        /// <summary>
        /// 请使用 MgrHudCommon.ShowCountDown 方法调用
        /// </summary>
        /// <param name="ms"></param>
        /// <param name="tips"></param>
        /// <param name="onFinish"></param>
        /// <param name="onClickCancel"></param>
        /// <param name="iconUrl"></param>
        public void SetCountdown(int ms, string tips, EventCallback0 onFinish = null, EventCallback0 onClickCancel = null, EventCallback0 onAbort = null, string iconUrl = null, bool autoHideWhenCancel = true)
        {
            this.comCountdown.SetData(ms, tips, onFinish, onClickCancel, onAbort, autoHideWhenCancel);
            this.comCountdown.SetIcon(iconUrl);
        }
        /// <summary>
        /// 中止倒计时
        /// </summary>
        public void AbortCountDown()
        {
            this.comCountdown?.CallAbort();
        }
        /// <summary>
        /// 边界模式下显示取消按钮
        /// </summary>
        public override void StartEdit()
        {
            base.StartEdit();
            var node = this.TryGetNode();
            if (node == null) return;
            this.binder = new HudCountDownBinder(node);
            binder.Countdown.CtrlStyle.SetSelectedPage("withCancel");
        }

        public override void Show()
        {
            base.Show();
            this.comCountdown?.SetKeyTipsAction();
        }

        public override void Hide()
        {
            base.Hide();
            this.comCountdown?.ClearKeyTipsAction();
        }
    }
}