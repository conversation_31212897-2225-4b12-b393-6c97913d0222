﻿using Sirenix.OdinInspector;
using Soc.Common.Unity.PlatformResOpt;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;
using WizardGames.Soc.SocAssetAuditing;
using Object = UnityEngine.Object;

namespace PackTool
{

    public enum ECopyRefType
    {
        None,
        MeshFilter_Mesh,
        MeshRenderer_Mat,
        SkinnedMeshRenderer_Mesh,
        SkinnedMeshRenderer_Mat,
    }

    [System.Serializable]
    public class PlatformCpyStrPair
    {
        [LabelText("替换的源")]
        public string cpySrc;
        
        [LabelText("替换的目的")]
        public string cpyDst;
    }
    
    //替换同名同路径的引用
    [CreateAssetMenu(fileName = "PlatformCopyRefRule", menuName = "平台资产配置/规则/拷贝引用", order = 0)]
    public class PlatformCopyRefRule : PlatformPackPrefabSearchRule
    {
        public static List<MeshFilter> CachedMeshFilters = new ();
        public static List<MeshRenderer> CachedMeshRenderers = new ();
        public static List<SkinnedMeshRenderer> CachedSkinnedMeshRenderers = new ();
        
        [LabelText("拷贝目标资产路径匹配规则")]
        public PlatformCpyStrPair[] copySearchRule = new PlatformCpyStrPair[0];
        
        [LabelText("拷贝的类型")]
        public ECopyRefType[] copyRefTypes = new ECopyRefType[0];
        
        [LabelText("是否fallback")]
        public WhiteList.DirConfig[] fallbackSearchRules = new WhiteList.DirConfig[0];

        [LabelText("后处理方法,:为分隔符,分别为程序集名:类型名:方法名")] 
        public string method;

        public override void Prepare(PackInputParams packInputParams)
        {
            base.Prepare(packInputParams);

            if (packInputParams.processSvnFileInfo == null)
            {
                return;
            }
            
            if (!base.ShouldExecute(packInputParams))
            {
                return ;
            }
            
            StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>Prepare 尝试追加依赖:{0}", packInputParams.processSourcePath);
            
            var context = packInputParams.context;
            string processSourcePath = TryFindReplaceSourcePath(packInputParams);
            
            if (!string.IsNullOrEmpty(processSourcePath))
            {
                PlatformPackSvnFileInfo svnFileInfo = null;
                if (context.svnCache != null)
                {
                    context.svnCache.files.TryGetValue(processSourcePath, out svnFileInfo);
                }

                if (svnFileInfo == null)
                {
                    StepOptimizePlatformRes.logger.InfoFormat("Prepare 没有找到svn缓存信息 :{0}", processSourcePath);
                    return;
                }
                
                //拿到特殊资产的依赖列表，合并在一起
                GameObject asset = AssetDatabase.LoadAssetAtPath<GameObject>(processSourcePath);
                if (asset == null)
                {
                    return;
                }
                
                var oldCnt = packInputParams.processDepCaches.dependencyList.Count;
                
                var depdendency = PlatformPackUtils.BuildDepCache(asset, svnFileInfo,context,true);
                PlatformPackBuildDepCache.Combine( packInputParams.processDepCaches,depdendency);

                StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>Prepare 合并依赖:{0}到{1} 依赖数量:{2} -> {3}",
                    processSourcePath,packInputParams.processSourcePath, oldCnt, packInputParams.processDepCaches.dependencyList.Count);
            }
            else if(fallbackSearchRules.Length > 0)
            {
                var assetTarget = packInputParams.assetTarget;

                GameObject targetGo = null;
                if (assetTarget is GameObject)
                {
                    targetGo = assetTarget as GameObject;
                }
                else if (assetTarget is Component comp)
                {
                    targetGo = comp.gameObject;
                }
            
                if (targetGo == null)
                {
                    ErrorFormat(packInputParams.context,"<PlatformCopyRefRule> 不支持的资源类型:{0} 找不到gameobject", assetTarget);
                    return ;
                }
                
                if (Array.IndexOf(copyRefTypes, ECopyRefType.MeshFilter_Mesh) != -1)
                {
                    targetGo.GetComponentsInChildren(CachedMeshFilters);
                    
                    StepProcessMeshFilter(packInputParams.context,(targetComponent,meshPath, fallbackMesh) =>
                    {
                        if (fallbackMesh == null)
                        {
                            return;
                        }

                        PlatformPackSvnFileInfo svnFileInfo = null;
                        if (context.svnCache != null)
                        {
                            context.svnCache.files.TryGetValue(meshPath, out svnFileInfo);
                        }

                        if (svnFileInfo == null)
                        {
                            StepOptimizePlatformRes.logger.InfoFormat("Prepare 没有找到svn缓存信息 :{0} ", meshPath);
                            return;
                        }

                        var oldCnt = packInputParams.processDepCaches.dependencyList.Count;

                        var depdendency = PlatformPackUtils.BuildDepCache(fallbackMesh, svnFileInfo, context,true);
                        PlatformPackBuildDepCache.Combine(packInputParams.processDepCaches, depdendency);

                        StepOptimizePlatformRes.logger.InfoFormat(
                            "<PlatformCopyRefRule>Prepare MeshFilter 合并依赖:{0}到{1} 依赖数量:{2} -> {3}",
                            meshPath, packInputParams.processSourcePath, oldCnt,
                            packInputParams.processDepCaches.dependencyList.Count);
                    });
                }
                
                //处理meshrenderer
                if (Array.IndexOf(copyRefTypes, ECopyRefType.MeshRenderer_Mat) != -1)
                {
                    //fallback流程
                    targetGo.GetComponentsInChildren(CachedMeshRenderers);

                    //永远返回false，不需要更新资产
                    StepProcessMeshRenderer(packInputParams.context,(targetComponent,matPath, fallbackMat) =>
                    {
                        if (fallbackMat == null)
                        {
                            return false;
                        }

                        PlatformPackSvnFileInfo svnFileInfo = null;
                        if (context.svnCache != null)
                        {
                            context.svnCache.files.TryGetValue(matPath, out svnFileInfo);
                        }

                        if (svnFileInfo == null)
                        {
                            StepOptimizePlatformRes.logger.InfoFormat("Prepare 没有找到svn缓存信息 :{0} ", matPath);
                            return false;
                        }

                        var oldCnt = packInputParams.processDepCaches.dependencyList.Count;

                        var depdendency = PlatformPackUtils.BuildDepCache(fallbackMat, svnFileInfo, context,true);
                        PlatformPackBuildDepCache.Combine(packInputParams.processDepCaches, depdendency);

                        StepOptimizePlatformRes.logger.InfoFormat(
                            "<PlatformCopyRefRule>Prepare MeshRenderer 合并依赖:{0}到{1} 依赖数量:{2} -> {3}",
                            matPath, packInputParams.processSourcePath, oldCnt,
                            packInputParams.processDepCaches.dependencyList.Count);

                        return false;
                    });
                }
                
                bool includeSkinnedMeshRd_material = Array.IndexOf(copyRefTypes, ECopyRefType.SkinnedMeshRenderer_Mat) != -1;
                bool includeSkinnedMeshRd_mesh = Array.IndexOf(copyRefTypes, ECopyRefType.SkinnedMeshRenderer_Mesh) != -1;
                if (includeSkinnedMeshRd_material || includeSkinnedMeshRd_mesh)
                {
                    targetGo.GetComponentsInChildren(CachedSkinnedMeshRenderers);

                    //不修改资源
                    StepProcessSkinnedMeshRenderer(packInputParams.context,(targetComponent,resPath,res) =>
                    {
                        if (res == null)
                        {
                            return false;
                        }

                        if (res is Mesh fallbackMesh)
                        {
                            PlatformPackSvnFileInfo svnFileInfo = null;
                            if (context.svnCache != null)
                            {
                                context.svnCache.files.TryGetValue(resPath, out svnFileInfo);
                            }

                            if (svnFileInfo == null)
                            {
                                StepOptimizePlatformRes.logger.InfoFormat("Prepare 没有找到svn缓存信息 :{0} ", resPath);
                                return false;
                            }

                            var oldCnt = packInputParams.processDepCaches.dependencyList.Count;

                            var depdendency = PlatformPackUtils.BuildDepCache(fallbackMesh, svnFileInfo, context,true);
                            PlatformPackBuildDepCache.Combine(packInputParams.processDepCaches, depdendency);

                            StepOptimizePlatformRes.logger.InfoFormat(
                                "<PlatformCopyRefRule>Prepare SkinnedMeshRenderer 合并依赖:{0}到{1} 依赖数量:{2} -> {3}",
                                resPath, packInputParams.processSourcePath, oldCnt,
                                packInputParams.processDepCaches.dependencyList.Count);
                            
                        }
                        else if (res is Material fallbackMat)
                        {
                            PlatformPackSvnFileInfo svnFileInfo = null;
                            if (context.svnCache != null)
                            {
                                context.svnCache.files.TryGetValue(resPath, out svnFileInfo);
                            }

                            if (svnFileInfo == null)
                            {
                                StepOptimizePlatformRes.logger.InfoFormat("Prepare 没有找到svn缓存信息 :{0} ", resPath);
                                return false;
                            }

                            var oldCnt = packInputParams.processDepCaches.dependencyList.Count;

                            var depdendency = PlatformPackUtils.BuildDepCache(fallbackMat, svnFileInfo, context,true);
                            PlatformPackBuildDepCache.Combine(packInputParams.processDepCaches, depdendency);

                            StepOptimizePlatformRes.logger.InfoFormat(
                                "<PlatformCopyRefRule>Prepare SkinnedMeshRenderer 合并依赖:{0}到{1} 依赖数量:{2} -> {3}",
                                resPath, packInputParams.processSourcePath, oldCnt,
                                packInputParams.processDepCaches.dependencyList.Count);
                        }
                        
                        return false;
                    });
                }
            }
        }

        public override bool ShouldExecute(PackInputParams packInputParams)
        {
            if (!base.ShouldExecute(packInputParams))
            {
                return false;
            }

            if (copyRefTypes.Length == 0)
            {
                StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>没有拷贝配置 无需拷贝引用:{0}", packInputParams.rulePath);
                return false;
            }

            var assetTarget = packInputParams.assetTarget;

            GameObject targetGo = null;
            if (assetTarget is GameObject)
            {
                targetGo = assetTarget as GameObject;
            }
            else if (assetTarget is Component comp)
            {
                targetGo = comp.gameObject;
            }
            
            if (targetGo == null)
            {
                ErrorFormat(packInputParams.context,"<PlatformCopyRefRule> 不支持的资源类型:{0} 找不到gameobject", assetTarget);
                return false;
            }
            
            var sourceGo = TryFindReplaceGo(packInputParams);
            if (sourceGo == null)
            {
                if (fallbackSearchRules.Length == 0)
                {
                    ErrorFormat(packInputParams.context,"<PlatformCopyRefRule>没有找到拷贝目标:{0}", packInputParams.rulePath);
                    return false;
                }
            }
            else if (sourceGo == assetTarget)
            {
                StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>拷贝目标和当前处理的资产相同:{0}", packInputParams.rulePath);
                return false;
            }

            return true;
        }

        public override int TryApplyCache(PackInputParams packInputParams)
        {
            if (!ShouldExecute(packInputParams))
            {
                return (int)EPlatformErrorCode.ContinueCode;
            }
            
            if (packInputParams.ShouldReadPlatformCache())
            {
                //处理成功，则返回100，break后续rule阶段
                if (ApplyCache(packInputParams, out PlatformPackBuildFileCache cache))
                {
                    //拷贝缓存的时候尝试把依赖对象也拷贝过去
                    bool childAssetSuc = true;
                    if (cache != null && cache.childAssets != null)
                    {
                        foreach (var cachePath in cache.childAssets)
                        {
                            var childCache = TryFindUsableBuildCache(packInputParams.context, cachePath,
                                packInputParams.ruleHash, null);
                            if (childCache == null)
                            {
                                childAssetSuc = false;
                                ErrorFormat(packInputParams.context, "<PlatformCopyRefRule>没有找到缓存的子资产:{0}", cachePath);
                                continue;
                            }

                            bool cpySuc = CopyCache(packInputParams.context, childCache, cachePath, null);
                            if (!cpySuc)
                            {
                                childAssetSuc = false;
                                ErrorFormat(packInputParams.context, "<PlatformCopyRefRule>拷贝当前资产的依赖缓存失败 ");
                            }
                            else
                            {
                                StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>拷贝当前资产的依赖缓存成功");
                            }
                        }
                    }

                    if (childAssetSuc)
                    {
                        return (int)EPlatformErrorCode.Suc;
                    }
                }

                //应用阶段，失败也跳出
                if (packInputParams.context.processStage == EPlatformProcessStage.CI_Apply)
                {
                    //如果是当前规则要处理的资产,应用缓存失败了，则认为是一个异常
                    if (cache != null && cache.cacheRuleHash == packInputParams.ruleHash)
                    {
                        ErrorFormat(packInputParams.context, " 应用缓存失败:{0}", packInputParams.processSourcePath);
                        return (int)EPlatformErrorCode.ErrorCode;
                    }
                    //否则继续执行
                    else
                    {
                        return (int)EPlatformErrorCode.ContinueCode;
                    }
                }
            }

            return (int)EPlatformErrorCode.ContinueCode;
        }

        public override int ExecuteMainRule(PackInputParams packInputParams)
        {
            GameObject targetGo = null;
            if (packInputParams.assetTarget is GameObject go)
            {
                targetGo = go;
            }
            else if (packInputParams.assetTarget is Component comp)
            {
                targetGo = comp.gameObject;
            }
            
            var sourceGo = TryFindReplaceGo(packInputParams);

            if (sourceGo != null)
            {
                StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>找到拷贝目标来源模板:{0} -> {1}", packInputParams.rulePath, sourceGo);
            }
            
            //建造只处理prefabs_art目录即可，因为meshconfig和sourcemesh等资产都可以通过后期处理的method，执行建造那边的重新生成逻辑，刷新meshconfig和renderer，至于gpuinstobject，因为存的是meshrendererer的引用，所以mesh和材质更新不影响它。

            bool assetChanged = false;
            //处理mesh filters
            if (Array.IndexOf(copyRefTypes, ECopyRefType.MeshFilter_Mesh) != -1)
            {
                //存在模板资产的时候
                if (sourceGo != null)
                {
                    targetGo.GetComponentsInChildren(CachedMeshFilters);

                    foreach (var targetComponent in CachedMeshFilters)
                    {
                        var transformPath = PlatformPackUtils.GetTransformPathSkipRoot(targetComponent.transform);
                        var sourceTransform = sourceGo.transform.Find(transformPath);
                        if (sourceTransform == null)
                        {
                            ErrorFormat(packInputParams.context,"<PlatformCopyRefRule>请检查层级结构 来源模板 拷贝MeshFilter_Mesh引用失败:找不到源对象的Transform:{0} ",transformPath);
                            return (int)EPlatformErrorCode.ErrorCode;
                        }
                        
                        var sourceMeshFilter = sourceTransform.GetComponent<MeshFilter>();
                        if (sourceMeshFilter == null || sourceMeshFilter.sharedMesh == null)
                        {
                            ErrorFormat(packInputParams.context,"<PlatformCopyRefRule>来源模板 拷贝MeshFilter_Mesh引用失败:源对象没有MeshFilter或Mesh:{0}", transformPath);
                            return (int)EPlatformErrorCode.ErrorCode;
                        }

                        if (targetComponent.sharedMesh == sourceMeshFilter.sharedMesh)
                        {
                            StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>来源模板 拷贝MeshFilter_Mesh引用 因为引用相同，所以不执行替换:{0}", transformPath);
                        }
                        else
                        {
                            StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>来源模板 拷贝MeshFilter_Mesh引用成功:{0} -> {1} transform:{2}",
                                targetComponent.sharedMesh, sourceMeshFilter.sharedMesh,transformPath);
                        
                            targetComponent.sharedMesh = sourceMeshFilter.sharedMesh;

                            assetChanged = true;
                        }
                    }
                }
                else if(fallbackSearchRules.Length > 0)
                {
                    //fallback流程
                    targetGo.GetComponentsInChildren(CachedMeshFilters);

                    StepProcessMeshFilter(packInputParams.context,(targetComponent,meshPath, fallbackMesh) =>
                    {
                        if (fallbackMesh != null)
                        {
                            StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule> fallback 拷贝MeshFilter_Mesh引用成功:{0} -> {1}",
                                targetComponent.sharedMesh, fallbackMesh);
                            
                            targetComponent.sharedMesh = fallbackMesh;
                            assetChanged = true;
 
                        }
                        else
                        {
                            StepOptimizePlatformRes.logger.WarnFormat("<PlatformCopyRefRule> fallback 未找到匹配的fallback资产 拷贝MeshFilter_Mesh引用失败:{0} ", meshPath);
                        }
                    });
                }
                else
                {
                    StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>没有找到拷贝MeshFilter_Mesh引用的源对象:{0} 并且没有fallback规则", packInputParams.processSourcePath);
                }
            }

            //处理meshrenderer
            if (Array.IndexOf(copyRefTypes, ECopyRefType.MeshRenderer_Mat) != -1)
            {
                 //存在模板资产的时候
                if (sourceGo != null)
                {
                    targetGo.GetComponentsInChildren(CachedMeshRenderers);

                    foreach (var targetComponent in CachedMeshRenderers)
                    {
                        var transformPath = PlatformPackUtils.GetTransformPathSkipRoot(targetComponent.transform);
                        var sourceTransform = sourceGo.transform.Find(transformPath);
                        if (sourceTransform == null)
                        {
                            ErrorFormat(packInputParams.context,"<PlatformCopyRefRule>请检查层级结构 来源模板 拷贝MeshRenderer材质引用失败:找不到源对象的Transform:{0} -> {1} ", targetComponent, transformPath);
                            return (int)EPlatformErrorCode.ErrorCode;
                        }
                        
                        var sourceMeshRd = sourceTransform.GetComponent<MeshRenderer>();
                        if (sourceMeshRd == null || sourceMeshRd.sharedMaterials == null)
                        {
                            ErrorFormat(packInputParams.context,"<PlatformCopyRefRule>来源模板 拷贝MeshRenderer材质引用失败:源对象没有MeshRenderer或sharedMaterials:{0}", transformPath);
                            return (int)EPlatformErrorCode.ErrorCode;
                        }

                        StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>来源模板 拷贝MeshRenderer材质引用:{0} -> {1} transform:{2}",
                            PlatformPackUtils.GetPrintableName(targetComponent.sharedMaterials), PlatformPackUtils.GetPrintableName(sourceMeshRd.sharedMaterials), transformPath);
                        
                        targetComponent.sharedMaterials = sourceMeshRd.sharedMaterials;
                        assetChanged = true;
                    }
                }
                else if(fallbackSearchRules.Length > 0)
                {
                    //fallback流程
                    targetGo.GetComponentsInChildren(CachedMeshRenderers);

                    StepProcessMeshRenderer(packInputParams.context,(renderer, matpath, fallbackMat) =>
                    {
                        if (fallbackMat != null)
                        {
                            assetChanged = true;
                            return true;
                        }
                        return false;
                    });
                }
                else
                {
                    StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>没有找到拷贝MeshFilter_Mesh引用的源对象:{0} 并且没有fallback规则", packInputParams.processSourcePath);
                }
            }

            bool includeSkinnedMeshRd_material = Array.IndexOf(copyRefTypes, ECopyRefType.SkinnedMeshRenderer_Mat) != -1;
            bool includeSkinnedMeshRd_mesh = Array.IndexOf(copyRefTypes, ECopyRefType.SkinnedMeshRenderer_Mesh) != -1;
            if (includeSkinnedMeshRd_material || includeSkinnedMeshRd_mesh)
            {
                //存在模板资产的时候
                if (sourceGo != null)
                {
                    targetGo.GetComponentsInChildren(CachedSkinnedMeshRenderers);

                    foreach (var targetComponent in CachedSkinnedMeshRenderers)
                    {
                        var transformPath = PlatformPackUtils.GetTransformPathSkipRoot(targetComponent.transform);
                        var sourceTransform = sourceGo.transform.Find(transformPath);
                        if (sourceTransform == null)
                        {
                            ErrorFormat(packInputParams.context,"<PlatformCopyRefRule>请检查层级结构 来源模板 拷贝SkinnedMeshRenderer引用失败:找不到源对象的Transform:{0}", transformPath);
                            return (int)EPlatformErrorCode.ErrorCode;
                        }

                        var sourceRd = sourceTransform.GetComponent<SkinnedMeshRenderer>();
                        if (sourceRd == null )
                        {
                            ErrorFormat(packInputParams.context,"<PlatformCopyRefRule>来源模板 拷贝SkinnedMeshRenderer引用失败:源对象没有SkinnedMeshRenderer:{0} ", transformPath);
                            return (int)EPlatformErrorCode.ErrorCode;
                        }

                        if (includeSkinnedMeshRd_mesh)
                        {
                            StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>来源模板 拷贝SkinnedMeshRenderer_Mesh引用:{0} -> {1} transform:{2}",
                                targetComponent.sharedMesh, sourceRd.sharedMesh, transformPath);

                            targetComponent.sharedMesh = sourceRd.sharedMesh;
                            assetChanged = true;
                        }
                        
                        if (includeSkinnedMeshRd_material)
                        {
                            StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule> 拷贝SkinnedMeshRenderer_Mat引用:{0} -> {1} transform:{2}",
                                PlatformPackUtils.GetPrintableName(targetComponent.sharedMaterials), PlatformPackUtils.GetPrintableName(sourceRd.sharedMaterials), transformPath);
                            
                            targetComponent.sharedMaterials = sourceRd.sharedMaterials;
                            assetChanged = true;
                        }
                    }
                }
                else if (fallbackSearchRules.Length > 0)
                {
                    //fallback流程
                    targetGo.GetComponentsInChildren(CachedSkinnedMeshRenderers);

                    StepProcessSkinnedMeshRenderer(packInputParams.context,(targetComponent,resPath,res) =>
                    {
                        if (res == null)
                        {
                            return false;
                        }

                        if (res is Mesh fallbackMesh)
                        {
                            targetComponent.sharedMesh = fallbackMesh;
                            assetChanged = true;
                            return true;
                        }
                        else if (res is Material fallbackMat)
                        {
                            assetChanged = true;
                            return true;
                        }
                        
                        return false;
                    });

                }
                else
                {
                    StepOptimizePlatformRes.logger.InfoFormat(
                        "<PlatformCopyRefRule>没有找到拷贝MeshFilter_Mesh引用的源对象:{0} 并且没有fallback规则",
                        packInputParams.processSourcePath);
                }
            }


            if (assetChanged)
            {
                StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>尝试触发保存资产:{0}", packInputParams.processSourcePath);
                //这里不需要处理依赖资产，再这次主资产的修改中，如果因为主体修改，而导致其他内嵌了它的资产发生了变更，就不用关心。
                // PrefabPostprocessor.Listening = true;
                
                if (TrySaveAsset(packInputParams))
                {
                    AssetDatabase.SaveAssetIfDirty(targetGo);
                    AssetDatabase.Refresh();
                    StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>触发刷新资产:{0}", packInputParams.processSourcePath);
                }

                // PrefabPostprocessor.Listening = false;
                //
                // //收集变更的依赖资产加入到modify列表中
                // foreach (var updatedPath in PrefabPostprocessor.listeningPaths)
                // {
                //     SaveDependencyAsset(packInputParams, updatedPath);
                // }
                //
                // PrefabPostprocessor.listeningPaths.Clear();
            
                return InvokePostMethod(packInputParams);
            }

            return (int)EPlatformErrorCode.Suc;
        }

        private int InvokePostMethod(PackInputParams packInputParams)
        {
            if (!string.IsNullOrEmpty(method) && method.Contains(":"))
            {
                var splitMethod = method.Split(':');
                if (splitMethod.Length == 3)
                {
                    var assemblyName = splitMethod[0];
                    var typeName = splitMethod[1];
                    var methodname = splitMethod[2];

                    Assembly asm = Assembly.Load(assemblyName);
                    var type = asm.GetType(typeName);
                    if (type == null)
                    {
                        ErrorFormat(packInputParams.context,"<PlatformCopyRefRule>找不到类型:{0} 在程序集:{1}", typeName, assemblyName);
                        return (int)EPlatformErrorCode.ErrorCode;
                    }
                    
                    var method = type.GetMethod(methodname, BindingFlags.Public | BindingFlags.Static | BindingFlags.NonPublic);
                    if (method == null)
                    {
                        ErrorFormat(packInputParams.context,"<PlatformCopyRefRule>找不到方法:{0} 在类型:{1} 中", methodname, typeName);
                        return (int)EPlatformErrorCode.ErrorCode;
                    }

                    //然后运行时预览这部分夜视收集不到的
                    //调用方法的时候，可能存在修改其他部分的资产，比如动态产出的资产，而不是直接直接使用它的，就得额外监听处理。
                    if (!Application.isPlaying)
                    {
                        PrefabPostprocessor.Listening = true;
                    }

                    if (method.ReturnParameter.ParameterType != typeof(void))
                    {
                        var result = method.Invoke(null, new object[] { packInputParams }) ;
                        StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>调用后处理方法结束 {0} 方法为:{0}.{1}",result, typeName, methodname);
                    }
                    else
                    {
                        method.Invoke(null, new object[] { packInputParams });
                        StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>调用后处理方法成功  方法为:{0}.{1}", typeName, methodname);
                    }

                    PrefabPostprocessor.Listening = false;
                        
                    //收集变更的列表
                    //收集变更的依赖资产加入到modify列表中
                    foreach (var updatedPath in PrefabPostprocessor.listeningPaths)
                    {
                        SaveDependencyAsset(packInputParams, updatedPath);
                    }
                        
                    PrefabPostprocessor.listeningPaths.Clear();
                }
                else
                {
                    ErrorFormat(packInputParams.context,"<PlatformCopyRefRule>方法格式错误,请使用程序集名:方法名的格式:{0}", method);
                }
            }
            return (int)EPlatformErrorCode.Suc;
        }

        private void SaveDependencyAsset(PackInputParams packInputParams, string updatedPath)
        {
            if (updatedPath ==  packInputParams.processSourcePath)
            {
                return;
            }

            StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>收集变更的依赖资产:{0}", updatedPath);

            var context = packInputParams.context;
            if (context.modifySet.ContainsKey(updatedPath))
            {
                StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>收集变更的依赖资产:{0} 已经存在于modifySet中,跳过保存", updatedPath);
                return;
            }

            var depAsset = AssetDatabase.LoadAssetAtPath<Object>(updatedPath);
            if (depAsset != null)
            {
                //动态资产不一定存在缓存中，需要动态收集
                PlatformPackSvnFileInfo svnFileInfo = null;
                if (context.svnCache != null)
                {
                    context.svnCache.files.TryGetValue(updatedPath, out svnFileInfo);
                }
       
                if (context.depCache.TryGetValue(updatedPath, out var depCache) == false)
                {
                    StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>收集变更的依赖资产:{0} 没有找到对应的依赖缓存,创建新的", updatedPath);
                    
                    depCache = new PlatformPackBuildDepCache();
                    
                    var curDep = PlatformPackUtils.BuildDepCache(depAsset, svnFileInfo,context);
                    depCache.dependencyList.AddRange(curDep);
                    
                    context.depCache[updatedPath] = depCache;
                }
                
                if(context.ruleStats.TryGetValue(this, out var stats) == false)
                {
                    stats = new PlatformPackRuleStats();
                    stats.ruleAsset = this;
                    context.ruleStats[this] = stats;
                }

                stats.processSucDepPaths.Add(updatedPath);

                //尝试保存资产
                bool suc = TrySaveAsset(packInputParams.context, depAsset, updatedPath, svnFileInfo,packInputParams.ruleHash);
                if (suc)
                {
                    StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>收集变更的依赖资产:{0} 保存成功", updatedPath);
                    if(context.modifySet.TryGetValue(packInputParams.processSourcePath, out var modifyData))
                    {
                        if (modifyData.childAssets == null)
                        {
                            modifyData.childAssets = new();
                        }
                        
                        modifyData.childAssets.Add(updatedPath);
                        StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>收集变更的依赖资产:{0} 添加到modifySet的子资产列表中", updatedPath);
                    }
                    else
                    {
                        ErrorFormat(packInputParams.context,"<PlatformCopyRefRule>收集变更的依赖资产:{0} 没有找到对应的modifySet数据", packInputParams.processSourcePath);
                    }
                }
            }
            else
            {
                StepOptimizePlatformRes.logger.WarnFormat("<PlatformCopyRefRule>收集变更的依赖资产失败:{0} 可能是因为该路径没有对应的资产",
                    updatedPath);
            }
        }

        T TryFindFallbackPath<T>(PlatformPackContext context,T source,string referencePath) where T:Object
        {
            if (source == null || string.IsNullOrEmpty(referencePath))
            {
                ErrorFormat(context,"<PlatformCopyRefRule>尝试查找fallback路径时，源对象或路径无效:{0} {1}", source, referencePath);
                return null;
            }
            
            bool isMaterial = source is Material;
            bool isMesh = source is Mesh;
            
            if (isMaterial == false && isMesh == false)
            {
                ErrorFormat(context,"<PlatformCopyRefRule>尝试查找fallback路径时，源对象不是Material或Mesh:{0} {1}", source, referencePath);
                return null;
            }

            //查找匹配的路径
            for (int fallbackIdx = 0; fallbackIdx < fallbackSearchRules.Length; fallbackIdx++)
            {
                var fallbackConf = fallbackSearchRules[fallbackIdx];
                if (fallbackConf.matchingType == WhiteList.MatchingType.Special)
                {
                    //先找后缀，
                    //找到.分隔符，然后查找xxx_prefix.xxx是否存在
                    var pointIdx = referencePath.LastIndexOf(".", StringComparison.Ordinal);
                    if (pointIdx < 0)
                    {
                        continue;
                    }

                    var ext = Path.GetExtension(referencePath);
                    var testPath = referencePath.Substring(0, pointIdx) + fallbackConf.configStr + ext;
                    if (!File.Exists(testPath))
                    {
                        continue;
                    }

                    //再找Lod
                    var sourceName = source.name;

                    Object[] assets = AssetDatabase.LoadAllAssetsAtPath(testPath);
                    foreach (var testAsset in assets)
                    {
                        if (testAsset == null || testAsset.GetType() != typeof(T))
                        {
                            continue;
                        }

                        //直接找到同名的
                        var testName = testAsset.name;
                        if (testName == sourceName)
                        {
                            StepOptimizePlatformRes.logger.InfoFormat(
                                "<PlatformCopyRefRule>找到匹配原始名称的fallback资产:{0} -> {1}", sourceName, testName);
                            return testAsset as T;
                        } 
                        //再找前缀匹配的
                        //先看是否是_Lod结尾的
                        var match = Regex.Match(sourceName, "_Lod\\d+$", RegexOptions.IgnoreCase);
                        if (match.Success)
                        {
                            var sub_testPath = sourceName.Insert(match.Index, fallbackConf.configStr);
                            if (testName.Equals(sub_testPath, StringComparison.OrdinalIgnoreCase))
                            {
                                //找到这个中缀资产
                                StepOptimizePlatformRes.logger.InfoFormat(
                                    "<PlatformCopyRefRule>找到匹配的fallback资产:{0} -> {1}", sourceName, testName);
                                return testAsset as T;
                            }
                        }
                        else
                        {
                            //否则找后缀的
                            var sub_testPath = sourceName + fallbackConf.configStr;
                            if (testName.Equals(sub_testPath, StringComparison.OrdinalIgnoreCase))
                            {
                                //找到这个中缀资产
                                StepOptimizePlatformRes.logger.InfoFormat(
                                    "<PlatformCopyRefRule>找到匹配的fallback资产:{0} -> {1}", sourceName, testName);
                                return testAsset as T;
                            }
                        }
                    }
                }
                else
                {
                    for (int regidx = 0; regidx < fallbackConf.regexList.Count; regidx++)
                    {
                        var regexStr = fallbackConf.regexList[regidx];
                        //后缀
                        if (regexStr.stringConfig == 1)
                        {
                            //找到.分隔符，然后查找xxx_prefix.xxx是否存在
                            var pointIdx = referencePath.LastIndexOf(".", StringComparison.Ordinal);
                            if (pointIdx < 0)
                            {
                                continue;
                            }

                            var ext = Path.GetExtension(referencePath);
                            var testPath = referencePath.Substring(0, pointIdx) + regexStr.str + ext;
                            if (!File.Exists(testPath))
                            {
                                continue;
                            }

                            var sourceName = source.name;
                            
                            var sub_testPath = sourceName +regexStr.str;

                            Object[] assets = AssetDatabase.LoadAllAssetsAtPath(testPath);
                            foreach (var testAsset in assets)
                            {
                                if (testAsset == null || testAsset.GetType() != typeof(T))
                                {
                                    continue;
                                }

                                //直接找到同名的
                                var testName = testAsset.name;
                                if (testName == sourceName)
                                {
                                    StepOptimizePlatformRes.logger.InfoFormat(
                                        "<PlatformCopyRefRule>找到匹配原始名称的fallback资产:{0} -> {1}", sourceName, testName);
                                    return testAsset as T;
                                } 
                                //再找后缀匹配的
                                if(testName.Equals(sub_testPath, StringComparison.OrdinalIgnoreCase))
                                {
                                    StepOptimizePlatformRes.logger.InfoFormat(
                                        "<PlatformCopyRefRule>找到匹配后缀的fallback资产:{0} -> {1}", sourceName, testName);
                                    return testAsset as T;
                                }
                            }
                        }
                        //前缀
                        else if (regexStr.stringConfig == 3)
                        {
                            //查找prefix xxxx.xxxx是否存在
                            var testPath = regexStr.str + referencePath;
                            if (!File.Exists(testPath))
                            {
                                continue;
                            }

                            var sourceName = source.name;
                            var sub_testPath = regexStr.str +sourceName;

                            Object[] assets = AssetDatabase.LoadAllAssetsAtPath(testPath);
                            foreach (var testAsset in assets)
                            {
                                if (testAsset == null || testAsset.GetType() != typeof(T))
                                {
                                    continue;
                                }

                                //直接找到同名的
                                var testName = testAsset.name;
                                if (testName == sourceName)
                                {
                                    StepOptimizePlatformRes.logger.InfoFormat(
                                        "<PlatformCopyRefRule>找到匹配原始名称的fallback资产:{0} -> {1}", sourceName, testName);
                                    return testAsset as T;
                                } 
                                //再找前缀匹配的
                                if(testName.Equals(sub_testPath, StringComparison.OrdinalIgnoreCase))
                                {
                                    StepOptimizePlatformRes.logger.InfoFormat(
                                        "<PlatformCopyRefRule>找到匹配前缀的fallback资产:{0} -> {1}", sourceName, testName);
                                    return testAsset as T;
                                }
                            }
                        }
                    }
                }
                

            }

            return null;
        }
        
        private string TryFindReplaceSourcePath(PackInputParams packInputParams)
        {
            //查找匹配的路径
            string processSourcePath = packInputParams.processSourcePath;
            for (int idx = 0; idx < copySearchRule.Length; idx++)
            {
                var pair = copySearchRule[idx];
                if (string.IsNullOrEmpty(pair.cpySrc) || string.IsNullOrEmpty(pair.cpyDst))
                {
                    continue;
                }
                
                processSourcePath = processSourcePath.Replace(pair.cpySrc, pair.cpyDst);
            }

            //还是原始资产
            if (processSourcePath == packInputParams.processSourcePath)
            {
                StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>没有找到替换路径规则,原始路径和替换路径相同:{0}", processSourcePath);
                return null;
            }
            else
            {
                if (!File.Exists(processSourcePath))
                {
                    StepOptimizePlatformRes.logger.InfoFormat("<PlatformCopyRefRule>尝试查找替换的源路径不存在:{0}", processSourcePath);
                    return null;
                }

                return processSourcePath;
            }
            
            return null;
        }
        
        private GameObject TryFindReplaceGo(PackInputParams packInputParams)
        {
            //查找匹配的路径
            string processSourcePath = TryFindReplaceSourcePath(packInputParams);
            if (string.IsNullOrEmpty(processSourcePath))
            {
                //没有找到替换路径
                return null;
            }
            GameObject asset = AssetDatabase.LoadAssetAtPath<GameObject>(processSourcePath);
            if (asset != null)
            {
                return asset;
            }
            else
            {
                ErrorFormat(packInputParams.context,"<PlatformCopyRefRule>尝试查找替换的源路径不存在:{0}", processSourcePath);
            }
            
            return null;
        }

        private void StepProcessMeshFilter(PlatformPackContext context, Action<MeshFilter,string,Mesh> callback)
        {
            if(callback == null)
            {
                ErrorFormat(context,"<PlatformCopyRefRule> StepProcessMeshFilter callback is null");
                return;
            }
            
            foreach (var targetComponent in CachedMeshFilters)
            {
                if (targetComponent.sharedMesh == null)
                {
                    var transformPath = PlatformPackUtils.GetTransformPath(targetComponent.transform);
                    StepOptimizePlatformRes.logger.WarnFormat("<PlatformCopyRefRule> fallback 目标对象没有MeshFilter或Mesh:{0} ",transformPath);
                    continue;
                }

                string meshPath = AssetDatabase.GetAssetPath(targetComponent.sharedMesh);
                Mesh fallbackMesh = TryFindFallbackPath(context,targetComponent.sharedMesh, meshPath) ;

                string fallbackPath = "";
                if (fallbackMesh)
                {
                    fallbackPath = AssetDatabase.GetAssetPath(fallbackMesh);
                }

                callback(targetComponent,fallbackPath, fallbackMesh);
            }
        }
        
        private void StepProcessMeshRenderer(PlatformPackContext context, Func<MeshRenderer,string,Material,bool> callback)
        {
            if(callback == null)
            {
                ErrorFormat(context,"<PlatformCopyRefRule> StepProcessMeshRenderer callback is null");
                return;
            }

            foreach (var targetComponent in CachedMeshRenderers)
            {
                var transformPath = PlatformPackUtils.GetTransformPath(targetComponent.transform);
                if (targetComponent.sharedMaterials == null || targetComponent.sharedMaterials.Length == 0)
                {
                    StepOptimizePlatformRes.logger.WarnFormat(
                        "<PlatformCopyRefRule> fallback 目标对象没有sharedMaterials:{0}", transformPath);
                    continue;
                }

                bool materialChanged = false;
                var newMaterials = targetComponent.sharedMaterials;

                for (int materialIdx = 0; materialIdx < newMaterials.Length; materialIdx++)
                {
                    var mat = newMaterials[materialIdx];
                    if (mat == null)
                    {
                        ErrorFormat(context,"<PlatformCopyRefRule> fallback 拷贝MeshRenderer材质引用失败:目标对象的sharedMaterials中有空材质:{0} ", transformPath);
                        continue;
                    }

                    var matPath = AssetDatabase.GetAssetPath(mat);
                    Material fallbackMat = TryFindFallbackPath(context,mat, matPath);
                    
                    string fallbackPath = "";
                    if (fallbackMat)
                    {
                        fallbackPath = AssetDatabase.GetAssetPath(fallbackMat);
                    }

                    if (callback(targetComponent, fallbackPath, fallbackMat))
                    {
                        newMaterials[materialIdx] = fallbackMat;
                        materialChanged = true;
                        
                        StepOptimizePlatformRes.logger.InfoFormat(
                            "<PlatformCopyRefRule> fallback 拷贝MeshRenderer材质引用成功:{0} -> {1} transform:{2}",
                            mat, fallbackMat, transformPath);
                    }
                    else
                    {
                        StepOptimizePlatformRes.logger.WarnFormat(
                            "<PlatformCopyRefRule> fallback 未找到匹配的fallback资产 拷贝MeshRenderer材质引用失败:{0} -> {1} transform:{2}",
                            mat, matPath, transformPath);
                    }
                }

                if (materialChanged)
                {
                    targetComponent.sharedMaterials = newMaterials;
                    StepOptimizePlatformRes.logger.InfoFormat(
                        "<PlatformCopyRefRule> fallback 更新MeshRenderer材质引用成功:{0} transform:{1}",
                        targetComponent, transformPath);
                }
            }
        }

        private void StepProcessSkinnedMeshRenderer(PlatformPackContext context, Func<SkinnedMeshRenderer, string, Object, bool> callback)
        {
            if(callback == null)
            {
                ErrorFormat(context,"<PlatformCopyRefRule> StepProcessSkinnedMeshRenderer callback is null");
                return;
            }
            
            bool includeSkinnedMeshRd_material = Array.IndexOf(copyRefTypes, ECopyRefType.SkinnedMeshRenderer_Mat) != -1;
            bool includeSkinnedMeshRd_mesh = Array.IndexOf(copyRefTypes, ECopyRefType.SkinnedMeshRenderer_Mesh) != -1;

            foreach (var targetComponent in CachedSkinnedMeshRenderers)
            {
                var transformPath = PlatformPackUtils.GetTransformPath(targetComponent.transform);
                if (includeSkinnedMeshRd_mesh)
                {
                    if (targetComponent.sharedMesh == null)
                    {
                        StepOptimizePlatformRes.logger.WarnFormat(
                            "<PlatformCopyRefRule> fallback 目标对象没有sharedMesh:{0} ",transformPath);
                        continue;
                    }

                    var meshPath = AssetDatabase.GetAssetPath(targetComponent.sharedMesh);
                    Mesh fallbackMesh = TryFindFallbackPath(context,targetComponent.sharedMesh, meshPath);
                                        
                    string fallbackPath = "";
                    if (fallbackMesh)
                    {
                        fallbackPath = AssetDatabase.GetAssetPath(fallbackMesh);
                    }

                    if (callback(targetComponent,fallbackPath,fallbackMesh))
                    {
                        StepOptimizePlatformRes.logger.InfoFormat(
                            "<PlatformCopyRefRule> fallback 拷贝SkinnedMeshRenderer_Mesh引用成功:{0} -> {1} transform:{2}",
                            targetComponent.sharedMesh, fallbackMesh, transformPath);
                    }
                    else
                    {
                        StepOptimizePlatformRes.logger.WarnFormat(
                            "<PlatformCopyRefRule> fallback 未找到匹配的fallback资产 拷贝SkinnedMeshRenderer_Mesh引用失败:{0} -> {1} transform:{2}",
                            targetComponent.sharedMesh, meshPath, transformPath);
                    }

                }

                if (includeSkinnedMeshRd_material)
                {
                    if (targetComponent.sharedMaterials == null || targetComponent.sharedMaterials.Length == 0)
                    {
                        StepOptimizePlatformRes.logger.WarnFormat(
                            "<PlatformCopyRefRule> fallback 目标对象没有sharedMaterials:{0}", transformPath);
                        continue;
                    }

                    bool materialChanged = false;
                    var newMaterials = targetComponent.sharedMaterials;

                    for (int materialIdx = 0; materialIdx < newMaterials.Length; materialIdx++)
                    {
                        var mat = newMaterials[materialIdx];
                        if (mat == null)
                        {
                            ErrorFormat(context,"<PlatformCopyRefRule> fallback 拷贝SkinnedMeshRenderer_Mat引用失败:目标对象的sharedMaterials中有空材质:{0} ", transformPath);
                            continue;
                        }

                        string matPath = AssetDatabase.GetAssetPath(mat);
                        Material fallbackMat = TryFindFallbackPath(context,mat, matPath);
                        
                        string fallbackPath = "";
                        if (fallbackMat)
                        {
                            fallbackPath = AssetDatabase.GetAssetPath(fallbackMat);
                        }
                        
                        if (callback(targetComponent,fallbackPath,fallbackMat))
                        {
                            newMaterials[materialIdx] = fallbackMat;
                            materialChanged = true;
                            StepOptimizePlatformRes.logger.InfoFormat(
                                "<PlatformCopyRefRule> fallback 拷贝SkinnedMeshRenderer_Mat引用成功:{0} -> {1} transform:{2}",
                                mat, fallbackMat, transformPath);
                        }
                        else
                        {
                            StepOptimizePlatformRes.logger.WarnFormat(
                                "<PlatformCopyRefRule> fallback 未找到匹配的fallback资产 拷贝SkinnedMeshRenderer_Mat引用失败:{0} -> {1} transform:{2}",
                                mat, matPath, transformPath);
                        }
                    }


                    if (materialChanged)
                    {
                        targetComponent.sharedMaterials = newMaterials;
                        StepOptimizePlatformRes.logger.InfoFormat(
                            "<PlatformCopyRefRule>更新SkinnedMeshRenderer材质引用成功:{0} transform:{1}",
                            targetComponent, transformPath);
                    }
                }
            }
        }
    }
}