﻿using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Share.Framework;

namespace WizardGames.Soc.Common.Component
{
    [Component(ComponentId = EComponentIdEnum.TerritoryBase, SyncRange = ESyncRange.All, ClassLod = CustomLod.LOD_MEDIUM)]
    public partial class TerritoryBaseComponent : ComponentBase
    {
        /// <summary>
        /// 领地柜实体Id
        /// </summary>
        [CustomField(SyncRange = ESyncRange.All, IsPersistent = true, FieldLod = CustomLod.LOD_MEDIUM)]
        private long _partEntityId;
        /// <summary>
        /// 所述格子Entity的Id
        /// </summary>
        [CustomField(SyncRange = ESyncRange.UnityDs, IsPersistent = true)]
        private long _gridId;

        /// <summary>
        /// 刷新类型
        /// </summary>
        [CustomField(SyncRange = ESyncRange.All, IsPersistent = true, FieldLod = CustomLod.LOD_MEDIUM)]
        private int _spawnType;

        /// <summary>
        /// 位置信息
        /// </summary>
        [CustomField(SyncRange = ESyncRange.All, IsPersistent = true, FieldLod = CustomLod.LOD_MEDIUM)]
        private CustomTypeList<TerritoryCenterLocationInfo> _locList;

        /// <summary>
        /// 创建时间
        /// </summary>
        [CustomField(SyncRange = ESyncRange.All, IsPersistent = true, FieldLod = CustomLod.LOD_MEDIUM)]
        private long _createTimeStamp;

        /// <summary>
        /// 创建者
        /// </summary>
        [CustomField(SyncRange = ESyncRange.All, IsPersistent = true, FieldLod = CustomLod.LOD_MEDIUM)]
        private ulong _creatorRoleId;

        /// <summary>
        /// 该领地管辖的PartEntity的Id
        /// </summary>
        [CustomField(SyncRange = ESyncRange.LocalOnly, IsPersistent = true)]
        private BasicTypeHashSet<long> _normalPartSet;

        /// <summary>
        /// 该领地管辖的PartEntity的数量
        /// </summary>
        [CustomField(SyncRange = ESyncRange.All, IsPersistent = true, FieldLod = CustomLod.LOD_MEDIUM)]
        private int _normalPartCount;

        /// <summary>
        /// 移动次数
        /// </summary>
        [CustomField(SyncRange = ESyncRange.Clients, IsPersistent = true, FieldLod = CustomLod.LOD_MEDIUM)]
        private int _movedTimes;

        /// <summary>
        /// 个人建筑数量
        /// </summary>
        [CustomField(SyncRange = ESyncRange.All, IsPersistent = false, FieldLod = CustomLod.LOD_MEDIUM)]
        private PartLimitInfo _partLimitInfo;

        #region 一键升级
        /// <summary>
        /// 上一次一键升级时间
        /// </summary>
        [CustomField(SyncRange = ESyncRange.Clients, IsPersistent = true, FieldLod = CustomLod.LOD_MEDIUM)]
        private long _lastBatchUpgradeTs;

        #endregion

        /// <summary>
        /// 当前管辖的美缝数量
        /// </summary>
        [CustomField(SyncRange = ESyncRange.All, IsPersistent = true)]
        protected CustomValueDictionary<long, ConstructionGapLinkInfo> _gapLinkInfoDic;

        /// <summary>
        /// 需要与工具柜一起摧毁的建筑Id
        /// </summary>
        [CustomField(SyncRange = ESyncRange.LocalOnly, IsPersistent = true)]
        protected BasicTypeList<long> _partsDestroyWithToolcupBoard;

        /// <summary>
        /// 领地残骸EntityIds
        /// </summary>
        [CustomField(SyncRange = ESyncRange.UnityDs, IsPersistent = true)]
        private BasicTypeHashSet<long> _debrisEntityIds;

        /// <summary>
        /// 领地残骸消失时间戳 ProcessEntity.Instance.SecSinceStartup
        /// </summary>
        [CustomField(SyncRange = ESyncRange.Clients, IsPersistent = true)]
        private uint _toolCupboardDebrisDestroyTs;
    }
}
