using UnityEngine;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Manager;

namespace WizardGames.Soc.SocClient
{
    public enum EClientChatCDType
    {
        Normal,
        GameTeam,
        LobbyTeam,
        Appointment
    }
    public class ClientChatRoom
    {
        public bool visLock = false;
        private string channelId;
        public string ChannelId
        {
            get
            {
                return channelId;
            }
            set
            {
                channelId = value;
                ChannelType = ChatChannelData.GetChannelType(channelId);
            }
        }
        public Deque_RandomAccess<ClientChatMessage> chatMessages;
        public ClientChatMessage finalMsg;

        public int MessageCount => chatMessages.Count;
        public uint UnseenCount => RemoteSeq > LastReadSeq ? RemoteSeq - LastReadSeq : 0;
        public uint UnRecvCount => RemoteSeq > CurrentSeq ? RemoteSeq - CurrentSeq : 0;
        public uint CurrentSeq => (uint)(chatMessages.Offset + chatMessages.Count - 1);
        //当前消息是否是简要消息
        public bool IsMessageBrief = false;

        public bool haveNotSeenTeamInvite = false;
        public uint RemoteSeq = 0;
        public uint LastReadSeq = 0;
        public float nxtSendTime;
        //额外三个组队分享按钮的时间
        public float nxtSendTime_GameTeam;
        public float nxtSendTime_LobbyTeam;
        public float nxtSendTime_Appointment;

        public EChatChannelType ChannelType { get; private set; }

        public ClientChatRoom(uint offset = 1)
        {
            chatMessages = new(Mc.Tables.TbGlobalConfig.ChannelMaxChatMessage, offset);
        }

        public void ReceiveMsg(ClientChatMessage msg)
        {
            while (chatMessages.Count >= chatMessages.Capacity && chatMessages.Count > 0)
            {
                chatMessages.PopFront();
            }
            msg.Id = (uint)chatMessages.PushBack(msg);
            UpdateFinalMsg(msg);

            if (msg.msgType == EClientChatMessageType.TeamRecruitMessage)
            {
                //暂不需要
                //this.haveNotSeenTeamInvite = true;
            }
        }

        public string GetRoomName(bool withRelationship = false)
        {
            if (this.ChannelType == EChatChannelType.Personal)
            {
                var roomName = Mc.Chat.GetRoleInfo(ulong.Parse(ChannelId))?.roleName ?? ChannelId.ToString();
                if (!withRelationship) { return roomName; }
                else
                {
                    var relationship = Mc.LobbyFriend.IsFriend(ChannelId.ToString()) ? LanguageManager.GetTextConst(LanguageConst.ChatIsFriend) : LanguageManager.GetTextConst(LanguageConst.ChatIsStranger);
                    return $"{roomName}{relationship}";
                }
            }
            else if (this.ChannelType == EChatChannelType.Tribe)
            {
                var tribeInfo = Mc.Tribe.GetMyTribeInfoByInGameGroupId(ChannelId);
                return tribeInfo.Name;
            }
            else
            {
                return ChatChannelData.GetChannelName(ChannelId);
            }
        }

        public string GetRoomRelationship()
        {
            if (ChatChannelData.GetChannelType(ChannelId) != EChatChannelType.Personal) return string.Empty;
            var relationship = Mc.LobbyFriend.IsFriend(ChannelId.ToString()) ? LanguageManager.GetTextConst(LanguageConst.ChatIsFriend) : LanguageManager.GetTextConst(LanguageConst.ChatIsStranger);
            return relationship;
        }

        public void UpdateFinalMsg(ClientChatMessage msg) => finalMsg = msg;

        public void ClearUnseen()
        {
            LastReadSeq = CurrentSeq;
            RemoteSeq = CurrentSeq;
            haveNotSeenTeamInvite = false;
            Mc.Chat.RemoveRedUnSeen(ChannelId);
        }

        public void ClearRoom()
        {
            chatMessages.Clear();
        }
        /// <summary>
        /// 发送消息
        /// </summary>
        /// <param name="msgContent">消息内容</param>
        /// <param name="msgMeta">消息元数据，防和谐</param>
        /// <returns></returns>
        public bool SendMessage(string msgContent, string msgMeta = null, EClientChatCDType cDType = EClientChatCDType.Normal)
        {
            if (nxtSendTime > Time.time) { return false; }
            if (string.IsNullOrEmpty(msgContent)) { return false; }
            switch (cDType)
            {
                case EClientChatCDType.Normal:
                    nxtSendTime = Time.time + ChatChannelData.GetSendMessageCD(ChannelId);
                    break;
                case EClientChatCDType.GameTeam:
                    nxtSendTime_GameTeam = Time.time + ChatChannelData.GetSendMessageCD(ChannelId);
                    break;
                case EClientChatCDType.LobbyTeam:
                    nxtSendTime_LobbyTeam = Time.time + ChatChannelData.GetSendMessageCD(ChannelId);
                    break;
                case EClientChatCDType.Appointment:
                    nxtSendTime_Appointment = Time.time + ChatChannelData.GetSendMessageCD(ChannelId);
                    break;
            }
            switch (ChannelType)
            {
                case EChatChannelType.Team:
                    Mc.Chat.RemoteCallSendTeamChat(msgContent);
                    break;
                case EChatChannelType.World:
                    Mc.Chat.RemoteCallSendWorldChat(msgContent);
                    break;
                case EChatChannelType.Appointment:
                    Mc.Chat.RemoteCallSendAppointmentChat(msgContent);
                    break;
                case EChatChannelType.Personal:
                    Mc.Chat.RemoteCallSendPrivateChat(ulong.Parse(ChannelId), msgContent);
                    break;
                case EChatChannelType.Tribe:
                    var tribeInfo = Mc.Tribe.GetMyTribeInfoByInGameGroupId(ChannelId);
                    Mc.Tribe.SendMessage(tribeInfo.TribeID, msgContent, msgMeta);
                    break;
                default:
                    break;
            }
            return true;
        }

        public bool MessageUpdate()
        {
            // 存在未接收的消息，需要发送Get
            if (UnRecvCount > 0)
            {
                switch (ChannelId)
                {
                    case "1":
                        Mc.Chat.RemoteCallGetTeamChatMessage();
                        break;
                    case "2":
                        break;
                }
            }
            // 存在未与远端同步已读的消息，需要设置已读
            else if (ChannelId != ChatChannelData.channelWorldId && LastReadSeq < CurrentSeq)
            {
                //SetMessageRead
                Mc.Chat.RemoteCallSetMessageRead(ChannelId, CurrentSeq);
                RemoteSeq = CurrentSeq;
            }
            return UnRecvCount > 0;
        }
        /// <summary>
        /// 更新逻辑，当前房间被激活时有效
        /// FPS 10
        /// </summary>
        internal void Update()
        {

        }
    }
}
