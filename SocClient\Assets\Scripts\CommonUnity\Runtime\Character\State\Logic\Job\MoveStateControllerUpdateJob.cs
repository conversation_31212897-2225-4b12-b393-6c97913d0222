using Unity.Burst;
using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.State.Character;

namespace WizardGames.Soc.Common.Unity.Character.Job
{
#if !UNITY_EDITOR
    [BurstCompile]
#endif
    public struct MoveStateControllerUpdateJob : IJobParallelFor
    {
        public NativeArray<StateUpdateJobData> StateUpdateJobDataArray;
        public NativeArray<StateUpdateResultData> StateUpdateResultDataArray;

        // public CharacterBreakJobData CharacterBreakJobData;
        public CharacterParameterJobData CP_Tb;
        public bool StressTest;
        
        public void Execute(int index)
        {
            StateUpdateJobData jobData = StateUpdateJobDataArray[index];
            if (!jobData.HasStateJob)
            {
                return;
            }

            jobData.StressTest = StressTest;
            StateUpdateResultData resultData = new StateUpdateResultData()
            {
                BreakState = -1,
                SubBreakState = -1,
                DeltaTime = jobData.Interval == 0 ? 0.033f : jobData.Interval / 1000f,
            };
            PlayerStateJobData playerStateJobData = jobData.PlayerState;

            int nextState = -1;
            int nextSubState = -1;
            // int stateValue = playerStateJobData.CurStateValue;
            // int nextState = CharacterBreakJobData.GetBreakValue(stateValue, playerStateJobData);
            // resultData.BreakState = nextState;
            //
            // int subStateValue = playerStateJobData.SubStateValue;
            // int nextSubState = CharacterBreakJobData.GetBreakValue(subStateValue, playerStateJobData);
            // resultData.SubBreakState = nextSubState;
            //
            // if (nextState != -1)
            // {
            //     StateUpdateResultDataArray[index] = resultData;
            //     return;
            // }
            var oldWorldSpeed = jobData.WantSpeed;
            bool support = true;
            switch (playerStateJobData.MoveState)
            {
                case PlayerMoveStateEnum.MoveIdle:
                    //nextState = jobData.MoveIdleUpdate(CP_Tb, ref resultData);
                    SpeedFormulaUtility.HandleSpeed(CP_Tb, jobData, ref resultData);
                    break;
                case PlayerMoveStateEnum.Run:
                    //nextState = jobData.MoveRunUpdate(CP_Tb, ref resultData);
                    SpeedFormulaUtility.HandleSpeed(CP_Tb, jobData, ref resultData);
                    break;
                case PlayerMoveStateEnum.Sprint:
                    //nextState = jobData.MoveRunUpdate(CP_Tb, ref resultData);
                    SpeedFormulaUtility.HandleSpeed(CP_Tb, jobData, ref resultData);
                    break;
                case PlayerMoveStateEnum.Fall:
                    //nextState = jobData.MoveFallUpdate(CP_Tb, ref resultData);
                    SpeedFormulaUtility.HandleSpeed(CP_Tb, jobData, ref resultData);
                    break;
                case PlayerMoveStateEnum.Fly:
                    //nextState = jobData.MoveFlyUpdate(CP_Tb, ref resultData);
                    SpeedFormulaUtility.HandleSpeed(CP_Tb, jobData, ref resultData);
                    break;
                case PlayerMoveStateEnum.Observer:
                    //nextState = jobData.MoveObserverUpdate(CP_Tb, ref resultData);
                    SpeedFormulaUtility.HandleSpeed(CP_Tb, jobData, ref resultData);
                    break;
                case PlayerMoveStateEnum.Jump:
                    if (resultData.SubBreakState == -1)
                    {
                        switch (playerStateJobData.MoveJumpState)
                        {
                            case PlayerMoveJumpStateEnum.EnterJump:
                                //nextSubState = jobData.MoveJumpUpdate(CP_Tb, ref resultData);
                                SpeedFormulaUtility.HandleSpeed(CP_Tb, jobData, ref resultData);
                                break;
                            case PlayerMoveJumpStateEnum.InJump:
                                //nextSubState = jobData.MoveJumpUpdate(CP_Tb, ref resultData);
                                SpeedFormulaUtility.HandleSpeed(CP_Tb, jobData, ref resultData);
                                break;
                            case PlayerMoveJumpStateEnum.LeaveJump:
                                //nextSubState = jobData.MoveFallEnd(ref resultData);
                                SpeedFormulaUtility.HandleSpeed(CP_Tb, jobData, ref resultData);
                                break;
                        }
                    }
                    break;
                case PlayerMoveStateEnum.MoveSwim:
                    if (resultData.SubBreakState == -1)
                    {
                        //nextSubState = jobData.MoveSwimUpdate(CP_Tb, ref resultData);
                        SpeedFormulaUtility.HandleSpeed(CP_Tb, jobData, ref resultData);
                    }
                    break;
                default:
                    {
                        support = false;
                        break;
                    }
            }

            //不支持job的状态，数据应该在主线程更新
            if (support)
            {
                resultData.BreakState = nextState;
                resultData.SubBreakState = nextSubState;
            
                var deltaSpeed = resultData.WantSpeed - oldWorldSpeed;
                resultData.WantAccSpeed = deltaSpeed / resultData.DeltaTime;

                StateUpdateResultDataArray[index] = resultData;
            }
        }
        
    }
}