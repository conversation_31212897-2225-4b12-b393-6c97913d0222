﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WizardGames.Soc.Common.Algorithm;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Data.Plant;
using WizardGames.Soc.Common.Data.Play;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Algorithm;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Plant;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.TimerWheel;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Event;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.Ability;
using WizardGames.Soc.SocWorld.Event;
using WizardGames.Soc.SocWorld.Framework;
using WizardGames.Soc.SocWorld.Main;
using WizardGames.Soc.SocWorld.NodeSystem;
using WizardGames.Soc.SocWorld.NodeSystem.VirtualNode;
using WizardGames.Soc.SocWorld.Spawn;
using WizardGames.Soc.SocWorld.WorldCommon;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.Common.Component
{
    [HotfixClass]
    public static partial class PlantBoxComponentHotfix
    {
        [Hotfix]
        public static void Init(this PlantBoxComponent self)
        {
            BaseInit.Invoke(self);
            self.slots = new ItemContainerNode(ContainerConst.PlantBoxSlots, StorageContainerConst.PlantBoxSlot, NodeConst.PlantBoxContainerNodeId);
            self.slots.Capacity = McCommon.Tables.TbPlantBoxConfig.GetOrDefault(self.templateId).Capacity;
            self.Root.AddChildNode(self.slots);
            // IMP：默认增加一个Timer 由于时间较长间隔1分钟。回调函数中会先判断当前植物个数。没有加跳过了。不会有性能问题
            AddPlantTick(self);
        }

        [Hotfix]
        public static void InitFromDb(this PlantBoxComponent self)
        {
            BaseInitFromDb.Invoke(self);
            self.slots = self.Root.GetChildNode(StorageContainerConst.PlantBoxSlot) as ItemContainerNode;
        }

        [Hotfix]
        public static void PostInit(this PlantBoxComponent self, bool isLoadFromDb)
        {
            BasePostInit.Invoke(self, isLoadFromDb);
            PlantBoxComponent.globalPlantBoxIds.Add(self.ParentId);

            self.AddAbility(OnDropWaterAbilityHotfix);

            // 初始化温度范围
            if (PlantBoxComponent.temperatureRange == null && PlantBoxComponent.temperatureRate == null)
            {
                PlantBoxComponent.temperatureRange = new();
                PlantBoxComponent.temperatureRate = new();

                var temperatureRange = McCommon.Tables.TbPlantConstConfig.TemperatureRange;
                var temperatureRate = McCommon.Tables.TbPlantConstConfig.TemperatureRateRange;
                for (int i = 0; i < temperatureRange.Length - 1; i++)
                {
                    PlantBoxComponent.temperatureRange.Add((temperatureRange[i], temperatureRange[i + 1]));
                    PlantBoxComponent.temperatureRate.Add((temperatureRate[i], temperatureRate[i + 1]));
                }
            }

            var checkRange = McCommon.Tables.TbPlantConstConfig.LightSeekingRange;
            self.checkBound = new Bounds(new Vector3(self.partEntity.PosX, self.partEntity.PosY, self.partEntity.PosZ), new Vector3(checkRange * 2, checkRange * 2, checkRange * 2));

            checkRange = (int)McCommon.Tables.TbPlantConstConfig.TemperatureSeekingRange;
            self.tempraturecheckBound = new Bounds(new Vector3(self.partEntity.PosX, self.partEntity.PosY, self.partEntity.PosZ), new Vector3(checkRange, checkRange, checkRange));
        }

        [Hotfix]
        public static void BeforeRemoveVirtual(this PlantBoxComponent self)
        {
            PlantBoxComponent.globalPlantBoxIds.Remove(self.ParentId);
        }

        [Hotfix(TimerCallback = true)]
        public static void OnPlantTick(PlantBoxComponent self, long _)
        {
            var plantCount = 0;
            foreach (var (_, slot) in self.slots)
            {
                if (slot is PlantNode plant)
                {
                    if (plant.Stage == (int)PlantStage.Harvest)
                        continue;

                    plantCount++;
                }
            }


            if (plantCount != 0)
            {
                var range = McCommon.Tables.TbPlantConstConfig.SunSeekingLength;
                // 异步计算当前位置是否在室内
                RpcEntity.Instance.RemoteCallCheckInRoom(ERpcTarget.Simulator, self.partEntity.PosX, self.partEntity.PosY, self.partEntity.PosZ, range, self.ParentEntity.EntityId);
            }

            AddPlantTick(self);
        }

        public static void OnCheckInRoomCallback(this PlantBoxComponent self, bool ret)
        {
            // 消耗水量
            UpdateWater(self, self.LastTickTime);
            self.context.Reset();
            self.context.IsInRoom = ret;
            self.context.Temprature = GetTemperatureRate(self);
            self.context.LightRate = GetLightRate(self);

            // 计算进度
            UpdatePlantProcess(self, self.LastTickTime);

            // 更新计算标记
            UpdatePlantCalcFlag(self);
            self.LastTickTime = ProcessEntity.Instance.TimeSinceStartup;
        }

        /// <summary>
        /// 补充水分
        /// </summary>
        [RpcHandler(ExposeToClient = true, CallInterval = 0.5f)]
        public static void AddWater(this PlantBoxComponent self)
        {
            var roleId = RpcContext.CurrentRoleId;
            if (!BuildingPrivilegeChecker.Instance.CheckPermissionAndDistance(EPrivilegeType.Password, roleId, self.ParentEntity as PartEntity))
                return;
            self.DoAddWater(roleId);
        }

        /// <summary>
        /// 补充水分 sim调用
        /// </summary>
        [RpcHandler]
        public static void AddWaterOnSimulater(this PlantBoxComponent self, ulong roleId)
        {
            self.DoAddWater(roleId);
        }

        public static void DoAddWater(this PlantBoxComponent self, ulong roleId)
        {
            var playerEntity = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (playerEntity == null || (self.usingRoleId != 0 && roleId != self.usingRoleId))
            {
                playerEntity.ConstructionComp.RemoteCallOnAddWater(ERpcTarget.OwnClient, 0, self.ParentId, (int)EOpCode.ContainerAlreadyOccupied);
                return;
            }

            var limit = McCommon.Tables.TbPlantBoxConfig.GetOrDefault(self.templateId).WaterLimit;
            var addValue = Math.Max(Math.Min(limit - self.Water, McCommon.Tables.TbPlantConstConfig.AddWaterCount), 0);
            if (addValue == 0)
            {
                playerEntity.ConstructionComp.RemoteCallOnAddWater(ERpcTarget.OwnClient, 0, self.ParentId, (int)EOpCode.Success);
                return;
            }

            var waterItemId = McCommon.Tables.TbGlobalConfig.WaterItemId;
            if (waterItemId <= 0 || McCommon.Tables.TbItemConfig.GetOrDefault(waterItemId) == null)
            {
                playerEntity.ConstructionComp.RemoteCallOnAddWater(ERpcTarget.OwnClient, 0, self.ParentId, (int)EOpCode.PlantWaterItemIdError);
                self.Logger.Info($"waterItemId:{waterItemId}, please check global excel");
                return;
            }

            using var ctx = NodeOpContext.GetNew(OpContextConst.LOOTING_PLANT_BOX).SetOpRoleId(roleId);
            using var transaction = EntityTransaction.Start($"Plant-{playerEntity.RoleId}-costwater");
            var opCode = playerEntity.Root.RequireNode(waterItemId, addValue);
            if (opCode != EOpCode.Success && opCode != EOpCode.CountNotEnough)
            {
                transaction.Rollback(OpCodeReason.Reason[opCode]);
                return;
            }

            transaction.Commit();

            var realAddValue = 0;
            foreach (var item in ctx.RequireList)
                if (item is StackableItemNode stackNode)
                    realAddValue += stackNode.Count;

            self.Water += realAddValue;

            // 刷新属性
            foreach (var (_, slot) in self.slots)
            {
                if (slot is PlantNode plant)
                {
                    GetAndUpdateGrowRate(self, plant);
                }
            }

            playerEntity.ConstructionComp.RemoteCallOnAddWater(ERpcTarget.OwnClient, realAddValue, self.ParentId, (int)EOpCode.Success);

            EntityStaticCallback<PlayerEntity>.InvokeStaticCallback(playerEntity, new FinishActionEvent(McCommon.Tables.TbQuestConst.Watering));
        }

        /// <summary>
        /// 补充肥料
        /// </summary>
        /// <param name="indexes">位置列表</param>
        [RpcHandler(ExposeToClient = true, CallInterval = 0.5f)]
        public static void AddManure(this PlantBoxComponent self, BasicTypeList<int> indexes)
        {
            var roleId = RpcContext.CurrentRoleId;
            var playerEntity = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (playerEntity == null || roleId != self.usingRoleId)
            {
                playerEntity.ConstructionComp.RemoteCallOnAddManure(ERpcTarget.OwnClient, indexes, self.ParentId, (int)EOpCode.ContainerAlreadyOccupied);
                return;
            }
            if (!BuildingPrivilegeChecker.Instance.CheckPermissionAndDistance(EPrivilegeType.Password, playerEntity, self.ParentEntity as PartEntity))
                return;
            // 检查所有施肥位置的状态
            if (indexes.Count == 0)
            {
                playerEntity.ConstructionComp.RemoteCallOnAddManure(ERpcTarget.OwnClient, indexes, self.ParentId, (int)EOpCode.ArgsError);
                return;
            }

            foreach (var index in indexes)
            {
                var solt = self.slots.GetChildNode(index) as PlantNode;
                if (solt == null)
                {
                    self.Logger.Info($"slot:{index}, not find plant");
                    playerEntity.ConstructionComp.RemoteCallOnAddManure(ERpcTarget.OwnClient, indexes, self.ParentId, (int)EOpCode.ArgsError);
                    return;
                }

                if (solt.IsManure)
                {
                    self.Logger.Info($"slot:{index}, have already manure");
                    playerEntity.ConstructionComp.RemoteCallOnAddManure(ERpcTarget.OwnClient, indexes, self.ParentId, (int)EOpCode.PlantHaveManure);
                    return;
                }
            }

            var haveCount = playerEntity.Root.GetNodeValByPath(NodePathConst.VPlayerInventoryMain, McCommon.Tables.TbPlantConstConfig.ManureId);
            if (haveCount < indexes.Count)
            {
                self.Logger.Info($"manure:{haveCount} not enough");
                return;
            }

            using var ctx = NodeOpContext.GetNew(OpContextConst.LOOTING_PLANT_BOX).SetOpRoleId(playerEntity.RoleId);
            var opCode = playerEntity.Root.RequireNode(McCommon.Tables.TbPlantConstConfig.ManureId, indexes.Count);
            if (opCode != EOpCode.Success)
            {
                playerEntity.ConstructionComp.RemoteCallOnAddManure(ERpcTarget.OwnClient, indexes, self.ParentId, (int)opCode);
                return;
            }

            foreach (var index in indexes)
            {
                var slot = self.slots.GetChildNode(index) as PlantNode;
                slot.IsManure = true;
                GetAndUpdateGrowRate(self, slot);
                SetTlog(playerEntity, self.templateId, "Manure", slot);
                self.Logger.Debug($"solt:{index}, add manure success");
            }

            EntityStaticCallback<PlayerEntity>.InvokeStaticCallback(playerEntity, new FinishActionEvent(McCommon.Tables.TbQuestConst.Fertilize));

            playerEntity.ConstructionComp.RemoteCallOnAddManure(ERpcTarget.OwnClient, indexes, self.ParentId, (int)opCode);
        }

        private static EOpCode DoPlant(PlantBoxComponent self, PlayerEntity playerEntity, long srcIndex, long dstIndex)
        {
            var srcPath = new List<long>() { NodeSystemType.SeedBackpackSystem, NodeConst.SeedBackpackContainerNodeId, srcIndex };
            var srcNode = playerEntity.Root.GetNodeByPath(srcPath) as NodeBase;
            if (srcNode == null)
            {
                self.Logger.Warn($"srcPath:{string.Join(",", srcPath)}, fail to get node from player seed backpack");
                return EOpCode.NodeNotExist;
            }

            var seedId = srcNode.BizId;
            var seedConfig = McCommon.Tables.TbPlantSeedConfig.GetOrDefault(seedId);
            if (seedConfig == null)
            {
                self.Logger.Warn($"seedId:{seedId}, fail to get plant seed config");
                return EOpCode.PlantSeedConfigNotFind;
            }

            if (self.slots.Capacity <= dstIndex || dstIndex < 0)
            {
                self.Logger.Warn($"dstIndex:{dstIndex}, invaild plant box index");
                return EOpCode.PlantIndexError;
            }

            var dstPath = new List<long>() { NodeSystemType.PlantSystem, StorageContainerConst.PlantBoxSlot, dstIndex };
            var dstNode = self.rootNodeComponent.GetNodeByPath(dstPath);
            if (dstNode != null)
            {
                self.Logger.Warn($"dstPath:{string.Join(",", dstPath)}, exist node");
                return EOpCode.NodeExists;
            }

            // 先尝试创建
            PlantNode plantNode = null;
            if (srcNode is PlantSeedNode plantSeedNode)
            {
                plantNode = new PlantNode(plantSeedNode, dstIndex);
            }
            else if (srcNode is PlantWildSeedNode plantWildSeedNode)
            {
                plantNode = new PlantNode(plantWildSeedNode, dstIndex);
            }
            else
            {
                self.Logger.Warn($"srcPath:{string.Join(",", srcPath)}, src node is not plant node");
                return EOpCode.NodeExists;
            }

            using var ctx = NodeOpContext.GetNew(OpContextConst.LOOTING_PLANT_BOX).SetOpRoleId(playerEntity.RoleId);
            using var transaction = EntityTransaction.Start($"Plant-{playerEntity.RoleId}-{string.Join(",", srcPath)}-{string.Join(",", dstPath)}");
            var opCode = playerEntity.Root.RequireNode(new NodeOpByIndex(srcPath).WithDetail(new IntCount(1)));
            if (opCode != EOpCode.Success)
            {
                transaction.Rollback(OpCodeReason.Reason[opCode]);
                return opCode;
            }

            GetAndUpdateGrowRate(self, plantNode);

            opCode = self.rootNodeComponent.MergeNode(new NodeOpByIndex(dstPath).WithDetail(new ExistingNode(plantNode)));
            self.Logger.Info($"player:{playerEntity.RoleId}, plant:opCode:{opCode}");

            if (opCode != EOpCode.Success)
            {
                transaction.Rollback(OpCodeReason.Reason[opCode]);
                return opCode;
            }
            transaction.Commit();

            SetTlog(playerEntity, self.templateId, "Plant", plantNode);
            EntityStaticCallback<PlayerEntity>.InvokeStaticCallback(playerEntity, new GrowCropsEvent(seedId, 1));

            return EOpCode.Success;
        }

        /// <summary>
        /// 种植
        /// nodeId 种子NodeId
        /// </summary>
        [RpcHandler(ExposeToClient = true, CallInterval = 0.5f)]
        public static void Plant(this PlantBoxComponent self, List<Alpha3PlantArgs> args)
        {
            var roleId = RpcContext.CurrentRoleId;
            var playerEntity = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (playerEntity == null || roleId != self.usingRoleId)
            {
                self.Logger.Warn($"roleId{roleId}, usingRoleId:{self.usingRoleId}, fail to get player col");
                playerEntity.ConstructionComp.RemoteCallOnPlant(ERpcTarget.OwnClient, null, self.ParentId, (int)EOpCode.ContainerAlreadyOccupied);
                return;
            }
            if (!BuildingPrivilegeChecker.Instance.CheckPermissionAndDistance(EPrivilegeType.Password, playerEntity, self.ParentEntity as PartEntity))
                return;
            var successList = new List<Alpha3PlantArgs>();
            var fail = new Alpha3PlantArgs();
            foreach (var arg in args)
            {
                var srcIndex = arg.SrcIndex;
                var dstIndex = arg.DstIndex;
                var ret = DoPlant(self, playerEntity, srcIndex, dstIndex);
                if (ret != EOpCode.Success)
                {
                    fail.SrcIndex = srcIndex;
                    fail.DstIndex = dstIndex;
                    playerEntity.ConstructionComp.RemoteCallOnPlant(ERpcTarget.OwnClient, successList, self.ParentId, (int)ret);
                    return;
                }
                else
                {
                    successList.Add(new Alpha3PlantArgs() { SrcIndex = srcIndex, DstIndex = dstIndex });
                }
            }

            playerEntity.ConstructionComp.RemoteCallOnPlant(ERpcTarget.OwnClient, successList, self.ParentId, (int)EOpCode.Success);
        }

        private static void HarvestAll(PlantBoxComponent self, PlayerEntity player)
        {
            List<long> indexes = new();
            foreach (var (_, solt) in self.slots)
            {
                if (solt is not PlantNode plant) continue;

                if (plant.Stage != (int)PlantStage.Harvest) continue;

                SetTlog(player, self.templateId, "Harvest", plant);
                indexes.Add(solt.Index);
            }

            if (indexes.Count == 0)
            {
                player.ConstructionComp.RemoteCallOnHarvest(ERpcTarget.OwnClient, -1, self.ParentId, (int)EOpCode.PlantNotInHarvest);
                return;
            }

            using var ctx = NodeOpContext.GetNew(OpContextConst.LOOTING_PLANT_BOX).SetOpRoleId(player.RoleId);
            using var transaction = EntityTransaction.Start($"Plant-Harvest-{player.RoleId}-{string.Join(",", indexes)}");
            var srcPath = new List<long>() { NodeSystemType.PlantSystem, StorageContainerConst.PlantBoxSlot, -1 };
            foreach (var index in indexes)
            {
                srcPath[srcPath.Count - 1] = index;
                var opCode = self.rootNodeComponent.RequireNode(new NodeOpByIndex(srcPath));
                if (opCode != EOpCode.Success)
                {
                    transaction.Rollback(OpCodeReason.Reason[opCode]);
                    self.Logger.Error($"fail to harvest plant");
                    player.ConstructionComp.RemoteCallOnHarvest(ERpcTarget.OwnClient, -1, self.ParentId, (int)EOpCode.TransactionFail);
                    return;
                }

                opCode = HarvestAward(self, player, ctx.RequireList.PopFront().Data as PlantNode);
                if (opCode != EOpCode.Success)
                {
                    transaction.Rollback(OpCodeReason.Reason[opCode]);
                    self.Logger.Error($"fail to harvest plant");
                    player.ConstructionComp.RemoteCallOnHarvest(ERpcTarget.OwnClient, -1, self.ParentId, (int)EOpCode.TransactionFail);
                    return;
                }
            }

            transaction.Commit();
            player.ConstructionComp.RemoteCallOnHarvest(ERpcTarget.OwnClient, -1, self.ParentId, (int)EOpCode.Success);
        }

        private static void HarvestAssign(PlantBoxComponent self, PlayerEntity player, int index)
        {
            var srcPath = new List<long>() { NodeSystemType.PlantSystem, StorageContainerConst.PlantBoxSlot, index };
            var plant = self.Root.GetNodeByPath(srcPath) as PlantNode;
            if (plant == null)
            {
                player.ConstructionComp.RemoteCallOnHarvest(ERpcTarget.OwnClient, index, self.ParentId, (int)EOpCode.ArgsError);
                return;
            }

            if (plant.Stage != (int)PlantStage.Harvest)
            {
                player.ConstructionComp.RemoteCallOnHarvest(ERpcTarget.OwnClient, index, self.ParentId, (int)EOpCode.PlantNotInHarvest);
                return;
            }

            using var ctx = NodeOpContext.GetNew(OpContextConst.LOOTING_PLANT_BOX).SetOpRoleId(player.RoleId);
            using var transaction = EntityTransaction.Start($"Plant-Harvest-{player.RoleId}-{index}");
            var opCode = self.rootNodeComponent.RequireNode(new NodeOpByIndex(srcPath));
            if (opCode != EOpCode.Success)
            {
                transaction.Rollback(OpCodeReason.Reason[opCode]);
                self.Logger.Error($"fail to harvest plant");
                player.ConstructionComp.RemoteCallOnHarvest(ERpcTarget.OwnClient, index, self.ParentId, (int)EOpCode.TransactionFail);
                return;
            }

            opCode = HarvestAward(self, player, ctx.RequireList.PopFront().Data as PlantNode);
            if (opCode != EOpCode.Success)
            {
                transaction.Rollback(OpCodeReason.Reason[opCode]);
                self.Logger.Error($"fail to harvest plant");
                player.ConstructionComp.RemoteCallOnHarvest(ERpcTarget.OwnClient, index, self.ParentId, (int)EOpCode.TransactionFail);
                return;
            }

            transaction.Commit();
            player.ConstructionComp.RemoteCallOnHarvest(ERpcTarget.OwnClient, index, self.ParentId, (int)EOpCode.Success);
            SetTlog(player, self.templateId, "Harvest", plant);
        }

        /// <summary>
        /// 收获
        /// index 指定槽位，-1表示全部收获
        /// </summary>
        [RpcHandler(ExposeToClient = true, CallInterval = 0.5f)]
        public static void Harvest(this PlantBoxComponent self, int index)
        {
            var roleId = RpcContext.CurrentRoleId;
            var playerEntity = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (playerEntity == null || (self.usingRoleId != 0 && roleId != self.usingRoleId))
            {
                self.Logger.Warn($"roleId{roleId}, usingRoleId:{self.usingRoleId}, fail to get player col");
                playerEntity.ConstructionComp.RemoteCallOnHarvest(ERpcTarget.OwnClient, index, self.ParentId, (int)EOpCode.ContainerAlreadyOccupied);
                return;
            }
            if (!BuildingPrivilegeChecker.Instance.CheckPermissionAndDistance(EPrivilegeType.Password, playerEntity, self.ParentEntity as PartEntity))
                return;
            if (index != -1)
                HarvestAssign(self, playerEntity, index);
            else
                HarvestAll(self, playerEntity);
        }

        /// <summary>
        /// 移除
        /// index 指定槽位
        /// </summary>
        [RpcHandler(ExposeToClient = true, CallInterval = 0.5f)]
        public static void Remove(this PlantBoxComponent self, int index)
        {
            var roleId = RpcContext.CurrentRoleId;
            var playerEntity = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (playerEntity == null || roleId != self.usingRoleId)
            {
                self.Logger.Warn($"roleId{roleId}, usingRoleId:{self.usingRoleId}, fail to get player col");
                playerEntity.ConstructionComp.RemoteCallOnRemove(ERpcTarget.OwnClient, index, self.ParentId, (int)EOpCode.ContainerAlreadyOccupied);
                return;
            }
            if (!BuildingPrivilegeChecker.Instance.CheckPermissionAndDistance(EPrivilegeType.Password, playerEntity, self.ParentEntity as PartEntity))
                return;
            var srcPath = new List<long>() { NodeSystemType.PlantSystem, StorageContainerConst.PlantBoxSlot, index };
            var plant = self.Root.GetNodeByPath(srcPath) as PlantNode;
            if (plant == null)
            {
                playerEntity.ConstructionComp.RemoteCallOnRemove(ERpcTarget.OwnClient, index, self.ParentId, (int)EOpCode.ArgsError);
                return;
            }

            using var ctx = NodeOpContext.GetNew("PlantBoxRemove").SetOpRoleId(playerEntity.RoleId);
            var retCode = self.rootNodeComponent.RequireNode(new NodeOpByIndex(srcPath));
            playerEntity.ConstructionComp.RemoteCallOnRemove(ERpcTarget.OwnClient, index, self.ParentId, (int)retCode);
            self.Logger.Info($"remove plant index:{index}, code:{retCode}");
        }

        private static bool CanHybridizeStart(PlantBoxComponent self)
        {
            if (self.recordPlantGeneIds.Count != 0 || self.magicNum != -1 || self.recordPlants.Count != 0 || self.hybridizeIndex != -1)
            {
                self.Logger.Info($"last hybridize is not finish");
                return false;
            }
            return true;
        }

        private static EOpCode ManualHybridizeStartOp(PlantBoxComponent self, long index, BasicTypeList<int> selectIndexes)
        {
            var srcPath = new List<long>() { NodeSystemType.PlantSystem, StorageContainerConst.PlantBoxSlot, index };
            var plant = self.Root.GetNodeByPath(srcPath) as PlantNode;
            if (plant == null) return EOpCode.ArgsError;

            if (plant.Stage != (int)PlantStage.Hybridization) return EOpCode.PlantNotInHybridize;

            if (!CanHybridizeStart(self)) return EOpCode.PlantHybridizeNotFinish;

            var needStages = McCommon.Tables.TbPlantConstConfig.CanHybridizationStage;
            if (needStages.Length == 0)
            {
                self.Logger.Error($"plant hybridize need stage is empty");
                return EOpCode.PlantHybridizeStageNonCompliant;
            }

            // 检查杂交植物是否符合条件
            var minValue = McCommon.Tables.TbPlantConstConfig.HybridizationNeedMinCount;
            var maxValue = McCommon.Tables.TbPlantConstConfig.HybridizationNeedMaxCount;
            if (selectIndexes.Count < minValue || maxValue < selectIndexes.Count)
            {
                self.Logger.Error($"plant hybridize start select index is error, select:{selectIndexes.Count}, min:{minValue}, max:{maxValue}");
                return EOpCode.PlantHybridizeSelectIndexesError;
            }

            var selectPlants = new List<PlantNode>();
            foreach (var selectIndex in selectIndexes)
            {
                if (selectIndex == index) return EOpCode.PlantHybridizeSelectIndexesError;

                var selectPath = new List<long>() { NodeSystemType.PlantSystem, StorageContainerConst.PlantBoxSlot, selectIndex };
                var selectPlant = self.Root.GetNodeByPath(selectPath) as PlantNode;
                if (selectPlant == null) return EOpCode.PlantHybridizeSelectIndexesError;

                if (!needStages.Contains(selectPlant.Stage)) return EOpCode.PlantHybridizeStageNonCompliant;

                selectPlants.Add(selectPlant);
            }

            foreach (var selectPlant in selectPlants)
            {
                self.recordPlantGeneIds.Add(CopyPlantGenes(selectPlant));
                self.recordPlants.Add(selectPlant.Index);
            }

            self.hybridizeIndex = plant.Index;
            self.magicNum = RandomUtil.Instance.Next(0, 10000);

            return EOpCode.Success;
        }

        /// <summary>
        /// 杂交
        /// index 杂交植物
        /// selectIndex 参与杂交植物
        /// slotPoints 各个槽位的加点
        /// </summary>
        [RpcHandler(ExposeToClient = true, CallInterval = 0.5f)]
        public static void ManualHybridizeStart(this PlantBoxComponent self, long index, BasicTypeList<int> selectIndexes)
        {
            var roleId = RpcContext.CurrentRoleId;
            var playerEntity = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (playerEntity == null || roleId != self.usingRoleId)
            {
                self.Logger.Warn($"roleId{roleId}, usingRoleId:{self.usingRoleId}, fail to get player col");
                playerEntity.ConstructionComp.RemoteCallOnManualHybridizeStart(ERpcTarget.OwnClient, index, self.ParentId, (int)EOpCode.ContainerAlreadyOccupied, 0);
                return;
            };
            if (!BuildingPrivilegeChecker.Instance.CheckPermissionAndDistance(EPrivilegeType.Password, playerEntity, self.ParentEntity as PartEntity))
                return;
            playerEntity.ConstructionComp.RemoteCallOnManualHybridizeStart(ERpcTarget.OwnClient, index, self.ParentId, (int)ManualHybridizeStartOp(self, index, selectIndexes),
            PlantUtils.EncryptMagicNum(self.magicNum, McCommon.Tables.TbPlantConstConfig.HybridizationNorNum));
        }

        private static EOpCode DoManualHybridize(PlantBoxComponent self, PlantNode plant, List<Tuple<int, int>> points, PlantMiniGameConfig miniGameConfig)
        {
            PrintPlantGeneIds(self, plant);
            // 总加点数
            int totalCount = 0;
            foreach (var (index, value) in points)
                totalCount += value;

            // 单点增加概率 = 表格配置0-1的值，比如0.3表示30%，总共选择点数为6， 单点加成概率 = 0.3 * 100 / 6 = 5
            float singlePointRate = 1f * miniGameConfig.AddProbability * 100 / totalCount;
            self.Logger.Info($"plant:{plant.Index}, totalCount:{totalCount}, singlePointRate:{singlePointRate}");

            // 统计基因IDS = 杂交植物 + 参与杂交植物
            var geneIds = new List<int>(1 + self.recordPlantGeneIds.Count);
            var rates = new List<float>(1 + self.recordPlantGeneIds.Count);
            for (int i = 0; i < plant.Genes.Count; i++)
            {
                float totalRate = 0f;
                var geneId = plant.Genes[i];
                var config = McCommon.Tables.TbPlantGeneConfig.GetOrDefault(geneId);
                if (config == null)
                {
                    self.Logger.Error($"gene:{geneId}, fail to get plant gene config");
                    return EOpCode.PlantHybridizeGeneIdError;
                }

                geneIds.Add(geneId);
                totalRate += config.CrossbreedWeight;
                rates.Add(config.CrossbreedWeight);

                // 遍历参与的植株的基因概率，获取单列的圆桌总概率
                foreach (var genids in self.recordPlantGeneIds)
                {
                    var selectGeneId = genids[i];
                    var selectConfig = McCommon.Tables.TbPlantGeneConfig.GetOrDefault(selectGeneId);
                    if (selectConfig == null)
                    {
                        self.Logger.Error($"select:{selectGeneId}, fail to get plant gene config");
                        return EOpCode.PlantHybridizeGeneIdError;
                    }

                    geneIds.Add(selectGeneId);
                    totalRate += selectConfig.CrossbreedWeight;
                    rates.Add(selectConfig.CrossbreedWeight);
                }

                self.Logger.Info($"origin rate:{string.Join(",", rates)}, geneIds:{string.Join(",", geneIds)}, totalRate:{totalRate}");

                // 根据小游戏结果重新修正概率，选择加点的位置会增加 %1 + (小游戏结果提供概率 / 总点数 * 加点数值)
                var calcTotalRate = totalRate;
                var onePercentRate = totalRate * 1 / 100;
                var count = points[i].Item2;
                var selectIndex = points[i].Item1;
                if (count != 0)
                {
                    calcTotalRate = 0f;
                    for (int idx = 0; idx < geneIds.Count; idx++)
                    {
                        if (selectIndex == idx)
                        {
                            rates[idx] += onePercentRate;
                            if (miniGameConfig != null)
                            {
                                rates[idx] += count * singlePointRate * onePercentRate;
                            }

                            calcTotalRate += rates[idx];
                        }
                        else
                        {
                            rates[idx] -= onePercentRate / self.recordPlantGeneIds.Count;
                            if (miniGameConfig != null)
                            {
                                rates[idx] -= count * singlePointRate * onePercentRate / self.recordPlantGeneIds.Count;
                            }
                            calcTotalRate += rates[idx];
                        }
                    }
                    self.Logger.Info($"calc static rate:{string.Join(",", rates)}, geneIds:{string.Join(",", geneIds)}, totalRate:{calcTotalRate}");
                }

                // 随机结果
                var randomRate = RandomUtil.Instance.NextSingle() * calcTotalRate;
                var calcRate = 0f;
                for (int k = 0; k < rates.Count; k++)
                {
                    calcRate += rates[k];
                    if (randomRate < calcRate)
                    {
                        plant.Genes[i] = geneIds[k];
                        self.Logger.Info($"randomRate:{randomRate}, totalRate:{calcTotalRate}, randomIndex:{k}");
                        break;
                    }
                }

                geneIds.Clear();
                rates.Clear();
            }

            // 切换阶段，重置生长时间
            plant.GrowTime = 0;
            plant.Stage = (int)PlantStage.Mature;

            ClearRecordInfo(self);
            PrintPlantGeneIds(self, plant);
            return EOpCode.Success;
        }

        private static EOpCode ManualHybridizeFinishOp(PlantBoxComponent self, int magicNum, BasicTypeList<int> pointIndexes, BasicTypeList<int> pointValues, int result)
        {
            // 校验码不符合
            if (self.magicNum == -1 || magicNum != self.magicNum)
            {
                self.Logger.Error($"fail to check magic num, client:{magicNum}, record:{self.magicNum}");
                return EOpCode.ArgsError;
            }

            if (self.recordPlants.Count == 0 || self.recordPlantGeneIds.Count == 0 || self.hybridizeIndex == -1) return EOpCode.PlantHybridizeNotStart;

            // 杂交 和 选中的植物不存在
            var srcPath = new List<long>() { NodeSystemType.PlantSystem, StorageContainerConst.PlantBoxSlot, self.hybridizeIndex };
            var plant = self.Root.GetNodeByPath(srcPath) as PlantNode;
            if (plant == null) return EOpCode.ArgsError;

            foreach (var recordIndex in self.recordPlants)
            {
                var selectPath = new List<long>() { NodeSystemType.PlantSystem, StorageContainerConst.PlantBoxSlot, recordIndex };
                if (self.Root.GetNodeByPath(selectPath) is not PlantNode) return EOpCode.PlantHybridizeSelectNotExist;
            }

            // 校验结果
            var miniGameConfig = McCommon.Tables.TbPlantMiniGameConfig.GetOrDefault(result);
            if (miniGameConfig == null)
            {
                self.Logger.Error($"plant hybridize mini game result:{result} is error, fail to get config");
                return EOpCode.PlantHybridizeMiniGameResultError;
            }

            if (pointIndexes.Count != pointValues.Count || pointIndexes.Count != McCommon.Tables.TbPlantConstConfig.PlantGeneCount)
            {
                self.Logger.Error($"plant hybridize finish client  indexes:{pointIndexes.Count}, values:{pointValues.Count}," +
                    $" limitCount:{McCommon.Tables.TbPlantConstConfig.PlantGeneCount}");
                return EOpCode.ArgsError;
            }

            // 检查加点是否符合条件
            var totalPointValue = 0;
            var limitPoint = McCommon.Tables.TbPlantConstConfig.HybridizationPoint;
            var points = new List<Tuple<int, int>>(pointIndexes.Count);
            for (int i = 0; i < pointIndexes.Count; i++)
            {
                var index = pointIndexes[i];
                var value = pointValues[i];

                // 这里条件是选择的index大于记录的record数量 || 或者选择的index小于0 || 或者选择了植物但是加点为0
                if ((index != -1 && index < 0) || index > self.recordPlants.Count || (value == 0 && index != -1))
                {
                    self.Logger.Error($"plant hybridize index:{index} is error");
                    return EOpCode.PlantHybridizePointError;
                }

                totalPointValue += value;
                points.Add(new Tuple<int, int>(index, value));
            }

            if (totalPointValue > limitPoint || totalPointValue <= 0)
            {
                self.Logger.Error($"plant hybridize point:{totalPointValue} ,limit:{limitPoint}, total point is error");
                return EOpCode.PlantHybridizePointError;
            }

            return DoManualHybridize(self, plant, points, miniGameConfig);
        }

        /// <summary>
        /// 杂交
        /// magicNum 在开始时返回的随机数，需要异或上传
        /// index 杂交植物
        /// selectIndex 参与杂交植物
        /// slotPoints 各个槽位的加点
        /// result 小游戏结果
        /// </summary>
        [RpcHandler(ExposeToClient = true, CallInterval = 0.5f)]
        public static void ManualHybridizeFinish(this PlantBoxComponent self, int magicNum, BasicTypeList<int> pointIndexes, BasicTypeList<int> pointValues, int result)
        {
            var roleId = RpcContext.CurrentRoleId;
            var playerEntity = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (playerEntity == null || roleId != self.usingRoleId)
            {
                self.Logger.Warn($"roleId{roleId}, usingRoleId:{self.usingRoleId}, fail to get player col");
                playerEntity.ConstructionComp.RemoteCallOnManualHybridizeFinish(ERpcTarget.OwnClient, self.hybridizeIndex, self.ParentId, (int)EOpCode.ContainerAlreadyOccupied);
                return;
            };
            if (!BuildingPrivilegeChecker.Instance.CheckPermissionAndDistance(EPrivilegeType.Password, playerEntity, self.ParentEntity as PartEntity))
                return;
            playerEntity.ConstructionComp.RemoteCallOnManualHybridizeFinish(ERpcTarget.OwnClient, self.hybridizeIndex, self.ParentId, (int)ManualHybridizeFinishOp(self, magicNum, pointIndexes, pointValues, result));
        }

        /// <summary>
        /// 清理杂交记录
        /// magicNum start返回的魔法数
        /// </summary>
        /// <param name="magicNum"></param>

        [RpcHandler(ExposeToClient = true, CallInterval = 0.5f)]
        public static void ManualHybridizeClear(this PlantBoxComponent self, int magicNum)
        {
            var roleId = RpcContext.CurrentRoleId;
            var playerEntity = UserManagerEntity.Instance.GetPlayerEntity(roleId);
            if (playerEntity == null || roleId != self.usingRoleId)
            {
                self.Logger.Warn($"roleId{roleId}, usingRoleId:{self.usingRoleId}, fail to get player col");
                return;
            };
            if (!BuildingPrivilegeChecker.Instance.CheckPermissionAndDistance(EPrivilegeType.Password, playerEntity, self.ParentEntity as PartEntity))
                return;
            // 校验码不符合
            if (self.magicNum == -1 || magicNum != self.magicNum)
            {
                playerEntity.ConstructionComp.RemoteCallOnManualHybridizeClear(ERpcTarget.OwnClient, self.ParentId, (int)EOpCode.ArgsError);
                self.Logger.Error($"fail to check magic num, client:{magicNum}, record:{self.magicNum}");
                return;
            }

            ClearRecordInfo(self);
            playerEntity.ConstructionComp.RemoteCallOnManualHybridizeClear(ERpcTarget.OwnClient, self.ParentId, (int)EOpCode.Success);
        }

        internal static string GetDebugInfo(this PlantBoxComponent self)
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine($"当前时间阶段{ServerInstanceEntity.Instance.GetGameTimeStage()}");
            sb.AppendLine($"种植箱温度{GetTemperature(self)}");
            return sb.ToString();
        }

        private static EOpCode HarvestAward(PlantBoxComponent self, PlayerEntity player, PlantNode node)
        {
            var plantSeedConfig = McCommon.Tables.TbPlantSeedConfig.GetOrDefault(node.BizId);
            if (plantSeedConfig != null)
            {
                var awardList = new List<DropParam>() { new(plantSeedConfig.PickupItem, GetHarvestRate(node), 0) };
                if (plantSeedConfig.RemoveDropId != 0)
                {
                    var removeAwardList = DropUtil.GetRewardByDropId(plantSeedConfig.RemoveDropId);
                    if (removeAwardList.Count != 0)
                    {
                        awardList.AddRange(removeAwardList);
                    }
                }

                foreach (var item in awardList)
                {
                    var itemId = item.ItemId;
                    var itemCount = item.Amount;
                    var awardNode = new NodeOpByBizId(itemId, itemCount, item.ItemParam).WithOption(EOversizeOptionEnum.Drop);
                    var code = player.Root.MergeNode(awardNode);
                    if (code != EOpCode.Success)
                    {
                        self.Logger.Info($"MergeNode failed. RoleId {player.RoleId},id {itemId}, count {itemCount}");
                        return EOpCode.PlantHarvestFail;
                    }
                }

                if (plantSeedConfig.DropSeedNum != 0 && plantSeedConfig.SeedItem != 0)
                {
                    var plantSeedNode = new PlantSeedNode(plantSeedConfig.SeedItem, plantSeedConfig.DropSeedNum, node.Genes);
                    var opCode = player.Root.MergeNode(new NodeOpByIndex(NodePathConst.AnywhereInPlayerSeedBackpack).WithOption(EOversizeOptionEnum.Drop).WithDetail(new ExistingNode(plantSeedNode)));
                    if (opCode != EOpCode.Success)
                    {
                        return opCode;
                    }
                }
            }

            return EOpCode.Success;
        }

        private static void UpdateWater(PlantBoxComponent self, long lastTickTime)
        {
            var tickInterval = McCommon.Tables.TbPlantConstConfig.PlantTickInit;
            var passTime = (1f * ProcessEntity.Instance.TimeSinceStartup - lastTickTime) / 1000;
            var calcWater = 0f;
            foreach (var (_, slot) in self.slots)
            {
                if (slot is PlantNode plant)
                {
                    if (plant.Stage == (int)PlantStage.Harvest)
                        continue;

                    var plantSeedConfig = McCommon.Tables.TbPlantSeedConfig.GetOrDefault(plant.BizId);
                    var singleCalcWater = plantSeedConfig.BasicWaterIntake * passTime;
                    var singlePassTime = passTime;
                    if (!plant.HaveCalc)
                    {
                        singlePassTime = (1f * ProcessEntity.Instance.TimeSinceStartup - plant.CreateTime) / 1000;
                        singleCalcWater = plantSeedConfig.BasicWaterIntake * singlePassTime;
                    }

                    calcWater += (singleCalcWater / tickInterval);
                    self.Logger.Debug($"[PlantBox] UpdateWater plant:{plant.ToPrettyString()}, cost:{singleCalcWater}, pass:{singlePassTime}");
                }
            }

            var costWater = (int)MathF.Floor(calcWater + 0.5f);
            self.Water = Math.Max(0, self.Water - costWater);
            self.Logger.Info($"[PlantBox] UpdateWater plantBox:{self.ParentId}, remain:{self.Water}-cost:{costWater}");
        }

        private static float GetTemperatureRate(PlantBoxComponent self)
        {
            var temperature = GetTemperature(self);
            var findRate = 0f;
            for (int i = 0; i < PlantBoxComponent.temperatureRange.Count; i++)
            {
                // 温度在一个区间范围内
                if (PlantBoxComponent.temperatureRange[i].Item1 <= temperature && temperature < PlantBoxComponent.temperatureRange[i].Item2)
                {
                    // 温差
                    var diffTemperature = temperature - PlantBoxComponent.temperatureRange[i].Item1;
                    // 温度范围
                    var tempertureRange = PlantBoxComponent.temperatureRange[i].Item2 - PlantBoxComponent.temperatureRange[i].Item1;
                    var rateRange = Math.Abs(PlantBoxComponent.temperatureRate[i].Item2 - PlantBoxComponent.temperatureRate[i].Item1);

                    // 上升曲线
                    if (PlantBoxComponent.temperatureRate[i].Item1 < PlantBoxComponent.temperatureRate[i].Item2)
                    {
                        findRate = PlantBoxComponent.temperatureRate[i].Item1 + ((diffTemperature / tempertureRange) * rateRange);
                    }
                    else
                    {
                        findRate = PlantBoxComponent.temperatureRate[i].Item1 - ((diffTemperature / tempertureRange) * rateRange);
                    }
                    break;
                }
            }
            return findRate;
        }

        private static float GetTemperature(PlantBoxComponent self)
        {
            // 计算太阳温度
            var sunTemperature = GetTemperatureByPosition(self, new Vector3(self.partEntity.PosX, self.partEntity.PosY, self.partEntity.PosZ));

            // 计算取暖器温度
            var heaterTemperature = 0f;
            var retlist = self.context.CheckList;
            retlist.Clear();
            EntityManager.Instance.OctreeManager.FindPartEntityByBoundingBox(retlist, self.tempraturecheckBound);
            foreach (var entId in retlist)
            {
                var ent = EntityManager.Instance.GetEntity(entId);
                if (ent != null && ent is PartEntity partEnt)
                {
                    var heaterComp =
                        partEnt.GetComponent<ElectricHeaterComponent>(EComponentIdEnum.ElectricBase);
                    if (heaterComp != null)
                    {
                        heaterTemperature = Mathf.Max(heaterComp.GetHeat(new Vector3(self.partEntity.PosX, self.partEntity.PosY, self.partEntity.PosZ)), heaterTemperature);
                    }
                }
            }

            sunTemperature += heaterTemperature;
            sunTemperature = Mathf.Max(sunTemperature, McCommon.Tables.TbPlantBoxConfig.GetOrDefault(self.templateId).Temprature);
            return sunTemperature;
        }

        private static float GetTemperatureByPosition(PlantBoxComponent self, Vector3 position)
        {
            int biome = SpawnFilter.GetBiomeType(position);
            var config = McCommon.Tables.TbGameTemperature.GetOrDefault((BiomeType)biome);
            if (config == null)
            {
                self.Logger.Error($"type:{biome}, fail to get temperature config");
                return 0f;
            }

            var hour = ServerInstanceEntity.Instance.Hour;
            var min = ServerInstanceEntity.Instance.Minute;
            var nextHour = (hour + 1) % 24;
            var nowTemp = config.Temperature[hour];
            var nextTemp = config.Temperature[nextHour];
            float rate = (float)min / 60;

            return nowTemp + (nextTemp - nowTemp) * rate;
        }

        private static float GetLightRate(PlantBoxComponent self)
        {
            var retlist = self.context.CheckList;
            var checkHaveLightPart = false;

            if (self.context.IsInRoom)
            {
                EntityManager.Instance.OctreeManager.FindPartEntityByBoundingBox(retlist, self.checkBound);
                foreach (var entId in retlist)
                {
                    var ent = EntityManager.Instance.GetEntity(entId);
                    // TODO:@cq ID暂时写死，后续会有多个，也就不配置宏了
                    if (ent != null && ent is PartEntity partEnt)
                    {
                        var distance = Vector3.Distance(new Vector3(partEnt.PosX, partEnt.PosY, partEnt.PosZ), new Vector3(partEnt.PosX, partEnt.PosY, partEnt.PosZ));
                        var checkRange = McCommon.Tables.TbPlantConstConfig.LightSeekingRange;
                        if (distance < checkRange)
                        {
                            var lighter = partEnt.GetComponent<ElectricBaseComponent>(EComponentIdEnum.ElectricBase);
                            if (partEnt.TemplateId == (long)PartType.CeilingLight
                                && lighter != null
                                && lighter.ElectricC.HasPower)
                            {
                                checkHaveLightPart = true;
                                break;
                            }
                        }
                    }
                }
            }
            if (checkHaveLightPart) return 1;
            return GetSunRate();
        }

        private static float GetSunRate()
        {
            return ServerInstanceEntity.Instance.GetGameTimeStage() switch
            {
                GameTimeStage.Day => 1,
                GameTimeStage.Night => 0f,
                GameTimeStage.Sunrise => 0.5f,
                GameTimeStage.Sunset => 0.5f,
                _ => 0f
            };
        }

        private static void UpdatePlantProcess(PlantBoxComponent self, long lastTickTime)
        {
            var passTime = (1f * ProcessEntity.Instance.TimeSinceStartup - lastTickTime) / 1000;
            foreach (var (_, slot) in self.slots)
            {
                if (slot is PlantNode plant)
                {
                    if (plant.Stage == (int)PlantStage.Harvest)
                        continue;
                    var plantSeedConfig = McCommon.Tables.TbPlantSeedConfig.GetOrDefault(plant.BizId);
                    var stageTime = plantSeedConfig.StageTime[plant.Stage];
                    var rate = GetAndUpdateGrowRate(self, plant);

                    // 计算成长基因个数
                    var geneGrowId = McCommon.Tables.TbPlantConstConfig.GeneGrowId;
                    var geneCount = GetGeneIdCount(plant, geneGrowId);

                    // 计算成长因素
                    var geneGrowRate = McCommon.Tables.TbPlantConstConfig.GeneHGrowRate;
                    var growTime = (rate * passTime) * (1 + geneGrowRate * geneCount);
                    if (!plant.HaveCalc)
                    {
                        var calcTime = (1f * ProcessEntity.Instance.TimeSinceStartup - plant.CreateTime) / 1000;
                        growTime = rate * calcTime;
                    }

                    plant.GrowTime += growTime;
                    self.Logger.Debug($"[PlantBox] UpdatePlantProcess plantBox:{self.ParentId}, plant:{plant.ToPrettyString()}, growTime:{plant.GrowTime}");

                    if (plant.GrowTime > stageTime)
                    {
                        if (plant.Stage == (int)PlantStage.Hybridization)
                        {
                            DoNatureHybridize(self, plant);
                        }

                        plant.Stage += 1;
                        if (plant.Stage == (int)PlantStage.Mature)
                        {
                            if (self.partEntity.OwnerId > 1)
                            {
                                var playerEntity = UserManagerEntity.Instance.GetPlayerEntity((ulong)self.partEntity.OwnerId);
                                if (playerEntity != null)
                                {
                                    SetTlog(playerEntity, self.templateId, "Mature", plant);
                                }
                                else
                                {
                                    self.Logger.Info($"[UpdatePlantProcess] plantEntity.OwnerId :{self.partEntity.OwnerId} is not exist");
                                }
                            }
                            else
                            {
                                self.Logger.Info($"[UpdatePlantProcess] plantEntity.OwnerId error:{self.partEntity.OwnerId}");
                            }
                        }
                        plant.GrowTime = 0;
                        self.Logger.Info($"[PlantBox] UpdatePlantProcess plantBox:{self.ParentId}, plant:{plant.ToPrettyString()}, change stage:{plant.Stage}");
                    }
                }
            }
        }

        private static void DoNatureHybridize(PlantBoxComponent self, PlantNode plant)
        {
            PrintPlantGeneIds(self, plant);

            var randPlants = new List<PlantNode>();
            foreach (var (_, slot) in self.slots)
            {
                if (slot is PlantNode rand && rand.Id != plant.Id)
                {
                    randPlants.Add(rand);
                }
            }

            var minCount = McCommon.Tables.TbPlantConstConfig.HybridizationNeedMinCount;
            if (randPlants.Count < minCount)
                return;

            var hybridizeGeneIds = new List<List<int>>(minCount);

            for (int i = 0; i < minCount; i++)
            {
                int randIdx = RandomUtil.Instance.Next(0, randPlants.Count);
                (randPlants[randPlants.Count - 1], randPlants[randIdx]) = (randPlants[randIdx], randPlants[randPlants.Count - 1]);
                hybridizeGeneIds.Add(CopyPlantGenes(randPlants[0]));
                randPlants.RemoveAt(randPlants.Count - 1);
            }

            var geneIds = new List<int>(1 + hybridizeGeneIds.Count);
            var rates = new List<float>(1 + hybridizeGeneIds.Count);

            for (int i = 0; i < plant.Genes.Count; i++)
            {
                float totalRate = 0f;

                var geneId = plant.Genes[i];
                var config = McCommon.Tables.TbPlantGeneConfig.GetOrDefault(geneId);
                if (config == null)
                {
                    self.Logger.Error($"gene:{geneId}, fail to get plant gene config");
                    return;
                }

                geneIds.Add(geneId);
                totalRate += config.CrossbreedWeight;
                rates.Add(config.CrossbreedWeight);

                foreach (var genids in hybridizeGeneIds)
                {
                    var selectGeneId = genids[i];
                    var selectConfig = McCommon.Tables.TbPlantGeneConfig.GetOrDefault(selectGeneId);
                    if (selectConfig == null)
                    {
                        self.Logger.Error($"select:{selectGeneId}, fail to get plant gene config");
                        return;
                    }

                    geneIds.Add(selectGeneId);
                    totalRate += selectConfig.CrossbreedWeight;
                    rates.Add(selectConfig.CrossbreedWeight);
                }


                // 随机结果
                var randomRate = RandomUtil.Instance.NextSingle() * totalRate;
                var calcRate = 0f;
                for (int k = 0; k < rates.Count; k++)
                {
                    calcRate += rates[k];
                    if (randomRate < calcRate)
                    {
                        plant.Genes[i] = geneIds[k];
                        break;
                    }
                }

                rates.Clear();
                geneIds.Clear();
            }

            PrintPlantGeneIds(self, plant);
        }

        private static void SetTlog(PlayerEntity spc, long planter, string operation, PlantNode pn)
        {
            var plantGene = "";
            for (int i = 0; i < pn.Genes.Count; i++)
            {
                plantGene = string.Concat(plantGene, pn.Genes[i].ToString());
            }
            TLogUtil.LogPlantEvent(spc, pn.BizId, planter, plantGene, operation, pn.LightRate, pn.WaterRate, pn.TemperatureRate, pn.GroudRate
                , pn.OverAllRate,
                pn.CreateTime, ProcessEntity.Instance.TimeSinceStartup - pn.CreateTime);
        }

        private static void PrintPlantGeneIds(PlantBoxComponent self, PlantNode plant)
        {
            var str = "";
            foreach (var geneId in plant.Genes)
            {
                str += $"{geneId},";
            }
            self.Logger.Info($"plant:{plant.Index} gene ids:{str}");
        }

        private static List<int> CopyPlantGenes(PlantNode plant)
        {
            var genes = new List<int>();
            foreach (var gene in plant.Genes)
            {
                genes.Add(gene);
            }

            return genes;
        }

        private static void UpdatePlantCalcFlag(PlantBoxComponent self)
        {
            foreach (var (_, slot) in self.slots)
            {
                if (slot is PlantNode plant)
                {
                    if (!plant.HaveCalc) plant.HaveCalc = true;
                }
            }
        }

        private static int GetHarvestRate(PlantNode node)
        {
            var pickupMultiplier = McCommon.Tables.TbPlantSeedConfig.GetOrDefault(node.BizId).PickupMultiplier;
            var serverBaseMultiplier = McCommon.Tables.TbPlantConstConfig.BaseYieldIncreaseMultiplier;
            return (int)Mathf.Floor((1 * pickupMultiplier * serverBaseMultiplier) + 0.5f);
        }

        [Hotfix]
        public static EOpCode OnCheckLooting(this PlantBoxComponent self, CheckLootingAbilityArgs args)
        {
            var ret = BaseOnCheckLooting.Invoke(self, args);
            if (ret != EOpCode.Success)
            {
                return ret;
            }

            if (self.usingRoleId == 0 || self.usingRoleId == args.RoleId)
            {
                return EOpCode.Success;
            }

            return EOpCode.ContainerOccupied;
        }

        [Hotfix]
        public static void OnStartLooting(this PlantBoxComponent self, StartLootingAbilityArgs args)
        {
            self.usingRoleId = args.RoleId;
            SyncEntityHelper.SyncPartEntityUsingState(self.ParentId, true);
            // 重置杂交记录，防止客户端开始后为结束
            ClearRecordInfo(self);
        }

        [Hotfix]
        public static void OnStopLooting(this PlantBoxComponent self, StopLootingAbilityArgs args)
        {
            self.usingRoleId = 0;
            SyncEntityHelper.SyncPartEntityUsingState(self.ParentId, false);
            ClearRecordInfo(self);
        }

        internal static int GmDoRandomPlant(this PlantBoxComponent self)
        {
            var successCount = 0;
            var seedMap = McCommon.Tables.TbPlantSeedConfig.DataMap;
            for (int index = 0; index < self.slots.Capacity; index++)
            {
                var dstPath = new List<long>() { NodeSystemType.PlantSystem, StorageContainerConst.PlantBoxSlot, index };
                if (self.Root.GetNodeByPath(dstPath) != null) continue;
                int randomNumber = RandomUtil.Instance.Next(seedMap.Count);
                var seedConfig = seedMap.Skip(randomNumber).FirstOrDefault();
                PlantSeedNode plantSeedNode = new PlantSeedNode(seedConfig.Key, 1);
                PlantNode plantNode = new PlantNode(plantSeedNode, index);
                GetAndUpdateGrowRate(self, plantNode);
                var opCode = self.rootNodeComponent.MergeNode(new NodeOpByIndex(dstPath).WithDetail(new ExistingNode(plantNode)));
                if (opCode == EOpCode.Success)
                {
                    successCount++;
                    self.Logger.Info($"gm do plant. plantBox:{self.ParentId}, dstPath:{index}, seedId:{seedConfig.Key}");
                }
            }
            return successCount;
        }

        private static float GetAndUpdateGrowRate(PlantBoxComponent self, PlantNode node)
        {
            var waterRate = GetWaterRete(self, node);
            var matureRate = GetGroudRate(self, node);
            var temperatureRate = GetTemperatureRate(self, node);
            var lightRate = self.context.LightRate;

            node.WaterRate = waterRate;
            node.GroudRate = matureRate;
            node.TemperatureRate = temperatureRate;
            node.LightRate = lightRate;

            self.Logger.Debug($"[PlantBox] GetAndUpdateGrowRate plant:{node.ToPrettyString()}, water:{node.WaterRate}, groud:{node.GroudRate}, temperature:{node.TemperatureRate}, light:{node.LightRate}");

            var minRate = Math.Min(waterRate, matureRate);
            minRate = Math.Min(minRate, lightRate);
            minRate = Math.Min(minRate, temperatureRate);

            node.OverAllRate = Mathf.Max(minRate, McCommon.Tables.TbPlantConstConfig.MinOverallQuality);
            return node.OverAllRate;
        }

        /// <summary>
        /// 水分影响因子
        /// </summary>
        /// <param name="sedtemplateId"></param>
        /// <returns></returns>
        private static float GetWaterRete(PlantBoxComponent self, PlantNode plant)
        {
            var plantBoxConfig = McCommon.Tables.TbPlantBoxConfig.GetOrDefault(self.templateId);
            var limit = plantBoxConfig.WaterLimit;
            var waterStatusFactor = McCommon.Tables.TbPlantConstConfig.WaterStatusFactor;
            // 水量占比
            var waterStatus = (float)self.Water / limit;
            // 水分状态
            var waterRate = waterStatus > waterStatusFactor ? 1f : RmapValue(waterStatus, 0, waterStatusFactor, 0, 1);
            // 最佳水分状态因子
            var bestWaterRate = McCommon.Tables.TbPlantSeedConfig.GetOrDefault(plant.BizId).BestWaterRate;

            return RmapValue(waterRate, 0, bestWaterRate, 0, 1);
        }

        private static float GetGroudRate(PlantBoxComponent self, PlantNode plant)
        {
            var initRate = McCommon.Tables.TbPlantConstConfig.GroudInitRate;
            var groudRate = plant.IsManure ? 1 : initRate;
            var bestGroudRate = McCommon.Tables.TbPlantSeedConfig.GetOrDefault(plant.BizId).BestGroudRate;

            return RmapValue(groudRate, 0, bestGroudRate, 0, 1);
        }

        private static float GetTemperatureRate(PlantBoxComponent self, PlantNode plant)
        {
            var geneTempratureId = McCommon.Tables.TbPlantConstConfig.GeneHTempratureId;
            var geneTempratureRate = McCommon.Tables.TbPlantConstConfig.GeneHTempratureRate;
            var geneCount = GetGeneIdCount(plant, geneTempratureId);
            var TempratureRate = self.context.Temprature + geneCount * geneTempratureRate;
            var bestTemperatureRate = McCommon.Tables.TbPlantSeedConfig.GetOrDefault(plant.BizId).BestTemperatureRate;

            self.Logger.Debug($"[PlantBox] GetTemperatureRate plant:{plant.Id}, origin:{self.context.Temprature}, geneCount:{geneCount}, calc:{geneCount * geneTempratureRate}");
            return RmapValue(TempratureRate, 0, bestTemperatureRate, 0, 1);
        }

        private static float RmapValue(float value, float minA, float maxA, float minB, float maxB)
        {
            if (value >= maxA) return maxB;
            var normal = Mathf.InverseLerp(minA, maxA, value);
            return Mathf.Lerp(minB, maxB, normal);
        }

        private static void ClearRecordInfo(PlantBoxComponent self)
        {
            self.magicNum = -1;
            self.hybridizeIndex = -1;
            self.recordPlants.Clear();
            self.recordPlantGeneIds.Clear();
        }

        private static int GetGeneIdCount(PlantNode plant, int geneId) => plant.Genes.Where(g => g == geneId).Count();

        /// <summary>
        /// 加水接口
        /// </summary>
        /// <param name="water"></param>
        [Hotfix(NeedWrapper = true)]
        public static int DoSplash(this PlantBoxComponent self, long itemId, int amount)
        {
            if (amount < 0)
            {
                self.Logger.Info($"add water, value:{amount} < 0");
                return 0;
            }
            if (itemId == ItemConst.SaltWaterItemId) { return 0; }

            var limit = McCommon.Tables.TbPlantBoxConfig.GetOrDefault(self.templateId).WaterLimit;
            var addValue = Math.Min(limit - self.Water, amount);

            self.Water += addValue;
            self.Logger.Info($"AddWaterForPart, type：{itemId}, water:{amount}, addValue:{addValue}");
            return addValue;
        }

        [Hotfix(NeedWrapper = true)]
        public static int GetSplashAmount(this PlantBoxComponent self)
        {
            var limit = McCommon.Tables.TbPlantBoxConfig.GetOrDefault(self.templateId).WaterLimit;
            return limit - self.Water;
        }

        public static void AddPlantTick(PlantBoxComponent self)
        {
            var tickInit = McCommon.Tables.TbPlantConstConfig.PlantTickInit;
            var tickDithering = McCommon.Tables.TbPlantConstConfig.PlantTickDithering;
            var randTick = RandomUtil.Instance.Next(tickInit - tickDithering, tickInit + tickDithering) * 1000;
            self.AddArchiveTimerOnce((uint)randTick, OnPlantTickHotfix);
        }

        [Hotfix(AbilityCallback = true)]
        private static void OnDropWaterAbility(this PlantBoxComponent self, DropWaterAbility ability) => ability.RetVal = self;
    }
}
