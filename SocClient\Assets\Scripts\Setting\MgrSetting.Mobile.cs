﻿using System.Collections.Generic;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Ui;

namespace WizardGames.Soc.SocClient.Setting
{
    /// <summary>
    /// 手机独有的逻辑
    /// </summary>
    public partial class MgrSetting
    {
        /// <summary>
        /// 配置表的数据
        /// </summary>
        private Dictionary<ESettingTab, Dictionary<ESettingItemGroup, List<int>>> configData = new();

        /// <summary>
        /// 手游的Tab数据
        /// </summary>
        private List<ESettingTab> tabList = new();
        private Dictionary<ESettingTab, int> tabToId = new();

        private List<ESettingTab> canShowTabs = new();
        
        /// <summary>
        /// 新手关需要隐藏的Tab
        /// </summary>
        private List<ESettingTab> newbieHideTabs = new()
        {
            ESettingTab.Control,
            ESettingTab.PickUp,
            
#if PUBLISH
            ESettingTab.Screen,
            ESettingTab.GmCommand,
            ESettingTab.GmProperty,
            ESettingTab.GmSkin,
            ESettingTab.GmMonster,
            ESettingTab.Debug,
            ESettingTab.Observer,
#endif
        };
        
        /// <summary>
        ///  publish发布版本需要隐藏的Tab
        /// </summary>
        private List<ESettingTab> publishHideTabs = new()
        {
            ESettingTab.GmMonster,
            ESettingTab.Debug,

        };
        
        /// <summary>
        /// 从表格里读取Tab数据
        /// </summary>
        public void LoadTabDataFromTb()
        {
            if (IsUsePCUi()) return;
            if (null == Mc.Tables.TbSettingTab) return;

            var tempList = new List<Common.Data.Item.SettingTab>();
            tempList.AddRange(Mc.Tables.TbSettingTab.DataList);
            tempList.Sort((a, b) => a.Sort.CompareTo(b.Sort));
            foreach (var data in tempList)
            {
                if (data.IsShow)
                {
                    tabList.Add(data.TabId);
                    tabToId.Add(data.TabId, data.Id);
                }
            }
        }

        /// <summary>
        /// 从配置表里读取数据
        /// </summary>
        public void LoadConfigFromTb()
        {
            if (IsUsePCUi()) return;
            if (null == Mc.Tables.TbSetting) return;
            foreach (var data in Mc.Tables.TbSetting.DataList)
            {
                if (!configData.ContainsKey(data.TabId))
                {
                    configData[data.TabId] = new Dictionary<ESettingItemGroup, List<int>>();
                }

                if (!configData[data.TabId].ContainsKey(data.Group))
                {
                    configData[data.TabId][data.Group] = new List<int>();
                }

                if (data.NotShow == 0)
                {
                    configData[data.TabId][data.Group].Add(data.Id);
                }
            }

            // 对List<int>的ID按sort字段进行排序
            foreach (var tab in configData)
            {
                foreach (var group in tab.Value)
                {
                    group.Value.Sort((a, b) =>
                    {
                        var aSort = Mc.Tables.TbSetting.GetOrDefault(a).Sort;
                        var bSort = Mc.Tables.TbSetting.GetOrDefault(b).Sort;
                        return aSort.CompareTo(bSort);
                    });
                }
            }
        } 

        /// <summary>
        /// 标签数据
        /// </summary>
        /// <returns></returns>
        public List<ESettingTab> GetTabList()
        {
            return tabList;
        }
        
        /// <summary>
        /// 获取可显示的标签数据
        /// </summary>
        /// <returns></returns>
        public List<ESettingTab> GetCanShowTabs()
        {
            bool isPublish = false;
#if PUBLISH
            isPublish = true;
#endif
            canShowTabs.Clear();
            foreach (var tab in tabList)
            {
                if (PlayHelper.IsNewbie && newbieHideTabs.Contains(tab)) continue;
                if (isPublish && publishHideTabs.Contains(tab)) continue; 
                canShowTabs.Add(tab);
            }

            logger.InfoFormat("GetCanShowTabs: {0} tabs can be shown newbieHideTabs Count {1} isPublish {2}", canShowTabs.Count,
                newbieHideTabs.Count, isPublish);
            return canShowTabs;
        }

        public int GetTabId(ESettingTab tab)
        {
            if (tabToId.ContainsKey(tab))
            {
                return tabToId[tab];
            }
            return 0;
        }

        /// <summary>
        /// 获取标签里的数据
        /// </summary>
        /// <param name="tab"></param>
        /// <returns></returns>
        public Dictionary<ESettingItemGroup, List<int>> GetConfigDataByTab(ESettingTab tab)
        {
            if (configData.ContainsKey(tab))
            {
                return configData[tab];
            }
            return null;
        }

        /// <summary>
        /// 获取标签里的数据
        /// </summary>
        /// <param name="tab"></param>
        /// <returns></returns>
        public Dictionary<ESettingItemGroup, List<int>> GetConfigDataByTab(ESettingTab_PC tab)
        {
            if (configDataPC.ContainsKey(tab))
            {
                return configDataPC[tab];
            }
            return null;
        }
#if !STANDALONE_REAL_PC
        
        /// <summary>
        /// 打开设置界面
        /// </summary>
        /// <summary>
        /// 是否使用PC的UI
        public bool IsUsePCUi()
        {
            return false;
        }
#endif
    }
}