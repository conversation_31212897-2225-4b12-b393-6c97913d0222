#if UNITY_EDITOR
using UnityEditor;
#endif

using Sirenix.Utilities;
using System.Collections.Generic;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using WizardGames.Soc.Common.Profile;
using static UnityEngine.Rendering.Universal.SkyRendering.SkyRenderingVolumetricCloudsSettings;

namespace UnityEngine.AzureSky
{
    [ExecuteInEditMode]
    public class AzureCustomSkyRenderController : MonoBeh<PERSON>our, IAzureTimeController, IAzureWeatherController, IAzureWaterController
    {
        public static AzureCustomSkyRenderController Instance;
        private static LinkedList<AzureCustomSkyRenderController> s_ActiveControllers = new LinkedList<AzureCustomSkyRenderController>();
        private static int MainLightPosition_ID = Shader.PropertyToID("_MainLightPosition");
        private static int MainLightColor_ID = Shader.PropertyToID("_MainLightColor");
        public enum EAzureCustomRenderingState
        {
            Unkown = -1,
            Sun = 0,
            Moon,
        }

        private struct LightColorLerpInfo
        {
            public EAzureCustomRenderingState FromState;
            public EAzureCustomRenderingState ToState;

            public float FromLightCosZenithAngle;
            public float ToLightCosZenithAngle;

            public Vector3 FromDirectionalLightColor;
            public Vector3 ToDirectionalLightColor;
            public float DayNightDirectionalLightLerpTime;

            public Vector3 FromAmbientLightColor;
            public Vector3 ToAmbientLightColor;

            public float DayNightAmbientLightLerpTime;

            public Color GetDirectionalLightLerpColor(Color fromScatColor, Color toScatColor)
            {
                return Color.Lerp(GetFromDirectionalLightColor(fromScatColor), GetToDirectionalLightColor(toScatColor), DayNightDirectionalLightLerpTime);
            }

            public Color GetFromDirectionalLightColor(Color scatColor)
            {
                var fromColor = new Color(FromDirectionalLightColor.x, FromDirectionalLightColor.y, FromDirectionalLightColor.z);
                return fromColor * scatColor;
            }

            public Color GetToDirectionalLightColor(Color scatColor)
            {
                var toColor = new Color(ToDirectionalLightColor.x, ToDirectionalLightColor.y, ToDirectionalLightColor.z);
                return toColor * scatColor;
            }

            public Color GetAmbientLightLerpColor(Color fromScatColor, Color toScatColor)
            {
                var fromColor = new Color(FromAmbientLightColor.x, FromAmbientLightColor.y, FromAmbientLightColor.z);
                var toColor = new Color(ToAmbientLightColor.x, ToAmbientLightColor.y, ToAmbientLightColor.z);
                return Color.Lerp(fromColor * fromScatColor, toColor * toScatColor, DayNightAmbientLightLerpTime);
            }
        }

        private int m_CurrentRenderingDataIndex = -1;


        public bool IsUpdateTimeLocally = false;

        public bool IsStopTimeUpdate { get; set; } = false;

        public int TimeScale { get; set; } = 1;
        
        public float TimeOfDay
        {
            get { return CurrentControllerPreset.TimeOfDay; }
            set {  CurrentControllerPreset.TimeOfDay = value; } 
        }

        public float TimeProgressionStep = 0.0f;
        //DayNight Switch Time is Always 6.0 and 18.0
        public const float DAY_TO_NIGHT_SWITCH_TIME = 18.2f;
        public const float NIGHT_TO_DAY_SWITCH_TIME = 5.9f;
        private const float DURATION_OF_DAY = DAY_TO_NIGHT_SWITCH_TIME - NIGHT_TO_DAY_SWITCH_TIME;
        private const float DURATION_OF_NIGHT = 24.0f - DURATION_OF_DAY;
        private Quaternion m_SunBodyRotation;
        private Quaternion m_MoonBodyRotation;
        private Quaternion m_SunLightRotation;
        private Quaternion m_MoonLightRotation;
        private Quaternion m_CurrentLightRotation;
        private Quaternion m_CurrentCelestialBodyRotation;
        private Quaternion m_NextCelestialBodyRotation;

        private LightColorLerpInfo m_LightColorLerpInfo;

        [SerializeField] public AzureCustomObjectControllerData ObjectControllerData = new AzureCustomObjectControllerData();
        [SerializeField] public AzureCustomSkyRenderControllerPreset CurrentControllerPreset;
        [SerializeField] public List<AzureCustomSkyRenderControllerPreset> ControllerPresets;
        [SerializeField] int CurrentControllerPresetIndex = 0;

        [SerializeField] public AzureCustomSkyRenderingPreset CurrentSkyRenderingPreset;
        [SerializeField] public int LastUsedSkyRenderingPresetIndex = 0;
        [SerializeField] public int CurrentSkyRenderingPresetIndex = 0;
        [SerializeField] public List<AzureCustomSkyRenderingPresetData> SkyRenderingPresets;
        private int m_TargetSkyRenderingPresetIndex = 0;
        public AzureCustomSkyRenderingPreset m_TargetSkyRenderingPreset;
        private bool m_IsInPresetTransition = false;
        private float m_PresetTransitionStartTime = 0.0f;
        private float m_PresetTransitionDuration = 0.0f;
        private bool m_NeedRecomputeLuts = false;
        [SerializeField] private float m_PresetTransitionProgress = 0.0f;

        [SerializeField] public bool DebugAlwaysComputeLUTs = true;
        [SerializeField] public bool DebugSunColorLUT = false;

        private Transform m_FollowerTarget;
        public void SetFollowTarget(Transform target)
        {
            m_FollowerTarget = target;
        }

        public bool NeedRecomputeLuts
        {
           set { m_NeedRecomputeLuts = value; }
        }

        private bool m_ForceUpdate = false;
        public static void ForceUpdate()
        {
            if (Instance != null)
                Instance.m_ForceUpdate = true;
        }

        private float m_CustomDirectionalLightUpdateRate = -1.0f;
        public float CustomDirectionalLightUpdateRate
        {
            get { return m_CustomDirectionalLightUpdateRate; }
            set { m_CustomDirectionalLightUpdateRate = value; }
        }

        public float Latitude
        {
            get { return CurrentControllerPreset != null ? CurrentControllerPreset.Latitude : 0.0f; }
            set { if (CurrentControllerPreset != null) CurrentControllerPreset.Latitude = value; }
        }

        public float Longitude
        {
            get { return CurrentControllerPreset != null ? CurrentControllerPreset.Longitude : 0.0f; }
            set { if (CurrentControllerPreset != null) CurrentControllerPreset.Longitude = value; }
        }


#if UNITY_EDITOR

        [SerializeField] public RenderTexture DebugMipFogCubemap = null;
        [SerializeField] public RenderTexture DebugMipFogFilteredCubemap0 = null;
        [SerializeField] public RenderTexture DebugMipFogFilteredCubemap1 = null;

        private Transform m_ControllerPresetsParent = null;
        private Transform ControllerPresetsParent
        {
            get
            {
                if (m_ControllerPresetsParent == null)
                {
                    // TODO: 需要改进，目前这样做比较恶心
                    m_ControllerPresetsParent = transform.Find("CustomControllerPresets");
                }
                return m_ControllerPresetsParent;
            }
        }

        private Transform m_WeatherPresetsParent = null;
        private Transform WeatherPresetsParent
        {
            get
            {
                if (m_WeatherPresetsParent == null)
                {
                    // TODO: 需要改进，目前这样做比较恶心
                    m_WeatherPresetsParent = transform.Find("CustomRenderingPresets");
                }
                return m_WeatherPresetsParent;
            }
        }

        public void SelectActiveController()
        {
            if (Instance != null)
            {
                var go = Instance.gameObject;
                Selection.activeObject = go;
                EditorGUIUtility.PingObject(go);
            }
        }
#endif

        [SerializeField] public bool RenderSunLensFlare = true;

        private AzureCustomSkyRenderingData m_CurrentRenderingData = new AzureCustomSkyRenderingData();
        private AzureCustomSkyRenderingData m_NextRenderingData = new AzureCustomSkyRenderingData();
        private AzureCustomSkyRenderingCommonData m_CommonRenderingData = new AzureCustomSkyRenderingCommonData();
        private AzureCustomSkyRenderingLightData m_LightData = new AzureCustomSkyRenderingLightData();
        private AzureCustomSkyRenderingAdvanceData m_AdvanceData = new AzureCustomSkyRenderingAdvanceData();
        private AzureCustomFogRenderingData m_FogRenderingData = new AzureCustomFogRenderingData();

        [SerializeField] public ReflectionProbe AzureReflectionProbe;
        private Texture m_OriginAzureReflectionProbeTexture;
        private Texture m_OriginEnviromentReflectionTexture;
        private Vector4 m_OriginSpecCube0Hdr;
        private float m_OriginAzureReflectionIntensity;
        private float m_OriginEnviromentReflectionIntensity;
        private bool m_OriginReflectionTextureCached = false;

        private Texture m_RuntimeReflectionProbeTexture;
        private Texture m_RuntimeEnviromentReflectionTexture;

        [SerializeField] public LensFlareComponentSRP[] SunLensFlares;
        [SerializeField] public Transform Clouds;
        [SerializeField] public Mesh CloudBillboardsMesh;
        [SerializeField] public Texture CloudBillboardsTexture;

        private SkyRenderingFeature m_SkyRenderingFeature;
        private Color m_DirectionalLightColor = Color.black;
        private Color m_AmbientLightColor = Color.black;
        private int m_CurrentRendererIndex;
        private bool m_AzureSkyActive = true;
        private AmbientMode m_OriginAmbientMode = AmbientMode.Trilight;
        private Color m_OriginAmbientLightColor;
        private Color m_OriginAmbientEquatorColor;
        private Color m_OriginAmbientGroundColor;
        private static Material m_OriginSkyMaterial = null;
        private static bool m_FeatureActive = true;
        private float m_DirectionalLightLastUpdateTime = 0.0f;

        private float m_MainLightAdjuster = 1.0f;
        private float m_EnvironmentAdjuster = 1.0f;
        private float m_RelectionAdjuster = 1.0f;
        private float m_TargetMainLightAdjuster = 1.0f;
        private float m_TargetEnviromentAdjuster = 1.0f;

        private bool m_IsIndoor = false;
        private float m_IndoorShadowIntensity = 1.0f;
        private bool m_StartShadowIntensityLerp = false;
        private float m_PreSrcIntensity = 1f;
        private bool m_IsShadowIntensityLerping = false;
        private float m_ShadowIntensityTimer = .0f;
        private float m_LerpA = 1.0f;
        private float m_LerpB = 1.0f;

        public Texture RuntimeReflectionProbeTexture
        {
            get { 
                if (m_AdvanceData.RealTimeReflectionCubemap || m_RuntimeReflectionProbeTexture == null)
                    return AzureReflectionProbe.customBakedTexture;
                return m_RuntimeReflectionProbeTexture;
             }

            set {
                if (m_AdvanceData.RealTimeReflectionCubemap)
                {
                    Debug.LogWarningFormat("Can not set 'RuntimeReflectionProbeTexture' when 'AzureCustomSkyRenderingAdvanceData.RealTimeReflectionCubemap = true'");
                    return;
                }
                m_RuntimeReflectionProbeTexture = value;
                if (AzureReflectionProbe != null && AzureReflectionProbe.customBakedTexture != m_RuntimeReflectionProbeTexture)
                    AzureReflectionProbe.customBakedTexture = m_RuntimeReflectionProbeTexture;
            }
        }

        public Texture RuntimeEnviromentReflectionTexture
        {
            get
            {
                if (m_AdvanceData.RealTimeReflectionCubemap || m_RuntimeEnviromentReflectionTexture == null)
                    return RenderSettings.customReflectionTexture;

                return m_RuntimeEnviromentReflectionTexture;
            }

            set {
                if (m_AdvanceData.RealTimeReflectionCubemap)
                {
                    Debug.LogWarningFormat("Can not set 'RuntimeEnviromentReflectionTexture' when 'AzureCustomSkyRenderingAdvanceData.RealTimeReflectionCubemap = true'");
                    return;
                }
                m_RuntimeEnviromentReflectionTexture = value; 
            }
        }

        public void ReflectionProbeRender()
        {
            if (AzureReflectionProbe != null)
                AzureReflectionProbe.RenderProbe();
        }

        public void ComputeTimeProgressionStep()
        {
            if (CurrentControllerPreset == null)
                return;

            var dayLength = CurrentControllerPreset.DayLength
;            if (dayLength > 0.0f)
            {
                TimeProgressionStep = (24.0f / 60.0f) / dayLength;
            }
            else
            {
                TimeProgressionStep = 0.0f;
            }
        }

        private void UpdateTimelineLocally()
        {
            // Moves the timeline forward
            if (CurrentControllerPreset.TimeSystemDirection == AzureTimeSystemDirection.Forward)
            {
                TimeOfDay += TimeScale * TimeProgressionStep * Time.deltaTime;

                // Change to the next day in the calendar
                if (TimeOfDay > 24.0f)
                {
                    //IncreaseDay();
                    TimeOfDay = 0.0f;
                }
            }
            // Moves the timeline backward
            else
            {
                TimeOfDay -= TimeProgressionStep * Time.deltaTime;

                // Change to the previous day in the calendar
                if (TimeOfDay < 0.0f)
                {
                    //DecreaseDay();
                    TimeOfDay = 24.0f;
                }
            }
        }

        public void ChangeControllerPresetTo(int presetIndex)
        {
            if (ControllerPresets == null || presetIndex < 0 || presetIndex >= ControllerPresets.Count)
                return;

            SetNewControllerPreset(presetIndex);
        }

        public void SetNewControllerPreset(int presetIndex)
        {
            m_NeedRecomputeLuts = CurrentControllerPresetIndex != presetIndex;
            CurrentControllerPresetIndex = presetIndex;
        }

        public AzureWeatherPresetEnum GetCurrentWeather()
        {
            if (CurrentSkyRenderingPresetIndex < 0 || CurrentSkyRenderingPresetIndex >= (int)AzureWeatherPresetEnum.Count)
            {
                return AzureWeatherPresetEnum.Default;
            }
            else
            {
                return (AzureWeatherPresetEnum)CurrentSkyRenderingPresetIndex;
            }
        }

        // Deprecated
        public void BlendDefaultAndUnderwaterPresets(float blendFactor)
        {
            //m_PresetTransitionProgress = Mathf.Clamp01(blendFactor);
            //if (blendFactor > 0f && blendFactor < 1f)
            //{
            //    m_IsBlendingPreset = true;
            //    CurrentSkyRenderingPreset = GetSkyRenderingPreset((int)AzureWeatherPresetEnum.UnderWater);
            //    m_TargetSkyRenderingPreset = GetSkyRenderingPreset((int)AzureWeatherPresetEnum.Default);
            //    m_PresetTransitionDuration = 0f;
            //    CurrentSkyRenderingPresetIndex = (int)AzureWeatherPresetEnum.Default;
            //}
            //else
            //{
            //    m_IsBlendingPreset = false;
            //    ChangeWeatherTo(blendFactor >= 1.0f ? AzureWeatherPresetEnum.Default : AzureWeatherPresetEnum.UnderWater);
            //    m_PresetTransitionProgress = 1f;
            //}

            //if (AzureWeatherController != null)
            //    AzureWeatherController.BlendDefaultAndUnderwaterPresets(blendFactor);
        }

        public void StopBlendDefaultAndUnderwaterPresets(AzureWeatherPresetEnum newWeather)
        {
            //m_IsBlendingPreset = false;
            //SetNewPreset((int)newWeather);
            //m_PresetTransitionProgress = 0f;

            //if (AzureWeatherController != null)
            //    AzureWeatherController.StopBlendDefaultAndUnderwaterPresets(newWeather);
        }

        public void ChangeWeatherTo(AzureWeatherPresetEnum targetWeather, float easeDuration = -1f)
        {
            int targetWeatherIndex = (int)targetWeather;

            if (targetWeatherIndex >= SkyRenderingPresets.Count || targetWeatherIndex < 0)
            {
                return;
            }

            if (LastUsedSkyRenderingPresetIndex !=  targetWeatherIndex)
            {
                SetNewPreset(targetWeatherIndex, easeDuration);
            }
        }

        public bool TryGetBloomValue(float time, out float bloomValue)
        {
            if (m_AdvanceData.BloomValue == null)
            {
                bloomValue = 0.0f;
                return false;
            }

            bloomValue = m_AdvanceData.BloomValue.Evaluate(time);
            return true;
        }

        public bool TryGetFixCompensationValue(float time, out float fixCompensationValue)
        {
            if (m_AdvanceData.FixedCompensation == null)
            {
                fixCompensationValue = 0.0f;
                return false;
            }
            
            fixCompensationValue = m_AdvanceData.FixedCompensation.Evaluate(time);
            return true;
        }

        public void Setlighting(float mainLightIntensity, float environmentIntensity, float reflectionIntensity = 1.0f)
        {
            if (m_IsInPresetTransition && m_TargetSkyRenderingPreset != null)
            {
                m_TargetMainLightAdjuster = mainLightIntensity;
                m_TargetEnviromentAdjuster = environmentIntensity;
            }
            else
            {
                m_MainLightAdjuster = mainLightIntensity;
                m_EnvironmentAdjuster = environmentIntensity;
            }
            m_RelectionAdjuster = reflectionIntensity;
        }

        private float AdjustReflectionIntensity(float srcIntensity)
        {
            return srcIntensity * m_RelectionAdjuster;
        }

        public void SetShadowIntensityMode(bool isIndoor, float indoorIntensity = 1.0f)
        {
            m_IndoorShadowIntensity = Mathf.Clamp01(indoorIntensity);

            if (isIndoor != m_IsIndoor)
            {
                if (m_IsShadowIntensityLerping)
                {
                    m_IsShadowIntensityLerping = false;
                }

                m_StartShadowIntensityLerp = true;

                if (isIndoor)
                {
                    m_LerpA = m_PreSrcIntensity;
                    m_LerpB = m_IndoorShadowIntensity;
                }
                else
                {
                    m_LerpA = m_IndoorShadowIntensity;
                    m_LerpB = m_PreSrcIntensity;
                }
            }

            m_IsIndoor = isIndoor;
        }

        private float AdjustMainLightIntensity(float lightIntensity)
        {
            return Mathf.Lerp(0f, lightIntensity, m_MainLightAdjuster);
        }

        private float AdjustShadowIntensity(float srcIntensity)
        {
            m_PreSrcIntensity = srcIntensity;

            if (m_IsShadowIntensityLerping)
            {
                if (m_ShadowIntensityTimer > AzureTimeController.Instance.ShadowIntensityLerpDuration)
                {
                    m_IsShadowIntensityLerping = false;
                    return m_LerpB;
                }

                float lerpedIntensity = Mathf.Lerp(m_LerpA, m_LerpB, m_ShadowIntensityTimer / AzureTimeController.Instance.ShadowIntensityLerpDuration);

                return lerpedIntensity;
            }
            else
            {
                if (m_IsIndoor)
                {
                    return m_IndoorShadowIntensity;
                }
                else
                {
                    return srcIntensity;
                }
            }
        }


        public void UpdateExternalMaterial(Material oceanMaterial, Material lakeMaterial)
        {
            m_AdvanceData.UpdateExternalMaterial(TimeOfDay, oceanMaterial, lakeMaterial);
        }

        public void UpdateCloudPreset(int cloudPresetEnumIndex)
        {
            var skyRenderingFeature = GetSkyRenderingFeature();
            if (skyRenderingFeature != null)
            {
                var cloudPreset = (CloudPresets) cloudPresetEnumIndex;
                skyRenderingFeature.UpdateCloudPreset(cloudPreset);
                if (cloudPreset != CloudPresets.Custom)
                {
                    CurrentControllerPreset.CopyCloudPresetFrom(skyRenderingFeature.GetCloudSettings(), skyRenderingFeature.GetCloudLayerSettings());
                }
            }
        }

        public void SetNewPreset(int index, float transitionDuration = -1f)
        {
            index = Mathf.Clamp(index, 0, SkyRenderingPresets.Count - 1);

            if (SkyRenderingPresets[index].Preset == null)
            {
                return;
            }

            // If transition is reversing the last unfinished one, refresh current weather.
            var isReverseTransition = CurrentSkyRenderingPresetIndex == index && m_PresetTransitionProgress > 0.01f && m_PresetTransitionProgress < 0.99f;
            if (isReverseTransition)
            {
                ChangeToTargetPreset();
            }

            m_TargetSkyRenderingPresetIndex = index;
            m_TargetSkyRenderingPreset = GetSkyRenderingPreset(m_TargetSkyRenderingPresetIndex);
            m_PresetTransitionDuration = transitionDuration < 0f ? Mathf.Max(0f, SkyRenderingPresets[index].TransitionDuration) : transitionDuration;

            if (m_PresetTransitionDuration < 0.01f)
            {
                // Change weather immediately.
                ChangeToTargetPreset();
                //AzureWeatherController.EvaluateCurrentWeather(true);
            }
            else
            {
                // Change weather with transition.
                if (isReverseTransition)
                {
                    m_PresetTransitionProgress = Mathf.Clamp(1f - m_PresetTransitionProgress, 0f, 1f); ;
                    m_PresetTransitionStartTime = Time.time - m_PresetTransitionDuration * m_PresetTransitionProgress;
                }
                else
                {
                    m_PresetTransitionProgress = 0.0f;
                    m_PresetTransitionStartTime = Time.time;
                }
                m_IsInPresetTransition = true;
            }

            LastUsedSkyRenderingPresetIndex = index;
        }

        private AzureCustomSkyRenderControllerPreset GetControllerPreset(int presetIndex)
        {
            if (ControllerPresets != null && presetIndex < ControllerPresets.Count)
            {
                return ControllerPresets[presetIndex];
            }

            return null;
        }

        private AzureCustomSkyRenderingPreset GetSkyRenderingPreset(int presetIndex)
        {
            if (SkyRenderingPresets != null && presetIndex < SkyRenderingPresets.Count)
            {
                return SkyRenderingPresets[presetIndex].Preset;
            }

            return null;
        }

        private AzureCustomSkyRenderingCommonData GetSkyRenderingCommonData(AzureCustomSkyRenderingPreset preset)
        {
            return preset != null ? preset.SkyRenderingCommonData : null;
        }

        private AzureCustomSkyRenderingLightData GetSkyRenderingLightData(AzureCustomSkyRenderingPreset preset)
        {
            return preset != null ? preset.LightData : null;
        }

        private AzureCustomSkyRenderingAdvanceData GetSkyRenderingAdvanceData(AzureCustomSkyRenderingPreset preset)
        {
            return preset != null ? preset.AdvanceData : null;
        }

        public AzureCustomSkyRenderingData[] CurrentSkyRenderingDatas
        {
            get { return CurrentSkyRenderingPreset != null ? CurrentSkyRenderingPreset.SkyRenderingDatas : null; }
        }

        private AzureCustomSkyRenderingData[] GetSkyRenderingDatas(AzureCustomSkyRenderingPreset preset)
        {
            return preset != null ? preset.SkyRenderingDatas : null;
        }

        private AzureCustomFogRenderingData GetFogRenderingData(AzureCustomSkyRenderingPreset preset)
        {
            return preset != null ? preset.FogRenderingData : null;
        }

        [ContextMenu("Reset Parameters")]
        private void ResetParameters()
        {
            TimeOfDay = 6.5f;

            if (CurrentControllerPreset != null)
                CurrentSkyRenderingPreset.ResetParameters();

            if (CurrentSkyRenderingPreset != null)
                CurrentSkyRenderingPreset.ResetParameters();
        }

        [ContextMenu("Moon Rendering Data From Sun")]
        private void SyncMoonRenderingDataFromSun()
        {
            if(CurrentSkyRenderingDatas != null)
                CurrentSkyRenderingDatas[(int)EAzureCustomRenderingState.Moon].CopyFrom(CurrentSkyRenderingDatas[(int)EAzureCustomRenderingState.Sun]);
        }

        [ContextMenu("Sun Rendering Data From Moon")]
        private void SyncSunRenderingDataFromMoon()
        {
            if (CurrentSkyRenderingDatas != null)
                CurrentSkyRenderingDatas[(int)EAzureCustomRenderingState.Sun].CopyFrom(CurrentSkyRenderingDatas[(int)EAzureCustomRenderingState.Moon]);
        }

        public int Hour
        {
            get { return (int)Mathf.Floor(TimeOfDay); }
        }

        public float Timeline
        {
            set { CurrentControllerPreset.Timeline = value; }
            get { return CurrentControllerPreset.Timeline; }
        }

        public string GetCurrentRenderingTimeName()
        {
            switch(m_CurrentRenderingDataIndex)
            {
                case (int)EAzureCustomRenderingState.Sun:
                    return "Day";
                case (int)EAzureCustomRenderingState.Moon:
                    return "Night";
                default:
                    return "Unkown";
            }
        }

        private void UpdateRenderingTimeOfDay()
        {
            UpdateTimeOfDay();
            UpdatePresetTransitionTime();
            UpdateSkyRenderingData();
        }

        private Quaternion ClampLightRotation(Quaternion lightRotation)
        {
            // Avoid the directional light to get close to the horizon line
            var lightEulerAngles = lightRotation.eulerAngles;
            var minLightAltitude = CurrentControllerPreset.MinLightAltitude;
            if (lightEulerAngles.x <= minLightAltitude)
            {
                lightEulerAngles.x = minLightAltitude;
                lightRotation = Quaternion.Euler(lightEulerAngles);
            }

            return lightRotation;
        }

        private Quaternion GetLightRotation(float timeOfDay)
        {
            return Quaternion.Euler(0.0f, CurrentControllerPreset.Longitude, -CurrentControllerPreset.Latitude) * Quaternion.Euler((timeOfDay * 360.0f / 24.0f) - 90.0f, 180.0f, 0.0f);
        }

        private void UpdateSunMoonRotation()
        {
            if (TimeOfDay >= NIGHT_TO_DAY_SWITCH_TIME && TimeOfDay < DAY_TO_NIGHT_SWITCH_TIME)
            {
                var sunTimeOfDay = NIGHT_TO_DAY_SWITCH_TIME + CurrentSkyRenderingPreset.DayNightTransitionTime + (TimeOfDay - NIGHT_TO_DAY_SWITCH_TIME) * (1.0f - CurrentSkyRenderingPreset.DayNightTransitionTime / DURATION_OF_DAY);
                m_SunLightRotation = GetLightRotation(sunTimeOfDay);
                if (sunTimeOfDay < DAY_TO_NIGHT_SWITCH_TIME - CurrentSkyRenderingPreset.DayNightTransitionTime)
                {
                    var t = (sunTimeOfDay - NIGHT_TO_DAY_SWITCH_TIME - CurrentSkyRenderingPreset.DayNightTransitionTime) / (DURATION_OF_DAY - 2 * CurrentSkyRenderingPreset.DayNightTransitionTime);
                    var moonTimeOfDay = t * DURATION_OF_DAY + NIGHT_TO_DAY_SWITCH_TIME;

                    m_MoonLightRotation = GetLightRotation(moonTimeOfDay) * Quaternion.Euler(0.0f, -180.0f, 0.0f);
                }
                else
                {
                    var moonTimeOfDay = sunTimeOfDay + CurrentSkyRenderingPreset.DayNightTransitionTime;
                    m_MoonLightRotation = GetLightRotation(moonTimeOfDay) * Quaternion.Euler(0.0f, -180.0f, 0.0f);
                }
            }
            else 
            {
                var moonTimeOfDay = TimeOfDay;
                if (TimeOfDay < NIGHT_TO_DAY_SWITCH_TIME)
                {
                    moonTimeOfDay += 24.0f;
                }

                moonTimeOfDay = DAY_TO_NIGHT_SWITCH_TIME + CurrentSkyRenderingPreset.DayNightTransitionTime + (moonTimeOfDay - DAY_TO_NIGHT_SWITCH_TIME) * (1.0f - CurrentSkyRenderingPreset.DayNightTransitionTime / DURATION_OF_NIGHT);
                m_MoonLightRotation = GetLightRotation(moonTimeOfDay % 24.0f) * Quaternion.Euler(0.0f, -180.0f, 0.0f);

                if (moonTimeOfDay < NIGHT_TO_DAY_SWITCH_TIME + 24.0f - CurrentSkyRenderingPreset.DayNightTransitionTime)
                {
                    var t = (moonTimeOfDay - DAY_TO_NIGHT_SWITCH_TIME - CurrentSkyRenderingPreset.DayNightTransitionTime) / (DURATION_OF_NIGHT - 2 * CurrentSkyRenderingPreset.DayNightTransitionTime);
                    var sunTimeOfDay = t * DURATION_OF_NIGHT + DAY_TO_NIGHT_SWITCH_TIME;

                    m_SunLightRotation = GetLightRotation(sunTimeOfDay % 24.0f);
                }
                else
                {
                    var sunTimeOfDay = moonTimeOfDay + CurrentSkyRenderingPreset.DayNightTransitionTime;
                    m_SunLightRotation = GetLightRotation(sunTimeOfDay % 24.0f);
                }
            }
        }
        
        private void UpdateTimeOfDay()
        {
            if(IsStopTimeUpdate) return;
            
            if (!Application.isPlaying || CurrentControllerPreset.CustomDayLength || IsUpdateTimeLocally)
                UpdateTimelineLocally();

            UpdateSunMoonRotation();

            if (TimeOfDay >= NIGHT_TO_DAY_SWITCH_TIME && TimeOfDay < DAY_TO_NIGHT_SWITCH_TIME)
            {
                ChangeRenderingState(EAzureCustomRenderingState.Sun);
            }
            else
            {
                ChangeRenderingState(EAzureCustomRenderingState.Moon);
            }

            bool isNightTime = IsNightTime();
            m_SunBodyRotation = m_SunLightRotation;
            m_MoonBodyRotation = m_MoonLightRotation;
            m_CurrentCelestialBodyRotation = isNightTime ? m_MoonLightRotation : m_SunLightRotation;
            m_NextCelestialBodyRotation = isNightTime ? m_SunLightRotation : m_MoonLightRotation;
            m_SunLightRotation = ClampLightRotation(m_SunLightRotation);
            m_MoonLightRotation = ClampLightRotation(m_MoonLightRotation);
            m_CurrentLightRotation = isNightTime ? m_MoonLightRotation : m_SunLightRotation;

            var mainCam = Camera.main;
            var followerTarget = m_FollowerTarget != null ? m_FollowerTarget : 
                                    (mainCam != null ? mainCam.transform : null);
            ObjectControllerData.Update(followerTarget, m_SunBodyRotation, m_MoonBodyRotation);
            UpdateGlobalTime();
        }


        private void UpdateGlobalTime()
        {
            // Set the global time info
            var timeOfDay = TimeOfDay;
            var timeline = Timeline;

            AzureNotificationCenter.GlobalTimeInfo.timeSystemMode = AzureTimeSystemMode.Locally;
            AzureNotificationCenter.GlobalTimeInfo.timeSystemDirection = CurrentControllerPreset.TimeSystemDirection;
            AzureNotificationCenter.GlobalTimeInfo.timeSystemLoop = AzureTimeSystemLoop.Off;
            AzureNotificationCenter.GlobalTimeInfo.timeline = timeline;
            AzureNotificationCenter.GlobalTimeInfo.timeOfDay = timeOfDay;
            AzureNotificationCenter.GlobalTimeInfo.sunElevation = ObjectControllerData.SunElevation;
            AzureNotificationCenter.GlobalTimeInfo.moonElevation = ObjectControllerData.MoonElevation;
            AzureNotificationCenter.GlobalTimeInfo.hour = (int)Mathf.Floor(timeOfDay);
            AzureNotificationCenter.GlobalTimeInfo.minute = (int)Mathf.Floor(timeOfDay * 60 % 60); ;
            AzureNotificationCenter.GlobalTimeInfo.utc = 0;
            AzureNotificationCenter.GlobalTimeInfo.evaluationTime = timeOfDay;
        }

        private void UpdatePresetTransitionTime()
        {
            if (m_IsInPresetTransition)
            {
                if (Mathf.Approximately(m_PresetTransitionDuration, 0.0f))
                {
                    m_PresetTransitionProgress = 1.0f;
                }
                else
                {
                    m_PresetTransitionProgress = Mathf.Clamp01((Time.time - m_PresetTransitionStartTime) / m_PresetTransitionDuration);
                }

                if (Mathf.Abs(m_PresetTransitionProgress - 1.0f) <= 0.0f)
                {
                    m_IsInPresetTransition = false;
                    m_PresetTransitionProgress = 0.0f;
                    m_PresetTransitionStartTime = 0.0f;
                    ChangeToTargetPreset();
                }
            }
        }

        private void ChangeToTargetPreset()
        {
            m_NeedRecomputeLuts = CurrentSkyRenderingPresetIndex != m_TargetSkyRenderingPresetIndex;
            CurrentSkyRenderingPresetIndex = m_TargetSkyRenderingPresetIndex;
            CurrentSkyRenderingPreset = m_TargetSkyRenderingPreset;
            m_MainLightAdjuster = m_TargetMainLightAdjuster;
            m_EnvironmentAdjuster = m_TargetEnviromentAdjuster; 

        }

        private void UpdateSkyRenderingData()
        {
            var from = EAzureCustomRenderingState.Sun;
            var to = EAzureCustomRenderingState.Sun;
            float t = 0.0f;

            //Directional Light Transition
            if (TimeOfDay + CurrentSkyRenderingPreset.DayNightTransitionTime * 0.5f >= NIGHT_TO_DAY_SWITCH_TIME && (TimeOfDay - CurrentSkyRenderingPreset.DayNightTransitionTime * 0.5f) < NIGHT_TO_DAY_SWITCH_TIME)
            {
                from = EAzureCustomRenderingState.Moon;
                to = EAzureCustomRenderingState.Sun;

                t = (TimeOfDay + CurrentSkyRenderingPreset.DayNightTransitionTime * 0.5f - NIGHT_TO_DAY_SWITCH_TIME) / CurrentSkyRenderingPreset.DayNightTransitionTime;
            }   
            else if (TimeOfDay + CurrentSkyRenderingPreset.DayNightTransitionTime >= DAY_TO_NIGHT_SWITCH_TIME && TimeOfDay < DAY_TO_NIGHT_SWITCH_TIME)
            {
                from = EAzureCustomRenderingState.Sun;
                to = EAzureCustomRenderingState.Moon;

                t = (TimeOfDay + CurrentSkyRenderingPreset.DayNightTransitionTime - DAY_TO_NIGHT_SWITCH_TIME) / CurrentSkyRenderingPreset.DayNightTransitionTime;
            }
            else
            {
                from = to = (EAzureCustomRenderingState)m_CurrentRenderingDataIndex;
                t = 0.0f;
            }

            var blendPreset = m_IsInPresetTransition && m_TargetSkyRenderingPreset != null;
            var autoDirectionalLightColor = CurrentControllerPreset.AutoDirectionalLightColor;
            if (autoDirectionalLightColor)
            {
                if (blendPreset)
                {
                    var currentRenderingDatas = GetSkyRenderingDatas(CurrentSkyRenderingPreset);
                    var currentCommonRenderingData = GetSkyRenderingCommonData(CurrentSkyRenderingPreset);
                    var currentFromRenderingData = currentRenderingDatas[(int)from];
                    var currentToRenderingData = currentRenderingDatas[(int)to];

                    var targetRenderingDatas = GetSkyRenderingDatas(m_TargetSkyRenderingPreset);
                    var targetCommonRenderingData = GetSkyRenderingCommonData(m_TargetSkyRenderingPreset);
                    var targetFromRenderingData = targetRenderingDatas[(int)from];
                    var targetToRenderingData = targetRenderingDatas[(int)to];

                    var currentFromDirLightColor = currentFromRenderingData.AutoUpdateDirectionalLight ? currentCommonRenderingData.SunIrradianceColor * currentFromRenderingData.CurrentSunIlluminance * currentFromRenderingData.LightColorIntensity : Vector3.zero;
                    var targetFromDirLightColor = targetFromRenderingData.AutoUpdateDirectionalLight ? targetCommonRenderingData.SunIrradianceColor * targetFromRenderingData.CurrentSunIlluminance * targetFromRenderingData.LightColorIntensity : Vector3.zero;
                    var currentToDirLightColor = currentToRenderingData.AutoUpdateDirectionalLight ? currentCommonRenderingData.SunIrradianceColor * currentToRenderingData.CurrentSunIlluminance * currentToRenderingData.LightColorIntensity : Vector3.zero;
                    var targetToDirLightColor = targetToRenderingData.AutoUpdateDirectionalLight ? targetCommonRenderingData.SunIrradianceColor * targetToRenderingData.CurrentSunIlluminance * targetToRenderingData.LightColorIntensity : Vector3.zero;
                    m_LightColorLerpInfo.FromDirectionalLightColor = AzureCustomSkyRenderingUtils.Lerp(currentFromDirLightColor, targetFromDirLightColor, m_PresetTransitionProgress);
                    m_LightColorLerpInfo.ToDirectionalLightColor = AzureCustomSkyRenderingUtils.Lerp(currentToDirLightColor, targetToDirLightColor, m_PresetTransitionProgress);
                    m_LightColorLerpInfo.DayNightDirectionalLightLerpTime = t;
                }
                else
                {
                    var currentRenderingDatas = GetSkyRenderingDatas(CurrentSkyRenderingPreset);
                    var currentCommonRenderingData = GetSkyRenderingCommonData(CurrentSkyRenderingPreset);
                    var fromRenderingData = currentRenderingDatas[(int)from];
                    var toRenderingData = currentRenderingDatas[(int)to];
                    m_LightColorLerpInfo.FromDirectionalLightColor = fromRenderingData.AutoUpdateDirectionalLight ? currentCommonRenderingData.SunIrradianceColor * fromRenderingData.CurrentSunIlluminance * fromRenderingData.LightColorIntensity : Vector3.zero;
                    m_LightColorLerpInfo.ToDirectionalLightColor = toRenderingData.AutoUpdateDirectionalLight ? currentCommonRenderingData.SunIrradianceColor * toRenderingData.CurrentSunIlluminance * toRenderingData.LightColorIntensity : Vector3.zero;
                    m_LightColorLerpInfo.DayNightDirectionalLightLerpTime = t;
                }
            }

            float minCoszenithAngle = Mathf.Sin(CurrentControllerPreset.MinLightAltitude * Mathf.Deg2Rad);
            float dayNightTransitionTime = CurrentSkyRenderingPreset.DayNightTransitionTime * 0.5f;
            //Ambient Light Transition
            if ((TimeOfDay + dayNightTransitionTime) >= NIGHT_TO_DAY_SWITCH_TIME && (TimeOfDay - dayNightTransitionTime ) < NIGHT_TO_DAY_SWITCH_TIME)
            {
                from = EAzureCustomRenderingState.Moon;
                to = EAzureCustomRenderingState.Sun;
                t = (TimeOfDay + dayNightTransitionTime - NIGHT_TO_DAY_SWITCH_TIME) / CurrentSkyRenderingPreset.DayNightTransitionTime;

                m_LightColorLerpInfo.FromLightCosZenithAngle = Mathf.Max(minCoszenithAngle, Vector3.Dot(Vector3.up, GetLightRotation(NIGHT_TO_DAY_SWITCH_TIME - dayNightTransitionTime) * -Vector3.forward));
                m_LightColorLerpInfo.ToLightCosZenithAngle = Mathf.Max(minCoszenithAngle, Vector3.Dot(Vector3.up, GetLightRotation(NIGHT_TO_DAY_SWITCH_TIME + dayNightTransitionTime) * -Vector3.forward));
            }
            else if ((TimeOfDay + dayNightTransitionTime) >= DAY_TO_NIGHT_SWITCH_TIME && (TimeOfDay - dayNightTransitionTime ) < DAY_TO_NIGHT_SWITCH_TIME )
            {
                from = EAzureCustomRenderingState.Sun;
                to = EAzureCustomRenderingState.Moon;
                t = (TimeOfDay + dayNightTransitionTime  - DAY_TO_NIGHT_SWITCH_TIME) / CurrentSkyRenderingPreset.DayNightTransitionTime;

                m_LightColorLerpInfo.FromLightCosZenithAngle = Mathf.Max(minCoszenithAngle, Vector3.Dot(Vector3.up, GetLightRotation(DAY_TO_NIGHT_SWITCH_TIME - dayNightTransitionTime) * -Vector3.forward));
                m_LightColorLerpInfo.ToLightCosZenithAngle = Mathf.Max(minCoszenithAngle, Vector3.Dot(Vector3.up, GetLightRotation(DAY_TO_NIGHT_SWITCH_TIME + dayNightTransitionTime) * -Vector3.forward));
            }
            else
            {
                from = to = (EAzureCustomRenderingState) m_CurrentRenderingDataIndex;
                t = 0.0f;

                m_LightColorLerpInfo.FromLightCosZenithAngle = m_LightColorLerpInfo.ToLightCosZenithAngle = Mathf.Max(minCoszenithAngle, Vector3.Dot(Vector3.up, m_CurrentLightRotation * -Vector3.forward));
            }

            m_LightColorLerpInfo.FromState = from;
            m_LightColorLerpInfo.ToState = to;

            var autoAmbientLightColor = CurrentControllerPreset.AutoAmbientLightColor;
            if (blendPreset)
            {
                CurrentSkyRenderingPreset.UpdateTimeOfDay(TimeOfDay, autoDirectionalLightColor, autoAmbientLightColor, m_MainLightAdjuster, m_EnvironmentAdjuster);
                m_TargetSkyRenderingPreset.UpdateTimeOfDay(TimeOfDay, autoDirectionalLightColor, autoAmbientLightColor, m_TargetMainLightAdjuster, m_TargetEnviromentAdjuster);

            }
            else
            {
                CurrentSkyRenderingPreset.UpdateTimeOfDay(TimeOfDay, autoDirectionalLightColor, autoAmbientLightColor, m_MainLightAdjuster, m_EnvironmentAdjuster);
            }

            if (autoAmbientLightColor)
            {
                if (blendPreset)
                {
                    var currentRenderingDatas = GetSkyRenderingDatas(CurrentSkyRenderingPreset);
                    var currentCommonRenderingData = GetSkyRenderingCommonData(CurrentSkyRenderingPreset);
                    var currentFromRenderingData = currentRenderingDatas[(int)from];
                    var currentToRenderingData = currentRenderingDatas[(int)to];

                    var targetRenderingDatas = GetSkyRenderingDatas(m_TargetSkyRenderingPreset);
                    var targetCommonRenderingData = GetSkyRenderingCommonData(m_TargetSkyRenderingPreset);
                    var targetFromRenderingData = targetRenderingDatas[(int)from];
                    var targetToRenderingData = targetRenderingDatas[(int)to];

                    var currentFromAmbLightColor = currentFromRenderingData.AutoUpdateAmbientLight ? currentCommonRenderingData.SunIrradianceColor * currentFromRenderingData.CurrentSunIlluminance * currentFromRenderingData.AmbientColorIntensity : Vector3.zero;
                    var targetFromAmbLightColor = currentToRenderingData.AutoUpdateAmbientLight ? targetCommonRenderingData.SunIrradianceColor * targetFromRenderingData.CurrentSunIlluminance * targetFromRenderingData.AmbientColorIntensity : Vector3.zero;
                    var currentToAmbLightColor = targetFromRenderingData.AutoUpdateAmbientLight ? currentCommonRenderingData.SunIrradianceColor * currentToRenderingData.CurrentSunIlluminance * currentToRenderingData.AmbientColorIntensity : Vector3.zero;
                    var targetToAmbLightColor = targetToRenderingData.AutoUpdateAmbientLight ? targetCommonRenderingData.SunIrradianceColor * targetToRenderingData.CurrentSunIlluminance * targetToRenderingData.AmbientColorIntensity : Vector3.zero;

                    m_LightColorLerpInfo.FromAmbientLightColor = AzureCustomSkyRenderingUtils.Lerp(currentFromAmbLightColor, targetFromAmbLightColor, m_PresetTransitionProgress);
                    m_LightColorLerpInfo.ToAmbientLightColor = AzureCustomSkyRenderingUtils.Lerp(currentToAmbLightColor, targetToAmbLightColor, m_PresetTransitionProgress);
                    m_LightColorLerpInfo.DayNightAmbientLightLerpTime = t;

                }
                else
                {
                    var currentRenderingDatas = GetSkyRenderingDatas(CurrentSkyRenderingPreset);
                    var fromRenderingData = currentRenderingDatas[(int)from];
                    var toRenderingData = currentRenderingDatas[(int)to];
                    m_LightColorLerpInfo.FromAmbientLightColor = fromRenderingData.AutoUpdateAmbientLight ? GetSkyRenderingCommonData(CurrentSkyRenderingPreset).SunIrradianceColor * fromRenderingData.CurrentSunIlluminance * fromRenderingData.AmbientColorIntensity : Vector3.zero;
                    m_LightColorLerpInfo.ToAmbientLightColor = toRenderingData.AutoUpdateAmbientLight ? GetSkyRenderingCommonData(CurrentSkyRenderingPreset).SunIrradianceColor * toRenderingData.CurrentSunIlluminance * toRenderingData.AmbientColorIntensity : Vector3.zero;
                    m_LightColorLerpInfo.DayNightAmbientLightLerpTime = t;
                }
            }

            if (blendPreset)
            {
                var currentRenderingDatas = GetSkyRenderingDatas(CurrentSkyRenderingPreset);
                var targetRenderingDatas = GetSkyRenderingDatas(m_TargetSkyRenderingPreset);
                var currentRenderingData = currentRenderingDatas[m_CurrentRenderingDataIndex];
                var nextRenderingData = currentRenderingDatas[(m_CurrentRenderingDataIndex + 1) % 2];
                var targetCurrentRenderingData = targetRenderingDatas[m_CurrentRenderingDataIndex];
                var targetNextRenderingData = targetRenderingDatas[(m_CurrentRenderingDataIndex + 1) % 2];

                m_CurrentRenderingData.Lerp(currentRenderingData, targetCurrentRenderingData, m_PresetTransitionProgress);
                m_NextRenderingData.Lerp(nextRenderingData, targetNextRenderingData, m_PresetTransitionProgress);

                m_CommonRenderingData.Lerp(GetSkyRenderingCommonData(CurrentSkyRenderingPreset), GetSkyRenderingCommonData(m_TargetSkyRenderingPreset), m_PresetTransitionProgress);
                m_FogRenderingData.Lerp(GetFogRenderingData(CurrentSkyRenderingPreset), GetFogRenderingData(m_TargetSkyRenderingPreset), m_PresetTransitionProgress);

                m_LightData.Lerp(GetSkyRenderingLightData(CurrentSkyRenderingPreset), GetSkyRenderingLightData(m_TargetSkyRenderingPreset), m_PresetTransitionProgress);
                m_AdvanceData.Lerp(GetSkyRenderingAdvanceData(CurrentSkyRenderingPreset), GetSkyRenderingAdvanceData(m_TargetSkyRenderingPreset), m_PresetTransitionProgress);
            }
            else
            {
                var currentRenderingDatas = GetSkyRenderingDatas(CurrentSkyRenderingPreset);
                var currentRenderingData = currentRenderingDatas[m_CurrentRenderingDataIndex];
                var nextRenderingData = currentRenderingDatas[(m_CurrentRenderingDataIndex + 1) % 2];
  
                m_CurrentRenderingData.CopyFrom(currentRenderingData);
                m_NextRenderingData.CopyFrom(nextRenderingData);

                m_CommonRenderingData.CopyFrom(GetSkyRenderingCommonData(CurrentSkyRenderingPreset));
                m_FogRenderingData.CopyFrom(GetFogRenderingData(CurrentSkyRenderingPreset));

                m_LightData.CopyFrom(GetSkyRenderingLightData(CurrentSkyRenderingPreset));
                m_AdvanceData.CopyFrom(GetSkyRenderingAdvanceData(CurrentSkyRenderingPreset));
            }

            CurrentControllerPreset.UpdateTimeOfDay(TimeOfDay, m_SkyRenderingFeature);
        }

        private void ChangeRenderingState(EAzureCustomRenderingState state)
        {
            if (m_CurrentRenderingDataIndex == (int)state)
                return;

            if ((int) state < CurrentSkyRenderingDatas.Length)
                m_CurrentRenderingDataIndex = (int)state;
        }

        [ContextMenu("Create Rendering Data")]
        void CreateCustomRenderingData()
        {
            UpdateAzureSkySystemObjects();
        }

        private void UpdateAzureSkySystemObjects()
        {
            if (AzureReflectionProbe == null)
                AzureReflectionProbe = gameObject.GetComponentInChildren<ReflectionProbe>(true);

            if (SunLensFlares == null)
                SunLensFlares = gameObject.GetComponentsInChildren<LensFlareComponentSRP>(true);
            if (Clouds == null)
                Clouds = gameObject.transform.Find("StaticCloudBillboards");
            if (Clouds != null)
            {
                CloudBillboardsMesh = null;
                var meshFilter = Clouds.GetComponent<MeshFilter>();
                if (meshFilter != null)
                    CloudBillboardsMesh = meshFilter.sharedMesh;

                CloudBillboardsTexture = null;
                var meshRenderer = Clouds.GetComponent<MeshRenderer>();
                if (meshRenderer != null)
                {
                    var materials = new List<Material>();
                    meshRenderer.GetSharedMaterials(materials);
                    if (materials.Count > 0)
                        CloudBillboardsTexture = materials[0].GetTexture("_MainTex");
                }
            }
        }

        private void SetRenderFeatureActive<T>(bool enabled) where T : ScriptableRendererFeature
        {
            var urpAsset = (UniversalRenderPipelineAsset)GraphicsSettings.currentRenderPipeline;
            if (urpAsset != null)
            {
                foreach (var renderaData in urpAsset.m_RendererDataList)
                {
                    if (renderaData == null)
                        continue;

                    foreach (var feature in renderaData.rendererFeatures)
                    {
                        if (feature.GetType() == typeof(T))
                        {
                            feature.SetActive(enabled);
                            break;
                        }
                    }
                }
            }
        }

        private void Start()
        {
            ComputeTimeProgressionStep();
            UpdateAzureSkySystemObjects();

            m_CurrentRendererIndex = CurrentSkyRenderingDatas != null && CurrentSkyRenderingDatas.Length > 0 ? 0 : m_CurrentRendererIndex;
        }

        private SkyRenderingFeature GetSkyRenderingFeature()
        {
            var urpAsset = (UniversalRenderPipelineAsset)GraphicsSettings.currentRenderPipeline;
            if (urpAsset != null)
            {
                var currentRenderIndex = urpAsset.m_DefaultRendererIndex;
                if (Application.isPlaying)
                {
                    var mainCamera = Camera.main;
                    if (mainCamera != null)
                    {
                        var cameraData = mainCamera.GetComponent<UniversalAdditionalCameraData>();
                        if (cameraData != null)
                        {
                            var renderIndex = cameraData.RenderIndex;
                            if (renderIndex >= 0 && renderIndex < urpAsset.m_RendererDataList.Length &&
                                urpAsset.m_RendererDataList[renderIndex] != null)
                                currentRenderIndex = renderIndex;
                        }
                    }
                }

                if (m_SkyRenderingFeature != null && m_CurrentRendererIndex == currentRenderIndex)
                {
                    return m_SkyRenderingFeature;
                }

                var renderaData = urpAsset.m_RendererDataList[currentRenderIndex];
                if (renderaData != null)
                {
                    foreach (var feature in renderaData.rendererFeatures)
                    {
                        if (feature is SkyRenderingFeature)
                        {
                            m_SkyRenderingFeature = feature as SkyRenderingFeature;
                            m_CurrentRendererIndex = currentRenderIndex;
                            return m_SkyRenderingFeature;
                        }
                    }
                }
            }

            m_SkyRenderingFeature = null;
            return null;
        }

        private static int SpecCube0Hdr = Shader.PropertyToID("_SpecCube0_HDR");
        private void SetReflectionCubemap(bool customSkyRendering)
        {
            if (customSkyRendering)
            {   
                if (GetSkyRenderingFeature() == null)
                    return;

                if (!m_OriginReflectionTextureCached)
                {
                    m_OriginAzureReflectionProbeTexture = AzureReflectionProbe.texture;
                    m_OriginAzureReflectionIntensity = AzureReflectionProbe.intensity;
                    m_OriginEnviromentReflectionTexture = RenderSettings.customReflectionTexture;
                    m_OriginEnviromentReflectionIntensity = RenderSettings.reflectionIntensity;

                    m_OriginSpecCube0Hdr = Shader.GetGlobalVector(SpecCube0Hdr);
                    m_OriginReflectionTextureCached = true;
                }

                if (m_AdvanceData.RealTimeReflectionCubemap)
                {
                    Texture reflectionCubemap = null;
                    if (GetSkyRenderingFeature().TryGetReflectionCubemap(out reflectionCubemap))
                    {
                        var specCube0Hdr = new Vector4(1.0f, 1.0f, 0.0f, 0.0f);
                        if (AzureReflectionProbe.customBakedTexture != reflectionCubemap)
                        {
                            AzureReflectionProbe.customBakedTexture = reflectionCubemap;
                            specCube0Hdr = AzureReflectionProbe.textureHDRDecodeValues;
                        }

                        if (RenderSettings.customReflectionTexture != reflectionCubemap)
                        {
                            RenderSettings.customReflectionTexture = reflectionCubemap;
                            Shader.SetGlobalVector(SpecCube0Hdr, specCube0Hdr);
                        }
                    }
                }
                else
                {
                    var reflectionCubemap = m_RuntimeReflectionProbeTexture != null ? m_RuntimeReflectionProbeTexture : m_AdvanceData.ReflectionCubemap;
                    if (AzureReflectionProbe.customBakedTexture != reflectionCubemap)
                        AzureReflectionProbe.customBakedTexture = reflectionCubemap;

                    var globalReflectionCubemap = m_RuntimeEnviromentReflectionTexture != null ? m_RuntimeReflectionProbeTexture : m_AdvanceData.GlobalReflectionCubemap;
                    if (RenderSettings.customReflectionTexture != globalReflectionCubemap)
                        RenderSettings.customReflectionTexture = globalReflectionCubemap;
                }
            }
            else
            {
                if (m_OriginReflectionTextureCached)
                {

                    AzureReflectionProbe.customBakedTexture = m_OriginAzureReflectionProbeTexture;
                    AzureReflectionProbe.intensity = m_OriginAzureReflectionIntensity;
                    RenderSettings.customReflectionTexture = m_OriginEnviromentReflectionTexture;
                    RenderSettings.reflectionIntensity = m_OriginEnviromentReflectionIntensity;
                    
                    Shader.SetGlobalVector(SpecCube0Hdr, m_OriginSpecCube0Hdr);
                    
                    m_OriginEnviromentReflectionTexture = null;
                    m_OriginAzureReflectionProbeTexture = null;

                    m_OriginReflectionTextureCached = false;
                }
            }
        }

        private void DeactiveAzureSky()
        {
            if(RenderSettings.skybox != null)
            {
                m_OriginSkyMaterial = RenderSettings.skybox;
                RenderSettings.skybox = null;
            }

            if (RenderSettings.ambientMode != AmbientMode.Trilight)
            {
                m_OriginAmbientMode = RenderSettings.ambientMode;
                m_OriginAmbientLightColor = RenderSettings.ambientLight;
                m_OriginAmbientEquatorColor = RenderSettings.ambientEquatorColor;
                m_OriginAmbientGroundColor = RenderSettings.ambientGroundColor;
                RenderSettings.ambientMode = AmbientMode.Trilight;
            }

            ActiveDirectionalLight(true);

            if (!m_AzureSkyActive && m_FeatureActive)
                return;
            m_AzureSkyActive = false;

            SetRenderFeatureActive<SkyRenderingFeature>(true);
            SetRenderFeatureActive<AzureFogScatteringFeature>(false);
            GetSkyRenderingFeature()?.UpdateGlobalKeyWords();
            m_FeatureActive = true;

            if (Clouds != null)
            {
                Clouds.gameObject.SetActive(false);
            }

            if (Camera.main != null)
            {
               Camera.main.clearFlags = CameraClearFlags.Depth;
            }
        }

        private void ActiveAzureSky(bool forceActive = false)
        {
            if (m_AzureSkyActive && !m_FeatureActive)
                return;

            if (!forceActive && Instance != null && Instance != this)
            {
                ActiveDirectionalLight(false);
                m_AzureSkyActive = true;
                return;
            }

            m_AzureSkyActive = true;

            //The old tod system is deprecated
            SetRenderFeatureActive<SkyRenderingFeature>(false);
            GetSkyRenderingFeature()?.UpdateGlobalKeyWords();

            m_FeatureActive = false;

            if (m_OriginSkyMaterial != null)
                RenderSettings.skybox = m_OriginSkyMaterial;

            if (s_ActiveControllers.Count > 1)
                ActiveDirectionalLight(false);

            return;

            SetRenderFeatureActive<AzureFogScatteringFeature>(true);
            SetReflectionCubemap(false);

            if (Camera.main != null)
                Camera.main.clearFlags = CameraClearFlags.Skybox;


            if (m_OriginAmbientMode != RenderSettings.ambientMode)
            {
                RenderSettings.ambientMode = m_OriginAmbientMode;
                RenderSettings.ambientLight = m_OriginAmbientLightColor;
                RenderSettings.ambientEquatorColor = m_OriginAmbientEquatorColor;
                RenderSettings.ambientGroundColor = m_OriginAmbientGroundColor;
            }

            if (Clouds != null)
            {
                Clouds.gameObject.SetActive(true);
            }
        }

        private void ActiveDirectionalLight(bool active)
        {
            if (ObjectControllerData.DirectionalLight != null)
            {
                var directionalLight = ObjectControllerData.DirectionalLight.GetComponent<Light>();
                if (directionalLight != null && directionalLight.enabled != active)
                    directionalLight.enabled = active;         
            }
        }

        private void Awake()
        {
            LastUsedSkyRenderingPresetIndex = CurrentSkyRenderingPresetIndex;
            m_ForceUpdate = true;
        }

        private void Update()
        {
            var feature = GetSkyRenderingFeature();
            if (feature == null || Application.isPlaying && !feature.isActive)
            {
                ActiveAzureSky(true);
                return;
            }

            if (Instance != this)
                return;

            if (CurrentControllerPresetIndex < 0 || ControllerPresets == null || CurrentControllerPresetIndex >= ControllerPresets.Count)
                CurrentControllerPresetIndex = 0;

            CurrentControllerPreset = GetControllerPreset(CurrentControllerPresetIndex);
            if (CurrentControllerPreset == null)
            {
                return;
            }

            if (CurrentSkyRenderingPresetIndex < 0 || SkyRenderingPresets == null || CurrentSkyRenderingPresetIndex >= SkyRenderingPresets.Count)
                CurrentSkyRenderingPresetIndex = 0;

            CurrentSkyRenderingPreset = GetSkyRenderingPreset(CurrentSkyRenderingPresetIndex);
            m_TargetSkyRenderingPreset = GetSkyRenderingPreset(m_TargetSkyRenderingPresetIndex);

            if (CurrentSkyRenderingPreset == null)
            {
                Debug.LogError("AzureCustomSkyRenderingController does not have a valid SkyRenderingPreset!");
                return;
            }

            ProfilerApi.BeginSample(EProfileFunc.Update_AzureSkySystem);

            DeactiveAzureSky();
            UpdateRenderingTimeOfDay();
            UpdateDirectionalLightSettings();
            UpdateAmbientLightColor();
            UpdateReflectionData();

            ActiveSkyFeatures();
            UpdateGlobalShaderUniforms();
            UpdateSkyRenderingSettings();
            UpdateCloudRenderingSettings();

#if UNITY_EDITOR
            feature.GetMipFogCubemap(out DebugMipFogCubemap, out DebugMipFogFilteredCubemap0, out DebugMipFogFilteredCubemap1);
            UpdateSceneViewSettings();
#endif
            m_ForceUpdate = false;
            ProfilerApi.EndSample(EProfileFunc.Update_AzureSkySystem);
        }

        private bool IsNightTime()
        {
            return m_CurrentRenderingDataIndex == (int)EAzureCustomRenderingState.Moon;
        }

        private void UpdateDirectionalLightSettings()
        {
            if (ObjectControllerData.DirectionalLight != null)
            {
                var directionalLight = ObjectControllerData.DirectionalLight.GetComponent<Light>();

                StaticShadowMapAPI.SetDirectionalLight(directionalLight);

                Color sunColor = Color.white;
                Color sunFlaresColor = Color.white;
                Color lightColor = directionalLight.color;
                float lightIntensity = directionalLight.intensity;
                float shadowStrength = directionalLight.shadowStrength;
                var autoDirectionalLightColor = CurrentControllerPreset.AutoDirectionalLightColor;
                if (autoDirectionalLightColor && m_CurrentRenderingData.AutoUpdateDirectionalLight)
                {
                    //Bais Directional Light Color
                    Color fromScatColor, toScatColor;
                    var lightColorBias = m_LightData.CurrentDirectionalLightColorBias;
                    if (m_LightColorLerpInfo.FromState != m_LightColorLerpInfo.ToState)
                    {
                        fromScatColor = m_SkyRenderingFeature.GetDirectionalLightColorScattering(m_LightColorLerpInfo.FromLightCosZenithAngle);
                        var fromScatColorMag = new Vector3(fromScatColor.r, fromScatColor.g, fromScatColor.b).magnitude;  
                        fromScatColor = Color.Lerp(fromScatColor, Color.white * fromScatColorMag, lightColorBias);

                        toScatColor = m_SkyRenderingFeature.GetDirectionalLightColorScattering(m_LightColorLerpInfo.ToLightCosZenithAngle);
                        var toScatColorMag = new Vector3(toScatColor.r, toScatColor.g, toScatColor.b).magnitude;
                        toScatColor = Color.Lerp(toScatColor, Color.white * toScatColorMag, lightColorBias);
                    }
                    else
                    {
                        fromScatColor = m_SkyRenderingFeature.GetDirectionalLightColorScattering(m_LightColorLerpInfo.FromLightCosZenithAngle);
                        var fromScatColorMag = new Vector3(fromScatColor.r, fromScatColor.g, fromScatColor.b).magnitude;
                        fromScatColor = Color.Lerp(fromScatColor, Color.white * fromScatColorMag, lightColorBias);

                        toScatColor = fromScatColor;
                    }

                    sunColor = m_LightColorLerpInfo.GetDirectionalLightLerpColor(fromScatColor, toScatColor);
                    var color = new Vector3(sunColor.r, sunColor.g, sunColor.b);
                    var colorIntensity = color.magnitude;
                    color = color / colorIntensity;

                    lightColor = new Color(Mathf.Max(color.x, 0.01f), Mathf.Max(color.y, 0.01f), Mathf.Max(color.z, 0.01f), 1.0f);
                    lightIntensity = Mathf.Max(colorIntensity, 0.01f); // make sure unity doesn't disable this light


                    sunFlaresColor = new Color(m_CommonRenderingData.SunIrradianceColor.x, m_CommonRenderingData.SunIrradianceColor.y, m_CommonRenderingData.SunIrradianceColor.z);
                    sunFlaresColor *= m_CurrentRenderingDataIndex == (int) EAzureCustomRenderingState.Sun ?
                                        m_CurrentRenderingData.CurrentSunIlluminance * m_CurrentRenderingData.LightColorIntensity : m_NextRenderingData.CurrentSunIlluminance * m_NextRenderingData.LightColorIntensity;
                    var sunCosAngle = Mathf.Clamp01(Vector3.Dot(Vector3.up, m_SunLightRotation * -Vector3.forward));
                    sunFlaresColor *= m_SkyRenderingFeature.GetDirectionalLightColorScattering(sunCosAngle);
                    var sunFlaresColorMag = new Vector3(sunFlaresColor.r, sunFlaresColor.g, sunFlaresColor.b).magnitude;
                    sunFlaresColor = Color.Lerp(sunFlaresColor, Color.white * sunFlaresColorMag, lightColorBias);
                }
                else if (!autoDirectionalLightColor)
                {
                    lightColor = m_LightData.CurrentDirectionalLightColor;
                    lightIntensity = m_LightData.CurrentDirectionalLightIntensity;
                    shadowStrength = m_LightData.CurrentDirectionShadowIntensity;
                    shadowStrength = AdjustShadowIntensity(shadowStrength);
                    sunColor = lightColor.NormalizeRGB();
                    sunFlaresColor = sunColor;
                }

                if (directionalLight.color  != lightColor)
                    directionalLight.color = lightColor;

                const float MIN_LIGHT_INTENSITY = 0.001f;
                lightIntensity = Mathf.Max(MIN_LIGHT_INTENSITY, AdjustMainLightIntensity(lightIntensity));
                if (directionalLight.intensity != lightIntensity)
                    directionalLight.intensity = lightIntensity;
                if (directionalLight.shadowStrength != shadowStrength)
                    directionalLight.shadowStrength = shadowStrength;
                directionalLight.gameObject.SetActive(lightIntensity > MIN_LIGHT_INTENSITY);

                Shader.SetGlobalVector(MainLightColor_ID, lightColor.linear * lightIntensity);
                Shader.SetGlobalVector(MainLightPosition_ID, -directionalLight.transform.forward);

                if (SunLensFlares != null)
                {
                    var sunTintColor = m_CurrentRenderingDataIndex == (int)EAzureCustomRenderingState.Sun ?
                                                                            m_CurrentRenderingData.CurrentSunTintColor : m_NextRenderingData.CurrentSunTintColor;
                                                                                                
                    for (int i = 0; i < SunLensFlares.Length; i++)
                    {
                        if (SunLensFlares[i] != null)
                            SunLensFlares[i].globalColorTint = sunFlaresColor * sunTintColor;
                    }
                }

                m_DirectionalLightColor = directionalLight.color * directionalLight.intensity;
                m_DirectionalLightLastUpdateTime += Time.deltaTime;
                var directionalLightUpdateRate = m_ForceUpdate ? 0 : (m_CustomDirectionalLightUpdateRate > 0 ? m_CustomDirectionalLightUpdateRate : CurrentControllerPreset.DirectionalLightUpdateRate);
                if (m_DirectionalLightLastUpdateTime > directionalLightUpdateRate)
                {
                    ObjectControllerData.DirectionalLight.transform.rotation = m_CurrentLightRotation;
                    m_DirectionalLightLastUpdateTime = 0.0f;
                }
                
            }
            else
                StaticShadowMapAPI.SetDirectionalLight(null);

            if (SunLensFlares != null)
            {
                if (CurrentControllerPreset.AutoDirectionalLightColor && m_CurrentRenderingData.AutoUpdateDirectionalLight)
                {
                    var cosAngle = Mathf.Clamp01(Vector3.Dot(Vector3.up, m_SunBodyRotation * -Vector3.forward));
                    cosAngle = cosAngle * cosAngle;
                    var intensity = m_CurrentRenderingDataIndex == (int)EAzureCustomRenderingState.Sun ?
                                                            m_CurrentRenderingData.CurrentSunIntensity : m_NextRenderingData.CurrentSunIntensity;
                    intensity *= cosAngle;
                    for (int i = 0; i < SunLensFlares.Length; i++)
                    {
                        if (SunLensFlares[i] != null)
                            SunLensFlares[i].intensity = intensity;
                    }
                }
                else
                {
                    if (SunLensFlares[0] != null)
                        SunLensFlares[0].intensity = m_AdvanceData.CurrentLensFlareIntensity;
                    if (SunLensFlares[1] != null)
                        SunLensFlares[1].intensity = m_AdvanceData.CurrentLensFlareIntensity2;
                }
            }
        }

        private void UpdateAmbientLightColor()
        {
            var autoAmbientLightColor = CurrentControllerPreset.AutoAmbientLightColor;
            if (autoAmbientLightColor && m_CurrentRenderingData.AutoUpdateAmbientLight)
            {
                if (RenderSettings.ambientMode != AmbientMode.Flat)
                    RenderSettings.ambientMode = AmbientMode.Flat;

                Color fromScatColor, toScatColor;
                if (m_LightColorLerpInfo.FromState != m_LightColorLerpInfo.ToState)
                {
                    fromScatColor = m_SkyRenderingFeature.GetAmbientLightColorScattering(m_LightColorLerpInfo.FromLightCosZenithAngle);
                    toScatColor = m_SkyRenderingFeature.GetAmbientLightColorScattering(m_LightColorLerpInfo.ToLightCosZenithAngle);
                }
                else
                {
                    fromScatColor = m_SkyRenderingFeature.GetAmbientLightColorScattering(m_LightColorLerpInfo.FromLightCosZenithAngle);
                    toScatColor = fromScatColor;
                }

                var ambientColor =  m_LightColorLerpInfo.GetAmbientLightLerpColor(fromScatColor, toScatColor);
                //Compensation Ambient Light Color
                ambientColor += m_LightData.CurrentAmbientLightCompensationColor;

                if (RenderSettings.ambientLight != ambientColor)
                    RenderSettings.ambientLight = ambientColor;
            }
            else if (!autoAmbientLightColor)
            {
                if (RenderSettings.ambientMode != AmbientMode.Trilight)
                    RenderSettings.ambientMode = AmbientMode.Trilight;

                var ambientIntensity = m_LightData.CurrentAmbientLightIntensity;
                if (RenderSettings.ambientIntensity != ambientIntensity)
                    RenderSettings.ambientIntensity = ambientIntensity;

                var ambientSkyColor = m_LightData.CurrentAmbientSkyColor;
                if (RenderSettings.ambientSkyColor != ambientSkyColor)
                    RenderSettings.ambientSkyColor = ambientSkyColor;

                var ambientEquatorColor = m_LightData.CurrentAmbientEquatorColor;
                if (RenderSettings.ambientEquatorColor != ambientEquatorColor)
                    RenderSettings.ambientEquatorColor = ambientEquatorColor;

                var ambientGroundColor = m_LightData.CurrentAmbientGroundColor;
                if (RenderSettings.ambientGroundColor != ambientGroundColor)
                    RenderSettings.ambientGroundColor = ambientGroundColor;
            }

            m_AmbientLightColor = RenderSettings.ambientLight;
        }

        private void UpdateReflectionData()
        {
            SetReflectionCubemap(true);

            if (AzureReflectionProbe != null)
            {
                var intensity = AdjustReflectionIntensity(m_AdvanceData.CurrentReflectionIntensity);
                if (AzureReflectionProbe.intensity != intensity)
                    AzureReflectionProbe.intensity = intensity;

                Shader.SetGlobalFloat(AzureShaderUniforms.IndirectSpecularIntensity, intensity);
            }

            if (RenderSettings.reflectionIntensity != m_AdvanceData.CurrentGlobalReflectionIntensity)
                RenderSettings.reflectionIntensity = m_AdvanceData.CurrentGlobalReflectionIntensity;
        }

        private void ActiveSkyFeatures()
        {
            if (SunLensFlares != null)
            {
                var sunCosAngle = Vector3.Dot(Vector3.up, m_SunBodyRotation * -Vector3.forward);
                var active = RenderSunLensFlare && sunCosAngle > 0.0f;
                for (int i = 0; i < SunLensFlares.Length; i++)
                {
                    if (SunLensFlares[i] != null && SunLensFlares[i].gameObject.activeSelf != active)
                        SunLensFlares[i].gameObject.SetActive(active);
                }
            }
        }

        private Matrix4x4 GetSkyBackGroundRotationMatrix()
        {
            var rotation =  m_MoonBodyRotation * Quaternion.Euler(0.0f, -180.0f, 0.0f) * Quaternion.Euler(m_CommonRenderingData.SkyBackGroundRotation);
            return Matrix4x4.TRS(Vector3.zero, rotation, Vector3.one);
        }

        private void UpdateGlobalShaderUniforms()
        {
            Shader.SetGlobalColor(AzureShaderUniforms.TerrainAmbientColor, m_AdvanceData.CurrentTerrainAmbientColor);
            Shader.SetGlobalColor(AzureShaderUniforms.WaterReflectionColor, m_AdvanceData.CurrentWaterRelfectionColor);
            Shader.SetGlobalColor(AzureShaderUniforms.UnderwaterFogColor, m_AdvanceData.CurrentUnderwaterFogColor);
        }

        private void UpdateSkyRenderingSettings()
        {
            var settings = m_SkyRenderingFeature.GetSettings();

            settings.CurrentTimeOfDay = TimeOfDay;
            settings.RenderSun = m_CurrentRenderingData.RenderSun;

            settings.NeedRecomputeLuts = m_NeedRecomputeLuts;
            m_NeedRecomputeLuts = false;

            m_SkyRenderingFeature.SetLightColor(m_CurrentLightRotation * -Vector3.forward, m_DirectionalLightColor, m_AmbientLightColor);
            settings.RealTimeReflectionCubemap = m_AdvanceData.RealTimeReflectionCubemap;

            settings.HighQualityMultiScattering = m_CommonRenderingData.HighQualityMultiScattering;
            settings.MultipleScatteringFactor = m_CommonRenderingData.CurrentMultipleScatteringFactor;
            settings.ViewRayMarchMinSPP = m_CommonRenderingData.ViewRayMarchMinSPP;
            settings.ViewRayMarchMaxSPP = m_CommonRenderingData.ViewRayMarchMaxSPP;
            settings.AmbientColorSPP = m_CommonRenderingData.AmbientColorSPP;
            settings.AmbientColorSampleRange = m_CommonRenderingData.AmbientColorSampleRange;
            settings.NonUniformCameraDepthSlice = m_CommonRenderingData.NonUniformCameraDepthSlice;
            settings.CameraScatVolumeScale = m_CommonRenderingData.CameraScatVolumeScale;
            settings.CameraNearDepthScale = m_CommonRenderingData.CurrentCameraNearDepthScale;
            settings.CameraFarDepthScale = m_CommonRenderingData.CurrentCameraFarDepthScale;
            settings.CameraHeightKM = m_CommonRenderingData.CameraHeightKM;

            var sunDirection = m_CurrentCelestialBodyRotation * -Vector3.forward;
            // settings.SunViewAngle = m_CurrentRenderingData.CurrentSunViewAngle;
            settings.SunPitch = Mathf.Asin(sunDirection.y) * 180.0f / Mathf.PI;
            settings.SunYaw = Mathf.Atan2(sunDirection.z, sunDirection.x) * 180.0f / Mathf.PI;
            settings.SunIrradianceColor = m_CommonRenderingData.SunIrradianceColor;
            settings.SunIlluminance = m_CurrentRenderingData.CurrentSunIlluminance;
            settings.SunIntensity = m_CurrentRenderingData.CurrentSunIntensity;
            var sunTintColor = m_CurrentRenderingData.CurrentSunTintColor;
            settings.SunTintColor = new Vector3(sunTintColor.r, sunTintColor.g, sunTintColor.b);
            settings.UpdateAmbientLight = CurrentControllerPreset.AutoAmbientLightColor && m_CurrentRenderingData.AutoUpdateAmbientLight;
            settings.UpdateDirectionalLight = CurrentControllerPreset.AutoDirectionalLightColor && m_CurrentRenderingData.AutoUpdateDirectionalLight;

            settings.UseSunTexture = m_CurrentRenderingData.UseSunTexture;
            settings.SunTexture = m_CurrentRenderingData.SunTexture;
            settings.SunTextureScale = m_CurrentRenderingData.CurrentSunTextureScale;

            bool isDayNightTransition = false;
            float sun2Weight = 0.0f;
            if (TimeOfDay < NIGHT_TO_DAY_SWITCH_TIME && TimeOfDay >= NIGHT_TO_DAY_SWITCH_TIME - CurrentSkyRenderingPreset.DayNightTransitionTime)
            {
                isDayNightTransition = true;

                sun2Weight = 1.0f - (NIGHT_TO_DAY_SWITCH_TIME - TimeOfDay) / CurrentSkyRenderingPreset.DayNightTransitionTime;
            }
            else if (TimeOfDay < DAY_TO_NIGHT_SWITCH_TIME && TimeOfDay >= DAY_TO_NIGHT_SWITCH_TIME - CurrentSkyRenderingPreset.DayNightTransitionTime)
            {
                isDayNightTransition = true;
                sun2Weight = 1.0f - (DAY_TO_NIGHT_SWITCH_TIME - TimeOfDay) / CurrentSkyRenderingPreset.DayNightTransitionTime;
            }
            if (isDayNightTransition)
            {
                settings.Sun2Weight = sun2Weight;
                sunDirection = m_NextCelestialBodyRotation * -Vector3.forward;
                settings.SunPitch2 = Mathf.Asin(sunDirection.y) * 180.0f / Mathf.PI;
                settings.SunYaw2 = Mathf.Atan2(sunDirection.z, sunDirection.x) * 180.0f / Mathf.PI;
                settings.SunIlluminance2 = m_NextRenderingData.CurrentSunIlluminance;
                settings.SunIntensity2 = m_NextRenderingData.CurrentSunIntensity;
                var sunTintColor2 = m_NextRenderingData.CurrentSunTintColor;
                settings.SunTintColor2 = new Vector3(sunTintColor2.r, sunTintColor2.g, sunTintColor2.b);
                settings.SunTextureScale2 = m_NextRenderingData.UseSunTexture ? m_NextRenderingData.CurrentSunTextureScale : 0.0f;
                settings.SunTexture2 = m_NextRenderingData.SunTexture;
            }
            else
            {
                settings.Sun2Weight = 0.0f;
                settings.SunPitch2 = 0.0f;
                settings.SunYaw2 = 0.0f;
                settings.SunIlluminance2 = 0.0f;
                settings.SunIntensity2 = 0.0f;
                settings.SunTintColor2 = Vector3.zero;
                settings.SunTextureScale2 = 0.0f;
                settings.SunTexture2 = null;
            }

            settings.UseSkyBackGroundTexture = m_CommonRenderingData.UseSkyBackGroundTexture;
            settings.SkyBackGroundTexture = m_CommonRenderingData.SkyBackGroundTexture;
            settings.SkyBackGroundIntensity = (EAzureCustomRenderingState)m_CurrentRenderingDataIndex == EAzureCustomRenderingState.Moon ? 
                m_CommonRenderingData.SkyBackGroundIntensity : 0;
            settings.SkyBackGroundColor = m_CommonRenderingData.SkyBackGroundColor;
            settings.SkyBackGroundRotationMatrix = GetSkyBackGroundRotationMatrix();

            settings.GroundAlbedo = m_CommonRenderingData.GroundAlbedo;

            settings.RayleighScatteringColor = m_CommonRenderingData.CurrentRayleighScatteringColor;
            settings.RayleighScatteringColorSaturation = m_CommonRenderingData.CurrentRayleighScatteringColorSaturation;

            settings.MiePhaseG = m_CommonRenderingData.MiePhaseG;
            settings.MieScatteringColor = m_CommonRenderingData.MieScatteringColor;
            settings.MieScatteringColorSaturation = m_CommonRenderingData.CurrentMieScatteringColorSaturation;
            settings.MieAbsorptionColor = m_CommonRenderingData.MieAbsorptionColor;
            settings.MieAbsorptionColorSaturation = m_CommonRenderingData.CurrentMieAbsorptionColorSaturation;

            settings.AbsorptionColor = m_CommonRenderingData.AbsorptionColor;
            settings.AbsorptionColorSaturation = m_CommonRenderingData.AbsorptionColorSaturation;

            settings.MinRecomputeRate = m_ForceUpdate ? 0 : (m_CustomDirectionalLightUpdateRate > 0 ? m_CustomDirectionalLightUpdateRate : Mathf.Max(1.0f, CurrentControllerPreset.DirectionalLightUpdateRate));

            settings.EnableFog = m_FogRenderingData.EnableFog;
            //settings.EnableFog = false;
            settings.FogColorMode = m_FogRenderingData.FogColorMode;

            m_FogRenderingData.WorldSpaceMipFog |= m_AdvanceData.RealTimeReflectionCubemap;
            settings.WorldSpaceMipFog = m_FogRenderingData.WorldSpaceMipFog;
            settings.FogUpdateMode = m_FogRenderingData.FogUpdateMode;
            settings.FogUpdateDeltaPositionThreshold = m_FogRenderingData.FogUpdateDeltaPositionThreshold;
            settings.FogUpdateDeltaTimeThreshold = m_FogRenderingData.FogUpdateDeltaTimeThreshold;
            settings.FogMeanFreePath = m_FogRenderingData.CurrentFogMeanFreePath;
            settings.MaxFogDistance = m_FogRenderingData.MaxFogDistance;
            var currentFogColor = m_FogRenderingData.CurrentFogColor;
            settings.FogColor = new Vector3(currentFogColor.r, currentFogColor.g, currentFogColor.b);
            settings.BaseFogHeight = m_FogRenderingData.BaseFogHeight;
            settings.MaximumFogHeight = m_FogRenderingData.MaximumFogHeight;
            settings.FogExposureMultiplier = m_FogRenderingData.FogExposureMultiplier;
            settings.FogSceneBlendWeight = m_FogRenderingData.FogSceneBlendWeight;
            settings.FogViewOffset = m_FogRenderingData.FogViewOffset;
            settings.MipFogStartDistance = m_FogRenderingData.MipFogStartDistance;
            settings.MipFogFallOffDistance = m_FogRenderingData.MipFogFallOffDistance;
            settings.MipFogMaxClip = m_FogRenderingData.MipFogMaxClip;
            settings.MipFogNear = m_FogRenderingData.MipFogNear;
            settings.MipFogFar = m_FogRenderingData.MipFogFar;

            settings.RenderFogVolume = m_FogRenderingData.RenderFogVolume;
            settings.FogVolumeWidth = m_FogRenderingData.FogVolumeWidth;
            settings.FogVolumeHeight = m_FogRenderingData.FogVolumeHeight;
            settings.FogVolumeDepth = m_FogRenderingData.FogVolumeDepth;
            settings.FogHeight = m_FogRenderingData.FogHeight;
            settings.FogDensity = m_FogRenderingData.FogDensity;
            settings.FogHeightFalloff = m_FogRenderingData.FogHeightFalloff;
            settings.FogStartDistance = m_FogRenderingData.FogStartDistance;
            settings.FogCullOffDistance = m_FogRenderingData.FogCullOffDistance;
            settings.FogDensitySecond = m_FogRenderingData.FogDensitySecond;
            settings.FogHeightFalloffSecond = m_FogRenderingData.FogHeightFalloffSecond;
            settings.FogHeightOffsetSecond = m_FogRenderingData.FogHeightOffsetSecond;
            settings.FogInscatteringColor = m_FogRenderingData.FogInscatteringColor;
            settings.FogMaxOpacity = m_FogRenderingData.FogMaxOpacity;
            settings.EnableFogDirectionalInscattering = m_FogRenderingData.EnableFogDirectionalInscattering;
            settings.FogDirectionalColor = m_FogRenderingData.FogDirectionalColor;
            settings.FogDirectionalStartDistance = m_FogRenderingData.FogDirectionalStartDistance;
            settings.FogDirectionalExponent = m_FogRenderingData.FogDirectionalExponent;
            settings.EnableFogSkyAtmosphereAmbient = m_FogRenderingData.EnableFogDirectionalInscattering;
            settings.FogSkyAtmosphereTintColor = m_FogRenderingData.FogSkyAtmosphereTintColor;
            settings.FogSkyAtmosphereSaturation = m_FogRenderingData.FogSkyAtmosphereSaturation;

            var underWaterFogColor = m_AdvanceData.CurrentUnderwaterFogColor;
            settings.UnderWaterFogColor = new Vector3(underWaterFogColor.r, underWaterFogColor.g, underWaterFogColor.b);
            settings.WaterReflectionIntensity = m_AdvanceData.CurrentOceanSkyReflectionIntensity;
            settings.WaterReflectionRange = m_AdvanceData.OceanSkyReflectionRange;

            settings.DebugAlwaysComputeLUTs = DebugAlwaysComputeLUTs;
            settings.DebugSunColorLUT = DebugSunColorLUT;

            settings.SunHaloFalloff = m_CommonRenderingData.CurrentSunHaloFalloff;
            settings.SunHaloIntensity = m_CommonRenderingData.CurrentSunHaloIntensity * 10f;
        }

        private void UpdateCloudRenderingSettings()
        {
            var settings = m_SkyRenderingFeature.GetCloudSettings();
            var skySettings = m_SkyRenderingFeature.GetSettings();
            skySettings.IsCloudLayerEnabled = false;

            var cloudRenderingData = CurrentControllerPreset.CloudRenderingData;
            settings.RenderMethod = m_AdvanceData.EnableCouds ? cloudRenderingData.RenderMethod : CloudsRenderMethod.None;
            if (settings.RenderMethod == CloudsRenderMethod.None)
                return;

            bool enableCloudBillboards = Clouds != null && CloudBillboardsMesh != null && CloudBillboardsTexture != null && settings.IsCloudBillboardsEnabled();
            if (enableCloudBillboards)
            {
                settings.CloudBillboardsPose = Clouds.transform.localToWorldMatrix;
                settings.SkyScale = cloudRenderingData.CurrentSkyScale;
                settings.SunsetColorMode = cloudRenderingData.SunsetColorMode;
                settings.SunDirection = m_SunBodyRotation * Vector3.forward;
                settings.MoonDirection = m_MoonBodyRotation * Vector3.forward;
                settings.SkyMie = 1.5f; //CloudRenderingData.CurrentSkyMie;
                settings.SkyRayleigh = 1.0f; //CloudRenderingData.CurrentSkyRayleigh;
                settings.SkyScattering = cloudRenderingData.CurrentSkyScattering;
                settings.SkyScatteringColor = cloudRenderingData.CurrentSkyScatteringColor;
                settings.SkyLuminance = cloudRenderingData.CurrentSkyLuminance;
                settings.StaticCloudScattering = cloudRenderingData.CurrentStaticCloudScattering;
                settings.StaticCloudColor = cloudRenderingData.CurrentStaticCloudColor;
                settings.StaticCloudExtinction = cloudRenderingData.CurrentStaticCloudExtinction;
                settings.CloudBillboardsTexture = CloudBillboardsTexture;
                settings.CloudBillboardsMesh = CloudBillboardsMesh;
            }

            if (settings.IsCloudLayerEnabled())
            {
                var cloudLayerSettings = m_SkyRenderingFeature.GetCloudLayerSettings();
                skySettings.IsCloudLayerEnabled = true;
                cloudLayerSettings.alwaysUpdate = cloudRenderingData.ForceUpdate;
                cloudLayerSettings.layers = cloudRenderingData.Layers;
                cloudLayerSettings.layerA = cloudRenderingData.LayerA;
                cloudLayerSettings.layerB = cloudRenderingData.LayerB;
                cloudLayerSettings.opacity = cloudRenderingData.Opacity;
                cloudLayerSettings.cameraDepthScale = cloudRenderingData.DepthScale;
                cloudLayerSettings.useOctMap = cloudRenderingData.UseOctMap;
                cloudLayerSettings.resolution = cloudRenderingData.Resolution;
                cloudLayerSettings.exposureMultipler = cloudRenderingData.ExposureMultipler;
            }
            
            if (!settings.IsVoluemtricCloudsEnabled())
                return;

            settings.LocalClouds = cloudRenderingData.LocalClouds;
            settings.CloudPreset = cloudRenderingData.CloudPreset;
            settings.DensityMultiplier = cloudRenderingData.CurrentDensityMultiplier;
            settings.DensityCurve = cloudRenderingData.DensityCurve;
            settings.ShapeFactor = cloudRenderingData.ShapeFactor;
            settings.ShapeScale = cloudRenderingData.ShapeScale;
            settings.ErosionFactor = cloudRenderingData.ErosionFactor;
            settings.ErosionScale = cloudRenderingData.ErosionScale;
            settings.ErosionCurve = cloudRenderingData.ErosionCurve;
            settings.AmbientOcclusionCurve = cloudRenderingData.AmbientOcclusionCurve;
            settings.MicroErosion = cloudRenderingData.MicroErosion;
            settings.MicroErosionFactor = cloudRenderingData.MicroErosionFactor;
            settings.MicroErosionScale = cloudRenderingData.MicroErosionScale;
            settings.BottomAltitude = cloudRenderingData.BottomAltitude;
            settings.AltitudeRange = cloudRenderingData.AltitudeRange;
            settings.ShapeOffset = cloudRenderingData.ShapeOffset;
            settings.EarthCurvature = cloudRenderingData.EarthCurvature;

            settings.EnableWind = cloudRenderingData.EnableWind;
            settings.GlobalOrientation = cloudRenderingData.GlobalOrientation;
            settings.GlobalSpeed = cloudRenderingData.GlobalSpeed;
            settings.ShapeSpeedMultiplier = cloudRenderingData.ShapeSpeedMultiplier;
            settings.ErosionSpeedMultiplier = cloudRenderingData.ErosionSpeedMultiplier;
            settings.AltitudeDistortion = cloudRenderingData.AltitudeDistortion;
            settings.VerticalShapeWindSpeed = cloudRenderingData.VerticalShapeWindSpeed;
            settings.VerticalErosionWindSpeed = cloudRenderingData.VerticalErosionWindSpeed;

            settings.AmbientProbeSkyLightWeight = cloudRenderingData.AmbientProbeSkyLightWeight;
            settings.AmbientLightProbeDimmer = cloudRenderingData.CurrentAmbientLightProbeDimmer;
            settings.SunLightDimmer = cloudRenderingData.CurrentSunLightDimmer;
            settings.ErosionOcclusion = cloudRenderingData.CurrentErosionOcclusion;
            settings.ScatteringTint = cloudRenderingData.CurrentScatteringTint;
            settings.PowderEffectIntensity = cloudRenderingData.CurrentPowderEffectIntensity;
            settings.MultiScattering = cloudRenderingData.CurrentMultiScattering;
            settings.TransmittanceFadeOut = cloudRenderingData.TransmittanceFadeOut;
            settings.TransmittanceFadeOutStart = cloudRenderingData.TransmittanceFadeOutStart * 1000.0f;
            settings.TransmittanceFadeOutDistance = cloudRenderingData.TransmittanceFadeOutDistance * 1000.0f;
            settings.ForwardEccentricity = cloudRenderingData.ForwardEccentricity;
            settings.BackwardEccentricity = cloudRenderingData.BackwardEccentricity;
#if false
            settings.Shadows = cloudRenderingData.Shadows;
            settings.ShadowResolution = cloudRenderingData.ShadowResolution;
            settings.ShadowDistance = cloudRenderingData.ShadowDistance;
            settings.ShadowOpacity = cloudRenderingData.ShadowOpacity;
            settings.ShadowOpacityFallback = cloudRenderingData.ShadowOpacityFallback;
#endif

            settings.TemporalAccumulationFactor = cloudRenderingData.TemporalAccumulationFactor;
            settings.NumPrimarySteps = cloudRenderingData.NumPrimarySteps;
            settings.NumLightSteps = cloudRenderingData.NumLightSteps;
            settings.FadeInMode = cloudRenderingData.FadeInMode;
            settings.FadeInStart = cloudRenderingData.FadeInStart * 1000.0f;
            settings.FadeInDistance = cloudRenderingData.FadeInDistance * 1000.0f;
            settings.FadeOut = cloudRenderingData.FadeOut;
            settings.FadeOutStart = cloudRenderingData.FadeOutStart * 1000.0f;
            settings.FadeOutDistance = cloudRenderingData.FadeOutDistance * 1000.0f;

            settings.RenderingDebugger = cloudRenderingData.RenderingDebugger;
            settings.ReflectionProbe = cloudRenderingData.ReflectionProbe;
            settings.ResolutionScale = cloudRenderingData.ResolutionScale;
            settings.UpscaleMode = cloudRenderingData.UpscaleMode;
            settings.PreferredRenderMode = cloudRenderingData.PreferredRenderMode;
            settings.AmbientProbe = cloudRenderingData.AmbientProbe;
            settings.SunAttenuation = cloudRenderingData.SunAttenuation;
            settings.ResetOnStart = cloudRenderingData.ResetOnStart;
            settings.OutputDepth = cloudRenderingData.OutputDepth;

            settings.MultiFrameUpdate = cloudRenderingData.MultiFrameUpdate;
            settings.SkyDomeTextureResolution = cloudRenderingData.SkyDomeTextureResolution;
            settings.MultiFrameCount = cloudRenderingData.MultiFrameCount;
            settings.StaticCloudsUpdateCycles = cloudRenderingData.StaticCloudsUpdateCycles;
            settings.DebugMultiFrameUpdate = cloudRenderingData.DebugMultiFrameUpdate;
        }

        private void OnDisable()
        {
            ActiveAzureSky();
            RemoveFromActiveControllers();
        }

        private void OnEnable()
        {
            var prevInstance = Instance;
            Instance = this;
            UpdateAzureSkySystemObjects();
            DeactiveAzureSky();

            m_NeedRecomputeLuts = prevInstance != this;

            //Disable Previous Instance 
            if (prevInstance != null && prevInstance != this)
                prevInstance.ActiveAzureSky();

            AddToActiveControllers();
        }

        private void OnDestroy()
        {
            ActiveAzureSky();
            RemoveFromActiveControllers();
        }

        private void AddToActiveControllers()
        {
            var scene = gameObject.scene;
            s_ActiveControllers.Remove(this);
            s_ActiveControllers.AddLast(this);

        }

        private void RemoveFromActiveControllers()
        {
            s_ActiveControllers.Remove(this);

            if (Instance == this && s_ActiveControllers.Count > 0)
            {
                Instance = s_ActiveControllers.Last.Value;
                Instance.NeedRecomputeLuts = true;
                Instance.DeactiveAzureSky();
            }
        }

#if UNITY_EDITOR
        public void UpdateSceneViewSettings()
        {
            SkyRenderingFeature.SceneViewSkySettings = m_SkyRenderingFeature.GetSettings();
            SkyRenderingFeature.SceneViewCloudSettings = m_SkyRenderingFeature.GetCloudSettings();
            SkyRenderingFeature.SceneViewCloudLayerSettings = m_SkyRenderingFeature.GetCloudLayerSettings();
        }

        public void AddControllerPreset()
        {
            if (ControllerPresetsParent)
            {
                var controllerPreset = AddNewGameObjectWithComponentTo<AzureCustomSkyRenderControllerPreset>(ControllerPresetsParent, "New Controller Preset");

                int index = Mathf.Max(0, ControllerPresets.Count - 1);
                ControllerPresets[index] = controllerPreset;


                Undo.RegisterCreatedObjectUndo(controllerPreset.gameObject, "Create Global Weather");
                EditorGUIUtility.PingObject(controllerPreset.gameObject);
            }
        }

        public void RemoveControllerPreset(int index)
        {
            if (ControllerPresetsParent)
            {
                Undo.DestroyObjectImmediate(ControllerPresets[index].gameObject);
            }
        }

        public void ReorderControllerPreset(int oldIndex, int newIndex)
        {
            if (ControllerPresetsParent)
            {
                var weatherPreset = ControllerPresets[oldIndex];
                for (int i = 0; i < ControllerPresetsParent.childCount; i++)
                {
                    Transform t = ControllerPresetsParent.GetChild(i);
                    var weather = t.GetComponent<AzureCustomSkyRenderControllerPreset>();
                    if (weather == weatherPreset)
                    {
                        Undo.RegisterCompleteObjectUndo(ControllerPresetsParent, "Reorder Controller Preset List");
                        t.SetSiblingIndex(newIndex);
                        return;
                    }
                }
            }
        }


        /// <summary>
        /// Editor Only: Instantiate a new weather preset when the user creates a new global weather.
        /// </summary>
        public void AddGlobalWeather()
        {
            if (WeatherPresetsParent)
            {
                
                var weatherPresetComponent = AddNewGameObjectWithComponentTo<AzureCustomSkyRenderingPreset>(WeatherPresetsParent, "NewWeather");

                // Add the weather preset to the global weather list
                int index = Mathf.Max(0, SkyRenderingPresets.Count - 1);
                SkyRenderingPresets[index].Preset = weatherPresetComponent;
                SkyRenderingPresets[index].TransitionDuration = 10f;


                Undo.RegisterCreatedObjectUndo(weatherPresetComponent.gameObject, "Create Global Weather");
                EditorGUIUtility.PingObject(weatherPresetComponent.gameObject);
            }
        }

        public void RemoveGlobalWeather(int index)
        {
            if (WeatherPresetsParent)
            {
                Undo.DestroyObjectImmediate(this.SkyRenderingPresets[index].Preset.gameObject);
            }
        }

        public void ReorderGlobalWeather(int oldIndex, int newIndex)
        {
            if (WeatherPresetsParent)
            {
                var weatherPreset = this.SkyRenderingPresets[oldIndex].Preset;
                for (int i = 0; i < WeatherPresetsParent.childCount; i++)
                {
                    Transform t = WeatherPresetsParent.GetChild(i);
                    var weather = t.GetComponent<AzureCustomSkyRenderingPreset>();
                    if (weather == weatherPreset)
                    {
                        Undo.RegisterCompleteObjectUndo(WeatherPresetsParent, "Reorder Global Weather List");
                        t.SetSiblingIndex(newIndex);
                        return;
                    }
                }
            }
        }

        private static T AddNewGameObjectWithComponentTo<T>(Transform parent, string name) where T : MonoBehaviour
        {
            // Create a new game object
            GameObject newObj = new GameObject();

            // Set an unique name to the new game object
            Transform t;
            int count = 1;

            var newName = name;
            for (int i = 0; i < parent.childCount; i++)
            {
                t = parent.GetChild(i).transform;

                if (t.name == newName)
                {
                    newName = name + " (" + count + ")";
                    count++;
                }
            }

            newObj.name = newName;
            newObj.transform.SetParent(parent);
            var component = newObj.AddComponent<T>();
            return component;
        }
#endif

    }
}