﻿using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using WizardGames.Soc.Common.Const;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Data.constraction;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Setting;
using WizardGames.Soc.SocClient.Ui;

namespace WizardGames.Soc.Common.Component
{
    public partial class PlayerConstructionComponent
    {
        public override void PostInit()
        {
            base.PostInit();

            Mc.Msg.FireMsg(EventDefine.RespawnRebornPointRefresh);

            SleepingBagInfo.SubscribeDictUpdate((_, _, _) =>
            {
                Mc.Msg.FireMsg(EventDefine.UpdateBedInfo);
            });

            SleepingBagInfo.SubscribeAnyUpdateCallback(() =>
            {
                Mc.Msg.FireMsg(EventDefine.RespawnRebornPointRefresh);
            });
            BriefBPInfo.SubscribeDictUpdate((opType, roleId, inviteInfo) =>
            {
                Mc.Msg.FireMsg(EventDefine.OnMyPermChanged);
            });
            Mc.Marker.UpdateDeadSheepTerritoryMarkerDatas();

            //logger.Error($"init set {OpenDoorAutoDirEnable}");
            var setting = Mc.SettingClient?.GetConfig("btnOpenDoorAutoDir");
            setting?.DirectSetCurValue(OpenDoorAutoDirEnable.ToString());

#if !PUBLISH
            CheckFreeConstruct();
#endif
        }
#if !PUBLISH
        public void CheckFreeConstruct()
        {
            var debugComp = Player.DebugComp;
            if (debugComp == null)
                return;
            // 读取设置
            var isFreeContructSettingConfig = Mc.SettingClient.GetConfig("btnFreeContruct");
            bool isFreeConstruct = false;
            if (isFreeContructSettingConfig != null)
            {
                if (bool.TryParse(isFreeContructSettingConfig.CurValue, out var result))
                {
                    isFreeConstruct = result;
                }
                else
                {
                    var settingSwitchConfig = isFreeContructSettingConfig as SettingSwitchConfig;
                    if (settingSwitchConfig != null && bool.TryParse(settingSwitchConfig.defaultValue, out var defaultValue))
                    {
                        isFreeConstruct = defaultValue;
                    }
                }
            }
            if (debugComp.NoConsumptionMode != isFreeConstruct)
            {
                debugComp.RemoteCallTestSetNoConsumptionMode(ERpcTarget.World, isFreeConstruct);
            }
        }
#endif

        public SleepingBagInfo GetSleepingBagInfo(long entityId)
        {
            if (SleepingBagInfo.TryGetValue(entityId, out var info))
            {
                return info;
            }
            return null;
        }

        public bool IsSleepingBagUnlocked(long entityId)
        {
            if (SleepingBagInfo.TryGetValue(entityId, out var info))
            {
                return info.UnlockTime < TimeStampUtil.GetNowTimeStampMSec();
            }
            return false;
        }

        #region 电力
        [RpcHandler]
        public void ReceiveElectricalData(long partId, List<int> powerNow)
            => Mc.Msg.FireMsg(EventDefine.ReceiveElectricalData, partId, powerNow);

        [RpcHandler]
        public void ChristmasLightsConstructed(long entityId)
        {
            Mc.Msg.FireMsgAtOnce(EventDefine.PowerLightDeploySuccess, entityId);
        }

        /// <summary>
        ///  batteryData 包含四个值 1. chargeLeft 供电时长, 2. chargeTime 充电时间, 3. usage 使用量, 4. power 电池功率
        /// </summary>
        /// <param name="partId"></param>
        /// <param name="batteryData"></param>
        [RpcHandler]
        public void ReceiveBatteryAddtionalData(long partId, List<int> batteryData)
        {
            if (batteryData == null || batteryData.Count < 4)
            {
                logger.Error("ReceiveBatteryAddtionalData batteryData is null or count less than 4");
                return;
            }

            // int chargeLeft = batteryData[0];
            // int chargeTime = batteryData[1];
            // int usage = batteryData[2];
            // int power = batteryData[3];
            //Debug.Log($"蓄电池chargeLeft:{chargeLeft}||||chargeTime:{chargeTime}||||usage:{usage}||||power:{power}||||");

            Mc.Msg.FireMsg(EventDefine.WireToolBuildModeRefreshBatteryInfo, partId, batteryData);
        }

        #endregion
        /// <summary>
        /// 0：第一次打开，1：非第一次
        /// </summary>
        /// <param name="firstModeType"></param>
        /// <returns></returns>
        public int GetModeFirstOpenState(EBuildFirstModeType firstModeType)
        {
            if (ConstrctionFirstOpenDic == null)
            {
                ConstrctionFirstOpenDic = new BasicValueDictionary<int, int>();
            }

            if (firstModeType != EBuildFirstModeType.None)
            {
                if (ConstrctionFirstOpenDic.ContainsKey((int)firstModeType))
                {
                    int state = ConstrctionFirstOpenDic[(int)firstModeType];
                    return state;
                }
            }

            return 0;
        }

        #region 通用建造
        /// <summary>
        /// 查询野地建造数量
        /// </summary>
        /// <param name="partType"></param>
        /// <returns></returns>
        private int GetWildPartTypeCount(long partType)
        {
            var partConfig = McCommon.Tables.TbBuildingCore.GetOrDefault(partType);
            if (partConfig.EntityGroupId > 0)
            {
                WildPartLimitInfo.PartGroupCount.TryGetValue(partConfig.EntityGroupId, out var count);
                return count;
            }
            else
            {
                WildPartLimitInfo.PartTypeCount.TryGetValue(partType, out var count);
                return count;
            }
        }

        private int GetPersonalPartTypeCount(BuildingCore partConfig)
        {
            if (partConfig.EntityGroupId > 0)
            {
                PartLimitInfo.PartGroupCount.TryGetValue(partConfig.EntityGroupId, out var count);
                return count;
            }
            else
            {
                PartLimitInfo.PartTypeCount.TryGetValue(partConfig.PartId, out var count);
                return count;
            }
        }

        /// <summary>
        /// 查询建筑数量，自动检查当前位置的领地
        /// </summary>
        /// <param name="partType"></param>
        /// <returns></returns>
        public int GetPartTypeCount(long partType, Vector3 pos)
        {
            if (partType == 0)
            {
                return 0;
            }
            int globalCountLimit = ConstructionUtils.GetPersonalPartTypeLimit(partType);
            var partConfig = McCommon.Tables.TbBuildingCore.GetOrDefault(partType);
            if (partConfig != null && globalCountLimit > 0)
            {
                return GetPersonalPartTypeCount(partConfig);
            }
            var terrEnt = TerritoryManagerEntity.Instance.GetInChargeTerritoryEntity(pos);
            if (terrEnt == null)
            {
                return GetWildPartTypeCount(partType);
            }
            else
            {
                return terrEnt.BaseComp.GetPartTypeCount(partType);
            }
        }

        public int GetPartTypeLimitCount(long partType, Vector3 pos)
        {
            if (partType == 0)
            {
                return 0;
            }
            int globalCountLimit = ConstructionUtils.GetPersonalPartTypeLimit(partType);

            if (globalCountLimit > 0)
            {
                return globalCountLimit;
            }
            var terrEnt = TerritoryManagerEntity.Instance.GetInChargeTerritoryEntity(pos);
            if (terrEnt == null)
            {
                return ConstructionUtils.GetWildPartTypeLimit(partType);
            }
            else
            {
                return ConstructionUtils.GetTerrPartTypeLimit(partType);
            }
        }

        /// <summary>
        /// 检查建筑数量上限，自动检查当前位置的领地
        /// </summary>
        /// <param name="partType"></param>
        /// <param name="addNum"></param>
        /// <returns></returns>
        public int CheckPartTypeLimit(long partType, int addNum, Vector3 pos)
        {
            var terrEnt = TerritoryManagerEntity.Instance.GetInChargeTerritoryEntity(pos);
            return CheckPartTypeLimit(partType, addNum, terrEnt);
        }


        /// <summary>
        /// 检查建筑数量上限
        /// </summary>
        /// <param name="partType"></param>
        /// <param name="addNum"></param>
        /// <param name="terrEnt"></param>
        /// <returns></returns>
        public int CheckPartTypeLimit(long partType, int addNum, TerritoryEntity terrEnt)
        {
            if (!ConstructionUtils.CheckPersonalCount(PartLimitInfo, partType, addNum))
            {
                return CountLimitType.PERSONAL_COUNT_LIMIT;
            }
            if (terrEnt == null)
            {
                // 野外数量检查
                if (!ConstructionUtils.CheckWildPartTypeLimit(WildPartLimitInfo, partType, addNum))
                {
                    return CountLimitType.WILD_PART_TYPE_LIMIT;
                }
            }
            else
            {
                // 领地数量检查
                if (!ConstructionUtils.CheckTerrPartTypeLimit(terrEnt.BaseComp.PartLimitInfo, partType, addNum))
                {
                    return CountLimitType.TERRITORY_PART_TYPE_LIMIT;
                }
            }


            return CountLimitType.NO_LIMIT;
        }

        [RpcHandler]
        public void ConstructionPartCountLimit(int limitType, long partType)
        {
            logger.InfoFormat("ConstructionPartCountLimit partType {0} limitType {1}", partType, limitType);
            var partConfig = Mc.Tables.TbBuildingCore.GetOrDefault(partType);
            if (partType != 0 && partConfig == null)
            {
                logger.ErrorFormat("ConstructionPartCountLimit partType {0} not found, limitType {1}", partType, limitType);
                return;
            }

            switch (limitType)
            {
                case CountLimitType.WILD_PART_TYPE_LIMIT:
                    {
                        if (partConfig.EntityGroupId > 0)
                        {
                            var groupConfig = McCommon.Tables.TbPartGroup.GetOrDefault(partConfig.EntityGroupId);
                            string[] str = { groupConfig.GroupEntityName, groupConfig.MaxGroupEntityWild.ToString() };
                            Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.PartGroupLimitAtWild, str);
                        }
                        else
                        {
                            string[] str = { partConfig.Text, partConfig.MaxBuildWild.ToString() };
                            Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.PartTypeLimitAtWild, str);
                        }
                    }
                    break;
                case CountLimitType.TERRITORY_PART_TYPE_LIMIT:
                    {
                        if (partConfig.EntityGroupId > 0)
                        {
                            var groupConfig = McCommon.Tables.TbPartGroup.GetOrDefault(partConfig.EntityGroupId);
                            string[] str = { groupConfig.GroupEntityName, groupConfig.MaxGroupEntityTerr.ToString() };
                            Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.PartGroupLimitInTerr, str);
                        }
                        else
                        {
                            string[] str = { partConfig.Text, partConfig.CountLimit.ToString() };
                            Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.PartTypeLimitInTerr, str);
                        }
                    }
                    break;
                case CountLimitType.TERRITORY_TOTAL_COUNT_LIMIT:
                    Mc.MsgTips.ShowRealtimeWeakTip(1099);
                    break;
                case CountLimitType.GRID_TOTAL_COUNT_LIMIT:
                    {
                        string[] str = { McCommon.Tables.TbConstructionConstantConfig.Sqm150BuildLimit.ToString() };
                        Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.AoiGridPartCountLimit, str);
                    }
                    break;
                case CountLimitType.PERSONAL_COUNT_LIMIT:
                    {
                        if (partConfig.EntityGroupId > 0)
                        {
                            var groupConfig = McCommon.Tables.TbPartGroup.GetOrDefault(partConfig.EntityGroupId);
                            string[] str = { groupConfig.GroupEntityName, groupConfig.OwnCountLimit.ToString() };
                            Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.PartGlobalLimit, str);
                        }
                        else
                        {
                            string[] str = { partConfig.Text, partConfig.OwnCountLimit.ToString() };
                            Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.PartGlobalLimit, str);
                        }
                    }
                    break;
            }
        }

        [RpcHandler]
        public void OnCreateComboPartOver(int result, long markId)
        {
            Mc.Construction.CreatComboPartOver(result, markId);
        }

        #endregion

        [RpcHandler]
        public void DeployPartPlayAnimationAndEffect(long partEntityId, bool isChangeSkin)
        {
            var partEntity = Mc.Entity.GetPartEntity(partEntityId);
            if (partEntity == null)
            {
                logger.WarnFormat("DeployPartPlayAnimationAndEffect partEntityId {0} not found", partEntityId);
                return;
            }

            if (partEntity.SkinAnimateComponent != null)
            {
                partEntity.SkinAnimateComponent.MarkAnimationFlag(isChangeSkin);
            }
        }


        #region 领地中枢管理
        /// <summary>
        /// 通知操作结果
        /// </summary>
        /// <param name="entityId"></param>
        /// <param name="op"></param>
        /// <param name="opCode"></param
        [RpcHandler]
        public void NotifyBuildingPrivilegeOperation(long entityId, int op, int opCode)
        {
            //success
            if (opCode == 0)
            {
                return;
            }

            if (opCode == (int)EBuildingPrivilegeOpCode.MinganciTestFail)
            {
                logger.InfoFormat("领地柜权限操作失败：{0}", ((EBuildingPrivilegeOperation)op).ToString());
                Mc.MsgTips.ShowRealtimeWeakTip(23179);
                //让目标界面刷新一次
                Mc.Msg.FireMsg(EventDefine.OnPermHubRenamed, entityId);
                return;
            }

            logger.ErrorFormat("领地柜权限操作失败：{0} : {1}",
                    ((EBuildingPrivilegeOperation)op).ToString(),
                    ((EBuildingPrivilegeOpCode)opCode).ToString());

        }

        /// <summary>
        /// 新增了一个领地柜标记
        /// </summary>
        /// <param name="territoryCenterId"></param>
        [RpcHandler]
        public void OnAddTerritoryInfo(long territoryCenterId)
        {
            Mc.Marker.OnTerritoryCenterAdd(territoryCenterId);
        }

        /// <summary>
        /// 移除了一个领地柜标记
        /// </summary>
        /// <param name="territoryCenterId"></param>
        [RpcHandler]
        public void OnRemoveTerritoryInfo(long territoryCenterId)
        {
            Mc.Marker.OnTerritoryCenterRemove(territoryCenterId);
        }

        /// <summary>
        /// 领地中枢放入材料提示
        /// </summary>
        /// <param name="result"></param>
        /// <param name="fillMaxLimitList"></param>
        [RpcHandler]
        public void OnPutInUpkeepMaterialsAck(int result, List<long> fillMaxLimitList)
        {
            if (result == 0)
            {
                Mc.Audio.Add2DAudioByConfig(Mc.Tables.TbUiAudio.PutItemSuccess);
                Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.PutInSuccess);
            }

            if (fillMaxLimitList != null && fillMaxLimitList.Count > 0)
            {

                StringBuilder sb = Pool.Get<StringBuilder>();
                sb.Clear();
                for (var i = 0; i < fillMaxLimitList.Count; i++)
                {
                    var itemId = fillMaxLimitList[i];
                    var partConfig = Mc.Tables.TbItemConfig.GetOrDefault(itemId);
                    if (partConfig != null)
                    {
                        sb.Append(partConfig.Name);
                        if (i < fillMaxLimitList.Count - 1)
                        {
                            sb.Append(",");
                        }
                    }
                }
                Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.ReachPutMaxLimit, sb.ToString());
            }



        }

        #endregion

        /// <summary>
        /// 获取一键升级数据
        /// </summary>
        /// <param name="partEntityId"></param>
        /// <param name="dataVersion"></param>
        /// <param name="nonComboPartCount">非组合建筑数据</param>
        /// <param name="comboPartInfo">组合建筑数据</param>
        /// <param name="storageBoxIngredients">升级所需材料--储物箱</param>
        /// <param name="territoryCabinetIngredients">升级所需材料--领地柜</param>
        [RpcHandler]
        public void OnRequestUpgradeData(long partEntityId, int dataVersion, Dictionary<long, GradeCountInfo> nonComboPartCount, List<ComboPartInfo> comboPartInfo, Dictionary<long, int> storageBoxIngredients, Dictionary<long, int> territoryCabinetIngredients)
        {
            var partEntity = EntityManager.Instance.GetEntity(partEntityId) as PartEntity;
            if (partEntity == null)
            {
                logger.InfoFormat("OnRequestUpgradeData {0}", partEntityId);
                return;
            }
            var comp = partEntity.GetComponent<TerritoryBatchUpgradeComponent>((int)EComponentIdEnum.TerritroyBatchUpgrade);
            comp?.OnRequestUpgradeData(dataVersion, nonComboPartCount, comboPartInfo, storageBoxIngredients, territoryCabinetIngredients);
        }

        /// <summary>
        /// 一键升级数据版本变化
        /// </summary>
        /// <param name="partEntityId"></param>
        /// <param name="dataVersion"></param>
        [RpcHandler]
        public void OnUpgradeDataVersionChanged(long partEntityId, int dataVersion)
        {
            var partEntity = EntityManager.Instance.GetEntity(partEntityId) as PartEntity;
            if (partEntity == null)
            {
                logger.InfoFormat("OnUpgradeDataVersionChanged {0}", partEntityId);
                return;
            }
            var comp = partEntity.GetComponent<TerritoryBatchUpgradeComponent>((int)EComponentIdEnum.TerritroyBatchUpgrade);
            comp?.OnUpgradeDataVersionChanged(dataVersion);
        }

        /// <summary>
        /// 一键升级操作返回
        /// </summary>
        /// <param name="result"></param>
        /// <param name="needSec"></param>
        [RpcHandler]
        public void OnRequestUpgradeBatch(long partEntityId, int result, int needSec, List<long> args)
        {
            var partEntity = EntityManager.Instance.GetEntity(partEntityId) as PartEntity;
            if (partEntity == null)
            {
                logger.InfoFormat("OnRequestUpgradeBatch {0}", partEntityId);
                return;
            }
            var comp = partEntity.GetComponent<TerritoryBatchUpgradeComponent>((int)EComponentIdEnum.TerritroyBatchUpgrade);
            comp?.OnRequestUpgradeBatch(result, needSec, args);
        }

        /// <summary>
        /// 一键升级结束
        /// </summary>
        /// <param name="entityId"></param>
        /// <param name="interrupt">是否中断</param>
        /// <param name="backItemByMail">中断是否通过邮件归还了材料</param>
        [RpcHandler]
        public void OnBatchUpgradeFinish(long entityId, bool interrupt, bool backItemByMail)
        {
            logger.InfoFormat("OnBatchUpgradeFinish {0} {1} ", entityId, interrupt);
            if (interrupt)
            {
                Mc.MsgTips.ShowRealtimeWeakTip(1081);
            }
            if (backItemByMail)
            {
                Mc.MsgTips.ShowRealtimeWeakTip(24280, "");
            }
            Mc.Msg.FireMsg(EventDefine.OnBatchUpgradeFinish, entityId, interrupt);
        }

        [RpcHandler]
        public void ReceiveNotice(PersonalNotice notice)
        {
            switch (notice.NoticeType)
            {
                case EPersonalNoticeType.TerritoryCaptureSucNotice:
                    {
                        var captureSucNotice = notice as TerritoryCaptureSucNotice;
                        // 占领成功
                        var mapGridName = Mc.Map.GetGridName(captureSucNotice.Pos.CV3ToUV3());
                        Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.TerrCenterOccupiedFinished, mapGridName);
                    }
                    break;
                case EPersonalNoticeType.TerritoryCapturedNotice:
                    {
                        var capturedNotice = notice as TerritoryCapturedNotice;
                        // 被占领完成
                        var mapGridName = Mc.Map.GetGridName(capturedNotice.Pos.CV3ToUV3());
                        Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.TerrCenterBeOccupied, mapGridName);
                    }
                    break;
                case EPersonalNoticeType.TerritoryMemberRemovedNotice:
                    {
                        var memberRemoved = notice as TerritoryMemberRemovedNotice;
                        var mapGridName = Mc.Map.GetGridName(memberRemoved.Pos.CV3ToUV3());
                        Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.PermPassivelyRemoveCompletely, mapGridName);
                    }
                    break;
                case EPersonalNoticeType.TerritoryAdminChangeNotice:
                    {
                        var adminChanged = notice as TerritoryAdminChangeNotice;
                        logger.InfoFormat("TerritoryCenterAdminChangeNotice {0} {1}", adminChanged.Pos, adminChanged.IsAdmin);
                    }
                    break;
                case EPersonalNoticeType.TerritoryGroupChangeNotice:
                    {
                        var groupChanged = notice as TerritoryGroupChangeNotice;

                        var mapGridName = Mc.Map.GetGridName(groupChanged.Pos.CV3ToUV3());

                        var tmpList = Pool.GetColl<List<int>>();
                        tmpList.Clear();
                        foreach (var groupId in groupChanged.ChangeGroupIds)
                        {
                            tmpList.Add(groupId);
                        }
                        var permNames = Mc.PermCenter.CollectPermGroupName(tmpList);
                        //有新增权限
                        if (groupChanged.AddGroup)
                        {
                            Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.PermPassivelyAdd, mapGridName, permNames);
                        }
                        //有移除权限
                        else
                        {
                            Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.PermPassivelyRemove, mapGridName, permNames);
                        }

                        Pool.ReleaseColl(tmpList);
                    }
                    break;
                case EPersonalNoticeType.LoseTeritoryOwnership:
                    Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.LostTerritoryAlert);
                    break;
                case EPersonalNoticeType.BecomeTeritoryOwner:
                    Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.GetTerritoryHint);
                    break;
                case EPersonalNoticeType.GetMemberTerritory:
                    {
                        Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.TeamMemberTerritoryTransfer);
                    }
                    break;
                default:
                    logger.WarnFormat("Unknown notice type: {0}", notice.NoticeType);
                    break;
            }
        }
        /// <summary>
        /// 有新的通知收到了
        /// </summary>
        [RpcHandler]
        public void ReceiveOfflieNotice()
        {
            if (Notices == null)
                return;
            foreach (var notice in Notices)
            {
                ReceiveNotice(notice);
            }
        }
        [RpcHandler]
        public void OnRenameSleepingBagCallback(int code, long partEntityId)
        {
            var UiInputBox = Mc.Ui.GetWindowT<UiInputBox>("UiInputBox");

            switch ((ErrorCode)code)
            {
                case ErrorCode.Success:
                    UiInputBox.CloseWindow();
                    Mc.MsgTips.ShowRealtimeWeakTip(2112);
                    break;
                case ErrorCode.MinganciTestFail:
                    UiInputBox?.ClearSearchData();
                    Mc.MsgTips.ShowRealtimeWeakTip(6003);
                    //PopMessage(1, 11050, null); // 重命名敏感词未通过
                    break;
            }
        }


        [RpcHandler]
        public void OnRenameSleepingBagGroupFailed(int code) { }


        /// <summary>
        /// 睡袋给予成功
        /// </summary>
        /// <param name="sourceName"></param>
        /// <param name="entityId"></param>
        [RpcHandler]
        public void GetPartOwnershipSuccess(string sourceName, long entityId)
        {
            if (string.IsNullOrEmpty(sourceName))
            {
                Mc.MsgTips.ShowRealtimeWeakTip(23009);//LanguageManager.GetTextConst(LanguageConst.BedSuccessGive));
            }
            else
            {
                Mc.MsgTips.ShowRealtimeWeakTip(22075, sourceName);
                SleepingBagComponent.TryShowNewGet(entityId, Time.time + 10f);
            }
        }

        #region PlantBox
        /// <summary>
        /// 补充水分回调
        /// </summary>
        [RpcHandler]
        public virtual void OnAddWater(int value, long plantBoxEntityId, int result)
        {
            // TODO: @sj value 代表增加的值
            if (result == (int)EOpCode.Success)
            {
                if (value > 0)
                {
                    Mc.Msg.FireMsg(EventDefine.WaterSuccess, plantBoxEntityId, value);
                    //浇水成功。
                    Mc.MsgTips.ShowRealtimeWeakTip(23004);
                    // Mc.MyPlayer.RefreshWaterBucketState();
                }
            }

            if (result == (int)EOpCode.ContainerAlreadyOccupied)
            {
                //种植箱被其他玩家使用中
                Mc.MsgTips.ShowRealtimeWeakTip(23005);
            }
        }

        /// <summary>
        /// 补充肥料回调
        /// </summary>
        [RpcHandler]
        public virtual void OnAddManure(BasicTypeList<int> indexes, long plantBoxEntityId, int result)
        {
            // TODO: @sj result 从opcode中判断
            if (indexes != null && indexes.Count > 0)
            {
                Mc.Msg.FireMsg(EventDefine.ManureSuccess, plantBoxEntityId);
                //施肥成功。
                Mc.MsgTips.ShowRealtimeWeakTip(23006);
            }
        }

        /// <summary>
        /// 种植回调
        /// </summary>
        [RpcHandler]
        public virtual void OnPlant(List<Alpha3PlantArgs> successList, long plantBoxEntityId, int result)
        {
            // TODO: @sj result 从opcode中判断
            if (result == (int)EOpCode.Success && successList.Count > 0)
            {
                Mc.Msg.FireMsg(EventDefine.PlantSuccess, plantBoxEntityId);
                //播种成功。
                Mc.MsgTips.ShowRealtimeWeakTip(23007);
            }
        }

        /// <summary>
        /// 收获回调
        /// </summary>
        [RpcHandler]
        public virtual void OnHarvest(int index, long plantBoxEntityId, int result)
        {
            // TODO: @sj index -1 代表全部，否则代表槽位
            // TODO: @sj result 从opcode中判断
            if (result == (int)EOpCode.Success)
            {
                Mc.Msg.FireMsgAtOnce(EventDefine.HarvestSuccess, plantBoxEntityId, index);
                //收割成功。
                Mc.MsgTips.ShowRealtimeWeakTip(23008);
            }
        }

        /// <summary>
        /// 移除回调
        /// </summary>
        [RpcHandler]
        public virtual void OnRemove(int index, long plantBoxEntityId, int result)
        {
            // TODO: @sj index
            // TODO: @sj result 从opcode中判断
            if (result == (int)EOpCode.Success)
            {
                Mc.Msg.FireMsg(EventDefine.RemovePlantSuccess, plantBoxEntityId, index);
            }
        }

        /// <summary>
        /// 杂交回调开始
        /// index 杂交植物
        /// result 结果
        /// magicNum 随机数0, 10000
        /// </summary>
        [RpcHandler]
        public virtual void OnManualHybridizeStart(long index, long plantBoxEntityId, int result, int magicNum = 0)
        {
            if (result == 0)
            {
                Mc.Plant.MagicNum = magicNum;
                logger.InfoFormat($"杂交OnManualHybridizeStart:magicNum:{magicNum}");
            }
            else
            {
                Mc.Plant.MagicNum = -1;
                logger.InfoFormat($"杂交OnManualHybridizeStart:failed： result：{result}");
            }
        }

        /// <summary>
        /// 杂交结束回调
        /// index 杂交植物
        /// result 结果
        /// </summary>
        [RpcHandler]
        public virtual void OnManualHybridizeFinish(long index, long plantBoxEntityId, int result)
        {
            if (result == (int)EOpCode.Success)
            {
                Mc.Msg.FireMsg(EventDefine.OnHybridizeFinish, index);
                logger.InfoFormat($"杂交OnManualHybridizeFinish: success index:{index}");
            }
            else
            {
                logger.InfoFormat($"杂交OnManualHybridizeFinish: failed： result：{result}:index:{index} ");
            }
        }

        /// <summary>
        /// 清理杂交记录回调
        /// result 结果
        /// </summary>
        [RpcHandler]
        public virtual void OnManualHybridizeClear(long plantBoxEntityId, int result)
        {
            if (result == 0)
            {
                Mc.Plant.MagicNum = -1;
                logger.InfoFormat($"杂交OnManualHybridizeClear: success");
            }
            else
            {
                logger.InfoFormat($"杂交OnManualHybridizeClear: failed： result：{result} ");
            }
        }

        /// <summary>
        /// 情报柜情报提交回调
        /// result 结果
        /// </summary>
        [RpcHandler]
        public virtual void CommitResponse(int result)
        {
            if (result == (int)EOpCode.ReputationCabinetStorageNotEnough)
            {
                Mc.MsgTips.ShowRealtimeWeakTip(11129);
            }
        }
        #endregion

        /// <summary>
        /// 返回一键修复的材料销毁
        /// </summary>
        /// <param name="terrId"></param>
        /// <param name="baseCost">基础结构+血量恢复消耗</param>
        /// <param name="deployCost">创建摆件的消耗</param>
        [RpcHandler]
        public void OnReqTerritoryRecoverCost(long terrId, Dictionary<long, int> baseCost, Dictionary<long, GradeCountInfo> tGrade, Dictionary<long, GradeCountInfo> cGrade, Dictionary<long, int> storageBoxIngredients, Dictionary<long, int> territoryCabinetIngredients)
        {
            logger.InfoFormat("OnReqTerritoryRecoverCost {0} {1} {2} {3}", terrId, baseCost?.Count ?? 0, tGrade?.Count ?? 0, cGrade?.Count ?? 0);

            if ((UiTerritoryEdit.GetCurLootingTerritory()?.EntityId ?? 0) != terrId) { return; }
            UiSubBatchRecover.ActiveInstance?.RecvRecoverCost(baseCost, tGrade, cGrade, storageBoxIngredients, territoryCabinetIngredients);
            // 参数太多调不了
            // Mc.Msg.FireMsg(EventDefine.BatchRecoverCostData, baseCost, tGrade, cGrade, storageBoxIngredients, territoryCabinetIngredients);
        }

        /// <summary>
        /// 修复请求返回，返回成功不表示修复完成；只表示本次请求成功了，后续会执行修复逻辑
        /// </summary>
        /// <param name="result"></param>
        [RpcHandler]
        public void OnReqBatchRecover(RecoverPartResult result)
        {
            var terrId = result.TerrId;
            RecoverPartCode code = (RecoverPartCode)result.Code;
            int tipId = -1;
            switch (code)
            {
                case RecoverPartCode.NoPermission:
                    tipId = 23118;
                    break;
                case RecoverPartCode.TerritoryNotFound:
                    tipId = 23119;
                    break;
                case RecoverPartCode.TerritoryInAttacking:
                    {
                        var remainSecond = result.Param[0]; // 需要的冷却时间
                        Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.TerritoryInAttacking, remainSecond.ToString());
                        break;
                    }
                case RecoverPartCode.InBatchCd:
                    {
                        var remainSecond = result.Param[0]; // 需要的冷却时间
                        Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.InBatchCD);
                        break;
                    }
                case RecoverPartCode.PartRecordNotFound:
                    tipId = 23120;
                    break;
                case RecoverPartCode.HpFull:
                    tipId = 23121;
                    break;
                case RecoverPartCode.RecordError:
                    tipId = 23122;
                    break;
                case RecoverPartCode.PlayerNearby:
                    tipId = 23123;
                    break;
                case RecoverPartCode.ConsumeItemFail:
                    Mc.MsgTips.ShowRealtimeWeakTip(1817);
                    break;
                case RecoverPartCode.CreatePartEntityFail:
                    tipId = 23124;
                    break;
                case RecoverPartCode.NoPart:
                    tipId = 23125;
                    break;
                case RecoverPartCode.ForbiddenState:
                    {
                        var remainSecond = result.Param[0]; // 需要的冷却时间
                        Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.TerritoryForbiddenState, remainSecond.ToString());
                        break;
                    }
                case RecoverPartCode.InBatchRecovering:
                    {
                        var remainSecond = result.Param[0]; // 需要的冷却时间
                        Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.InBatchRecovering, remainSecond.ToString());
                    }
                    break;
                case RecoverPartCode.InBatchUpgrading:
                    {
                        var remainSecond = result.Param[0]; // 需要的冷却时间
                        Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.InBatchUpgrading, remainSecond.ToString());
                    }
                    break;
                case RecoverPartCode.TerritoryUnowned:
                    {
                        Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.TerritoryUnowned);
                        break;
                    }
                case RecoverPartCode.IsRecovering:
                    {
                        var remainSecond = result.Param[0];
                        Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.InRecovering, remainSecond.ToString());
                        break;
                    }
                case RecoverPartCode.ToolCupboardNearby:
                    {
                        Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.ToolAreaConflict);
                        break;
                    }
                case RecoverPartCode.ConstructionMaxNumExceed:
                    //具体提示在ConstructionPartCountLimit里处理
                    break;
                case RecoverPartCode.RegionLock:
                    Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.CONSTRUCTION_REGION_LOCKED);
                    break;
                case RecoverPartCode.Suc:
                    tipId = 24065;
                    break;
            }
            if (tipId != -1)
                Mc.MsgTips.ShowRealtimeWeakTip(tipId);
        }

        [RpcHandler]
        public void OnFixComboChildren(long partId, int code)
        {
            logger.InfoFormat("OnFixComboChildren {0} {1}", partId, code);
            var ret = (EFixComboChildCode)code;
            switch (ret)
            {
                case EFixComboChildCode.Suc:
                    Mc.MsgTips.ShowRealtimeWeakTip(24061);
                    Mc.Construction.RecoverPartOver(code, partId);
                    break;
                case EFixComboChildCode.NoPermission:
                    break;
                case EFixComboChildCode.ConsumeFail:
                    break;
                case EFixComboChildCode.RegionLock:
                    Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.CONSTRUCTION_REGION_LOCKED);
                    break;
            }
        }

        /// <summary>
        /// 获的PartEntity的创建者
        /// </summary>
        /// <param name="entityId">建筑EntityId</param>
        /// <param name="roleId">建筑创建者RoleId</param>
        [RpcHandler]
        public void QueryPartOwnerAck(long entityId, ulong roleId)
        {
            logger.InfoFormat("QueryPartOwnerAck entityId {0} roleId {1}", entityId, roleId);
            Mc.Msg.FireMsg<long,ulong>(EventDefine.OnGetOutsideCreatorId,entityId,roleId);
        }

        /// <summary>
        /// 返回队友是否有领地柜和声望柜
        /// </summary>
        /// <param name="teammateRoleId">队友roleId</param>
        /// <param name="code">参考CheckTeammateTerritoryCabinetCode</param>
        [RpcHandler]
        public void CheckTeammateTerritoryCabinetAck(ulong teammateRoleId, int code)
        {
            logger.InfoFormat("CheckTeammateTerritoryCabinetAck teammateRoleId {0} code {1}", teammateRoleId, code);
            Mc.Msg.FireMsgAtOnce(EventDefine.OnCheckTeammateTerritoryCabinetAck, teammateRoleId,code);
            //if (code == CheckTeammateTerritoryCabinetCode.NON)
            //{
            //}
            //else if (code == CheckTeammateTerritoryCabinetCode.HAS_TERRITORY_CABINET)
            //{
            //}
            //else if (code == CheckTeammateTerritoryCabinetCode.HAS_REPUTATION_CABINET)
            //{
            //}
        }
        
        /// <summary>
        /// 请求生成领地蓝图数据
        /// </summary>
        /// <param name="partId"></param>
        /// <param name="respCode"></param>
        /// <param name="blueprintDatas"></param>
        public void RequestGenerateBlueprintData(int slot,long toolboxId,string name)
        {
            var playerEntity = ParentEntity as PlayerEntity;
            var playerPos = new Vector3(playerEntity.PosX,playerEntity.PosY, playerEntity.PosZ); 
            var terrCol = TerritoryManagerEntity.Instance.GetInChargeTerritoryEntity(playerPos);
            if (terrCol == null)
            {
                logger.Error("当前玩家不在任何领地内，无法生成蓝图配置");
                return;
            }
            RemoteCallGenerateBlueprintData(ERpcTarget.World,toolboxId,slot,name);
        }

        [RpcHandler]
        public void GenerateBlueprintDataResp(int respCode,int slot,string extraInfo)
        {
            var code = (EGenerateBlueprintDataRespCode)respCode;
            
            logger.InfoFormat("Slot {0} 生成蓝图配置结果: {1},extra info :{2} ", slot, code,extraInfo);
            switch (code)
            {
                case EGenerateBlueprintDataRespCode.Suc:
                    Mc.MsgTips.ShowRealtimeWeakTip(24272);
                    break;
                case EGenerateBlueprintDataRespCode.NoValidTerritory:
                    logger.InfoFormat("当前玩家不在任何领地内，无法生成蓝图配置 Slot:{0}", slot);
                    break;
                case EGenerateBlueprintDataRespCode.NotValidSlot:
                     logger.InfoFormat("槽位不合法 Slot:{0}", slot);
                    break;
                case EGenerateBlueprintDataRespCode.ServerBusy:
                    logger.InfoFormat("服务器繁忙，请稍后再试 Slot:{0}", slot);
                    break;
                case EGenerateBlueprintDataRespCode.ServerNoBriefData:
                    logger.InfoFormat("服务器没有简要数据，请稍后再试 Slot:{0}", slot);
                    break;
                case EGenerateBlueprintDataRespCode.PartNumTooLarge:
                    Mc.MsgTips.ShowRealtimeWeakTip(24271);
                    break;
                case EGenerateBlueprintDataRespCode.SameSlotSaving:
                    logger.InfoFormat("当前槽位正在保存中，请稍后再试 Slot:{0}", slot);
                    break;
                case EGenerateBlueprintDataRespCode.HasNoPermission:
                    Mc.MsgTips.ShowRealtimeWeakTip(24270);
                    break;
                case EGenerateBlueprintDataRespCode.SensitiveWords:
                    Mc.MsgTips.ShowRealtimeWeakTip(23012);
                    break;
            }
        }

        [RpcHandler]
        public void RequestCreatePlayerConstructionBlueprintResp(int respCode,long blueprintId)
        {
            var code = (ECreatePlayerConstructionBlueprintRespCode)respCode;
            switch (code)
            {
                case ECreatePlayerConstructionBlueprintRespCode.Suc:
                    Mc.Msg.FireMsgAtOnce(EventDefine.BlueprintCreatComplete);
                    break;
                case ECreatePlayerConstructionBlueprintRespCode.DistanceTooFar:
                    break;
                case ECreatePlayerConstructionBlueprintRespCode.PermissionNotPass:
                    break;
                case ECreatePlayerConstructionBlueprintRespCode.HasNoBriefDataList:
                    break;
                case ECreatePlayerConstructionBlueprintRespCode.PullConstructionDataFailed:
                    break;
                case ECreatePlayerConstructionBlueprintRespCode.HasNoBriefData:
                    break;
                case ECreatePlayerConstructionBlueprintRespCode.InCD:
                    break;
                case ECreatePlayerConstructionBlueprintRespCode.SimulatorCheckFailed:
                    break;
                case ECreatePlayerConstructionBlueprintRespCode.ConsumeNotEnough:
                    break;
                case ECreatePlayerConstructionBlueprintRespCode.WrongPlayerState:
                    break;
                case ECreatePlayerConstructionBlueprintRespCode.StoryUnLock:
                    logger.ErrorFormat("蓝图建筑{0}创建失败:故事未解锁", blueprintId);
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
            logger.InfoFormat("蓝图建筑{0}创建结果:{1}", blueprintId, code);
        }
    }
}