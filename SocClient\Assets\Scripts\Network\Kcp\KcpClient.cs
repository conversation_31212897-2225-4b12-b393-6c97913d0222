using System;
using System.Buffers;
using System.Collections.Concurrent;
using System.Net;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using WizardGames.Soc.Common.Cache;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Framework.Network.Impl;
using WizardGames.Soc.Common.Framework.Network.Kcp;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.SocClient.Framework;
#if !SOC_BOT_SERVER
#else
using WizardGames.Soc.SocBotServer.Threads;
#endif

using EDecompressorType = WizardGames.Soc.Common.Framework.Network.ECompressorType;

namespace WizardGames.Soc.SocClient.Network
{
    public class KcpClient : KcpPeer
    {
        private static readonly SocLogger logger = LogHelper.GetLogger(typeof(KcpClient));
        private const int MAX_TRY_COUNT = 2;
        protected readonly CallbackToken callbacks;
        protected KcpConfig config;
        protected readonly byte[] rawReceiveBuffer;
        protected readonly byte[] rawSendBuffer;
        private uint lastCookie;
        private long lastSwitchCookieTime;

        public volatile bool Connected;
        public volatile bool Active;

        private UdpNetClient udpClient;
        public IPEndPoint RemoteEndPoint;
        // 加密相关
        public Encrypter encrypter;
        public Decrypter decrypter;

        private KcpThread thread;

        public EDecompressorType DecompressorType { get; set; } = EDecompressorType.Lz4;
        public EKcpState ConnectState => state;
        public volatile DisconnectReason DisconnectReason;
        public ConcurrentQueue<IOutgoingPacket> OutgoingQueue { get; set; } = new();

        private string lastAddress;
        private ushort lastPort;
        private int tryCount;

#if SOC_BOT_SERVER
        public readonly ulong RoleId;
#endif


        // Debug功能，模拟丢包、错包、乱包
        /// <summary>
        /// 每N个收包丢一个包。配置0为关闭
        /// </summary>
        public int DropReceiveForEveryNPacket;
        /// <summary>
        /// 已正常收了多少个，达到DropReceiveForEveryNPacket就丢
        /// </summary>
        private int normalReceiveCount;
        /// <summary>
        /// 每N个发包丢一个包。配置0为关闭
        /// </summary>
        public int DropSendForEveryNPacket;
        /// <summary>
        /// 已正常发了多少个，达到DropSendForEveryNPacket就丢
        /// </summary>
        private int normalSendCount;
        /// <summary>
        /// 每N个收包改一个包。配置0为关闭
        /// </summary>
        public int ChangeReceiveForEveryNPacket;
        /// <summary>
        /// 已正常收了多少个，达到ChangeReceiveForEveryNPacket就改
        /// </summary>
        private int normalChangeReceiveCount;
        private Random random = new Random();
        // Debug功能End
#if !SOC_BOT_SERVER

        public KcpClient(CallbackToken token, KcpConfig config) : base(config, 0)
        {
            callbacks = token;
            this.config = config;
            rawReceiveBuffer = new byte[config.Mtu];
            rawSendBuffer = new byte[Kcp.COOKIE_SIZE];

            // 加密相关
            encrypter = new Encrypter(KcpUtils.GetEncryptSeed());
            decrypter = new Decrypter();
        }
#else
        public KcpClient(ulong roleId, CallbackToken token, KcpConfig config) : base(config, 0)
        {
            this.RoleId = roleId;
            callbacks = token;
            this.config = config;
            rawReceiveBuffer = new byte[config.Mtu];
            rawSendBuffer = new byte[Kcp.COOKIE_SIZE];

            // 加密相关
            encrypter = new Encrypter(KcpUtils.GetEncryptSeed());
            decrypter = new Decrypter();
        }
#endif

        public void SetKcpThread(KcpThread thread)
        {
            this.thread = thread;
        }

        public void DisconnectOnNextTick(DisconnectReason reason)
        {
            DisconnectReason = reason;
            callbacks.Reset();
        }

        public void Connect(string address, ushort port)
        {
            // 防重入
            if (Connected)
            {
                logger.Warn("[KCP] Client: already connected!");
                return;
            }

            if (!NetUtils.ResolveHostname(address, out IPAddress[] addresses))
            {
                callbacks.OnDisconnected?.Invoke(DisconnectReason.DnsResolve);
                return;
            }

            Reset();
            logger.Info($"[KCP] Connecting to server {address}:{port} / {addresses[0]}:{port}");

            RemoteEndPoint = new IPEndPoint(addresses[0], port);
            udpClient = new(RemoteEndPoint, OnRawDataReceived, OnDisconnected);
            udpClient.Start();
            Active = true;
            lastAddress = address;
            lastPort = port;
            byte[] aesData = encrypter.AesEncrypt();
            byte[] data = new byte[aesData.Length + 1];
            aesData.CopyTo(data, 0);
            //iOS设备让服务器使用lz4压缩
#if !SOC_BOT_SERVER
            data[^1] = UnityEngine.Application.platform == UnityEngine.RuntimePlatform.IPhonePlayer ? (byte)1 : (byte)0;
#else
            data[^1] = (byte)0;
#endif
            SendHello(new ArraySegment<byte>(data));
            tryCount++;
        }

        protected override void OnAuthed(ArraySegment<byte> message)
        {
            // |RpcHeader(1 byte) | AesData(16 bytes) | CompressMethod(1 byte)|
            //                    |               message                     |
            var haveCompressMethod = message.Count > 16;
            var messageLength = haveCompressMethod ? 17 : 16;
            if (message.Count > messageLength)
            {
                Disconnect(DisconnectReason.InvalidReceive);
                logger.ErrorFormat("OnAuthed message length is invalid {0}", message.Count);
                return;
            }
            try
            {
                decrypter.CheckCipherBytes(message.Array, message.Offset, 16);
            }
            catch (Exception e)
            {
                Disconnect(DisconnectReason.Unexpected);
                logger.ErrorFormat("OnAuthed decrypt error {0}", e);
                return;
            }

            if (haveCompressMethod)
            {
                DecompressorType = (EDecompressorType)message.Array[message.Count];
                logger.InfoFormat("DecompressorType: {0}", DecompressorType);
            }
            else
            {
                DecompressorType = EDecompressorType.Lz4;
                logger.InfoFormat("use default decompressor type: {0}", DecompressorType);
            }

            Connected = true;
            timeout = config.Timeout;
            callbacks.OnConnected?.Invoke();
        }

        protected static ArraySegment<byte> GetFullPacket(ArraySegment<byte> message, int offset)
        {
            var writerLen = message.Count - offset;
            if (writerLen < 4)
            {
                return ArraySegment<byte>.Empty;
            }

            var offsetPos = offset + message.Offset;
            var packetLength = (message.Array[offsetPos++] + (message.Array[offsetPos++] << 8) + (message.Array[offsetPos++] << 16) + (message.Array[offsetPos] << 24));
            if (packetLength <= 0 || packetLength > 10000000)
            {
                // this is not possible
                logger.Error($"Fatal network error, packet length is invalid {packetLength}");
                return ArraySegment<byte>.Empty;
            }

            if (writerLen >= packetLength + 4)
            {
                var ret = message.Slice(4 + offset, packetLength);
                return ret;
            }
            return ArraySegment<byte>.Empty;
        }

        protected static void Advance(ArrayAppender writer, Memory<byte> p)
        {
            // 包的长度为4个字节
            writer.Advance(p.Length + 4);
        }

        private void Decrypt(ref ArraySegment<byte> message)
        {
            try
            {
                var needDecrypt = message.Slice(1);
                decrypter.XorData(ref needDecrypt);
            }
            catch (Exception ex)
            {
                logger.Error($"[KCP] Client: decrypt error: {ex}");
                message = ArraySegment<byte>.Empty;
                return;
            }
        }

        protected override void OnData(ArraySegment<byte> message, EKcpChannel channel)
        {
            if (message.Count > 0)
            {
                try
                {
                    Decrypt(ref message);
                    callbacks.OnData?.Invoke(new Memory<byte>(message.Array, message.Offset, message.Count), channel);
                }
                catch (Exception ex)
                {
                    logger.ErrorFormat("[KCP] Client: callbacks.OnData with exception {0}", ex);
                }
            }
        }

        /// <summary>
        /// 多线程调用
        /// </summary>
        /// <param name="reason"></param>
        protected override void OnDisconnected(DisconnectReason reason)
        {
            if (!Active) return;
            logger.Info($"[KCP] Client: disconnected: {reason}");

            udpClient?.Disconnect();
            udpClient = null;
            RemoteEndPoint = null;
            if (!Connected && tryCount < MAX_TRY_COUNT)
            {
                Connected = false;
                ThreadHelper.PostToKcpThread(new SimpleTask(() => { Connect(lastAddress, lastPort); }));
            }
            else
            {
                Active = false;
                Connected = false;
                try
                {
                    callbacks.OnDisconnected?.Invoke(reason);
                }
                catch (Exception e)
                {
                    logger.Error($"[KCP] Client: OnDisconnected error: {e}");
                }
            }
        }

        protected override void OnError(EErrorCode error, string message)
        {
            callbacks.OnError?.Invoke(error, message);
        }

        private void OnRawDataReceived(ArraySegment<byte> segment, UdpBuffer buffer)
        {
            // 丢包测试
            if (DropReceiveForEveryNPacket > 0)
            {
                if (normalReceiveCount >= DropReceiveForEveryNPacket)
                {
                    normalReceiveCount = 0;
                    return;
                }
                normalReceiveCount++;
            }

            // 错包测试
            if (ChangeReceiveForEveryNPacket > 0)
            {
                if (normalChangeReceiveCount >= ChangeReceiveForEveryNPacket)
                {
                    normalChangeReceiveCount = 0;
                    var randomIndex = random.Next(segment.Offset, segment.Offset + segment.Count);
                    segment.Array[randomIndex] = (byte)random.Next(0, 256);
                    logger.Warn("OnRawDataReceived change received");
                }
                normalChangeReceiveCount++;
            }

#if !SOC_BOT_SERVER

            var task = OnDataReceivedTask.GetFromPool(ref segment, buffer, this);
            NetFlowCounter.Instance.RecvRawData(segment.Count);
            thread.Queue.PostAsyncTask(task);
#else
            var task = OnDataReceivedTask.GetFromPool(ref segment, buffer, this);
            thread.Queue.PostAsyncTask(task);
#endif
        }

        protected void Send(ArraySegment<byte> segment, EKcpChannel channel)
        {
            if (!Connected)
            {
                logger.Warn("[KCP] Client: can't send because not connected!");
                return;
            }

            try
            {
                var needEncrypt = segment.Slice(1);
                encrypter.XorData(ref needEncrypt);
                SendData(ref segment, channel);
            }
            catch (Exception ex)
            {
                logger.Warn($"[KCP] Client: Encrypt Error: {ex}");
                Disconnect(DisconnectReason.InvalidSend);
            }
        }

        private bool SwitchCookie(uint oldCookie, uint newCookie)
        {
            var now = TimeStampUtil.GetRawTimestampMsec();
            if (newCookie == lastCookie && now - lastSwitchCookieTime < NetConst.SWTICH_CONNECTION_TIMEOUT)
            {
                logger.InfoFormat("switch cookie fail old {0} -> new {1}", oldCookie, newCookie);
                return false;
            }

            lastCookie = oldCookie;
            lastSwitchCookieTime = now;
            logger.InfoFormat("switch cookie old {0} -> new {1}", oldCookie, newCookie);
            cookie = newCookie;
            return true;
        }

        public void RawInputInner(ArraySegment<byte> segment, EKcpChannel channel, uint messageCookie)
        {
            if (cookie == 0)
            {
                cookie = messageCookie;
                logger.Info($"[KCP] Client: received initial cookie: {cookie}");
            }
            else if (cookie != messageCookie)
            {
                // 切换Cookie失败 直接丢包
                if (!SwitchCookie(cookie, messageCookie))
                {
                    return;
                }
            }

            switch (channel)
            {
                case EKcpChannel.Reliable:
                    {
                        OnRawInputReliable(segment);
                        break;
                    }
                //case EKcpChannel.Unreliable:
                //    {
                //        OnRawInputUnreliable(segment);
                //        break;
                //    }
                default:
                    {
                        logger.Warn($"[KCP] Client: invalid channel header: {channel}, likely internet noise");
                        break;
                    }
            }
        }

        public void RawInput(ref ArraySegment<byte> segment)
        {
            try
            {
                KcpUtils.Decode32U(segment.Array, segment.Offset, out var messageCookie);
                segment = segment.Slice(Kcp.COOKIE_SIZE);
                RawInputInner(segment, EKcpChannel.Reliable, messageCookie);
            }
            catch (Exception ex)
            {
                logger.Warn($"[Kcp], fail to input inner {ex}");
                Disconnect(DisconnectReason.InvalidSend);
            }
        }

        public void Tick()
        {
            if (!Active) return;
            // 客户端本地强制断开 就不保证缓存区的包发送
            if (DisconnectReason == DisconnectReason.Force)
            {
                Disconnect(DisconnectReason.Force);
                return;
            }

            OnProcessSend();
            TickIncoming();
            TickOutgoing(false);

            if (DisconnectReason != DisconnectReason.Unknown)
            {
                Disconnect(DisconnectReason);
            }
        }

        private void OnProcessSend()
        {
            int count = 0;
            while (OutgoingQueue.TryDequeue(out var packet))
            {
                SendKcpPacket(packet);
                if (count++ > 100)
                {
                    logger.WarnFormat("Too many packts to send, break halfway left count={0}", OutgoingQueue.Count);
                    break;
                }
            }
        }

        private void SendKcpPacket(IOutgoingPacket packet)
        {
            try
            {
                if (!Connected) return;

                var packetBuffer = packet.GetBuffer();
                var len = packetBuffer.Length;

                using var msg = ArrayAppender.RentMemory((int)len + 1);
                msg.Memory.Span[0] = packet.GetPacketType();
                packetBuffer.CopyTo(msg.Memory.Span.Slice(1, (int)len));
                bool success = MemoryMarshal.TryGetArray(msg.Memory, out ArraySegment<byte> segment);
                if (!success)
                {
                    logger.Error($"Cannot convert Memory to ArraySegment");
                    return;
                }

                segment = segment.Slice(0, (int)len + 1);
                Send(segment, EKcpChannel.Reliable);
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("SendKcpPacket with exception {0}", ex.ToString());
            }
            finally
            {
                if (packet is IPooledObject ipo)
                {
                    ipo.ReturnToPool();
                }
            }
        }

        protected override void UdpSend(ArraySegment<byte> data, byte _)
        {

            if (DropSendForEveryNPacket > 0)
            {
                if (normalSendCount >= DropSendForEveryNPacket)
                {
                    normalSendCount = 0;
                    return;
                }
                normalSendCount++;
            }

            try
            {
                if (udpClient == null)
                {
                    return;
                }
                KcpUtils.Encode32U(rawSendBuffer, 0, cookie);
                udpClient.SendRawData(new ArraySegment<byte>(rawSendBuffer), data);
            }
            catch (SocketException e)
            {
                logger.Warn($"[KCP] Client: Send failed: {e}");
                OnError(EErrorCode.InvalidSend, "Send failed");
                state = EKcpState.Disconnected;
                OnDisconnected(DisconnectReason.SocketError);
            }
        }
    }
}
