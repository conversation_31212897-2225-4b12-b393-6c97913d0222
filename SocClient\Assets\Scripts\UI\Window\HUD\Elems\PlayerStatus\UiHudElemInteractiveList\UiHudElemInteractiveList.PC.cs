#if STANDALONE_REAL_PC
using FairyGUI;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Ui.Utils;

namespace WizardGames.Soc.SocClient.Ui
{
    /// <summary>
    /// 交互列表引导的相关逻辑挪到这里
    /// </summary>
    public partial class UiHudElemInteractiveList
    {
        public struct KeyTipsData
        {
            public ActionName actionName;
            public string title;
            public EventCallback0 callback;
        }

        // 快捷键列表控件
        private GList keyTipsList;

        /// <summary>
        /// 快捷键列表数据
        /// </summary>
        private List<KeyTipsData> keyTipsDatas = new ();

        // 类中预先分配一次缓存
        private readonly List<KeyTipsData> sortedKeyTipsDatas = new(8);  // 初始化容量足够大，避免扩容GC
        private static readonly KeyTipsData dummyData = default;

        private string switchPage = string.Empty;

        private static bool IsDummyData(ref KeyTipsData data)
        {
            return data.title == null && data.callback == null;
        }

        private void OnDisable_Platform()
        {
            switchPage = string.Empty;
        }

        private bool ShouldShowExpandListOnly(List<int> expandList, List<int> nearbyList, bool canShowPickList)
        {
            if (switchPage == SHOW_INTERACTIVE)
            {
                return true;
            }
            return expandList.Count > 0 && nearbyList.Count == 0 && !canShowPickList && !isInHorseFoodList;
        }

        private bool ShouldShowPickListOnly(List<int> expandList, List<int> nearbyList, bool canShowPickList)
        {
            if (switchPage == SHOW_PICKUP)
            {
                return true;
            }

            return expandList.Count == 0 && nearbyList.Count == 0 && canShowPickList && !isInHorseFoodList;
        }

        private bool ShouldTransferNearbyToExpand(List<int> expandList, bool canShowPickList)
        {
            if (switchPage == SHOW_NEARBY)
            {
                return true;
            }
            return expandList.Count == 0 && nearbyInteractiveType.Count == 1 && !canShowPickList && !isInHorseFoodList;
        }

        private void SetExpandListItemNum(int num)
        {
            if (IsInEdit) return;
            interactiveBtnList.numItems = num;
            interactiveBtnList.ResizeToFit(num);
            if (interactiveBtnList.visible && itemCountCtrl != null) itemCountCtrl.selectedIndex = num > 1 ? 1 : 0;
            if (lastNum != num)
            {
                //防刷标记
                lastNum = num;
                HotKeyUtils.RemoveHotKey(ref wheelUpAction);
                HotKeyUtils.RemoveHotKey(ref wheelDownAction);
                if (num > 1)
                {
                    wheelUpAction = HotKeyUtils.AddHotKey(ActionName.ListWheelUp, OnWheelUp);
                    wheelDownAction = HotKeyUtils.AddHotKey(ActionName.ListWheelDown, OnWheelDown);
                }
            }
        }

        private void AdjustInteractiveBtnListSize(List<int> expandList)
        {
            int itemCount = expandList.Count;
            if (itemCount > 5)
            {
                int maxHeight = interactiveRoot.maxWidth;
                interactiveBtnList.SetSize(interactiveBtnList.width, maxHeight);
                expandListCanScroll = true;
            }
            else
            {
                bool containsMatItem = false;
                float height = 0;
                for (int i = 0; i < itemCount; i++)
                {
                    var id = expandList[i];
                    if (id == EntityInteractiveIdHandle.BuildRepair || id == EntityInteractiveIdHandle.VehicleRepair)
                    {
                        containsMatItem = true;
                    }
                    var item = interactiveBtnList.GetChildAt(i);
                    if (item != null)
                    {
                        height += item.height + interactiveBtnList.lineGap;
                    }
                }

                if (containsMatItem)
                {
                    interactiveBtnList.SetSize(interactiveBtnList.width, height);
                }
                else
                {
                    interactiveBtnList.ResizeToFit(itemCount);
                }

                interactiveRoot.SetSize(interactiveRoot.width, interactiveBtnList.height + 10);
            }

            if (itemCount > 0)
            {
                if (interactiveBtnList.selectedIndex < 0)
                {
                    interactiveBtnList.selectedIndex = 0;
                    interactiveBtnList.ScrollToView(0);
                }

                SetPickKeyPosition(interactiveBtnList);
            }

            UpdateKeyTipsList();
        }

        private void RefreshHorseFoodList()
        {
            if (!horseFoodListCom.visible)
            {
                return;
            }

            horseFoodListTitle.text = GetTitleForNearbyType();
            curHorseFoods = GetAllHorseFoodsInBag();
            //如果背包中物品没了，就不显示了（打开喂食列表的同时，去背包把东西扔了）
            if (curHorseFoods.Count == 0)
            {
                isInHorseFoodList = false;
            }

            horseFoodList.numItems = curHorseFoods.Count;
            horseFoodList.ResizeToFit(horseFoodList.numItems);
            horseFoodListCanScroll = curHorseFoods.Count >= 5;
            if (curHorseFoods.Count > 0)
            {
                if (horseFoodList.selectedIndex < 0)
                {
                    horseFoodList.selectedIndex = 0;
                    horseFoodList.ScrollToView(0);
                }

                SetPickKeyPosition(horseFoodList);
            }

            UpdateKeyTipsList();
        }

        private void SetNearbyItemListNum(int num)
        {
            if (nearbyList == null) return;
            nearbyList.numItems = num;
            nearbyList.ResizeToFit(num);
            if (num > 0)
            {
                TryUnlockUpdate();
            }

            if (num > 0)
            {
                if (nearbyList.selectedIndex < 0)
                {
                    nearbyList.selectedIndex = 0;
                    nearbyList.ScrollToView(0);
                }

                SetPickKeyPosition(nearbyList);
            }

            UpdateKeyTipsList();
        }

        private void SetItemListNum(int num)
        {
            if (pickItemList == null) return;
            if (pickItemList.visible && itemCountCtrl != null) itemCountCtrl.selectedIndex = num > 1 ? 1 : 0;
            if (lastNum != num)
            {
                //防刷标记
                lastNum = num;
                HotKeyUtils.RemoveHotKey(ref wheelUpAction);
                HotKeyUtils.RemoveHotKey(ref wheelDownAction);
                if (num > 1)
                {
                    wheelUpAction = HotKeyUtils.AddHotKey(ActionName.ListWheelUp, OnWheelUp);
                    wheelDownAction = HotKeyUtils.AddHotKey(ActionName.ListWheelDown, OnWheelDown);
                }
            }

            if (num == 0)
            {
                pickItemList.numItems = 0;
                return;
            }

            if (pickItemList.visible)
            {
                pickItemList.numItems = num;
                pickItemList.ResizeToFit(num);
                if (num > 0)
                {
                    if (pickItemList.selectedIndex < 0)
                    {
                        pickItemList.selectedIndex = 0;
                        pickItemList.ScrollToView(0);
                    }

                    SetPickKeyPosition(pickItemList);
                }
            }

            if (num > 0)
            {
                SetPickTitleText();
            }

            FairyGuiUtil.SetListScrollEnable(pickItemList, num > 4);

            UpdateKeyTipsList();
        }

        private void SetPickSwitchComRootVisible(bool value)
        {
            pickSwitchComRoot.visible = value;
            var node = TryGetNode();
            SafeUtil.SafeSetVisible(node.GetChild("key"), value);
            node.SetKeyTipsAction("key", value ? OnHotKeyAction : null);
        }

        private void SetPickKeyPosition(GList list)
        {
            GObject item = null;
            for (int i = 0; i < list.numChildren; i++)
            {
                GObject obj = list.GetChildAt(i);
                var btn = obj.asButton;
                //list的item须是GButton单选
                if (btn.selected)
                {
                    item = obj;
                    break;
                }
            }

            if (item != null)
            {
                var selectKey = TryGetNode().GetChild("selectKey");
                var pos = item.LocalToGlobal(new Vector2(-selectKey.width - 15, item.height * 0.5f));
                selectKey.xy = selectKey.parent.GlobalToLocal(pos);
                (selectKey as ComKeyTips)?.SetBgStyle(2);
            }
        }

        private void OnWheelUp()
        {
            if (!IsElemEnable) return;
            GList list = GetList();
            if (list == null) return;
            var index = list.selectedIndex;
            if (index > 0)
            {
                index--;
                list.ScrollToView(index);
                list.selectedIndex = index;
                SetPickKeyPosition(list);
            }
        }

        private void OnWheelDown()
        {
            if (!IsElemEnable) return;
            GList list = GetList();
            if (list == null) return;
            var index = list.selectedIndex;
            if (index < list.numItems - 1)
            {
                index++;
                list.ScrollToView(index);
                list.selectedIndex = index;
                SetPickKeyPosition(list);
            }
        }

        private void OnBoardInit_Platform()
        {
            keyTipsList = TryGetNode().GetChild("keyTipsList").asList;
            keyTipsList.SetVirtual();
            keyTipsList.itemRenderer = OnKeyTipsListRender;
        }

        private void OnKeyTipsListRender(int index, GObject item)
        {
            if (index < 0 || index >= sortedKeyTipsDatas.Count) return;
            var data = sortedKeyTipsDatas[index];
            var com = item.asCom;

            bool left = index % 2 == 0;
            var titleKey = left ? "title" : "titleRight";
            var keyComKey = left ? "key" : "keyRight";

            var childName = keyComKey;
            var title = com.GetChild(titleKey).asTextField;
            var keyCom = com.GetChild(keyComKey).asCom;
            bool visible = !IsDummyData(ref data);
            SafeUtil.SafeSetVisible(keyCom, visible);
            SafeUtil.SafeSetVisible(title, visible);
            com.ClearKeyTipsAction(childName);
            if (visible)
            {
                SafeUtil.SafeSetText(title, data.title);
                com.InitKeyTips(childName, data.actionName);
                com.SetKeyTipsAction(childName, data.callback);
            }

            com.GetController("pos")?.SetSelectedIndex(index % 2);
        }

        private void UpdateKeyTipsList()
        {
            if (!IsElemEnable) return;
            if (keyTipsList == null) return;
            keyTipsDatas.Clear();

            // 交互对象常驻功能 -> 交互对象触发式功能显示 -> 单个选项功能显示
            CheckFullScreenHotKey();
            CheckPickAllHotKey();
            CheckSwitchListHotKey();
            CheckAutoOpenDoorHotKey();
            CheckCancelFoodHotKey();
            CheckSwitchFoodHotKey();
            CheckReloadHotKey();
            CheckQuickUseHotKey();

            SortKeyTipsData();

            keyTipsList.numItems = sortedKeyTipsDatas.Count;
        }

        private void SortKeyTipsData(int columnCount = 2)
        {
            sortedKeyTipsDatas.Clear();

            int totalCount = keyTipsDatas.Count;

            // 计算是否需要补空位
            bool needPad = totalCount % columnCount != 0;

            int paddedCount = totalCount;
            if (needPad)
                paddedCount += 1;  // 补一个占位数据

            int rowCount = paddedCount / columnCount;

            for (int row = 0; row < rowCount; row++)
            {
                int rowBase = row * columnCount;
                for (int col = columnCount - 1; col >= 0; col--)
                {
                    int index = rowBase + col;

                    if (index < totalCount)
                        sortedKeyTipsDatas.Add(keyTipsDatas[index]);
                    else
                        sortedKeyTipsDatas.Add(dummyData); // 补空位
                }
            }
        }


        private void UpdateUIStyle_Platform()
        {
            if (!string.IsNullOrEmpty(switchPage))
            {
                bool canShowThisPage = false;
                var canShowInteractiveIdList = CanShowInteractiveIdList();
                var canShowPickList = CanShowPickList();
                var canShowNearBy = nearbyList.numItems > 0;
                if (canShowInteractiveIdList && switchPage == SHOW_INTERACTIVE)
                {
                    canShowThisPage = true;
                }
                else if (canShowPickList && switchPage == SHOW_PICKUP)
                {
                    canShowThisPage = true;
                }
                else if (canShowNearBy && switchPage == SHOW_NEARBY)
                {
                    canShowThisPage = true;
                }

                if(canShowThisPage)
                {
                    stateCtrl.SetSelectedPage(switchPage);
                }
                else
                {
                    OnSwitchListHotKey();
                }
            }
        }

        #region 检测交互列表/拾取列表的各种快捷键操作
        private void CheckSwitchListHotKey()
        {
            if (!IsElemEnable) return;
            bool canShowPickList = CanShowPickList();
            bool canShowInteractiveIdList = CanShowInteractiveIdList();
            bool canShowNearBy = nearbyList.numItems > 0;
            if ((canShowPickList && canShowInteractiveIdList)|| (canShowInteractiveIdList && canShowNearBy))
            {
                keyTipsDatas.Add(new KeyTipsData
                {
                    actionName = ActionName.SwitchPickList,
                    title = LanguageManager.GetTextConst(LanguageConst.InteractiveShortcutTitleSwitchList),
                    callback = OnSwitchListHotKey
                });
            }
        }

        private void CheckFullScreenHotKey()
        {
            if (!IsElemEnable) return;
            var page = stateCtrl.selectedPage;
            if (page == SHOW_PICKUP)
            {
                keyTipsDatas.Add(new KeyTipsData
                {
                    actionName = ActionName.InteractiveFullScreen,
                    title = LanguageManager.GetTextConst(LanguageConst.InteractiveShortcutTitleFullScreen),
                    callback = OnFullScreenPickHotKey
                });
            }
        }

        private void CheckPickAllHotKey()
        {
            if (!IsElemEnable) return;
            var page = stateCtrl.selectedPage;
            if (page == SHOW_PICKUP)
            {
                keyTipsDatas.Add(new KeyTipsData
                {
                    actionName = ActionName.PickAll,
                    title = LanguageManager.GetTextConst(LanguageConst.PickAll),
                    callback = OnBtnQuickPickClick,
                });
            }
        }

        private void CheckAutoOpenDoorHotKey()
        {
            if (!IsElemEnable) return;

            var page = stateCtrl.selectedPage;
            if (page == SHOW_INTERACTIVE)
            {
                var canShowAutoOpen = InteractiveIdListChecker.CanShowAutoOpenDoorBtn();
                if (!canShowAutoOpen)
                {
                    return;
                }

                var autoOpenSwitch = InteractiveIdListChecker.CanAutoOpenDoor();
                var languageId = autoOpenSwitch ? LanguageConst.InteractiveShortcutTitleAutoOpenDoorTure :
                    LanguageConst.InteractiveShortcutTitleAutoOpenDoorFalse;
                keyTipsDatas.Add(new KeyTipsData
                {
                    actionName = ActionName.InteractiveShortcutAction,
                    title = LanguageManager.GetTextConst(languageId),
                    callback = OnAutoOpenDoorHotKey
                });
            }   
        }

        private void CheckCancelFoodHotKey()
        {
            if (!IsElemEnable) return;
            if (isInHorseFoodList && curHorseFoods.Count > 0)
            {
                keyTipsDatas.Add(new KeyTipsData
                {
                    actionName = ActionName.InteractiveShortcutAction,
                    title = LanguageManager.GetTextConst(LanguageConst.InteractiveShortcutTitleCancelFood),
                    callback = OnCancelFoodHotKey
                });
            }
        }

        private void CheckSwitchFoodHotKey()
        {
            if (!IsElemEnable) return;
            var page = stateCtrl.selectedPage;
            if (page != SHOW_INTERACTIVE) return;
            var selectIndex = interactiveBtnList.selectedIndex;
            if (selectIndex < 0 || selectIndex >= InteractiveIdListChecker.InteractiveIdSortList.Count)
            {
                return;
            }

            var interactiveId = InteractiveIdListChecker.InteractiveIdSortList[selectIndex];
            if(interactiveId != EntityInteractiveIdHandle.HorseFeed)
            {
                return;
            }

            if (canFeedHorse)
            {
                keyTipsDatas.Add(new KeyTipsData
                {
                    actionName = ActionName.InteractiveShortcutAction,
                    title = LanguageManager.GetTextConst(LanguageConst.InteractiveShortcutTitleSwitchFood),
                    callback = OnSwitchFoodHotKey
                });
            }
        }

        private void CheckReloadHotKey()
        {
            if (!IsElemEnable) return;
            var page = stateCtrl.selectedPage;
            if (page != SHOW_INTERACTIVE) return;
            var selectIndex = interactiveBtnList.selectedIndex;
            if (selectIndex < 0 || selectIndex >= InteractiveIdListChecker.InteractiveIdSortList.Count)
            {
                return;
            }

            var interactiveId = InteractiveIdListChecker.InteractiveIdSortList[selectIndex];
            if (interactiveId == EntityInteractiveIdHandle.SHOTGUN_ADD_AMMO)
            {
                keyTipsDatas.Add(new KeyTipsData
                {
                    actionName = ActionName.InteractiveShortcutAction,
                    title = LanguageManager.GetTextConst(LanguageConst.InteractiveShortcutTitleReload),
                    callback = OnReloadHotKey
                });
            }
        }

        private void CheckQuickUseHotKey()
        {
            if (!IsElemEnable) return;
            var page = stateCtrl.selectedPage;
            if (page != SHOW_PICKUP) return;
            var selectIndex = pickItemList.selectedIndex;
            if (selectIndex < 0 || selectIndex >= pickItemList.numItems)
            {
                return;
            }

            var btn = pickItemList.GetChildAt(selectIndex).asButton;
            var useStateCtrl = btn.GetController("useState");
            if (useStateCtrl.selectedIndex > 0)
            {
                keyTipsDatas.Add(new KeyTipsData
                {
                    actionName = ActionName.QuickUse,
                    title = LanguageManager.GetTextConst(LanguageConst.QuickUse),
                    callback = OnQuickUseHotKey
                });
            }
        }

        /// <summary>
        /// 切换列表
        /// </summary>
        private void OnSwitchListHotKey()
        {
            if (!IsElemEnable) return;
            var page = stateCtrl.selectedPage;
            var showPage = string.Empty;
            if (page == SHOW_PICKUP)
            {
                bool canShowNearBy = nearbyList.numItems > 0;
                if (canShowNearBy)
                {
                    showPage = SHOW_NEARBY;
                }
                else
                {
                    showPage = SHOW_INTERACTIVE;
                }
            }
            else if (page == SHOW_INTERACTIVE || page == SHOW_NEARBY)
            {
                showPage = SHOW_PICKUP;
                OpenPickList = true;
            }

            switchPage = showPage;

            stateCtrl.SetSelectedPage(showPage);
        }

        private void OnFullScreenPickHotKey()
        {
            if (!IsElemEnable) return;
            OpenBag();
        }

        private void OnAutoOpenDoorHotKey()
        {
            if (!IsElemEnable) return;
            InteractiveIdListChecker.OnAutoOpenDoorClick();
        }

        private void OnCancelFoodHotKey()
        {
            if (!IsElemEnable) return;
            CloseHorseFoodList(null);
        }

        private void OnSwitchFoodHotKey()
        {
            if (!IsElemEnable) return;
            ShowHorseFoodListOnly();
        }

        private void OnReloadHotKey()
        {
            if (!IsElemEnable) return;
            InteractiveIdListChecker.OnAddResItemClick();
        }

        private void OnQuickUseHotKey()
        {
            var selectIndex = pickItemList.selectedIndex;
            if (selectIndex < 0 || selectIndex >= pickItemList.numItems)
            {
                return;
            }

            if (pickRenderList != null && pickRenderList.Count > pickItemList.selectedIndex)
                DoQuickUseItem(pickRenderList[pickItemList.selectedIndex]);
        }

        #endregion
    }
}

#endif