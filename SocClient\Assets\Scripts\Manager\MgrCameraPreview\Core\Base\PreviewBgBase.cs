using com.pixui;
using Effect;
using System;
using UnityEngine;
using UnityEngine.Rendering.Universal;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.Unity.Extend;
using WizardGames.Soc.Common.Unity.ObjPool;
using WizardGames.Soc.SocClient.GoLoader;

namespace WizardGames.Soc.SocClient.Manager
{
    public class PreviewBgBase
    {
        #region 属性
        protected GameObject goPreviewBg;
        public Transform TrPreviewBg => goPreviewBg.transform;
        
        protected Transform trCamera;
        public Transform TrCamera => trCamera;

        private Transform trModelHolder;
        public Transform TrModelHolder => trModelHolder;
        private Camera previewCamera;
        public Camera PreviewCamera
        {
            get => previewCamera;
            set => previewCamera = value;
        }

        private Camera customMainCamera;
        public Camera CustomMainCamera
        {
            get => customMainCamera;
            set => customMainCamera = value;
        }

        protected PreviewModelBase previewModel;
        private EffectItemHandle<EffectItem> effectItemHandle = default;
        
        private const int UI_LAYER_INDEX = 5;
        protected int bgPrefabId = -1;
        private ulong asyncId = 0;
        
        private const string CAMERA_PREVIEW_LAYER = "VirtualConstruction";
        private const string UI_LAYER = "UI";

        public bool RemovePoolOnDispose = false;
        #endregion
        
        public virtual void InstanceBgPrefab(int bgPrefabId, Vector3 position, Vector3 eulerAngles,Transform trParent,Action<GameObject> actionBgLoadComplete,bool isAsync = true)
        {
            if (goPreviewBg == null)
            {
                Common.Data.DataItem.CameraPreviewBgConfig cameraPreviewBgConfig = Mc.Tables.TbCameraPreviewBgConfig.GetOrDefault(bgPrefabId);

                if (isAsync)
                {
                    if (asyncId == 0)
                    {
                        asyncId = GoPool.GetAsync(cameraPreviewBgConfig.Path,(goBg, objs) =>
                        {
                            asyncId = 0;
                            if (goBg == null)
                            {
                                return;
                            }
                            Init(bgPrefabId, position, eulerAngles, goBg, trParent, actionBgLoadComplete);

                        }, InstanceTypeId.DEFAULT_INSTANCE_TYPE_ID);
                    } 
                }
                else
                {
                    GameObject goBg = GoPool.Get(cameraPreviewBgConfig.Path);
                    Init(bgPrefabId, position, eulerAngles, goBg, trParent, actionBgLoadComplete);
                }
            }
            else
            {
                goPreviewBg.transform.position = position;
                goPreviewBg.transform.eulerAngles = eulerAngles;
                actionBgLoadComplete?.Invoke(goPreviewBg);
            }
        }

        private void Init(int bgPrefabId, Vector3 position, Vector3 eulerAngles, GameObject goBg, Transform trParent,
            Action<GameObject> actionBgLoadComplete)
        {
            goPreviewBg = goBg;
            goPreviewBg.SetParent(trParent);
            goPreviewBg.transform.position = position;
            goPreviewBg.transform.eulerAngles = eulerAngles;
            SetBgLayer();

            trCamera = goPreviewBg.transform.Find("CameraPreview");
            trModelHolder = goPreviewBg.transform.Find("ModelHolder");

            if (previewCamera == null && trCamera != null)
            {
                previewCamera = trCamera.GetComponent<Camera>();
            }

            this.bgPrefabId = bgPrefabId;
            SetCameraParams();
            SetCameraLayerCullDistances();
            SetCameraCullingMask();
            actionBgLoadComplete?.Invoke(goPreviewBg);
            Common.Data.DataItem.CameraPreviewBgConfig cameraPreviewBgConfig = Mc.Tables.TbCameraPreviewBgConfig.GetOrDefault(bgPrefabId);
            InstanceFx(cameraPreviewBgConfig.FxId);
        }

        protected virtual void SetBgLayer()
        {
            if (goPreviewBg == null)
            {
                return;
            }
            var renderers = goPreviewBg.GetComponentListInChildrenByPool<Renderer>(true);
            if (null == renderers || renderers.Count <= 0)
            {
                Pool.ReleaseList(renderers);
                return;
            }
            int previewLayerId = LayerMask.NameToLayer(CAMERA_PREVIEW_LAYER);
            for (int i = 0; i < renderers.Count; i++)
            {
                Renderer r = renderers[i];
                r.gameObject.layer = previewLayerId;
            }
            Pool.ReleaseList(renderers);
        }

        protected virtual void SetCameraParams()
        {
            if (previewCamera != null)
            {
                Common.Data.DataItem.CameraPreviewBgConfig cameraPreviewBgConfig = Mc.Tables.TbCameraPreviewBgConfig.GetOrDefault(bgPrefabId);
                Common.Data.DataItem.CameraParamsConfig cameraParamsConfig = Mc.Tables.TbCameraParamsConfig.GetOrDefault(cameraPreviewBgConfig.CameraParamsConfigId);

                if (cameraParamsConfig.PreviewCameraCullingMask.Length > 0)
                {
                    int cullingMask = 0;
                    for (int i = 0; i < cameraParamsConfig.PreviewCameraCullingMask.Length; i++)
                    {
                        int layerId = LayerMask.NameToLayer(cameraParamsConfig.PreviewCameraCullingMask[i]);
                        cullingMask = cullingMask | (1 << layerId);
                    }

                    previewCamera.cullingMask = cullingMask;
                }

                previewCamera.depth = cameraParamsConfig.PreviewCameraDepth;

                UniversalAdditionalCameraData universalAdditionalCameraData = previewCamera.GetUniversalAdditionalCameraData();
                universalAdditionalCameraData.renderPostProcessing = cameraParamsConfig.PreviewCameraPostProcessing == 1;
                universalAdditionalCameraData.gammaCorrectionBeforeUI = cameraParamsConfig.PreviewCameraCammaCorrection == 1;
                universalAdditionalCameraData.renderShadows = cameraParamsConfig.PreviewCameraRenderShadows == 1;
                if(Enum.TryParse(cameraParamsConfig.PreviewCameraAntiAliasing,out AntialiasingMode antialiasingMode))
                {
                    universalAdditionalCameraData.antialiasing = antialiasingMode;
                }
                else
                {
                    universalAdditionalCameraData.antialiasing = AntialiasingMode.None;
                }
                universalAdditionalCameraData.SetRenderer(cameraParamsConfig.PreviewCameraRenderIndex);
                renderResolutionSync(cameraParamsConfig.PreviewCameraRenderIndex);

                int uiLayerId = LayerMask.NameToLayer(UI_LAYER);
                previewCamera.skipCullingMask = (1 << uiLayerId);
            }
        }
        
        private void renderResolutionSync(int renderIndex) 
        {
            var renderer = UniversalRenderPipeline.asset.GetRenderer(renderIndex);
            var targetResolution = RMQualityManager.GetRenderResolutionHeight();

            if (renderer != null && previewCamera != null)
            {
                var resolution = new Vector2() { x = previewCamera.pixelWidth * renderer.renderScale, y = previewCamera.pixelHeight * renderer.renderScale };
                if (resolution.y != targetResolution)
                {
                    var renderScale = targetResolution / previewCamera.pixelHeight;
                    renderer.renderScale = renderScale;

                    Debug.LogFormat("renderResolutionSync:{0}", targetResolution);
                }
            }
        }

        protected virtual void SetCameraLayerCullDistances()
        {
            if (previewCamera != null)
            {
                float[] layerCullDistances = previewCamera.layerCullDistances;
                if (layerCullDistances.Length > UI_LAYER_INDEX)
                {
                    //用来设置UI layer裁剪距离,防止UI不显示
                    layerCullDistances[UI_LAYER_INDEX] = 10000;
                    previewCamera.layerCullDistances = layerCullDistances;
                }
            }
        }

        public virtual void SetPreviewModel(PreviewModelBase previewModel)
        {
            if(previewModel.GoModel == null)
            {
                return;
            }

            this.previewModel = previewModel;
            if (trModelHolder != null)
            {
                this.previewModel.GoModel.SetParent(trModelHolder);
            }
            
            this.previewModel.GoModel.transform.localPosition = Vector3.zero;
            this.previewModel.GoModel.transform.localEulerAngles = Vector3.zero;
        }

        protected virtual void InstanceFx(int fxId)
        {
            if (fxId != 0 && Mc.Effect != null)
            {
                effectItemHandle = Mc.Effect.PlayEffect<EffectItem>(fxId, 1000, goPreviewBg.transform);
                effectItemHandle.Play();
            }
        }
        
        /// <summary>
        ///使预览相机初始位置与场景相机位置相同一致
        /// </summary>
        /// <param name="sourceModel"></param>
        public virtual void SetCameraToSceneCamera()
        {
            if (trCamera != null)
            {
                Camera sceneMainCam = GetSceneCamera();
                if (sceneMainCam != null)
                {
                    trCamera.position = sceneMainCam.transform.position;
                    trCamera.rotation = sceneMainCam.transform.rotation;
                    previewCamera.fieldOfView = sceneMainCam.fieldOfView;
                }
            }
        }

        public virtual void SetCameraCullingMask()
        {
            Camera sceneMainCam = GetSceneCamera();
            if (null != sceneMainCam)
            {
                //剔除VirtualConstruction层
                int layerId = LayerMask.NameToLayer(CAMERA_PREVIEW_LAYER);
                sceneMainCam.cullingMask &= ~(1 << layerId);
            }
        }

        private Camera GetSceneCamera()
        {
            Camera sceneMainCam = null;
            if (customMainCamera != null)
            {
                sceneMainCam = customMainCamera;
            }
            else
            {
                if (Mc.Ui.IsInGame && Mc.MyPlayer != null)
                {
                    sceneMainCam = Mc.MyPlayer.GetMyCamera;
                } 
            }

            return sceneMainCam;
        }

        public virtual void Dispose()
        {
            Common.Data.DataItem.CameraPreviewBgConfig cameraPreviewBgConfig = Mc.Tables.TbCameraPreviewBgConfig.GetOrDefault(bgPrefabId);
            if (asyncId != 0)
            {
                if (!string.IsNullOrWhiteSpace(cameraPreviewBgConfig.Path))
                {
                    GoPool.CancelAsync(cameraPreviewBgConfig.Path, asyncId);
                    asyncId = 0;
                }
            }

            if (goPreviewBg != null &&!string.IsNullOrWhiteSpace(cameraPreviewBgConfig.Path))
            {
                GoPool.Release(cameraPreviewBgConfig.Path, goPreviewBg);
            }

            if (RemovePoolOnDispose && GoPool.TryGetPool(cameraPreviewBgConfig.Path, out var pool) && pool.IsClearable)
            {
                GoPool.RemovePool(cameraPreviewBgConfig.Path);
            }

            if (trModelHolder != null)
            {
                // trModelHolder.localPosition = Vector3.zero; 
                // trModelHolder.localRotation = Quaternion.identity;
                trModelHolder = null;
            }

            bgPrefabId = -1;
            goPreviewBg = null;
            previewCamera = null;
            previewModel = null;
            if(Mc.Effect != null)
                Mc.Effect.Release(effectItemHandle);
        }
        
        #region 异步操作，为了防止切换时防止闪烁，先新生成新的，再dispose旧的

        protected GameObject lastGoPreviewBg;
        protected ulong lastAsyncId;
        protected Transform lastTrCamera;
        protected Transform lastTrModelHolder;
        protected Camera lastPreviewCamera;
        
        protected int lastBgPrefabId;
        public virtual void SaveLast()
        {
            lastGoPreviewBg = goPreviewBg;
            lastAsyncId = asyncId;
            lastTrCamera = trCamera;
            lastTrModelHolder = trModelHolder;
            lastPreviewCamera = previewCamera;
            lastBgPrefabId = bgPrefabId;

            goPreviewBg = null;
            asyncId = 0;
            trCamera = null;
            trModelHolder = null;
            previewCamera = null;
        }

        public virtual void LastDispose()
        {
            Common.Data.DataItem.CameraPreviewBgConfig cameraPreviewBgConfig = Mc.Tables.TbCameraPreviewBgConfig.GetOrDefault(lastBgPrefabId);
            if (lastAsyncId != 0)
            {
                if (!string.IsNullOrWhiteSpace(cameraPreviewBgConfig.Path))
                {
                    GoPool.CancelAsync(cameraPreviewBgConfig.Path, lastAsyncId);
                    lastAsyncId = 0;
                }
            }
            
            if (lastGoPreviewBg != null &&!string.IsNullOrWhiteSpace(cameraPreviewBgConfig.Path))
            {
                GoPool.Release(cameraPreviewBgConfig.Path, lastGoPreviewBg);
            }

            if (lastTrModelHolder != null)
            {
                // lastTrModelHolder.localPosition = Vector3.zero; 
                // lastTrModelHolder.localRotation = Quaternion.identity;
                lastTrModelHolder = null;
            }

            if (lastTrCamera != null)
            {
                lastTrCamera.localPosition = Vector3.zero;
                lastTrCamera.localRotation = Quaternion.identity;
                lastTrCamera = null;
            }

            lastBgPrefabId = -1;
            lastGoPreviewBg = null;
            lastPreviewCamera = null;
        }

        #endregion
    }
}
