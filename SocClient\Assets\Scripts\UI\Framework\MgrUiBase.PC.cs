#if STANDALONE_REAL_PC

using FairyGUI;
using System;
using System.Collections.Generic;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Profile;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Ui;

namespace WizardGames.Soc.Common.Unity.Ui
{
    public partial class MgrUiBase
    {
        protected EProxyPostfix ProxyPostfix = EProxyPostfix._PC;

        /// <summary>
        /// 外部添加Esc回调
        /// 可手动添加esc回调，优先级 callback>topBar>EscClose
        /// 适用情况：
        /// 1.无topBar，或者不是ui界面表的配置项，如hud，需要响应esc
        /// 2.ui界面中的子界面、组件，需要在ui界面响应之前响应esc(另一种实现方式是在OnEscClose函数中，在关闭ui之前做条件判断return，参考UiMainMap、UiLobbyFriend)
        /// </summary>
        /// <param name="name">EEscCloseName</param>
        /// <param name="callback">回调返回值true则EscAction将被移除，false表示回调继续响应esc</param>
        /// <param name="layer">LayerIdConst</param>
        /// <param name="canAllEsc">是否可以被InvokeAllEsc批量关闭，默认可以</param>
        public void AddEscStack(EEscCloseName name, Func<bool> callback, int layer, bool canAllEsc = true)
        {
            if (callback == null)
            {
                throw new ArgumentNullException("callback", "Esc回调不能为null");
            }

            var act = AddEscStack(name.ToString(), callback, layer, canAllEsc);
            if (act != null) act.Name = name;
        }

        /// <summary>
        /// win添加到esc栈
        /// 可手动添加esc回调，优先级 callback>topBar>EscClose
        /// </summary>
        /// <param name="uiName">UI唯一name</param>
        /// <param name="callback">回调返回值true则EscAction将被移除，false表示回调继续响应esc</param>
        /// <param name="canAllEsc">是否可以被InvokeAllEsc批量关闭，默认可以</param>
        private EscAction AddEscStack(string uiName, Func<bool> callback, int layer = LayerIdConst.Photo,
            bool canAllEsc = true)
        {
            var win = GetWindow(uiName);
            if (win == null)
            {
                logger.WarnFormat("AddEscStack win already removed {0}", uiName);
                return null;
            }
            var uiInfo = GetUIRegisterInfo(uiName);
            if (callback == null)
            {
                //手动add时，callback不能为null
                var topBar = win.ComRoot?.GetChild("topBar") as ComTopBar;
                if (topBar == null)
                {
                    if (uiInfo.WinInfo.EscClose == EEscCloseType.None)
                    {
                        //不阻塞的界面不放入栈
                        return null;
                    }
                }
            }

            //默认情况下，填表的使用表layer
            if (uiInfo != null && layer == LayerIdConst.Photo) layer = uiInfo.WinInfo.Layer;

            var oldAct = RemoveEscStack(uiName);
            if (oldAct?.Callback == null)
            {
                //有callback的优先于topBar和配置EscClose
                oldAct = new EscAction() { UiName = uiName, Callback = callback, Layer = layer };
            }

            oldAct.CanAllEsc = canAllEsc;
            for (int i = EscStack.Count - 1; i > -1; i--)
            {
                if (EscStack[i].Layer <= layer)
                {
                    EscStack.Insert(i + 1, oldAct);
                    return oldAct;
                }
            }

            EscStack.Insert(0, oldAct);
            return oldAct;
        }

        /// <summary>
        /// 外部删除Esc回调
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public EscAction RemoveEscStack(EEscCloseName name)
        {
            return RemoveEscStack(name.ToString());
        }

        /// <summary>
        /// win从esc栈移除
        /// </summary>
        /// <param name="uiName"></param>
        /// <returns></returns>
        private EscAction RemoveEscStack(string uiName)
        {
            for (int i = 0; i < EscStack.Count; i++)
            {
                if (EscStack[i].UiName == uiName)
                {
                    var oldAct = EscStack[i];
                    EscStack.RemoveAt(i);
                    return oldAct;
                }
            }

            return null;
        }

        /// <summary>
        /// 设置GComponent创建后的统一初始化处理
        /// </summary>
        public virtual void OnFGUIComCreated(GComponent com)
        {
            //设置平台控制器选项 0 mobile, 1 pc
            //如果此控制器控制了scale，position和size，节点这些属性的统一处理就会失效。如root节点的缩放，bottomBar节点对content的挤压排列
            com.GetController("platform")?.SetSelectedIndex(1);
        }

        private void WinPlatformInit(WindowComBase win, GObject createObj)
        {
            // PC平台上，开启界面的默认缩放适配
            // 如果ContentPane本身就是在_PC包中，则认为所有PC相关的适配均在编辑器中得到妥善处理, 这里不再自动缩放root节点
            // 6.10 PC 再次需要缩放
            // string belongPackName = createObj?.packageItem?.owner?.name ?? string.Empty;
            // bool isFromPcProxy = belongPackName.EndsWith(EProxyPostfix._PC.ToString());
            //if (!isFromPcProxy)
            //{
                float factor = Mc.Tables.TbGlobalConfig.PCUIScale;
                if (win.WinInfo.MultiScale) factor *= Mc.Tables.TbGlobalConfig.PCUIMultiScale;
                win.MakePCRootScaleAdaption(factor);
            //}

            // pc计算root节点尺寸
            win.MakePCMonitorRootAdaption();
        }

        /// <summary>
        /// 遍历所有的UI节点
        /// </summary>
        private void ForEachUINode(WindowComBase win)
        {
            if (win == null) return;

            win.TraverseUINode(win.ContentPane, (ctrl) =>
            {
                // 检查控件是否需要解锁
                if (ctrl.NeedUnlock())
                {
                    win.AddNeedCheckUnlockCtrl(ctrl);
                }

                // 检查控件是否是视差效果组件
                if (ctrl.name.Contains(EParallaxCtrlName.BG))
                {
                    Mc.Ui.AddParallLayer(win, ctrl, EParallaxType.Bg);
                }
                else if (ctrl.name.Contains(EParallaxCtrlName.DECORATION))
                {
                    Mc.Ui.AddParallLayer(win, ctrl, EParallaxType.Decoration);
                }
                else if (ctrl.name.Contains(EParallaxCtrlName.BOARD))
                {
                    Mc.Ui.AddParallLayer(win, ctrl, EParallaxType.Board);
                }
                else if (ctrl.name.Contains(EParallaxCtrlName.CARD))
                {
                    var parallaxLayer = Mc.Ui.AddParallLayer(win, ctrl, EParallaxType.Card);
                    var originalPos = ctrl.position;
                    var parent = ctrl.parent;
                    if (parent != null)
                    {
                        parent.onRollOver.Add(() =>
                        {
                            if (parallaxLayer != null)
                            {
                                parallaxLayer.IsHover = true;
                            }
                            Mc.Ui.EnableParallaxMouseMoveEffect = false;
                        });

                        parent.onRollOut.Add(() =>
                        {
                            if (parallaxLayer != null)
                            {
                                parallaxLayer.IsHover = false;
                            }
                            Mc.Ui.EnableParallaxMouseMoveEffect = true;
                            ctrl.position = originalPos;
                        });
                    }
                }
            });
        }
    }
}
#endif