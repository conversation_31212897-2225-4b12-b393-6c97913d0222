#if STANDALONE_REAL_PC
using WizardGames.Soc.Common.Unity.Ui;

namespace WizardGames.Soc.SocClient.Ui
{
    /// <summary>
    /// 物品图标通用拖拽逻辑
    /// </summary>
    public partial class UiItemIconDragDrop : WindowComBase
    {
        private void SetDragIconSize(ComBaseIcon from)
        {
            if (UseFromIconSize)
            {
                dragIcon.scale = from.scale;
                dragIcon.size = from.size;
            }
        }
    }
}
#endif