using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Procedural;

#if SERVER
using TrackPosition = WizardGames.Soc.Procedural.TrainTrackSpline.TrackPosition;
using TrackOrientation = WizardGames.Soc.Procedural.TrainTrackSpline.TrackOrientation;
#endif

// Find all train track splines in the world and combine them into complete train track(s) wherever they join up
public class CollateTrainTracks
{
#if SERVER
    const float MAX_NODE_DIST = 0.2f; // Metres
    const float MAX_NODE_DIST_SQR = MAX_NODE_DIST * MAX_NODE_DIST;
    const float MAX_NODE_ANGLE = 15f; // Degrees

    public bool RunOnCache
    {
        get { return true; }
    }

    public static void Process(uint seed = 0)
    {
        var rails = GameObject.Find("Rails");

        if (rails == null)
            return;
        // Find all train track pieces
        List<TrainTrackSpline> trainTrackSplines = new List<TrainTrackSpline>( rails.GetComponentsInChildren<TrainTrackSpline>());
        if (trainTrackSplines == null || trainTrackSplines.Count == 0)
        {
            return;
        }
        trainTrackSplines.Sort((a, b) => a.hierarchy.CompareTo(b.hierarchy));
        // For track splines with more than two nodes, see if any other splines connect at their ends to
        // a node that's in the middle somewhere. If so, split that long spline at the connection point
        for (int splineIndex = 0; splineIndex < trainTrackSplines.Count; splineIndex++)
        {
            TrainTrackSpline ourSpline = trainTrackSplines[splineIndex];
            // DataIndex check: We can't modify splines that have pre-cached WorldSplineSharedData
            if (ourSpline.dataIndex < 0 && ourSpline.points.Length > 3)
            {
                for (int nodeIndex = ourSpline.points.Length - 2; nodeIndex >= 1; nodeIndex--)
                {
                    Vector3 ourPos = ourSpline.points[nodeIndex];
                    Vector3 ourTangent = ourSpline.tangents[nodeIndex];

                    foreach (TrainTrackSpline otherSpline in trainTrackSplines)
                    {
                        if (ourSpline == otherSpline)
                            continue;

                        Vector3 theirStartPos = otherSpline.GetStartPointWorld();
                        Vector3 theirEndPos = otherSpline.GetEndPointWorld();
                        Vector3 theirStartTangent = otherSpline.GetStartTangentWorld();
                        Vector3 theirEndTangent = otherSpline.GetEndTangentWorld();

                        // Compare all possible variations
                        if (!CompareNodes(theirStartPos, theirStartTangent))
                        {
                            if (!CompareNodes(theirEndPos, theirEndTangent))
                            {
                                if (!CompareNodes(theirStartPos, -theirStartTangent))
                                {
                                    CompareNodes(theirEndPos, -theirEndTangent);
                                }
                            }
                        }

                        bool CompareNodes(Vector3 theirPos, Vector3 theirTangent)
                        {
                            // For start-to-end, the tangents will match, but for end-to-end or start-to-start,
                            // they will be opposite, so check both
                            if (NodesConnect(ourPos, theirPos, ourTangent, theirTangent))
                            {
                                // Split our spline at the point where the other spline connects to it
                                // The central split-point node will end up in both
                                TrainTrackSpline newSpline = ourSpline.gameObject.AddComponent<TrainTrackSpline>();
                                Vector3[] newPartPoints = new Vector3[ourSpline.points.Length - nodeIndex];
                                Vector3[] newPartTangents = new Vector3[ourSpline.points.Length - nodeIndex];
                                Vector3[] oldPartPoints = new Vector3[nodeIndex + 1];
                                Vector3[] oldPartTangents = new Vector3[nodeIndex + 1];

                                for (int i = ourSpline.points.Length - 1; i >= 0; i--)
                                {
                                    if (i >= nodeIndex)
                                    {
                                        newPartPoints[i - nodeIndex] = ourSpline.points[i];
                                        newPartTangents[i - nodeIndex] = ourSpline.tangents[i];
                                    }
                                    if (i <= nodeIndex)
                                    {
                                        oldPartPoints[i] = ourSpline.points[i];
                                        oldPartTangents[i] = ourSpline.tangents[i];
                                    }
                                }

                                ourSpline.SetAll(oldPartPoints, oldPartTangents, ourSpline);
                                newSpline.SetAll(newPartPoints, newPartTangents, ourSpline);

                                nodeIndex--; // Skip an extra one, or it'd be possible to make length-one splines after splitting

                                return true;
                            }
                            return false;
                        }
                    }
                }
            }
        }

        // Find all train track pieces again, since we may have new ones now
        trainTrackSplines = new List<TrainTrackSpline>( GameObject.FindObjectsOfType<TrainTrackSpline>());

        int id = 0;

        // Now look at the end nodes of every section and see if they connect to an end node on any other section
        foreach (TrainTrackSpline ourSpline in trainTrackSplines)
        {
            ourSpline.id = id++;
            // Note: You might think checking our end and every other segment's start would
            // be enough, but consider a piece of track that's rotated 180 degrees. We
            // can't guarantee the "direction" of a track segment matches another
            Vector3 ourStartPos = ourSpline.GetStartPointWorld();
            Vector3 ourEndPos = ourSpline.GetEndPointWorld();
            Vector3 ourStartTangent = ourSpline.GetStartTangentWorld();
            Vector3 ourEndTangent = ourSpline.GetEndTangentWorld();

            if (NodesConnect(ourStartPos, ourEndPos, ourStartTangent, ourEndTangent))
            {
                // This track piece is a loop with itself!
                ourSpline.AddTrackConnection(ourSpline, TrackPosition.Next, TrackOrientation.Same);
                ourSpline.AddTrackConnection(ourSpline, TrackPosition.Prev, TrackOrientation.Same);
                continue;
            }

            foreach (TrainTrackSpline otherSpline in trainTrackSplines)
            {
                if (ourSpline == otherSpline)
                    continue;

                Vector3 theirStartPos = otherSpline.GetStartPointWorld();
                Vector3 theirEndPos = otherSpline.GetEndPointWorld();
                Vector3 theirStartTangent = otherSpline.GetStartTangentWorld();
                Vector3 theirEndTangent = otherSpline.GetEndTangentWorld();

                // Try all possible start/end connections
                if (!CompareNodes(false, true))
                {
                    if (!CompareNodes(false, false))
                    {
                        if (!CompareNodes(true, true))
                        {
                            CompareNodes(true, false);
                        }
                    }
                }

                bool CompareNodes(bool ourStart, bool theirStart)
                {
                    Vector3 ourPos = ourStart ? ourStartPos : ourEndPos;
                    Vector3 ourTangent = ourStart ? ourStartTangent : ourEndTangent;
                    Vector3 theirPos = theirStart ? theirStartPos : theirEndPos;
                    Vector3 theirTangent = theirStart ? theirStartTangent : theirEndTangent;

                    // For start-to-end, the tangents should match, but for end-to-end or start-to-start, they should be opposite
                    if (ourStart == theirStart)
                    {
                        theirTangent *= -1;
                    }

                    if (NodesConnect(ourPos, theirPos, ourTangent, theirTangent))
                    {
                        if (ourStart)
                        {
                            ourSpline.AddTrackConnection(otherSpline, TrackPosition.Prev, theirStart ? TrackOrientation.Reverse : TrackOrientation.Same);
                        }
                        else
                        {
                            ourSpline.AddTrackConnection(otherSpline, TrackPosition.Next, theirStart ? TrackOrientation.Same : TrackOrientation.Reverse);
                        }

                        if (theirStart)
                        {
                            otherSpline.AddTrackConnection(ourSpline, TrackPosition.Prev, ourStart ? TrackOrientation.Reverse : TrackOrientation.Same);
                        }
                        else
                        {
                            otherSpline.AddTrackConnection(ourSpline, TrackPosition.Next, ourStart ? TrackOrientation.Same : TrackOrientation.Reverse);
                        }

                        return true;
                    }
                    return false;
                }
            }
        }

        bool NodesConnect(Vector3 ourPos, Vector3 theirPos, Vector3 ourTangent, Vector3 theirTangent)
        {
            return Vector3.SqrMagnitude(ourPos - theirPos) < MAX_NODE_DIST_SQR && Vector3.Angle(ourTangent, theirTangent) < MAX_NODE_ANGLE;
        }
    }
#else
	public override void Process(uint seed)
	{
		// Don't need this on Client
	}
#endif
}
