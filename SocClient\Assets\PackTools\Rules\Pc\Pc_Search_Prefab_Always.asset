%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 366a9d9ae8674b94a2377bf0bbf01df8, type: 3}
  m_Name: Pc_Search_Prefab_Always
  m_EditorClassIdentifier: 
  desc: "\u6C38\u8FDC\u4F1A\u6253\u5305\u7684\u8D44\u4EA7"
  orMatchMode: 1
  regexList:
  - matchingType: 0
    configStr: Assets/DevRes/Char/NPC/Humanoid/Scientist/LV1/Prefabs/NPC_Scientist_Male_LV1_Tp.prefab
    regexList: []
    regex: 
  - matchingType: 0
    configStr: Assets/DevRes/Char/CharAnimator/Mannequin/Era03H_Male0000/Era03H_Male0000_Tp@era03h_male0000_tp/Prefabs/BM_Male_Lv1_Tp.prefab
    regexList: []
    regex: 
  - matchingType: 0
    configStr: Assets/SocClientRes/Env/Prop/water_drum_a.prefab
    regexList: []
    regex: 
  - matchingType: 0
    configStr: Assets/SocClientRes/Env/Prop/water_junk_pile_a_no_planks.prefab
    regexList: []
    regex: 
  - matchingType: 0
    configStr: Assets/SocClientRes/Env/Nature/Plants/_Runtime/Shrub/nature/Prefabs/bush_willow_snow_c.prefab
    regexList: []
    regex: 
  - matchingType: 0
    configStr: Assets/SocClientRes/Env/Prop/gates_external_high_wood.prefab
    regexList: []
    regex: 
  - matchingType: 0
    configStr: Assets/SocClientRes/Env/Prop/compact_car_c.prefab
    regexList: []
    regex: 
  - matchingType: 0
    configStr: Assets/SocClientRes/Env/Prop/pickup_truck_d_rusty.prefab
    regexList: []
    regex: 
  - matchingType: 0
    configStr: Assets/SocClientRes/Env/Prop/sedan_a_junkyard.prefab
    regexList: []
    regex: 
  - matchingType: 0
    configStr: Assets/SocClientRes/Env/Prop/compact_car_d.prefab
    regexList: []
    regex: 
  - matchingType: 0
    configStr: Assets/SocClientRes/Env/Prop/sedan_a_wrecked.prefab
    regexList: []
    regex: 
  - matchingType: 0
    configStr: Assets/SocClientRes/Env/Prop/van_d_rusty.prefab
    regexList: []
    regex: 
  - matchingType: 0
    configStr: Assets/SocClientRes/Env/Prop/van_f.prefab
    regexList: []
    regex: 
  - matchingType: 0
    configStr: Assets/SocClientRes/Env/Prop/pickup_truck_b_rusty.prefab
    regexList: []
    regex: 
  - matchingType: 0
    configStr: Assets/SocClientRes/Env/Prop/pickup_truck_c_rusty.prefab
    regexList: []
    regex: 
  - matchingType: 0
    configStr: Assets/SocClientRes/Env/Prop/bollard_c.prefab
    regexList: []
    regex: 
  whiteList: {fileID: 0}
