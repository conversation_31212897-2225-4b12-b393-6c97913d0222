using Cysharp.Text;
using FairyGUI;
using FairyGUI.Utils;
using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using WizardGames.Soc.Common.Data.common;
using WizardGames.Soc.Common.Data.resource;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Loader.Logger;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.SDK;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Ui.Binder.CommonGlobal;
using WizardGames.Soc.SocClient.Ui.Utils;

#if POCO
using Cysharp.Text;
#endif

namespace WizardGames.Soc.SocClient.Ui
{
    public class BottomButtonData
    {
        /// <summary>
        /// TbHotKeyGuide 表中的ID
        /// </summary>
        public int Id;
        /// <summary>
        /// 自定义标题
        /// </summary>
        public string Title;
        public EHotKeyLayer Group = EHotKeyLayer.ScreenUI;
        public EventCallback1 Action;
        public EResponseLevel ResponseLevel = EResponseLevel.OnUi;
    }

    public class CurrencyInfo
    {
        public string Icon;
        //TODO 细化成 Value MaxValue Color等属性
        public Func<string> TextFunc;
        public EventCallback1 OnAddButtonClicked;
        public EventCallback0 OnShowTips;
        public EventCallback0 OnHideTips;
        public Func<bool> VisibleFunc;
#if POCO
        public string PocoKey;
#endif
    }

    //跳转类型枚举，对应货币栏配置resourceIDArray第二个参数
    public enum ECurrencyJumpType
    {
        None = 0,//无
        Exchange = 1,//货币兑换
        OpenUI = 2,//跳转界面
    }

    /// <summary>
    /// 界面顶部title、返回按钮、货币等
    /// 注意：必须调用OnEnable，OnDisable使功能生效
    /// </summary>
    public partial class ComTopBar : GLabel
    {
        protected Controller navTabStyleCtrl;
        protected GLabel titleTextField;
        protected Controller titleCtrl;
        protected Controller coinTextVisible;
        protected GButton tipsButton;
        protected bool backButtonVisible = true;
        protected GButton backButton;
        protected Controller btnTipStyleCtrl;
        protected GLoader firstNavTab;
        protected GLoader secondNavTab;
        protected GList coinList;
        public GList CoinList => coinList;
        protected GTextField coinText;
        private bool isEnabled = false;
        private long timerId;

        private GButton homeBackBtn;
        private Controller showHomeBtnCtrl;

        private GButton reportBtn;
        private Controller reportBtnCtrl;

        //客服功能按钮
        private GButton serviceBtn;
        private Controller showServiceBtnCtrl;

        //日志上报按钮
        private GButton logBtn;
        private Controller showLogBtnCtrl;

        //制造界面科技等级
        public Controller TechLevelCtrl;
        public GTextField TechTipText;
        public GButton TechTipBtn;
        public GLoader TechIcon;
        public Controller TechIconCtrl;

        #region 势力等级信息
        protected GComponent forceLevel;
        protected GImage forceLevelProgress;
        protected GTextField forceName;
        protected GTextField forceLevelText;
        protected GTextField forceLevelExp;
        #endregion

        private CurrencyIDsVector3[] currencyIDsVector3Array;//货币栏配置表的resourceIDArray
        private bool isCurrencyAutoSetByConfig = true; //是否读配置表数据,默认是true
        public GList FirstNavList { get; private set; }
        public GList SecondNavList { get; private set; }

        private ComTopBarBinder rootBinder;
#if POCO
        public string backBtnPocoKey;
#endif
        /// <summary>
        /// 在代码中设置的文本，能提取到标题专用字体
        /// 不要用GLable.title
        /// </summary>
        public string Title
        {
            set
            {
                titleTextField.text = value;
                titleCtrl.selectedIndex = string.IsNullOrEmpty(value) ? 1 : 0;
            }
        }
        
        public string CoinText
        {
            set
            {
                coinText.text = value;
                coinTextVisible.selectedIndex = 1;
            }
        }

        private List<CurrencyInfo> currencyDatas;
        
        private ComBinderDictionary<ComTopBar_CoinListItemBinder> currencyBinders = new(c => new(c));
        private ComBinderDictionary<ToolTipsBinder> toolTipsBinders = new(c => new(c));
        
        private static SocLogger logger { get; } = LogHelper.GetLogger("ComTopBar");

        public override void ConstructFromXML(XML xml)
        {
            base.ConstructFromXML(xml);

            rootBinder = new ComTopBarBinder(this);

            navTabStyleCtrl = rootBinder.CtrlNavTabStyle;
            titleTextField = rootBinder.Title;
            titleCtrl = rootBinder.CtrlTitleVisible;
            coinTextVisible = rootBinder.RightList.CtrlCoinTextVisible;
            coinTextVisible.selectedIndex = 0;
            backButton = rootBinder.BtnBack;
            firstNavTab = rootBinder.FirstNavTab;
            secondNavTab = rootBinder.SecondNavTab;
            coinList = rootBinder.RightList.CoinSlot.component.GetChild("coinList").asList;
            coinList.itemRenderer = coinItemRenderer;
            //coinList.ResizeToFit(coinList.numItems);
            coinText = rootBinder.RightList.CoinTextSlot.component.GetChild("title").asTextField;
            forceLevel = rootBinder.RightList.PowerLevelSlot.component;
            forceLevelProgress = forceLevel?.GetChild("bar").asImage;
            forceLevelText = forceLevel?.GetChild("level").asTextField;
            forceName = forceLevel?.GetChild("title").asTextField;
            forceLevelExp = forceLevel?.GetChild("exp").asTextField;


            firstNavTab.onSizeChanged.Set(SetHotKeyPosition);

            homeBackBtn = rootBinder.RightList.HomeBtnSlot.component.asButton;
            showHomeBtnCtrl = rootBinder.RightList.CtrlShowHomeBtn;
            homeBackBtn.onClick.Set(OnHomeButtonClicked);

            reportBtn = rootBinder.RightList.ReportBtnSlot.component.asButton;
            reportBtnCtrl = rootBinder.RightList.CtrlShowReportBtn;
            reportBtn.onChanged.Set(OnReportBtnClicked);

            serviceBtn = rootBinder.RightList.ServiceBtnSlot.component.asButton;
            showServiceBtnCtrl = rootBinder.RightList.CtrlBtnServiceVisible;
            serviceBtn.SetSoundClick(Mc.Tables.TbUiAudio.UniversalButton, OnClickService);

            logBtn = rootBinder.RightList.BtnLogSlot.component.asButton;
            showLogBtnCtrl = rootBinder.RightList.CtrlBtnLogVisible;
            logBtn.SetSoundClick(Mc.Tables.TbUiAudio.UniversalButton, OnClickLogBtn);

            TechLevelCtrl = rootBinder.RightList.CtrlTechLevel;
            TechLevelCtrl.selectedIndex = 0;
            TechTipText = rootBinder.RightList.TechLevel.component.GetChild("techTipText").asTextField;
            TechTipBtn = rootBinder.RightList.TechLevel.component.GetChild("btnJump").asButton;
            TechIcon = rootBinder.RightList.TechLevel.component.GetChild("techIcon").asLoader;
            TechIconCtrl = rootBinder.RightList.TechLevel.component.GetController("techLevel");

            InitForPlatform();

#if POCO
            if(!string.IsNullOrEmpty(backBtnPocoKey))
            {
                backButton.PocoRegister(backBtnPocoKey);
            }
#endif
        }
        /// <summary>
        /// 举报按钮
        /// </summary>
        /// <param name="context"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void OnReportBtnClicked(EventContext context)
        {
            Mc.Msg.FireMsg(EventDefine.TopBarReportSelect, reportBtn.selected);
        }


        /// <summary>
        /// 根据平台获取_titleObject
        /// </summary>
        /// <param name="buffer"></param>
        protected override void ConstructExtension(ByteBuffer buffer)
        {
            base.ConstructExtension(buffer);
            _titleObject = GetChild("title");
        }
        public override void Setup_AfterAdd(ByteBuffer buffer, int beginPos)
        {
            base.Setup_AfterAdd(buffer, beginPos);
            // fgui上直接设置tab样式的情况
            var firstTabValue = firstNavTab.url;
            var secondTabValue = secondNavTab.url;
            if (!string.IsNullOrEmpty(secondTabValue))
                navTabStyleCtrl.selectedIndex = 2;
            else if (!string.IsNullOrEmpty(firstTabValue))
                navTabStyleCtrl.selectedIndex = 1;
            else
                navTabStyleCtrl.selectedIndex = 0;

            var titleValue = titleTextField.text;
            titleCtrl.selectedIndex = string.IsNullOrEmpty(titleValue) ? 1 : 0;
            SetupForPlatform();
        }

        public void InitCurrencyList(string uiName)
        {
            if (!uiName.Equals(string.Empty))
            {
                var config = Mc.Tables.TBConfigCurrencyBar.GetOrDefault(uiName);
                if (config != null && config.IsAuto)
                {
                    currencyIDsVector3Array = config.ResourceIDArray;
                    RefreshCurrency();
                }
            }
        }

        private void OnHomeButtonClicked()
        {
            Mc.Ui.PlaySocAudio("UI_Click_02");
            Mc.Ui.PopFullScreenWinStackToBottomWin();
        }

        private void OnClickLogBtn(EventContext context)
        {
            //TGPA本地任务不支持上传目录，遍历上传单个log文件
            string curLogFullPath = LogCollector.Instance.logFileFullPath;
            try
            {
                string logDir = Path.GetDirectoryName(curLogFullPath);
                if (Directory.Exists(logDir))
                {
                    var files = new DirectoryInfo(logDir).GetFiles();
                    Array.Sort(files, (x, y) => y.CreationTime.CompareTo(x.CreationTime));
                    for (int i = 0; i < TGPAWrapper.MaxLogUploadNum; i++)
                    {
                        TGPAWrapper.UploadLogFile(files[i].FullName);
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error($"[UpLoadLog] Error uploading log files: {ex.Message}");
            }
            finally
            {
                TGPAWrapper.UploadLogFile(curLogFullPath);
            }
            Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.LogUploadDone);
        }
        private void OnClickService(EventContext context)
        {
            HyperlinkUtil.OpenUrl(Mc.Tables.TbGlobalConfig.PrivacyPolicy);
        }
        public void RefreshServiceButton(bool isShow = false)
        {
            showServiceBtnCtrl.selectedIndex = isShow ? 1 : 0;
        }

        public void RefreshReportButton(bool isShow = false)
        {
            reportBtnCtrl.selectedIndex = isShow ? 1 : 0;
        }

        public void RefreshTechLevel(bool isShow = false)
        {
            TechLevelCtrl.selectedIndex = isShow ? 1 : 0;
        }

        public void SetReportButton(bool selected)
        {
            reportBtn.selected = selected;
        }

        public void RefreshLogButton(bool isShow = false)
        {
            showLogBtnCtrl.selectedIndex = isShow ? 1 : 0;
        }

        private class ToolTipsBinder : IToolTips
        {
            public GComponent Target;
            private CurrencyInfo data;
            private static long delayTimer;
            public CurrencyInfo Data
            {
                get => data;
                set
                {
                    data = value;
                    objGenaral = null;
                }
            }
            private OBJGenaral objGenaral;
            public OBJGenaral ObjGenaral
            {
                get => objGenaral;
                set
                {
                    objGenaral = value;
                    data = null;
                }
            }

            public ToolTipsBinder(GComponent target)
            {
                Target = target;
            }

            public void ShowTips(EventContext context)
            {
                if (delayTimer > 0)
                {
                    Mc.TimerWheel.CancelTimer(delayTimer);
                    delayTimer = 0;
                }

                //延时显示tips
                if (Mc.Tables.TbGlobalConfig.GetComTopBarCurrencyTipsDelay() > 0)
                {
                    delayTimer = Mc.TimerWheel.AddTimerOnce(Mc.Tables.TbGlobalConfig.GetComTopBarCurrencyTipsDelay(),  (_,_,_) =>
                    {
                        if (objGenaral != null)
                            UiItemTips.ShowTips(objGenaral, Target.LocalToGlobal(Vector2.zero), true, true, true);
                        else data?.OnShowTips?.Invoke();
                        delayTimer = 0;
                    });
                    return;
                }

                if (objGenaral != null)
                    UiItemTips.ShowTips(objGenaral, Target.LocalToGlobal(Vector2.zero), true, true, true);
                else data?.OnShowTips?.Invoke();
            }

            public void HideTips()
            {
                if (delayTimer > 0)
                {
                    Mc.TimerWheel.CancelTimer(delayTimer);
                    delayTimer = 0;
                }
                if (objGenaral != null) UiItemTips.HideTips();
                else data?.OnHideTips?.Invoke();
            }
        }
        private void coinItemRenderer(int index, GObject item)
        {
            var itemBinder = currencyBinders.Get(item);
            if (currencyDatas != null && currencyDatas.Count > index) //这个一般用在局内的道具显示
            {
                var data = currencyDatas[index];
                item.visible = data.VisibleFunc != null ? data.VisibleFunc.Invoke() : true;
                if (!item.visible) return;
                itemBinder.Icon.url = data.Icon;
                itemBinder.Num.text = data.TextFunc != null ? data.TextFunc.Invoke() : "";
                itemBinder.CtrlShowAdd.selectedIndex = data.OnAddButtonClicked != null ? 1 : 0;
                itemBinder.BtnAdd.onClick.Set(data.OnAddButtonClicked);
#if POCO
                    if (!string.IsNullOrEmpty(data.PocoKey))
                    {
                        itemBinder.Num.PocoRegister(data.PocoKey);
                    }
#endif
                var toolTips = toolTipsBinders.Get(item);
                toolTips.Data = data;
                SafeUtil.SafeSetTooltips(item.asCom, toolTips, true);
            }
            else //这个走货币栏表读取显示
            {
                if (currencyIDsVector3Array == null || currencyIDsVector3Array.Length <= index)
                {
                    logger.ErrorFormat("货币栏配置表的resourceIDArray长度不足, index:{0}, length:{1}", index, currencyIDsVector3Array.Length);
                    return;
                }

                var jumpType = currencyIDsVector3Array[index].JumpType;
                var jumpID = currencyIDsVector3Array[index].JumpID;
                var currencyId = currencyIDsVector3Array[index].ResourceID;
                itemBinder.CtrlShowAdd.selectedIndex = jumpType == (int)ECurrencyJumpType.None ? 0 : 1;
                itemBinder.BtnAdd.onClick.Set((context) =>
                {
                    if (jumpType == (int)ECurrencyJumpType.OpenUI)
                    {
                        Mc.Ui.JumpToOpenUI(jumpID);
                    }
                    else if (jumpType == (int)ECurrencyJumpType.Exchange)
                    {
                        UiCurrencyExchange.Open(currencyId);
                    }

                    context.StopPropagation();
                });
                var amount = Mc.Config.GetCurrencyAmountById(currencyId);
                var objGenaral = Mc.Tables.TBGeneral.GetOrDefault(currencyId);
                if (objGenaral != null)
                {
                    itemBinder.Icon.url = objGenaral.Icon;
                }
                itemBinder.Num.text = CommonNumberFormatUtils.CurrencyFormatNumber(amount);
                var toolTips = toolTipsBinders.Get(item);
                toolTips.ObjGenaral = objGenaral;
                SafeUtil.SafeSetTooltips(item.asCom, toolTips, true);
            }
        }

        public void RefreshCurrencyInfoByConfig()
        {
            coinList.numItems = currencyIDsVector3Array?.Length ?? 0;
            coinList.ResizeToFit(coinList.numItems);
        }

        /// <summary>
        /// 设置货币数据,作为主动设置货币数据的接口。
        /// </summary>
        /// <param name="currencyDatas"></param>
        public void SetCurrency(List<CurrencyInfo> currencyDatas)
        {
            this.currencyDatas = currencyDatas;
            isCurrencyAutoSetByConfig = false;
            RefreshCurrency();
        }

        /// <summary>
        /// 刷新货币显示
        /// </summary>
        /// <returns></returns>
        public void RefreshCurrency()
        {
            coinList.visible = true;
            if (isCurrencyAutoSetByConfig)
            {
                RefreshCurrencyInfoByConfig();
            }
            else
            {
                coinList.numItems = currencyDatas?.Count ?? 0;
                //list需要手动触发计算尺寸
                coinList.ResizeToFit(coinList.numItems);
            }

            var showIndex = coinList.numItems > 0 ? 1 : 0;
            rootBinder.RightList.CtrlCoinVisible.selectedIndex = showIndex;

            var loader = rootBinder.RightList.CoinSlot;
            loader.autoSize = false;
            if (showIndex > 0)
            {
                // 调整大小
                coinList.parent.SetSize(coinList.width, coinList.height);
                // 通知父级 Group 重新布局
                coinList.parent.SetBoundsChangedFlag();
                loader.SetSize(coinList.width, coinList.height);
            }
        }

        public void HideCurrency()
        {
            rootBinder.RightList.CtrlCoinVisible.selectedIndex = 0;
            coinList.visible = false;
            coinList.numItems = 0;
        }
        /// <summary>
        /// 触发返回按钮点击事件
        /// </summary>
        public void FireBackButtonClick()
        {
            if(backButtonVisible)
                backButton.FireClick(false,true);
        }
        
        public void OnEnable()
        {
            isEnabled = true;
            RegisterEvents();
            OnEnableImpl();
            RefreshCurrency();
        }
        public void OnDisable()
        {
            isEnabled = false;
            UnRegisterEvents();
            OnDisableImpl();
        }

        private void RegisterEvents()
        {
            Mc.Msg.AddListener(EventDefine.LayerComStackCoverUpdate, RefreshHomeButton);
            Mc.Msg.AddListener(EventDefine.OnCurrencyUpdate,RefreshCurrency);

        }
        private void UnRegisterEvents()
        {
            Mc.Msg.RemoveListener(EventDefine.OnCurrencyUpdate, RefreshCurrency);
            Mc.Msg.RemoveListener(EventDefine.LayerComStackCoverUpdate, RefreshHomeButton);
        }

        public void RefreshTime(long timerId, object data, bool needDelete)
        {
            // 获取当前时间
            DateTime currentTime = DateTime.Now;
            var resetTime = Mc.Tables.TbGlobalConfig.DailyRewardLimitResetTime;
            // 设置目标时间为今天的 4:00:00
            DateTime targetTime = new DateTime(currentTime.Year, currentTime.Month, currentTime.Day, resetTime, 0, 0);

            // 如果当前时间已经超过 4:00:00，则计算到明天的同一时间
            if (currentTime > targetTime)
            {
                targetTime = targetTime.AddDays(1); // 将目标时间移到明天的 4:00:00
            }

            // 计算剩余时间
            TimeSpan timeRemaining = targetTime - currentTime;
            coinText.text = ZString.Format(LanguageManager.GetTextConst(LanguageConst.TimeAfterNextClue), timeRemaining.Hours, timeRemaining.Minutes, timeRemaining.Seconds, 1);
        }
      
        /// <summary>
        /// 隐藏tab
        /// </summary>
        public virtual void HideTab()
        {
            SetNavTabStyle(0);
            firstNavTab.url = null;
            secondNavTab.url = null;
        }

        public void ShowForceLevel(int forceId, int level, int exp)
        {
            var cfg = Mc.Tables.TBMainTaskForce.GetOrDefault(forceId);
            var levelCfg = Mc.Tables.TBMainTaskForceLevel.GetOrDefault(forceId);
            if (forceLevel != null && cfg != null && levelCfg != null)
            {
                rootBinder.RightList.CtrlPowerLevelVisble.selectedIndex = 1;
                forceName.text = cfg.Name;
                forceLevelText.text = level.ToString();
                //level初始从1开始
                if (levelCfg.ForceLevelExps.Count > level)
                {
                    //取下一级经验
                    float maxExp = levelCfg.ForceLevelExps[level].Exp;
                    forceLevelProgress.visible = true;
                    forceLevelProgress.fillAmount = exp / maxExp;
                    forceLevelExp.text = exp + "/" + maxExp;
                }
                else
                {
                    //满级
                    forceLevelProgress.visible = false;
                    forceLevelExp.text = LanguageManager.GetTextConst(LanguageConst.CabinetMaxLevel);
                }
            }
        }

        public void HideForceLevel()
        {
            rootBinder.RightList.CtrlPowerLevelVisble.selectedIndex = 0;
        }
        public override void Dispose()
        {
            firstNavTab.onSizeChanged.Clear();
            backButton.onClick.Clear();
            base.Dispose();
            UnRegisterEvents();
        }
    }
}