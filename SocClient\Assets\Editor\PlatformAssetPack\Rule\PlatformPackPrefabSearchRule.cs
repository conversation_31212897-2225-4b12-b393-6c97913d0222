﻿using Sirenix.OdinInspector;
using Soc.Common.Unity.PlatformResOpt;
using System;
using System.IO;
using UnityEditor;
using UnityEditor.Presets;
using Object = UnityEngine.Object;

namespace PackTool
{
    //搜索规则通用基类
    public abstract class PlatformPackPrefabSearchRule :PlatformPackRule
    {
        [LabelText("资产路径匹配规则")]
        public AssetSearchConf[] AssetSearchRules = new AssetSearchConf[0];

        [LabelText("子规则(主资产成功之后会执行的规则)")]
        public PlatformPackPrefabSearchRule SubRule;
        
        [LabelText("支持的阶段")]
        public EPlatformProcessStage[] SupportStages = new EPlatformProcessStage[]
        {
            EPlatformProcessStage.EditorRuntime,
            EPlatformProcessStage.EditorPreview,
            EPlatformProcessStage.CI_Product,
            EPlatformProcessStage.CI_Apply
        };

        public override void PostProcessSuccessfully(PackInputParams packInputParams)
        {
            base.PostProcessSuccessfully(packInputParams);

            TrySaveAsset(packInputParams);
        }

        public static void ErrorFormat(PlatformPackContext context,string format)
        {
            StepOptimizePlatformRes.logger.Error(format);

            //收集错误信息
            context.ErrorList.Add(format);
        }
        
        public static void ErrorFormat<T1>(PlatformPackContext context,string format, T1 args)
        {
            StepOptimizePlatformRes.logger.ErrorFormat(format,args);

            //收集错误信息
            context.ErrorList.Add(string.Format(format, args));
        }
        
        public static void ErrorFormat<T1,T2>(PlatformPackContext context,string format, T1 args,T2 arg2)
        {
            StepOptimizePlatformRes.logger.ErrorFormat(format,args,arg2);

            //收集错误信息
            context.ErrorList.Add(string.Format(format, args,arg2));

        }
        
        public static void ErrorFormat<T1,T2,T3>(PlatformPackContext context,string format, T1 args,T2 arg2,T3 arg3)
        {
            StepOptimizePlatformRes.logger.ErrorFormat(format,args,arg2,arg3);

            //收集错误信息
            context.ErrorList.Add(string.Format(format, args,arg2,arg3));
        }
        
        public static void ErrorFormat<T1,T2,T3, T4>(PlatformPackContext context,string format, T1 args,T2 arg2,T3 arg3,T4 arg4)
        {
            StepOptimizePlatformRes.logger.ErrorFormat(format,args,arg2,arg3,arg4);

            //收集错误信息
            context.ErrorList.Add(string.Format(format, args,arg2,arg3,arg4));

        }

        public override int Execute(PackInputParams packInputParams)
        {
            var code = ExecuteMainRule(packInputParams);
            if (code != (int)EPlatformErrorCode.Suc)
            {
                return code;
            }
            //执行子规则
            return ExecuteSubRule(packInputParams);
        }
        
        public abstract int ExecuteMainRule(PackInputParams packInputParams);

        protected  int ExecuteSubRule(PackInputParams packInputParams)
        {
            if (SubRule == null)
            {
                return (int)EPlatformErrorCode.Suc; 
            }

            if (SubRule.ShouldExecute(packInputParams))
            {
                return SubRule.Execute(packInputParams);
            }
            else
            {
                return (int)EPlatformErrorCode.Suc;
            }
        }
        
        public override int TryApplyCache(PackInputParams packInputParams)
        {
            if (!ShouldExecute(packInputParams))
            {
                return (int)EPlatformErrorCode.ContinueCode;
            }
            
            if (packInputParams.ShouldReadPlatformCache())
            {
                //处理成功，则返回100，break后续rule阶段
                if (ApplyCache(packInputParams,out PlatformPackBuildFileCache cache))
                {
                    StepOptimizePlatformRes.logger.InfoFormat("应用缓存成功:{0}", packInputParams.processSourcePath);
                    return (int)EPlatformErrorCode.Suc;
                }
                else
                {
                    //应用阶段，失败也跳出
                    if (packInputParams.context.processStage == EPlatformProcessStage.CI_Apply)
                    {
                        //如果是当前规则要处理的资产,应用缓存失败了，则认为是一个异常
                        if (cache != null && cache.cacheRuleHash == packInputParams.ruleHash)
                        {
                            ErrorFormat(packInputParams.context,"应用缓存失败:{0}", packInputParams.processSourcePath);
                            return (int)EPlatformErrorCode.ErrorCode;
                        }
                        //否则继续执行
                        else
                        {
                            return (int)EPlatformErrorCode.ContinueCode;
                        }
                    }
                }
            }
            
            return (int)EPlatformErrorCode.ContinueCode;
        }

        protected PlatformPackBuildFileCache TryFindUsableBuildCache(PackInputParams packInputParams)
        {
            return TryFindUsableBuildCache(packInputParams.context, packInputParams.processSourcePath, packInputParams.ruleHash, packInputParams.processDepCaches);
        }

        protected virtual PlatformPackBuildFileCache TryFindUsableBuildCache(PlatformPackContext context,string targetPath,string ruleHash,PlatformPackBuildDepCache processDepCaches)
        {
            //判断缓存是否存在
            if (context.buildCache == null)
            {
                ErrorFormat(context, "打包缓存buildCache不存在 ,查找目标:{0}", targetPath);
                return null;
            }

            //判断平台是否一致
            if (context.buildCache.platform != context.targetPlatform)
            {
                ErrorFormat(context,"打包缓存平台不一致,当前:{0} 目标:{1}", context.buildCache.platform, context.targetPlatform);
                return null;
            }

            if (context.ruleRevision != context.buildCache.revision )
            {
                ErrorFormat(context,"规则版本不一致,当前:{0} 目标:{1}", context.ruleRevision, context.buildCache.revision);
                return null;
            }

            //找到文件缓存
            if (!context.buildCache.files.TryGetValue(targetPath, out var buildFileCache))
            {
                ErrorFormat(context,"<注意>buildCache.files 没有找到缓存:{0} 可能是异常也可能这个文件本身就是符合标准的所以没进入modify列表 ", targetPath);
                return null;
            }

            if (context.svnCache == null)
            {
                ErrorFormat(context,"SVN缓存 svnCache不存在,当前:{0}", targetPath);
                return null;
            }

            //找到文件的svn版本信息
            if (context.svnCache.files.TryGetValue(targetPath, out var svnInfo) == false)
            {
                ErrorFormat(context,"<注意>SVN缓存不存在于svnCache.files,当前:{0}", targetPath);
                return null;
            }
            
            //判断路径是否一致
            PlatformPackAssetInfo cmpInfo = new PlatformPackAssetInfo();
            cmpInfo.sourceUnityPath = svnInfo.unityPath;
            cmpInfo.sourceFilePath = svnInfo.path;
            cmpInfo.sourceRevision = svnInfo.revision;
            cmpInfo.sourceAssetGuid = svnInfo.assetGuid;

            if (!PlatformPackUtils.Compare(cmpInfo, buildFileCache.AssetInfo))
            {
                ErrorFormat(context,"<注意>路径不一致,缓存应用跳过,当前:{0} 版本信息:{1} 缓存:{2}",targetPath, cmpInfo, buildFileCache.AssetInfo);
                return null;
            }

            //TODO 处理，release需要合并
            //有依赖的话，就比较下依赖
            if (context.processStage != EPlatformProcessStage.CI_Apply)
            {
                //不是同一个规则产生的
                if (buildFileCache.cacheRuleHash != ruleHash)
                {
                    StepOptimizePlatformRes.logger.InfoFormat("<注意>不是当前规则产出的资产,当前:{0} 目标:{1}", buildFileCache.cacheRuleHash, ruleHash);
                    return null;
                }
                
                if (processDepCaches != null)
                {
                    if (!PlatformPackUtils.Compare(processDepCaches.dependencyList,
                            buildFileCache.dependencies.dependencyList))
                    {
                        ErrorFormat(context,"<注意>依赖不一致,当前:{0} 依赖:{1} 缓存:{2}", targetPath, processDepCaches.getPrintableStr(), buildFileCache.dependencies.getPrintableStr());
                
                        return null;
                    }
                }
            }

            return buildFileCache;
        }
        
        protected bool ApplyCache(PackInputParams packInputParams,out PlatformPackBuildFileCache finalCache)
        {
            finalCache = null;
            //是否可以读取缓存
            if (packInputParams.ShouldReadPlatformCache())
            {
                var context = packInputParams.context;
                PlatformPackBuildFileCache usableBuildCache = TryFindUsableBuildCache(packInputParams);
                if (usableBuildCache != null)
                {
                    if (!File.Exists(usableBuildCache.cacheFilePath))
                    {
                        ErrorFormat(context,"<注意>缓存文件不存在 :{0} 当前目标 :{1}", usableBuildCache, packInputParams.processSourcePath);
                        return false;
                    }
                    
                    //如果是运行时的话
                    if (!CopyCache(packInputParams, usableBuildCache))
                    {
                        return false;
                    }

                    finalCache = usableBuildCache;
                    return true;
                }
                else
                {
                    StepOptimizePlatformRes.logger.ErrorFormat("<注意>没有找到缓存:{0} ", packInputParams.processSourcePath);
                }
            }
            else
            {
                StepOptimizePlatformRes.logger.InfoFormat("当前stage 跳过读取缓存:{0} asset:{1}",packInputParams.context.processStage, packInputParams.processSourcePath);
            }

            return false;
        }

        protected bool CopyCache(PackInputParams packInputParams, PlatformPackBuildFileCache usableBuildCache)
        {
            return CopyCache(packInputParams.context, usableBuildCache,packInputParams.processSourcePath, packInputParams.assetTarget);
        }

        protected bool CopyCache(PlatformPackContext context, PlatformPackBuildFileCache usableBuildCache,string targetPath,Object target)
        {
            if (context == null || usableBuildCache == null)
            {
                ErrorFormat(context,"CopyCache 参数错误 context:{0} usableBuildCache:{1}", context, usableBuildCache);
                return false;
            }
            
            if (context.processStage == EPlatformProcessStage.EditorRuntime)
            {
                var loadAsset = AssetDatabase.LoadAssetAtPath<Object>(usableBuildCache.cacheFilePath);
                if (loadAsset != null)
                {
                    EditorUtility.CopySerialized(loadAsset, target);
                    StepOptimizePlatformRes.logger.InfoFormat("使用缓存:{0} ", usableBuildCache.cacheFilePath);
                }
                else
                {
                    ErrorFormat(context,"使用缓存失败:{0} ", usableBuildCache.cacheFilePath);
                    return false;
                }
            }
            else
            {
                //ci的话
                if (context.processStage == EPlatformProcessStage.CI_Product)
                {
                    StepOptimizePlatformRes.logger.InfoFormat("CI 拷贝文件从 :{0} 到:{1}", usableBuildCache.cacheFilePath, targetPath);
                    
                    File.Copy(usableBuildCache.cacheFilePath, targetPath, true);
                }
                else
                {
                    StepOptimizePlatformRes.logger.InfoFormat("Editor 拷贝文件从 :{0} 到:{1}", usableBuildCache.cacheFilePath, targetPath);
                    File.Copy(usableBuildCache.cacheFilePath, targetPath, true);
                }
            }

            return true;
        }

        public bool TryRecordDynamicAsset(PackInputParams packInputParams,string dynamicAssetPath,string meshassetPath,Object dynamicAsset)
        {
            if (dynamicAsset == packInputParams.assetTarget)
            {
                StepOptimizePlatformRes.logger.InfoFormat("使用规则:{0}处理动态资源{1}的时候发现动态资源和原始资源相同 ", packInputParams.rulePath,packInputParams.processSourcePath);
                return false;
            }
            
            if (dynamicAssetPath == packInputParams.processSourcePath)
            {
                StepOptimizePlatformRes.logger.InfoFormat("使用规则:{0}处理动态资源{1}���时候发现动态资源和原始资源路径相同 ", packInputParams.rulePath,packInputParams.processSourcePath);
                return false;
            }
            
            var context = packInputParams.context;
            if (packInputParams.ShouldSaveModifyAsset())
            {
                PlatformPackSvnFileInfo svnFileInfo = null;
                if (context.svnCache != null)
                {
                    if (!context.svnCache.files.TryGetValue(meshassetPath, out svnFileInfo))
                    {
                        throw new Exception(string.Format("动态资产的svn信息不存在,当前:{0} 可能引用的是git目录下的资产?", meshassetPath));
                    }
                }
                
                
                PlatformPackDynamicModifyData modifyData = new PlatformPackDynamicModifyData();
                modifyData.modifyObject = dynamicAsset;
                modifyData.sourceFileInfo = svnFileInfo;
                modifyData.dynamicModifyPath = dynamicAssetPath;
                modifyData.dynamicAssetGuid = AssetDatabase.GUIDFromAssetPath(dynamicAssetPath).ToString();
                
                if(modifyData.sourceFileInfo != null && string.IsNullOrEmpty(modifyData.sourceFileInfo.unityPath))
                {
                    ErrorFormat(context,"使用规则:{0}处理动态资源{1}的时候获取svn信息失败 ", packInputParams.rulePath,packInputParams.processSourcePath);
                    return false;
                }
                
                if (string.IsNullOrEmpty(  modifyData.dynamicAssetGuid))
                {
                    ErrorFormat(context,"使用规则:{0}处理动态资源{1}的时候获取动态资源guid失败 dynamicpath:{}", packInputParams.rulePath,packInputParams.processSourcePath,dynamicAssetPath);
                    return false;
                }
                
                if(packInputParams.context.dynamicModifySet.TryGetValue(dynamicAssetPath, out var oldAsset))
                {
                    if (oldAsset.modifyObject != dynamicAsset)
                    { 
                        ErrorFormat(context,"使用规则:{0}处理资源{1}的时候发现资源路径冲突,原有资源:{2} 新资源:{3}", packInputParams.rulePath,packInputParams.processSourcePath,oldAsset.modifyObject,dynamicAsset);
                    }
                }

                if (packInputParams.enableLog)
                {
                    StepOptimizePlatformRes.logger.InfoFormat("使用规则:{0} 为{1}记录生成的动态资源 {2}", packInputParams.rulePath,packInputParams.processSourcePath,dynamicAssetPath);
                }
                
                packInputParams.context.dynamicModifySet[dynamicAssetPath] =  modifyData;
                return true;
            }

            return false;
        }

        public bool TrySaveAsset(PackInputParams packInputParams)
        {
            return TrySaveAsset(packInputParams.context, packInputParams.assetTarget, packInputParams.processSourcePath, packInputParams.processSvnFileInfo,packInputParams.ruleHash);
        }
        
        //尝试标记资产
        public bool TrySaveAsset(PlatformPackContext context,Object assetTarget,string processSourcePath,PlatformPackSvnFileInfo processSvnFileInfo,string ruleHash)
        {
            var sourceAssetPath = processSourcePath;
            if (string.IsNullOrEmpty(sourceAssetPath))
            {
                ErrorFormat(context,"处理资源{0}的时候获取资源路径失败:{1} ", processSourcePath,sourceAssetPath);
                return false;
            }

            if (assetTarget)
            {
                EditorUtility.SetDirty(assetTarget);
            }
            
            if (assetTarget && context.processStage > EPlatformProcessStage.EditorRuntime)
            {
                if(context.modifySet.TryGetValue(sourceAssetPath, out var oldAsset))
                {
                    if (oldAsset.modifyObject != assetTarget)
                    { 
                        ErrorFormat(context,"处理资源{0}的时候发现资源路径冲突,原有资源:{1} 新资源:{2}", processSourcePath,oldAsset.modifyObject,assetTarget);
                        return false;
                    }
                }

                PlatformPackModifyData modifyData = oldAsset;
                if (modifyData == null)
                {
                    modifyData = new PlatformPackModifyData();
                }
                
                modifyData.modifyObject = assetTarget;
                modifyData.sourceFileInfo = processSvnFileInfo;
                modifyData.ruleGuid = ruleHash;
                
                if(modifyData.sourceFileInfo != null && string.IsNullOrEmpty(modifyData.sourceFileInfo.unityPath))
                {
                    ErrorFormat(context,"处理资源{0}的时候获取svn信息失败 ", processSourcePath);
                    return false;
                }
                
                context.modifySet[sourceAssetPath] =  modifyData;
                
                StepOptimizePlatformRes.logger.InfoFormat("{0} 作为修改项将加入到缓存数据中 ", processSourcePath);
                return true;
            }

            return false;
        }
        
        public override bool ShouldExecute(PackInputParams packInputParams)
        {
            if(Array.IndexOf(SupportStages,packInputParams.context.processStage) == -1)
            {
                if(packInputParams.enableLog)
                    StepOptimizePlatformRes.logger.InfoFormat("当前阶段不支持该规则:{0} stage {1}", packInputParams.rulePath,packInputParams.context.processStage);
                return false;
            }

            if (PlatformPackPath.ShouldPlatformPack(packInputParams.processSourcePath) == false)
            {
                return false;
            }

            var context = packInputParams.context;
            var buildAssetPath = packInputParams.processSourcePath;
            if (string.IsNullOrEmpty(buildAssetPath))
            {
                ErrorFormat(context,"获取资源路径失败:{0} ", buildAssetPath);
                return false;
            }
            
            var assetTarget = packInputParams.assetTarget;
            if (assetTarget == null)
            {
                ErrorFormat(context,"获取资源失败:{0} ", buildAssetPath);
                return false;
            }

            //AssetSearchRules 为空的时候，就默认通过，
            //如果有的话，就挨个判断是否符合规则
            bool valid = AssetSearchRules.Length == 0;
            foreach (var SearchRule in AssetSearchRules)
            {
                if (AssetSearchConf.ConfigIsValid(SearchRule, buildAssetPath))
                {
                    valid = true;
                    break;
                }
            }

            if (!valid)
            {
                if(packInputParams.enableLog)
                    StepOptimizePlatformRes.logger.InfoFormat("资源{0}不符合搜索规则 SearchRule:{1}", buildAssetPath ,desc);
                return false;
            }

            if(packInputParams.enableLog)
                StepOptimizePlatformRes.logger.InfoFormat("资源{0} 符合规则 {1} ", buildAssetPath,desc);
            
            return true;
        }

        //判断动态缓存资产是否有效
        public bool IsCachedDynamicResValid(PackInputParams packInputParams,string dynamicPath,string mainAssetPath)
        {
            if (File.Exists(dynamicPath) == false)
            {
                return false;
            }
            
            //再从当前的动态集合中查找一下
            if(packInputParams.context.dynamicModifySet.ContainsKey(dynamicPath))
            {
                StepOptimizePlatformRes.logger.InfoFormat("动态缓存存在于当前动态集合中,当前路径:{0} 主体资产:{1} ", dynamicPath, mainAssetPath);
                return true;
            }

            var context = packInputParams.context;
            var buildCache = packInputParams.context.buildCache;
            if (buildCache != null)
            {
                StepOptimizePlatformRes.logger.InfoFormat("检查动态缓存是否有效,当前路径:{0} ", dynamicPath);
                if (buildCache.dynamicFiles.TryGetValue(dynamicPath, out var buildFileCache))
                {
                    var guid = AssetDatabase.GUIDFromAssetPath(dynamicPath).ToString();
                    if (buildFileCache.dynamicAssetGuid == guid)
                    {
                        //找到主体资产的缓存和svn缓存
                        PlatformPackSvnFileInfo svnFileInfo = null;
                        if (context.svnCache != null)
                        {
                            context.svnCache.files.TryGetValue(mainAssetPath, out svnFileInfo);
                        }
                        
                        if(svnFileInfo != null)
                        {
                            if (svnFileInfo.revision == buildFileCache.sourceFileRevision && svnFileInfo.unityPath == buildFileCache.sourceUnityPath)
                            {
                                StepOptimizePlatformRes.logger.InfoFormat("动态缓存有效,当前路径:{0} 主体资产:{1} ", dynamicPath, mainAssetPath);
                                return true;
                            }
                            else
                            {
                                ErrorFormat(context,"动态缓存svn版本不一致,当前:{0} 主体资产:{1} svn版本:{2} 缓存版本:{3}", dynamicPath, mainAssetPath,
                                    svnFileInfo.revision, buildFileCache.sourceFileRevision);
                                return false;
                            }
                        }
                        
                        ErrorFormat(context,"动态缓存svnFileInfo 不存在,当前路径:{0} 主体资产:{1} ", dynamicPath, mainAssetPath);
                        return false;
                    }
                    else
                    {
                        //这时候说明存在异常了，guid在工程因为某些原因被修改了
                        throw new Exception(string.Format("动态缓存guid不一致,工程内:{0} 缓存内:{1}", guid,
                            buildFileCache.dynamicAssetGuid));
                    }
                }
                else
                {
                    //这个非常奇怪，文件存在，但是缓存里面不认为它存在，
                    ErrorFormat(context,"动态缓存不存在于缓存配置中,当前:{0}  ", dynamicPath);
                    return false;
                }
            }

            return true;
        }

        
    }
}