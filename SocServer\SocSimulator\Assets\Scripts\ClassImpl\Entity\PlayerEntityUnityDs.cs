﻿using Share.Common.ObjPool;
using SharedUnity;
using Soc.Vehicle.Util;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using WizardGames.Soc.Common.Combat;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Data.DataItem;
using WizardGames.Soc.Common.Data.Play;
using WizardGames.Soc.Common.Entity.Ability;
using WizardGames.Soc.Common.Entity.Interface;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Synchronization;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.Character.State.Event;
using WizardGames.Soc.Common.Unity.Character.Utility;
using WizardGames.Soc.Common.Unity.Config;
using WizardGames.Soc.Common.Unity.Construction;
using WizardGames.Soc.Common.Unity.Extension;
using WizardGames.Soc.Common.Unity.Go;
using WizardGames.Soc.Common.Unity.HeldItem;
using WizardGames.Soc.Common.Unity.Manager;
using WizardGames.Soc.Common.Unity.Monster.Point;
using WizardGames.Soc.Common.Unity.Utility;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Weapon;
using WizardGames.Soc.Procedural;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocAI.Utility;
using WizardGames.Soc.SocLogic.Go;
using WizardGames.Soc.SocSimulator;
using WizardGames.Soc.SocSimulator.Go;
using WizardGames.Soc.SocSimulator.Hit;
using WizardGames.Soc.SocSimulator.Item;
using WizardGames.Soc.SocSimulator.Logic.ShootInfo;
using WizardGames.Soc.SocSimulator.Main;
using WizardGames.Soc.SocSimulator.Manager;
using WizardGames.Soc.SocSimulator.Systems;
using WizardGames.Soc.SocSimulator.Utility;
using DamageRecord = WizardGames.Soc.SocSimulator.Systems.DamageRecord;
using GamePhysics = WizardGames.Soc.Common.Unity.Utility.GamePhysics;


namespace WizardGames.Soc.Common.Entity
{
    public partial class PlayerEntity : IWeaponClipHandle
    {
        private static Dictionary<ulong, long> roleId2PlayerEntity = new();
        private Vector3 shootPointCache = Vector3.zero;
        private long shootPointCacheFrame = 0;

        private GameObject standShootPoint;
        private GameObject crouchShootPoint;

        // public Vector3 NewPosition;

        public float NewRotateY;
        public float NewRotateX;

        // public WizardGames.Soc.Common.Unity.Character.ActionHoldStateCommonBeh commonBeh;
        public PlayerBotComponent BotComp => GetComponent<PlayerBotComponent>((int)EComponentIdEnum.PlayerBotComponentId);
        public PlayerInventoryComponent InventoryComponent => GetComponent<PlayerInventoryComponent>(EComponentIdEnum.PlayerInventoryComponentId);

        public PlayerVehicleComponent ComponentVehicle => GetComponent<PlayerVehicleComponent>(EComponentIdEnum.PlayerVehicleComponent);
        public BuffComponent Buff => GetComponent<BuffComponent>(EComponentIdEnum.BuffComponentId);
        public int ReloadSequence = -1;//换弹过程记录，默认值-1，换弹开始时使用cmd.sequnce赋值
        public long ReloadingHeldItemID = -1;//当前在换弹的手持物
        public int ReloadRetrieveAmmoBizId = 0;//换弹时从原弹夹中取出的子弹TableId
        public int ReloadRetrieveAmmoCount = 0;//换弹时从原弹夹中取出的子弹数量
        public int ReloadConfirmAmmoCount = 0;//换弹时，world确认的子弹数量
        public long NotInHandReloadTimer = -1;
        private bool InterruptNotInHandReload = false;//非手持换弹打断标记 

        public int OutpostRebornId = 0;//前哨站ID

        public bool NeedPredictUserCmd = true;//需要补帧的开关

        public ShootRateInfo FullShootRateInfo = new();

        public ShootRateInfo ShootInfoLast = new();

        public ShootRateInfo ShootInfoNow = new();

        public MoveInfo MoveInfoLast = new();

        public MoveInfo MoveInfoNow = new();

        public HashSet<long> KillPlayers = new();
        public HashSet<long> KillScientists = new();
        /// 击倒我的人,自己不算
        public long KnockDownMePlayerId = 0;
        /// 击杀我的人,自己不算
        public long KillMePlayerId = 0;

        /// 某些情况下切空手时记录的CurrentWeaponId，用于切回来，比如倒地恢复时需要再切回来
        public long RecordedWeaponId;

        public PlayerDrawComponentUnityDs DrawComponent => GetComponent<PlayerDrawComponentUnityDs>(EComponentIdEnum.PlayerDraw);

        public SleepGroupPlayerComponent SleepGroupPlayerComponent => GetComponent<SleepGroupPlayerComponent>(EComponentIdEnum.SleepGroup);

        public override void Init()
        {
            base.Init();
            AddComponent(new RootNodeComponent());
            AddComponent(new PlayerDrawComponentUnityDs());
            AddComponent(new SleepGroupPlayerComponent());
            AddComponent(new PlayerSecAimFlowTlogComponent());
        }

        public override void PostInit()
        {
            base.PostInit();

            //if (ServerConfig.Instance.IsSimulatorMode)
            //    McSimulator.Buff.EntityReceiveNewCustomType(this, BuffDataMap);

            InitWearedItemsOnCreate();
            Mc.System.Context.WearItemRequestSet.Add(EntityId);
            roleId2PlayerEntity.Add(RoleId, EntityId);
            InitLogicStateParser();
        }

        public override void Cleanup()
        {
            base.Cleanup();
            roleId2PlayerEntity.Remove(RoleId);
            ReleaseLogicStateParser();
            if (PlayerLogicParams != null && PlayerLogicParams.UserControllerSetting != null)
            {
                UserControllerSetting.Return(PlayerLogicParams.UserControllerSetting);
                PlayerLogicParams.UserControllerSetting = null;
            }
        }

        public bool IsGmEnabledFor() => DebugComp != null;

        public static bool IsGmEnabledFor(ulong roleId)
        {
            if (!roleId2PlayerEntity.TryGetValue(roleId, out var entityId)) return false;
            var entity = EntityManager.Instance.GetEntity(entityId) as PlayerEntity;
            return entity?.IsGmEnabledFor() ?? false;
        }

        public override void OnGoLoaded()
        {
            base.OnGoLoaded();

            MonsterInitShootPointObject();
        }

        public HistoryPlayerEntity Archive()
        {
            var archive = Alpha3ObjectPool.NewObject<HistoryPlayerEntity>();
            archive.ClientTime = ClientTime;
            archive.PosX = PosX;
            archive.PosY = PosY;
            archive.PosZ = PosZ;
            archive.RotateY = RotateY;
            archive.Height = Height;
            archive.PlatformPosX = PlatformPosX;
            archive.PlatformPosY = PlatformPosY;
            archive.PlatformPosZ = PlatformPosZ;
            return archive;
        }

        [RpcHandler]
        public void OnGetClientUnityDs()
        {

            logger.InfoFormat("OnGetClientUnityDs {0} {1}", RoleId, EntityId);
            Mc.SnapshotReceiver.AddPendingRpc(Mc.SnapshotReceiver.NowSnapshotSequence + 1, () =>
            {
                IsOnline = true;
                // entity.NeedRemove = false;
                LoginTime = McCommon.Time.ServerWorldTime;

                if (PlatformId > 0)
                {
                    //人物载具上下线 entity上的 posx 不会更新 重新登录需要重置
                    var go = Mc.Go.GetGo(EntityId);
                    if (go is PlayerGo playerGo)
                    {
                        var pos = playerGo.GetPlayerCCPos();
                        PosX = pos.x;
                        PosY = pos.y;
                        PosZ = pos.z;
                    }
                }

                if (MountableType == EntityTypeId.ParachuteEntity && NullHand != null && CurrentWeaponId == NullHand.EntityId)
                {
                    DrawComponent.SwitchHoldItem(HoldItemIndex.ItemParachuteModel);
                }

                Mc.Entity.OnPlayerLoadingSuccess?.Invoke(this);

                Mc.User.UserOnline(RoleId);
                Mc.Entity.PlayerEntityOnline(this);
                Mc.SnapshotReceiver.UpdatePlayerArchiveEntity(this);
                McSimulator.IOEntity.OnPlayerReLogin(this);
                RemoteCallTurnAirWall(ERpcTarget.OwnClient, McCommonUnity.MapConfig.CloseAirWall, false, true);
            });
        }

        [RpcHandler]
        public void OnLoseClientUnityDs()
        {

            logger.InfoFormat("OnLoseClientUnityDs {0} {1}", RoleId, EntityId);
            var request = OfflineRequest.Generate(EntityId);
            McCommon.SystemRequestMgr.AddRequest(ESystemRequest.OfflineRequestSet, request);
            IsLoggingOut = true;
            IsSoftOffline = false;
            if (Mc.User.RoleIdDic.TryGetValue(RoleId, out var user))
            {
                user.ServerCmdHistory.DisposeList();
            }
            McSimulator.IOEntity.OnPlayerDisconnect(EntityId);
        }

        [RpcHandler]
        public void OnSoftDisconnectUnityDs()
        {
            IsSoftOffline = true;
            FirstPredictCmd = true;
            ServerPredictCmdTime = 0;
            ServerPredictServerTime = 0;
            DisableAllEquips();
        }

        [RpcHandler(ExposeToClient = true)]
        public void TimelineNPCPathFind(float srcX, float srcY, float srcZ, float tarX, float tarY, float tarZ,
            ulong roleId, long modelId)
        {
            Vector3 srcV3 = new(srcX, srcY, srcZ);
            Vector3 tarV3 = new(tarX, tarY, tarZ);
            List<Vector3> nodes = EntityGoUtility.CalculatePath(srcV3, tarV3);
            List<float> nodeX = new();
            List<float> nodeY = new();
            List<float> nodeZ = new();
            foreach (var n in nodes)
            {
                nodeX.Add(n.x);
                nodeY.Add(n.y);
                nodeZ.Add(n.z);
            }

            PlayerEntity player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            logger.InfoFormat("[TimelineNPCPathFind] roleId {0}, modelId {1}, pathNodeNum {2}", roleId, modelId, nodes.Count);
            player.RemoteCallFindPathCallback(ERpcTarget.OwnClient, modelId, nodeX, nodeY, nodeZ);
        }

        [RpcHandler(ExposeToClient = true)]
        public void TimelineNPCMultiplePathFind(List<float> posXList, List<float> posYList, List<float> posZList, ulong roleId, long modelId)
        {
            if (posXList.Count != posYList.Count || posXList.Count != posZList.Count)
            {
                logger.ErrorFormat("[TimelineNPCMultiplePathFind] src data count not equal!");
                return;
            }
            if (posXList.Count < 2)
            {
                logger.ErrorFormat("[TimelineNPCMultiplePathFind] srcX.Count {0} not enough!", posXList.Count);
                return;
            }

            List<float> nodeX = new();
            List<float> nodeY = new();
            List<float> nodeZ = new();

            for (int index = 0; index < posXList.Count - 1; index++)
            {
                Vector3 srcV3 = new(posXList[index], posYList[index], posZList[index]);
                Vector3 tarV3 = new(posXList[index + 1], posYList[index + 1], posZList[index + 1]);
                List<Vector3> nodes = EntityGoUtility.CalculatePath(srcV3, tarV3);
                foreach (var n in nodes)
                {
                    nodeX.Add(n.x);
                    nodeY.Add(n.y);
                    nodeZ.Add(n.z);
                }
            }

            PlayerEntity player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            logger.InfoFormat("[TimelineNPCMultiplePathFind] roleId {0}, modelId {1}, pathNodeNum {2}", roleId, modelId, nodeX.Count);
            player.RemoteCallFindPathCallback(ERpcTarget.OwnClient, modelId, nodeX, nodeY, nodeZ);
        }

        [RpcHandler(ExposeToClient = true, CallInterval = -1f)]
        public void ReceiveUserCmd(UserCmd cmd)
        {
            // TODO 如果是增量cmd，需要恢复
            if (cmd.BaseSequence != UserCmd.FullCmdBaseSequence)
            {
                if (!cmd.TryRecover(RoleId))
                {
                    logger.Warn($"[UserCmd]{RoleId} Receive Incremental Cmd {cmd.Sequence}, Base : {cmd.BaseSequence}, but recover failed.");
                    cmd.Dispose();
                    return;
                }
            }

            if (cmd.HasTraceTime)
            {
                cmd.TraceTime.GateReceiveTs = RpcContext.GateReceiveTs;
                cmd.TraceTime.SimulatorReceiveTs = TimeStampUtil.GetNowTimeStampMSec();
            }

            Mc.User.ReceiveUserCmd(RoleId, EntityId, cmd);
        }

        public void TakeInPlatformPostInit(PlayerGo playerGo)
        {
            if (PlatformId > 0)
            {
                var playerEntity = Mc.Entity.GetEntity(EntityId) as PlayerEntity;
                var playerLogicParams = playerEntity?.PlayerLogicParams;
                var platformEntity = Mc.Entity.GetEntity(PlatformId) as IPlatformEntity;
                var platform = Mc.Go.GetGo(PlatformId);
                if (platform != null && platformEntity != null)
                {
                    PlayerLogicState.TakeInPlatform(playerLogicParams, PlatformId,
                        platformEntity);
                    PlatformId = 0;
                    Vector3 pos = playerGo.MainTransform.position;
                    var parent = platform.MainTransform;
                    if (platform is IPlatformGo platformGo)
                    {
                        parent = platformGo.GetPlaneTransform();
                    }
                    pos.y = parent.position.y;
                    UnityUtility.ServerSetCcPosition(playerLogicParams.NowSnapshotSequence, playerLogicParams.cc,
                        this, pos);
                    CheckPlatform(playerLogicParams);
                }
                else
                {
                    logger.ErrorFormat("player {0} platform {1} not found", playerGo.EntityId,
                        PlatformId);
                }
            }
        }


        public void Die()
        {
            //Hp = 0;
            DamageableComponent dc = GetComponent<DamageableComponent>(EComponentIdEnum.Damageable);
            dc.Hp = 0;
            LifeCycleFlags = LifeFlags.Died;
            Scale = 0;
            DyingFinishTime = 0;

            Vector3 rebornPosition = Mc.System.GetBaseSystem<RebornSystem>()
                .GetNearbyPoint2Relive(this, out var isNearbyPoint);
            var playerGo = Mc.Go.GetGo<PlayerGo>(EntityId);
            rebornPosition = GamePhysics.RaiseGroundPos(rebornPosition, playerGo.CharacterController);
            IsNearbyReborn = isNearbyPoint;
            NearbyRebornPosX = rebornPosition.x;
            NearbyRebornPosY = rebornPosition.y;
            NearbyRebornPosZ = rebornPosition.z;

            var currentTime = McCommon.Time.ServerWorldTime;
            DieTimeStamp = TimeStampUtil.GetNowTimeStampMSec();
            if (IsOffline)
            {
                // NeedRemove = true;
            }
            else
            {
                //下线时间不算进存活时间
                bool isOfflineHappen = OfflineTime > RebornTimePoint;
                AliveDuration +=
                    currentTime - (isOfflineHappen ? LoginTime : RebornTimePoint);
                SocUtility.Logger.InfoFormat("存活时间 {0}", AliveDuration);
            }
            
            logger.InfoFormat("[DieTag] PlayerDie EntityId:{0} IsNearbyReborn:{1} NearbyRebornPos:{2}", EntityId, isNearbyPoint, rebornPosition);
        }

        [RpcHandler(ExposeToClient = true)]
        public void GetMonumentBiomeForClient()
        {
            if (TerrainMeta.Monuments is null)
                return;

            Dictionary<int, int> monumentBiomeTypes = new();
            foreach (var (id, monument) in TerrainMeta.Monuments)
            {
                var pos = monument.transform.position;
                var biomeType = TerrainMeta.BiomeMap.GetBiomeMaxType(pos);
                monumentBiomeTypes[id] = biomeType;
            }
            RemoteCallUpdateMonumentBiome(ERpcTarget.OwnClient, monumentBiomeTypes);
        }

        [RpcHandler(ExposeToClient = true, CallInterval = 1f)]
        public void TrackMonumentTask(bool isSuccess)
        {
            logger.InfoFormat("[OnClientReplyMonumentTask] {0} {1}, isSuccess={2}", RoleId, EntityId, isSuccess);
            if (isSuccess)
            {
                McSimulator.IOEntity.OnPlayerTrackMonumentTask(this);
            }
        }

        /// <summary>
        /// 玩家喝地上的水
        /// </summary>
        [RpcHandler(ExposeToClient = true)]
        public void Drink(float riverX, float riverY, float riverZ)
        {
            var isRiver =
                TerrainMeta.TopologyMap.GetTopology(new Vector3(riverX, riverY, riverZ), TerrainTopology.RIVER);
            var isLake = TerrainMeta.TopologyMap.GetTopology(new Vector3(riverX, riverY, riverZ), TerrainTopology.LAKE);

            if (!isRiver && !isLake)
            {
                logger.InfoFormat("see river point is not the river or lake");
                return;
            }
            if ((PosX - riverX) * (PosX - riverX) + (PosZ - riverZ) * (PosZ - riverZ) > 225)
            {
                logger.InfoFormat("player is not near the river");
                return;
            }
            MgrWater.DoDrinkWater(this, ItemConst.PureWaterItemId, Mc.Tables.TbGlobalConfig.DrinkChangeWater, 0);
        }

        /// <summary>
        /// 获取物品到玩家身上
        /// </summary>
        [RpcHandler(ExposeToClient = true)]
        public void PickItem(SimulatorPickUpRequest data)
        {
            var roleId = RpcContext.CurrentRoleId;
            logger.InfoFormat("PickItem, roleId: {0}, itemUid:{1} {2}", roleId, data.ItemNodeId, data.TargetPath);
            McSimulator.GatherItemPickable.PickItem(roleId, data);
        }

        [RpcHandler(ExposeToClient = true)]
        public void MoveToRebornPoint(long entityId,
            int rebornIndex)
        {
            if (!IsGmEnabledFor(RpcContext.CurrentRoleId))
                return;

            var playerEntity = Mc.Entity.GetEntity(entityId) as PlayerEntity;
            if (null == playerEntity)
            {
                return;
            }

            if (!McSimulator.Go.TyrGetGo(playerEntity.EntityId, out PlayerGo player))
            {
                return;
            }

            var PlayerTransform = player.ColliderGo.transform;

            if (PlayerTransform == null)
            {
                return;
            }

            //参考 RebornSystem
            var zoneRoot = GameObject.Find("RebornZones");
            if (!zoneRoot)
            {
                return;
            }

            //负数则随机取一个
            var rebornZones = zoneRoot.transform;
            int count = rebornZones.childCount;
            if (rebornIndex < 0)
            {
                rebornIndex = UnityEngine.Random.Range(0, count - 1);
            }
            else
            {
                rebornIndex = Mathf.Clamp(rebornIndex, 0, count - 1);
            }

            //尝试获取出生点
            var targetZone = rebornZones.GetChild(rebornIndex);
            if (targetZone == null)
            {
                return;
            }

            var ZoneScale = targetZone.localScale;
            var ZonePos = targetZone.position;
            var bornX = ZonePos.x + UnityEngine.Random.Range(-ZoneScale.x / 2, ZoneScale.x / 2);
            var bornZ = ZonePos.z + UnityEngine.Random.Range(-ZoneScale.z / 2, ZoneScale.z / 2);

            var ray = new Ray(new UnityEngine.Vector3(bornX, 200, bornZ), UnityEngine.Vector3.down);
            int layerMask = 1 << LayerMask.NameToLayer("Default");
            layerMask |= 1 << LayerMask.NameToLayer("Ground");


            var bornY = PlayerTransform.position.y;
            if (Physics.Raycast(ray, out var hit, 250, layerMask))
            {
                bornY = hit.point.y;
            }

            var position = new UnityEngine.Vector3(bornX, bornY, bornZ);
            PlayerTransform.position = position;
        }

        bool ParachuteCanOpenCheck()
        {
            if (EquipBag == null || EquipBag.TableId != ItemConst.ParachuteBagItemId)//未装备降落伞包
            {
                RemoteCallShowTips(ERpcTarget.OwnClient, 24124);
                return false;
            }

            IEntity mountableEntity = Mc.Entity.GetEntity(MountableId);
            if (mountableEntity != null && mountableEntity.EntityId == EntityTypeId.ParachuteEntity)//降落伞已打开
            {
                RemoteCallShowTips(ERpcTarget.OwnClient, 24129);
                return false;
            }

            //下落时间和离地高度检查
            if (!HeightToGroundAndFallTimeCheck())
                return false;

            float parachuteCD = ParachuteEquipCDTime > 0 ? ParachuteEquipCDTime : ParachuteDestroyCDTime;
            if (parachuteCD > 0)
            {
                //冷却中
                RemoteCallShowTips(ERpcTarget.OwnClient, 24121);
                return false;
            }

            if (EquipBag.Condition <= 0)
            {
                //降落伞包耐久度0
                RemoteCallShowTips(ERpcTarget.OwnClient, 24123);
                return false;
            }

            if (EquipBag.Condition > Mc.Tables.TbParachute.ParachuteOpenConsume)
                EquipBag.Condition -= Mc.Tables.TbParachute.ParachuteOpenConsume;
            else
                EquipBag.Condition = 0;

            return true;
        }


        bool createParachuteLock = false;
        [RpcHandler(ExposeToClient = true)]
        public void SpawnAndMountParachute(long vehicleTableId/* 战斗_载具表.xlsx的载具id*/,
            float posx, float posy, float posz,
            float rotx, float roty, float rotz)
        {
            if (!createParachuteLock && ParachuteCanOpenCheck())
            {
                createParachuteLock = true;
                long entityId = SpawnVehicle(vehicleTableId, posx, posy, posz, rotx, roty, rotz);
                //WantsMount(entityId, 1, 0);
                //long timerID = -1;
                //timerID = Mc.TimerWheel.AddTimerOnce(1, (id, data, delete) =>
                //{
                //RpcContext.CurrentRoleId = (ulong)data;
                WantsMount(entityId, 1, 0);
                //Mc.TimerWheel.CancelTimer(timerID);
                createParachuteLock = false;
                //}, RpcContext.CurrentRoleId);
            }
        }

        [RpcHandler]
        public long SpawnVehicle(long vehicleTableId/* 战斗_载具表.xlsx的载具id*/,
            float posx, float posy, float posz,
            float rotx, float roty, float rotz)
        {
            var vehicleInfo = Mc.Tables.TbVehicleInfo.GetOrDefault(vehicleTableId);
            if (vehicleInfo == null)
            {
                logger.ErrorFormat("要创建的载具在不表 战斗_载具表.xlsx 中配置， vehicleTableId：{0}", vehicleTableId);
                return 0;
            }

            var car = MountableEntityFactory.CreateEntity(vehicleTableId, vehicleInfo.CombatAttributes);
            car.PosX = posx;
            car.PosY = posy;
            car.PosZ = posz;
            car.RotateX = rotx;
            car.RotateY = roty;
            car.RotateZ = rotz;


            logger.InfoFormat("表id：{0}，创建载具：{1}", vehicleTableId.ToString(), car.EntityId.ToString());
            // 添加Entity并实例化Go
            Mc.Go.CreateEntityAndGo(car);

            McSimulator.Go.CreateOrUpdateGo(car);
            return car.EntityId;
        }

        [RpcHandler]
        public void ChangeItemSkinId(long entityId, long skinId)
        {
            bool success = false;
            var item = Mc.Entity.GetEntity(entityId) as IItemEntity;
            if (item != null)
            {
                item.SkinId = skinId;
                success = true;
            }
            logger.InfoFormat("ChangeItemSkinId {0}, {1} success:{2}", entityId, skinId, success);
        }

        /// <summary>
        /// 快捷栏的枪卸下子弹
        /// </summary>
        [RpcHandler(ExposeToClient = true)]
        public void UnloadBullet(long weaponId)
        {
            var roleId = RpcContext.CurrentRoleId;
            var player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            var playerId = player.EntityId;
            IHaveBulletEntity weapon = player.GetCustomTypeById(weaponId) as IHaveBulletEntity;
            logger.InfoFormat("UnloadBullet uid:{0},pid:{1},weaponid:{2}", roleId, playerId, weaponId);
            if (player != null && weapon != null)
            {
                if (weapon.OwnerEntityId == playerId)
                {
                    if (!weapon.IsReloading && weapon.Clips > 0 && player.ActionState != PlayerActionStateEnum.Fire)
                    {
                        logger.InfoFormat(" send to logic add item id:{0},count:{1}", weapon.UsingAmmoId, weapon.Clips);

                        var bullectItemConfig = Mc.Tables.TbItemConfig.GetOrDefault(weapon.UsingAmmoId);
                        player.RemoteCallReloadGiveAmmo(ERpcTarget.World, weapon.UsingAmmoId, weapon.Clips);
                        weapon.Clips = 0;
                        weapon.SubClipConsume = 0;
                    }
                }
            }
        }

        /// <summary>
        /// 挂件状态变化
        /// </summary>
        [RpcHandler(ExposeToClient = true)]
        public void ChangeAccessoryStatus(long accessoryId, int status, bool add)
        {
            var roleId = RpcContext.CurrentRoleId;
            PlayerEntity player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            var playerId = player.EntityId;
            WeaponAccessoryItemCustom accessory = Mc.Entity.GetEntity(accessoryId) as WeaponAccessoryItemCustom;
            if (player != null && accessory != null && accessory.OwnerEntityId == playerId)
            {
                if (add)
                {
                    accessory.Status |= status;
                }
                else
                {
                    accessory.Status &= ~status;
                }

                logger.InfoFormat("ChangeAccessoryStatus uid:{0},pid:{1},accessory:{2},status:{3},state:{4}", roleId, playerId, accessoryId, status, add);
                WeaponCustom weapon = player.GetCurrentWeapon();
                if (weapon != null)
                {
                    weapon.UpdateWeaponAccessoryCorrection();
                }
            }
        }

        /// <summary>
        /// 玩家喝水
        /// </summary>
        [RpcHandler]
        public void DoDrinkWater(long entityid, long bottleId, long waterId, int amount)
        {
            MgrWater.DoDrinkWater(Mc.Entity.GetPlayerEntity(entityid), waterId, amount, bottleId);
        }



        [RpcHandler]
        public void EatFood(long entityId, long foodTableId)
        {
            Food food = McCommonUnity.Tables.TbFood.GetOrDefault(foodTableId);
            if (food == null)
            {
                logger.ErrorFormat("找不到食物配表 {0}", foodTableId);
                return;
            }

            var properties = food.Properties;
            var propertiesNums = food.PropertiesNum;
            bool isRandom = food.IsRandom;

            //随机食物需要根据随机结果决定使用的效果
            int randomResult = 0;
            if (isRandom)
            {
                var foodRandom = McCommonUnity.Tables.TbFoodRandom.GetOrDefault(foodTableId);
                if (foodRandom == null)
                {
                    logger.ErrorFormat("随机食物配置{0}不存在 请检查！！！！ ", foodTableId);
                    return;
                }

                float factor = UnityEngine.Random.value;
                float delta = 0;
                //随机食物要根据概率来恢复属性
                for (int i = 0; i < foodRandom.Random.Length; i++)
                {
                    delta += foodRandom.Random[i];
                    if (delta < factor) continue;

                    if (i >= foodRandom.PropertiesNum.Count ||
                        i >= foodRandom.Properties.Count)
                    {
                        logger.ErrorFormat("随机食物配置{0} 数组长度不相等 请检查！！！！ ", foodTableId);
                        return;
                    }

                    properties = foodRandom.Properties[i];
                    propertiesNums = foodRandom.PropertiesNum[i];
                    randomResult = i;
                    break;
                }
            }

            if (properties == null)
            {
                logger.ErrorFormat("食物{0}恢复属性配表错误 请检查！！！！ ", foodTableId);
                return;
            }

            RemoteCallEatFoodCallback(ERpcTarget.OwnClient, foodTableId, randomResult);
            PropModifier.ModifyMultiProperties(this, properties, propertiesNums);
        }

        [RpcHandler(ExposeToClient = true)]
        public void CancelCorpseHighlight(long corpseEntityId, long viewPlayerEntityId)
        {
            if (Mc.Entity.GetEntity(corpseEntityId) is CorpseEntity corpseEntity)
            {
                // 自己看自己的实体
                if (corpseEntity.HostEntityId == viewPlayerEntityId)
                {
                    if (corpseEntity.IsHighlight2Self)
                    {
                        corpseEntity.IsHighlight2Self = false;
                        corpseEntity.Highlight2SelfCountdown = -1;
                    }
                }
                //杀手看尸体
                else if (corpseEntity.KillPlayerId == viewPlayerEntityId)
                {
                    if (corpseEntity.IsHighlight2Killer)
                    {
                        corpseEntity.IsHighlight2Killer = false;
                        corpseEntity.Highlight2KillCountdown = -1;
                    }
                }
            }
        }

        [RpcHandler(ExposeToClient = true)]
        public void StartCorpseHighlight(long corpseEntityId, long viewPlayerEntityId)
        {
            var corpseEntity = Mc.Entity.GetEntity(corpseEntityId) as CorpseEntity;
            if (corpseEntity != null)
            {
                // 自己看自己的实体
                if (corpseEntity.HostEntityId == viewPlayerEntityId)
                {
                    if (!corpseEntity.IsHighlight2Self && corpseEntity.Highlight2SelfCountdown == 0)
                    {
                        corpseEntity.IsHighlight2Self = true;
                    }
                }
                //杀手看尸体
                else if (corpseEntity.KillPlayerId == viewPlayerEntityId)
                {
                    if (!corpseEntity.IsHighlight2Killer && corpseEntity.Highlight2KillCountdown == 0)
                    {
                        corpseEntity.IsHighlight2Killer = true;
                    }
                }
            }
        }

        [RpcHandler(ExposeToClient = true, CallInterval = 0.5f)]
        public void FinishLoading()
        {
            var roleId = RpcContext.CurrentRoleId;
            var playerEntity = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (playerEntity != null)
            {
                playerEntity.ObserverLoadingFinishTime = long.MinValue;
            }
        }

        [RpcHandler(ExposeToClient = true)]
        public void WakeUpAction()
        {
            // simulator去掉WakeUpEventSystem，把对应的处理放在下面，其实就这一句而已
            HasWakeUpRequest = true;
            RemoteCallWakeUpActionAck(ERpcTarget.OwnClient);
            // WakeUpEvent dataEvent = WakeUpEvent.NewEvent(this);
            // McCommon.LocationBasedEvent.AddEvent(dataEvent);

            // 离开帐篷
            EntityGoUtility.LeaveTent(this);
            logger.Info($"WakeUpAction:{Name}, {EntityId}, {CharacterState}, {UnAliveState}");
            this.GuaranteeCheck.Clear();//清空保底列表
        }

        // [RpcHandler(ExposeToClient = true)]
        // public void StartWaterBottleCollect(long waterTypeId)
        // {
        //     var roleId = RpcContext.CurrentRoleId;
        //     logger.InfoFormat("[StartWaterBottleCollect] userId: {0}, waterTypeId: {1}", roleId, waterTypeId);
        //     var playerEntity = Mc.Entity.GetPlayerEntityByRoleId(roleId);
        //     if (playerEntity == null)
        //     {
        //         logger.ErrorFormat("[StartWaterBottleCollect] player not found! userId: {0}", roleId);
        //         return;
        //     }
        //
        //     var waterType = MgrWater.BodyWaterType(playerEntity.EntityId);
        //     var realWaterTypeId = ItemConst.SaltWaterItemId;
        //     if (waterType != null)
        //     {
        //         realWaterTypeId = waterType.drinkable ? ItemConst.PureWaterItemId : ItemConst.SaltWaterItemId;
        //     }
        //     if (realWaterTypeId != waterTypeId)
        //     {
        //         logger.WarnFormat("[StartWaterBottleCollect] waterTypeId different! waterTypeId: {0}, realWaterTypeId: {1}", waterTypeId, realWaterTypeId);
        //     }
        //     var pos = new Vector3(playerEntity.PosX, playerEntity.PosY, playerEntity.PosZ);
        //     if (!TerrainMeta.TopologyMap.GetTopology(pos, TerrainTopology.WATER))
        //     {
        //         logger.ErrorFormat("[StartWaterBottleCollect] player not stand in water! userId: {0}, pos: {1}", roleId, pos);
        //         return;
        //     }
        //     logger.Error($"StartWaterBottleCollect :{playerEntity.CurrentWeaponId}");
        //     playerEntity.CollectWaterStartTime = TimeStampUtil.GetNowTimeStampMSec();
        //     playerEntity.CollectWaterTypeId = realWaterTypeId;
        //     Mc.CurrentChangeWorld.ChangeEntity(playerEntity, "StartCollectWater");
        // }

        // [RpcHandler(ExposeToClient = true)]
        // public void WaterBottleCollect(long bottleId, int amount)
        // {
        //     if (amount <= 0) return;
        //     var roleId = RpcContext.CurrentRoleId;
        //     logger.InfoFormat("[WaterBottleCollect] userId: {0}, amount: {1}", roleId, amount);
        //     var playerEntity = Mc.Entity.GetPlayerEntityByRoleId(roleId);
        //     if (playerEntity == null)
        //     {
        //         logger.ErrorFormat("[WaterBottleCollect] player not found! userId: {0}", roleId);
        //         return;
        //     }
        //     if (playerEntity.CollectWaterTypeId != ItemConst.PureWaterItemId && playerEntity.CollectWaterTypeId != ItemConst.SaltWaterItemId)
        //     {
        //         return;
        //     }
        //
        //     var pos = new Vector3(playerEntity.PosX, playerEntity.PosY, playerEntity.PosZ);
        //     var maxWaterLevel = TerrainMeta.WaterMap.GetHeight(pos);
        //     if (playerEntity.PosY >= maxWaterLevel)
        //     {
        //         logger.WarnFormat("[WaterBottleCollect] player stand above water! userId: {0}, pos: {1}", roleId, pos);
        //     }
        //
        //     if (!TerrainMeta.TopologyMap.GetTopology(pos, TerrainTopology.WATER))
        //     {
        //         logger.ErrorFormat("[WaterBottleCollect] player not stand in water! userId: {0}, pos: {1}", roleId, pos);
        //         return;
        //     }
        //
        //     var realAmount = ((TimeStampUtil.GetNowTimeStampMSec() - playerEntity.CollectWaterStartTime) / 1000.0f) *
        //         Mc.Tables.TbGlobalConfig.ReceivesWaterPerSecond;
        //     if (Mathf.Abs(realAmount - amount) > 0.2f * Mc.Tables.TbGlobalConfig.ReceivesWaterPerSecond)
        //     {
        //         logger.WarnFormat("[WaterBottleCollect] water amount different! amount: {0}, realAmount: {1}", amount, realAmount);
        //         playerEntity.RemoteCallWaterBottleDoCollect(ERpcTarget.World, bottleId, playerEntity.CollectWaterTypeId, Mathf.RoundToInt(realAmount));
        //     }
        //     else
        //     {
        //         playerEntity.RemoteCallWaterBottleDoCollect(ERpcTarget.World, bottleId, playerEntity.CollectWaterTypeId, amount);
        //     }
        //     playerEntity.CollectWaterStartTime = 0;
        //     playerEntity.CollectWaterTypeId = 0;
        //     Mc.CurrentChangeWorld.ChangeEntity(playerEntity, "EndCollectWater");
        // }

        [RpcHandler(ExposeToClient = true)]
        // 0 随机复活，1 附近点， 2 存档点
        public void Reborn(int type)
        {
            var playerEntity = this;
            var entityId = playerEntity.EntityId;

            if (playerEntity.IsAlive)
            {
                logger.InfoFormat("Reborn Error: entity IsAlive:{0}", entityId);
                return;
            }

            logger.InfoFormat("[RebornTag] Reborn EntityId:{0} Type:{1}", entityId, type);

            var rebornRequest = RebornRequestType.None;
            switch (type)
            {
                case 0:
                    if (PlayHelper.EnableRandomReborn == false)
                    {
                        logger.InfoFormat("EnableRandomReborn为false");
                        return;
                    }

                    rebornRequest = RebornRequestType.RandomPos;
                    break;
                case 1:
                    if (PlayHelper.EnableNearbyReborn == false)
                    {
                        logger.InfoFormat("EnableNearbyReborn为false");
                        return;
                    }

                    if (playerEntity.Buff.IsBuffActive(BuffType.RespawnNearDeathPoint) == false)
                    {
                        logger.Info("未解锁附近点复活功能");
                        return;
                    }

                    if (playerEntity.IsNearbyRebornValid)
                    {
                        rebornRequest = RebornRequestType.Nearby;
                    }
                    else
                    {
                        rebornRequest = RebornRequestType.RandomPos;
                    }
                    break;
                case 2:
                    if (PlayHelper.EnableArchiveReborn == false)
                    {
                        logger.InfoFormat("EnableArchiveReborn为false");
                        return;
                    }

                    rebornRequest = RebornRequestType.Archive;
                    break;
                default:
                    logger.InfoFormat("Reborn:无效的复活type:{0}", type);
                    return;
            }

            if (rebornRequest == RebornRequestType.RandomPos)
            {
                int mapId = ServerBootstrap.Instance.MapId;
                bool randomReborn = Mc.Tables.TbRandomReborn.MapHasRandomReborn(mapId);
                if (randomReborn)
                {
                    rebornRequest = RebornRequestType.RingRandomPos;
                    var randomRebornTb = Mc.Tables.TbRandomReborn.GetMapStageConfig(mapId, playerEntity.RandomRebornStage);
                    if (playerEntity.RandomRebornLastTime == 0 || Mc.Time.ServerWorldTime - playerEntity.RandomRebornLastTime >= randomRebornTb.Time * 1000)
                    {
                        playerEntity.RandomRebornStage = 1;
                    }
                    else
                    {
                        var nextRandomRebornStage = playerEntity.RandomRebornStage + 1;
                        var maxStage = Mc.Tables.TbRandomReborn.GetMapMaxStage(mapId);
                        nextRandomRebornStage = Mathf.Min(nextRandomRebornStage, maxStage);
                        playerEntity.RandomRebornStage = nextRandomRebornStage;
                    }
                    playerEntity.RandomRebornLastTime = Mc.Time.ServerWorldTime;
                }
            }

            playerEntity.RebornRequest = rebornRequest;
            // playerEntity.RebornRequest = RebornRequestType.Nearby;
        }

        [RpcHandler(ExposeToClient = true)]
        public void OutpostReborn(int outpostId)
        {
            if (PlayHelper.EnableOutpostReborn == false)
            {
                logger.InfoFormat("OutpostReborn, EnableOutpostReborn为false");
                return;
            }

            if (Buff.IsBuffActive(BuffType.OutpostRespawn) == false)
            {
                logger.Info("未解锁前哨站复活功能");
                return;
            }

            var roleId = RpcContext.CurrentRoleId;
            var playerEntity = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            var entityId = playerEntity.EntityId;

            if (playerEntity.IsAlive)
            {
                logger.InfoFormat("Reborn Error: entity IsAlive:{0}", entityId);
                return;
            }

            if (!McSimulator.Spawn.HasOutpostSpawnPoint(outpostId))
            {
                logger.ErrorFormat("GetOutpostPoint: 找不到指定要复活的前哨站 {0}", outpostId);
                return;
            }
            if (playerEntity.IsOutpostRebornValid)
            {
                playerEntity.RebornRequest = RebornRequestType.OutpostRandomPos;
                playerEntity.OutpostRebornId = outpostId;
            }

            logger.InfoFormat("[RebornTag] Reborn EntityId:{0} Type:OutpostReborn", entityId);
        }

        [RpcHandler(ExposeToClient = true)]
        public void Suicide(int doSuicideType)
        {
            var roleId = RpcContext.CurrentRoleId;
            var playerEntity = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (playerEntity != null)
            {
                SuicideUtil.Suicide(Mc.System.Context, playerEntity, (DoSuicideType)doSuicideType);
            }
        }

        [RpcHandler(ExposeToClient = true)]
        public void SuicideWhenDismountFailed()
        {
            DismountWithParam("SuicideWhenDismountFailed", DismountFailedResponse.Suicide, VehicleGetOffReason.Dismount);
        }

        // [RpcHandler(ExposeToClient = true)]
        // public void ModifyBlockBreakTables(int tag)
        // {
        //     var roleId = RpcContext.CurrentRoleId;
        //     var playerEntity = Mc.Entity.GetPlayerEntityByRoleId(roleId);
        //     if (playerEntity != null)
        //     {
        //         playerEntity.PlayerLogicBlockBreakData.ModifyBlockAndBreakTables(tag);
        //     }
        // }

        /// <summary>
        /// 改变可开关装备状态热爱
        /// </summary>
        /// <param name="entityId"></param>
        /// <param name="equipIndex"></param>
        /// <param name="enable"></param>
        [RpcHandler(ExposeToClient = true, CallInterval = -1)]
        public void SetEquipEnable(int equipIndex, bool enable)
        {
            var roleId = RpcContext.CurrentRoleId;
            PlayerEntity player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (player != null)
            {
                player.SetEquipEnableFlag(equipIndex, enable);
            }
        }

        [RpcHandler(ExposeToClient = true)]
        public void WantsMountZipline(int ziplineId, long interactionId)
        {
            var roleId = RpcContext.CurrentRoleId;
            var ziplines = TerrainMeta.GetZiplines(ziplineId);
            if (ziplines == null)
                return;

            var playerEntity = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (playerEntity == null || playerEntity.MountableId > 0)
            {
                return;
            }

            var interaction = Mc.Entity.GetEntity(interactionId) as InteractionEntity;
            if (interaction == null)
            {
                logger.InfoFormat("InteractionEntity {0} 不存在", interactionId);
                return;
            }

            var canMountable = MoveStateZipline.Instance.CanStateEnter(playerEntity);// MoveStateController.Instance.CanEnter(PlayerMoveStateEnum.Mountable, playerEntity.PlayerLogicParams);
            if (canMountable == false)
            {
                logger.InfoFormat("WantsMountZipline:状态机阻塞无法乘坐");
                return;
            }
            var lineStartPoint = ziplines[0].GetPoint(0);
            var playerPosition = new Vector3(playerEntity.PosX, playerEntity.PosY, playerEntity.PosZ);

            var distance = Vector3.Distance(lineStartPoint, playerPosition);
            if (distance > 5)
            {
                // 防止作弊远程上滑索
                logger.InfoFormat("WantsMountZipline:, distance:{0},太远无法乘坐", distance);
                return;
            }

            var spawnRot = Quaternion.LookRotation((lineStartPoint - Vector3Ex.WithY(playerPosition, lineStartPoint.y)).normalized);
            var startRot = Quaternion.FromToRotation(Vector3.forward, ziplines[0].startOrientation);
            Vector3 startPosition = playerPosition + Vector3.up * 2.1f;//ziplines[0].GetPoint(0);
            //var rotation = //Quaternion.FromToRotation(Vector3.forward, ziplines[0].startOrientation);
            Vector3 rotateEuler = spawnRot.eulerAngles;
            var id = IdGenerator.GenWarZoneUniqueId();
            var ziplineEntity = new ZiplineEntity(id)
            {
                PosX = lineStartPoint.x,
                PosY = lineStartPoint.y,
                PosZ = lineStartPoint.z,
                RotateX = startRot.eulerAngles.x,
                RotateY = startRot.eulerAngles.y,
                RotateZ = 0,
                PlayerId = playerEntity.EntityId,
                ZiplineId = ziplineId
            };
            Mc.Entity.AddEntity(ziplineEntity);

            interaction.LastInterStartTime = Mc.Time.ServerWorldTime;
            playerEntity.MountableId = id;
            playerEntity.MountableType = EntityTypeId.ZiplineEntity;
        }

        [RpcHandler]
        public void SleepBagRebornRequest(long playerEntityId, float spawnPosX, float spawnPosY, float spawnPosZ, float spawnRotX, float spawnRotY, float spawnRotZ, float spawnRotW)
        {
            var rebornRequest = WizardGames.Soc.Common.Combat.RebornRequest.Generate(playerEntityId, new System.Numerics.Vector3(spawnPosX, spawnPosY, spawnPosZ),
                new System.Numerics.Quaternion(spawnRotX, spawnRotY, spawnRotZ, spawnRotW));
            McCommon.SystemRequestMgr.AddRequest(ESystemRequest.RebornRequestSet, rebornRequest);
        }

        [RpcHandler]
        public void GetInVehicle(long playerEntityId, long vehicleEntityId)
        {
            if (McSimulator.Katyusha.AllKatyushaEntities.Count > 0 && McSimulator.Katyusha.AllKatyushaEntities[0].EntityId == vehicleEntityId)
            {
                var go = Mc.Go.GetGo(vehicleEntityId) as KatyushaGo;
                MountableUtil.GetInVehicle(Mc.Entity.GetPlayerEntity(playerEntityId), vehicleEntityId, go.sitPoint);
            }
        }

        [RpcHandler(ExposeToClient = true)]
        public void GetOutVehicle()
        {
            var roleId = RpcContext.CurrentRoleId;
            var playerEntity = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            var pos = MountableUtil.GetAroundPos(playerEntity);
            MountableUtil.GetOutVehicle(playerEntity, pos);
        }

        [RpcHandler(ExposeToClient = true)]
        public void SpeedUpZipLine(long playerEntityId, bool speedUp)
        {
            var roleId = RpcContext.CurrentRoleId;
            var playerEntity = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (playerEntity != null)
            {
                if (Mc.Entity.GetEntity(playerEntity.MountableId) is ZiplineEntity ziplineEntity)
                {
                    ziplineEntity.SpeedUp = speedUp;
                }
            }
        }

        /// <summary>
        /// 载具换座位
        /// </summary>
        /// <param name="playerId"></param>
        /// <param name="mountableId"></param>
        /// <param name="seatIndex"></param>
        [RpcHandler(ExposeToClient = true)]
        public void SwitchMountSeat(int seatIndex)
        {
            var roleId = RpcContext.CurrentRoleId;
            /* 0. 根据playerId获取玩家实体 */
            var player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (player == null)
            {
                logger.InfoFormat("SwitchMountSeat不存在id:{0}的玩家实体", roleId);
                return;
            }
            var mountableId = player.MountableId;
            logger.InfoFormat($"收到RPC消息SwitchMountSeat,playerId:{player.EntityId}, mountableId:{mountableId}");

            /*1. 根据mountableId获取载具*/
            if (Mc.Go.Gos.TryGetValue(mountableId, out var ego) == false)
            {
                logger.InfoFormat("SwitchMountSeat载具id:{0},不存在Go实例", mountableId);
                return;
            }

            /*2. 获取BasePlayer*/
            if (player.BasePlayerType is not BasePlayer)
            {
                logger.InfoFormat("SwitchMountSeat载具id:{0},不存在BasePlayer", player.EntityId);
                return;
            }

            if (ego is BaseMountableGo mountableGo == false)
            {
                logger.InfoFormat("SwitchMountSeat载具id:{0},载具不是BaseMountableGo类型,type:{1}", mountableId, ego.GetType());
                return;
            }

            if (VehicleMountableUtil.GetPermission(player, mountableGo.MountableEntity) != VehiclePermission.Operate)
            {
                logger.Info($"SwitchMountSeat:载具{mountableId}无权限，player:{player.RoleId},name:{player.Name},roleid:{player.RoleId}");
                return;
            }

            var seatPlayerId = mountableGo.GetPlayerIdViaSeatIndex(seatIndex);
            if (seatPlayerId > 0)
            {
                mountableGo.PopMessage(roleId, 2, LanguageConst.TargetSeatIsNotEmpty, null);
                logger.InfoFormat("SwitchMountSeat载具id:{0},座位{1}上已经有玩家{2}", player.EntityId, seatIndex, seatPlayerId);
                return;
            }

            var oldIsDriver = player.IsDriver;
            if (mountableGo.SwitchMountSeat(player, seatIndex) == false)
            {
                return;
            }
            player.TryRecordGetOffVehicle(mountableGo.MountableEntity, VehicleGetOffReason.SwitchSeat);
            player.TryRecordGetInVehicle(mountableGo.MountableEntity, player.IsDriver ? VehicleGetInReason.Driver : VehicleGetInReason.Passenger);

            if (player.IsDriver && !oldIsDriver)
            {
                player.RemoteCallMountEvent(ERpcTarget.World, ((IMountable)ego).TemplateId);
            }
        }

        /// <summary>
        /// 玩家坐上可乘坐物(载具)
        /// </summary>
        [RpcHandler(ExposeToClient = true)]
        public void WantsMount(long mountableId, int seatType, int seatIndex)
        {
            /*
             * tip 选择上车座位有两种方式
             * 1. 通过seatType指定需要上的座位类型上车，系统会选择一个合适的座位
             * 2. 通过seatIndex指定需要上的座位序号上车，可以直接选择一个指定的座位
             */
            var roleId = RpcContext.CurrentRoleId;

            /* 0. 根据playerId获取玩家实体 */
            var player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (player == null)
            {
                logger.InfoFormat("WantsMount不存在id:{0}的玩家实体", roleId);
                return;
            }

            var playerId = player.EntityId;
            if (player.CharacterState == PlayerCharacterStateEnum.UnActive)
            {
                logger.InfoFormat($"WantsMount:玩家{playerId}在睡觉状态");
                return;
            }

            if (player.MountableId > 0)
            {
                logger.InfoFormat($"WantsMount:已在载具{player.MountableId}上,请先下载具");
                return;
            }

            logger.InfoFormat($"收到RPC消息WantsMount,playerId:{playerId}, mountableId:{mountableId}");

            if (!player.CanOperateMount)
            {
                logger.InfoFormat("WantsMount:倒地无法乘坐");
                return;
            }

            var canMountable = MoveStateMountable.Instance.CanStateEnter(player);// MoveStateController.Instance.CanEnter(PlayerMoveStateEnum.Mountable, player.PlayerLogicParams);
            if (canMountable == false)
            {
                logger.InfoFormat("WantsMount:状态机阻塞无法乘坐");
                return;
            }

            /*1. 根据mountableId获取载具*/
            if (Mc.Go.Gos.TryGetValue(mountableId, out var ego) == false)
            {
                logger.InfoFormat("WantsMount载具id:{0},不存在Go实例", mountableId);
                return;
            }

            if (ego.Entity.EntityType != EntityTypeId.ParachuteEntity)//降落伞不判断这个
            {
                var distance = Vector3.Distance(new Vector3(player.PosX, player.PosY, player.PosZ),
       ego.MainGo.transform.position);
                if (distance > 7)
                {
                    // 防止作弊远程上车，不用客户端上车碰撞体是因为无法保证双端位置相同
                    logger.InfoFormat("WantsMount:, distance:{0},太远无法乘坐", distance);
                    return;
                }
            }

            if (ego is BaseMountableGo mountableGo == false)
            {
                logger.InfoFormat("WantsMount载具id:{0},载具不是BaseMountableGo类型,type:{1}", mountableId, ego.GetType());
                return;
            }

            if (VehicleMountableUtil.CanMountViaAngle(mountableGo) == false)
            {
                logger.Info($"WantsMount:载具{mountableId}角度限制，player:{player.RoleId},name:{player.Name},roleid:{player.RoleId}");
                return;
            }

            if (VehicleMountableUtil.GetPermission(player, mountableGo.MountableEntity) != VehiclePermission.Operate)
            {
                logger.Info($"WantsMount:载具{mountableId}无权限，player:{player.RoleId},name:{player.Name},roleid:{player.RoleId}");
                return;
            }

            var entity = mountableGo.ParentGo.MountableEntity;
            if (entity.TryGetIHitable(out IHitableEntity hitable) && hitable.Hp <= 0)
            {
                logger.InfoFormat($"WantsMount:载具{mountableId}已死亡");
                return;
            }

            if (entity is HorseEntity horseEntity)
            {
                if (player.ProhibitMountHorse)
                {
                    player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 2, LanguageConst.ProhibitMountHorse);
                    return;
                }

                if (horseEntity.EquipSaddle == null)
                {
                    logger.Info("WantsMount:无马鞍,无法乘坐");
                    return;
                }

                bool seatBack = seatType == 2 || (seatType == 1 && seatIndex == 1);
                if (seatBack && horseEntity.EquipSaddle.TableId != 13030004)
                {
                    logger.InfoFormat($"WantsMount:非双人马鞍13030004,无法乘坐");
                    return;
                }
            }

            var info = mountableGo.GetMountPointInfo(seatIndex);
            if (seatType < 0 && info == null)
            {
                logger.InfoFormat("WantsMount载具id:{0},上车信息错误seatType:{1},seatIndex:{2}", mountableId, seatType, seatIndex);
                return;
            }

            var seatTypeEnum = (SeatType)seatType;
            if (info != null)
            {
                seatType = info.IsDrive ? (int)SeatType.Driver : (int)SeatType.Passenger;
            }

            if (mountableGo.CanMountPlayer(player.EntityId, seatTypeEnum, seatIndex) == false)
            {
                return;
            }

            var oldIsDriver = player.IsDriver;
            var oldMountableId = player.MountableId;
            var oldAttribution = entity.Attribution;
            mountableGo.MountPlayer(player.EntityId, seatTypeEnum, seatIndex);
            if (oldAttribution == BaseMountableEntity.Attribution_Unclaimed &&
                entity.Attribution == BaseMountableEntity.Attribution_Claimed)
            {
                player.RemoteCallShowTips(ERpcTarget.OwnClient, CommonTipConst.HorseClaimed);
            }

            player.TryRecordGetInVehicle(mountableGo.MountableEntity, seatTypeEnum == SeatType.Driver ? VehicleGetInReason.Driver : VehicleGetInReason.Passenger);
            if (player.IsDriver && !oldIsDriver)
            {
                player.RemoteCallMountEvent(ERpcTarget.World, ((IMountable)ego).TemplateId);
            }
            if (entity is IPurchasableMountableEntity ipme && ipme.JustBought && oldMountableId == 0 && player.MountableId != 0)
            {
                // 首次上飞机、牵马需要通知world服
                RpcEntity.Instance.RemoteCallFirstMountIPurchasableMountableEntity(ERpcTarget.World, entity.EntityId, player.RoleId);
            }
        }

        /// <summary>
        /// 玩家离开可乘坐物(载具)
        /// </summary>
        [RpcHandler(ExposeToClient = true)]
        public void WantsDismount()
        {
            DismountWithParam("WantsDismount", DismountFailedResponse.PopMsg, VehicleGetOffReason.Dismount);
        }

        private void DismountWithParam(string action, DismountFailedResponse response, VehicleGetOffReason reason)
        {
            var roleId = RpcContext.CurrentRoleId;

            /* 0. 根据playerId获取玩家实体 */
            var player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (player == null)
            {
                logger.InfoFormat($"{action}不存在id:{0}的玩家实体", roleId);
                return;
            }

            var playerId = player.EntityId;
            logger.InfoFormat($"收到RPC消息{action},playerId:{playerId}");

            /*1. 根据玩家获取载具*/
            var mountableId = player.MountableId;
            if (mountableId <= 0)
            {
                logger.InfoFormat($"{action}载具id:{0}为0", mountableId);
                return;
            }

            if (Mc.Go.Gos.TryGetValue(mountableId, out var ego) == false)
            {
                logger.InfoFormat($"{action}载具id:{0},不存在Go实例", mountableId);
                return;
            }

            if (ego is BaseMountableGo mountableGo == false)
            {
                logger.InfoFormat($"{action}载具id:{0},载具不是BaseMountableGo类型,type:{1}", mountableId,
                    ego.GetType());
                return;
            }

            mountableGo.TryDismountPlayer(player.EntityId, response, reason);
        }

        /// <summary>
        /// 电镐充能
        /// </summary>
        [RpcHandler(ExposeToClient = true)]
        public void JackHammerCharge(long jackhammerId, long workbenchID)
        {
            var roleId = RpcContext.CurrentRoleId;
            var player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (player == null)
            {
                logger.WarnFormat("JackHammerCharge不存在id:{0}的玩家实体", roleId);
                return;
            }
            var playerId = player.EntityId;

            IRechargeableEntity jackhammer = player.GetCustomTypeById(jackhammerId) as IRechargeableEntity;
            if (jackhammer == null)
            {
                logger.WarnFormat("{0}的玩家实体 没有这个 {1} 的充能道具", playerId, jackhammerId);
                return;
            }

            if (!jackhammer.CanRecharge)
            {
                logger.WarnFormat("{0}的玩家实体 这个 {1} 的武器不可充能，TableId：{2}", playerId, jackhammerId, jackhammer.TableId);
                return;
            }

            PartEntity workbench = Mc.Entity.GetPartEntity(workbenchID);
            if (workbench == null || !WorkBenchUtil.IsWorkBench((int)workbench.TemplateId))
            {
                logger.WarnFormat("{0}的玩家实体 充能 {1} ,工作台 {2} 不存在 ，TableId：{3}", playerId, jackhammerId, workbenchID, jackhammer.TableId);
                return;
            }

            Vector3 playerpos = new Vector3(player.PosX, player.PosY, player.PosZ);
            Vector3 workbenchpos = new Vector3(workbench.PosX, workbench.PosY, workbench.PosZ);
            if (Vector3.Distance(playerpos, workbenchpos) > Mc.Tables.TbGlobalConfig.WorkBenchSearchDistance + 1)
            {
                logger.WarnFormat("{0}的玩家实体 充能 {1} ,工作台 {2} 和玩家距离{3}超过 {4}",
                   playerId, jackhammerId, workbenchID, Vector3.Distance(playerpos, workbenchpos), Mc.Tables.TbGlobalConfig.WorkBenchSearchDistance);
                return;
            }

            float condition = ItemUtility.FormulaCalcNewMaxCondition(jackhammer.Condition, jackhammer.MaxCondition,
                McCommon.Tables.TbGlobalConfig.RechargeConditionLoss);
            var isBroken = jackhammer.isBroken;
            jackhammer.Condition = jackhammer.MaxCondition = condition;
            if (isBroken)
            {
                jackhammer.OnRepaired();
            }

            logger.InfoFormat("JackHammerCharge playerId :{0},entityId: {1},workbench:{2},耐久：{3},tableid:{4}",
                playerId, jackhammerId, workbenchID, condition, jackhammer.TableId);
        }

        [RpcHandler(ExposeToClient = true)]
        public void WantsPush(long mountableId)
        {
            var roleId = RpcContext.CurrentRoleId;

            /* 0. 根据playerId获取玩家实体 */
            var player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (player == null)
            {
                logger.InfoFormat("WantsPush不存在id:{0}的玩家实体", roleId);
                return;
            }

            var playerId = player.EntityId;
            logger.InfoFormat($"收到RPC消息WantsPush,playerId:{playerId}, mountableId:{mountableId}");

            if (!player.CanOperateMount)
            {
                logger.InfoFormat("倒地无法推车");
                return;
            }

            if (player.MountableId > 0)
            {
                logger.InfoFormat("WantsPush人物在车驾驶舱内不能推车");
                return;
            }

            /*1. 根据mountableId获取载具*/
            if (Mc.Go.Gos.TryGetValue(mountableId, out var ego) == false)
            {
                logger.InfoFormat("WantsPush载具id:{0},不存在Go实例", mountableId);
                return;
            }

            if (ego is BaseMountableGo mountableGo == false)
            {
                return;
            }

            if (mountableGo.ParentGo.HasDriver())
            {
                player.RemoteCallShowTips(ERpcTarget.OwnClient, CommonTipConst.PushVehicleHint);
                return;
            }

            var mainTransform = mountableGo.MainTransform;
            if (mainTransform != null)
            {
                float distanceLimit = 7;
                float distance = Vector3.Distance(mainTransform.position, new Vector3(player.PosX, player.PosY, player.PosZ));
                if (distance > distanceLimit)
                {
                    logger.InfoFormat("WantsPush人物与车距离大于{0},不能推车", distanceLimit);
                    return;
                }
            }

            ((IMountable)ego).WantsPush(player);
        }

        [RpcHandler(ExposeToClient = true)]
        public void EscapeFromStuck()
        {
            var entity = this;
            if (entity.IsOffline || !entity.IsAlive || entity.IsOnMount)
            {
                return;
            }

            var current = TimeStampUtil.GetNowTimeStampMSec();
            if (current < entity.EscapeFromStuckValidTime)
            {
                return;
            }

            if (!Mc.Go.TyrGetGo(entity.EntityId, out PlayerGo player))
            {
                return;
            }

            player.MainGo.transform.localScale = Vector3.one; // 这句拷贝自#17857，原因不记得了

            var success = player.TryGetEscapePosition(out var position);
            if (success)
            {
                UnityUtility.ServerSetCcPositionWithEvent(Mc.SnapshotReceiver.NowSnapshotSequence, player.SocCharacterController, entity, position);
            }
            entity.EscapeFromStuckCount += 1;
            var cd = McCommon.Tables.TbGlobalConfig.EscapeInitCD +
                     (entity.EscapeFromStuckCount - 1) * McCommon.Tables.TbGlobalConfig.EscapeOverlayCD;
            cd = Mathf.Min(cd, 3600);
            entity.EscapeFromStuckValidTime = TimeStampUtil.GetNowTimeStampMSec() + cd * 1000;
        }

        [RpcHandler(ExposeToClient = true)]
        /// followOrCancel: true表示跟随，false表示取消跟随
        public void HorseFollow(long mountableId, bool followOrCancel)
        {
            /* 马匹跟随*/
            var roleId = RpcContext.CurrentRoleId;

            /* 0. 根据playerId获取玩家实体 */
            var player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (player == null)
            {
                logger.InfoFormat("HorseFollow不存在id:{0}的玩家实体", roleId);
                return;
            }

            var playerId = player.EntityId;

            logger.InfoFormat($"收到RPC消息HorseFollow,playerId:{playerId}, mountableId:{mountableId}");
            if (!player.CanOperateMount)
            {
                logger.InfoFormat("倒地无法操作");
                return;
            }

            /*1. 根据mountableId获取载具*/
            if (Mc.Go.Gos.TryGetValue(mountableId, out var ego) == false)
            {
                logger.InfoFormat("HorseFollow载具id:{0},不存在Go实例", mountableId);
                return;
            }

            if (ego is ServerSocHorseGo horseGo == false)
            {
                logger.InfoFormat("HorseFollow,载具id:{0},不是BaseHorseGo", mountableId);
                return;
            }

            if (VehicleMountableUtil.GetPermission(player, horseGo.Entity as HorseEntity) != VehiclePermission.Operate)
            {
                logger.Info($"HorseFollow:{mountableId}无权限，player:{player.RoleId},name:{player.Name},roleid:{player.RoleId}");
                return;
            }

            var distance = Vector3.Distance(new Vector3(player.PosX, player.PosY, player.PosZ), ego.MainGo.transform.position);
            if (distance > 7)
            {
                logger.InfoFormat("HorseFollow:, distance:{0},太远无法操作", distance);
                return;
            }

            logger.InfoFormat($"马匹跟随玩家，playerId:{playerId}, mountableId:{mountableId}");
            if (followOrCancel)
            {
                horseGo.StartFollow(player);
            }
            else
            {
                horseGo.TryStopFollow(player);
            }
        }

        [RpcHandler]
        public void HorseFeedAck(long horseEntityId, long foodTemplateId, int count, int code)
        {
            var horseEntity = Mc.Entity.GetEntity(horseEntityId) as HorseEntity;
            if (horseEntity == null)
            {
                logger.InfoFormat("HorseFeedAck id:{0},不存在entity", horseEntityId);
                return;
            }

            // horseEntity.InEat = false;
            if (code != (int)EOpCode.Success)
            {
                logger.InfoFormat("HorseFeedAck 不成功，{0},{1},{2},{3}", horseEntityId, foodTemplateId, count, code);
                return;
            }

            /* 获取马匹Go*/
            if (Mc.Go.Gos.TryGetValue(horseEntityId, out var go) == false)
            {
                logger.InfoFormat("HorseFeedAck 找不到{0}对应Go", horseEntityId);
                return;
            }
            if (go is ServerSocHorseGo horseGo == false)
            {
                logger.InfoFormat("HorseFeedAck 找不到{0}对应的ServerHorseGo", horseEntityId);
                return;
            }

            horseGo.OnHorseEatFoodAck(code, foodTemplateId, count, true);
        }

        [RpcHandler(ExposeToClient = true, CallInterval = 1f)]
        public void HorseFeed(long horseEntityId, long foodTemplateId)
        {
            var roleId = RpcContext.CurrentRoleId;
            var player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (player == null)
            {
                logger.InfoFormat("HorseFeed不存在id:{0}的玩家实体", roleId);
                return;
            }

            var playerId = player.EntityId;
            logger.InfoFormat($"收到RPC消息HorseFeed,playerId:{playerId}, horseEntityId:{horseEntityId}");
            var horseEntity = Mc.Entity.GetEntity(horseEntityId) as HorseEntity;
            if (horseEntity == null)
            {
                logger.InfoFormat("HorseFeed载具id:{0},不存在entity", horseEntityId);
                return;
            }
            if (horseEntity.CanFeed(player) == false)
            {
                return;
            }

            var distance = Vector3.Distance(new Vector3(player.PosX, player.PosY, player.PosZ),
                new Vector3(horseEntity.PosX, horseEntity.PosY, horseEntity.PosZ));
            if (distance > 7)
            {
                logger.InfoFormat("HorseFeed:, distance:{0},太远无法乘坐", distance);
                return;
            }

            if (VehicleMountableUtil.GetPermission(player, horseEntity) != VehiclePermission.Operate)
            {
                logger.Info($"HorseFeed:{horseEntityId}无权限，player:{player.RoleId},name:{player.Name},roleid:{player.RoleId}");
                return;
            }
            RemoteCallSimulatorHorseFeed(ERpcTarget.World, horseEntityId, (int)foodTemplateId, 1);
        }

        [RpcHandler(ExposeToClient = true)]
        public void OpenVehicleLight(bool isOpen)
        {
            var playerEntity = Mc.Entity.GetPlayerEntityByRoleId(RpcContext.CurrentRoleId);
            if (playerEntity == null)
            {
                return;
            }

            if (playerEntity.IsOnMount == false)
            {
                logger.InfoFormat("OpenVehicleLight 玩家不在载具上，不能控制车灯");
                return;
            }

            if (playerEntity.IsDriver == false)
            {
                logger.InfoFormat("OpenVehicleLight 玩家不在司机位，不能控制车灯");
                return;
            }

            var mountableId = playerEntity.MountableId;
            /*1. 根据mountableId获取载具*/
            if (Mc.Go.Gos.TryGetValue(mountableId, out var ego) == false)
            {
                logger.ErrorFormat("OpenVehicleLight载具id:{0},不存在Go实例", mountableId);
                return;
            }

            if (ego is BaseVehicleGo vehicleGo == false)
            {
                logger.ErrorFormat("OpenVehicleLight载具id:{0},不是BaseVehicleGo", mountableId);
                return;
            }

            vehicleGo.OpenVehicleLight(isOpen);
        }

        /// <summary>
        /// 模块间换座
        /// </summary>
        /// <param name="playerId"></param>
        /// <param name="mountableId"></param>
        /// <param name="seatIndex"></param>
        [RpcHandler(ExposeToClient = true)]
        public void ModuleSwitchMountSeat(long targetModuleId, int targetSeatIndex)
        {
            var roleId = RpcContext.CurrentRoleId;

            /* 0. 根据playerId获取玩家实体 */
            var player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (player == null)
            {
                logger.InfoFormat("ModuleSwitchMountSeat不存在id:{0}的玩家实体", roleId);
                return;
            }

            MountableUtil.ModuleSwitchMountSeat(targetModuleId, targetSeatIndex, player);
        }

        /// <summary>
        /// 开关装甲模块窗口
        /// </summary>
        [RpcHandler(ExposeToClient = true)]
        public void OpenArmorModuleWindow(bool isOpen)
        {
            var roleId = RpcContext.CurrentRoleId;
            /* 0. 根据playerId获取玩家实体 */
            var player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (player == null)
            {
                logger.ErrorFormat("OpenArmorModuleWindow不存在id:{0}的玩家实体", roleId);
                return;
            }
            var playerId = player.EntityId;
            var currentModuleId = player.MountableId;
            var seatIndex = player.MountSeatIndex;
            logger.InfoFormat($"收到RPC消息OpenArmorModuleWindow,打开装甲模块窗户,playerId:{playerId}, 当前模块：{currentModuleId}, 座位：{seatIndex}, isOpen:{isOpen}");
            /* 玩家是否在载具上*/
            if (player.IsOnMount == false)
            {
                return;
            }

            /* 获取当前模块*/
            if (Mc.Go.Gos.TryGetValue(currentModuleId, out var curGo) == false)
            {
                logger.ErrorFormat("OpenArmorModuleWindow载具id:{0},不存在当前Go实例", currentModuleId);
                return;
            }

            if (curGo is ServerVehicleModuleGo curVehicleModuleGo == false)
            {
                return;
            }

            /* 座位索引是否合法（只能是0或1）*/
            if (seatIndex != 0 && seatIndex != 1)
            {
                logger.ErrorFormat("OpenArmorModuleWindow载具id:{0},座位{1}不合法，座位索引只能是0或1", currentModuleId, seatIndex);
                return;
            }

            /* 是否是装甲模块*/
            if (curVehicleModuleGo.ModuleInfo.ModuleType.Contains(VehicleModuleType.Armor) == false)
            {
                logger.ErrorFormat("OpenArmorModuleWindow载具id:{0},不是装甲模块", currentModuleId);
                return;
            }

            curVehicleModuleGo.OpenArmorModuleWindow(seatIndex, isOpen);
        }

        [RpcHandler(ExposeToClient = true)]
        public void RowBoatEngineToggle(bool isOn)
        {
            var roleId = RpcContext.CurrentRoleId;
            logger.InfoFormat($"收到RPC消息RowBoatEngineToggle, roleId:{roleId}");
            /* 0. 根据playerId获取玩家实体 */
            var player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (player == null)
            {
                logger.Error("RowBoatEngineToggle不存在的玩家实体");
                return;
            }

            logger.InfoFormat($"收到RPC消息RowBoatEngineToggle, player:{player.EntityId}, isOn:{isOn}");
            if (player.MountableId <= 0)
            {
                return;
            }

            if (Mc.Go.GetGo(player.MountableId) is BaseVehicleGo vehicleGo == false)
            {
                return;
            }

            if (vehicleGo.VehicleType != WizardGames.Soc.Common.Data.VehicleType.RowBoat)
            {
                return;
            }

            vehicleGo.EngineToggle(isOn);
        }

        [RpcHandler(ExposeToClient = true)]
        public void UpdateForagingState(long horseId)
        {
            if (Mc.Go.Gos.TryGetValue(horseId, out var go) == false)
            {
                return;
            }
            if (go is ServerSocHorseGo horseGo == false)
            {
                return;
            }
            horseGo.StartForagingState();
        }

        [RpcHandler]
        public void OnInventoryChangeBullet(long weaponEntityId, long reloadAmmoId, long reloadAmmoNodeId)
        {
            var weapon = Mc.Entity.GetEntity(weaponEntityId) as WeaponCustom;
            if (weapon == null)
            {
                return;
            }
            //FastReload(weapon, reloadAmmoId, reloadAmmoNodeId);
        }


        private void MonsterInitShootPointObject()
        {
            if (McSimulator.Go.GetGo(EntityId) is PlayerGo playerGo)
            {
                var binder = playerGo.MainGo.GetComponentInChildren<MonsterPointBinder>(true);
                MonsterInitShootPointObjectFromBinder(binder);
            }
        }

        private void MonsterInitShootPointObjectFromBinder(MonsterPointBinder binder)
        {
            if (binder == null)
            {
                return;
            }
            var binders = binder.GetComponents<MonsterPointBinder>();
            if (binders.Length < 1)
            {
                return;
            }

            standShootPoint = BindShootPosObject(binders[0], 0);
            crouchShootPoint = BindShootPosObject(binders[1], 1);
        }

        private GameObject BindShootPosObject(MonsterPointBinder binder, int index)
        {
            var binderData = binder.GetPoint(Data.Monster.PointName.Body);
            if (binderData != null && binderData.shootPointDatas.Count > 0)
            {
                return binderData.shootPointDatas[0].shootPoint.gameObject;
            }
            return null;
        }

        public bool TryGetShootPos(bool isStand, out Vector3 pos)
        {
            if (isStand && standShootPoint != null)
            {
                pos = standShootPoint.transform.position;
                return true;
            }
            else if (!isStand && crouchShootPoint != null)
            {
                pos = crouchShootPoint.transform.position;
                return true;
            }
            pos = Vector3.zero;
            return false;
        }


        public void GetFirePositionAndOffset(UserCmd cmd, out System.Numerics.Vector3 pos, out System.Numerics.Vector3 offset)
        {
            offset = new System.Numerics.Vector3(0, Height, 0);
            HistorySnapshot history = Mc.SnapshotReceiver.ArchiveHistory.Last;
            var newpos = pos = new System.Numerics.Vector3(PosX, PosY, PosZ);
            if (history != null && history.TryGetHistory(EntityId, out HistoryPlayerEntity lastModel))
            {
                float factor = UnityEngine.Mathf.Clamp01((cmd.ClientSmoothRenderTime - lastModel.ClientTime) * 1.0f / (ClientTime - lastModel.ClientTime));

                var lastPosX = lastModel.PosX;
                var lastPosY = lastModel.PosY;
                var lastPosZ = lastModel.PosZ;

                var linearLerpX = (1 - factor) * lastPosX + pos.X * factor;
                var linearLerpY = (1 - factor) * lastPosY + pos.Y * factor;
                var linearLerpZ = (1 - factor) * lastPosZ + pos.Z * factor;

                var height = (1 - factor) * lastModel.Height + Height * factor;
                pos = new System.Numerics.Vector3(linearLerpX, linearLerpY, linearLerpZ);
                offset = new System.Numerics.Vector3(0, height, 0);
                //logger.Error($"lastModel.ClientTime：{lastModel.ClientTime}，cmd.ClientTime：{ClientTime}，factor：{factor}，pos:{pos},offset:{offset},lastModel.PosX：{lastModel.PosX}，lastModel.PosY：{lastModel.PosY}，lastModel.PosZ：{lastModel.PosZ}，newpos.x：{newpos.X}，newpos.y：{newpos.Y}，newpos.z：{newpos.Z}，lastheight:{lastModel.Height},Height:{Height},frame:{UnityEngine.Time.frameCount}");
            }
        }

        [RpcHandler(ExposeToClient = true)]
        public void OnTeamMemberChange()
        {
            McSimulator.IOEntity.SetPlayerTrackTask(this, MonumentTaskGroupId, MonumentTaskId);
        }

        [RpcHandler(ExposeToClient = true)]
        public void OnTriggerInteract(long interactEntityId)
        {
            var interactEntity = Mc.Entity.GetEntity(interactEntityId);
            if (interactEntity is IOEntity io)
            {
                var ioConfig = McCommon.Tables.TbIOInteractive.GetOrDefault(io.TemplateId);
                if (ioConfig.DurabilityDeductionItemID > 0)
                {
                    logger.InfoFormat("Cannot interact without item deduct {0} {1}, use ConsumeItemComponent.TryInteract", io.TemplateId, io.EntityId);
                    return;
                }
                McSimulator.IOEntity.TrrigerIOEntityInteract(io, RoleId);
            }
        }

        [RpcHandler(ExposeToClient = true)]
        public void HasTreasuretTaskTracked(long interactEntityId)
        {
            var roleId = RpcContext.CurrentRoleId;
            var player = Mc.Entity.GetPlayerEntityByRoleId(roleId);

            IReadOnlyDictionary<long, TrainDischarge> map = McCommon.Tables.TbTrainDischarge.DataMap;

            IOEntity ioEntity = Mc.Entity.GetEntity(interactEntityId) as IOEntity;
            long unloadArea = (from item in map where ioEntity != null && item.Value.UnloadTrigger == ioEntity.TemplateId select item.Value.UnloadArea).FirstOrDefault();

            IOEntity unloadAreaEntity = null;
            Dictionary<long, IOEntity> ioEntities = Mc.Entity.GetEntitiesViaType<IOEntity>();
            using Dictionary<long, IOEntity>.Enumerator enumerator = ioEntities.GetEnumerator();
            while (enumerator.MoveNext())
            {
                IOEntity entity = enumerator.Current.Value;
                if (entity.TemplateId == unloadArea && entity.Activation == (int)EIOActivationStatusEnum.Activation)
                {
                    unloadAreaEntity = entity;
                    break;
                }
            }

            if (unloadAreaEntity != null)
            {
                FixPointSpawner.Instance.TrainCarEntities.TryGetValue(unloadAreaEntity.HeadEntityId,
                    out LinkedList<TrainCarEntity> trainCarEntities);

                if (trainCarEntities != null)
                {
                    float totalHp = 0;
                    float totalMaxHp = 0;
                    foreach (TrainCarEntity trainCarEntity in trainCarEntities)
                    {
                        totalHp += trainCarEntity.Hp;
                        totalMaxHp += trainCarEntity.MaxHp;
                    }

                    float perfectDeliveryHpPercent = McCommon.Tables.TbQuestConst.PerfectDeliveryHPPercent;
                    bool isHpEnough = totalHp >= totalMaxHp * perfectDeliveryHpPercent * 0.01f;

                    if (!isHpEnough)
                    {
                        player.RemoteCallShowTips(ERpcTarget.OwnClient, 24201);
                    }

                    if (trainCarEntities.Count < 3) player.RemoteCallShowTips(ERpcTarget.OwnClient, 24202);
                }
            }
        }

        [RpcHandler(ExposeToClient = true)]
        public void OnTriggerInteractTrain(long interactEntityId, int currentSelected)
        {
            var interactEntity = Mc.Entity.GetEntity(interactEntityId);
            if (interactEntity is IOEntity io)
            {
                var ioConfig = McCommon.Tables.TbIOInteractive.GetOrDefault(io.TemplateId);
                if (ioConfig.DurabilityDeductionItemID > 0)
                {
                    logger.InfoFormat(
                        "Cannot interact without item deduct {0} {1}, use ConsumeItemComponent.TryInteract",
                        io.TemplateId, io.EntityId);
                    return;
                }

                switch (currentSelected)
                {
                    case 0:
                        io.CurrentSelected = ETrainUnloadType.MANUAL;
                        break;
                    case 1:
                        io.CurrentSelected = ETrainUnloadType.PERMISSION_CARD;
                        break;
                    case 2:
                        io.CurrentSelected = ETrainUnloadType.HIGH_PERMISSION_CARD;
                        break;
                    default:
                        io.CurrentSelected = ETrainUnloadType.MANUAL;
                        logger.ErrorFormat("OnTriggerInteractTrain currentSelected:{0} is invalid", currentSelected);
                        break;
                }

                IOGo ioGo = Mc.Go.GetGo(io.EntityId) as IOGo;
                ioGo?.TrrigerUIInteractTrain(RoleId);
            }
        }

        [RpcHandler(ExposeToClient = true)]
        public void Ping()
        {
            RemoteCallPingCallback(ERpcTarget.OwnClient);
        }

        [RpcHandler]
        public void OnLeaveTeam(BasicTypeHashSet<long> lockVehicleIds, BasicTypeHashSet<long> buyVehicles)
        {
            if (IsOnMount == false)
            {
                return;
            }

            if (lockVehicleIds.Count == 0 && buyVehicles.Count == 0)
            {
                return;
            }

            var mountableGo = Mc.Go.GetGo<BaseMountableGo>(MountableId);
            if (mountableGo == null)
            {
                logger.Error($"OnLeaveTeam {MountableId} mountableGo is null");
                return;
            }

            var mountableEntityId = mountableGo.ParentGo != null ? mountableGo.ParentGo.EntityId : mountableGo.EntityId;
            if (lockVehicleIds.Contains(mountableEntityId) ||
                (buyVehicles.Contains(mountableEntityId) && mountableGo.MountableEntity.RemainLimitedAuthorityTs > 0)
                )
            {
                mountableGo.TryDismountPlayer(EntityId, DismountFailedResponse.SetMountPos, VehicleGetOffReason.LeaveTeam);
            }
        }

        [RpcHandler(ExposeToClient = true)]
        public void EnterCampingTent(long tentId)
        {
            var roleId = RpcContext.CurrentRoleId;
            var player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (player == null)
            {
                logger.ErrorFormat("EnterTent不存在id:{0}的PlayerEntity", roleId);
                return;
            }

            if (!McSimulator.Go.TyrGetGo(player.EntityId, out PlayerGo playerGo))
            {
                logger.ErrorFormat("EnterTent不存在id:{0}的PlayerGo", roleId);
                return;
            }

            // TODO 帐篷是否是自己搭建的
            // TODO 获取帐篷指定睡觉位置
            var pos = playerGo.MainGo.transform.position;

            playerGo.SocCharacterController.SetEnable(false);
            UnityUtility.ServerSetCcPosition(Mc.SnapshotReceiver.NowSnapshotSequence,
                playerGo.SocCharacterController, player, pos);
            playerGo.SocCharacterController.SetEnable(true);

            player.LifeCycleFlags |= LifeFlags.InCampingTent;
        }

        [RpcHandler]
        public void ChangeCurHandle(long itemEntityId)
        {
            var itemEntity = GetCustomTypeById(itemEntityId) as IItemEntity;
            if (itemEntity != null)
            {
                CurrentWeaponId = itemEntity.EntityId;
            }
        }

        [RpcHandler(ExposeToClient = true)]
        public void RepairPartOrVehicle(long targetEntityId)
        {
            McSimulator.Construction.TryRepairEntity(this, targetEntityId);
        }


        /// <summary>
        /// 拆除地雷
        /// </summary>
        [RpcHandler(ExposeToClient = true)]
        public void Defuse(SimulatorPickUpRequest req)
        {
            var roleId = RpcContext.CurrentRoleId;
            logger.InfoFormat("[Defuse]roleId: {0}, itemUid:{1} {2}", roleId, req.ItemNodeId, req.TargetPath);

            if (!Mc.Go.Gos.TryGetValue(req.EntityId, out var entityGo))
            {
                logger.ErrorFormat($"[Defuse]GameObject not found! EntityId: {{req.EntityId}}");
                return;
            }

            if (entityGo is ServerThrownEntityGo go)
            {
                go.Defuse(roleId, this, req);
            }
        }

        [RpcHandler(ExposeToClient = true)]
        public void TriggerOverlap(long entityid)
        {
            if (!Mc.Go.Gos.TryGetValue(entityid, out var entityGo))
                return;
            if (entityGo is ServerThrownEntityGo go)
                go.TriggerOverlap();
        }


        [RpcHandler]
        public void TestSetPlayerDamageDisable(bool disable)
        {
            SetInvincible(disable);
        }
        public void SetInvincible(bool isInvincible)
        {
            logger.InfoFormat("SetInvincible {0}", isInvincible);
            var dc = GetComponent(EComponentIdEnum.Damageable) as IHitableEntity;
            if (dc == null)
                return;
            dc.IsInvincible = isInvincible;
        }

        [RpcHandler]
        public void GMRequirePathNodeSim(float srcX, float srcY, float srcZ, float tarX, float tarY, float tarZ,
            ulong roleId)
        {
            Vector3 srcV3 = new Vector3(srcX, srcY, srcZ);
            Vector3 tarV3 = new Vector3(tarX, tarY, tarZ);
            List<Vector3> nodes = EntityGoUtility.CalculatePath(srcV3, tarV3);
            List<float> nodeX = new();
            List<float> nodeY = new();
            List<float> nodeZ = new();
            foreach (var n in nodes)
            {
                nodeX.Add(n.x);
                nodeY.Add(n.y);
                nodeZ.Add(n.z);
            }

            PlayerEntity player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            player.RemoteCallTestReceivePathNodes(ERpcTarget.OwnClient, nodeX, nodeY, nodeZ);
        }


        /// <summary>
        /// 设置玩家位置
        /// </summary>
        [RpcHandler]
        public void TestSetPlayerPosition(long entityId, float posX, float posY, float posZ, bool checkPosY)
        {
            var entity = Mc.Entity.GetPlayerEntity(entityId);
            if (entity == null)
                return;
            if (checkPosY)
            {
                posY = Mc.Terrain.GetPosYInWorldSpace(posX, posZ);
                var upperPos = new Vector3(posX, posY + 10, posZ);
                if (Physics.Raycast(upperPos, Vector3.down, out var hitInfo, 100, MgrConfigPhysicsLayer.MaskScene,
                        QueryTriggerInteraction.Ignore) && hitInfo.collider != null)
                {
                    posY = hitInfo.point.y;
                }
            }

            void Action()
            {
                var userId = entity.RoleId;
                if (!McSimulator.Go.TyrGetGo(entityId, out PlayerGo player))
                {
                    return;
                }
                var position = new Vector3(posX, posY, posZ);

                if (entity.IsOnline)
                {
                    entity.RemoteCallOnTeleport(ERpcTarget.OwnClient);
                }

                //transform.position = new Vector3(posX, posY, posZ);
                entity.ObserverLoadingFinishTime = McCommon.Time.ServerWorldTime + McCommonUnity.Tables.TbGlobalConfig.MaximumInvincibilityTimeAtResurrection * 1000;
                UnityUtility.ServerSetCcPositionWithEvent(Mc.SnapshotReceiver.NowSnapshotSequence, player.SocCharacterController, entity, position);
            }

            if (entity.IsOnMount)
            {
                if (Mc.Go.Gos.TryGetValue(entity.MountableId, out var ego) == false)
                {
                    Action();
                    logger.ErrorFormat("WantsDismount载具id:{0},不存在Go实例", entity.MountableId);
                    return;
                }

                ((IMountable)ego).TryDismountPlayer(entityId, DismountFailedResponse.SetMountPos, VehicleGetOffReason.Dismount);
                Action();
                // var request = GetOutVehicleRequest.Generate(entityId, null, Action);
                // ((SimulatorContext)McCommon.System.Context).GetOutVehicleRequests.Enqueue(ref request);
            }
            else
            {
                Action();
            }
        }

        /// <summary>
        /// 把玩家传到另一个玩家身后
        /// </summary>
        [RpcHandler]
        public void TestSetToOtherPlayerPosition(long entityId, ulong roleId, bool checkPosY)
        {
            var entity = Mc.Entity.GetPlayerEntity(entityId);
            if (entity == null)
                return;
            var otherEntity = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            var pos = new Vector3(otherEntity.PosX, otherEntity.PosY, otherEntity.PosZ);
            var qua = Quaternion.Euler(-otherEntity.RotateX, otherEntity.RotateY, 0);//为什么不用玩家的rotation？
            var forward = qua.Froward();
            pos -= forward.normalized * 5.0f;
            var posX = pos.x;
            var posY = pos.y + 1f;
            var posZ = pos.z;
            if (checkPosY)
            {
                posY = Mc.Terrain.GetPosYInWorldSpace(posX, posZ);
                var upperPos = new Vector3(posX, posY + 10, posZ);
                if (Physics.Raycast(upperPos, Vector3.down, out var hitInfo, 100, MgrConfigPhysicsLayer.MaskScene,
                        QueryTriggerInteraction.Ignore) && hitInfo.collider != null)
                {
                    posY = hitInfo.point.y;
                }
            }

            void Action()
            {
                var userId = entity.RoleId;
                if (!McSimulator.Go.TyrGetGo(entityId, out PlayerGo player))
                    return;
                var position = new Vector3(posX, posY, posZ);
                if (entity.IsOnline)
                {
                    entity.RemoteCallOnTeleport(ERpcTarget.OwnClient);
                }
                //transform.position = new Vector3(posX, posY, posZ);
                entity.ObserverLoadingFinishTime = McCommon.Time.ServerWorldTime + McCommonUnity.Tables.TbGlobalConfig.MaximumInvincibilityTimeAtResurrection * 1000;
                UnityUtility.ServerSetCcPositionWithEvent(Mc.SnapshotReceiver.NowSnapshotSequence, player.SocCharacterController, entity, position);
            }

            if (entity.IsOnMount)
            {
                if (Mc.Go.Gos.TryGetValue(entity.MountableId, out var ego) == false)
                {
                    Action();
                    logger.ErrorFormat("WantsDismount载具id:{0},不存在Go实例", entity.MountableId);
                    return;
                }

                ((IMountable)ego).TryDismountPlayer(entityId, DismountFailedResponse.SetMountPos, VehicleGetOffReason.Dismount);
                Action();
                // var request = GetOutVehicleRequest.Generate(entityId, null, Action);
                // ((SimulatorContext)McCommon.System.Context).GetOutVehicleRequests.Enqueue(ref request);
            }
            else
            {
                Action();
            }
        }
        #region 换弹逻辑

        [RpcHandler(ExposeToClient = true)]
        public void StartReloadNotInHand(long heldItemEntityId, int ammoId, long nodeid, int expectTime)
        {//快捷栏非手持武器换子弹,expectTime需要客户端透传期望时间，因为sim端没有背包
            //logger.Error($"reload StartReloadNotInHand heldItemEntityId:{heldItemEntityId} ReloadSequence:{ReloadSequence},ReloadingHeldItemID:{ReloadingHeldItemID}");
            if (ReloadSequence != -1)
                return; //如果已经在换弹过程中，不允许这种换弹流程
            IHaveBulletEntity reloadable = this.GetHeldItemByEntityId(heldItemEntityId) as IHaveBulletEntity;
            if (reloadable == null)
            {
                return;
            }

            reloadable.IsEmptyReload = reloadable.EmptyClip;
            reloadable.LastAmmoId = reloadable.UsingAmmoId;
            reloadable.UsingAmmoId = ammoId;
            reloadable.UsingAmmoNodeId = nodeid;
            this.InterruptNotInHandReload = false;
            StartReloadLoop(this, reloadable, 0, reloadable.ClipCapacity);
            if (NotInHandReloadTimer != -1)
            {
                Mc.TimerWheel.CancelTimer(NotInHandReloadTimer);
                NotInHandReloadTimer = -1;
            }
            RemoteCallReloadNotInHand(ERpcTarget.OwnClient, reloadable.EntityId, reloadable.TableId, expectTime * 0.001f, true);
            NotInHandReloadTimer = Mc.TimerWheel.AddTimerOnce(expectTime,
                (_, _, _) =>
                {
                    if (InterruptNotInHandReload) InterruptNotInHandReload = false;
                    else ReloadSetClip(this, reloadable);
                });
            //logger.Error($"[Reload] StartReloadNotInHand:{NotInHandReloadTimer}");
        }

        public void StartReloadLoop(PlayerEntity player, IHaveBulletEntity weapon, long seqID, int maxAmmo = 0)
        {
            IHaveBulletEntity currentReloading = GetHeldItemByEntityId(ReloadingHeldItemID) as IHaveBulletEntity;
            ReloadInterrupt(player, currentReloading);//清空已经存在的换弹过程
            weapon.IsReloading = true;
            this.ReloadSequence = (int)seqID;
            this.ReloadingHeldItemID = weapon.EntityId;
            //logger.Error($"reload StartReloadLoop ReloadingHeldItemID:{ReloadingHeldItemID}");
            this.ReloadRetrieveAmmoBizId = weapon.LastAmmoId;
            this.ReloadRetrieveAmmoCount = weapon.Clips; //记录换弹前弹夹中的子弹信息

            int reloadClipNum = maxAmmo <= 0 ? weapon.SingleReloadClipNum : maxAmmo; //单次换弹数
            int want;
            if (weapon.LastAmmoId != weapon.UsingAmmoId)
            {
                //如果换不同弹药，需要将原子弹换出
                weapon.Clips = 0;
                want = reloadClipNum;
                weapon.StepReloadingAmmoAmount = weapon.ClipCapacity;
            }
            else
            {
                if (weapon.Clips + reloadClipNum > weapon.ClipCapacity)
                {
                    //如果这次换弹超出容量
                    want = weapon.ClipCapacity - weapon.Clips;
                }
                else
                {
                    want = reloadClipNum;
                }

                weapon.StepReloadingAmmoAmount = weapon.ClipCapacity - weapon.Clips;
            }
            if (weapon.UsingAmmoId == 0)
                logger.ErrorFormat("[Reload] nextAmmoId==0, error!\ntrace:{0}", System.Environment.StackTrace);
            else
            {
                McCommonUnity.ThreadTask.PostToMainThread(() =>
                {//帧末向world发送换弹请求，只有seqid对应时，说明先前没有打断
                    if (this.ReloadSequence != -1 && this.ReloadSequence == seqID)
                    {
                        logger.InfoFormat("[Reload] 开始换弹, seqID:{0}, player:{1}, weapon:{2}, ammo:{3}, want:{4},snapshot:{5},ss1:{6},nodeid:{7}", seqID, player.EntityId, weapon.EntityId, weapon.UsingAmmoId, want, Mc.SnapshotReceiver.NowSnapshotSequence, ProcessEntity.Instance.CurrentSequence, weapon.UsingAmmoNodeId);
                        player.RemoteCallReloadDeductAmmo(ERpcTarget.World, seqID, weapon.EntityId, weapon.UsingAmmoNodeId, weapon.UsingAmmoId, want);
                    }
                    else
                    {
                        logger.InfoFormat("[Reload] 换弹过程被打断过，无法换弹, player:{0}, weapon:{1},snapshot:{2},ss1:{3}", player.EntityId, weapon.EntityId, Mc.SnapshotReceiver.NowSnapshotSequence, ProcessEntity.Instance.CurrentSequence);
                    }
                });
            }
        }


        public void ReloadInterrupt(PlayerEntity player, IHaveBulletEntity weapon)
        {
            if (weapon == null)
                return;
            OnReloadEnd(weapon);
            logger.InfoFormat("[Reload] ReloadInterrupt, player:{0}, weapon:{1},retriveAmmo {2},count:{3},seq:{4},snapshot:{5},ss1:{6},reloading:{7}", player.EntityId, weapon.EntityId, this.ReloadRetrieveAmmoBizId, this.ReloadRetrieveAmmoCount, this.ReloadSequence, Mc.SnapshotReceiver.NowSnapshotSequence, ProcessEntity.Instance.CurrentSequence, weapon.IsReloading);
            if (!weapon.IsReloading) return;
            if (this.ReloadSequence != -1)
            {
                if (ReloadSequence == 0)
                {
                    InterruptNotInHandReload = true;
                    using var _ = NetworkHelper.EnableAsyncSend();
                    RemoteCallReloadNotInHand(ERpcTarget.OwnClient, weapon.EntityId, weapon.TableId, 0, false);
                }
                if (ReloadConfirmAmmoCount > 0)
                {//先回滚扣除的子弹，如果有的话，此时UsingAmmoId是换弹时记录的要扣除的子弹
                    using var _ = NetworkHelper.EnableAsyncSend();
                    player.RemoteCallReloadGiveAmmo(ERpcTarget.World, weapon.UsingAmmoId, ReloadConfirmAmmoCount);
                }
                weapon.UsingAmmoId = this.ReloadRetrieveAmmoBizId; //需要将UsingAmmoId，设置回换弹开始之前的值
                weapon.ContinueReloadingAmmoID = 0; //清空继续换弹标记
                //如果带着扩容弹夹，且此时弹夹超过初始容量，换弹被打断时扩容弹夹已经卸下，防止超出初始子弹容量
                weapon.Clips = Math.Min(weapon.ClipCapacity, this.ReloadRetrieveAmmoCount);
                {
                    using var _ = NetworkHelper.EnableAsyncSend();
                    player.RemoteCallSyncBulletEntityWhenInterruptReload(ERpcTarget.World, weapon.EntityId,
                        weapon.UsingAmmoId, weapon.Clips);
                }
            }

            weapon.IsReloading = false;
            weapon.IsEmptyReload = false;
            CleanReloadInfo();
        }

        public void ReloadSetClip(PlayerEntity player, IHaveBulletEntity weapon)
        {
            logger.InfoFormat("[Reload] 子弹上膛, seqID:{0}, player:{1}, weapon:{2}, confirm:{3}", ReloadSequence, player.EntityId, weapon.EntityId, this.ReloadConfirmAmmoCount);
            if (this.ReloadSequence == -1)
                return;
            int retrieveCount = ReloadRetrieveAmmoCount;
            if (weapon.UsingAmmoId == ReloadRetrieveAmmoBizId)
            {
                //新上弹药和弹夹中已有一致
                weapon.Clips += ReloadConfirmAmmoCount;
                retrieveCount = 0;
            }
            else
            {
                weapon.Clips = ReloadConfirmAmmoCount;
            }
            if (weapon.Clips > weapon.ClipCapacity)
            {//超出部分归还背包
                weapon.ContinueReloadingAmmoID = 0;
                using var _ = NetworkHelper.EnableAsyncSend();
                RemoteCallReloadGiveAmmo(ERpcTarget.World, weapon.UsingAmmoId, weapon.Clips - weapon.ClipCapacity);
                weapon.Clips = weapon.ClipCapacity;

            }

            using var c = NetworkHelper.EnableAsyncSend();
            RemoteCallHandWeaponReloadFinish(ERpcTarget.OwnClient, Mc.SnapshotReceiver.NowSnapshotSequence);
            weapon.StepReloadingAmmoAmount -= weapon.SingleReloadClipNum;
            if (weapon.StepReloadingAmmoAmount > 0)
            {
                weapon.ContinueReloadingAmmoID = weapon.UsingAmmoId;
            }
            else
            {
                weapon.ContinueReloadingAmmoID = 0;
            }

            if (retrieveCount > 0)
            {
                using var _ = NetworkHelper.EnableAsyncSend();
                RemoteCallReloadGiveAmmo(ERpcTarget.World, this.ReloadRetrieveAmmoBizId, retrieveCount);
            }


            PlayerLogicStateMachine.Event.Emit(StateEventType.OnReloadSetClip, this, weapon);
            weapon.IsReloading = false;
            CleanReloadInfo();
        }

        private void CleanReloadInfo()
        {
            this.ReloadSequence = -1;
            this.ReloadingHeldItemID = -1;
            //logger.Error($"reload CleanReloadInfo");
            this.ReloadRetrieveAmmoBizId = 0;
            this.ReloadRetrieveAmmoCount = 0;
            this.ReloadConfirmAmmoCount = 0;
        }


        [RpcHandler]
        public void UnloadHiddenItemUse()
        {
            if (this.ItemHiddenUse != null)
            {
                //logger.Error($"UnloadHiddenItemUse CurrentWeaponId:{CurrentWeaponId},ItemHiddenUse:{this.ItemHiddenUse.EntityId}");
                if (this.ItemHiddenUse.EntityId == this.CurrentWeaponId)
                {
                    this.SwitchHeldItem(PreviousWeaponId);
                    this.SetItemEntity((int)HoldItemIndex.ItemHiddenUse, null);
                }
                else
                {
                    if (Mc.Entity.GetEntity(CurrentWeaponId) != null)
                    {//如果已经删除就不用再调用，否则可能把新换上的删除
                        RemoveItemEntityTask.Excute(this, (int)HoldItemIndex.ItemHiddenUse);
                    }
                }
            }
        }

        [RpcHandler]
        public void OnReloadDeductAmmo(ulong roleId, long seqId, int ammoBizId, int realReloadAmount, long weaponEntityId, int ammoLeft)
        {
            logger.InfoFormat("[Reload] 收到服务器换弹确认 roleId:{0},seqId: {1} ,ammoBizId:{2},realReloadAmount: {3},left:{4},localseq:{5},snapshot:{6},ss1:{7}", roleId, seqId, ammoBizId, realReloadAmount, ammoLeft, ReloadSequence, Mc.SnapshotReceiver.NowSnapshotSequence, ProcessEntity.Instance.CurrentSequence);
            var weapon = GetCustomTypeById(weaponEntityId) as IHaveBulletEntity;
            if (weapon == null)
                return;
            if (seqId != this.ReloadSequence)
            {
                RemoteCallReloadGiveAmmo(ERpcTarget.World, ammoBizId, realReloadAmount);
                return;
            }

            this.ReloadConfirmAmmoCount = realReloadAmount;
            if (ammoLeft == 0)
            {//没子弹可换，说明背包空了
                weapon.StepReloadingAmmoAmount = 0;
            }
        }

        #endregion

        /// 统计我的攻击
        private void AddAttackRecord(Dictionary<long, SummarizedDamageRecord> records, DamageRecord record)
        {
            if (records.TryGetValue(record.HitTargetEntityId, out SummarizedDamageRecord summarizedRecord) == false)
            {
                summarizedRecord = new SummarizedDamageRecord();
                summarizedRecord.HitName = record.HitTargetName;
                summarizedRecord.HitRoleId = record.HitTargetRoleId;
                summarizedRecord.HitPlayerRoleId = record.HitTargetPlayerRoleId;
                summarizedRecord.HitTableId = record.HitTargetTableId;
                summarizedRecord.HitEntityId = record.HitTargetEntityId;
                summarizedRecord.HitEntityType = record.HitTargetEntityType;

                records.Add(record.HitTargetEntityId, summarizedRecord);
            }

            if (record.IsWounded == false)
            {
                summarizedRecord.AttackDamage += record.HitDamage;
                summarizedRecord.AttackDamageByPart.TryAdd(record.HitPart, 0);
                summarizedRecord.AttackDamageByPart[record.HitPart] += record.HitDamage;
            }

            if (record.KillType == KillType.Kill)
            {
                summarizedRecord.KillCount++;
            }
            else if (record.KillType == KillType.Wounded)
            {
                summarizedRecord.KnockdownCount++;
            }
        }

        /// 统计我的受击
        private void AddSufferRecord(Dictionary<long, SummarizedDamageRecord> records, DamageRecord record)
        {
            if (records.TryGetValue(record.HitSourceEntityId, out SummarizedDamageRecord summarizedRecord) == false)
            {
                summarizedRecord = new SummarizedDamageRecord();
                summarizedRecord.HitName = record.HitSourceName;
                summarizedRecord.HitRoleId = record.HitSourceRoleId;
                summarizedRecord.HitPlayerRoleId = record.HitSourcePlayerRoleId;
                summarizedRecord.HitTableId = record.HitSourceTableId;
                summarizedRecord.HitEntityId = record.HitSourceEntityId;
                summarizedRecord.HitEntityType = record.HitSourceEntityType;
                summarizedRecord.WaysToDieID = record.WaysToDieID;

                records.Add(record.HitSourceEntityId, summarizedRecord);
            }

            if (record.IsWounded == false)
            {
                summarizedRecord.SufferDamage += record.HitDamage;
                summarizedRecord.SufferDamageByPart.TryAdd(record.HitPart, 0);
                summarizedRecord.SufferDamageByPart[record.HitPart] += record.HitDamage;
            }

            if (record.KillType == KillType.Kill)
            {
                summarizedRecord.IsKilledByThis = true;
            }
        }

        public void ClearDamageRecords()
        {
            foreach (var record in DamageRecords)
            {
                record?.TryRelease();
            }
            DamageRecords.Clear();
        }

        [RpcHandler(ExposeToClient = true)]
        public void GetSummarizedDamageRecords()
        {
            Dictionary<long, SummarizedDamageRecord> records = Pool.GetColl<Dictionary<long, SummarizedDamageRecord>>();
            List<SummarizedDamageRecord> recordsList = Pool.GetColl<List<SummarizedDamageRecord>>();

            records.Clear();
            recordsList.Clear();

            var recordStartTime = DamageRecordEndTime - DamageRecordTotalMSecs;

            foreach (var record in DamageRecords)
            {
                if (record.RecordServerTimeMs < recordStartTime)
                {
                    continue;
                }

                if (record.HitTargetEntityId == record.HitSourceEntityId && record.HitSourceEntityId == EntityId)
                {
                    continue;
                }

                if (record.HitTargetEntityId == EntityId)
                {
                    AddSufferRecord(records, record);
                }
                else if (record.HitSourceEntityId == EntityId)
                {
                    AddAttackRecord(records, record);
                }
            }

            foreach (var (_, record) in records)
            {
                recordsList.Add(record);
            }

            RemoteCallGetSummarizedDamageRecordsAck(ERpcTarget.OwnClient, recordsList);
            Pool.ReleaseColl(records);
            Pool.ReleaseColl(recordsList);
        }

        [RpcHandler(ExposeToClient = true)]
        public void ParachuteCutEnd()
        {
            if (MountableType == EntityTypeId.ParachuteEntity)
            {
                RpcContext.CurrentRoleId = RoleId;
                long mountableId = MountableId;
                WantsDismount();
                EntityManager.Instance.RemoveEntity(mountableId);
                DrawComponent.UnEquip();
            }
        }


        [RpcHandler(ExposeToClient = true, CallInterval = -1f)]
        public void ReceiveUserControllerSetting(UserControllerSetting setting)
        {
            if (PlayerLogicParams == null)
                return;
            if (PlayerLogicParams.UserControllerSetting != null)
            {
                UserControllerSetting.Return(PlayerLogicParams.UserControllerSetting);
            }
            PlayerLogicParams.UserControllerSetting = setting;
        }

        [RpcHandler(ExposeToClient = true)]
        public void RequestPlayTimeline(long timelineId)
        {
            McSimulator.ServerTimeline.TryPlayTimeline(timelineId, this.EntityId);
        }

        [RpcHandler(ExposeToClient = true)]
        public void NotifyTimelineFinish(long timelineId)
        {
            McSimulator.ServerTimeline.ClientTimelineFinish(timelineId);
        }


        private Dictionary<long, int> GuaranteeCheck = new Dictionary<long, int>();//保底命中目标列表
        public bool CheckGuarantee(long targetid, IProjectile projectile, int threshold)
        {
            bool has_checked = !projectile.CheckServerGuaranteeTarget(targetid);
            if (has_checked) return false;//过滤多次命中
            int misscount = 0;
            GuaranteeCheck.TryGetValue(targetid, out misscount);
            misscount++;
            GuaranteeCheck[targetid] = misscount;
            if (misscount == threshold)
            {
                logger.InfoFormat("伤害源 entity :{0} 服务器保底命中启用,目标:{1} ", EntityId, targetid);
            }
            return misscount >= threshold;
        }

        public void StopGuarantee(long targetid)
        {
            if (GuaranteeCheck.TryGetValue(targetid, out int misscount))
            {
                if (misscount >= 5)
                {
                    logger.InfoFormat("伤害源 entity :{0} 服务器保底命中停用,目标:{1} ", EntityId, targetid);
                }
                GuaranteeCheck.Remove(targetid);
            }
        }

        [RpcHandler(ExposeToClient = true)]
        public void Graffiti(long targetid, long tableid, float posX, float posY, float posZ, float nx, float ny, float nz)
        {
            // McSimulator.Decal.TryAttachDecal(EntityId, tableid, targetid, posX, posY, posZ, nx, ny, nz);
        }

        /// <summary>
        /// 服务端修改玩家的额外速度快照序列号
        /// </summary>
        public long ExtraSpeedSnapshotSequence;

        public void RecordKiller()
        {
            PlayerEntity knockDownMePlayer = KnockDownMePlayerId > 0 ? Mc.Entity.GetPlayerEntity(KnockDownMePlayerId) : null;
            PlayerEntity killMePlayer = KillMePlayerId > 0 ? Mc.Entity.GetPlayerEntity(KillMePlayerId) : null;
            if (knockDownMePlayer == null && killMePlayer == null)
            {
                return;
            }

            if (knockDownMePlayer == killMePlayer)
            {
                knockDownMePlayer.ShootInfoNow.AddKillCount();
                return;
            }

            if (knockDownMePlayer != null)
            {
                knockDownMePlayer.ShootInfoNow.AddKillCount();
            }
            if (killMePlayer != null)
            {
                killMePlayer.ShootInfoNow.AddKillCount();
            }
        }

        #region 减速相关

        private HashSet<ReduceSpeedAction> reduceSpeedActions = new HashSet<ReduceSpeedAction>(8);
        private float ServerReducedSpeedRate = 1;//服务端实际使用的减速率，当客户端上报的快照seq大于ToApplyReducedSpeedSequence 后，ServerReducedSpeedRate = ReduceSpeedRate
        private long ToApplyReducedSpeedSequence = -1;//要应用的减速率的快照序列号

        public void AddReduceSpeed(ReduceSpeedAction reduceSpeedAction)
        {
            if (reduceSpeedAction == null)
            {
                return;
            }

            reduceSpeedActions.Add(reduceSpeedAction);
            float minReduceRate = 1;
            foreach (var speedAction in reduceSpeedActions)
            {
                if (minReduceRate > speedAction.ReduceRate)
                {
                    minReduceRate = speedAction.ReduceRate;
                }
            }

            if (!Mathf.Approximately(minReduceRate, ReduceSpeedRate))
            {//若最小的减速率有变化，则更新ToApplyReducedSpeedSequence
                ToApplyReducedSpeedSequence = ProcessEntity.Instance.CurrentSequence;
            }
            //Debug.LogError($" AddReduceSpeed ToApplyReducedSpeedSequence:{ToApplyReducedSpeedSequence}.ToApplyReducedSpeedRate:{minReduceRate}");

            ReduceSpeedRate = minReduceRate;
        }

        public void RemoveReduceSpeed(ReduceSpeedAction reduceSpeedAction)
        {
            if (reduceSpeedAction == null)
            {
                return;
            }

            if (reduceSpeedActions.Remove(reduceSpeedAction))
            {
                float minReduceRate = 1;
                foreach (var speedAction in reduceSpeedActions)
                {
                    if (minReduceRate > speedAction.ReduceRate)
                    {
                        minReduceRate = speedAction.ReduceRate;
                    }
                }

                if (!Mathf.Approximately(minReduceRate, ReduceSpeedRate))
                {//若最小的减速率有变化，则更新ToApplyReducedSpeedSequence
                    ToApplyReducedSpeedSequence = ProcessEntity.Instance.CurrentSequence;

                }
                //Debug.LogError( $" RemoveReduceSpeed ToApplyReducedSpeedSequence:{ToApplyReducedSpeedSequence}.ToApplyReducedSpeedRate:{minReduceRate}");

                ReduceSpeedRate = minReduceRate;
            }
        }

        public void ClearReduceSpeed()
        {
            reduceSpeedActions.Clear();
            ServerReducedSpeedRate = ReduceSpeedRate = 1f;
            ToApplyReducedSpeedSequence = -1;
        }

        #endregion

        //包含交互 第一次选中检测层级
        private static readonly LayerMask _visibilityInteractionMask = MgrConfigPhysicsLayer.MaskHitDetail | MgrConfigPhysicsLayer.MaskDefault |
                                     MgrConfigPhysicsLayer.MaskGround | MgrConfigPhysicsLayer.MaskConstruction | MgrConfigPhysicsLayer.MaskInteraction ;
        
        private static readonly LayerMask _visibilityMask = MgrConfigPhysicsLayer.MaskHitDetail | MgrConfigPhysicsLayer.MaskDefault |
                                                            MgrConfigPhysicsLayer.MaskGround | MgrConfigPhysicsLayer.MaskConstruction  ;
        //判断交换角色和目标中间是否有建筑或者场景阻挡
        private bool VisibilityProbe(PlayerEntity player, Vector3 cameraPos, Vector3 forward, long targetId, float distance)
        {
            var testYawPitch = VisibilityTest(targetId, distance, cameraPos, forward);
            if (!testYawPitch)
            {
                var entity = Mc.Entity.GetEntity(targetId);
                if(entity is IPositionEntity posEntity)
                {
                    var targetPos = new Vector3(posEntity.PosX, posEntity.PosY, posEntity.PosZ);
                    var dir = targetPos - cameraPos;
                    var dis = dir.magnitude - 0.1f;//和客户端统一只要
                    forward = dir.normalized;
                    testYawPitch = NoBlock(targetId, dis, cameraPos, forward);
                }
                else
                {
                    return true;
                }
            }

            return testYawPitch;
        }
        private void GetCameraPos(PlayerEntity player, SimpleVector3 cameraPos, float yaw, float pitch, out Vector3 outCameraPos, out Vector3 direct)
        {
            var eyePos = new Vector3(player.PosX, player.PosY + player.Height, player.PosZ);
            var cameraPosV3 = new Vector3(cameraPos.X, cameraPos.Y, cameraPos.Z);
            var cameraDir = cameraPosV3 - eyePos;
            var cameraDis = cameraDir.magnitude;
            if (cameraDis > 0.1f && cameraDis < 13f)
            {//位置差距大可能是TP模式，检测角色眼睛和TP相机直接支付有阻挡
                int cameraHitCount = RaycastNonAllocSortUtil.RaycastNonAlloc(eyePos, cameraDir, cameraDis, _visibilityMask);
                if (cameraHitCount == 0)
                {//没有阻挡使用客户端上报相机位置
                    eyePos = cameraPosV3;
                }
            }
            outCameraPos = eyePos;
            
            var quaternion = Quaternion.Euler(-pitch, yaw, 0);
            var forward = quaternion.Froward();
            direct = forward;
        }

        private long VisibilityTargetId(PlayerEntity player, Vector3 cameraPos, Vector3 direct, float distance)
        {//玩家准心entityId 需要包含交互盒子
            int hitCount = RaycastNonAllocSortUtil.RaycastNonAllocSort(cameraPos, direct, distance, _visibilityInteractionMask);
            for (int i = 0; i < hitCount; i++)
            {
                var raycastHit = RaycastNonAllocSortUtil.hitBuffer[i];
                if (raycastHit.collider.TryGetComponent<ColliderConfig>(out var cc))
                {
                    if (cc.OwnerEntity is PartEntity)
                    {
                        return cc.OwnerEntityId;
                    }
                }
            }
            return 0;
        }
        private static bool NoBlock(long targetId, float distance, Vector3 eyePos, Vector3 forward)
        {
            int hitCount = RaycastNonAllocSortUtil.RaycastNonAllocSort(eyePos, forward, distance, _visibilityMask);
            if (hitCount == 0) return true;
            for (int i = 0; i < hitCount; i++)
            {
                var raycastHit = RaycastNonAllocSortUtil.hitBuffer[i];
                if (raycastHit.collider.TryGetComponent<ColliderConfig>(out var cc))
                {
                    if (cc.OwnerEntityId == targetId)
                    {
                        //打中目标 说明没有穿墙
                        return true;
                    }
                    if(cc.OwnerEntity is PartEntity partEntity)
                    {//建筑认为是阻挡了, 普通摆件排除可以不认为是阻挡
                        if (CommonConstructionUtils.IsCoreOrBuilding(partEntity.TemplateId))
                        {
                            return false;
                        }
                    }
                }
                else
                {
                    return false;
                }
            }

            return true;
        }

        private static bool VisibilityTest(long targetId, float distance, Vector3 eyePos, Vector3 forward)
        {
            int hitCount = RaycastNonAllocSortUtil.RaycastNonAllocSort(eyePos, forward, distance, _visibilityMask);
            for (int i = 0; i < hitCount; i++)
            {
                var raycastHit = RaycastNonAllocSortUtil.hitBuffer[i];
                if (raycastHit.collider.TryGetComponent<ColliderConfig>(out var cc))
                {
                    if (cc.OwnerEntityId == targetId)
                    {//打中目标 说明没有穿墙
                        return true;
                    }

                    if (cc.OwnerEntity is PartEntity)
                    {//建筑认为是阻挡了
                        return false;
                    }
                }
                else
                {//没有 ColliderConfig 全认为是场景认为是阻挡
                    return false;
                }
            }

            return false;
        }
        
        //simulator判断阻挡后转发给world
        [RpcHandler(ExposeToClient = true, CallInterval = .5f)]
        public void StartLooting(SimpleVector3 cameraPos, float yaw, float pitch, long targetId)
        {
            var entity = Mc.Entity.GetEntity(targetId);
            if (!PlayerBaseState.LootingCheckWall || entity is DigEntity)
            {
                RemoteCallStartLootingAfterSimulatorCheck(ERpcTarget.World, targetId);
                return;
            }
            var roleId = RpcContext.CurrentRoleId;
            var player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (player == null) return;
            GetCameraPos(player,cameraPos, yaw, pitch, out var cameraPosV3, out var forward);
            var hitTarget = VisibilityProbe( player,  cameraPosV3, forward, targetId, 13f);
            
            if (hitTarget)
            {
                RemoteCallStartLootingAfterSimulatorCheck(ERpcTarget.World, targetId);
            }
            else
            {
                logger.InfoFormat("StartLooting check failed targetId:{0}, roleId:{1}", targetId, roleId);
            }
        }

        private Ray viewRay = new Ray();
        //simulator判断阻挡后转发给world
        [RpcHandler(ExposeToClient = true, CallInterval = 0.2f)]
        public void SwitchLooting(SimpleVector3 cameraPos,float yaw, float pitch, long entityId, List<long> entityIds)
        {
            if (!PlayerBaseState.LootingCheckWall)
            {
                RemoteCallSwitchLootingAfterSimulatorCheck(ERpcTarget.World, entityId, entityIds);
                return;
            }
            var roleId = RpcContext.CurrentRoleId;
            var player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (player == null) return;
            GetCameraPos(player,cameraPos, yaw, pitch, out var cameraPosV3, out var forward);
            long viewId = VisibilityTargetId(player, cameraPosV3, forward, 13f);
            if (viewId == entityId)
            {
                RemoteCallSwitchLootingAfterSimulatorCheck(ERpcTarget.World, entityId, entityIds);
                return;
            }
            var entity = Mc.Entity.GetEntity(entityId);
            if(entity is IPositionEntity posEntity)
            {
                var targetPos = new Vector3(posEntity.PosX, posEntity.PosY, posEntity.PosZ);
                var dir = targetPos - cameraPosV3;
                var dis = dir.magnitude - 0.1f;//和客户端统一
                forward = dir.normalized;
                if (NoBlock(entityId, dis, cameraPosV3, forward))
                {
                    RemoteCallSwitchLootingAfterSimulatorCheck(ERpcTarget.World, entityId, entityIds);
                    return;
                }
            }
            viewRay.origin = cameraPosV3;
            viewRay.direction = forward;

            List<long> list = Pool.GetList<long>();
            CommonConstructionUtils.FindContainerNeighbourInRayView(viewId, viewRay, list);
            if (list.Contains(entityId))
            {
                RemoteCallSwitchLootingAfterSimulatorCheck(ERpcTarget.World, entityId, entityIds);
            }
            else
            {
                logger.InfoFormat("SwitchLooting check failed targetId:{0}, roleId:{1}", entityId, roleId);
            }
            Pool.ReleaseList(list);
        }

        //simulator判断阻挡后转发给world
        [RpcHandler(ExposeToClient = true, CallInterval = 0.1f)]
        public void GetItemWithoutLooting(SimpleVector3 cameraPos, float yaw, float pitch, long entityId, long itemUid)
        {
            if (!PlayerBaseState.LootingCheckWall)
            {
                RemoteCallGetItemWithoutLootingAfterSimulatorCheck(ERpcTarget.World, entityId, itemUid);
                return;
            }

            var roleId = RpcContext.CurrentRoleId;
            var player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (player == null) return;
            GetCameraPos(player,cameraPos, yaw, pitch, out var cameraPosV3, out var forward);
            var hitTarget = VisibilityProbe(player,cameraPosV3, forward, entityId, 13f);
            
            if (hitTarget)
            {
                RemoteCallGetItemWithoutLootingAfterSimulatorCheck(ERpcTarget.World, entityId, itemUid);
            }
            else
            {
                logger.InfoFormat("GetItemWithoutLooting check failed targetId:{0}, roleId:{1}", entityId, roleId);
            }
        }

        //simulator判断阻挡后转发给world
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        public void QuickMerge(SimpleVector3 cameraPos, float yaw, float pitch, long entityId, long bizId, int count)
        {
            if (!PlayerBaseState.LootingCheckWall)
            {
                RemoteCallQuickMergeAfterSimulatorCheck(ERpcTarget.World, entityId, bizId, count);
                return;
            }

            var roleId = RpcContext.CurrentRoleId;
            var player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (player == null) return;
            GetCameraPos(player,cameraPos, yaw, pitch, out var cameraPosV3, out var forward);
            var hitTarget = VisibilityProbe(player,cameraPosV3, forward, entityId, 13f);
            if (hitTarget)
            {
                RemoteCallQuickMergeAfterSimulatorCheck(ERpcTarget.World, entityId, bizId, count);
            }
            else
            {
                logger.InfoFormat("QuickMerge check failed targetId:{0}, roleId:{1}", entityId, roleId);
            }
        }

        //simulator判断阻挡后转发给world
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        public void QuickRequire(SimpleVector3 cameraPos, float yaw, float pitch, long entityId, long bizId, int count)
        {
            if (!PlayerBaseState.LootingCheckWall)
            {
                RemoteCallQuickRequireAfterSimulatorCheck(ERpcTarget.World, entityId, bizId, count);
                return;
            }

            var roleId = RpcContext.CurrentRoleId;
            var player = Mc.Entity.GetPlayerEntityByRoleId(roleId);
            if (player == null) return;
            GetCameraPos(player,cameraPos, yaw, pitch, out var cameraPosV3, out var forward);
            var hitTarget = VisibilityProbe(player, cameraPosV3, forward, entityId, 13f);
            if (hitTarget)
            {
                RemoteCallQuickRequireAfterSimulatorCheck(ERpcTarget.World, entityId, bizId, count);
            }
            else
            {
                logger.InfoFormat("QuickRequire check failed targetId:{0}, roleId:{1}", entityId, roleId);
            }
        }
    }
}