using UnityEngine;


namespace WizardGames.Soc.SocClient.Device
{
    public class PlatformDeviceInfo
    {
        private static string CachedProcessorType;
        private static string CachedDeviceID;
        private static string CachedDeviceName;
        private static string CachedDeviceModel;
        private static string CachedDeviceType;
        private static string CachedDeviceVersion;
        private static string CachedDeviceVendor;
        private static string CachedOSVersion;

        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
        private static void Initialize()
        {
            CachedProcessorType = SystemInfo.processorType;
            CachedDeviceID = SystemInfo.deviceUniqueIdentifier;
            CachedDeviceName = SystemInfo.graphicsDeviceName;
            CachedDeviceModel = SystemInfo.deviceModel;
            CachedDeviceType = SystemInfo.graphicsDeviceType.ToString();
            CachedDeviceVersion = SystemInfo.graphicsDeviceVersion;
            CachedDeviceVendor = SystemInfo.graphicsDeviceVendor;
            CachedOSVersion = SystemInfo.operatingSystem;
        }
        
        public static string GetDeviceID()
        {
            return CachedDeviceID;
        }

        public static string GetDeviceName()
        {
            return CachedDeviceName;  // Apple A15 GPU；针对PC：NVIDIA GeForce RTX 3060 Ti
        }

        public static string GetDeviceModel()
        {
            return CachedDeviceModel; // iPhone13,4; 针对PC：Z790 S DDR4 (Gigabyte Technology Co., Ltd.)
        }
        
        public static string GetDeviceType()
        {
            return CachedDeviceType; // 针对PC：Direct3D11
        }

        public static string GetDeviceVersion()
        {
            return CachedDeviceVersion;  // 针对PC：Direct3D 11.0 [level 11.0]
        }
        
        public static string GetDeviceManufacturer()
        {
            return CachedDeviceVendor; // 针对PC：Nvidia
        }

        public static string GetDeviceVendor()
        {
            return CachedDeviceVendor;  // 针对PC：Nvidia
        }

        public static int GetDeviceTotalRam()
        {
            return SystemInfo.systemMemorySize;
        }

        public static int GetDeviceVideoRam()
        {
            return SystemInfo.graphicsMemorySize; // 针对PC：8038 MB
        }

        public static int IsDeviceSupportVulkan()
        {
            return (SystemInfo.graphicsDeviceType == UnityEngine.Rendering.GraphicsDeviceType.Vulkan) ? 1 : 0;
        }
        
        public static string GetOSVersion()
        {
            return CachedOSVersion;
        }
        
        public static string GetCPUName()
        {
#if UNITY_ANDROID && !UNITY_EDITOR
            try
            {
                AndroidJavaClass androidBuild = new AndroidJavaClass("android.os.Build");
                CachedProcessorType =  androidBuild.GetStatic<string>("HARDWARE");
                return CachedProcessorType;
            }
            catch
            {
                return CachedProcessorType;
            }
#else
            return CachedProcessorType;
#endif
        }
    }
}
