using Sirenix.OdinInspector;
using System.IO;
using CommonUnity.Runtime.Animation;
using CommonUnity.Runtime.Character;
using CommonUnity.Runtime.Character.Resource;
using System;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Unity.Character;

#if UNITY_EDITOR
using UnityEditor;
using Sirenix.Utilities.Editor;
using UnityEditor.Animations;
#endif

namespace WizardGames.Editor
{
    
    [CreateAssetMenu(fileName = "TpClipSettingMeta", menuName = "Character/TpClipSettingMeta", order = 0)]
    public class TpClipSettingMeta : ScriptableObject
    {
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawTpBone", OnEndListElementGUI = "EndDrawTpBone", HideAddButton = false, HideRemoveButton = true)]
        public SkeletonMaskWeightValue[] idleWeightList = new SkeletonMaskWeightValue[(int)TpBoneNameConf.End];
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawTpBone", OnEndListElementGUI = "EndDrawTpBone", HideAddButton = false, HideRemoveButton = true)]
        public SkeletonMaskWeightValue[] stand2CrouchWeightList = new SkeletonMaskWeightValue[(int)TpBoneNameConf.End];
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawTpBone", OnEndListElementGUI = "EndDrawTpBone", HideAddButton = false, HideRemoveButton = true)]
        public SkeletonMaskWeightValue[] crouch2StandWeightList = new SkeletonMaskWeightValue[(int)TpBoneNameConf.End];
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawTpBone", OnEndListElementGUI = "EndDrawTpBone", HideAddButton = false, HideRemoveButton = true)]
        public SkeletonMaskWeightValue[] jogWeightList = new SkeletonMaskWeightValue[(int)TpBoneNameConf.End];
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawTpBone", OnEndListElementGUI = "EndDrawTpBone", HideAddButton = false, HideRemoveButton = true)]
        public SkeletonMaskWeightValue[] sprintWeightList = new SkeletonMaskWeightValue[(int)TpBoneNameConf.End];
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawTpBone", OnEndListElementGUI = "EndDrawTpBone", HideAddButton = false, HideRemoveButton = true)]
        public SkeletonMaskWeightValue[] jumpWeightList = new SkeletonMaskWeightValue[(int)TpBoneNameConf.End];
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawTpBone", OnEndListElementGUI = "EndDrawTpBone", HideAddButton = false, HideRemoveButton = true)]
        public SkeletonMaskWeightValue[] swimIdleWeightList = new SkeletonMaskWeightValue[(int)TpBoneNameConf.End];
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawTpBone", OnEndListElementGUI = "EndDrawTpBone", HideAddButton = false, HideRemoveButton = true)]
        public SkeletonMaskWeightValue[] swimJogWeightList = new SkeletonMaskWeightValue[(int)TpBoneNameConf.End];
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawTpBone", OnEndListElementGUI = "EndDrawTpBone", HideAddButton = false, HideRemoveButton = true)]
        public SkeletonMaskWeightValue[] swimSprintWeightList = new SkeletonMaskWeightValue[(int)TpBoneNameConf.End];
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawTpBone", OnEndListElementGUI = "EndDrawTpBone", HideAddButton = false, HideRemoveButton = true)]
        public SkeletonMaskWeightValue[] mantleWeightList = new SkeletonMaskWeightValue[(int)TpBoneNameConf.End];
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawTpBone", OnEndListElementGUI = "EndDrawTpBone", HideAddButton = false, HideRemoveButton = true)]
        public SkeletonMaskWeightValue[] ladderWeightList = new SkeletonMaskWeightValue[(int)TpBoneNameConf.End];
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawTpBone", OnEndListElementGUI = "EndDrawTpBone", HideAddButton = false, HideRemoveButton = true)]
        public SkeletonMaskWeightValue[] horseLocomotionWeightList =
            new SkeletonMaskWeightValue[(int)TpBoneNameConf.End];
        [ListDrawerSettings(OnBeginListElementGUI = "BegDrawTpBone", OnEndListElementGUI = "EndDrawTpBone", HideAddButton = false, HideRemoveButton = true)]
        public SkeletonMaskWeightValue[] parachuteLocomotionWeightList =
            new SkeletonMaskWeightValue[(int)TpBoneNameConf.End];
        
        public SerializableDictionary<AnimParametersTp.ELocomotionLayer, TpMetaLocomotionWeight> LocomotionLayerWeightCollection;
        
        public AnimKeyDict<TpClipData> NewClipInfos = new();
        
        [Tooltip("第三人称武器状态机hashid和动画片段对应关系")] [SerializeField]
        public AnimKeyDict< int> newTpWpnStringToHash = new ();
        
        public SerializableDictionary<string, string> AudioDict = new();
        
        public SerializableDictionary<PhaseKey, PhaseGroupScriptObj> AnimPhaseMap = new();

        public NewPhaseKeyContainer NewPhaseKeyContainer = new();

        public SerializableDictionary<AnimMoveEnum, AnimMoveSpeedConf> MoveSpeedConfs = new();

        public SerializableDictionary<AnimParametersTp.EAdditiveLayer, TpMetaAdditiveWeight> AddLayerWeightCollection;
        
        public SerializableDictionary<AnimParametersTp.EOverrideLayer, TpMetaOverrideWeight> OcLayerWeightCollection;
        
        public bool JumpInteriaOverride = false;
        
        //跳跃时候速度投影到前方的惯性曲线,起跳的时候计算
        public AnimationCurve JumpProj2ForwardInteriaCurve = AnimationCurve.Linear(0, 0, 1, 1);
        
        //跳跃时候速度投影到右方的惯性曲线,起跳的时候计算
        public AnimationCurve JumpProj2RightInteriaCurve = AnimationCurve.Linear(0, 0, 1, 1);
        //惯性应用时间
        public float JumpStartInertialTime = 0.3f;
        //惯性持续时间
        public float JumpKeepInertialTime = 0.3f;
        //惯性消失时间
        public float JumpClearInertialTime = 1;

        /// <summary>
        /// 第三人称武器资源绑定列表
        /// </summary>
        [Tooltip("第三人称武器资源绑定列表")] [SerializeField]
        public ClientWeaponBindingData WeaponBindings = new ClientWeaponBindingData();

        
        /// 武器idlePose导出数值
        /// </summary>
        [Tooltip("武器idlePose导出数值")] [SerializeField]
        public ClientClipValue WeaponClipValue = new ClientClipValue();
        
        /// <summary>
        /// 角色standhipPose导出数值
        /// </summary>
        [Tooltip("角色standhipPose导出数值")] [SerializeField]
        public ClientClipValue CharClipValue = new ClientClipValue();
        
        /// <summary>
        /// 动画warping Config
        /// </summary>
        [Tooltip("动画warping配置")] 
        [SerializeField]
        public CharacterAnimationConfig AnimationWarpingConfig = new CharacterAnimationConfig();

        /// <summary>
        /// 覆盖层state的time
        /// </summary>
        [Tooltip("Loc层state的time")] 
        [SerializeField]
        [ReadOnly]
        public float[] LocomotionLayerStateTime; 
        
        /// <summary>
        /// 覆盖层state的time
        /// </summary>
        [Tooltip("覆盖层state的time")] 
        [SerializeField]
        [ReadOnly]
        public float[] OverrideLayerStateTime; 
        
        /// <summary>
        /// 覆盖层state的loop
        /// </summary>
        [Tooltip("覆盖层state的loop")] 
        [SerializeField]
        [ReadOnly]
        public bool[] OverrideLayerStateLoop; 

        [Tooltip("LocomotionSpecial")] 
        [SerializeField]
        public List<AnimParametersTp.ELocomotionLayer> LocomotionSpecial;

        /// <summary>
        /// tip左转曲线
        /// </summary>
        public AnimationCurve TurnInPlaceLeftCurve = AnimationCurve.Linear(0, 0, 1, 1);
        /// <summary>
        /// tip右转曲线
        /// </summary>
        public AnimationCurve TurnInPlaceRightCurve= AnimationCurve.Linear(0, 0, 1, 1);
        public AnimationCurve TurnInPlaceCrouchLeftCurve = AnimationCurve.Linear(0, 0, 1, 1);
        /// <summary>
        /// tip右转曲线
        /// </summary>
        public AnimationCurve TurnInPlaceCrouchRightCurve= AnimationCurve.Linear(0, 0, 1, 1);
        
        
#if UNITY_EDITOR
        [HideInInspector]
        private bool IsInitialized = false;
        
        // public SkeletonMaskTemplate SkeletonMaskTemplate;
        
        [HideInInspector]
        public bool NeedUpdateWeight = false;
        
        [HideInInspector]
        public bool NeedUpdateOcWeight = false;
        
        private void BegDrawTpBone(int index)
        {
            SirenixEditorGUI.BeginBox(string.Format("[{0}] {1}", index, ((TpBoneNameConf)index).ToString()));
        }

        private void EndDrawTpBone(int index)
        {
            SirenixEditorGUI.EndBox();
        }


        [ButtonGroup("刷新骨骼权重")]
        public void UpdateSkeletonMaskWeight()
        {
            NeedUpdateWeight = true;
        }
        

        [ButtonGroup("刷新Oc权重")]
        public void UpdateSkeletonMaskOcWeight()
        {
            NeedUpdateOcWeight = true;
        }

        [OnCollectionChanged("OnOcLayerWeightCollectionChanged")]
        public void OnOcLayerWeightCollectionChanged()
        {
            if (!IsInitialized)
            {
                //自动创建empty
                if (!OcLayerWeightCollection.ContainsKey(AnimParametersTp.EOverrideLayer.OverrideEmpty))
                {
                    //创建序列化文件
                    var thiPath = AssetDatabase.GetAssetPath(this);
                    var subPath = thiPath.Substring(0, thiPath.LastIndexOf("/"));
                    var emptyPath = subPath + "/TpMetaOcWeight_" + AnimParametersTp.EOverrideLayer.OverrideEmpty +
                                    ".asset";
                    var emptyAsset = AssetDatabase.LoadAssetAtPath<TpMetaOverrideWeight>(emptyPath);
                    if (emptyAsset != null)
                    {
                        OcLayerWeightCollection[AnimParametersTp.EOverrideLayer.OverrideEmpty] = emptyAsset;
                    }
                    else
                    {
                        emptyAsset = ScriptableObject.CreateInstance<TpMetaOverrideWeight>();
                        emptyAsset.InitOcLayerUpperWeightCollection(1);
                        AssetDatabase.CreateAsset(emptyAsset, emptyPath);
                        AssetDatabase.SaveAssets();
                        OcLayerWeightCollection.Add(AnimParametersTp.EOverrideLayer.OverrideEmpty, emptyAsset);
                    }
                }

                IsInitialized = true;
            }

            var temp = new SerializableDictionary<AnimParametersTp.EOverrideLayer, TpMetaOverrideWeight>();
            foreach (var item in OcLayerWeightCollection)
            {
                var collect = item.Value;
                if (collect == null)
                {
                    //创建序列化文件
                    var thiPath = AssetDatabase.GetAssetPath(this);
                    var subPath = thiPath.Substring(0, thiPath.LastIndexOf("/"));
                    var collectPath = subPath + "/TpMetaOcWeight_" + item.Key + ".asset";
                    var haveAsset = AssetDatabase.LoadAssetAtPath<TpMetaOverrideWeight>(collectPath);
                    if (haveAsset != null)
                    {
                        temp.Add(item.Key, collect);
                    }
                    else
                    {
                        collect = CreateInstance<TpMetaOverrideWeight>();
                        if (item.Key == AnimParametersTp.EOverrideLayer.OverrideEmpty)
                        {
                            collect.InitOcLayerUpperWeightCollection(1);
                        }
                        else
                        {
                            collect.InitOcLayerUpperWeightCollection(0);
                        }

                        AssetDatabase.CreateAsset(collect, collectPath);
                        EditorUtility.SetDirty(collect);
                        AssetDatabase.SaveAssetIfDirty(collect);
                        temp.Add(item.Key, collect);
                    }
                }
            }

            foreach (var item in temp)
            {
                if (OcLayerWeightCollection.TryGetValue(item.Key, out var value))
                {
                    OcLayerWeightCollection[item.Key] = item.Value;
                }
                else
                {
                    OcLayerWeightCollection.Add(item.Key, item.Value);
                }
            }
        }

        [Button("Fix")]
        public void Fix()
        {
            //现在比较临时，后面采用使用一个通用的方式来处理，就是扫描字段自动适配，这样就不需要手动维护了。
            var assetPath = AssetDatabase.GetAssetPath(this);
            var folderPath = Path.GetDirectoryName(assetPath);
            //在目标目录下创建一个字符串文件夹
            var strFilePath = Path.Combine(folderPath, AnimStrKey.localStrFile);
            AnimStrGroupRef animStrGroupRef = null;
            if (!File.Exists(strFilePath))
            {
                animStrGroupRef = ScriptableObject.CreateInstance<AnimStrGroupRef>();
                AssetDatabase.CreateAsset(animStrGroupRef, strFilePath);
            }
            else
            {
                animStrGroupRef = AssetDatabase.LoadAssetAtPath<AnimStrGroupRef>(strFilePath);
            }
            
            var curveFilePath = Path.Combine(folderPath, AnimCurveKey.localStrFile);
            AnimCurveGroupRef animCurveGroup = null;
            if (!File.Exists(curveFilePath))
            {
                animCurveGroup = ScriptableObject.CreateInstance<AnimCurveGroupRef>();
                AssetDatabase.CreateAsset(animCurveGroup, curveFilePath);
            }
            else
            {
                animCurveGroup = AssetDatabase.LoadAssetAtPath<AnimCurveGroupRef>(curveFilePath);
            }
            
            Fix( NewClipInfos, animStrGroupRef, animCurveGroup);
            Fix( newTpWpnStringToHash, animStrGroupRef, animCurveGroup);
            
            Fix( WeaponBindings.newBasicWeaponInfos, animStrGroupRef, animCurveGroup);
            
            //数据并入到list中
            Fix(ref idleWeightList);
            Fix(ref stand2CrouchWeightList);
            Fix(ref crouch2StandWeightList);    
            Fix(ref jogWeightList);   
            Fix(ref sprintWeightList);   
            Fix(ref jumpWeightList);
            Fix(ref swimIdleWeightList);
            Fix(ref swimJogWeightList);
            Fix(ref swimSprintWeightList);
            Fix(ref mantleWeightList);
            Fix(ref ladderWeightList);
            Fix(ref horseLocomotionWeightList);
            Fix(ref parachuteLocomotionWeightList);

            ////更新武器绑定数据
            //PhaseGroupScriptObj anyObj = null;
            //PhaseGroupScriptObj leisureObj = null;

            //var phaseGroupScriptObjs = AssetDatabase.FindAssets("t:PhaseGroupScriptObj",
            //    new string[] { "Assets/SocClientRes/Weapon", "Assets/SocClientRes/Anim" });

            //foreach (var tpguid in phaseGroupScriptObjs)
            //{
            //    var asspath = AssetDatabase.GUIDToAssetPath(tpguid);

            //    var obj = AssetDatabase.LoadAssetAtPath<PhaseGroupScriptObj>(asspath);

            //    if (obj != null && obj.name == "PhaseGroupScriptObjgun2hs")
            //    {
            //        anyObj = obj;
            //    }
            //    else if (obj != null && obj.name == "PhaseGroupScriptObjgun2hsLeisureWalk")
            //    {
            //        leisureObj = obj;
            //    }
            //}

            //if (anyObj != null)
            //{
            //    //自动添加walk的
            //    var newDict = new Dictionary<PhaseKey, PhaseGroupScriptObj>();

            //    var expectStrs = new string[]
            //    {
            //         "LocomotionLayer.HipLocomotion.Locomotion_JogFStand",
            //         "LocomotionLayer.HipLocomotion.Locomotion_JogLStand",
            //         "LocomotionLayer.HipLocomotion.Locomotion_JogRStand" ,
            //         "LocomotionLayer.HipLocomotion.Locomotion_JogBStand" ,
            //         "LocomotionLayer.HipLocomotion.Locomotion_JogFLStand" ,
            //         "LocomotionLayer.HipLocomotion.Locomotion_JogFRStand" ,
            //         "LocomotionLayer.HipLocomotion.Locomotion_JogBLStand" ,
            //         "LocomotionLayer.HipLocomotion.Locomotion_JogBRStand" ,
            //         "LocomotionLayer.HipLocomotion.Locomotion_WalkFStand",
            //         "LocomotionLayer.HipLocomotion.Locomotion_WalkFLStand",
            //         "LocomotionLayer.HipLocomotion.Locomotion_WalkFRStand",
            //         "LocomotionLayer.HipLocomotion.Locomotion_WalkBRStand",
            //         "LocomotionLayer.HipLocomotion.Locomotion_WalkBLStand",
            //         "LocomotionLayer.HipLocomotion.Locomotion_WalkBStand",
            //         "LocomotionLayer.HipLocomotion.Locomotion_WalkLStand",
            //         "LocomotionLayer.HipLocomotion.Locomotion_WalkRStand",
            //         "LocomotionLayer.HipLocomotion.Locomotion_CrouchFStand" ,
            //         "LocomotionLayer.HipLocomotion.Locomotion_CrouchFRStand" ,
            //         "LocomotionLayer.HipLocomotion.Locomotion_CrouchRStand" ,
            //         "LocomotionLayer.HipLocomotion.Locomotion_CrouchBRStand" ,
            //         "LocomotionLayer.HipLocomotion.Locomotion_CrouchBLStand" ,
            //         "LocomotionLayer.HipLocomotion.Locomotion_CrouchBStand" ,
            //         "LocomotionLayer.HipLocomotion.Locomotion_CrouchLStand" ,
            //         "LocomotionLayer.HipLocomotion.Locomotion_CrouchFLStand" ,
            //    };

            //    AnimPhaseMap.Clear();

            //    // AnimParametersTp.AllHashMapKeys
            //    foreach (var expectStr in expectStrs)
            //    {
            //        var idx = Array.IndexOf(AnimParametersTp.AllHashMapKeys, expectStr);
            //        if (idx < 0)
            //        {
            //            continue;
            //        }

            //        {
            //            var newKey = new PhaseKey();
            //            newKey.selectIdx = idx;
            //            newKey.sourcePath = PhaseKeySource.Tp;
            //            newKey.fullPathHash = AnimParametersTp.AllHashMapValues[idx];

            //            newDict.Add(newKey, anyObj);
            //        }
            //    }

            //    var monsterExpectStr = new string[]
            //    {
            //         "LocomotionLayer.Locomotion.LocomotionSprintF",
            //         "LocomotionLayer.Locomotion.Locomotion_JogLStand",
            //         "LocomotionLayer.Locomotion.Locomotion_JogRStand",
            //         "LocomotionLayer.Locomotion.Locomotion_JogBStand" ,
            //         "LocomotionLayer.Locomotion.Locomotion_JogFStand" ,
            //         "LocomotionLayer.Locomotion.LeisureWalk" ,
            //    };

            //    // AnimParametersTp.AllHashMapKeys
            //    foreach (var expectStr in monsterExpectStr)
            //    {
            //        var idx = Array.IndexOf(AnimParametersScientist.AllHashMapKeys, expectStr);
            //        if (idx < 0)
            //        {
            //            continue;
            //        }

            //        {
            //            var newKey = new PhaseKey();
            //            newKey.selectIdx = idx;
            //            newKey.sourcePath = PhaseKeySource.Monster;
            //            newKey.fullPathHash = AnimParametersScientist.AllHashMapValues[idx];

            //            if (expectStr == "LocomotionLayer.Locomotion.LeisureWalk")
            //            {
            //                newDict.Add(newKey, leisureObj);
            //            }
            //            else
            //            {
            //                newDict.Add(newKey, anyObj);
            //            }
            //        }
            //    }

            //    foreach (var item in newDict)
            //    {
            //        AnimPhaseMap.Add(item.Key, item.Value);
            //    }
            //}

            EditorUtility.SetDirty(animCurveGroup);
            EditorUtility.SetDirty(animStrGroupRef);
            EditorUtility.SetDirty(this);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
        
        private void Fix<T>(ref T[] values)
        {
            var total = (int)TpBoneNameConf.End;
            if (values.Length != total)
            {
                //长度异常
                var oldvalues = values;
                values = new T[total];
                
                Array.Copy(oldvalues, values, Math.Min(oldvalues.Length, total));
            }
        }

        private void Fix<T>(AnimKeyDict<T> curves, AnimStrGroupRef animStrGroupRef, AnimCurveGroupRef animCurveGroup)
        {
            var newCurves = new AnimKeyDict<T>();
            
            //尽量按照之前的顺序来修复
            var keys = new List<AnimStrKey>(curves.Keys);
            
            foreach (var key in keys)
            {
                //需要修正
                var newK = key;
                if (newK.cache != animStrGroupRef)
                {
                    var stridx = animStrGroupRef.Add(key.displayName);

                    newK.index = (short)stridx;
                    newK.cache = animStrGroupRef;
                }
                
                newK.TryFixRef();
                
                newCurves.Add(newK, curves[key]);
            }
            curves.CopyFrom(newCurves);
        }
        
        private void Fix(AnimKeyDict<ClientWeaponClipInfo> curves, AnimStrGroupRef animStrGroupRef, AnimCurveGroupRef animCurveGroup)
        {
            var newCurves = new AnimKeyDict<ClientWeaponClipInfo>();
            
            //尽量按照之前的顺序来修复
            var keys = new List<AnimStrKey>(curves.Keys);
            
            foreach (var k in keys)
            {
                //需要修正
                var newK = k;
                if (newK.cache != animStrGroupRef)
                {
                    var stridx = animStrGroupRef.Add(k.displayName);

                    newK.index = (short)stridx;
                    newK.cache = animStrGroupRef;
                }
                
                newK.TryFixRef();
                
                var newValue = curves[k];
                if (newValue.newClipName.cache != animStrGroupRef)
                {
                    var stridx = animStrGroupRef.Add(k.displayName);

                    newValue.newClipName.index = (short)stridx;
                    newValue.newClipName.cache = animStrGroupRef;
                }

                newValue.newClipName.TryFixRef();
                
                newCurves.Add(newK,newValue);
            }
            curves.CopyFrom(newCurves);
        }
        
        public void RefreshOcLayerWeightCollection()
        {
            var thiPath = AssetDatabase.GetAssetPath(this);
            var subPath = thiPath.Substring(0, thiPath.LastIndexOf("/"));
            var guids = AssetDatabase.FindAssets("t:TpMetaOverrideWeight", new []{subPath});
            var assets = new Dictionary<string, TpMetaOverrideWeight>();
            for (int i = 0; i < guids.Length; i++)
            {
                var guid = guids[i];
                var path = AssetDatabase.GUIDToAssetPath(guid);
                var asset = AssetDatabase.LoadAssetAtPath<TpMetaOverrideWeight>(path);
                if (asset != null)
                {
                    assets.Add(path, asset);
                }
            }
            var temp = new SerializableDictionary<AnimParametersTp.EOverrideLayer, TpMetaOverrideWeight>();
            for (var i = AnimParametersTp.EOverrideLayer.OverrideEmpty; i < AnimParametersTp.EOverrideLayer.Sum; i++)
            {
               
                var collectPath = subPath+"/TpMetaOcWeight_"+i+".asset";
                if (assets.TryGetValue(collectPath, out var asset))
                {
                    temp[i] = asset;
                    assets.Remove(collectPath);
                }
            }
            OcLayerWeightCollection = temp;
            EditorUtility.SetDirty(this);
            AssetDatabase.SaveAssetIfDirty(this);
            //清楚多余的
            foreach (var item in assets)
            {
                AssetDatabase.DeleteAsset(item.Key);
            }
        }

        public void RefreshOcLayerTimeAndLoop()
        {
            var animatorPath = "Assets/SocClientRes/Char/CharAnimator/Ac_Common@ac_common_tp/Ac_Basic_Tp.controller";
            var animator = AssetDatabase.LoadAssetAtPath<AnimatorController>(animatorPath);
            // 遍历状态机中的所有状态
            var overrideStateTypeToClip =
                new SerializableDictionary<AnimParametersTp.EOverrideLayer,
                    System.Collections.Generic.List<AnimationClip>>();
            var headName = "";
            ProcessLayerClipWithType<AnimParametersTp.EOverrideLayer>(
                animator.layers[AnimParametersTp.OverrideLayer_LayerIndex].stateMachine, overrideStateTypeToClip,
                ref headName);
            var locomotionStateTypeToClip =
                new SerializableDictionary<AnimParametersTp.ELocomotionLayer,
                    System.Collections.Generic.List<AnimationClip>>();
            ProcessLayerClipWithType<AnimParametersTp.ELocomotionLayer>(
                animator.layers[AnimParametersTp.LocomotionLayer_LayerIndex].stateMachine,
                locomotionStateTypeToClip, ref headName);
            
            //获取oc状态机里的所有的片段clip
            var thiPath = AssetDatabase.GetAssetPath(this);
            var subPath = thiPath.Substring(0, thiPath.LastIndexOf("/"));
            //gun/bow/bow/animation
            var subDir = subPath.Substring(0, subPath.LastIndexOf("/"));
            //gun/bow/bow/
            var weaponDir = subDir.Substring(0, subDir.LastIndexOf("/"));
            var name = weaponDir.Substring(weaponDir.LastIndexOf("/") + 1).ToLower();
            var weaponAnimDir = weaponDir + "/Animator/";
            var tpAcFolderName = "Tp_ac_" + name + "_tp";
            var fpTpAcFolder = weaponAnimDir + tpAcFolderName;
            var ctrlName = "Ac_" + name + "_Tp";
            var tpAcPath = fpTpAcFolder + "/" + ctrlName + ".overrideController";
            var ac = AssetDatabase.LoadAssetAtPath<AnimatorOverrideController>(tpAcPath);
            var finalClipsDic = new List<KeyValuePair<AnimationClip, AnimationClip>>();
            if (ac != null)
            {
                ac.GetOverrides(finalClipsDic);
            }
            var finalClips = new List<AnimationClip>();
            foreach (var item in finalClipsDic)
            {
                var value = item.Value;
                if(value!=null)
                    finalClips.Add(value);
            }
            //获取对应的list的里的clip的任一个clip的长度
            var overrideStateTypeToClipTimeDic =
                new SerializableDictionary<AnimParametersTp.EOverrideLayer, List<float>>();
            var overrideStateLoopToClipTimeDic =
                new SerializableDictionary<AnimParametersTp.EOverrideLayer, List<bool>>();
            foreach (var pair in overrideStateTypeToClip)
            {
                var clips = pair.Value;
                var clipTimeList = new List<float>();
                var clipLoopList = new List<bool>();
                foreach (var clip in clips)
                {
                    if (clip != null)
                    {
                        //找到对用的oc的片段
                        var clipName = clip.name;
                        clipName = clipName.Substring(clipName.IndexOf("_") + 1);
                        var matchClip = finalClips.Find(p => p.name.Substring(p.name.IndexOf("_") + 1) == clipName);
                        if (matchClip != null)
                        {
                            clipTimeList.Add(matchClip.length);
                            clipLoopList.Add(matchClip.isLooping);
                        }
                        else
                        {
                            clipTimeList.Add(0);
                            clipLoopList.Add(false);
                        }
                    }
                    else
                    {
                        clipTimeList.Add(0);
                        clipLoopList.Add(false);
                    }
                }

                overrideStateTypeToClipTimeDic.Add(pair.Key, clipTimeList);
                overrideStateLoopToClipTimeDic.Add(pair.Key, clipLoopList);
            }

            var locStateTypeToClipTimeDic =
                new SerializableDictionary<AnimParametersTp.ELocomotionLayer, List<float>>();
            foreach (var pair in locomotionStateTypeToClip)
            {
                var clips = pair.Value;
                var clipTimeList = new List<float>();
                var clipLoopList = new List<bool>();
                foreach (var clip in clips)
                {
                    if (clip != null)
                    {
                        //找到对用的oc的片段
                        var clipName = clip.name;
                        clipName = clipName.Substring(clipName.IndexOf("_") + 1);
                        var matchClip = finalClips.Find(p => p.name.Substring(p.name.IndexOf("_") + 1) == clipName);
                        if (matchClip != null)
                        {
                            clipTimeList.Add(matchClip.length);
                            clipLoopList.Add(matchClip.isLooping);
                        }
                        else
                        {
                            clipTimeList.Add(0);
                            clipLoopList.Add(false);
                        }
                    }
                    else
                    {
                        clipTimeList.Add(0);
                        clipLoopList.Add(false);
                    }
                }

                locStateTypeToClipTimeDic.Add(pair.Key, clipTimeList);
            }

            var locETypeCount = (int)AnimParametersTp.ELocomotionLayer.Sum;
            var locLayerStateTime = new float[locETypeCount];
            for (var i = 0; i < locETypeCount; i++)
            {
                locLayerStateTime[i] = 0;
                var type = (AnimParametersTp.ELocomotionLayer)Enum.ToObject(typeof(AnimParametersTp.ELocomotionLayer),
                    i);
                if (locStateTypeToClipTimeDic.TryGetValue(type, out var clips))
                {
                    foreach (var clipTime in clips)
                    {
                        if (!Mathf.Approximately(clipTime, 0.0f))
                        {
                            locLayerStateTime[i] = clipTime;
                            break;
                        }
                    }
                }
            }

            LocomotionLayerStateTime = locLayerStateTime;
            var eTypeCount = (int)AnimParametersTp.EOverrideLayer.Sum;
            //time
            var overrideLayerStateTime = new float[eTypeCount];
            for (int i = 0; i < eTypeCount; i++)
            {
                overrideLayerStateTime[i] = 0;
                var type = (AnimParametersTp.EOverrideLayer)Enum.ToObject(typeof(AnimParametersTp.EOverrideLayer), i);
                if (overrideStateTypeToClipTimeDic.TryGetValue(type, out var clips))
                {
                    foreach (var clipTime in clips)
                    {
                        if (!Mathf.Approximately(clipTime, 0.0f))
                        {
                            overrideLayerStateTime[i] = clipTime;
                            break;
                        }
                    }
                }
            }

            OverrideLayerStateTime = overrideLayerStateTime;
            //loop
            var overrideLayerStateLoop = new bool[eTypeCount];
            for (int i = 0; i < eTypeCount; i++)
            {
                overrideLayerStateLoop[i] = false;
                var type = (AnimParametersTp.EOverrideLayer)Enum.ToObject(typeof(AnimParametersTp.EOverrideLayer), i);
                if (overrideStateLoopToClipTimeDic.TryGetValue(type, out var clips))
                {
                    foreach (var clipBool in clips)
                    {
                        if (clipBool)
                        {
                            overrideLayerStateLoop[i] = true;
                            break;
                        }
                    }
                }
            }
            OverrideLayerStateLoop = overrideLayerStateLoop;
        }
        
        private static void ProcessLayerClipWithType<T>(AnimatorStateMachine machineStateMachine, SerializableDictionary<T, List<AnimationClip>> clips, ref string headName) where T : struct
        {
            for (int i = 0; i < machineStateMachine.states.Length; i++)
            {
                var state = machineStateMachine.states[i].state;
                var enumName = "";
                if (string.IsNullOrEmpty(headName))
                {
                    enumName = GetPrintName(state.name);
                }
                else
                {
                    enumName =headName + "_" + GetPrintName(state.name);
                }
                ProcessStateClipWithType(state.motion, clips, ref enumName);
            }

            for (int i = 0; i < machineStateMachine.stateMachines.Length; i++)
            {
                var ms = machineStateMachine.stateMachines[i];
                var str = "";
                if (string.IsNullOrEmpty(headName))
                {
                    str = GetPrintName(ms.stateMachine.name);
                }
                else
                {
                    str = headName + "_" + GetPrintName(ms.stateMachine.name);
                }
                ProcessLayerClipWithType<T>(ms.stateMachine, clips, ref str);
            }
        }
        
        private static string GetPrintName(string value)
        {
            return value.Replace(" ", "_");
        }
        
        private static void ProcessStateClipWithType<T>(Motion motion, SerializableDictionary<T, List<AnimationClip>> clips, ref string headName) where T : struct
        {
            if (motion == null) return;
           
            var eType = Enum.Parse<T>(headName);
            if(clips.TryGetValue(eType, out var clipList))
            {
                
            }
            else
            {
                clipList = new List<AnimationClip>();
                clips.Add(eType, clipList);
                
            }
            if (motion is AnimationClip clip)
            {
                // 如果是普通动画片段，直接添加
                if (!clipList.Contains(clip))
                {
                    clipList.Add(clip);
                }
            }
            else if (motion is BlendTree blendTree)
            {
                // 如果是混合树，递归处理
                ProcessBlendTreeByType(blendTree, clips, ref headName);
            }
        }
        
        private static void ProcessBlendTreeByType<T>(BlendTree blendTree, SerializableDictionary<T, List<AnimationClip>> clips, ref string headName) where T : struct
        {
            foreach (ChildMotion child in blendTree.children)
            {
                ProcessStateClipWithType(child.motion, clips, ref headName);
            }
        }
#endif
    }
}