using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Combat;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Data.character;
using WizardGames.Soc.Common.Data.holdItem;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Unity.Character.State.Event;
using WizardGames.Soc.Common.Unity.Config;
using WizardGames.Soc.Common.Unity.Contexts;
using WizardGames.Soc.Common.Unity.Extension;
using WizardGames.Soc.Common.Unity.Hit;
using WizardGames.Soc.Common.Unity.Manager;
using WizardGames.Soc.Common.Unity.Utility;

namespace WizardGames.Soc.Common.Unity.Character
{
    public enum CureSubState:byte{
        Enter,
        Detect,//检测帧
        Detected,//检测到了
        Cure,
    }

    //治疗类交互逻辑
    public class InteractiveBehHeldItemCure : BaseInteractiveBeh
    {
        public InteractiveBehHeldItemCure(PlayerInteractiveState config) : base(config)
        {
        }
        
        private bool Valid(PlayerEntity p,out MedicalBase medicalBase,out HoldItemBase holdItemBase)
        {
            medicalBase = null;
            holdItemBase = null;
            var helditem = p?.GetHeldItemByEntityId(p.CurrentWeaponId);
            if(helditem == null) return false;
            medicalBase = McCommon.Tables.TbMedicalBase.GetOrDefault(helditem.TableId);
            holdItemBase = McCommon.Tables.TbHoldItemBase.GetOrDefault(helditem.TableId);
            if (medicalBase == null || holdItemBase == null || holdItemBase.DetectionTimes.Length <=0)
            {
                return false;//
            }
            return true;
        }

        public override bool CanEnter(PlayerLogicParams p)
        {
            if (Valid(p.entity, out _, out _) && base.CanEnter(p))
            {
                bool need_recover = false;
                if (p.entity.InteractiveId != this.Config.Id)
                {//
                   if(this.Config.Id == (int)PlayerInteractiveId.SyringeOther && p.entity.InteractiveId == (int)PlayerInteractiveId.Syringe)
                   {//要进入治疗别人，如果当前在治疗自己，需要判断后摇
                       need_recover = true;
                   }
                }
                else
                {
                    need_recover = true;
                }
                bool isInRecovery = IsInRecovery(p.entity);
                if (!need_recover || IsInRecovery(p.entity)) return true;//不要判断后摇，或者在后摇
            }
            return false;
        }

        public override void OnEnter(PlayerLogicParams p)
        {
            base.OnEnter(p);
            p.entity.InteractiveSubState = (int)CureSubState.Enter;
        }
        
        public override void OnLeave(PlayerLogicParams p)
        {
            //logger.Error($"cure CheckBagUseDone 1:{p.entity.ClientTime}");
            //p.entity.CheckBagUseDone(p.HeldItem);
            base.OnLeave(p);
        }

        public override int OnUpdate(PlayerLogicParams p)
        {
            if (!Valid(p.entity,out var medicalBase,out var holdItemBase))
            {
                //logger.Error($"exit not Valid {p.entity.ClientTime}");
                return (int)PlayerInteractiveStateEnum.Idle;//退出交互状态
            }

            if (CommonStopBehCheck(p))
            {
               // logger.Error($"exit common stop {p.entity.ClientTime}");
                return (int)PlayerInteractiveStateEnum.Idle; //退出交互状态
            }

            PlayerEntity player = p.entity;
            if (p.cmd.InteractiveTargetID > 0)
            {
                logger.InfoFormat("cure target {0},time:{1}", p.cmd.InteractiveTargetID, p.entity.ClientTime);
            }

            bool cureOther = Config.Id == (int)PlayerInteractiveId.SyringeOther;
            float stateTime = Config.StateTime;//真实状态时间
            if (cureOther)
            {
                stateTime = player.InteractiveTargetId>0?Config.StateTime:(int)(medicalBase.AttackMissTime * 1000);
            }
           
            int detectTime = (int)(stateTime*holdItemBase.DetectionTimes[0]);
            if (p.entity.InteractiveSubState == (int)CureSubState.Enter && Trigger(p, detectTime))
            {//命中检测
                EnterInteractiveSubState( (byte)CureSubState.Detect,p.entity);
                player.InteractiveTrigger = PlayerStateTrigger.SetStateTrigger(player.InteractiveTrigger,
                    (int)InterActiveTriggerEnum.CureDetectTrigger, true);
            }
            if(p.entity.InteractiveSubState == (int)CureSubState.Detect)
            {
                if (p.entity.InteractiveTargetId > 0)
                {
                    EnterInteractiveSubState( (byte)CureSubState.Detected,p.entity);
                }
            }

            if (p.entity.InteractiveSubState == (int)CureSubState.Detected)
            {
                int cureTime = (int)(medicalBase.AttackCureTime * 1000);
                if (!CheckTargetValid(p, holdItemBase.CoillderLength + 1f))
                {
                    logger.InfoFormat("目标不可治疗，退出治疗状态");
                    return (int)PlayerInteractiveStateEnum.Idle;
                }
                // if (!p.MgrEntity.TryGetEntity(player.InteractiveTargetId, out PlayerEntity target) ||
                //     target.IsDead || target.IsWounded)
                // {
                //     logger.InfoFormat("目标不可治疗，退出治疗状态");
                //     return (int)PlayerInteractiveStateEnum.Idle;
                // }

                if (player.ClientTime >= player.InteractiveStateStartTime + cureTime)
                {//加血检测 
                    EnterInteractiveSubState( (byte)CureSubState.Cure,p.entity);
#if SOC_SIMULATOR
                    var request = UseItemRequest.Generate(player, p.HeldItem,player.InteractiveTargetId);
                    McCommon.SystemRequestMgr.AddRequest(ESystemRequest.UseItemRequestSet, request);         
                   
#endif
                    //logger.Error($"cure request:{player.ClientTime}");
                    //p.entity.CheckBagUseDone(p.HeldItem);
                }                
            }



            if (p.entity.InteractiveStateStartTime + stateTime <= p.entity.ClientTime)
            {
                //logger.Error($"exit common finish {p.entity.ClientTime}");
                return (int)PlayerInteractiveStateEnum.Idle;
            }

            return base.OnUpdate(p);
        }

        protected bool CheckTargetValid(PlayerLogicParams p,float dis)
        {
            PlayerEntity source = p.entity;
            if (source.EntityId == source.InteractiveTargetId)
                return true;
            if (!p.MgrEntity.TryGetEntity(source.InteractiveTargetId, out PlayerEntity target))
                return false;
            
            if (target.IsDead || target.IsWounded) return false;
            Vector3 self = new Vector3(source.PosX, source.PosY, source.PosZ);
            Vector3 targetPos = new Vector3(target.PosX, target.PosY, target.PosZ);
            return Vector3.Distance(self, targetPos) <= dis;
            
        }

        public override bool IsInRecovery(PlayerEntity player)
        {
            float rec_rate = 1;
            if (!Valid(player,out var medicalBase,out var holdItemBase))
            {
                return false;
            }
            if (player.InteractiveTargetId > 0)
            {
                rec_rate = holdItemBase.AttackAgainTime;
            }
            else
            {
                rec_rate = medicalBase.AttackMissAgainTime;
            }
           
            int rec_time = (int)(Config.StateTime * rec_rate);
            //logger.Error($"IsInRecovery statetime:{Config.StateTime},target:{player.InteractiveTargetId},rec_rate:{rec_rate},rec_time:{rec_time},start:{player.InteractiveStateStartTime}，ClientTime：{player.ClientTime}");
            return player.InteractiveStateStartTime + rec_time <= player.ClientTime;
        }
    }
}
