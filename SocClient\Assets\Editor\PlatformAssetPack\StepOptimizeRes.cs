﻿using PackTool;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using UnityEditor;
using UnityEngine;

namespace Soc.Common.Unity.PlatformResOpt
{
    public enum StatsType
    {
        None,
        Ignore,
        CacheProcess,
        CacheProcessSuc,
        CacheProcessIgnore,
        CacheProcessFailed,
        Process,
        ProcessFailed,
        ProcessSuc,
        ProcessDepSuc,
    }
    
    //执行资产处理的主入口
    public class StepOptimizePlatformRes
    {
        public static SocLogger logger = LogHelper.GetLogger<StepOptimizePlatformRes>();

        public bool CanOptimize(PlatformPackPlatform targetPlatform)
        {
            //看是否开启了资产处理
            PlatformPackToolSettings currentPlatformSetting = PackToolSettings.TryFindSettings(targetPlatform);
            if (null == currentPlatformSetting)
            {
                throw new Exception($"没有找到对应的平台配置 target:{targetPlatform}");
            }

            if (currentPlatformSetting.Enable == false)
            {
                logger.ErrorFormat("当前平台配置未开启 target:{0}", targetPlatform);
                return false;
            }

            if (currentPlatformSetting.rules == null || currentPlatformSetting.rules.Length == 0)
            {
                throw new Exception($"当前平台配置没有规则 target:{targetPlatform}");
            }

            return true;
        }
        
        public static bool IsIgnoreDir(string assetPath)
        {
            PlatformPackToolSettings currentPlatformSetting = PackToolSettings.TryFindSettings(PackToolSettings.Instance.target);
            if (currentPlatformSetting == null)
            {
                return false;
            }
            
            return IsIgnoreDir(assetPath, currentPlatformSetting);
        }
        
        public static bool IsIgnoreDir(string assetPath, PlatformPackToolSettings currentPlatformSetting)
        {
            if (currentPlatformSetting == null)
            {
                return false;
            }
            
            foreach (var ignoreDir in currentPlatformSetting.ignoreDirs)
            {
                if (assetPath.StartsWith(ignoreDir))
                {
                    return true;
                }
            }
            
            //或者不在扫描目录下
            bool hasDir = false;
            foreach (var dir in currentPlatformSetting.SearchDirs)
            {
                if (assetPath.StartsWith(dir))
                {
                    hasDir = true;
                }
            }

            if (hasDir)
            {
                return false;
            }
            return true;
        }

        public bool Execute(PlatformPackContext context)
        {
            //判断是否可以优化
            if (!CanOptimize(context.targetPlatform))
            {
                return false;
            }

            logger.InfoFormat("开始执行平台资源处理targetPlatform:{0} processStage:{1}", context.targetPlatform, context.processStage);
            bool success = true;
            //看是否开启了资产处理
            PlatformPackToolSettings currentPlatformSetting = PackToolSettings.TryFindSettings(context.targetPlatform);
            
            var startTime = EditorApplication.timeSinceStartup;
            
            context.ruleRevision = PackToolSettings.Instance.revision;

            bool enableLog = PackToolSettings.Instance.EnableLog;
            if (context.processStage >= EPlatformProcessStage.CI_Product)
            {
                enableLog = true;
                var executeMethod = PlatformAssetPackCI.ParseCmdLine("-executeMethod");
                //关闭cache server的上传和下载
                if (!string.IsNullOrEmpty(executeMethod))
                {
                    logger.InfoFormat("执行方法:{0} 开始关闭cache server的上传和下载", executeMethod);
                    EditorSettings.cacheServerEnableUpload = false;
                    EditorSettings.cacheServerEnableDownload = false;
                    //只影响项目
                    EditorSettings.cacheServerMode = CacheServerMode.Disabled;
                }

                if (context.svnCache == null)
                {
                    context.svnCache = PlatformPackVCTools.BuildSvnInfo();
                }

                if (context.buildCache == null)
                {
                    context.buildCache = BuildPlatformBuildCache(context);
                }
            }

            //校验缓存是否正常
            ValidateCache(context);
            
            var prepareStartTime = EditorApplication.timeSinceStartup;

            Dictionary<string, PackInputParams> assetInputMap = new();
            //prepare
            foreach (var (assetPath, assetObject) in context.assetList)
            {
                //在这里是为了保底
                if (currentPlatformSetting != null && IsIgnoreDir(assetPath, currentPlatformSetting))
                {
                    logger.InfoFormat("跳过处理资产：{0}，因为在ignoreDirs列表中", assetPath);
                    continue;
                }
                
                PlatformPackSvnFileInfo svnFileInfo = null;
                if (context.svnCache != null)
                {
                    context.svnCache.files.TryGetValue(assetPath, out svnFileInfo);
                }

                if (svnFileInfo == null && context.processStage >= EPlatformProcessStage.CI_Product)
                {
                    logger.InfoFormat("没有找到svn缓存信息 assetpath:{0} 该资产不做处理", assetPath);
                    continue;
                }

                var assguid = AssetDatabase.GUIDFromAssetPath(assetPath).ToString();
                if (string.IsNullOrEmpty(assguid))
                {
                    throw new Exception($"没有找到对应的guid assetpath:{assetPath}");
                }
                
                PackInputParams inputParams = new PackInputParams();
                inputParams.context = context;
                inputParams.processSvnFileInfo = svnFileInfo;
                inputParams.processSourcePath = assetPath;
                inputParams.assetTarget = assetObject;
                inputParams.assetGuid = assguid;
                inputParams.enableLog = enableLog;
                
                var curDep = PlatformPackUtils.BuildDepCache(inputParams.assetTarget, inputParams.processSvnFileInfo,context);
                inputParams.processDepCaches =  new PlatformPackBuildDepCache();

                if (curDep != null)
                {
                    inputParams.processDepCaches.dependencyList.AddRange(curDep);

                    if (inputParams.enableLog)
                    {
                        logger.InfoFormat("准备阶段 收集依赖 assetpath:{0} depCount:{1}", assetPath, curDep.Count);
                    }
                }
     
                context.depCache.Add(assetPath, inputParams.processDepCaches);

                foreach (var rule in currentPlatformSetting.rules)
                {
                    rule.Prepare(inputParams);
                }
                
                assetInputMap.Add(assetPath, inputParams);
            }
            

            var prepareEndTime = EditorApplication.timeSinceStartup;

            if (enableLog)
            {
                logger.InfoFormat("准备阶段完成 耗时:{0} ", FormatTime( prepareEndTime - prepareStartTime));
            }

            //遍历资产，判断是否能发生了变化，
            foreach (var (assetPath, assetObject) in context.assetList)
            {
                var subStartTime =EditorApplication.timeSinceStartup;
                
                if (!assetInputMap.TryGetValue(assetPath, out var inputParams))
                {
                    continue;
                }
                
                if (enableLog)
                    logger.InfoFormat("开始检测资产 {0} ", assetPath, inputParams);

                //执行全局规则 ,尝试应用缓存
                bool cacheSuc = false;
                foreach (var rule in currentPlatformSetting.rules)
                {
                    var code = RunCacheRule(inputParams, rule, false);
                    if (code == (int)EPlatformErrorCode.ContinueCode)
                    {
                        continue;
                    }
                    
                    if (code == (int)EPlatformErrorCode.BreakCode)
                    {
                        break;
                    }
                    
                    if ( code ==(int)EPlatformErrorCode.Suc)
                    {
                        cacheSuc = true;
                        break;
                    }
                }

                //不满足的话再检查自定义规则
                if (!cacheSuc && context.processStage != EPlatformProcessStage.CI_Apply)
                {
                    //执行平台规则,去处理和生成资产
                    foreach (var rule in currentPlatformSetting.rules)
                    {
                        var code = RunRule(inputParams, rule, false);
                        if (code == (int)EPlatformErrorCode.BreakCode)
                        {
                            break;
                        }
                        
                        bool isSuc = code == (int)EPlatformErrorCode.Suc ;
                        if (isSuc)
                        {
                            success = true;
                            logger.InfoFormat("执行规则成功 assetpath:{0} rule:{1} ", assetPath, rule.desc);
                        }
                        else
                        {
                            success = false;
                            logger.ErrorFormat("执行规则失败 assetpath:{0} rule:{1} ", assetPath, rule.desc);
                        }
                    }
                }
                
                var subEndTime = EditorApplication.timeSinceStartup;
                
                context.processCostMap[assetPath] = subEndTime - subStartTime;

                if (enableLog)
                    logger.InfoFormat("处理 {0} 耗时:{1}", assetPath, FormatTime(subEndTime - subStartTime));
            }
            
            //输出下Stats
            logger.InfoFormat("开始输出规则统计信息");
            foreach (var (rule, stats) in context.ruleStats)
            {
                var dbgInfo = GetFormattedStats(stats);
                logger.Info(dbgInfo);
            }

            //成功才去生成
            if (success && context.processStage == EPlatformProcessStage.CI_Product)
            {
                var build_startTime = EditorApplication.timeSinceStartup;
                
                //必须确保saveassets
                AssetDatabase.SaveAssets();
                
                Copy2PlatformBuildCache(context);
                GeneratePlatformBuildCache(context);

                var build_endTime = EditorApplication.timeSinceStartup;
                if (enableLog)
                    logger.InfoFormat("生成平台资源缓存 耗时:{0}", FormatTime(build_endTime - build_startTime));
            }
            
            //最后保底刷新下
            if (!Application.isPlaying)
            {
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
            }  

            var endTime = EditorApplication.timeSinceStartup;
            if (enableLog)
                logger.InfoFormat("平台资源优化完成 耗时:{0}", FormatTime(endTime - startTime));

            return success;
        }
        
        private static string FormatTime(double totalTimeInSeconds)
        {
            const double minutes = 60;
            const double hours = 3600;
            const double days = 86400;

            if (totalTimeInSeconds < minutes)
            {
                return $"{totalTimeInSeconds:0.00} 秒";
            }
            else if (totalTimeInSeconds < hours)
            {
                return $"{(totalTimeInSeconds / minutes):0.00} 分钟";
            }
            else if (totalTimeInSeconds < days)
            {
                return $"{(totalTimeInSeconds / hours):0.00} 小时";
            }
            else
            {
                return $"{(totalTimeInSeconds / days):0.00} 天";
            }
        }
        
        public static string GetFormattedStats(PlatformPackRuleStats stats)
        {
            StringBuilder sb = new StringBuilder();
    
            // 标题部分
            sb.AppendLine($"<b><color=#FFAA00>【 {stats.ruleAsset?.desc ?? "Null Rule"} 打包统计 】</color></b>");
            sb.AppendLine($"<color=#999999>════════════════════════════════</color>");

            // 路径统计部分
            void AppendSet(string title, HashSet<string> set, Color color)
            {
                sb.AppendLine($"<b><color=#{ColorUtility.ToHtmlStringRGB(color)}>{title} ({set.Count})</color></b>");
                sb.AppendLine(set.Count > 0 
                    ? string.Join("\n", set.Select(p => $"  - {p}")) 
                    : "  None");
                sb.AppendLine();
            }
            
            AppendSet("✅ 缓存处理成功", stats.cacheProcessSucPaths, Color.green);
            AppendSet("❌ 缓存处理失败", stats.cacheProcessFailPaths, Color.red);
            AppendSet("⚪ 缓存处理忽略", stats.cacheProcessIgnorePaths, Color.gray);
            AppendSet("⚪ 缓存处理路径", stats.cacheProcessPaths, Color.gray);

            AppendSet("✅ 处理成功", stats.processSucPaths, Color.green);
            AppendSet("✅ 处理成功的依赖资产", stats.processSucDepPaths, Color.green);
            AppendSet("❌ 处理失败",stats. processFailPaths, Color.red);
            AppendSet("⚪ 忽略路径", stats.ignorePaths, Color.gray);
            AppendSet("📦 总处理路径", stats.processPaths, new Color(0.2f, 0.6f, 1f));
            sb.AppendLine($"<color=#999999>──────────────────────────────</color>");
            // 耗时统计部分 时分秒
            //更好的格式化
            var total = stats.processSucPaths.Count + stats.processFailPaths.Count;
            var cacheTotal = stats.cacheProcessSucPaths.Count + stats.cacheProcessFailPaths.Count;

            sb.AppendLine($"<b>总耗时:</b>  {FormatTime(stats.CostTotalTime + stats.CacheCostTotalTime)}");
            sb.AppendLine($"<b>缓存处理总耗时:</b>  {FormatTime(stats.CacheCostTotalTime)}");
            sb.AppendLine($"<b>缓存处理总量:</b>  {cacheTotal}");
            sb.AppendLine($"<b>缓存处理平均耗时:</b> {(cacheTotal > 0 ? stats.CacheCostTotalTime / cacheTotal : 0)} 秒");
            
            sb.AppendLine($"<b>资产处理总耗时:</b>  {FormatTime(stats.CostTotalTime)}");
            sb.AppendLine($"<b>资产处理总量:</b>  {total}");
            sb.AppendLine($"<b>资产处理平均耗时:</b> {(total > 0 ? stats.CostTotalTime / total : 0)} 秒");
            //输出最长耗时列表
            if (stats.CostMaxPaths.Count > 0)
            {
                sb.AppendLine($"<b>最长耗时路径:</b>");
                //数据是按照耗时从小到大排序的，所以我们需要倒序输出
                for (int i = stats.CostMaxPaths.Count - 1; i >= 0; i--)
                {
                    var costStat = stats.CostMaxPaths[i];
                    sb.AppendLine($"  - {costStat.ProcessPath} : {costStat.CostTime:0.00} 秒");
                }
            }
            else
            {
                sb.AppendLine($"<b>最长耗时路径:</b> 无");
            }
            
            // 统计总结
            sb.AppendLine($"<color=#999999>──────────────────────────────</color>");
            if (total > 0)
            {
                float sucPercent = ((float)stats.processSucPaths.Count / total) * 100;
                sb.AppendLine($"<b>资产处理成功率:</b> {sucPercent}%");
            }
            
            if (cacheTotal > 0)
            {
                float cacheProcessSucPercent = ((float)stats.cacheProcessSucPaths.Count / cacheTotal) * 100;
                sb.AppendLine($"<b>缓存处理成功率:</b> {cacheProcessSucPercent}%");
            }
    
            return sb.ToString();
        }


        private static int RunRule(PackInputParams inputParams, PlatformPackRule rule, bool throwExp = false)
        {
            inputParams.rulePath = AssetDatabase.GetAssetPath(rule);
            inputParams.ruleHash = AssetDatabase.GUIDFromAssetPath(inputParams.rulePath).ToString();

            var assetpath = inputParams.processSourcePath;
            bool shouldOptimize = rule.ShouldExecute(inputParams);

            AddRuleStats(inputParams, rule, StatsType.Process);
            
            if (shouldOptimize)
            {
                int code = -1;

                if (throwExp)
                {
                    code = rule.Execute(inputParams);
                }
                else
                {
                    try
                    {
                        code = rule.Execute(inputParams);
                    }
                    catch (Exception e)
                    {
                        logger.ErrorFormat("执行规则异常 assetpath:{0} rule:{1} error:{2}", assetpath, rule.desc, e);
                        code = (int)EPlatformErrorCode.ExceptionCode;
                    }
                }

                if (code == (int)EPlatformErrorCode.Suc)
                {
                    rule.PostProcessSuccessfully(inputParams);
                }
                
                var suc = code == (int)EPlatformErrorCode.Suc || code == (int)EPlatformErrorCode.BreakCode;
                
                StatsType type = StatsType.None;
                if (suc)
                {
                    type = StatsType.ProcessSuc;
                }
                else
                {
                    type = StatsType.ProcessFailed;
                }
                
                AddRuleStats(inputParams, rule, type);

                return code;
            }
            else
            {
                AddRuleStats(inputParams, rule, StatsType.Ignore);
            }

            return 0;
        }
        
        private static int RunCacheRule(PackInputParams inputParams, PlatformPackRule rule, bool throwExp = false)
        {
            inputParams.rulePath = AssetDatabase.GetAssetPath(rule);
            inputParams.ruleHash = AssetDatabase.GUIDFromAssetPath(inputParams.rulePath).ToString();

            var assetpath = inputParams.processSourcePath;

            AddRuleStats(inputParams, rule, StatsType.CacheProcess);
            
            int code = -1;

            logger.InfoFormat("尝试应用缓存 assetpath:{0} rule:{1}", assetpath, rule.desc);
            if (throwExp)
            {
                code = rule.TryApplyCache(inputParams);
            }
            else
            {
                try
                {
                    code = rule.TryApplyCache(inputParams);
                }
                catch (Exception e)
                {
                    logger.ErrorFormat("尝试应用缓存 assetpath:{0} rule:{1} error:{2}", assetpath, rule.desc, e);
                    code = (int)EPlatformErrorCode.ExceptionCode;
                }
            }
            
            StatsType statsType = StatsType.None;
            if (code == (int)EPlatformErrorCode.ContinueCode || code == (int)EPlatformErrorCode.BreakCode)
            {
                statsType = StatsType.CacheProcessIgnore;
            }
            else if (code == (int)EPlatformErrorCode.Suc )
            {
                statsType = StatsType.CacheProcessSuc;
            }
            else
            {
                statsType = StatsType.CacheProcessFailed;
            }
                
            AddRuleStats(inputParams, rule, statsType);

            return code;
        }

        private static void AddRuleStats(PackInputParams inputParams,PlatformPackRule rule,StatsType statsType)
        {
            logger.InfoFormat("添加规则统计信息 assetpath:{0} rule:{1} statsType:{2}", 
                inputParams.processSourcePath, rule.desc, statsType);
            var context = inputParams.context;
            if (context == null || statsType == StatsType.None)
            {
                return;
            }
            
            if(context.ruleStats.TryGetValue(rule, out var stats) == false)
            {
                stats = new PlatformPackRuleStats();
                stats.ruleAsset = rule;
                context.ruleStats[rule] = stats;
            }

            if (statsType == StatsType.Process)
            {
                stats.processPaths.Add(inputParams.processSourcePath);

                stats.CostBeginTime = EditorApplication.timeSinceStartup;
            }
            else if (statsType == StatsType.ProcessFailed)
            {
                stats.processFailPaths.Add(inputParams.processSourcePath);
                
                var costTime = EditorApplication.timeSinceStartup - stats.CostBeginTime;
                stats.CostTotalTime += costTime;
                
                stats.AddCostTime(inputParams.processSourcePath,costTime);
            }
            else if (statsType == StatsType.ProcessSuc)
            {
                stats.processSucPaths.Add(inputParams.processSourcePath);
                
                var costTime = EditorApplication.timeSinceStartup - stats.CostBeginTime;
                stats.CostTotalTime += costTime;
                
                stats.AddCostTime(inputParams.processSourcePath,costTime);
            }
            else if (statsType == StatsType.ProcessDepSuc)
            {
                stats.processSucDepPaths.Add(inputParams.processSourcePath);
            }
            else if (statsType == StatsType.Ignore)
            {
                stats.ignorePaths.Add(inputParams.processSourcePath);
                var costTime = EditorApplication.timeSinceStartup - stats.CostBeginTime;
                stats.CostTotalTime += costTime;
                
            }
            else if (statsType == StatsType.CacheProcess)
            {
                stats.cacheProcessPaths.Add(inputParams.processSourcePath);
                stats.CacheCostBeginTime = EditorApplication.timeSinceStartup;
            }
            else if (statsType == StatsType.CacheProcessFailed)
            {
                stats.cacheProcessFailPaths.Add(inputParams.processSourcePath);
                
                var costTime = EditorApplication.timeSinceStartup - stats.CacheCostBeginTime;
                stats.CacheCostTotalTime += costTime;
                
                stats.AddCostTime(inputParams.processSourcePath,costTime);
            }
            else if (statsType == StatsType.CacheProcessSuc)
            {
                stats.cacheProcessSucPaths.Add(inputParams.processSourcePath);
                
                var costTime = EditorApplication.timeSinceStartup - stats.CacheCostBeginTime;
                stats.CacheCostTotalTime += costTime;
                
                stats.AddCostTime(inputParams.processSourcePath,costTime);
            }
            else if (statsType == StatsType.CacheProcessIgnore)
            {
                stats.cacheProcessIgnorePaths.Add(inputParams.processSourcePath);
                
                var costTime = EditorApplication.timeSinceStartup - stats.CacheCostBeginTime;
                stats.CacheCostTotalTime += costTime;
            }
        }

        public static PlatformPackBuildCache BuildPlatformBuildCache(PlatformPackContext context)
        {
            logger.InfoFormat( "开始构建BuildCache" +
                               " targetPlatform:{0} processStage:{1}", context.targetPlatform, context.processStage);
            var targetPlatformPath = Application.dataPath + "/" + PlatformPackPath.SocPlatformRes + "/" +
                                     context.targetPlatform;

            if (Directory.Exists(targetPlatformPath) == false)
            {
                logger.InfoFormat("没有找到平台资源目录 targetPlatformPath:{0}", targetPlatformPath);
                return null;
            }

            var targetPlatformManifestPath = targetPlatformPath + "/" +PlatformPackConst.platformBuildCacheJson;
            if (File.Exists(targetPlatformManifestPath) == false)
            {
                logger.InfoFormat("没有找到平台资源目录 manifest targetPlatformPath:{0}", targetPlatformManifestPath);
                return null;
            }

            var jsonStr = File.ReadAllText(targetPlatformManifestPath);
            var result = JsonConvert.DeserializeObject<PlatformPackBuildCache>(jsonStr);
            if (result == null)
            {
                logger.InfoFormat("buildcache 反序列化 manifest失败 targetPlatformPath:{0}", targetPlatformManifestPath);
                return null;
            }

            if (result.revision != context.ruleRevision)
            {
                logger.InfoFormat("buildcache 平台资源缓存版本不一致 gitDataPath:{0} revision:{1}", context.ruleRevision, result.revision);
                return null;
            }

            logger.InfoFormat("构建BuildCache 完成 targetPlatformPath:{0} revision:{1}", targetPlatformPath,
                result.revision);
            return result;
        }

        //校验缓存
        public static void ValidateCache(PlatformPackContext context)
        {
            //开始校验svn
            if (context.svnCache != null)
            {
                HashSet<string> errorSet = new HashSet<string>();
                foreach (var (key, file) in context.svnCache.files)
                {
                    var guid = AssetDatabase.GUIDFromAssetPath(file.unityPath).ToString();
                    if (guid != file.assetGuid && !errorSet.Contains(file.assetGuid))
                    {
                        logger.ErrorFormat("ValidateCache svn缓存的guid和原始文件的guid不一致 工程内guid:{0} 缓存的guid:{1} 强制修正 path:{2}", guid, file.assetGuid,file.unityPath);

                        errorSet.Add(file.assetGuid);
                        
                        file.assetGuid = guid;
                    }
                }

                foreach (var (key, dir) in context.svnCache.dirs)
                {
                    foreach (var file in dir.files)
                    {
                        var guid = AssetDatabase.GUIDFromAssetPath(file.unityPath).ToString();
                        if (guid != file.assetGuid && !errorSet.Contains(file.assetGuid))
                        {
                            logger.ErrorFormat("ValidateCache svn缓存的guid和原始文件的guid不一致 工程内guid:{0} 缓存的guid:{1} 强制修正 path:{2}",
                                guid, file.assetGuid,file.unityPath);
                            errorSet.Add(file.assetGuid);
                            file.assetGuid = guid;
                        }
                    }
                }
            }
            //git不校验

            //buildcache校验
            if (context.buildCache != null)
            {
                HashSet<string> removeSet = new ();
                //guid不匹配的话，说明可能存在手动修改，或者后处理修改，那这个文件就不可信任了，需要删除
                foreach (var (key, file) in context.buildCache.files)
                {
                    var guid = AssetDatabase.GUIDFromAssetPath(file.AssetInfo.sourceUnityPath).ToString();
                    if (guid != file.AssetInfo.sourceAssetGuid)
                    {
                        logger.ErrorFormat("ValidateCache buildcache的guid和原始文件的guid不一致 工程内guid:{0} 缓存的guid:{1} path:{2} 需要移除",
                            guid, file.AssetInfo.sourceAssetGuid,file.AssetInfo.sourceUnityPath);

                        removeSet.Add(key);
                    }
                    
                    var cacheGuid = AssetDatabase.GUIDFromAssetPath(file.cacheFilePath).ToString();
                    if (cacheGuid != file.cacheFileAssetGuid)
                    {
                        logger.ErrorFormat("ValidateCache buildcache的guid和原始文件的guid不一致 工程内guid:{0} 缓存的guid:{1} path:{2} 需要移除",
                            cacheGuid, file.cacheFileAssetGuid,file.AssetInfo.sourceUnityPath);

                        removeSet.Add(key);
                    }
                }

                foreach (var (key, file) in context.buildCache.dynamicFiles)
                {
                    var sourceAssetGuid = AssetDatabase.GUIDFromAssetPath(file.sourceUnityPath).ToString();
                    if (sourceAssetGuid != file.sourceAssetGuid)
                    {
                        logger.ErrorFormat("ValidateCache buildcache 的sourceUnityPath guid和原始文件的guid不一致 工程内guid:{0} 缓存的guid:{1} 需要移除 {2}",
                            sourceAssetGuid, file.sourceAssetGuid,file.sourceUnityPath);
                        removeSet.Add(key);
                    }
                    
                    var cacheAssetGuid = AssetDatabase.GUIDFromAssetPath(file.dynamicFilePath).ToString();
                    if (cacheAssetGuid != file.dynamicAssetGuid)
                    {
                        logger.ErrorFormat("ValidateCache buildcache的dynamicFilePath guid和原始文件的guid不一致 工程内guid:{0} 缓存的guid:{1} 需要移除 {2}",
                            cacheAssetGuid, file.dynamicAssetGuid,file.dynamicFilePath);
                        
                        removeSet.Add(key);
                    }
                }

                foreach (var removekey in removeSet)
                {
                    bool suc1 = context.buildCache.files.Remove(removekey);
                    bool suc2 = context.buildCache.dynamicFiles.Remove(removekey);

                    if (suc1)
                    {
                        logger.InfoFormat("ValidateCache 移除files缓存成功 removekey:{0}", removekey);
                    }
                    
                    if (suc2)
                    {
                        logger.InfoFormat("ValidateCache 移除dynamicFiles缓存成功 removekey:{0}", removekey);
                    }
                    
                    if(!suc1 && !suc2)
                    {
                        throw new Exception($"ValidateCache 移除缓存失败 removekey:{removekey}");
                    }
                }
            }
        }

        public static void Copy2PlatformBuildCache(PlatformPackContext context)
        {
            string saveFolder = context.cacheRootPath;

            foreach (var (key, modifyData) in context.modifySet)
            {
                var assetPath = AssetDatabase.GetAssetPath(modifyData.modifyObject);
                
                string finalPath = "";
                if (assetPath.StartsWith(saveFolder) == false)
                {
                    var relativePath = PlatformPackPath.GetGameRelativePath(assetPath);
                    finalPath = PlatformPackPath.PathCombine(saveFolder, relativePath);
                    logger.InfoFormat("开始拼接路径 assetPath:{0} finalPath:{1}", assetPath, finalPath);
                }
                else
                {
                    finalPath = assetPath;
                }

                var dir = Path.GetDirectoryName(finalPath);
                if (Directory.Exists(dir) == false)
                {
                    Directory.CreateDirectory(dir);
                }
                
                //存在也无所谓，因为只是用到了其内容，就算guid变化了也没关系
                bool suc = PlatformPackUtils.CopyAsset(assetPath, finalPath);
                if (suc)
                {
                    logger.InfoFormat("copy asset success assetpath:{0} finalPath:{1}", assetPath, finalPath);
                }
                else
                {
                    logger.ErrorFormat("copy asset failed assetpath:{0} finalPath:{1}", assetPath, finalPath);
                }
            }

            //动态文件直接在缓存目录操作，不需要拷贝了
            // foreach (var (key, modifyData) in context.dynamicModifySet)
            // {
            //     var assetPath = modifyData.dynamicModifyPath;
            //     var relativePath = PlatformPackPath.GetGameRelativePath(assetPath);
            //     var finalPath = PlatformPackPath.PathCombine(saveFolder, relativePath);
            //
            //     var dir = Path.GetDirectoryName(finalPath);
            //     if (Directory.Exists(dir) == false)
            //     {
            //         Directory.CreateDirectory(dir);
            //     }
            //     
            //     //需要判断是否存在，且guid发生改变
            //     if (File.Exists(finalPath))
            //     {
            //         var targetGuid = AssetDatabase.GUIDFromAssetPath(finalPath).ToString();
            //         //说明这边新生成的资产存在问题，不能覆盖过去,一旦覆盖过去，就会导致guid变化，只能拷贝文件本身
            //         if (targetGuid != modifyData.dynamicAssetGuid)
            //         {
            //             logger.InfoFormat("copy dynamic asset 发现目标文件的guid和原始文件的guid不一致 targetGuid:{0} dynamicAssetGuid:{1} path:{2} 只能只拷贝文件本身，而不拷贝guid",
            //                 targetGuid, modifyData.dynamicAssetGuid, finalPath);
            //             File.Copy(finalPath, finalPath, true);
            //             continue;
            //         }
            //     }
            //
            //     bool suc = AssetDatabase.CopyAsset(assetPath, finalPath);
            //     if (suc)
            //     {
            //         logger.InfoFormat("copy dynamic asset success assetpath:{0} finalPath:{1}", assetPath, finalPath);
            //     }
            //     else
            //     {
            //         logger.ErrorFormat("copy dynamic asset failed assetpath:{0} finalPath:{1}", assetPath, finalPath);
            //     }
            // }
        }

        public static void GeneratePlatformBuildCache(PlatformPackContext context)
        {
            string saveFolder = context.cacheRootPath;

            var oldBuildCache = context.buildCache;

            PlatformPackBuildCache buildCache = new PlatformPackBuildCache();
            buildCache.revision = context.ruleRevision;
            buildCache.platform = context.targetPlatform;

            foreach (var (assetPath, modifyData) in context.modifySet)
            {
                var relativePath = PlatformPackPath.GetGameRelativePath(assetPath);
                var finalPath = PlatformPackPath.PathCombine(context.cacheRootPath, relativePath);

                if (File.Exists(finalPath) == false)
                {
                    logger.ErrorFormat("没有找到生成的缓存文件 finalPath:{0}", finalPath);
                    continue;
                }

                if (modifyData.sourceFileInfo == null)
                {
                    throw new Exception($"GeneratePlatformBuildCache 没有找到原始文件信息 modifyData:{assetPath}");
                }
                
                if(context.depCache.TryGetValue(assetPath, out var depCache) == false)
                {
                    throw new Exception($"GeneratePlatformBuildCache 没有找到依赖缓存信息 assetPath:{assetPath}");
                }

                PlatformPackBuildFileCache fileCache = new ();
                fileCache.AssetInfo.sourceRevision = modifyData.sourceFileInfo.revision;
                fileCache.AssetInfo.sourceFilePath = modifyData.sourceFileInfo.path;
                fileCache.AssetInfo.sourceUnityPath = modifyData.sourceFileInfo.unityPath;
                fileCache.AssetInfo.sourceAssetGuid = modifyData.sourceFileInfo.assetGuid;
                fileCache.dependencies = depCache;
                fileCache.cacheRuleHash = modifyData.ruleGuid;
                fileCache.childAssets = modifyData.childAssets;
                fileCache.cacheFilePath = finalPath;
                fileCache.cacheFileAssetGuid = AssetDatabase.GUIDFromAssetPath(finalPath).ToString();

                buildCache.files.Add(assetPath, fileCache);
                
                logger.InfoFormat("GeneratePlatformBuildCache 添加files缓存 assetPath:{0} ", assetPath);
            }

            foreach (var (dynamicAssetPath, modifyData) in context.dynamicModifySet)
            {
                var finalPath = dynamicAssetPath;

                if (File.Exists(finalPath) == false)
                {
                    logger.ErrorFormat("没有找到生成的缓存文件 finalPath:{0}", finalPath);
                    continue;
                }
                
                if (modifyData.sourceFileInfo == null)
                {
                    throw new Exception($"GeneratePlatformBuildCache 没有找到原始文件信息 modifyData:{dynamicAssetPath}");
                }

                PlatformPackBuildDynamicFileCache fileCache = new ();
                fileCache.sourceFileRevision = modifyData.sourceFileInfo.revision;
                fileCache.sourceFilePath = modifyData.sourceFileInfo.path;
                fileCache.sourceUnityPath = modifyData.sourceFileInfo.unityPath;
                fileCache.sourceAssetGuid = modifyData.sourceFileInfo.assetGuid;
                fileCache.dynamicFilePath = dynamicAssetPath;
                fileCache.dynamicAssetGuid = modifyData.dynamicAssetGuid;
                buildCache.dynamicFiles.Add(dynamicAssetPath, fileCache);
                
                logger.InfoFormat("GeneratePlatformBuildCache 添加dynamicFiles缓存 dynamicAssetPath:{0} ", dynamicAssetPath);
            }

            //合并
            if (oldBuildCache != null)
            {
                foreach (var (oldhash, oldfileCache) in oldBuildCache.files)
                {
                    //首先检查file cache的有效性
                    //检查原始文件数据 和缓存文件数据是否正常
                    if (File.Exists(oldfileCache.cacheFilePath) == false)
                    {
                        throw new Exception($"GeneratePlatformBuildCache Files 没有找到缓存文件 fileCache.cacheFilePath:{oldfileCache.cacheFilePath}");
                    }

                    if (File.Exists(oldfileCache.AssetInfo.sourceUnityPath) == false)
                    {
                        throw new Exception($"GeneratePlatformBuildCache Files 没有找到原始文件 fileCache.sourceUnityPath:{oldfileCache.AssetInfo.sourceUnityPath}");
                    }

                    var sourceGuid = AssetDatabase.GUIDFromAssetPath(oldfileCache.AssetInfo.sourceUnityPath).ToString();
                    if (sourceGuid != oldfileCache.AssetInfo.sourceAssetGuid)
                    {
                        throw new Exception($"GeneratePlatformBuildCache Files 原始文件的guid和缓存的guid不一致 工程内guid:{sourceGuid} 缓存的guid:{oldfileCache.AssetInfo.sourceAssetGuid} sourceUnityPath:{oldfileCache.AssetInfo.sourceUnityPath}");
                    }

                    var cacheGuid = AssetDatabase.GUIDFromAssetPath(oldfileCache.cacheFilePath).ToString();
                    if (cacheGuid != oldfileCache.cacheFileAssetGuid)
                    {
                        throw new Exception($"GeneratePlatformBuildCache Files 缓存文件的guid和原始文件的guid不一致 工程内guid:{cacheGuid} 缓存的guid:{oldfileCache.cacheFileAssetGuid} cacheFilePath:{oldfileCache.cacheFilePath}");
                    }

                    //检查当前的缓存记录中是否存在老资产，如果存在，则简单检查下老资源配置的有效性
                    if (buildCache.files.TryGetValue(oldhash, out var newFileCache))
                    {
                        //校验数据是否一致
                        if (newFileCache.CompareSafe(oldfileCache) == false)
                        {
                            logger.ErrorFormat("GeneratePlatformBuildCache Files 发现和现有的缓存数据不匹配 hash:{0} fileCache:{1} newFileCache:{2}", oldhash, oldfileCache.GetPrintableStr(), newFileCache.GetPrintableStr());
                        }
                    }
                    //如果存在不在当前缓存的老资产记录，那就合并过去
                    else
                    {
                        logger.InfoFormat("GeneratePlatformBuildCache Files 合并缓存数据 hash:{0} fileCache:{1} ", oldhash, oldfileCache.GetPrintableStr());
                        buildCache.files.Add(oldhash, oldfileCache);
                    }
                }

                foreach (var (key, fileCache) in oldBuildCache.dynamicFiles)
                {
                    //首先检查file cache的有效性
                    //检查原始文件数据 和缓存文件数据是否正常
                    if (File.Exists(fileCache.dynamicFilePath) == false)
                    {
                        throw new Exception($"GeneratePlatformBuildCache dynamicFiles 没有找到缓存文件 fileCache.dynamicFilePath:{fileCache.dynamicFilePath}");
                    }

                    if (File.Exists(fileCache.sourceUnityPath) == false)
                    {
                        throw new Exception($"GeneratePlatformBuildCache dynamicFiles 没有找到原始文件 fileCache.sourceFilePath:{fileCache.sourceUnityPath}");
                    }

                    var sourceGuid = AssetDatabase.GUIDFromAssetPath(fileCache.sourceUnityPath).ToString();
                    if (sourceGuid != fileCache.sourceAssetGuid)
                    {
                        throw new Exception($"GeneratePlatformBuildCache dynamicFiles 原始文件的guid和缓存的guid不一致 工程内guid:{sourceGuid} 缓存的guid:{fileCache.sourceAssetGuid} sourceUnityPath:{fileCache.sourceUnityPath}");
                    }

                    var cacheGuid = AssetDatabase.GUIDFromAssetPath(fileCache.dynamicFilePath).ToString();
                    if (cacheGuid != fileCache.dynamicAssetGuid)
                    {
                        throw new Exception($"GeneratePlatformBuildCache dynamicFiles 缓存文件的guid和原始文件的guid不一致 工程内guid:{cacheGuid} 缓存的guid:{fileCache.dynamicAssetGuid} dynamicFilePath:{fileCache.dynamicFilePath}");
                    }

                    //检查当前的缓存记录中是否存在老资产，如果存在，则简单检查下老资源配置的有效性
                    if (buildCache.dynamicFiles.TryGetValue(key, out var newFileCache))
                    {
                        //校验数据是否一致
                        if (newFileCache.Compare(fileCache) == false)
                        {
                            logger.ErrorFormat("GeneratePlatformBuildCache dynamicFiles 发现和现有的缓存数据不匹配 hash:{0} fileCache:{1} newFileCache:{2}", key, fileCache.GetPrintableStr(), newFileCache.GetPrintableStr());
                        }
                    }
                    else
                    {
                        logger.InfoFormat("GeneratePlatformBuildCache dynamicFiles 合并缓存数据 hash:{0} fileCache:{1}", key, fileCache.GetPrintableStr());
                        buildCache.dynamicFiles.Add(key, fileCache);
                    }
                }
            }

            var finalJson = JsonConvert.SerializeObject(buildCache, Formatting.Indented);
            var finaljsonPath = PlatformPackPath.PathCombine(saveFolder, PlatformPackConst.platformBuildCacheJson);

            // //验证用
            // if (File.Exists(finaljsonPath))
            // {
            //     var oldJson = File.ReadAllText(finaljsonPath);
            //     if (oldJson == finalJson)
            //     {
            //         logger.InfoFormat("GeneratePlatformBuildCache 发现缓存文件没有变化，不需要重新生成");
            //         return;
            //     }
            // }
            
            File.WriteAllText(finaljsonPath, finalJson);
        }
    }
}