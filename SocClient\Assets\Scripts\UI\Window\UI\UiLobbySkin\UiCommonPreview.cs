﻿using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using FairyGUI;
using System;
using System.Collections.Generic;
using Systems;
using UnityEngine;
using WizardGames.Soc.Common.Data.DataItem;
using WizardGames.Soc.Common.Data.resource;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.Unity.Config;
using WizardGames.Soc.Common.Unity.ObjPool;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.Common.Unity.Utility;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Unity;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.Combat;
using WizardGames.Soc.Common.Weapon;
using WizardGames.Soc.SocClient.GoLoader;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Ui.Binder.LobbySkin;
using WizardGames.Soc.SocClient.Utility;
using Cinemachine.Utility;
using UnityEngine.Rendering.Universal;
using WizardGames.Soc.Common.Data.collection;
using UnityEngine.Rendering.Universal.WDVT;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.Common.NodeSystem;
using Object = UnityEngine.Object;

namespace WizardGames.Soc.SocClient.Ui
{
    public class UiCommonPreview : WindowComBase, IUiFpsUnlimitedUpdate
    {
        //视角
        enum EPerspectiveType
        {
            ModelP,
            FirstP,
            ThirdP
        }
        EPerspectiveType perspectiveType=EPerspectiveType.ModelP;
        List<SkinPreviewData> previewDataList;
        List<SkinPreviewItem> previewItemList;
        ComPreviewRootBinder binderRoot;
        ComPreviewContentBinder binder;
        SkinPreviewData curSelectData;
        SkinPreviewItem curSelectItem;
        
        ComUiPlayerModel tpPlayerModel;
        ComUiPlayerModel fpPlayerModel;
        ComUiModel WeaponModel;
        ComUiModel itemModel;
        ComTopBar topBar;
        private Vector3 uiModelCameraPosition;
        private Transform uiModelCamera;
        //当前需要显示的所有装备数据，可叠加
        public List<SkinPreviewData> EquipDataList { private set; get; }
        /// <summary>
        /// 界面关闭时调用
        /// </summary>
        Action onWndClose;
        //是否显示tab,单个不显示，多个时：收藏界面进入不显示，其他界面进入显示
        bool isShowTab = false;
        //是否显示描述，正常情况都需要显示。特殊：时装收藏界面如果没选择任何衣服，则不显示描述
        //bool isAutoShowDesc = true;
        ComPartPreviewBinder previewBind;
        long[] curShowAccess;
        bool isLongPressing = false;

        /// -------枪械部件预览------
        private Dictionary<long, (GameObject go, string path)> accessorys = new();
        private List<long> tempAccessoryList = new();
        // private int accessoryGroupIndex = 0;
        private long selectedAccessorySkinId = 0;
        private TweenerCore<Vector3, Vector3, VectorOptions> tweener;
        //上一个选中的武器配件
        private GButton lastSelectPartItem=null;
        /// -------------End-----------
        private int curBgId = 0;

        protected override void OnInit()
        {
            base.OnInit();
            binderRoot = new ComPreviewRootBinder(ContentPane.GetChild("root").asCom);
            binder = binderRoot.Content;
            topBar = binderRoot.TopBar as ComTopBar;
            topBar.SetBackButtonClicked(OnEscClose);
            topBar.Title = LanguageManager.GetTextConst(LanguageConst.PreViewTitle);
            binder.HighLightBtn.changeStateOnClick = false;
            binder.HighLightBtn.onClick.Add(OnClickHighLight);
            binder.PerspectiveBtn.onClick.Add(OnClickSwitchPerspective);
            binder.PreviewModel.onClick.Add(OnClickPreviewModel);
            binder.ComDescription.CtrlCollected.selectedPage = "Hide";
            binder.ComDescription.CtrlShare.selectedPage = "Hide";
            binder.PreviewList.itemRenderer = OnRenderItem;
            previewBind = binder.ComPartPreview;
            previewBind.PartPreviewBtn.onClick.Add(OnPartPreviewClick);
            previewBind.NormalBtn.onClick.Add(OnNormalClick);
            previewBind.SuperBtn.onClick.Add(OnSuperClick);
            previewBind.PartPreviewList.itemRenderer = OnRenderPartPreviewList;
            previewDataList = new ();
            previewItemList = new();
            EquipDataList = new();
            itemModel = binder.UiModel as ComUiModel;
            WeaponModel = binder.UiModelWeapon as ComUiModel;
            binder.CtrlModelType.selectedPage = "tplayer";
            InitItemModel();

        }
        protected override void OnEnable()
        {
            base.OnEnable();
            UnityEngine.Shader.SetGlobalFloat(PropertyToID.UberAHash, 1);
            topBar.OnEnable();
        }

        public void SetModelCacheState(bool openCache)
        {
            if (null == itemModel) return;
            itemModel.DestroyModelResOnFree = !openCache;
        }

        /// <summary>
        /// 显示多个预览皮肤，如果是单个请使用SetShowData(SkinPreviewData data)
        /// </summary>
        /// <param name="dataList">"资源分表-皮肤"ID</param>
        /// <param name="onClose">关闭预览的回调</param>
        /// <param name="showTab">是否显示右边tab栏，一般是True，收藏界面跳转有特殊需求</param>
        /// <param name="selectIndex">选中的item下标</param>
        public void SetShowData(List<SkinPreviewData> dataList,Action onClose=null,bool showTab=true,int selectIndex=0)
        {
            isShowTab = showTab;
            //isAutoShowDesc = autoShowDesc;
            onWndClose = onClose;
            previewItemList.Clear();
            
            binder.CtrlStatus.selectedPage =showTab?"normal":"hideTab";
            previewDataList = dataList;


            if (showTab)
            {
                for (int i = 0; i < previewDataList.Count; i++)
                {
                    previewItemList.Add(new SkinPreviewItem(previewDataList[i]));
                }
                binder.PreviewList.numItems = dataList.Count;
                selectIndex = Mathf.Clamp(selectIndex, 0, previewItemList.Count - 1);
                previewItemList[selectIndex].OnClick();
            }else
            {
                EquipDataList.AddRange(dataList);
                InitTpPlayerModel();
                RefreshTpPlayerModel();
            }
            //curSelectData = previewDataList[0];
        }

        /// <summary>
        /// 显示单个预览皮肤，隐藏右侧Tab页签
        /// </summary>
        /// <param name="skinId"></param>
        public void SetShowData(SkinPreviewData data,Action onClose=null)
        {
            if (data==null)
            {
                return;
            }
            onWndClose = onClose;
            binder.CtrlStatus.selectedPage = "hideTab";
            UpdateOneWnd(data);
        }
        private void OnRenderItem(int index,GObject obj)
        {
            var comItem = obj.asCom;
            GButton tabItem = comItem.GetChild("TabItem").asButton;
            SkinPreviewData previewData = previewDataList[index];
            SkinPreviewItem previewItem = previewItemList[index];
            previewItem.UpdateCom(this, tabItem, previewData);
            var ctrlGotState = comItem.GetController("gotState");
            ctrlGotState.SetSelectedPage(previewData.isGot? "show": "hide");
        }
        protected override void OnDisable()
        {
            base.OnDisable();
            //itemModel.Hide();
            //uiModelCamera.position = uiModelCameraPosition;
        }
        public override void OnDestroy()
        {
            base.OnDestroy();
            if (WeaponModel != null)
            {
                RemoveAllAccessories();
            }
            curBgId = 0;
            WeaponModel?.Dispose();
            itemModel?.Dispose();
            tpPlayerModel?.Dispose();
            tpPlayerModel = null;

            fpPlayerModel?.Dispose();
            fpPlayerModel = null;

            onWndClose?.Invoke();
            ResetPartPreview();
            UnityEngine.Shader.SetGlobalFloat(PropertyToID.UberAHash, 0);
            topBar.OnDisable();
        }

        /// <summary>
        /// 更新单个
        /// </summary>
        /// <param name="data"></param>
        public void UpdateOneWnd(SkinPreviewData data)
        {
            curSelectData = data;
            EquipDataList.Add(curSelectData);
            SetContent(curSelectData);
            if (curSelectData.GetSkinCfg()!=null)
            {
                SetBtnGroupVisible(curSelectData.GetSkinCfg().SkinType == ENUMSkinType.Weapon);
            }else
            {
                SetBtnGroupVisible(false);
            }
            UpdateShowModelType(curSelectData);
        }
        /// <summary>
        /// 多个Item更新界面处理
        /// </summary>
        /// <param name="data"></param>
        public void UpdateMultiWnd(SkinPreviewItem item,bool isSelect)
        {

            if (isSelect)
            {
                if (curSelectItem!=null)
                {
                    curSelectItem.SetSelect(false);
                }
                curSelectItem = item;
                curSelectData = item.PreviewData;
                SetContent(curSelectData);
                if (curSelectData.GetSkinCfg()!=null)
                {
                    SetBtnGroupVisible(curSelectData.GetSkinCfg().SkinType == ENUMSkinType.Weapon);
                }else
                {
                    SetBtnGroupVisible(false);
                }
                if (CheckCanAdd(curSelectData))
                {
                    if (!EquipDataList.Contains(curSelectData))//不能重复添加
                    {
                        CheckConflict(curSelectData);
                        EquipDataList.Add(curSelectData);
                    }
                }
                else
                {
                    //重置选中
                    for (int i = 0; i < previewItemList.Count; i++)
                    {
                        if (previewItemList[i] != item )
                        {
                            previewItemList[i].SetEquip(false);
                        }
                    }
                    EquipDataList.Clear();
                    EquipDataList.Add(curSelectData);
                }
                UpdateShowModelType(curSelectData);
            }
            else
            {
                SetContent(null);
                EquipDataList.Remove(curSelectData);
                curSelectItem = null;
                curSelectData = null;
                if (EquipDataList.Count==0)
                {
                    SetCtrlState("empty");
                }else
                {

                    SetBtnGroupVisible(false);
                    //根据剩下装备选择使用那种模型
                    SkinPreviewData data = null;
                    bool isSet = false;
                    for (int i = 0; i < EquipDataList.Count; i++)
                    {
                        if (CheckCostumeSuit(EquipDataList[i].skinId))
                        {
                            isSet = true;
                            UpdateShowModelType(EquipDataList[i]);
                            break;
                        }else
                        {
                            if(EquipDataList[i].GetSkinCfg().SkinType==ENUMSkinType.Equipment|| EquipDataList[i].GetSkinCfg().SkinType == ENUMSkinType.Costume)
                            {
                                isSet = true;
                                UpdateShowModelType(EquipDataList[i]);
                                break;
                            }
                        }
                    }
                    if(!isSet)
                    {
                        UpdateShowModelType(EquipDataList[0]);
                    }

                }

            }
        }
        /// <summary>
        /// 检查所有穿戴，自动提示低优先级，以及脱下互斥装备
        /// </summary>
        public void CheckConflict(SkinPreviewData curData)
        {
            var curCfg = curData.GetSkinCfg();
            if (curCfg!=null&&curData.GetSkinCfg().SkinType==ENUMSkinType.Weapon)
            {
                return;
            }
            List<int> skinIds = new List<int>();
            //已装备物品中的时装套装Id
            int equipSuitId = 0;
            for (int i = 0; i < EquipDataList.Count; i++)
            {
                if (CheckCostumeSuit(EquipDataList[i].skinId))
                {
                    equipSuitId = EquipDataList[i].skinId;
                    AddCostumeSuit(EquipDataList[i].skinId, skinIds, null);
                }else
                {
                    skinIds.Add(EquipDataList[i].skinId);
                }
            }
            List<OBJSkin> equipIds = new List<OBJSkin>();
            List<OBJSkin> costumeIds = new List<OBJSkin>();
            for (int i = 0; i < skinIds.Count; i++)
            {
                var skinCfg= Mc.Tables.TBSkin.GetOrDefault(skinIds[i]);
                if (skinCfg!=null)
                {
                    if (skinCfg.SkinType==ENUMSkinType.Equipment)
                    {
                        equipIds.Add(skinCfg);
                    }else if(skinCfg.SkinType==ENUMSkinType.Costume)
                    {
                        costumeIds.Add(skinCfg);
                    }
                }
            }
            Dictionary<int, int> equipDic = new();
            Dictionary<int, int> costumeDic = new();
            for (int i = 0; i < equipIds.Count; i++)
            {
                equipDic.Add(equipIds[i].BelongPosition, equipIds[i].ID);
            }
            for (int i = 0; i < costumeIds.Count; i++)
            {
                costumeDic.Add(costumeIds[i].BelongPosition, costumeIds[i].ID);
            }
            ///--------同装备或同时装的互斥脱下-------
            List<int> removeEquipId = new();
            List<int> removeCostumeId = new();
            if(CheckCostumeSuit(curData.skinId))
            {
                for (int i = 0; i < costumeIds.Count; i++)
                {
                    removeCostumeId.Add(costumeIds[i].ID);
                }
            }else
            {
                if (curCfg.SkinType == ENUMSkinType.Costume)
                {
                    int part = Mc.Tables.TbCostumeConfig.GetOrDefault(curCfg.ID).BelongPart;
                    Mc.LobbySkin.WearCostume(part, curCfg.ID, costumeDic);

                    for (int i = 0; i < costumeIds.Count; i++)
                    {
                        if (!costumeDic.ContainsValue(costumeIds[i].ID))
                        {
                            removeCostumeId.Add(costumeIds[i].ID);
                        }
                    }
                }
                else if (curCfg.SkinType == ENUMSkinType.Equipment)
                {
                    Mc.LobbySkin.WearEquip(curCfg.BelongPosition, curCfg.ID, equipDic);
                    for (int i = 0; i < equipIds.Count; i++)
                    {
                        if (!equipDic.ContainsValue(equipIds[i].ID))
                        {
                            removeEquipId.Add(equipIds[i].ID);
                        }
                    }
                }
            }
            for (int i = 0; i < removeEquipId.Count; i++)
            {
                GetPreviewItem(removeEquipId[i]).SetEquip(false);
            }
            bool isRemoveSuit = false;
            if (equipSuitId!=0)
            {
                var suitCfg = Mc.Tables.TBCostumeSuit.GetOrDefault(equipSuitId);
                for (int i = 0; i < removeCostumeId.Count; i++)
                {
                    if (suitCfg.CostumeIDs.Contains(removeCostumeId[i]))
                    {
                        isRemoveSuit = true;
                        break;
                    }
                }
            }

            if (isRemoveSuit)
            {
                var item = GetPreviewItem(equipSuitId);
                EquipDataList.Remove(item.PreviewData);
                item.SetEquip(false);
            }
            else
            {
                for (int i = 0; i < removeCostumeId.Count; i++)
                {
                    var item = GetPreviewItem(removeCostumeId[i]);
                    EquipDataList.Remove(item.PreviewData);
                    item.SetEquip(false);
                }
            }
            ///---------End----------

            ///---------低优先级显示提示--------
            if (CheckCostumeSuit(curData.skinId))
            {
                List<int> tempIds = new();
                AddCostumeSuit(curData.skinId, tempIds, null);
                for (int i = 0; i < tempIds.Count; i++)
                {
                    if(CheckPartIsLow(tempIds[i], false, equipDic, curData.skinId))
                    {
                        break;
                    }
                }
            }else
            {
                var skinCfg = Mc.Tables.TBSkin.GetOrDefault(curData.skinId);
                if (skinCfg.SkinType==ENUMSkinType.Costume)
                {
                    CheckPartIsLow(skinCfg.ID, false, equipDic, equipSuitId);
                }
                else
                {
                    CheckPartIsLow(skinCfg.ID, true, costumeDic, equipSuitId);
                }
            }
            ///-------------End--------------
        }
        public bool CheckPartIsLow(int partSkinId, bool partIsEquip, Dictionary<int, int> targetDic,int suitId=0)
        {
            long overrId = Mc.LobbySkin.PartIsLowWeight(partSkinId, partIsEquip, targetDic);
            if (overrId != 0 && partSkinId != 0)
            {
                OBJCostumeSuit suitCfg = null;
                if (suitId!=0)
                {
                    suitCfg=Mc.Tables.TBCostumeSuit.GetOrDefault(suitId);
                }
                if (partIsEquip)
                {
                    var skinCfg = Mc.Tables.TBSkin.GetOrDefault(partSkinId);
                    string skinName;
                    if (skinCfg.DefaultSkin == 1)
                    {
                        skinName = Mc.Tables.TbSkinArmorConfig.GetOrDefault(partSkinId).Name;
                    }
                    else
                    {
                        skinName = Mc.Tables.TbSkinConfig.GetOrDefault(partSkinId).Name;
                    }
                    string costumeName;
                    if (suitId!=0)
                    {
                        costumeName = suitCfg.Name;
                    }else
                    {
                        costumeName= Mc.Tables.TbCostumeConfig.GetOrDefault(overrId).Name;
                    }

                    Mc.MsgTips.ShowRealtimeWeakTip(24107, costumeName, skinName);
                    return true;
                }
                else
                {
                    string costumeName;
                    if (suitId==0)
                    {
                        costumeName = Mc.Tables.TbCostumeConfig.GetOrDefault(partSkinId).Name;
                    }else
                    {
                        costumeName = suitCfg.Name; ;
                    }
                    string skinName;
                    var skinCfg = Mc.Tables.TBSkin.GetOrDefault((int)overrId);
                    if (skinCfg.DefaultSkin == 1)
                    {
                        skinName = Mc.Tables.TbSkinArmorConfig.GetOrDefault((int)overrId).Name;
                    }
                    else
                    {
                        skinName = Mc.Tables.TbSkinConfig.GetOrDefault(overrId).Name;
                    }
                    Mc.MsgTips.ShowRealtimeWeakTip(24107, skinName, costumeName);
                    return true;
                }
            }
            return false;
        }
        private void UpdateShowModelType(SkinPreviewData data)
        {
            if (accessorys.Count > 0) RemoveAllAccessories();
            var cfg = data.GetSkinCfg();
            if (cfg != null)
            {
                if (cfg.SkinType == ENUMSkinType.Equipment || cfg.SkinType == ENUMSkinType.Armor || cfg.SkinType == ENUMSkinType.Costume||cfg.SkinType==ENUMSkinType.Gesture)
                {
                    SetPerspective(EPerspectiveType.ThirdP);
                }
                else
                {
                    SetPerspective(EPerspectiveType.ModelP);
                }
            }else if(CheckCostumeSuit(data.skinId))
            {
                SetPerspective(EPerspectiveType.ThirdP);
            }
            else
            {
                SetPerspective(EPerspectiveType.ModelP);
            }
            UpdatePartPreviewVisble();
        }
        //更新配件预览显示
        private void UpdatePartPreviewVisble(bool isOnlySetVisible=false)
        {
            bool isShow=false;
            if (curSelectData != null)
            {
                var cfg = curSelectData.GetSkinCfg();
                if (cfg != null)
                {
                    //首先得是武器，且不是原皮
                    isShow = cfg.SkinType == ENUMSkinType.Weapon&&cfg.DefaultSkin!=1;
                }
                if (isShow)
                {
                    //只有配置了配件组才显示
                    isShow = Mc.Tables.TbSkinConfig.GetOrDefault(curSelectData.skinId).AccessoryGroup.Length > 0;
                }
            }

            previewBind.BinderRoot.visible = isShow;
            if (isOnlySetVisible)
            {
                return;
            }
            binder.ComDescription.TxtDetail.visible = !isShow;
            if(isShow)
            {
                previewBind.SuperBtn.visible = Mc.Tables.TbSkinConfig.GetOrDefault(curSelectData.skinId).AccessoryGroup.Length > 1;
                SetCtrlParPreview(0);
            }
        }
        //更新配件预览界面
        private void UpdatePartPreview()
        {
            var accessGroup= Mc.Tables.TbSkinConfig.GetOrDefault(curSelectData.skinId).AccessoryGroup;
            curShowAccess = null;
            if (previewBind.CtrlSwitchPart.selectedIndex==0&&accessGroup.Length>0)
            {
                curShowAccess = Mc.Tables.TbSkinWeaponAccessoryGroup.GetOrDefault(accessGroup[0]).AccessoryList;
            }else if(previewBind.CtrlSwitchPart.selectedIndex == 1 && accessGroup.Length > 1)
            {
                curShowAccess = Mc.Tables.TbSkinWeaponAccessoryGroup.GetOrDefault(accessGroup[1]).AccessoryList;
            }

            CancelSelectAccessory();
            AddAccessories(curShowAccess);
            previewBind.PartPreviewList.numItems = curShowAccess.Length;
        }
        private void OnRenderPartPreviewList(int index,GObject obj)
        {
            GComponent partItem = obj.asButton;
            var skinCfg = Mc.Tables.TbSkinConfig.GetOrDefault(curShowAccess[index]);
            partItem.icon = skinCfg.Icon;
            partItem.data = skinCfg;
            LongPressGesture longPress = new LongPressGesture(partItem);
            longPress.once = false;
            longPress.trigger = 0.5f;
            longPress.interval = (float)0.02;
            longPress.holdRangeRadius = Int32.MaxValue;
            longPress.onBegin.Add(OnLongPressBegin);
            longPress.onEnd.AddFinalPass(OnLongPressEnd);
            partItem.onClick.Add(OnClickPartItem);
        }
        private void OnLongPressBegin(EventContext context)
        {
            isLongPressing = true;
            GButton btn = ((LongPressGesture)context.sender).host.asButton;
            //btn.onClick.Remove(OnClickPartItem);
            SkinConfig skinCfg = (SkinConfig)btn.data;
            var generalCfg = Mc.Tables.TBGeneral.GetOrDefault((int)skinCfg.Id);
            var globalPosition = btn.parent.LocalToGlobal(btn.position);
            globalPosition.x += btn.width;
            globalPosition.y += btn.height;
            UiItemTips.ShowTips(generalCfg, globalPosition,false);
        }
        private void OnLongPressEnd()
        {
            //GButton btn = ((LongPressGesture)context.sender).host.asButton;
            //btn.onClick.Add(OnClickPartItem);
            Mc.TimerWheel.AddTimerOnce(2, LateResetLongPressState);
            UiItemTips.HideTips();
        }
        private void LateResetLongPressState(long timerId, object data = null, bool delete = false)
        {
            isLongPressing = false;
        }
        private void OnClickPartItem(EventContext context)
        {
            if (isLongPressing)
            {
                //((GButton)context.sender).selected = false;
                isLongPressing = false;
                return;
            }
            GButton btn = ((GButton)context.sender);
            var ctr = btn.GetController("select");
            ctr.selectedPage= ctr.selectedPage=="yes"?"no":"yes";
            if (ctr.selectedPage=="yes")
            {
                if(lastSelectPartItem!=null)
                {
                    lastSelectPartItem.GetController("select").selectedPage = "no";
                }
                lastSelectPartItem = btn;
                SkinConfig skinCfg = (SkinConfig)btn.data;
                SelectAccessory(skinCfg.Id);
            }else
            {
                lastSelectPartItem = null;
                CancelSelectAccessory();
            }

        }
        private void OnPartPreviewClick()
        {
            var partPop =Mc.Ui.OpenWindowT<UiWeaponPartPop>("UiWeaponPartPop");
            partPop.UpdateWeaponPart(curSelectData.skinId);
        }
        private void OnNormalClick()
        {
            SetCtrlParPreview(0);
        }
        private void OnSuperClick()
        {
            SetCtrlParPreview(1);
        }
        private void SetCtrlParPreview(int index)
        {
            previewBind.CtrlSwitchPart.selectedIndex = index;
            UpdatePartPreview();
        }
        public void SetCtrlState(string page)
        {
            binder.CtrlStatus.selectedPage = page;
        }
        /// <summary>
        /// 设置界面标题和tab栏上方名称,
        /// </summary>
        /// <param name="title">默认显示预览，也可以自定义</param>
        /// <param name="tabName">如果不显示tab则不需要传</param>
        public void SetTitleAndTabName(string title, string tabName = null)
        {
            if (!string.IsNullOrEmpty(title)) topBar.Title = title;
            if (!string.IsNullOrEmpty(tabName)) binder.TitleCom.GetChild("title").asTextField.text = tabName;
        }
        public void SetContent(SkinPreviewData data)
        {
            if (data!=null)
            {
                SetCtrlState(previewDataList.Count > 1 ? "normal" : "hideTab");
                binder.ComDescription.TxtTitle.text = data.skinName;
                binder.ComDescription.TxtDetail.text = data.skinDesc;
                binder.ComDescription.CtrlRarity.selectedIndex = data.rarity;
            }
        }
        private void SetBtnGroupVisible(bool visible)
        {
            binder.BtnGroup.visible = visible;
            binder.HighLightBtn.visible = false;
            if (visible)
            {
                binder.HighLightBtn.visible = curSelectData.GetSkinCfg().Highlights.Count > 0;
                binder.PreviewModel.FireClick(true, true);
            }
        }

        private void PlayFpAnim(string animPath, string wpnAnim)
        {
            if (fpPlayerModel == null)
            {
                return;
            }

            
            if (fpPlayerModel.PlayAnim(animPath))
            {
                fpPlayerModel.PlayWeaponAnim(wpnAnim);
            }
        }
        /// <summary>
        /// 根据预览数据确定使用性别，如果没有性别限制则优先使用大厅性别
        /// </summary>
        private int GetSex()
        {
            for (int i = 0; i < EquipDataList.Count; i++)
            { 
                if(EquipDataList[i].GetSkinCfg()!=null&&EquipDataList[i].GetSkinCfg().SkinType==ENUMSkinType.Costume)
                {
                    var sex= Mc.Tables.TbCostumeConfig.GetOrDefault(EquipDataList[i].GetSkinCfg().ID).EquipGender;
                    if (sex!=2)
                    {
                        return sex;
                    }
                }
            }
            if (Mc.Ui.IsInGame)
            {
                var playerEntity = Mc.Entity != null ? Mc.Entity.GetEntityByRoleId(Mc.Config.RoleId) : null;
                if(playerEntity!=null)
                {
                    return playerEntity.Sex;
                }else
                {
                    return 0;
                }
            }
            else
            {
                return Mc.Config.lobbyRoleInfo.sex;
            }

        }
        
        private void InitFpPlayerModel()
        {
            fpPlayerModel = binder.BinderRoot.GetChild("fpPlayerModel") as ComUiPlayerModel;

            var objPlayerModel = Object.Instantiate(PlayerLoader.GetPlayerFpBone());
            var objPlayerModelPoint = objPlayerModel.AddComponent<ObjectPointComponent>();
            objPlayerModelPoint.InitData();

            var displayData = new PlayerDisplayData();
            //提前把脸部数据设置进去
            var lobbyRoleInfo = Mc.Config.lobbyRoleInfo;
            if (lobbyRoleInfo != null)
            {
                displayData.SetCustomizeData(lobbyRoleInfo.sex, lobbyRoleInfo.faceID, lobbyRoleInfo.hairID, lobbyRoleInfo.hairColorID);
            }
            //displayData.EquipmentDisplayDatas.Add(new EquipmentDisplayData(13010006));
            
            var fpModel = new DisplayModel(new PlayerData(displayData), objPlayerModelPoint, ModelType.FP, MgrConfigPhysicsLayer.LayerVirtualConstruction, true);
            //var config = Mc.Tables.TbSkinWeaponDisplayAnim.GetOrDefault(weaponData.TableId);
            //if (config != null)
            //{
            //    fpModel.DefaultIdleAnimPath = config.IdleAnim;
            //    fpModel.DefaultStartAnimPath = config.DrawAnim;
            //}

            fpModel.CreateAnimator(Mc.Tables.TbChracterParameter.UiAnimatorTp);
            fpModel.ModelAnimator.cullingMode = AnimatorCullingMode.AlwaysAnimate;
            fpPlayerModel.SetPlayData(displayData);
            fpPlayerModel.SetScenePreview(EPreviewType.PreviewFpPlayer,10);
            fpPlayerModel.InitModel(0, fpModel, MgrUiModel.ModelType.FpPlayer,true);
            fpPlayerModel.ChangeSex(GetSex());
            //fpPlayerModel.EnableRotate(fpPlayerModel,ERotateMode.All);
            //fpPlayerModel.camPlayerModel.fieldOfView = Mc.Tables.TbChracterParameter.DefaultSceneFov;
            //fpPlayerModel.camPlayerModel.nearClipPlane = BaseCameraState.NearClipPlane;
            //fpPlayerModel.camPlayerModel.farClipPlane = BaseCameraState.FarClipPlane;
            fpPlayerModel?.SetTouch(false);
            //fpPlayerModel.Show();

            //fpModel.AddHandWeapon(weaponData);

            //fpModel.Show(true);
            //fpModel.CheckBackWeapon();
            //fpModel.UpdateAllPart();
        }
        private void RefreshFpPlayerModel()
        {
            var fpModel = fpPlayerModel.displayModel;
            binder.CtrlModelType.selectedPage = "fplayer";
            WeaponData weaponData = null;
            List<int> itemIds = new List<int>();
            List<int> itemSkinIds = new List<int>();
            for (int i = 0; i < EquipDataList.Count; i++)
            {
                if (CheckCostumeSuit(EquipDataList[i].skinId))
                {
                    AddCostumeSuit(EquipDataList[i].skinId, itemIds, itemSkinIds);
                }
                else
                {
                    var skinCfg = Mc.Tables.TBSkin.GetOrDefault(EquipDataList[i].skinId);
                    if (skinCfg.SkinType == ENUMSkinType.Costume)
                    {
                        itemIds.Add((int)skinCfg.ID);
                        itemSkinIds.Add((int)skinCfg.OwnerID);
                    }
                    else
                    {
                        if (skinCfg.SkinType == ENUMSkinType.Weapon)
                        {
                            if (skinCfg.DefaultSkin==1)
                            {
                                weaponData = new WeaponData(new WeaponDisplayData(skinCfg.OwnerID, 0));
                            }
                            else
                            {
                                weaponData = new WeaponData(new WeaponDisplayData(skinCfg.OwnerID, skinCfg.ID));
                            }
                        }
                        itemIds.Add((int)skinCfg.OwnerID);
                        itemSkinIds.Add(skinCfg.ID);
                    }
                }

            }
            if(weaponData!=null)
            {
                var config = Mc.Tables.TbSkinWeaponDisplayAnim.GetOrDefault(weaponData.TableId);
                if (config != null)
                {
                    fpModel.IdleAnimPath = config.IdleAnim;
                    fpModel.StartAnimPath = config.DrawAnim;
                }
                fpModel.AddHandWeapon(weaponData);

                fpModel.Show(true);
                fpModel.CheckBackWeapon();
            }

            fpPlayerModel.PutOn(itemIds, itemSkinIds);
            fpPlayerModel.Show(true);
            //fpPlayerModel.SetStartAnim(2);
            //fpPlayerModel.SetSpecialIdleAnim(1);
            fpPlayerModel.UpdateAllPart();
            //手势需要特殊处理

        }
        /// <summary>
        /// 检查是否是一个套装id
        /// </summary>
        /// <returns></returns>
        private bool CheckCostumeSuit(int id)
        {
            var suitCfg= Mc.Tables.TBCostumeSuit.GetOrDefault(id);
            return suitCfg != null;
        }
        /// <summary>
        /// 添加一个时装套装
        /// </summary>
        private void AddCostumeSuit(int suitId,List<int> itemIds,List<int> skinIds)
        {
            var suitCfg = Mc.Tables.TBCostumeSuit.GetOrDefault(suitId);
            if (suitCfg!=null)
            {
                for (int i = 0; i < suitCfg.CostumeIDs.Count; i++)
                {
                    var skinCfg = Mc.Tables.TBSkin.GetOrDefault(suitCfg.CostumeIDs[i]);
                    if (itemIds!=null)
                    {
                        itemIds.Add((int)skinCfg.ID);
                    }
                    if (skinIds!=null)
                    {
                        skinIds.Add((int)skinCfg.OwnerID);
                    }
                }
            }

        }
        private void RemoveCostumeSuit()
        {

        }
        private void InitTpPlayerModel()
        {
            tpPlayerModel = ComUiPlayerModel.OnlyInitModel(null);
            tpPlayerModel.createAnimPath = Mc.Tables.TbChracterParameter.UiAnimatorTp;
            tpPlayerModel.isPlaySuitAnim = false;
            tpPlayerModel.SetScenePreview(EPreviewType.PreviewPlayer, 9);
            //if (weaponData != null)
            //{
            //    var config = Mc.Tables.TbSkinWeaponDisplayAnim.GetOrDefault(weaponData.TableId);
            //    if (config != null)
            //    {
            //        tpPlayerModel.DefaultIdleAnimPath = config.TpIdleAnim;
            //    }
            //}
            tpPlayerModel.InitModel("tpPlayerModel", 0, ModelType.Lb, null, MgrUiModel.ModelType.LobbyPlayer);
            tpPlayerModel.EnableRotate(binder.ModelLoader, ERotateMode.Horizontal);
            if (previewDataList.Count > 1)
            {
                var weaponOffset = Mc.Tables.TbGlobalConfig.LobbyWeaponOffset;
                tpPlayerModel.SetOffset(new Vector3(weaponOffset[0], weaponOffset[1], weaponOffset[2]));
            }
            tpPlayerModel.ChangeSex(GetSex());
            if (curBgId > 0) tpPlayerModel.SetBg(curBgId);
            //if (weaponData != null)/
            //{
            //    tpPlayerModel.AddHandWeapon(weaponData);
            //}



            
        }
        
        private void RefreshTpPlayerModel()
        {
            binder.CtrlModelType.selectedPage = "tplayer";
            List<int> itemIds = new List<int>();
            List<int> itemSkinIds = new List<int>();
            WeaponData weaponData=null;
            for (int i = 0; i < EquipDataList.Count; i++)
            {
                if (CheckCostumeSuit(EquipDataList[i].skinId))
                {
                    AddCostumeSuit(EquipDataList[i].skinId, itemIds, itemSkinIds);
                }else
                {
                    var skinCfg = Mc.Tables.TBSkin.GetOrDefault(EquipDataList[i].skinId);
                    if (skinCfg.SkinType == ENUMSkinType.Costume)
                    {
                        itemIds.Add((int)skinCfg.ID);
                        itemSkinIds.Add((int)skinCfg.OwnerID);
                    }
                    else
                    {
                        if (skinCfg.SkinType == ENUMSkinType.Weapon)
                        {
                            if (skinCfg.DefaultSkin==1)
                            {
                                weaponData = new WeaponData(new WeaponDisplayData(skinCfg.OwnerID, 0));
                            }
                            else
                            {
                                weaponData = new WeaponData(new WeaponDisplayData(skinCfg.OwnerID, skinCfg.ID));
                            }
                        }
                        itemIds.Add((int)skinCfg.OwnerID);
                        itemSkinIds.Add(skinCfg.ID);
                    }
                }
            }
            if (weaponData != null)
            {
                var config = Mc.Tables.TbSkinWeaponDisplayAnim.GetOrDefault(weaponData.TableId);
                if (config != null)
                {
                    // tpPlayerModel.displayModel.IdleAnimPath = config.TpIdleAnim;
                    // tpPlayerModel.DisplayModel.CreateAnimator(Mc.Tables.TbChracterParameter.UiAnimatorTp, false);
                    tpPlayerModel.AddHandWeapon(weaponData, config, AfterWeaponLoaded);
                }
            }
            else
            {
                // tpPlayerModel.displayModel.DefaultIdleAnimPath = Mc.Tables.TbGlobalConfig.CostumeDefaultIdleAnim;
                //tpPlayerModel.DisplayModel.CreateAnimator(Mc.Tables.TbChracterParameter.UiAnimatorTp, false);
                tpPlayerModel.RemoveHandWeapon(true);
            }
            tpPlayerModel.PutOn(itemIds, itemSkinIds);
            tpPlayerModel.Show();
            tpPlayerModel.SetStartAnim(2);
            tpPlayerModel.SetSpecialIdleAnim(1);
            tpPlayerModel.UpdateAllPart();
            Mc.TimerWheel.AddTimerOnce(100, (a, b, c) =>
            {
                if (curSelectData != null)
                {
                    var skinCfg = curSelectData.GetSkinCfg();
                    if (skinCfg != null && skinCfg.SkinType == ENUMSkinType.Gesture)
                    {

                        tpPlayerModel.PlayAnim(Mc.Tables.TbGestureConfig.GetOrDefault(skinCfg.ID).Anim);
                    }
                }
            });
        }

        private void AfterWeaponLoaded(DisplayWeapon weapon)
        {
            if (weapon == null || tpPlayerModel == null || tpPlayerModel.displayModel == null 
                || tpPlayerModel.displayModel.ModelAnimator == null)
            {
                return;
            }
            var config = Mc.Tables.TbSkinWeaponDisplayAnim.GetOrDefault(weapon.EntityTableId);
            if (config == null)
            {
                return;
            }

            var normalizedTime = tpPlayerModel.displayModel.ModelAnimator.GetCurrentAnimatorStateInfo(0)
                .normalizedTime;
            weapon.PlayAnim(config.TpWpnIdleAnim, normalizedTime);
        }
        
        private void InitItemModel()
        {
            itemModel.SetScenePreview(EPreviewType.PreviewItem, 8);
            itemModel.InitData("Preview");

            WeaponModel.SetScenePreview(EPreviewType.PreviewWeaponItem, 24);
            WeaponModel.InitData("WeaponPreview");
            //itemModel.EnableTouchRotate();
            //itemModel.EnableAutoStraighten();

        }
        private void RefreshItemModel()
        {


            binder.CtrlModelType.selectedPage = "item";
            SkinPreviewData data = null;
            if(curSelectData!=null)
            {
                data = curSelectData;
            }else
            {
                data = EquipDataList[0];
            }
            ComUiModel model = itemModel;
            if (data.GetSkinCfg()!=null)
            {
                var cfg = data.GetSkinCfg();
                if (cfg.SkinType==ENUMSkinType.Weapon)
                {
                    model = WeaponModel;
                }
            }
            long itemId;
            long skinId;
            var skinCfg= data.GetSkinCfg();
            if (skinCfg!=null)
            {
                itemId = skinCfg.OwnerID;
                skinId = skinCfg.ID;
            }else
            {
                itemId = data.skinId;
                skinId = 0;
            }

            model.SetItemData(itemId, skinId);
            if (skinCfg!=null&&skinCfg.SkinType==ENUMSkinType.Weapon)
            {
                model.EnableRotate(model, ERotateMode.All);
            }else
            {
                model.EnableRotate(model, ERotateMode.Horizontal);
            }
            if (uiModelCamera==null)
            {
                uiModelCamera = model.StateCamera.transform;
                uiModelCameraPosition = uiModelCamera.position;
            }

            model.Show(true);
            if (previewDataList.Count>1)
            {
                var weaponOffset = Mc.Tables.TbGlobalConfig.LobbyWeaponOffset;
                model.SetOffset(new Vector3(weaponOffset[0], weaponOffset[1], weaponOffset[2]));
            }

        }
        public ComUiModel GetCurrItemModel()
        {
            if (curSelectData!=null&& curSelectData.GetSkinCfg()!=null)
            {
                var skinCfg = curSelectData.GetSkinCfg();
                if (skinCfg.SkinType==ENUMSkinType.Weapon)
                {
                    return WeaponModel;
                }else
                {
                    return itemModel;
                }
            }
            return null;
        }
        public override void MakeFullScreen()
        {
            base.MakeFullScreen();
            WeaponModel?.SetMakeFullScreen();
            itemModel?.SetMakeFullScreen();
            tpPlayerModel?.SetMakeFullScreen();
            fpPlayerModel?.SetMakeFullScreen();

        }
        private bool CheckCanAdd(SkinPreviewData data)
        {
            var skinCfg = data.GetSkinCfg();
            bool isAdd = false;
            bool isSuit = CheckCostumeSuit(data.skinId);
            if (skinCfg != null)
            {
                if (skinCfg.SkinType == ENUMSkinType.Equipment || skinCfg.SkinType == ENUMSkinType.Armor || skinCfg.SkinType == ENUMSkinType.Costume)
                {
                    isAdd = true;
                    for (int i = 0; i < EquipDataList.Count; i++)
                    {
                        var equipSkinCfg = EquipDataList[i].GetSkinCfg();
                        bool tempSuit= CheckCostumeSuit(EquipDataList[i].skinId);
                        if (equipSkinCfg != null && (equipSkinCfg.SkinType == ENUMSkinType.Weapon || equipSkinCfg.SkinType == ENUMSkinType.Armor || equipSkinCfg.SkinType == ENUMSkinType.Costume || equipSkinCfg.SkinType == ENUMSkinType.Equipment))
                        {
                            continue;
                        }
                        else if (tempSuit)
                        {
                            continue;
                        }
                        isAdd = false;
                    }
                } else if (skinCfg.SkinType == ENUMSkinType.Weapon)//当前选中武器
                {
                    isAdd = true;
                    SkinPreviewData removeData = null;
                    for (int i = 0; i < EquipDataList.Count; i++)
                    {
                        bool tempSuit = CheckCostumeSuit(EquipDataList[i].skinId);
                        var equipSkinCfg = EquipDataList[i].GetSkinCfg();
                        if (equipSkinCfg != null && (equipSkinCfg.SkinType == ENUMSkinType.Armor || equipSkinCfg.SkinType == ENUMSkinType.Costume || equipSkinCfg.SkinType == ENUMSkinType.Equipment))
                        {
                            continue;
                        }else if(equipSkinCfg != null && equipSkinCfg.SkinType == ENUMSkinType.Weapon)//已经有武器，再装备武器，会把旧武器卸下
                        {
                            if (EquipDataList[i].skinId != data.skinId)
                            {
                                removeData = EquipDataList[i];
                            }
                            continue;   
                        }else if(tempSuit)
                        {
                            continue;
                        }
                        isAdd = false;
                    }
                    if (removeData != null)
                    {
                        GetPreviewItem(removeData.skinId).SetEquip(false);
                        EquipDataList.Remove(removeData);
                    }
                }
            }else if(isSuit)
            {
                isAdd = true;
                for (int i = 0; i < EquipDataList.Count; i++)
                {
                    bool tempSuit = CheckCostumeSuit(EquipDataList[i].skinId);
                    var equipSkinCfg = EquipDataList[i].GetSkinCfg();
                    if (equipSkinCfg != null && (equipSkinCfg.SkinType == ENUMSkinType.Weapon || equipSkinCfg.SkinType == ENUMSkinType.Armor || equipSkinCfg.SkinType == ENUMSkinType.Costume || equipSkinCfg.SkinType == ENUMSkinType.Equipment))
                    {
                        continue;
                    }else if(tempSuit)
                    {
                        continue;
                    }
                    isAdd = false;
                }
            }
            return isAdd;
        }
        private SkinPreviewItem GetPreviewItem(int skinId)
        {
            for (int i = 0; i < previewItemList.Count; i++)
            {
                if (previewItemList[i].PreviewData.skinId==skinId)
                {
                    return previewItemList[i];
                }
            }
            return null;
        }
        public void OnClickHighLight()
        {
            var cfg = curSelectData.GetSkinCfg();
            if (cfg!=null)
            {
                UiHighLightBox.ShowWnd(cfg.Highlights);
            }

        }
        public void OnClickSwitchPerspective()
        {
            //if (perspectiveType == EPerspectiveType.ModelP||perspectiveType==EPerspectiveType.ThirdP)
            //{
            //    SetPerspective(EPerspectiveType.FirstP);
            //    binder.PreviewModel.selected = false;
            //    Mc.MsgTips.ShowRealtimeWeakTip(24242);
            //    //binder.PerspectiveBtn.title = "第一人称";
            //}
            //else
            //{
            //    SetPerspective(EPerspectiveType.ThirdP);
            //    Mc.MsgTips.ShowRealtimeWeakTip(24243);
            //    //binder.PerspectiveBtn.title = "第三人称";
            //}

            //重复切换过滤
            if (perspectiveType==EPerspectiveType.ThirdP)
            {
                return;
            }
            binder.PreviewModel.selected = false;
            previewBind.BinderRoot.visible = false;
            SetPerspective(EPerspectiveType.ThirdP);
            Mc.MsgTips.ShowRealtimeWeakTip(24243);
        }
        private void SetPerspective(EPerspectiveType type)
        {
            perspectiveType = type;
            SwitchPerspective();
        }
        
        private void SwitchPerspective()
        {
            itemModel?.Hide();
            WeaponModel?.Hide();
            tpPlayerModel?.Hide();
            fpPlayerModel?.Hide();
            
            if (perspectiveType == EPerspectiveType.ModelP)
            {
                ResetPartPreview();
                if (itemModel == null)
                {
                    InitItemModel();
                }
                RefreshItemModel();
            }
            else if (perspectiveType == EPerspectiveType.FirstP)
            {
                if (fpPlayerModel == null)
                {
                    InitFpPlayerModel();
                }
                RefreshFpPlayerModel();
            }
            else if (perspectiveType == EPerspectiveType.ThirdP)
            {
                if (tpPlayerModel == null)
                {
                    InitTpPlayerModel();
                }
                RefreshTpPlayerModel();
            }
        }
        
        public void OnClickPreviewModel()
        {
            if (binder.PerspectiveBtn.selected)
            {

                SetPerspective(EPerspectiveType.ModelP);
                binder.PerspectiveBtn.selected = false;
                UpdatePartPreviewVisble(true);
            }

            //binder.PerspectiveBtn.title = "第一人称";
        }
        public override void OnEscClose(EventContext context)
        {
            RemoveSelf();
        }
        #region 枪械部件预览
        private void AddAccessories(long[] accessoryList)
        {
            if (curSelectData == null || accessoryList == null || accessoryList.Length == 0)
            //if (curSelectWeapon == null || curSelectWeapon.SkinConfig == null || curSelectWeapon.SkinConfig.AccessoryGroup.Length == 0)
            {
                RemoveAllAccessories();
                return;
            }

            // var accessoryGroupId = curSelectWeapon.SkinConfig.AccessoryGroup[accessoryGroupIndex];
            // var group = Mc.Tables.TbSkinWeaponAccessoryGroup.GetOrDefault(accessoryGroupId);
            // if (group == null)
            // {
            //     RemoveAllAccessories();
            //     return;
            // }

            tempAccessoryList.Clear();
            foreach (var key in accessorys.Keys)
            {
                if (accessoryList.ContainsWithoutLinq(key) == false)
                {
                    tempAccessoryList.Add(key);
                }
            }
            foreach (var removeKey in tempAccessoryList)
            {
                RemoveAccessory(removeKey);
            }
            tempAccessoryList.Clear();

            foreach (var accessorySkinId in accessoryList)
            {
                SkinConfig tbSkinConfig = Mc.Tables.TbSkinConfig.GetOrDefault(accessorySkinId);
                if (tbSkinConfig != null)
                {
                    tempAccessoryList.Add(tbSkinConfig.ItemId);
                }
            }
            ClientHeldItemUtil.CheckPartModel(curSelectData.GetSkinCfg().OwnerID, curSelectData.skinId, WeaponModel.CurModelGo.transform, tempAccessoryList);
            tempAccessoryList.Clear();
            foreach (var toAdd in accessoryList)
            {
                AddAccessory(toAdd, curSelectData.GetSkinCfg().OwnerID);
            }
        }
        private void AddAccessory(long accessorySkinId, long weaponTableId, ModelType modelType = ModelType.TP)
        {
            if (accessorys.ContainsKey(accessorySkinId))
            {
                return;
            }

            SkinConfig tbSkinConfig = Mc.Tables.TbSkinConfig.GetOrDefault(accessorySkinId);
            if (tbSkinConfig == null)
            {
                return;
            }

            var accessoryTableId = tbSkinConfig.ItemId;
            var Table = Mc.Tables.TbPartBase.GetOrDefault(accessoryTableId);
            if (HeldItemUtility.TryGetAccessoryAsset(accessoryTableId, accessorySkinId, weaponTableId, out var asset) == false)
            {
                logger.ErrorFormat("CreateAccessory: TryGetAccessoryAsset failed, accessoryTableId:{0}, skinId:{1}, accessoryParentTableId:{2}", accessoryTableId, accessorySkinId, weaponTableId);
                return;
            }

            string prefabPath = modelType == ModelType.FP ? asset.FpPrefabPath : asset.NewtpPrefabPath;
            if (!GoPool.IsExist(prefabPath))
            {
                GoPool.Init(prefabPath, new PoolConfig<GameObject>(10));
            }

            GameObject obj = GoPool.Get(prefabPath);
            if (obj == null)
                return;
            obj.SetLayer(MgrConfigPhysicsLayer.LayerVirtualConstruction);
            obj.transform.TryAttachAccessoryGoToTarget(WeaponModel.CurModelGo.transform, Table.Hangingpoint, new Vector3(asset.Scale, asset.Scale, asset.Scale));
            accessorys[accessorySkinId] = (obj, prefabPath);
        }
        private void RemoveAllAccessories()
        {
            CancelSelectAccessory();
            foreach (var pair in accessorys.Values)
            {
                pair.go.SetLayer(MgrConfigPhysicsLayer.LayerDefault);
                GoPool.Release(pair.path, pair.go);
                if (GoPool.TryGetPool(pair.path, out var pool))
                {
                    if (pool.IsClearable) GoPool.RemovePool(pair.path);
                }
            }
            accessorys.Clear();
        }

        private void RemoveAccessory(long accessorySkinId)
        {
            if (accessorys.TryGetValue(accessorySkinId, out var pair) == false)
            {
                return;
            }

            accessorys.Remove(accessorySkinId);
            pair.go.SetLayer(MgrConfigPhysicsLayer.LayerDefault);
            GoPool.Release(pair.path, pair.go);
        }
        private void SelectAccessory(long accessorySkinId)
        {
            if (accessorySkinId == selectedAccessorySkinId)
            {
                return;
            }
            CancelSelectAccessory();

            selectedAccessorySkinId = accessorySkinId;
            if (accessorys.TryGetValue(selectedAccessorySkinId, out var value) == false)
            {
                return;
            }

            DOMove(value.go.transform.position - uiModelCamera.forward * 5.5f);
            EnhancedDisplayEffect.AddOutlineComponent(value.go, true);
            string outlineColor = "#FF7132";
            float outlineBoldness = 0.45f;
            float outlineOpacity = 0.87f;
            float outlineBlurShift = 0.3f;
            EnhancedDisplayEffect.EnableOutline(value.go, outlineColor, outlineBoldness, outlineOpacity, EdgesType.Normal, outlineBlurShift);
        }
        private void DOMove(Vector3 pos)
        {
            WeaponModel.ResetModelRotate();
            WeaponModel.touchable = false;
            if (tweener != null && tweener.IsPlaying())
            {
                tweener.Kill();
            }
            tweener = uiModelCamera.DOMove(pos, 0.5f).SetEase(Ease.OutQuart);
        }
        private void CancelSelectAccessory()
        {
            DOMove(uiModelCameraPosition);
            WeaponModel.touchable = true;
            if (accessorys.TryGetValue(selectedAccessorySkinId, out var value) == false)
            {
                selectedAccessorySkinId = 0;
                return;
            }

            EnhancedDisplayEffect.DisableOutline(value.go);
            selectedAccessorySkinId = 0;
        }
        private void ResetPartPreview()
        {
            if (tweener != null && tweener.IsPlaying())
            {
                tweener.Kill();
            }
            if(uiModelCamera!=null)
            {
                uiModelCamera.position = uiModelCameraPosition;
            }
        }
        #endregion
        public void OnFpsUnlimitedUpdate(float dt)
        {
            if (fpPlayerModel != null && fpPlayerModel.visible)
            {
                fpPlayerModel.AlignCameraLocator();

                //测试代码
                //if (Input.GetKeyDown(KeyCode.Alpha1))
                //{
                //    var config = Mc.Tables.TbSkinWeaponDisplayAnim.GetOrDefault(curSelectData.GetSkinCfg().ID);
                //    if (config != null)
                //    {
                //        PlayFpAnim(config.DrawAnim, "");
                //    }
                //}

                //if (Input.GetKeyDown(KeyCode.Alpha2))
                //{
                //    var config = Mc.Tables.TbSkinWeaponDisplayAnim.GetOrDefault(curSelectData.GetSkinCfg().ID);
                //    if (config != null)
                //    {
                //        PlayFpAnim(config.InspectionAnim, config.WpnInspectionAnim);
                //    }
                //}

                //if (Input.GetKeyDown(KeyCode.Alpha3))
                //{
                //    var config = Mc.Tables.TbSkinWeaponDisplayAnim.GetOrDefault(curSelectData.GetSkinCfg().ID);
                //    if (config != null)
                //    {
                //        PlayFpAnim(config.ReloadAnim, config.WpnReloadAnim);
                //    }
                //}

            }
        }

        /// <summary>
        /// 设置3D模型背景
        /// </summary>
        public void SetBg(int bgId)
        {
            if (bgId <= 0) return;
            curBgId = bgId;
            var model = GetCurrItemModel();
            if (null != model) model.SetBg(bgId);
            if (null != tpPlayerModel) tpPlayerModel.SetBg(bgId);
        }
    }
    
    public class SkinPreviewItem
    {
        public SkinPreviewData PreviewData { private set; get; }
        UiCommonPreview previewParent;
        public ENUMSkinType PreviewType { private set; get; }
        ThirdTabItemBinder bind;
        public bool isSelect;
        public SkinPreviewItem(SkinPreviewData skinData)
        {
            PreviewData = skinData;
        }
        public void UpdateCom(UiCommonPreview preview,GComponent item,SkinPreviewData data)
        {
            PreviewData = data;
            previewParent = preview;
            bind = new ThirdTabItemBinder(item);
            bind.TxtSkinName.text =PreviewData.skinName;
            bind.LoaderSkinBanner.url = PreviewData.skinIcon;
            bind.CtrlStyle.selectedPage = "Short";
            SetEquip(false);
            bind.CtrlRarity.selectedIndex = PreviewData.rarity;
            bind.CtrlRedDotState.selectedPage = "Hide";
            bind.CtrlStarState.selectedPage = "no";
            bind.BinderRoot.onClick.Add(OnClick);
        }
        public void SetSelect(bool select)
        {
            isSelect = select;
            bind.CtrlButton.selectedPage = isSelect ? "down" : "up";
        }
        public void SetEquip(bool equip)
        {
            if (PreviewData.isLock)
            {
                bind.CtrlEquiped.selectedPage ="Locked" ;
            }else
            {
                bind.CtrlEquiped.selectedPage = equip ? "Equiped" : "UnEquiped";
            }
        }
        public void OnClick()
        {
            //最后一个不能取消选中by cs #66100
            if (isSelect&& previewParent.EquipDataList.Count==1)
            {
                return;
            }
            SetSelect(!isSelect);
            SetEquip(isSelect);
            previewParent.UpdateMultiWnd(this,isSelect);
        }
        public void EquipState(bool isEquip)
        {
            bind.CtrlEquiped.selectedPage = isEquip ? "Equiped" : "UnEquiped";
        }
    }
}