using Construction.Util;
using Cysharp.Text;
using FairyGUI;
using System;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Unity.Construction;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Manager;

namespace WizardGames.Soc.SocClient.Ui
{
    public class UiOthersideNearBy : UiOthersideBase
    {
        protected const string STYLE_HIDE = "hide";
        protected const string STYLE_SINGLE = "single";
        protected const string STYLE_MUILT = "muilt";
        private const float CLICK_CD = 1.5f;

        protected Controller ctrlTabCount;
        /// <summary>
        /// 只有一个tab时，显示的title UI
        /// </summary>
        protected GButton tabSingle;
        /// <summary>
        /// 多个tab时，显示的title UI
        /// </summary>
        protected GList listTitleTabs;
        
        protected GButton btnPickableTab;
        protected GButton btnFirsColTab;
        protected GObject downTrigger;
        protected GObject upTrigger;
        protected bool showPickableTab = false;

        /// <summary>
        /// 当前选中的标签的索引
        /// </summary>
        protected int curSelectTabIndex = int.MaxValue;

        public int CurSelectTabIndex => curSelectTabIndex;

        /// <summary>
        /// 异端容器-散落物/宝箱
        /// </summary>
        public UiOthersideNearbyStorage storage;

        /// <summary>
        /// 异端容器-玩家资产
        /// </summary>
        public UiOthersideNearbyInventory inventory;
        /// <summary>
        /// 异端容器-玩家资产
        /// </summary>
        public UiOthersideNearbyInventoryNoGroup inventoryNoGroup;

        /// <summary>
        /// 当前显示的客户端容器
        /// </summary>
        protected UiOthersideBase curMode;

        /// <summary>
        /// 物品组源
        /// </summary>
        protected Func<List<ItemGroup>> curItemGroupSource = null;

        #region 以 collection 区分 tab

        /// <summary>
        /// 集合列表源
        /// </summary>
        private Func<List<EntityBase>> curCollectionSource = null;

        /// <summary>
        /// 当前集合列表
        /// </summary>
        private List<EntityBase> curCollections = null;

        /// <summary>
        /// 当前集合名称获取函数
        /// </summary>
        private Func<EntityBase, string> curColNameProvider = null;

        #endregion


        /// <summary>
        /// 上次点击的时间
        /// </summary>
        private float fLastClickTime = 0;

        /// <summary>
        /// 快速丢弃的状态
        /// </summary>
        private bool quickDropState = false;

        /// <summary>
        /// 触发自动滚动的时间
        /// </summary>
        private float autoScrollTime = 300;

        /// <summary>
        /// 自动滚动步长
        /// </summary>
        private float autoScrollStep = 2f;

        /// <summary>
        /// 自动滚动因子
        /// </summary>
        private float autoScrollFactor = 0;

        /// <summary>
        /// 当前累计的触发自动滚动时间
        /// </summary>
        private float autoScrollTimer = 0;

        private bool distanceToClose;
        /// <summary>
        /// 检查交互距离点
        /// </summary>
        private Vector3 checkPos;

        /// <summary>
        /// 玩家点
        /// </summary>
        private Vector3 playerPos;

        /// <summary>
        /// 关闭界面时的最小距离平方（默认）
        /// </summary>
        private readonly float defaultCloseWinDistanceSqr = 36f;
        /// <summary>
        /// 容器位置控制器
        /// </summary>
        public Controller ctrlContainerPosState;
        protected UiInventoryOtherside uiInventory;
        /// <summary>
        /// 上一次掠夺的对象
        /// </summary>
        private long lastLootingId = -1;

        protected virtual bool needNewLootWhenChangeTab => true;
        private long autoHideTimerHandler = 0;

        protected override void OnInit()
        {
            ctrlTabCount = root.GetController("tabCount");
            ctrlTabCount.SetSelectedPage(STYLE_HIDE);
            tabSingle = root.GetChild("singleTab").asButton;
            
            listTitleTabs = root.GetChild("tabs").asCom.GetChild("LeftList").asList;
            listTitleTabs.SetVirtual();
            listTitleTabs.itemRenderer = TabRenderer;
            
            ctrlContainerPosState = root.GetController("Status");
            ctrlContainerPosState.SetSelectedPage("Hide");
            
            var ctrlStyle = root.GetController("lenStyle");
            ctrlStyle.SetSelectedPage(Mc.Ui.CheckIsNarrowWin() ? "narrow" : "wide");
            var comStorage = root.GetChild("storage").asCom;
            storage = new UiOthersideNearbyStorage(Mc.Ui.GlobalIconPool,this);
            storage.Bind(comStorage);
            
            InitScrollTriggers(root);
            
            var comInventory = root.GetChild("inventory").asCom;
            inventory = new UiOthersideNearbyInventory(Mc.Ui.GlobalIconPool, curColNameProvider);
            inventory.Bind(comInventory);
            
            var comInventoryNoGroup = root.GetChild("inventoryNoGroup").asCom;
            inventoryNoGroup = new UiOthersideNearbyInventoryNoGroup(Mc.Ui.GlobalIconPool, curColNameProvider);
            inventoryNoGroup.Bind(comInventoryNoGroup);
            
            uiInventory = Mc.Ui.GetWindowT<UiInventoryOtherside>("UiInventoryOtherside");

            quickDropState = false;
            uiInventory.panel.ToolBarCreateCall = () =>
            {
                UpdateContainerPos();
            };
        }

        public override void RefreshOtherSideName()
        {
            uiInventory.panel.SetOtherSideTitle(uiInventory.panel.GetContainerOthersideName(), 0, storage.GetOthersideCapacity());

        }


        private void InitScrollTriggers(GComponent comRoot)
        {
            autoScrollTime = Mc.Tables.TbGlobalConfig.GetBackpackAutoScrollTriggerTime();
            autoScrollStep = Mc.Tables.TbGlobalConfig.GetBackpackAutoScrollStep();
            upTrigger = comRoot.GetChild("upTrigger");
            downTrigger = comRoot.GetChild("downTrigger");
            upTrigger.onRollOver.Set(() =>
            {
                autoScrollFactor = -1 * autoScrollStep;
            });
            upTrigger.onRollOut.Set(ResetAutoScroll);
            downTrigger.onRollOver.Set(() => autoScrollFactor = autoScrollStep);
            downTrigger.onRollOut.Set(ResetAutoScroll);
            downTrigger.visible = false;
            upTrigger.visible = false;
        }

        /// <summary>
        /// 重置自动滚动状态
        /// </summary>
        private void ResetAutoScroll()
        {
            autoScrollFactor = 0;
            autoScrollTimer = 0;
        }

        /// <summary>
        /// 获取离线或死亡玩家的名称标题
        /// </summary>
        private string GetPlayerColTitle(EntityBase ent, out bool isOfflinePlayer)
        {
            string finalName = null;
            string playerName = null;
            isOfflinePlayer = false;
            // 下线玩家是通过交互列表打开的, 所以需要校验交互列表注释的实体
            if (ent is PlayerEntity playerNormalCol)
            {
                isOfflinePlayer = true;
                playerName = playerNormalCol.Name;
            }
           
            // 尸体或尸体盒子是通过拾取列表打开的, 使用传进来的方法获取名称即可
            if (string.IsNullOrEmpty(playerName))
            {
                playerName = curColNameProvider?.Invoke(ent) ?? null;
                if (string.IsNullOrEmpty(playerName))
                {
                    finalName = LanguageManager.GetTextConst(LanguageConst.OthersideTitleDefault);
                    Mc.LootCollection.PreSetLootName(finalName);
                    return finalName;
                }
            }
            string strFormat;

            if ((ent.GetComponent(EComponentIdEnum.Box) is BoxComponent comp && comp.CorpseId > 0) || ent is CorpseEntity)
            {
                // strFormat = LanguageManager.GetTextConst(LanguageConst.OthersideTitleDeadPlayer);
                finalName = playerName;
            }
            else
            {
                strFormat = LanguageManager.GetTextConst(LanguageConst.OthersideTitleOfflinePlayer);
                finalName = string.Format(strFormat, playerName);
            }
            Mc.LootCollection.PreSetLootName(finalName);
            return finalName;
        }

        /// <summary>
        /// 获取非玩家的名称标题
        /// </summary>
        public virtual string GetNormalColTitle(EntityBase col, int index)
        {
            string name = null;
            bool useDefault = false;
            if (col is BoxEntity)
            {
                name = curColNameProvider?.Invoke(col) ?? null;
                if (string.IsNullOrEmpty(name))
                {
                    useDefault = true;
                }
                else
                {
                    name = ZString.Format(LanguageManager.GetTextConst(LanguageConst.PickListDropItem), name);
                }
            }
            // 当前注视的实体
            else if (null != col && col.EntityId == InteractiveIdListChecker.CurEyeEntityId)
            {
                name = InteractiveIdListChecker.CurEyeEntityName;
            }
            else
            {
                useDefault = true;
            }

            if (useDefault)
            {
                name = LanguageManager.GetTextConst(LanguageConst.OthersideTitleDefault);
            }
            Mc.LootCollection.PreSetLootName(name);
            return name;
        }

        public virtual bool HaveDestroyTip(GTextField textField)
        {
            return false;
        }

        public virtual string GetBtnTips()
        {
            return "";
        }

        private void TabRenderer(int index, GObject item)
        {
            var btnTab = item.asButton;
            if (null == btnTab) return;
            
            var redPoint = btnTab.GetChild("reddot").asCom;
            redPoint.visible = false;
            var icon = btnTab.GetChild("icon").asLoader;
            
            btnTab.selected = curSelectTabIndex == index;
            btnTab.onClick.Set(() => OnClickNearByTab(index));
            // var ctrlStyle = btnTab.GetController("style");
            if (showPickableTab && index == GetTabCount() - 1)
            {
                //附近物品
                // btnTab.title = LanguageManager.GetTextConst(LanguageConst.OthersideTitleNearBy);
                // ctrlStyle.SetSelectedPage("text");
                // 附近
                btnTab.title = LanguageManager.GetTextConst(LanguageConst.OthersideTabTitleNearBy);
                btnPickableTab = btnTab;
                icon.url = Mc.Tables.TbContainerConstantConfig.NearByItemTabIcon;
            }
            else
            {
                var curCol = GetCurEntity(index);
                if (IsPlayerInventoryCol(curCol))
                {
                    btnTab.title = GetPlayerColTitle(curCol, out bool isOffline);
                    icon.url = Mc.Tables.TbContainerConstantConfig.DeathItemTabIcon;
                    // ctrlStyle.SetSelectedPage(isOffline ? "playerOffline" : "playerDead");
                }
                else
                {
                    btnTab.title = GetNormalColTitle(curCol, index);
                    icon.url = Mc.Tables.TbContainerConstantConfig.AIDeathItemTabIcon;
                    // ctrlStyle.SetSelectedPage("text");
                }
                if (null == btnFirsColTab) btnFirsColTab = btnTab;
            }
        }

        protected virtual EntityBase GetCurEntity(int curIndex)
        {
            return curCollections[curIndex];
        }

        /// <summary>
        /// 点击tab
        /// </summary>
        private void OnClickNearByTab(int index,bool isForce = false)
        {
            if (isForce == false)
            {
                if (curSelectTabIndex == index) 
                    return;
            }
            else
            {
            }
            float curTime = Time.time;
            if (curTime - fLastClickTime <= CLICK_CD)
            {
                return;
            }
            fLastClickTime = curTime;
            curSelectTabIndex = index;
            listTitleTabs.RefreshVirtualList();
            Mc.QuickLoot.SetQuickLootState(false);
            var playerCol = Mc.MyPlayer.MyEntityServer;
            // 先回收所有的图标到公共图标池中
            storage.ClearAllIcons();
            inventory.ClearAlIcons();
            inventoryNoGroup.ClearAlIcons();
            // 散落物
            if (showPickableTab && index == GetTabCount() - 1)
            {
                // 停止当前掠夺
                Mc.LootCollection.LootingComponent.RemoteCallStopLooting(ERpcTarget.World);
                ShowItemListPage();
                return;
            }
            
            inventory.Root.visible = false;
            inventoryNoGroup.Root.visible = false;
            storage.Root.visible = false;
            var curCol = GetCurEntity(index);
            if (null == curCol) return;
            var curLooting = Mc.LootCollection.CurLootingEntity;
            if (null != curLooting)
            {
                // 如果当前掠夺的就是这个容器, 那么直接显示
                if (curLooting.EntityId == curCol.EntityId)
                {
                    curMode = null;
                    ShowContainerPage(index);
                    return;
                }
                // 先停止当前掠夺
                if (needNewLootWhenChangeTab)
                {
                    Mc.LootCollection.LootingComponent.RemoteCallStopLooting(ERpcTarget.World);
                }
            }
            if (lastLootingId != curCol.EntityId)
            {
                curMode = null;
                // 开始新的掠夺
                CancelTimer();
                listTitleTabs.touchable = false;
                autoHideTimerHandler = Mc.TimerWheel.AddTimerOnce((int)(CLICK_CD*1000), (_, _, _)=>
                {
                    listTitleTabs.touchable = true;
                    autoHideTimerHandler = 0;
                });
                Mc.LootCollection.OnStartLootingAck = res =>
                {
                    listTitleTabs.touchable = true;
                    CancelTimer();
                    if (!res) return;
                    ShowContainerPage(index);
                };
                uiInventory.panel.SetButtonTouchable(true);
                if (needNewLootWhenChangeTab)
                {
                    lastLootingId = curCol.EntityId;
                    Mc.MyPlayer.MyEntityServer.RemoteCallStartLooting(ERpcTarget.Simulator, Mc.Construction.ViewRayPos, Mc.UserCmd.NowCmd.Yaw, Mc.UserCmd.NowCmd.Pitch, curCol.EntityId);
                }
            }
        }
        private void CancelTimer(){
            if (autoHideTimerHandler > 0)
            {
                Mc.TimerWheel.CancelTimer(autoHideTimerHandler);
                autoHideTimerHandler = 0;
            }
        }

        /// <summary>
        /// 刷新tab栏
        /// </summary>
        protected virtual void RefreshTabs()
        {
            //if (!inited) return;
            btnPickableTab = null;
            btnFirsColTab = null;
            int tabCount = 0;
            // 只有在有掉落物组数据的情况下在显示掉落物页签
            showPickableTab = false;
            if (null != curItemGroupSource)
            {
                var listGroup = curItemGroupSource.Invoke();
                showPickableTab = null != listGroup && listGroup.Count > 0 && (listGroup[0].Items?.Count ?? 0) > 0;
            }
            int colCount = GetColCount();
            int sumCount = (showPickableTab ? 1 : 0) + colCount;
            UpdateTabCoutCtrl(sumCount);
            
            if (sumCount > 1)
            {
                // 更新当前选中的index
                UpdateCurSelectTabIndex();
                tabCount = GetTabCount();
                listTitleTabs.numItems = tabCount;
                if (curSelectTabIndex == int.MaxValue)
                {
                    if (null != btnPickableTab) 
                        btnPickableTab.FireClick(false, true);
                    else if (null != btnFirsColTab)
                        btnFirsColTab.FireClick(false, true);
                }
            }
            // 没有集合
            else
            {
                tabCount = 1;
                tabSingle.selected = true;
                listTitleTabs.numItems = 0;
                if (null != Mc.LootCollection.CurLootingEntity)
                {
                    ShowContainerPage(0);    // 先掠夺再打开的情况
                }
                else if (colCount > 0)
                {
                    OnClickNearByTab(0,true);     // 由于散落物捡完而二变一的情况, 此时容器还没有掠夺, 需要手动调用点击tab逻辑开始掠夺
                }
                else if(showPickableTab)
                {
                    ShowItemListPage();    // 只有散落物的情况
                }
            }

            UpdateContainerPos();
            // 如果有超过一个以上的tab， 则StopLooting时不自动关闭背包
            Mc.Ui.DontHideUiInventoryWhenStopLooting = tabCount > 1;
        }

        private void UpdateTabCoutCtrl(int tabCount)
        {
            if (tabCount > 1)
            {
                ctrlTabCount.SetSelectedPage(STYLE_MUILT);
                uiInventory?.panel?.SetAllPickPos(1);
            }
            else
            {
                var curLootCol = Mc.LootCollection.CurLootingEntity;
                if (IsPlayerInventoryCol(curLootCol))
                {
                    //线下掠夺
                    if (curLootCol is PlayerEntity)
                    {
                        ctrlTabCount.SetSelectedPage(STYLE_HIDE);
                        uiInventory?.panel?.SetAllPickPos(0);
                    }
                    //死亡盒子,附近物品，需要切换
                    else
                    {
                        ctrlTabCount.SetSelectedPage(STYLE_SINGLE);
                        uiInventory?.panel?.SetAllPickPos(1);
                    }
                }
                else
                {
                    //储物箱
                    if (uiInventory.panel.containerOtherside.Firstmode == UiOthersideType.NearBy)
                    {
                        ctrlTabCount.SetSelectedPage(STYLE_HIDE);
                        uiInventory?.panel?.SetAllPickPos(0);
                    }
                    //各种宝箱
                    else
                    {
                        var entity = curLootCol as BoxEntity;
                        if (entity != null)
                        {
                            var config = Mc.Tables.TbTreasureBox.GetOrDefault(entity.TemplateId);
                            //Ai敌人掉落的战利品
                            if (config.BoxType == 17)
                            {
                                ctrlTabCount.SetSelectedPage(STYLE_SINGLE);
                                uiInventory?.panel?.SetAllPickPos(1);
                            }
                            else
                            {
                                //附近物品
                                if (showPickableTab)
                                {
                                    ctrlTabCount.SetSelectedPage(STYLE_SINGLE);
                                    uiInventory?.panel?.SetAllPickPos(1);
                                }
                                //其他宝箱
                                else
                                {
                                    ctrlTabCount.SetSelectedPage(STYLE_HIDE);
                                    uiInventory?.panel?.SetAllPickPos(0);
                                }
                            }
                        }
                        else
                        {
                            //附近物品
                            if (showPickableTab)
                            {
                                ctrlTabCount.SetSelectedPage(STYLE_SINGLE);
                                uiInventory?.panel?.SetAllPickPos(1);
                            }
                            //其他宝箱
                            else
                            {
                                ctrlTabCount.SetSelectedPage(STYLE_HIDE);
                                uiInventory?.panel?.SetAllPickPos(0);
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 当前显示的Entity
        /// </summary>
        private EntityBase curShowCol = null;

        /// <summary>
        /// 同时设置当前的Entity
        /// </summary>
        /// <returns></returns>
        protected virtual int GetColCount()
        {
            // 传入集合的情况下, 显示分页的tabs
            curShowCol = null;
            if (null != curCollections && curSelectTabIndex >= 0 && curSelectTabIndex < curCollections.Count)
            {
                curShowCol = curCollections[curSelectTabIndex];
            }
            curCollections = curCollectionSource?.Invoke();
            int colCount = curCollections?.Count ?? 0;

            return colCount;
        }

        protected virtual void UpdateCurSelectTabIndex()
        {
            // 更新当前选中的index
            if (null != curShowCol)
            {
                curSelectTabIndex = curCollections.IndexOf(curShowCol);
                if (curSelectTabIndex < 0) curSelectTabIndex = int.MaxValue;
            }
            else
            {
                curSelectTabIndex = int.MaxValue;
            }
        }

        protected virtual int GetTabCount()
        {
            return (showPickableTab ? 1 : 0) + curCollections?.Count ?? 0;
        }

        /// <summary>
        /// 设置需要显示的附近内容(散落物/宝箱/死亡盒子/玩家)
        /// </summary>
        /// <param name="itemGroupSource"></param>
        /// <param name="colSource"></param>
        /// <param name="colNameProvider"></param>
        public void SetNearBy(Func<List<ItemGroup>> itemGroupSource, Func<List<EntityBase>> colSource = null, Func<EntityBase, string> colNameProvider = null)
        {
            curItemGroupSource = itemGroupSource;
            curCollectionSource = colSource;
            curColNameProvider = colNameProvider;
        }

        /// <summary>
        /// 是否是需要显示玩家资产样式的集合
        /// </summary>
        private bool IsPlayerInventoryCol(EntityBase ent)
        {
            bool b1  = ent is PlayerEntity;
            bool b2 = ent is CorpseEntity;
            bool b3 = ent is BoxEntity be && be.ComponentBox?.CorpseId > 0;
            
            return b1 || b2 || b3;
        }

        /// <summary>
        /// 显示散落物
        /// </summary>
        private void ShowItemListPage()
        {
            inventory.Root.visible = false;
            inventoryNoGroup.Root.visible = false;
            storage.Root.visible = true;
            storage.itemGroupSource = curItemGroupSource;
            curMode = storage;
            curMode.Show(-1);
            
            //附近
            string title = LanguageManager.GetTextConst(LanguageConst.OthersideTabTitleNearBy);
            string iconUrl = Mc.Tables.TbContainerConstantConfig.NearByItemTabIcon;
            UpdateTabSingle(title, iconUrl);
            RefreshDropState();
            Mc.Msg.FireMsg<long>(EventDefine.OnStartLootingAck, 0);
        }

        /// <summary>
        /// 显示当前容器
        /// </summary>
        protected void ShowContainerPage(int index = -1)
        {
            var curLootCol = Mc.LootCollection.CurLootingEntity;
            SetCurContainer(index);
            inventory.Root.visible = curMode == inventory;
            inventoryNoGroup.Root.visible = curMode == inventoryNoGroup;
            storage.Root.visible = curMode == storage;

            string titleTab = "";
            string iconUrlTab = "";
            string titlePanel = "";
            if (IsPlayerInventoryCol(curLootCol))
            {
                titleTab = GetPlayerColTitle(curLootCol, out bool isOffline1);
                iconUrlTab = Mc.Tables.TbContainerConstantConfig.NearByItemTabIcon;

                //线下掠夺
                if (curLootCol is PlayerEntity)
                {
                    curMode = inventory;
                    //掠夺
                    titlePanel = LanguageManager.GetTextConst(LanguageConst.Looting);
                }
                //ai敌人死亡未变成盒子
                else if (curLootCol is CorpseEntity)
                {
                    //拾取
                    titlePanel = LanguageManager.GetTextConst(LanguageConst.Gather);
                    curMode = storage;
                }
                //死亡盒子
                else
                {
                    curMode = inventoryNoGroup;
                    //拾取
                    titlePanel = LanguageManager.GetTextConst(LanguageConst.Gather);
                }
            }
            else
            {
                titleTab = GetNormalColTitle(curLootCol, index);
                iconUrlTab = Mc.Tables.TbContainerConstantConfig.AIDeathItemTabIcon;
                //储物箱
                if(uiInventory.panel.containerOtherside.Firstmode == UiOthersideType.NearBy)
                {
                    //储物
                    titlePanel = LanguageManager.GetTextConst(LanguageConst.StorageOtherVehicles);
                }
                //各种宝箱
                else
                {
                    //拾取
                    titlePanel = LanguageManager.GetTextConst(LanguageConst.Gather);
                }
                curMode = storage;
            }
            uiInventory?.SetNewToolCupBoardType(true, titlePanel);

            UpdateTabSingle(titleTab, iconUrlTab);
            UpdateContainerPos();
            storage.itemGroupSource = null;
            curMode.Show(curLootCol.EntityId);
            RefreshDropState();
            SetBtnTitle();
        }

        private void UpdateTabSingle(string title,string iconUrl)
        {
            tabSingle.title = title;
            var icon = tabSingle.GetChild("icon").asLoader;
            icon.url = iconUrl;
            var redPoint = tabSingle.GetChild("reddot").asCom;
            redPoint.visible = false;
        }

        protected virtual void UpdateContainerPos()
        {
            if (uiInventory.panel.GetToolBarType() == 1)
            {
                ctrlContainerPosState.SetSelectedPage(uiInventory.panel.TabDatas.Count > 0 ? "Hide" : "Show");
            }
            else
            {
                if (ctrlTabCount.selectedIndex == 0)
                {
                    ctrlContainerPosState.SetSelectedPage(uiInventory.panel.TabDatas.Count > 0 ? "HideWhenTitleInvisible" : "ShowWhenTitleInvisible");
                }
                else
                {
                    ctrlContainerPosState.SetSelectedPage("HideWhenTitleInvisible");
                }
            }
            if (Mc.LootCollection.CurLootingEntity is PartEntity partEntity)
            {
                if ((PartType)partEntity.TemplateId == PartType.VendingMachine)
                {
                    ctrlContainerPosState.SetSelectedPage("Hide");
                }
            }
        }

        public virtual void SetBtnTitle()
        {

        }

        public void SetBtnNormalTitle(string title)
        {
            tabSingle.title = title;
        }

        protected virtual void SetCurContainer(int index)
        {

        }

        public override ComItemIcon SetOtherHightLight(int tarIndex, bool on)
        {
            var icon = ScrollAndGetIconOfIndex(tarIndex);
            if (null == icon) return null;
            icon.SetHighlight(on);
            return icon;
        }

        protected virtual ComItemIcon ScrollAndGetIconOfIndex(int index)
        {
            return storage.ScrollAndGetIconOfIndex(index);
        }

        public override void Show(long collectionId = -1)
        {
            base.Show(collectionId);
            InitCollectionData(uiInventory?.panel.curCollectionIds);
            RefreshTabs();
        }

        protected override void RegisterEvents()
        {
            Mc.Msg.AddListener<long, List<long>>(EventDefine.OnSwitchLootingAck, OnSwitchLootingAck);
            Mc.Msg.AddListener(EventDefine.OnNearbyCollectionChanged, OnNearbyCollectionChanged);
            Mc.Msg.AddListener(EventDefine.UiPlayerLootUpdate, UiPlayerLootUpdate);
            Mc.Msg.AddListener(EventDefine.UiPickableDataChange, UiPickableDataChange);
            Mc.Msg.AddListener<BaseItemNode>(EventDefine.UiItemDragStart, OnItemDragStart);
            Mc.Msg.AddListener<BaseItemNode, bool>(EventDefine.UiItemDragEnd, OnItemDragEnd);
            Mc.Msg.AddListener<long, long, long>(EventDefine.PartContainerLinkUpdate, OnPartContainerLinkUpdate);
            Mc.Entity.OnEntityCreate += OnClientGoCreate;
            Mc.Entity.OnEntityRemove += OnClientGoRemove;
            Mc.Msg.AddListener<long>(EventDefine.OvenNameUpdate, OnOvenNameUpdate);
        }

        protected override void UnRegisterEvents()
        {
            Mc.Msg.RemoveListener<long, List<long>>(EventDefine.OnSwitchLootingAck, OnSwitchLootingAck);
            Mc.Msg.RemoveListener(EventDefine.OnNearbyCollectionChanged, OnNearbyCollectionChanged);
            Mc.Msg.RemoveListener(EventDefine.UiPlayerLootUpdate, UiPlayerLootUpdate);
            Mc.Msg.RemoveListener(EventDefine.UiPickableDataChange, UiPickableDataChange);
            Mc.Msg.RemoveListener<BaseItemNode>(EventDefine.UiItemDragStart, OnItemDragStart);
            Mc.Msg.RemoveListener<BaseItemNode, bool>(EventDefine.UiItemDragEnd, OnItemDragEnd);
            Mc.Msg.RemoveListener<long, long, long>(EventDefine.PartContainerLinkUpdate, OnPartContainerLinkUpdate);
            Mc.Entity.OnEntityCreate -= OnClientGoCreate;
            Mc.Entity.OnEntityRemove -= OnClientGoRemove;
            Mc.Msg.RemoveListener<long>(EventDefine.OvenNameUpdate, OnOvenNameUpdate);
        }

        private void OnOvenNameUpdate(long entityId)
        {
            RefreshOtherSideName();
        }

        private void OnNearbyCollectionChanged()
        {
            RefreshTabs();
        }
        
        private void UiPlayerLootUpdate()
        {
            RefreshTabs();
        }
        
        private void UiPickableDataChange()
        {
            RefreshTabs();
        }

        protected override void OnEnable()
        {
            Mc.Msg.FireMsg(EventDefine.OnUiOthersideNearByOpen, true);
            uiInventory?.SetNewToolCupBoardType(true, LanguageManager.GetTextConst(LanguageConst.Gather));
            if (uiInventory != null)
            {
                uiInventory.ShowOtherSideAnim(true);
            }
        }

        public override void OnDisable()
        {
            base.OnDisable();
            lastLootingId = -1;
            CancelTimer();
            distanceToClose = false;
            if (uiInventory != null)
            {
                uiInventory.ShowOtherSideAnim(false);
            }
            storage?.OnDisable();
            inventory?.OnDisable();
            inventoryNoGroup?.OnDisable();
            curItemGroupSource = null;
            curCollections = null;
            curMode = null;
            btnPickableTab = null;
            btnFirsColTab = null;
            curSelectTabIndex = int.MaxValue;
            Mc.LootCollection.OnStopLootingAck = null;
            Mc.LootCollection.PreSetLootName(null);

            Mc.Ui.DontHideUiInventoryWhenStopLooting = false;
            Mc.Msg.FireMsg(EventDefine.OnUiOthersideNearByOpen, false);
        }

        /// <summary>
        /// 图标拖拽开始的处理
        /// </summary>
        private void OnItemDragStart(BaseItemNode dragItem)
        {
            downTrigger.visible = true;
            if (!curMode.IsScrollPaneAtTop()) // 如果不在顶部
            {
                upTrigger.visible = true;
            }
        }

        /// <summary>
        /// 图标拖拽结束的处理
        /// </summary>
        private void OnItemDragEnd(BaseItemNode dragItem, bool isDragValid)
        {
            downTrigger.visible = false;
            upTrigger.visible = false;
        }

        public override void OnUpdateFps10(float dt)
        {
            if (null != curMode) curMode.OnUpdateFps10(dt);
            CheckAutoScroll(dt);
            CheckDisAndPermission();
            CheckReloadWin();
        }

        /// <summary>
        /// 检查自动滚动状态
        /// </summary>
        private void CheckAutoScroll(float dt)
        {
            if (0 == autoScrollFactor || !UiItemIconDragDrop.IsDragging) return;
            if (autoScrollTimer < autoScrollTime)
            {
                autoScrollTimer += dt;
                return;
            }
            curMode.ScrollContentPane(dt * autoScrollFactor);
        }

        public override void Refresh()
        {
            if (null != curMode)
            {
                curMode.Refresh();
                CheckEmptyToClose();
            }
        }

        private void CheckEmptyToClose()
        {
            var tabCount = GetTabCount();
            bool isEmpty = curMode.IsEmpty();
            bool isOpenFromPickList = UiInventoryOtherside.OpenFromPickList;
            
            if (tabCount <= 1 && isOpenFromPickList && isEmpty)
            {
                UiInventoryOtherside.CloseInventory();
            }
        }

        public override void UnChooseAllIcons()
        {
            if (null != curMode) curMode.UnChooseAllIcons();
        }

        public override void OnQuickLootStateChanged(bool state)
        {
            if (null != curMode) curMode.OnQuickLootStateChanged(state);
        }

        /// <summary>
        /// 刷新快速丢弃相关状态
        /// </summary>
        protected void RefreshDropState()
        {
            if (null != curMode) curMode.OnQuickDropStateChanged(quickDropState);
        }

        public override void OnQuickDropStateChanged(bool state)
        {
            quickDropState = state;
            RefreshDropState();
        }

        public override void DoAllQuickLoot()
        {
            if (null != curMode) curMode.DoAllQuickLoot();
        }

        // 快速掠夺结束
        public override void OnQuickLootFinished()
        {
            bool b1 = !Mc.Ui.DontHideUiInventoryWhenStopLooting;
            bool b2 = !Mc.QuickLoot.HasFromOtherToMineQuickLootingData();
            bool b3 = null != curMode && curMode.IsEmpty();
            bool b4 = curMode != storage;
            
            // 掠夺完关闭背包界面
            if ( b1 && b2 && b3 && b4)
            {
                UiInventoryOtherside.CloseInventory();
            }
        }

        public override bool ContainItem(long id)
        {
            if (storage.curContainer == null) return false;
            foreach (var (_, item) in storage.curContainer)
            {
                if (item is BaseItemNode node)
                {
                    if (id == item.BizId)
                        return true;
                }
            }
            return false;
        }

        public override ItemContainerNode GetItemContainerNode()
        {
            return (ItemContainerNode)storage.curContainer;
        }
        public override void ScrollViewToTop()
        {
            storage.ScrollViewToTop();
        }

        public void InitCollectionData(List<long> collectionIds)
        {
            if (uiInventory == null)
            {
                return;
            }

            foundationIds.Clear();
            entityIdToNames.Clear();
            for (int i = 0; i < uiInventory.panel.TabDatas.Count; i++)
            {
                var collectionItem = uiInventory.panel.TabDatas[i];
                entityIdToNames.Add(collectionItem.EntityId, collectionItem.Name);

                if (!foundationIds.Contains(collectionItem.Entity.ContainersFoundation))
                {
                    foundationIds.Add(collectionItem.Entity.ContainersFoundation);
                }
            }

            UpdateContainerPos();
            curCollectionIds.Clear();
            curCollectionIds.AddRange(collectionIds);
        }

        public void SetCheckPosClose(long collectionId)
        {
            if (uiInventory == null)
            {
                return;
            }

            for (int i = 0; i < uiInventory.panel.TabDatas.Count; i++)
            {
                if (uiInventory.panel.TabDatas[i].EntityId == collectionId)
                {
                    distanceToClose = true;
                    checkPos = uiInventory.panel.TabDatas[i].Entity.Position.CV3ToUV3();
                    break;
                }
            }
        }

        /// <summary>
        /// 太远了或者没权限了就把界面关了
        /// </summary>
        private void CheckDisAndPermission()
        {
            if (!distanceToClose)
            {
                return;
            }
            if (uiInventory == null)
            {
                return;
            }

            if (Mc.MyPlayer == null)
            {
                return;
            }

            playerPos.Set(Mc.MyPlayer.MyEntityLocal.PosX, Mc.MyPlayer.MyEntityLocal.PosY, Mc.MyPlayer.MyEntityLocal.PosZ);
            float dis = Vector3.SqrMagnitude(playerPos - checkPos);
            if (dis > defaultCloseWinDistanceSqr)
            {
                Mc.MsgTips.ShowRealtimeWeakTip(11173);
                uiInventory.RemoveSelf();
                return;
            }

            var partEntity = Mc.Entity.GetPartEntity(this.EntityId);
            if (partEntity == null)
            {
                return;
            }

            //0.检查密码权
            if (!ClientConstructionUtils.CheckInteractivePermission(partEntity, EPrivilegeType.Password, true))
            {
                Mc.MsgTips.ShowRealtimeWeakTip(23106);
                uiInventory.RemoveSelf();
                return;
            }
        }

        private void CheckReloadWin()
        {
            if (needReloadWin)
            {
                needReloadWin = false;

                uiInventory.panel.ReloadSwitchLooting();
            }
        }


        private void OnSwitchLootingAck(long collectionId, List<long> collectionIds)
        {
            RefreshInfoByTargetEntity(collectionId);
            InitCollectionData(collectionIds);
            RefreshTabs();
            RefreshOtherSideName();
            storage.ScrollViewToTop();
        }

        /// <summary>
        /// 地基变化
        /// </summary>
        /// <param name="entityId"></param>
        /// <param name="oldValue"></param>
        /// <param name="newValue"></param>
        private void OnPartContainerLinkUpdate(long entityId, long oldValue, long newValue)
        {
            if (foundationIds.Contains(oldValue) && foundationIds.Contains(newValue))
            {
                return;
            }
            
            if (foundationIds.Contains(oldValue) || foundationIds.Contains(newValue))
            {
                needReloadWin = true;

                if (curCollectionIds.Contains(entityId))
                {
                    if (entityIdToNames.TryGetValue(entityId, out var entityName))
                    {
                        Mc.MsgTips.ShowRealtimeWeakTip(11175, entityName);
                    }
                }
            }
        }

        /// <summary>
        /// 地基的id
        /// </summary>
        private List<long> foundationIds = new List<long>();

        /// <summary>
        /// 当前打开的储物箱
        /// </summary>
        private List<long> curCollectionIds = new List<long>();

        /// <summary>
        /// id 对应名字
        /// </summary>
        private Dictionary<long, string> entityIdToNames = new Dictionary<long, string>();


        private bool needReloadWin = false;
        private void OnClientGoCreate(IEntity entity)
        {
            if (entity is not PartEntity partEntity)
            {
                return;
            }

            long foundId = partEntity.ContainersFoundation;
            if (foundId != 0 && foundationIds.Contains(foundId))
            {
                needReloadWin = true;
            }
        }

        private void OnClientGoRemove(IEntity entity)
        {
            if (entity is not PartEntity partEntity)
            {
                return;
            }

            if (curCollectionIds.Contains(partEntity.EntityId))
            {
                needReloadWin = true;

                if (entityIdToNames.TryGetValue(partEntity.EntityId, out var entityName))
                {
                    Mc.MsgTips.ShowRealtimeWeakTip(11174, entityName);
                    entityIdToNames.Remove(partEntity.EntityId);
                }
            }
        }
    }
}