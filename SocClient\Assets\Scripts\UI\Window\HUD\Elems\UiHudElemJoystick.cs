using System;
using UnityEngine;
using FairyGUI;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.Character.State.Event;
using WizardGames.Soc.Common.Unity.DebugLog;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.SocClient.Control;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Unity.Go;
using System.Collections.Generic;
using WizardGames.Soc.SocClient.Ui.Joystick;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Common.Unity.Horse;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Weapon;

namespace WizardGames.Soc.SocClient.Ui
{
    /// <summary>
    /// 移动轮盘模式
    /// </summary>
    public enum JoystickType { Fixed, Floating, Dynamic }
    /// <summary>
    /// 移动状态
    /// </summary>
    public enum MoveState
    {
        Idle,
        Run,
        Sprint,
    }
    /// <summary>
    /// 左摇杆
    /// </summary>
    public partial class UiHudElemJoystick : UiHudElem, IUiFps30Update
    {
        /// <summary>
        /// 开始触屏的位置指向当前的手指位置的向量
        /// </summary>
        private Vector2 input = Vector2.zero;
        private bool sprintLock = false;

        /// <summary>
        /// X轴角度豁免
        /// </summary>
        private bool snapX = true;

        /// <summary>
        /// y轴角度豁免
        /// </summary>
        private bool snapY = true;

        /// <summary>
        /// 角度豁免参数
        /// </summary>
        private float exemptAngleUp = 20f;

        /// <summary>
        /// 摇杆生效范围 摇杆移动小于该值不起作用
        /// </summary>
        private float deadZone = 10;
#pragma warning disable 0414
        /// <summary>
        /// 摇杆灵敏度
        /// </summary>
        private float sensitivity = 1;

        /// <summary>
        /// 冲刺热区触发角度
        /// </summary>
        private float sprintHotspotAng = 60f;

        /// <summary>
        /// 冲刺热区锁定角度
        /// </summary>
        //private float sprintLockAng = 20f;

        /// <summary>
        /// 冲刺热区显示高度
        /// </summary>
        private float sprintHotspotHeight = 360f;

        /// <summary>
        /// 移动轮盘固定点
        /// </summary>
        private Vector2 touchPadFixedPos;

        /// <summary>
        /// 移动轮盘固定模式。触摸热区大小
        /// </summary>
        private Vector2 touchPadHotspotFixedSize;

        /// <summary>
        /// 移动轮盘中心点
        /// </summary>
        private Vector2 initCenterPos;

        /// <summary>
        /// 当前控制轮盘的id
        /// </summary>
        private int touchId;

        /// <summary>
        /// 移动轮盘类型
        /// </summary>
        public JoystickType JoystickType { get; private set; } = JoystickType.Dynamic;

        /// <summary>
        /// 移动轮盘
        /// </summary>
        private GComponent touchPad;

        /// <summary>
        /// 轮盘触摸块
        /// </summary>
        private GImage handle;

        /// <summary>
        /// 轮盘背景图
        /// </summary>
        private GImage handleBg1;
        private GImage handleBg2;
        private GLoader horseState;
        private float handleBgAlpha1;
        private float handleBgAlpha2;

        /// <summary>
        /// 冲刺热区
        /// </summary>
        private GComponent sprintHotspot;

        /// <summary>
        /// 滑动热区
        /// </summary>
        public GComponent joystickHotspot;

        /// <summary>
        /// 奔跑状态
        /// </summary>
        private GComponent sprintLockCom;

        /// <summary>
        /// 触摸过滤组件
        /// </summary>
        public ComTouchArea ComTouchArea;
        /// <summary>
        /// 触摸过滤组件
        /// </summary>
        public ComTouchArea ComTouchArea2;
        
        /// <summary>
        /// 摇杆盘面上的圆形白色背景图标
        /// </summary>
        private GObject sprintLockBG;

        /// <summary>
        /// 置灰的冲刺按钮，点击后进入冲刺状态
        /// </summary>
        private GObject preSprintBtn;

        /// <summary>
        /// 高亮的冲刺按钮，点击后退出冲刺状态，根据当前是否有摇杆操作进入自动奔跑。
        /// </summary>
        private GObject inSprintBtn;

        private GObject sprintArrow;

        /// <summary>
        /// 横向偏移量
        /// </summary>
        public float Horizontal => input.x;

        /// <summary>
        /// 纵向偏移量
        /// </summary>
        public float Vertical => input.y;

        /// <summary>
        /// 是否处在奔跑状态
        /// </summary>
        public bool Sprint => sprintLock;

        /// <summary>
        /// 输入垂直方向 按键W或S 摇杆前或后
        /// </summary>
        private ActionName inputVerticalDirect;

        /// <summary>
        /// 输入水平方向 按键A或D 摇杆前或后
        /// </summary>
        private ActionName inputHorizontalDirect;

        /// <summary>
        /// 移动状态
        /// </summary>
        private MoveState moveState
        {
            get
            {
                return _moveState;
            }
            set
            {
                _moveState = value;
            }
        }

        private MoveState _moveState;
        /// <summary>
        /// 移动状态
        /// </summary>
        private float inputAngle;
        
        private float joystickPercent;

        private float runThreshold = 0.8f;
        
        private float horseRunThreshold = 0.36f;

        /// <summary>
        /// 是都移动到原点
        /// </summary>
        private bool isOriginPoint = false;

        /// <summary>
        /// 冲刺热区UI状态是否锁定
        /// </summary>
        private bool isSprintUILocked = false;
        //public Action<Vector2, float, ActionName, MoveState,bool> TouchMoveInputAction;

        private UiMoveView _moveView => HudBoard?.moveView;

        private bool _isStartInForbidArea = false;

        private Controller hotspotComModeCtrl;
        private Controller sprintLockModeCtrl;
        private Controller sprintLockShowModeCtrl;
        private Controller sprintInOutCtrl;
        private Controller sprintHorseTextCtrl;

        private bool IsHorseDriver => Mc.MyPlayer.MyEntityLocal.IsHorseDriver;
        private bool IsHorseSprint => sprintLock && IsHorseDriver;

        private bool IsHorseAutoRun;

        public bool runLock
        {
            get => _runlock;
            set
            {
                if (_runlock == value)
                {
                    return;
                }

                _runlock = value;
                OnRunLockChanged(value);
            }
        }

        private bool _runlock;

        private bool IsTouching = false;
        /// <summary>
        /// 当帧是否执行过move，包括begin、move、end
        /// </summary>
        private bool IsTouchEvent = false;
        
        private PlayerJoystick nowJoystick;
        private PlayerJoystick playerJoystick;
        private HorseJoystick horseJoystick;

        private Transition newbieLevelShowAnim;

        private bool isNewbieLevelAnimFinished = false;

        protected override void OnCreate(GComponent node)
        {
            base.OnCreate(node);
            touchPad = node.GetChild("handleDirCom").asCom;
            touchPad.onTouchBegin.Add(TouchBegin);
            touchPad.onTouchMove.Add(TouchMove);
            touchPad.onTouchEnd.Add(TouchEnd);

            handle = touchPad.GetChild("handleImg").asImage;
            handleBg1 = touchPad.GetChild("bgImg1").asImage;
            handleBg2 = touchPad.GetChild("bgImg2").asImage;
            handleBgAlpha1 = handleBg1.alpha;
            handleBgAlpha2 = handleBg2.alpha;
            sprintHotspot = node.GetChild("sprintHotspotCom").asCom;
            hotspotComModeCtrl = sprintHotspot.GetController("mode");
            sprintLockCom = node.GetChild("sprintLock").asCom;
            sprintLockModeCtrl = sprintLockCom.GetController("mode");
            sprintInOutCtrl = sprintHotspot.GetController("Touch");
            sprintLockShowModeCtrl = sprintHotspot.GetController("show");
            sprintHorseTextCtrl = sprintHotspot.GetController("horseText");
            horseState = sprintHotspot.GetChild("horseState").asLoader;
            sprintHotspot.GetChild("n8").asTextField.color = Color.white;


            sprintLockCom.visible = false;
            var springUnlockCom = sprintLockCom.GetChild("btnSprintUnlock").asCom;
            sprintLockBG = springUnlockCom.GetChild("n0");

            preSprintBtn = sprintHotspot.GetChild("n0");
            // preSprintBtn.onClick.Add(() =>
            // {
            //     EnterHorseSprintState();
            // });

            inSprintBtn = sprintHotspot.GetChild("n2");
            // inSprintBtn.onClick.Add(() =>
            // {
            //     ExitHorseSprintStateIntoRun(false);
            // });

            sprintArrow = sprintHotspot.GetChild("n3");

            joystickHotspot = (board as UiHud)?.LowerRoot?.GetChild("joyStickHotSpot")?.asCom;

            //PC不需要触摸，Mobile需要触摸
            this.AttachScreenTouchArea();

            ComTouchArea = joystickHotspot.GetChild("ComTouchArea") as ComTouchArea;
            ComTouchArea2 = touchPad.GetChild("ComTouchArea") as ComTouchArea;


            newbieLevelShowAnim = touchPad.GetTransition("appear");
            //冲刺热区
            //sprintHotspot.onRollOver.Add(SprintHotspotOnRollOver);
            //sprintHotspot.onRollOut.Add(SprintHotspotOnRollOut);
            //sprintHotspot.onTouchEnd.Add(SprintHotspotTouchEnd);
            sprintHotspot.visible = false;
            initCenterPos = touchPad.xy;
            touchPadFixedPos = touchPad.xy;

            touchId = -1;
            moveState = MoveState.Idle;
            touchPadHotspotFixedSize = new Vector2(touchPad.size.x, touchPad.size.x);
            SetTouchHotspotType(JoystickType);

            // 倒地死亡取消疾跑状态
            PlayerLogicStateMachine.Event.BindEvent<PlayerEntity>(StateEventType.UnAliveStateDeadEnter, CancelSprintMode);
            PlayerLogicStateMachine.Event.BindEvent<PlayerEntity>(StateEventType.PoseStateDyingEnter, CancelSprintMode);
            PlayerLogicStateMachine.Event.BindEvent<PlayerEntity>(StateEventType.OnMountEnter, CancelSprintMode);
            PlayerLogicStateMachine.Event.BindEvent<PlayerEntity>(StateEventType.OnMountLeave, CancelSprintMode);

            Mc.Msg.AddListener<float, Action>(EventDefine.ShowEscapeLoadingBar, CancelSprintMode);
            Mc.Msg.AddListener<bool>(EventDefine.AidingOtherEvent, Forbid);

            runThreshold = Mc.Tables.TbChracterParameter.JoyStickRunThreshold;

            Mc.Tables.TbPartBase.RegisterDataUpdateEvent(UpdateThreshold);

            if (Mc.MyPlayer != null)
                Mc.MyPlayer.onMountableChanged += onMountableChanged;
            
            playerJoystick = new PlayerJoystick();
            horseJoystick = new HorseJoystick();
            nowJoystick = playerJoystick;
        }

        protected override ActionName HotKey { get{return ActionName.AutoRun; } }
        protected override void OnHotKeyAction()
        {
            logger.InfoFormat("xxxxxxxxxxxxxxxxxxxxx UiHudElemJoystick {0}", runLock);
            if (runLock) {
                runLock = false;
                inputVerticalDirect = ActionName.None;
                Reset(true);
            }
            else
            {
                inputVerticalDirect = ActionName.MoveForward;
                ExitHorseSprintStateIntoRun(true);
            }
        }

        public override void Show()
        {
            if (Mc.MyPlayer != null && (EParachuteState)Mc.MyPlayer.MyEntityServer.ParachuteOperState == EParachuteState.CLOSE && Mc.MyPlayer.MyEntityLocal.TryGetCurrentHeldItem(out IHeldItemEntity heldItem))
            {
                if (heldItem == null || heldItem.TableId != ItemConst.ParachuteItemId)
                {
                    return;
                }
            }

            base.Show();
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            if (PlayHelper.IsNewbie && !isNewbieLevelAnimFinished) 
            {
                SetUpdateMode(FGUIUpdateType.Free);
                CanSetUpdateMode = false;
                newbieLevelShowAnim.Play(() => 
                {
                    CanSetUpdateMode = true;
                    ResetUpdateMode();
                });
                isNewbieLevelAnimFinished = true;
            }
        }

        public override void SetStatus(UiEditableElemStatus status, bool reset = false) {
            base.SetStatus(status, reset);
            if (float.TryParse(this.CustomStatusData, out float result))
            {
                sprintHotspotHeight = result;
            }
            else
            {
                sprintHotspotHeight = 360f;//默认值
            }
            sprintHotspotHeight = sprintHotspotHeight / (status.ScaleX100 / 100f);
            //编辑状态同步
            this.RefreshEditStatus();
        }

        private void UpdateThreshold()
        {
            runThreshold = Mc.Tables.TbChracterParameter.JoyStickRunThreshold;
        }

        /// <summary>
        /// 进入骑马冲刺状态
        /// </summary>
        private void EnterHorseSprintState()
        {
            if (IsHorseSprint)
            {
                return;
            }
            /* 处于自动奔跑状态的马匹可以进入冲刺状态*/
            var mountableGo = Mc.MyPlayer.GetMountableGo();
            if (mountableGo == null)
            {
                return;
            }
            if (!mountableGo.CanSprint())
            {
                /* 耐力不足不能冲刺*/
                return;
            }

            SocDebug.VehicleLog("EnterHorseSprintState 进入骑马冲刺状态");

            IsHorseAutoRun = false;
            runLock = false;
            EnterSprintMode();
            handle.SetXY(touchPad.width / 2, touchPad.width / 2 - touchPad.width / 2);
            SetSprintHotspot(true, true, true);
        }

        /// <summary>
        /// 退出骑马冲刺状态进入奔跑
        /// </summary>
        private void ExitHorseSprintStateIntoRun(bool isAutoRun)
        {
            SocDebug.VehicleLog("ExitHorseSprintState 退出骑马冲刺状态进入奔跑状态");
            /* 退出冲刺进入奔跑*/
            CancelSprintIntoRun();
            runLock = true;
            SetSprintHotspot(true, false, false);
            sprintArrow.visible = false;
            handle.SetXY(touchPad.width / 2, touchPad.width / 2 - touchPad.width / 2);
            IsHorseAutoRun = isAutoRun;
        }

        private void ExitHorseSprintState()
        {
            SocDebug.VehicleLog("ExitHorseSprintState 退出骑马冲刺状态");

            /* 退出冲刺也不进入奔跑*/
            CancelSprintMode(null);
            SetSprintHotspot(false, false, false);
            handle.SetXY(touchPad.width / 2, touchPad.height / 2);
            IsHorseAutoRun = false;
        }

        private void onMountableChanged(long entityId)
        {
            SetPlayerOrHorseMode(Mc.Entity.GetEntity(entityId) is not HorseEntity);
            SwitchJoyStick();
            if (Mc.Entity.GetEntity(entityId) is not HorseEntity)
            {
                ExitHorseSprintState();
            }

            UpdateLock();

            if (sprintLock)
            {
                SetSprintHotspot(true, true, true);
                handle.SetXY(touchPad.width / 2, touchPad.width / 2 - touchPad.width / 2);
            }
        }

        private void SetPlayerOrHorseMode(bool isPlayer)
        {
            hotspotComModeCtrl.selectedIndex = sprintLockModeCtrl.selectedIndex = isPlayer ? 0 : 1;
        }

        private void CancelSprintMode(float total, Action onComplete)
        {
            CancelSprintMode(Mc.MyPlayer.MyEntityLocal);
        }

        public void OnFps30Update(float dt)
        {
            UpdateHorseSprint();
            
            // logger.InfoFormat("sprint:{0}, run : {1}, movestate:{2}", sprintLock, runLock, moveState);

            IsTouchEvent = false;
        }

        private void UpdateHorseSprint()
        {
            if (IsHorseDriver)
            {
                if (IsQuickStop())
                {
                    /* 骑马自动冲刺中如果急停（蹲下按键）则停止自动冲刺*/
                    ExitHorseSprintState();
                }

                var mountableGo = Mc.MyPlayer.GetMountableGo();
                if (mountableGo == null)
                {
                    CancelSprintMode(Mc.MyPlayer.MyEntityLocal);
                    return;
                }
                
                if (IsTouching&& IsTouchEvent)
                {
                    return;
                }
                var fuel = mountableGo.GetCurrentFuel();
                if (IsHorseSprint)
                {
                    /* 因为耐力不足停止冲刺，进入奔跑状态*/
                    if (fuel <= BaseMountableGo.HorseExitSprintIntoRunFuel)
                    {
                        ExitHorseSprintStateIntoRun(true);
                    }
                    else if (fuel >= BaseMountableGo.HorseCanSprintMinFuel)
                    {
                        EnterSprintMode();
                    }
                }
                else if (IsHorseAutoRun || runLock)
                {
                    /* 处于自动奔跑状态的马匹耐力足够可以进入冲刺状态*/
                    if (fuel >= BaseMountableGo.HorseCanSprintMinFuel)
                    {
                        EnterHorseSprintState();
                    }
                }
            }
        }

        private bool IsQuickStop()
        {
            /* 骑马时按下下蹲键认为是急停*/
            return Mc.UserCmd.NowCmd.Crouch || Mc.UserCmd.LastCmd.Crouch;
        }

        public void CancelSprintIntoRun()
        {
            SetSprintState(false);
            input = sprintLock ? Vector2.up * (touchPad.width/2.0f + 1) : Vector2.up * (touchPad.width/4.0f);   
            //先判断死区
            var isInDeadZone = nowJoystick.InDeadZone(ref input, touchPad.parent.size);
            //角度修正
            inputAngle = nowJoystick.GetInputAngle(ref input, snapX, snapY);//计算输入角度(0~359)
            //获取移动类型
            moveState = nowJoystick.GetInputMoveState(moveState, input, isInDeadZone, touchPad.width, touchPad.parent.size, out joystickPercent);

            UpdateJoystickInputToMoveControl();
        }

        public void EnterSprintMode()
        {
            SetSprintState(true);
            //这里加不在输入的判定，是因为不想输入被强制顶掉
            if(!IsTouching)
                input = sprintLock ? Vector2.up * (touchPad.width/2.0f + 1) : Vector2.up * (touchPad.width/4.0f);   
            //先判断死区
            var isInDeadZone = nowJoystick.InDeadZone(ref input, touchPad.parent.size);
            //角度修正
            inputAngle = nowJoystick.GetInputAngle(ref input, snapX, snapY);//计算输入角度(0~359)
            //获取移动类型
            moveState = nowJoystick.GetInputMoveState(moveState, input, isInDeadZone, touchPad.width, touchPad.parent.size, out joystickPercent);

            UpdateJoystickInputToMoveControl();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            if (Mc.Msg != null)
            {
                Mc.Msg.RemoveListener<bool>(EventDefine.AidingOtherEvent, Forbid);
                Mc.Msg.RemoveListener<float, Action>(EventDefine.ShowEscapeLoadingBar, CancelSprintMode);
            }
            PlayerLogicStateMachine.Event.UnBindEvent<PlayerEntity>(StateEventType.UnAliveStateDeadEnter, CancelSprintMode);
            PlayerLogicStateMachine.Event.UnBindEvent<PlayerEntity>(StateEventType.PoseStateDyingEnter, CancelSprintMode);
            PlayerLogicStateMachine.Event.UnBindEvent<PlayerEntity>(StateEventType.OnMountEnter, CancelSprintMode);
            PlayerLogicStateMachine.Event.UnBindEvent<PlayerEntity>(StateEventType.OnMountLeave, CancelSprintMode);
            Mc.Tables.TbPartBase.UnregisterDataUpdateEvent(UpdateThreshold);
            if (Mc.MyPlayer != null)
                Mc.MyPlayer.onMountableChanged -= onMountableChanged;
        }

        /// <summary>
        /// 打断摇杆移动
        /// </summary>
        public void Interrupt()
        {
            if (null != ComTouchArea) ComTouchArea.Interrupt();
            
            if (null != ComTouchArea2) ComTouchArea2.Interrupt();
        }

        /// <summary>
        /// 设置移动轮盘模式
        /// </summary>
        /// <param name="joystickType"></param>
        public void SetTouchHotspotType(JoystickType joystickType)
        {
            JoystickType = joystickType;
            //摇杆节点还没创建，把类型缓存起来，等创建了再设置
            if (joystickHotspot == null)
            {
                return;
            }
            switch (joystickType)
            {
                case JoystickType.Fixed:
                    touchPad.SetXY(touchPadFixedPos.x, touchPadFixedPos.y);
                    joystickHotspot.size = Vector2.zero;
                    break;
                case JoystickType.Floating:
                    //向左偏移3个像素，防止隐藏鼠标时触发点击事件（隐藏鼠标时，指针位于屏幕正中间）
                    joystickHotspot.size = new Vector2(joystickHotspot.parent.width / 2 - 3, GRoot.inst.height);
                    break;
                case JoystickType.Dynamic:
                    touchPad.SetXY(touchPadFixedPos.x, touchPadFixedPos.y);
                    //向左偏移3个像素，防止隐藏鼠标时触发点击事件（隐藏鼠标时，指针位于屏幕正中间）
                    joystickHotspot.size = new Vector2(joystickHotspot.parent.width / 2 - 3, GRoot.inst.height);
                    break;
            }
        }
        
        private void SwitchJoyStick()
        {
            PlayerJoystick wantJoystick = null;
            if (IsHorseDriver)
            {
                wantJoystick = horseJoystick;
            }
            else
            {
                wantJoystick = playerJoystick;
            }
            if (nowJoystick == wantJoystick)
            {
                return;
            }
            nowJoystick = wantJoystick;
        }

        /// <summary>
        /// 移动轮盘热区按下
        /// </summary>
        /// <param name="context"></param>
        public void TouchBegin(EventContext context)
        {
            Mc.MyPlayer?.SecMoveReportInfo?.SetAngle(-1);
            
            IsTouching = true;
            IsTouchEvent = true;
            var node = TryGetNode();
            if (IsInEdit || node == null || node is { visible: false })
                return;
            // 切换刷新模式， 以确保跑步锁定节点的动画能够正常播放
            if (updateType == FGUIUpdateType.LockAfterShow)
            {
                SetUpdateMode(FGUIUpdateType.SkipFrame);
            }

            var touchPos = GetTouchBeginPos(node, context);

            // Debug.LogError("TouchBegin origin pos" + inputEvt.position + " transformPos" + touchPos + " scale" + GRoot.inst.scale);

            if (IsHorseSprint)
            {
                /* 往下减一段y值是为了让摇杆在冲刺的时候向上偏移*/
                touchPos.y -= -touchPad.height / 2;
            }

            initCenterPos = touchPos;
            if (JoystickType == JoystickType.Floating)
            {
                touchPad.SetXY(touchPos.x, touchPos.y);
            }
            else
            {
                touchPad.SetXY(touchPadFixedPos.x, touchPadFixedPos.y);
                if (JoystickType == JoystickType.Fixed)
                {
                    // 不知道为啥touchPadFixedPos永远（不管锚点是否设在中点）是基于左上角的，所以这里手动转换一下。之前不知道为啥可以正确，fgui日常抽风
                    initCenterPos = GetRelativePosToParent(touchPadFixedPos, node);
                    var dis = Vector2.Distance(initCenterPos, touchPos);
                    // Debug.LogError($"dis {dis} initcenterPos {initCenterPos} touchPos {touchPos} inputEvt.position {inputEvt.position} {node.pivot} {node.position} {node.width} {node.height}");

                    if (dis > touchPad.width)
                    {
                        // Debug.LogError($"dis {dis} initcenterPos {initCenterPos} touchPos {touchPos} inputEvt.position {inputEvt.position}");
                        touchId = -1;
                        return;
                    }
                    CollectTouchInput(touchPos);
                }
                else
                {
                    var touchPadLocalPos = GetRelativePosToParent(touchPadFixedPos, node);
                    var isInTouchPad = touchPos.x >= touchPadLocalPos.x - touchPad.width / 2.0f * touchPad.scaleX
                                       && touchPos.x <= touchPadLocalPos.x + touchPad.width / 2.0f * touchPad.scaleX
                                       && touchPos.y >= touchPadLocalPos.y - touchPad.height / 2.0f * touchPad.scaleY
                                       && touchPos.y <= touchPadLocalPos.y + touchPad.height / 2.0f * touchPad.scaleY;

                    if (!isInTouchPad && _moveView != null && _moveView.IsInForbidMoveArea(touchPos))
                    {
                        _isStartInForbidArea = true;
                    }
                }
            }

            // Debug.LogError($"ui Debug: joystick touchBegin");
            context.CaptureTouch();
        }
        /// <summary>
        /// 移动轮盘热区抬起
        /// </summary>
        /// <param name="context"></param>
        public void TouchEnd(EventContext context)
        {
            Mc.MyPlayer?.SecMoveReportInfo?.SetAngle(-1);
            
            IsTouchEvent = true;
            var node = TryGetNode();
            if (IsInEdit || node == null || node is { visible: false })
                return;
            // 恢复刷新模式
            ResetUpdateMode();
            if(BreakTouchEnd(context)) return;

            //触摸抬起时，复位移动轮盘
            if (JoystickType == JoystickType.Floating)
            {
                touchPad.SetXY(touchPadFixedPos.x, touchPadFixedPos.y);
            }

            //手指抬起时，判断冲刺热区UI是否被锁定，并且抬起点满足冲刺条件，才锁定疾跑状态
            if (isSprintUILocked)
            {
                if (moveState == MoveState.Sprint)
                {
                    SetSprintState(true);
                }
                else if (moveState == MoveState.Run && IsHorseDriver)
                {
                    ExitHorseSprintStateIntoRun(true);
                }
                if (IsHorseSprint)
                {
                    /* 如果是骑马冲刺，需要保留冲刺热区UI，并显示高亮状态*/
                    SetSprintHotspot(true, true, true);
                }
                else if (IsHorseDriver && runLock)
                {
                    SetSprintHotspot(true, true, false);
                }
                else
                {
                    SetSprintHotspot(true, false);
                }
                isSprintUILocked = false;
            }
            Reset();
        }
        private void SetSprintHotspot(bool visible, bool isHighlight, bool runOrSprint=false)
        {
            // logger.InfoFormat("===================sprint{0}, run : {1}, runorsprint{1}", sprintLock, runLock, runOrSprint);
            RefreshSprintHotspot(false);
            RefreshSprintHotspot(visible);
            if(visible == false)
                return;
            RefreshSprintHotspotHighlight(isHighlight);
            RefreshSprintHotspotHorseText(runOrSprint);
            

            // if (horseState != null)
            // {
            //     horseState.color = runOrSprint ?  new Color(1, 0.4745098f, 0, 1) : Color.white;
            // }
        }
        

        /// <summary>
        /// 冲刺整体的组件
        /// </summary>
        /// <param name="visible"></param>
        private void RefreshSprintHotspot(bool visible)
        {
            sprintHotspot.visible = visible;
            if (visible == false)
            {
                return;
            }
            sprintArrow.visible = true;
        }

        /// <summary>
        /// 冲刺高亮
        /// </summary>
        /// <param name="isHighlight"></param>
        private void RefreshSprintHotspotHighlight(bool isHighlight)
        {
            int idx = isHighlight ? 1 : 0;
            if (idx != sprintInOutCtrl.selectedIndex)
            {
                // var str = idx == 1 ? "移入" : "移出";
                // Debug.LogError($"{str}");
                sprintInOutCtrl.selectedIndex = idx;
            }
        }
        
        /// <summary>
        /// 冲刺马的文本
        /// </summary>
        /// <param name="isHighlight"></param>
        private void RefreshSprintHotspotHorseText(bool isHighlight)
        {
            if (IsHorseDriver)
            {
                int idx = isHighlight ? 1 : 0;
                if (idx != sprintHorseTextCtrl.selectedIndex)
                {
                    sprintHorseTextCtrl.selectedIndex = idx;
                }
            }
        }

        /// <summary>
        /// 取消疾跑模式
        /// </summary>
        public void CancelSprintMode(PlayerEntity entity)
        {
            SetSprintState(false);
            input = sprintLock ? Vector2.up * (touchPad.width/2.0f + 1) : Vector2.zero;
            UpdateJoystickInputToMoveControl();
        }

        private void Reset(bool cancelSprint = false)
        {
            IsTouching = false;
            if (cancelSprint)
                SetSprintState(false);
            if (sprintLock)
            {
                input =Vector2.up * (touchPad.width/2.0f + 1);
            }else if (runLock)
            {
                input = Vector2.up * (touchPad.width / 2.0f);
            }
            else
            {
                input = Vector2.zero;
            }
            
            //锁定马的疾跑或则还在马的自动奔跑阶段
            if (IsHorseDriver&&(IsHorseSprint|| runLock))
            {
                /* 骑马冲刺状态下松开摇杆后直线冲刺*/
                // inputAngle = GetInputAngle();
                // GetInputDirect(false, out inputVerticalDirect, out inputHorizontalDirect);
                //马在疾跑需要显示在顶部
                handle.SetXY(touchPad.width / 2, touchPad.width / 2 - touchPad.width / 2);
            }
            else
            {
                //回到中心，因为锁定的时候，不是马的话是隐藏的
                handle.SetXY(touchPad.width / 2, touchPad.height / 2);
                SetSprintHotspot(false, false);
            }
            //复位
            touchId = -1;
            isOriginPoint = false;
            _isStartInForbidArea = false;
            /* 结束触碰前，更新移动状态*/
            //先判断死区
            var isInDeadZone = nowJoystick.InDeadZone(ref input, touchPad.parent.size);
            //角度修正
            inputAngle = nowJoystick.GetInputAngle(ref input, snapX, snapY);//计算输入角度(0~359)
            //获取移动类型
            moveState = nowJoystick.GetInputMoveState(moveState, input, isInDeadZone, touchPad.width, touchPad.parent.size, out joystickPercent);
            
            UpdateJoystickInputToMoveControl();

            if (IsHorseDriver)
            {
                /* 有任何操作都会退出自动奔跑*/
                IsHorseAutoRun = false;
            }
        }

        /// <summary>
        /// 收集Touch输入并刷新移动轮盘状态
        /// </summary>
        /// <param name="touchPos"></param>
        private void CollectTouchInput(Vector2 touchPos)
        {
            Vector2 inputPos = (touchPos - initCenterPos);
            Vector2 handlePos;
           
            HandleInputPos(inputPos);
            var radius = touchPad.width / 2;
            if (input.magnitude > radius)
            {
                var tempInput = new Vector2(input.x, -input.y).normalized;
                handlePos = tempInput * radius;
            }
            else
            {
                //y翻转
                handlePos = new Vector2(input.x, -input.y);
            }
            handle.SetXY(handlePos.x + radius, handlePos.y + radius);

            if (moveState == MoveState.Sprint)
            {
                runLock = false;
                //[B2下]新需求，锁定按钮永远在摇杆的上方，不需要修改位置
                var showPos = touchPad.xy + new Vector2(0, -sprintHotspotHeight);
                sprintHotspot.SetXY(showPos.x, showPos.y);
                //SetSprintCompPos(showPos);
                var logicPos = initCenterPos + new Vector2(0, -sprintHotspotHeight);
                //这里是判断是否需要锁定冲刺
                if (IsHorseDriver)
                {
                    if (sprintLock)
                    {
                        
                    }
                    else
                    {
                        if (Vector2.Distance(touchPos, logicPos) <= 130)
                        {
                            //高亮，锁定
                            SetSprintHotspot(true, true, true);
                            isSprintUILocked = true;
                            //马要不松手就显示锁定按钮
                            // SetSprintState(true);

                        }
                        else
                        {
                            //不高亮，不锁定
                            SetSprintHotspot(false, false, true);
                            isSprintUILocked = false;
                        }
                    }
                }
                else
                {
                    if (Vector2.Distance(touchPos, logicPos) <= 130)
                    {
                        //高亮，锁定
                        SetSprintHotspot(true, true, true);
                        isSprintUILocked = true;
                    }
                    else
                    {
                        //不高亮，不锁定
                        SetSprintHotspot(true, false, true);
                    }
                }
            }
            else if (moveState == MoveState.Run && IsHorseDriver)
            {
                var mountableGo = Mc.MyPlayer.GetMountableGo();
                if (mountableGo == null)
                    return;
                var fuel = mountableGo.GetCurrentFuel();
                var logicPos = initCenterPos + new Vector2(0, -sprintHotspotHeight);
                if (runLock)
                {
                    if (horseJoystick.CancleLockRun(input, touchPad.width))
                    {
                        SetSprintHotspot(false, false, false);
                        isSprintUILocked = false;
                        runLock = false;
                    }
                    else if (fuel > 60)//Mc.Tables.TbHorsePropertyFactor.HorsSprintThreshold)
                    {
                        EnterHorseSprintState();
                    }
                }
                else
                {
                    if (fuel < Mc.Tables.TbHorsePropertyFactor.HorsSprintThreshold && Vector2.Distance(touchPos, logicPos) <= 130)
                    {
                        if (!sprintLock)
                            Mc.MsgTips.ShowRealtimeWeakTip(24283);
                        SetSprintHotspot(true, true, false);
                        isSprintUILocked = true;
                    }
                    else
                    {
                        SetSprintHotspot(false, false, false);
                        isSprintUILocked = false;
                        runLock = false;
                    }
                }
                SetSprintState(false);
            }
            else
            {
                //取消锁定显示
                SetSprintState(false);
                runLock = false;
                //取消疾跑UI显示
                SetSprintHotspot(false, false);
            }
        }

        /// <summary>
        /// 设置冲刺热区位置
        /// </summary>
        /// <param name="showPos"></param>
        private void SetSprintCompPos(Vector2 showPos)
        {
            if (!sprintLock)
            {
                //马就不改位置了
                var node = TryGetNode();
                if (node != null)
                {
                    var showPosXY = GetRelativePosToTopLeft(showPos, node);
                    sprintHotspot.SetXY(showPosXY.x, showPosXY.y); // 这个函数永远基于左上角了，不理解，fgui日常抽风
                }
            }
        }

        /// <summary>
        /// 设置冲刺状态
        /// </summary>
        /// <param name="lockSprint"></param>
        private void SetSprintState(bool lockSprint)
        {
            sprintLock = lockSprint;
            // if (sprintLockCom != null)
            // {
            //     sprintLockCom.visible = lockSprint;
            //     sprintLockShowModeCtrl.selectedIndex = lockSprint && sprintLockModeCtrl.selectedIndex == 1 ? 1 : 0;
            // }
            // if (handle != null && IsHorseSprint == false /* 骑马冲刺状态下需要显示摇杆*/)
            // {
            //     handle.visible = !lockSprint;
            // }
            // if (sprintLockBG != null)
            // {
            //     /* 冲刺的背景图标，在摇杆盘的上方的圆形，因为冲刺会显示摇杆，所以不需要显示这个圆形*/
            //     sprintLockBG.visible = IsHorseDriver ? false : lockSprint;
            // }
            // runLock = sprintLock;
            UpdateLock();
        }

        private void UpdateLock()
        {
            if (IsHorseDriver)
            {
                var val = sprintLock || runLock;
                if (sprintLockCom != null)
                {
                    sprintLockCom.visible = val;
                    sprintLockShowModeCtrl.selectedIndex = val && sprintLockModeCtrl.selectedIndex == 1 ? 1 : 0;
                    
                }
                if (handle != null /* 骑马冲刺状态下需要显示摇杆*/)
                {
                    handle.visible = true;
                }
                if (sprintLockBG != null)
                {
                    /* 冲刺的背景图标，在摇杆盘的上方的圆形，因为冲刺会显示摇杆，所以不需要显示这个圆形*/
                    sprintLockBG.visible = false;
                }

                // RefreshSprintHotspotHighlight(sprintLock);
                // RefreshSprintHotspotHorseText(!runLock);
            }
            else
            {
                var val = sprintLock;
                if (sprintLockCom != null)
                {
                    sprintLockCom.visible = val;
                }
                if (handle != null /* 骑马冲刺状态下需要显示摇杆*/)
                {
                    handle.visible = !val;
                }
                if (sprintLockBG != null)
                {
                    /* 冲刺的背景图标，在摇杆盘的上方的圆形，因为冲刺会显示摇杆，所以不需要显示这个圆形*/
                    sprintLockBG.visible = val;
                }
            }
        }

        private void OnRunLockChanged(bool value)
        {
            Mc.Control?.OnRunLockChanged(value);
            
            UpdateLock();
        }

        public void SetSnapXYOn(bool isOn)
        {
            snapX = isOn;
            snapY = isOn;
        }
        
        /// <summary>
        /// 计算物理距离
        /// </summary>
        /// <param name="len"></param>
        /// <returns></returns>
        private float CalcInchDis(float len)
        {
            // 先转唤出窗口像素长度，再转换出设备像素长度，最后转换成inch
            var targetScreenDpiRatio = Mc.Control.TouchInput.TargetDpi / Screen.dpi;
            return len * GRoot.inst.scaleX / targetScreenDpiRatio / Mc.Control.RotationControl.Dpi;
        }

        private float CalcPixelDis(float inchLen)
        {
            var targetScreenDpiRatio = Mc.Control.TouchInput.TargetDpi / Screen.dpi;
            return inchLen / GRoot.inst.scaleX * targetScreenDpiRatio * Mc.Control.RotationControl.Dpi;
        }

        private void UpdateJoystickInputToMoveControl()
        {
#if UNITY_EDITOR || STANDALONE_REAL_PC
            if (input.magnitude > 0)
            {
                Mc.Control.MoveControl.UiJoystickInput = true;
            }
            else
            {
                Mc.Control.MoveControl.UiJoystickInput = false;
            }
#endif
            //禁止sprint 摇杆部分
            if (moveState == MoveState.Sprint && Mc.MyPlayer != null && Mc.MyPlayer.MyEntityLocal != null)
            {
                IEntity mountableEntity = Mc.Entity.GetEntity(Mc.MyPlayer.MyEntityServer.MountableId);
                bool isParachuteOpen = mountableEntity != null && mountableEntity.EntityType == EntityTypeId.ParachuteEntity;
                if (isParachuteOpen)
                {
                    moveState = MoveState.Run;
                }
                else if (TaskBattleUtil.GetNewbieSprintJoystickOff())
                {
                    moveState = MoveState.Run;
                }
                else if (Mc.MyPlayer.MyEntityLocal.MoveState == PlayerMoveStateEnum.MoveLadder)
                {//爬梯
                    moveState = MoveState.Run;
                }
                else if (Mc.MyPlayer.MyEntityLocal.AdsState == PlayerAdsStateEnum.AdsOn)
                {//手机防误触 ADS下不能冲刺和PC 不一样
                    moveState = MoveState.Run;
                }
            }
            
            Mc.Control.MoveControl.UpdateJoystickData(input, inputAngle, inputVerticalDirect, inputHorizontalDirect, moveState, isOriginPoint, joystickPercent);
        }
        /// <summary>
        /// 移动轮盘热区拖动
        /// </summary>
        /// <param name="context"></param>
        public void TouchMove(EventContext context)
        {
            IsTouchEvent = true;
            var node = TryGetNode();
            if (IsInEdit || node == null || node is { visible: false }) return;

            Vector2 touchPos = GetTouchMovePos(context, node);

            // 假如一开始不在禁止区就是一直是false,假如一开始在，则要再计算一下
            if (_isStartInForbidArea)
            {
                _isStartInForbidArea = _moveView != null && _moveView.IsInForbidMoveArea(touchPos);
                if (!_isStartInForbidArea)
                {
                    initCenterPos = touchPos;
                }
            }

            if (!_isStartInForbidArea)
            {
                CollectTouchInput(touchPos);
            }
        }
        protected override void OnDisable()
        {
            base.OnDisable();
            Reset(true);
        }

        private void Forbid(bool forbidden)
        {
            if (forbidden)
            {
                joystickHotspot.enabled = false;
                sprintHotspot.enabled = false;
                handle.alpha = 0.5f;
                handleBg1.alpha = 0.5f * handleBgAlpha1;
                handleBg2.alpha = 0.5f * handleBgAlpha2;
            }
            else
            {
                joystickHotspot.enabled = true;
                sprintHotspot.enabled = true;
                handle.alpha = 1f;
                handleBg1.alpha = handleBgAlpha1;
                handleBg2.alpha = handleBgAlpha2;
            }
        }
        /// <summary>
        /// 可点击区域
        /// </summary>
        /// <returns></returns>
        public override List<GObject> ExtraTouchableAreas()
        {
            var node = TryGetNode();
            if (node == null) return null;
            var touchPad = node.GetChild("handleDirCom").asCom;
            return new List<GObject>() { touchPad };
        }
        protected override GComponent HudDefaultTouchNode()
        {
            var node = TryGetNode();
            if (node == null) return null;
            var touchPad = node.GetChild("handleDirCom")?.asCom;
            return touchPad;
        }

        #region 新版输入
        public void HandleInputPos(Vector2 inputPos)
        {
            if (input.magnitude == 0)
            {
                isOriginPoint = true;
            }
            input = new Vector2(inputPos.x, inputPos.y * -1);//FGUI Y轴反向
            //先判断死区
            var isInDeadZone = nowJoystick.InDeadZone(ref input, touchPad.parent.scale);
            //角度修正
            inputAngle = nowJoystick.GetInputAngle(ref input, snapX, snapY);//计算输入角度(0~359)
            //输入的方向
            nowJoystick.GetInputDirect(input, out inputVerticalDirect, out inputHorizontalDirect);
            //获取移动类型
            moveState = nowJoystick.GetInputMoveState(moveState, input, isInDeadZone, touchPad.width, touchPad.parent.scale, out joystickPercent);
            
            UpdateJoystickInputToMoveControl();
            
            Mc.MyPlayer?.SecMoveReportInfo?.SetAngle(Mathf.FloorToInt(inputAngle));
        }
        #endregion
      
    }
}