{"hash": 751265004, "data": [{"id": 60001, "name": {"index": **********, "text": "熊"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60000, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/bear_corpse_new.prefab", "treasureDropId": 60010, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1007, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 0, "mass": [], "CountType": 3, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 60002, "name": {"index": **********, "text": "北极熊"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60001, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/polarBear_corpse_new.prefab", "treasureDropId": 60010, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1007, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 0, "mass": [], "CountType": 3, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 60003, "name": {"index": **********, "text": "野猪"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60002, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/boar_corpse_new.prefab", "treasureDropId": 60010, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1007, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 0, "mass": [], "CountType": 3, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 60004, "name": {"index": **********, "text": "鹿"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60003, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/stag_corpse_new.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1007, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 0, "mass": [], "CountType": 3, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 60005, "name": {"index": **********, "text": "鸡"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60004, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/chicken_corpse_new.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1007, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 0, "mass": [], "CountType": 3, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 60006, "name": {"index": **********, "text": "狼"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60005, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/wolf_corpse_new.prefab", "treasureDropId": 60010, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1007, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 0, "mass": [], "CountType": 3, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 60007, "name": {"index": **********, "text": "科学家"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 101002, "boxId": 31306, "isShowUI": false, "UIType": 2, "highLightId": 1007, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 60008, "name": {"index": 657645971, "text": "玩家"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/CharAnimator/Mannequin/Era03H_Male0000/Era03H_Male0000_Corpse@era03h_male0000_corpse/Prefabs/BON_Common_Male0000_Corpse.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 1, "maxCorpseRigVel": 0, "isPreload": false, "floatAttri": 10000}, {"id": 60009, "name": {"index": **********, "text": "重甲科学家"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_Heavy_corpse.prefab", "treasureDropId": 0, "scientistDropId": 101003, "boxId": 31310, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 60010, "name": {"index": **********, "text": "科学家"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 101001, "boxId": 31306, "isShowUI": false, "UIType": 2, "highLightId": 1007, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 60011, "name": {"index": **********, "text": "运输机残骸（底门）"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Vehicle/Ch47/Prefabs/Corpse/bottom_door.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [-0.41730928, 0.8937163, -0.99714565], "localRotation": [0, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [10, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60012, "name": {"index": **********, "text": "运输机残骸（前螺旋桨）"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Vehicle/Ch47/Prefabs/Corpse/front_propellor.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [1.6170001, 4.603, 5.757], "localRotation": [350.17926, 7.0847363, 342.75244], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [10, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60013, "name": {"index": 275568981, "text": "运输机残骸（后螺旋桨）"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Vehicle/Ch47/Prefabs/Corpse/rear_propellor.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0.42, 7.142, -5.113], "localRotation": [9.455929, 2.4917643, 15.883576], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [10, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60014, "name": {"index": 779162346, "text": "运输机残骸（左门）"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Vehicle/Ch47/Prefabs/Corpse/left_door.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [-2.272469, 2.3715422, 4.189975], "localRotation": [0, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [10, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60015, "name": {"index": 714115574, "text": "运输机残骸（右门）"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Vehicle/Ch47/Prefabs/Corpse/right_door.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0.982526, 1.2180184, 5.0078125], "localRotation": [270, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [10, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60016, "name": {"index": 585845176, "text": "运输机残骸（后门）"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Vehicle/Ch47/Prefabs/Corpse/rear_door.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0.012544708, 1.0937521, -5.891944], "localRotation": [0, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [10, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60017, "name": {"index": **********, "text": "运输机残骸（主体）"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Vehicle/Ch47/Prefabs/Corpse/mainbody.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [-0.17015296, 3.5312817, 0.41280234], "localRotation": [0, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "Helicopter_Explo", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [10, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60018, "name": {"index": **********, "text": "坦克残骸"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/M2Bradley/prefabs/m2bradley_gibs/left_wheel-1.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [-0.2, 0.4, 0.776], "localRotation": [270, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [500, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60019, "name": {"index": **********, "text": "坦克残骸(主体)"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/M2Bradley/prefabs/m2bradley_gibs/mainbody.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0.06, 1.28, 0.22], "localRotation": [270, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "Helicopter_Explo", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [500, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60020, "name": {"index": 929360056, "text": "坦克残骸"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/M2Bradley/prefabs/m2bradley_gibs/rear_door.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [-0.01, 0.95, 3], "localRotation": [270, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [500, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60021, "name": {"index": 37624647, "text": "坦克残骸"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/M2Bradley/prefabs/m2bradley_gibs/rear_hatch.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [-0.04, 2, -2.5], "localRotation": [270, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [500, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60022, "name": {"index": 443653764, "text": "坦克残骸"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/M2Bradley/prefabs/m2bradley_gibs/right_wheel-1.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [2, 0.46, -0.87], "localRotation": [270, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [500, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60023, "name": {"index": **********, "text": "坦克残骸"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/M2Bradley/prefabs/m2bradley_gibs/right_wheel-2.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [2, 0.46, 2.27], "localRotation": [270, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [500, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60024, "name": {"index": **********, "text": "坦克残骸"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/M2Bradley/prefabs/m2bradley_gibs/rocketpod.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [-1.5, 2.17, -0.33], "localRotation": [270, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [500, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60025, "name": {"index": 368202758, "text": "坦克残骸"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/M2Bradley/prefabs/m2bradley_gibs/side_MG.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [1.11, 3.08, 1.51], "localRotation": [270, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [500, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60026, "name": {"index": **********, "text": "坦克残骸"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/M2Bradley/prefabs/m2bradley_gibs/side_panel.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [2.25, 1.35, 0.25], "localRotation": [270, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [500, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60027, "name": {"index": 670823259, "text": "坦克残骸"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/M2Bradley/prefabs/m2bradley_gibs/turret.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0.43, 2.74, -0.01], "localRotation": [270, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [500, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60028, "name": {"index": **********, "text": "马匹"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60007, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Vehicle/Horse/VEH_Horse_LV1_Tp/Prefabs/Horse Realistic Corpse.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1007, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 2400, "durationInChina": 2400, "canCollectCorpse": true, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 0, "isPreload": false, "floatAttri": 0}, {"id": 60029, "name": {"index": **********, "text": "武直残骸（后螺旋桨）"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Vehicle/PatrolHelicopter/Prefabs/Corpse/back_propellor.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0.417275, 3.9310434, -10.589379], "localRotation": [0, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [10, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60030, "name": {"index": **********, "text": "武直残骸（前螺旋桨）"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Vehicle/PatrolHelicopter/Prefabs/Corpse/front_propellor.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0, 3.467539, 0], "localRotation": [0, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [10, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60031, "name": {"index": 710594848, "text": "武直残骸（主体）"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Vehicle/PatrolHelicopter/Prefabs/Corpse/mainbody.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0.072883144, 2.3708417, -3.2909245], "localRotation": [0, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [10, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60032, "name": {"index": 412645936, "text": "武直残骸（后门）"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Vehicle/PatrolHelicopter/Prefabs/Corpse/rear_door.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [-1.2066969, 1.4648778, -1.4401748], "localRotation": [0, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [10, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60033, "name": {"index": **********, "text": "武直残骸（右门）"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Vehicle/PatrolHelicopter/Prefabs/Corpse/right_door.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [1.1258978, 1.6598167, 3.036894], "localRotation": [0, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [10, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60034, "name": {"index": **********, "text": "武直残骸（尾部）"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41004, "startHealth": 100, "BaseProtection": 60001, "gatherType": "2", "dropId": 10302, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Vehicle/PatrolHelicopter/Prefabs/Corpse/tailbody.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": true, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0, 1.7063607, -9.809461], "localRotation": [0, 0, 0], "isPhysics": true, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 300, "mass": [10, 2000], "CountType": 0, "maxCorpseRigVel": 10, "isPreload": true, "floatAttri": 0}, {"id": 60035, "name": {"index": 916036693, "text": "重甲科学家"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_Heavy_corpse.prefab", "treasureDropId": 0, "scientistDropId": 101003, "boxId": 31310, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 61001, "name": {"index": **********, "text": "BOT尸体"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102001, "boxId": 31311, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 0, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 61002, "name": {"index": **********, "text": "BOT尸体"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102002, "boxId": 31311, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 0, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 61003, "name": {"index": 952644447, "text": "BOT尸体"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102003, "boxId": 31311, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 0, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 61004, "name": {"index": **********, "text": "BOT尸体"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102004, "boxId": 31311, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 0, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 61005, "name": {"index": 618065796, "text": "BOT尸体"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102005, "boxId": 31311, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 0, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 61006, "name": {"index": **********, "text": "BOT尸体"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102005, "boxId": 31311, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 0, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 61007, "name": {"index": 985883685, "text": "BOT尸体"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102006, "boxId": 31311, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 0, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 61008, "name": {"index": 850107038, "text": "BOT尸体"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102006, "boxId": 31311, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 0, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 61013, "name": {"index": 81594823, "text": "BOT尸体"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102007, "boxId": 31311, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 0, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 62001, "name": {"index": **********, "text": "鸡"}, "interactiveId": 0, "containerId": 0, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60004, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/chicken_mutation_corpse.prefab", "treasureDropId": 60008, "scientistDropId": 0, "boxId": 31306, "isShowUI": true, "UIType": 2, "highLightId": 1007, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 1800, "canCollectCorpse": true, "gatherDelay": 0, "mass": [], "CountType": 3, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 62002, "name": {"index": 367337072, "text": "重甲科学家"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_Heavy_corpse.prefab", "treasureDropId": 60009, "scientistDropId": 0, "boxId": 31310, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 62101, "name": {"index": 315054863, "text": "特遣队队员"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102101, "boxId": 31310, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 62102, "name": {"index": **********, "text": "特遣队队员"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102102, "boxId": 31310, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 62103, "name": {"index": 466688, "text": "特遣队队员"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102103, "boxId": 31310, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 62104, "name": {"index": 473081787, "text": "特遣队队长"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102104, "boxId": 31310, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 62105, "name": {"index": 961739662, "text": "特遣队队员"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102105, "boxId": 31310, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 62106, "name": {"index": **********, "text": "特遣队队员"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102106, "boxId": 31310, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 62107, "name": {"index": 439711335, "text": "特遣队队长"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102107, "boxId": 31310, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 62108, "name": {"index": **********, "text": "风险清除小组组员"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102108, "boxId": 31310, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 62109, "name": {"index": **********, "text": "风险清除小组组员"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102109, "boxId": 31310, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 62110, "name": {"index": 682182465, "text": "风险清除小组组员"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102110, "boxId": 31310, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 62111, "name": {"index": 730223121, "text": "风险清除小组组员"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102111, "boxId": 31310, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 62112, "name": {"index": **********, "text": "风险清除小组组员"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102112, "boxId": 31310, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 62113, "name": {"index": **********, "text": "风险清除小组组员"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 10203, "boxId": 31310, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 62114, "name": {"index": **********, "text": "风险清除小组组员"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102113, "boxId": 31310, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 62115, "name": {"index": **********, "text": "特遣队队长"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 102107, "boxId": 31310, "isShowUI": false, "UIType": 2, "highLightId": 1006, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}, {"id": 63001, "name": {"index": **********, "text": "科学家"}, "interactiveId": 0, "containerId": 10005, "combatComponent": 41003, "startHealth": 100, "BaseProtection": 60000, "gatherType": "3", "dropId": 60006, "maxDestroyFractionForFinishBonus": 0.2, "finishBonusDropId": 0, "resPath": "Char/NPC/Monster/DeadBody/scientist_corpse.prefab", "treasureDropId": 0, "scientistDropId": 0, "boxId": 0, "isShowUI": false, "UIType": 2, "highLightId": 1007, "heightOffset": 0, "localPosition": [0], "localRotation": [0], "isPhysics": false, "kinematicDuration": 0, "createSoundEffect": "", "duration": 1800, "durationInChina": 3, "canCollectCorpse": false, "gatherDelay": 0, "mass": [], "CountType": 2, "maxCorpseRigVel": 2, "isPreload": false, "floatAttri": 0}]}