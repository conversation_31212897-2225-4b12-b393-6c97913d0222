using System.Collections.Generic;

namespace WizardGames.Soc.Common.NodeSystem
{
    public class LanguageConst
    {
		/// <summary>
        /// 断开当前连接...
        /// </summary>
        public const int DisconnectConnect = 2079349080;
		/// <summary>
        /// 退出游戏场景...
        /// </summary>
        public const int ExitGameScene = 1772341855;
		/// <summary>
        /// 连接{0}......
        /// </summary>
        public const int ConnectSvr = 773518353;
		/// <summary>
        /// 进入离线地图{0}...
        /// </summary>
        public const int EnterOffline = 1291970644;
		/// <summary>
        /// 米
        /// </summary>
        public const int UnitMeter = 469619012;
		/// <summary>
        /// 时
        /// </summary>
        public const int UnitHour = 239284321;
		/// <summary>
        /// 分
        /// </summary>
        public const int UnitMin = 1731041262;
		/// <summary>
        /// 秒
        /// </summary>
        public const int UnitSec = 722324195;
		/// <summary>
        /// {0}毫升
        /// </summary>
        public const int UnitWater = 59166383;
		/// <summary>
        /// rWm
        /// </summary>
        public const int UnitPower = 1133737055;
		/// <summary>
        /// 私聊
        /// </summary>
        public const int ChatPrivate = 18217164;
		/// <summary>
        /// 频道
        /// </summary>
        public const int ChatChannel = 1214334740;
		/// <summary>
        /// 队伍
        /// </summary>
        public const int ChatTeam = 1500327936;
		/// <summary>
        /// 世界
        /// </summary>
        public const int ChatWorld = 800317670;
		/// <summary>
        /// Hud_Chat_Title01
        /// </summary>
        public const int ChatTeamLabel = 1854228139;
		/// <summary>
        /// Hud_Chat_Title02
        /// </summary>
        public const int ChatWorldLabel = 93499275;
		/// <summary>
        /// Hud_Chat_Title03
        /// </summary>
        public const int ChatPrivateLabel = 1611967366;
		/// <summary>
        /// 自我解脱
        /// </summary>
        public const int KillSelf = 194669483;
		/// <summary>
        /// 救助
        /// </summary>
        public const int Aid = 236718688;
		/// <summary>
        /// 解除威胁
        /// </summary>
        public const int DangerLifting = 312184840;
		/// <summary>
        /// 预约
        /// </summary>
        public const int ChatAppointment = 1712122609;
		/// <summary>
        /// 追踪
        /// </summary>
        public const int DetailTrace = 181526304;
		/// <summary>
        /// 无法追踪
        /// </summary>
        public const int DetailCanntTrace = 1238250579;
		/// <summary>
        /// 暂无掉落。
        /// </summary>
        public const int DetailNoDrop = 1019435516;
		/// <summary>
        /// 无法通过交易获得。
        /// </summary>
        public const int DetailCanntGetByTrade = 1438227932;
		/// <summary>
        /// 前往制作
        /// </summary>
        public const int GoToCraft = 263055538;
		/// <summary>
        /// 道具未解锁
        /// </summary>
        public const int NotUnlockItem = 894964112;
		/// <summary>
        /// Hud_Chat_Title01
        /// </summary>
        public const int ChatAppointmentLabel = 1739898229;
		/// <summary>
        /// 调制台
        /// </summary>
        public const int DetailMixingTable = 1606658277;
		/// <summary>
        /// 打开调制台
        /// </summary>
        public const int OpenMixingTable = 520547381;
		/// <summary>
        /// 打开工作台
        /// </summary>
        public const int OpenWorkbench = 938662027;
		/// <summary>
        /// 道具已解锁。
        /// </summary>
        public const int HasUnlocked = 587262695;
		/// <summary>
        /// 该道具无法通过工作台解锁。
        /// </summary>
        public const int CantUnlockViaWorkbench = 1501076611;
		/// <summary>
        /// 研究工作台
        /// </summary>
        public const int ResearchWorckbench = 329703585;
		/// <summary>
        /// 打开研究台
        /// </summary>
        public const int OpenResearchWorckbench = 1020017083;
		/// <summary>
        /// 分解机
        /// </summary>
        public const int NameRecycler = 119152975;
		/// <summary>
        /// 打开分解机
        /// </summary>
        public const int OpenRecycler = 1860298748;
		/// <summary>
        /// 该道具无法被分解。
        /// </summary>
        public const int ItemCantRecycle = 1043939078;
		/// <summary>
        /// 该道具无法通过分解获得。
        /// </summary>
        public const int CantGetByRecycle = 142684066;
		/// <summary>
        /// 确定
        /// </summary>
        public const int Confirm = 1381094159;
		/// <summary>
        /// 取消
        /// </summary>
        public const int Cancel = 1421253321;
		/// <summary>
        /// 确定
        /// </summary>
        public const int Yes = 1991366089;
		/// <summary>
        /// 取消
        /// </summary>
        public const int No = 1560502734;
		/// <summary>
        /// 确定
        /// </summary>
        public const int OK = 21370788;
		/// <summary>
        /// 清空并替换
        /// </summary>
        public const int ClearAndReplace = 1015322185;
		/// <summary>
        /// 手工制作
        /// </summary>
        public const int WorkBenchNoNeed = 267176636;
		/// <summary>
        /// 一级工作台
        /// </summary>
        public const int WorkBenchLv1Need = 424608332;
		/// <summary>
        /// 二级工作台
        /// </summary>
        public const int WorkBenchLv2Need = 1467859520;
		/// <summary>
        /// 三级工作台
        /// </summary>
        public const int WorkBenchLv3Need = 547433182;
		/// <summary>
        /// 放入一个盛水容器
        /// </summary>
        public const int SelectWaterContainer = 2099344852;
		/// <summary>
        /// {0}% 支撑力
        /// </summary>
        public const int BuildStable = 1036144535;
		/// <summary>
        /// 解锁
        /// </summary>
        public const int Unlock = 1085540240;
		/// <summary>
        /// 解锁全部
        /// </summary>
        public const int UnlockAll = 1677188108;
		/// <summary>
        /// 你没有工具柜权限，无法进行升级。需要工具柜权限。
        /// </summary>
        public const int NoRightToUpdate = 665354001;
		/// <summary>
        /// 你没有工具柜权限,无法进行拆除。需要工具柜权限。
        /// </summary>
        public const int NoRightToRemove = 2082855907;
		/// <summary>
        /// 你没有工具柜权限，无法进行维修。需要工具柜权限。
        /// </summary>
        public const int NoRightToFix = 591646161;
		/// <summary>
        /// 你没有工具柜权限，无法进行回收。需要工具柜权限。
        /// </summary>
        public const int NoRightToRecycle = 855021187;
		/// <summary>
        /// 你没有权限, 需要工具柜权限。
        /// </summary>
        public const int NoRight = 2130432412;
		/// <summary>
        /// 无法回收:你只能在建造后{0:N0}秒内进行回收。
        /// </summary>
        public const int BuildCantOpCDTip1 = 1389226307;
		/// <summary>
        /// 无法升级:你只能在建造后{0:N0}秒内进行升级。
        /// </summary>
        public const int BuildCantOpCDTip2 = 1951803005;
		/// <summary>
        /// 无法拆除:你只能在建造后{0:N0}秒内进行拆除。
        /// </summary>
        public const int BuildCantOpCDTip3 = 2105000206;
		/// <summary>
        /// 无法修理:你只能在建造后{0:N0}秒内进行修理。
        /// </summary>
        public const int BuildCantOpCDTip5 = 106884982;
		/// <summary>
        /// 无法修理:最近受损，你只能在{0:N0}秒后进行修理。
        /// </summary>
        public const int BuildDamageCantOpTip1 = 114425233;
		/// <summary>
        /// 无法回收:最近受损，你只能在{0:N0}秒后进行回收。
        /// </summary>
        public const int BuildDamageCantOpTip2 = 223169856;
		/// <summary>
        /// 无法拆除:最近受损，你只能在{0:N0}秒后进行拆除。
        /// </summary>
        public const int BuildDamageCantOpTip4 = 1159036085;
		/// <summary>
        /// 无法旋转:最近受损，你只能在{0:N0}秒后进行旋转。
        /// </summary>
        public const int BuildDamageCantOpTip5 = 697316144;
		/// <summary>
        /// 正在绘制地形地貌…
        /// </summary>
        public const int LoadingScene = 2068850943;
		/// <summary>
        /// 正在搭建领地建筑…
        /// </summary>
        public const int WaitForBuidRecover = 212172717;
		/// <summary>
        /// 正在部署树木矿石…
        /// </summary>
        public const int WaitForStreamLoadFinish = 978073293;
		/// <summary>
        /// 开始部署树木矿石…
        /// </summary>
        public const int WaitForStreamLoad = 614879921;
		/// <summary>
        /// 此战局正在维护中，暂不可用，请尝试其他战局。
        /// </summary>
        public const int ServerNotUsable = 1347183886;
		/// <summary>
        /// 正在同步动态信息…
        /// </summary>
        public const int WaitForSnapshot = 842973435;
		/// <summary>
        /// 用户{0}登录中
        /// </summary>
        public const int UserInLogin = 115553351;
		/// <summary>
        /// 等待战局数据同步
        /// </summary>
        public const int WaitGameSync = 451909534;
		/// <summary>
        /// 战局连接断开，请重新进入游戏
        /// </summary>
        public const int DisconnectExitTips = 2073155871;
		/// <summary>
        /// 与战局断开连接，是否重试?
        /// </summary>
        public const int ServerDisconnect = 1096251895;
		/// <summary>
        /// 尝试重连服务器...{0}/{1}
        /// </summary>
        public const int ReconnectGame = 549490061;
		/// <summary>
        /// 官方
        /// </summary>
        public const int SvrGroupOfficial = 735754653;
		/// <summary>
        /// trunk
        /// </summary>
        public const int SvrGroupPublic = 1623215723;
		/// <summary>
        /// performance
        /// </summary>
        public const int SvrGroupOutnet = 1927012983;
		/// <summary>
        /// 策划
        /// </summary>
        public const int SvrGroupDesign = 792852214;
		/// <summary>
        /// 程序
        /// </summary>
        public const int SvrGroupProgrammer = 650464349;
		/// <summary>
        /// 本地
        /// </summary>
        public const int SvrGroupLocal = 328587553;
		/// <summary>
        /// 地图验证
        /// </summary>
        public const int SvrGroupMap = 1219197451;
		/// <summary>
        /// 炸家
        /// </summary>
        public const int SvrGroupBomb = 1066607797;
		/// <summary>
        /// 是否离开当前队伍
        /// </summary>
        public const int TeamLeave = 482475198;
		/// <summary>
        /// 是否将{0}踢出队伍
        /// </summary>
        public const int TeamKickout = 407566930;
		/// <summary>
        /// 你确定要把队长权限让给队员吗？（一旦转移，无法撤回！）
        /// </summary>
        public const int TeamLeaderChange = 1864793932;
		/// <summary>
        /// 给与权限后，该玩家可向其他玩家发送组队邀请，同时可以请离普通队员，是否给与权限？
        /// </summary>
        public const int TeamRightChange = 458090632;
		/// <summary>
        /// 是否移除该队友的权限?
        /// </summary>
        public const int TeamRightRemove = 1014477421;
		/// <summary>
        /// 与服务器断开连接, 点击重试或返回登录界面
        /// </summary>
        public const int IfReconnect = 1122184351;
		/// <summary>
        /// 尝试重连
        /// </summary>
        public const int TryToReconnect = 10321205;
		/// <summary>
        /// 返回大厅
        /// </summary>
        public const int BackToLobby = 1034618756;
		/// <summary>
        /// 赠送成功
        /// </summary>
        public const int BedSuccessGive = 1503610359;
		/// <summary>
        /// {0}赠送了你新的床位
        /// </summary>
        public const int BedSuccessGet = 1560589016;
		/// <summary>
        /// 床铺命名
        /// </summary>
        public const int BedRename = 1165410858;
		/// <summary>
        /// 赠送
        /// </summary>
        public const int Give = 1939977617;
		/// <summary>
        /// 是否确认赠送？
        /// </summary>
        public const int BedGiveConfirm = 1833542167;
		/// <summary>
        /// 开始清理游戏世界
        /// </summary>
        public const int StartCleanGameData = 328141494;
		/// <summary>
        /// 客户端的{0}与服务器不匹配，此客户端被禁止登录
        /// </summary>
        public const int SvrNotMatchReason = 1991816901;
		/// <summary>
        /// 服务器版本不匹配
        /// </summary>
        public const int SvrNotMatchTips = 976665568;
		/// <summary>
        /// 服务器登录失败: {0}
        /// </summary>
        public const int SvrFailLogin = 691953480;
		/// <summary>
        /// 组队
        /// </summary>
        public const int DanceWave = 2028050233;
		/// <summary>
        /// 检视
        /// </summary>
        public const int DanceCheck = 2031525485;
		/// <summary>
        /// 研究花费
        /// </summary>
        public const int ResearchCost = 1611624415;
		/// <summary>
        /// 无敌
        /// </summary>
        public const int HUDBloodNoDamage = 60363542;
		/// <summary>
        /// 重连大厅中......{0}
        /// </summary>
        public const int InReconnect = 633781420;
		/// <summary>
        /// 立即制作
        /// </summary>
        public const int StartToMake = 997355689;
		/// <summary>
        /// 互斥部位：
        /// </summary>
        public const int ExclusivePart = 866697118;
		/// <summary>
        /// 全身
        /// </summary>
        public const int ExclusiveFull = 1279210360;
		/// <summary>
        /// 头盔
        /// </summary>
        public const int ExclusiveHelmet = 1363282401;
		/// <summary>
        /// 眼镜
        /// </summary>
        public const int ExclusiveGlasses = 1741901296;
		/// <summary>
        /// 下巴
        /// </summary>
        public const int ExclusiveChin = 877561999;
		/// <summary>
        /// 上身护甲
        /// </summary>
        public const int ExclusiveUpArmor = 1318344096;
		/// <summary>
        /// 上身内衬
        /// </summary>
        public const int ExclusiveUpLining = 1953481193;
		/// <summary>
        /// 手套
        /// </summary>
        public const int ExclusiveGlove = 96065882;
		/// <summary>
        /// 裤子
        /// </summary>
        public const int ExclusiveTrousers = 1673200380;
		/// <summary>
        /// 下身护甲
        /// </summary>
        public const int ExclusiveLowerArmor = 1857444189;
		/// <summary>
        /// 鞋子
        /// </summary>
        public const int ExclusiveShoe = 1697064310;
		/// <summary>
        /// 保护
        /// </summary>
        public const int EquipProtect = 1404748656;
		/// <summary>
        /// 全身
        /// </summary>
        public const int EquipFull = 1483041933;
		/// <summary>
        /// 头部
        /// </summary>
        public const int EquipHead = 270748985;
		/// <summary>
        /// 上半身
        /// </summary>
        public const int EquipUpper = 1189137339;
		/// <summary>
        /// 下半身
        /// </summary>
        public const int EquipLower = 2080521043;
		/// <summary>
        /// 背包已满, 卸下的配件将会丢弃, 是否继续？
        /// </summary>
        public const int BackpackFull = 791728147;
		/// <summary>
        /// 把物品拖动到这里进行修复。
        /// </summary>
        public const int FixTips1 = 1648523207;
		/// <summary>
        /// 此物品无法修理。
        /// </summary>
        public const int FixTips2 = 1961755139;
		/// <summary>
        /// 学会制作该物品后才能进行修理。
        /// </summary>
        public const int FixTips3 = 2002237588;
		/// <summary>
        /// 此物品无需修理。
        /// </summary>
        public const int FixTips5 = 1523554122;
		/// <summary>
        /// 暂无消息
        /// </summary>
        public const int ChatNoMsg = 1445472251;
		/// <summary>
        /// 点击输入
        /// </summary>
        public const int ChatClickToInput = 405995391;
		/// <summary>
        /// 发送
        /// </summary>
        public const int ChatSend = 1268562713;
		/// <summary>
        /// 未选择频道
        /// </summary>
        public const int ChatNoChannel = 1387319046;
		/// <summary>
        /// 没有更多消息了
        /// </summary>
        public const int ChatNoMoreMsg = 2026182531;
		/// <summary>
        /// {0}条未读消息
        /// </summary>
        public const int ChatUnreadMsg = 825237511;
		/// <summary>
        /// {0}条新消息
        /// </summary>
        public const int ChatNewMsg = 1384259493;
		/// <summary>
        /// 昨天
        /// </summary>
        public const int ChatYesterdayTime = 9869673;
		/// <summary>
        /// 星期一
        /// </summary>
        public const int ChatDayTime1 = 1759270027;
		/// <summary>
        /// 星期二
        /// </summary>
        public const int ChatDayTime2 = 563619385;
		/// <summary>
        /// 星期三
        /// </summary>
        public const int ChatDayTime3 = 1966335972;
		/// <summary>
        /// 星期四
        /// </summary>
        public const int ChatDayTime4 = 1725624151;
		/// <summary>
        /// 星期五
        /// </summary>
        public const int ChatDayTime5 = 648175726;
		/// <summary>
        /// 星期六
        /// </summary>
        public const int ChatDayTime6 = 606487871;
		/// <summary>
        /// 星期日
        /// </summary>
        public const int ChatDayTime7 = 1056189893;
		/// <summary>
        /// 常用
        /// </summary>
        public const int BpTypeCommon = 1402790459;
		/// <summary>
        /// 收藏
        /// </summary>
        public const int BpTypeFavor = 664866907;
		/// <summary>
        /// 需要{0}级工作台
        /// </summary>
        public const int NeedWorkBenchLevel = 458884972;
		/// <summary>
        /// 将按钮方案同步到该玩法，核心按钮包括：
        /// </summary>
        public const int SettingSyncPop = 1859343892;
		/// <summary>
        /// 自定义同步
        /// </summary>
        public const int SettingSyncTitle = 1408470667;
		/// <summary>
        /// 加入上一次游玩的服务器{0}？
        /// </summary>
        public const int QuickJoinConfirm = 959526206;
		/// <summary>
        /// 右侧攻击按钮
        /// </summary>
        public const int SettingBtnRAttack = 315309534;
		/// <summary>
        /// 左侧攻击按钮
        /// </summary>
        public const int SettingBtnLAttack = 944149131;
		/// <summary>
        /// 移动控制按钮
        /// </summary>
        public const int SettingBtnMove = 805761050;
		/// <summary>
        /// 跳跃按钮
        /// </summary>
        public const int SettingBtnJump = 1119115416;
		/// <summary>
        /// 开镜按钮
        /// </summary>
        public const int SettingBtnMirror = 860083762;
		/// <summary>
        /// 下蹲按钮
        /// </summary>
        public const int SettingBtnCrouch = 288239305;
		/// <summary>
        /// 换弹按钮
        /// </summary>
        public const int SettingBtnReload = 1232944981;
		/// <summary>
        /// 快捷栏按钮
        /// </summary>
        public const int SettingBtnShortcuts = 33260401;
		/// <summary>
        /// 背包按钮
        /// </summary>
        public const int SettingBtnBackpack = 1788357987;
		/// <summary>
        /// 当前配置未保存，是否切换并保存？
        /// </summary>
        public const int HUDSwitchTips = 1035042266;
		/// <summary>
        /// 直接切换
        /// </summary>
        public const int HudDirectlySwitch = 1446970876;
		/// <summary>
        /// 保存并切换
        /// </summary>
        public const int HudSaveSwitch = 118447964;
		/// <summary>
        /// 当前配置未保存，是否保存后退出？
        /// </summary>
        public const int HudSaveTips = 504102451;
		/// <summary>
        /// 直接退出
        /// </summary>
        public const int HudDirectlyExit = 619218634;
		/// <summary>
        /// 保存并退出
        /// </summary>
        public const int HudSaveExit = 943108606;
		/// <summary>
        /// 身负重伤
        /// </summary>
        public const int SeriouslyInjured = 1830496673;
		/// <summary>
        /// 如果您没有及时救助，您将被淘汰或有{0}的机会原地重生
        /// </summary>
        public const int SeriouslyInjuredTips = 1444629651;
		/// <summary>
        /// 自救
        /// </summary>
        public const int RescueSelf = 73797882;
		/// <summary>
        /// 没有医疗包用于自救
        /// </summary>
        public const int NoMedicineToRescue = 1530559594;
		/// <summary>
        /// 垂落伤无法自救
        /// </summary>
        public const int FallDamageCantRescue = 952176386;
		/// <summary>
        /// 您的背包中没有足够的医疗包用于自救
        /// </summary>
        public const int NoMedicineToRescueTips = 1508155663;
		/// <summary>
        /// 垂落受伤时无法使用医疗包进行自救
        /// </summary>
        public const int FallDamageCantRescueTips = 1029053930;
		/// <summary>
        /// 没有医疗包用于治疗
        /// </summary>
        public const int NoAidBox = 2020785090;
		/// <summary>
        /// 治疗中
        /// </summary>
        public const int InEmergency = 1777811643;
		/// <summary>
        /// 救援中
        /// </summary>
        public const int InRescue = 1794432833;
		/// <summary>
        /// 救援
        /// </summary>
        public const int RescueOthers = 1656598115;
		/// <summary>
        /// 电力
        /// </summary>
        public const int HudElect = 811247533;
		/// <summary>
        /// 建造
        /// </summary>
        public const int HudConstruction = 1282802725;
		/// <summary>
        /// 功率      电力端口
        /// </summary>
        public const int ElecInterface = 1221359110;
		/// <summary>
        /// 已经升到当前等级
        /// </summary>
        public const int BuildUpdateLevelComplete = 304014840;
		/// <summary>
        /// 头部防御属性
        /// </summary>
        public const int ProtectPropHead = 234135434;
		/// <summary>
        /// 上身防御属性
        /// </summary>
        public const int ProtectPropUpper = 2036802711;
		/// <summary>
        /// 下身防御属性
        /// </summary>
        public const int ProtectPropLower = 1184802917;
		/// <summary>
        /// 弹药防御
        /// </summary>
        public const int AttrProtectFromGun = 1844113282;
		/// <summary>
        /// 近战防御
        /// </summary>
        public const int AttrProtectFromMelee = 593881921;
		/// <summary>
        /// 抵御爆炸
        /// </summary>
        public const int AttrProtectFromExplosion = 1906128470;
		/// <summary>
        /// 抵御寒冷
        /// </summary>
        public const int AttrProtectFromCold = 601369171;
		/// <summary>
        /// 抵御辐射
        /// </summary>
        public const int AttrProtectFromRadiotion = 441325921;
		/// <summary>
        /// 啮咬防御
        /// </summary>
        public const int AttrProtectFromAnimal = 650676316;
		/// <summary>
        /// 全身防御属性
        /// </summary>
        public const int AttrWholeAttr = 1041117326;
		/// <summary>
        /// 拆分
        /// </summary>
        public const int ItemUseSplit = 1544221321;
		/// <summary>
        /// 拆入背包
        /// </summary>
        public const int ItemUseSplitToBackpack = 764333407;
		/// <summary>
        /// 拆入其他容器
        /// </summary>
        public const int ItemUseSplitToOther = 31759517;
		/// <summary>
        /// 部分丢弃
        /// </summary>
        public const int ItemUsePartDrop = 259319878;
		/// <summary>
        /// 移至{0}
        /// </summary>
        public const int ItemUseMove = 1123211208;
		/// <summary>
        /// 倒水
        /// </summary>
        public const int ItemUseDropWater = 1604778312;
		/// <summary>
        /// 食用
        /// </summary>
        public const int ItemUseEat = 1913835067;
		/// <summary>
        /// 饮用
        /// </summary>
        public const int ItemUseDrink = 1136962348;
		/// <summary>
        /// 学习蓝图
        /// </summary>
        public const int ItemUseLearnBp = 1523550083;
		/// <summary>
        /// 医疗
        /// </summary>
        public const int ItemUseTreat = 660883059;
		/// <summary>
        /// 装备
        /// </summary>
        public const int ItemUseEquip = 46056450;
		/// <summary>
        /// 使用
        /// </summary>
        public const int ItemUse = 1379841750;
		/// <summary>
        /// 取出子弹
        /// </summary>
        public const int ItemUseUnloadAmmo = 1668275154;
		/// <summary>
        /// 卸下配件
        /// </summary>
        public const int ItemUseUnloadPart = 591468972;
		/// <summary>
        /// 卸下装备
        /// </summary>
        public const int ItemUseTakeOff = 1992349891;
		/// <summary>
        /// 丢弃
        /// </summary>
        public const int ItemUseDrop = 2034077118;
		/// <summary>
        /// 移至背包
        /// </summary>
        public const int ItemUseMoveToInventory = 2018017595;
		/// <summary>
        /// 移至其他容器
        /// </summary>
        public const int ItemUseMoveToBox = 1177101474;
		/// <summary>
        /// 充能
        /// </summary>
        public const int ItemUseRecharge = 1353301433;
		/// <summary>
        /// CE测试专用服务器，请遵循现场人员指引进行游戏
        /// </summary>
        public const int SvrDescTest = 965907683;
		/// <summary>
        /// Fail to deserialize the server info, serverId = {0}, error = {1}
        /// </summary>
        public const int SvrParseError = 283786387;
		/// <summary>
        /// 与原昵称相同
        /// </summary>
        public const int NameCardNoModify = 1631255518;
		/// <summary>
        /// 名称不能为空。
        /// </summary>
        public const int NameCardEmpty = 608754413;
		/// <summary>
        /// 输入的昵称过短
        /// </summary>
        public const int NameCardRuler = 1780629205;
		/// <summary>
        /// 返回大厅
        /// </summary>
        public const int SettingBackToLobby = 1176378315;
		/// <summary>
        /// 退出登录
        /// </summary>
        public const int SettingLogout = 40111937;
		/// <summary>
        /// 请根据个人习惯调整
        /// </summary>
        public const int SettingAdjustTips = 247190489;
		/// <summary>
        /// 人称功能设置
        /// </summary>
        public const int SettingGameView = 1936848927;
		/// <summary>
        /// 是否暂离战局，返回大厅？（请在领地建筑内或使用野营帐篷安全下线）
        /// </summary>
        public const int SettingBackToLobbyAsk = 2113231245;
		/// <summary>
        /// 是否退出登录？
        /// </summary>
        public const int SettingLogoutAsk = 499341893;
		/// <summary>
        /// 通用
        /// </summary>
        public const int SettingTabCommon = 394696335;
		/// <summary>
        /// 道具
        /// </summary>
        public const int SettingTabTool = 642979253;
		/// <summary>
        /// 战斗
        /// </summary>
        public const int SettingTabBattle = 1714426743;
		/// <summary>
        /// 关卡
        /// </summary>
        public const int SettingTabLevel = 520174463;
		/// <summary>
        /// 建造
        /// </summary>
        public const int SettingTabBuild = 469836271;
		/// <summary>
        /// 角色
        /// </summary>
        public const int SettingRole = 1575632911;
		/// <summary>
        /// 场景
        /// </summary>
        public const int SettingScene = 369291255;
		/// <summary>
        /// 更改属性
        /// </summary>
        public const int SettingChangeProp = 477759347;
		/// <summary>
        /// 服务器
        /// </summary>
        public const int SettingSvr = 1532503655;
		/// <summary>
        /// 任务
        /// </summary>
        public const int SettingTask = 1178041514;
		/// <summary>
        /// 容器修改
        /// </summary>
        public const int SettingContainer = 1597772197;
		/// <summary>
        /// 配置道具
        /// </summary>
        public const int SettingItemConfig = 551160813;
		/// <summary>
        /// 出生道具
        /// </summary>
        public const int SettingItemReborn = 1715752929;
		/// <summary>
        /// 灵敏度
        /// </summary>
        public const int SettingSensitive = 128598865;
		/// <summary>
        /// 镜头
        /// </summary>
        public const int SettingCamera = 1483827943;
		/// <summary>
        /// 枪械
        /// </summary>
        public const int SettingWeapon = 1156513149;
		/// <summary>
        /// 渲染
        /// </summary>
        public const int SettingShader = 1569268141;
		/// <summary>
        /// 操作
        /// </summary>
        public const int SettingOp = 1246183336;
		/// <summary>
        /// 辅助瞄准
        /// </summary>
        public const int SettingAimAssist = 594669862;
		/// <summary>
        /// 日志打印
        /// </summary>
        public const int SettingLog = 1035737673;
		/// <summary>
        /// 空投
        /// </summary>
        public const int SettingAirDrop = 42046323;
		/// <summary>
        /// 怪物
        /// </summary>
        public const int SettingMonster = 1308730599;
		/// <summary>
        /// 光照
        /// </summary>
        public const int SettingLight = 15492831;
		/// <summary>
        /// 基础设定
        /// </summary>
        public const int SettingBasic = 1169409311;
		/// <summary>
        /// 救援中
        /// </summary>
        public const int TeamInAiding = 1783169605;
		/// <summary>
        /// 被淘汰
        /// </summary>
        public const int TeamIsDied = 1144852822;
		/// <summary>
        /// 倒地
        /// </summary>
        public const int TeamIsWounded = 90545531;
		/// <summary>
        /// 在线
        /// </summary>
        public const int TeamIsLive = 2071276723;
		/// <summary>
        /// 离线
        /// </summary>
        public const int TeamIsOffline = 1861556653;
		/// <summary>
        /// 发送消息
        /// </summary>
        public const int TeamSendMsg = 1194857668;
		/// <summary>
        /// 转移队长
        /// </summary>
        public const int TeamChangeLeader = 1512294654;
		/// <summary>
        /// 踢出队友
        /// </summary>
        public const int TeamKickoutMate = 287669689;
		/// <summary>
        /// 收回权限
        /// </summary>
        public const int TeamFeachBackRight = 315357108;
		/// <summary>
        /// 给与权限
        /// </summary>
        public const int TeamGiveOutRight = 1634253723;
		/// <summary>
        /// 自由镜头灵敏度
        /// </summary>
        public const int SettingFreeCameraSenstive = 189576841;
		/// <summary>
        /// 镜头灵敏度（用于不开火情况下，滑动屏幕时镜头转动的灵敏度）
        /// </summary>
        public const int SettingCameraSenstive = 1419255586;
		/// <summary>
        /// 开火镜头灵敏度（用于开火情况下，划动屏幕时镜头转动的灵敏度，可用于压枪操作）
        /// </summary>
        public const int SettingFireCameraSenstive = 763437900;
		/// <summary>
        /// 陀螺仪灵敏度（用于陀螺仪开启时，通过调整手机姿态来转动镜头的灵敏度）
        /// </summary>
        public const int SettingGyroSensitive = 231919598;
		/// <summary>
        /// 开火陀螺仪灵敏度（用于陀螺仪开启时，通过调整手机姿态来转动开火时镜头的灵敏度）
        /// </summary>
        public const int SettingFireGyroSensitive = 154641608;
		/// <summary>
        /// 请输入玩家昵称或编号
        /// </summary>
        public const int TeamInputSearch = 1832909283;
		/// <summary>
        /// 本服
        /// </summary>
        public const int TeamCurSvr = 257080430;
		/// <summary>
        /// {0} 秒
        /// </summary>
        public const int TeamCDTime = 1776777369;
		/// <summary>
        /// 刷新
        /// </summary>
        public const int TeamRefresh = 850812936;
		/// <summary>
        /// 离线抄家 - 【{0}】 \n 【{1}】
        /// </summary>
        public const int GameModeReadyTitle = 750393854;
		/// <summary>
        /// 是否要退出服务器？
        /// </summary>
        public const int GameModeExitConfirm = 1944301756;
		/// <summary>
        /// 队长
        /// </summary>
        public const int GameModeLeaderLabel = 907983783;
		/// <summary>
        /// 确认
        /// </summary>
        public const int GameModeSelectConfirm = 1136888170;
		/// <summary>
        /// 取消
        /// </summary>
        public const int GameModeSelectCancel = 1618854267;
		/// <summary>
        /// 电线颜色
        /// </summary>
        public const int ElecCableColor = 1131561660;
		/// <summary>
        /// 使用中
        /// </summary>
        public const int ElecInUse = 491431245;
		/// <summary>
        /// 使用
        /// </summary>
        public const int ElecUse = 36286231;
		/// <summary>
        /// 玩家已经是队友
        /// </summary>
        public const int ChatPlayerAlreadyInTeam = 1577453926;
		/// <summary>
        /// 发送消息
        /// </summary>
        public const int ChatSendMsg = 1805240599;
		/// <summary>
        /// 邀请入队
        /// </summary>
        public const int CharInvite = 756806397;
		/// <summary>
        /// 退出
        /// </summary>
        public const int GameModeCampExit = 557764141;
		/// <summary>
        /// 观战
        /// </summary>
        public const int GameModeCampWatch = 1581456616;
		/// <summary>
        /// 观战确认
        /// </summary>
        public const int GameModeCampWatchConfirm = 1043569995;
		/// <summary>
        /// 退出确认
        /// </summary>
        public const int GameModeCampExitConfirm = 22585065;
		/// <summary>
        /// 防守
        /// </summary>
        public const int GameModeCampDefend = 386829696;
		/// <summary>
        /// 进攻
        /// </summary>
        public const int GameModeCampAttack = 1647774285;
		/// <summary>
        /// 选择阵营
        /// </summary>
        public const int GameModeCampSelect = 1858667778;
		/// <summary>
        /// 加入失败，当前选择阵营人数已达上限
        /// </summary>
        public const int GameModeCampFull = 1981155530;
		/// <summary>
        /// 加入失败，所选阵营与当前阵营一致
        /// </summary>
        public const int GameModeCampSame = 578477748;
		/// <summary>
        /// 加入失败，此阵营不存在
        /// </summary>
        public const int GameModeCampError = 1752550987;
		/// <summary>
        /// 加入失败，与玩法服务器已断开链接
        /// </summary>
        public const int GameModeNotInGame = 1806080102;
		/// <summary>
        /// 加入失败，当前阶段不允许切换阵营
        /// </summary>
        public const int GameModeStageBan = 1145245861;
		/// <summary>
        /// 正在搭建防御体系，请稍候
        /// </summary>
        public const int GameModeWait1 = 14942777;
		/// <summary>
        /// 宝箱内物资清单更新中
        /// </summary>
        public const int GameModeWait2 = 361179280;
		/// <summary>
        /// 正在更新领地权限设置
        /// </summary>
        public const int GameModeWait3 = 507643819;
		/// <summary>
        /// 仓库
        /// </summary>
        public const int NameBackpack = 529095024;
		/// <summary>
        /// 未拥有
        /// </summary>
        public const int CollectorNotHave = 995130244;
		/// <summary>
        /// 不可转移水
        /// </summary>
        public const int CollectorCantTransWater = 1070004283;
		/// <summary>
        /// 已充满
        /// </summary>
        public const int ElecFull = 2099792464;
		/// <summary>
        /// 确认退出布线吗？ 会清除此条电缆所有的已部署的电路走线
        /// </summary>
        public const int ElecExitCable = 2041395560;
		/// <summary>
        /// 修理物
        /// </summary>
        public const int NameFixItem = 346273737;
		/// <summary>
        /// 修理所需材料不足。
        /// </summary>
        public const int FixMaterialCheck = 1488090946;
		/// <summary>
        /// 选择一个水瓶或塑料水桶，然后用它来倒入或灌取露水收集器中的水
        /// </summary>
        public const int CollectorNoBottle = 737848214;
		/// <summary>
        /// 装有盐水的容器无法倒入或灌取水。请先倒空盐水或选择盛有淡水的容器。
        /// </summary>
        public const int CollectorNotSameWaterType = 542014612;
		/// <summary>
        /// 当前容器里已装满水，无法取水。
        /// </summary>
        public const int CollectorTakeFullBottle = 629173996;
		/// <summary>
        /// 露水收集器里没有水，无法灌取水。
        /// </summary>
        public const int CollectorTakeEmptyCatcher = 562360396;
		/// <summary>
        /// 露水收集器里已装满水，无法注入水。
        /// </summary>
        public const int CollectorGiveFullCatcher = 1864589572;
		/// <summary>
        /// 当前容器里没有水，无法注入水。
        /// </summary>
        public const int CollectorGiveEmptyBottle = 706605596;
		/// <summary>
        /// 操作设置
        /// </summary>
        public const int SettingOpConfig = 167236329;
		/// <summary>
        /// 填写参数
        /// </summary>
        public const int SettingFillParams = 2124848559;
		/// <summary>
        /// 背包中数量 {0}
        /// </summary>
        public const int BackpackItemNum = 1356042319;
		/// <summary>
        /// 你正在睡觉
        /// </summary>
        public const int SleepTips = 2143165034;
		/// <summary>
        /// (按任意键唤醒)
        /// </summary>
        public const int PCSleepTips = 1218560666;
		/// <summary>
        /// (点击任意位置唤醒)
        /// </summary>
        public const int MobileSleepTips = 1613537129;
		/// <summary>
        /// 是否拒绝全部组队邀请？
        /// </summary>
        public const int IsRefuseAll = 328882;
		/// <summary>
        /// {0}邀请您加入他的队伍
        /// </summary>
        public const int HudInvitePanel = 206255212;
		/// <summary>
        /// 正在刷新领地建筑等级…
        /// </summary>
        public const int BuildLevelAssetPreLoad = 16992443;
		/// <summary>
        /// 正在恢复领地设施状态…
        /// </summary>
        public const int BuildAssetPreLoad = 877217470;
		/// <summary>
        /// 正在加载角色基础信息…
        /// </summary>
        public const int LoadAlwaysInMemAB = 450346999;
		/// <summary>
        /// 正在卸载角色基础信息…
        /// </summary>
        public const int UnloadAlwaysInMemAB = 943687398;
		/// <summary>
        /// 正在从云端获取新地图…
        /// </summary>
        public const int AssetDownloadScene = 1962179533;
		/// <summary>
        /// 正在从本地载入地形图…
        /// </summary>
        public const int AssetLoadLocalScene = 1285843915;
		/// <summary>
        /// 正在获取补充内容清单…
        /// </summary>
        public const int AssetLoadAB = 1573223052;
		/// <summary>
        /// 正在从清单中载入地图…
        /// </summary>
        public const int AssetLoadSceneFromAB = 1635812185;
		/// <summary>
        /// 正在构建岛屿遗迹信息…
        /// </summary>
        public const int AssetLoadDesignScene = 58827366;
		/// <summary>
        /// 正在抹除地形信息…
        /// </summary>
        public const int AssetStartUnloadScene = 845377137;
		/// <summary>
        /// 正在启动…
        /// </summary>
        public const int AssetLoadPreloadAssets = 385334005;
		/// <summary>
        /// 正在加载地图：{0}
        /// </summary>
        public const int AssetLoadScene = 741900064;
		/// <summary>
        /// 加载补充地图内容失败：
        /// </summary>
        public const int AssetFailToLoadSceneAB = 27306930;
		/// <summary>
        /// 加载地图失败
        /// </summary>
        public const int AssetFailToLoadScene = 812998782;
		/// <summary>
        /// 正在从云端获取地图信息，速度：{0}
        /// </summary>
        public const int AssetDownloadSceneAssets = 1491784236;
		/// <summary>
        /// 下载资源包失败。
        /// </summary>
        public const int AssetFailToDownloadAssets = 393929591;
		/// <summary>
        /// 正在拼装摆件设施…
        /// </summary>
        public const int AssetBrokenAssetPreload = 1537044883;
		/// <summary>
        /// 正在准备快捷栏位…
        /// </summary>
        public const int AssetShortcutsAssetPreload = 1606555708;
		/// <summary>
        /// 正在唤醒遗迹守卫…
        /// </summary>
        public const int AssetMonsterAssetPreload = 1249391724;
		/// <summary>
        /// 正在装填背包道具…
        /// </summary>
        public const int AssetCommonAssetPreload = 1114004001;
		/// <summary>
        /// 正在绘制完整地图…
        /// </summary>
        public const int AssetMapAssetPreload = 1579745543;
		/// <summary>
        /// 正在建立岛外通讯…
        /// </summary>
        public const int AssetGoAssetPreload = 1749016425;
		/// <summary>
        /// 正在提前载入资源…
        /// </summary>
        public const int AssetAssetPreload = 1941025600;
		/// <summary>
        /// 设施
        /// </summary>
        public const int DetailFacility = 1879830965;
		/// <summary>
        /// 制作方式
        /// </summary>
        public const int DetailMakeBp = 1091465357;
		/// <summary>
        /// 查看
        /// </summary>
        public const int DetailSee = 1967264899;
		/// <summary>
        /// 蓝图解锁消耗
        /// </summary>
        public const int DetailBpUnlockCost = 375303449;
		/// <summary>
        /// 逆向研究解锁
        /// </summary>
        public const int DetailResearchReq = 701696997;
		/// <summary>
        /// 分解可得
        /// </summary>
        public const int DetailRecycledFor = 1189326091;
		/// <summary>
        /// 可通过分解以下物品获得
        /// </summary>
        public const int DetailRecycledFrom = 1919978088;
		/// <summary>
        /// 信息
        /// </summary>
        public const int DetailDropSource = 511800572;
		/// <summary>
        /// 地点
        /// </summary>
        public const int DetailTradePos = 129698950;
		/// <summary>
        /// 交易价格
        /// </summary>
        public const int DetailTradePrice = 126129210;
		/// <summary>
        /// 可用于制作
        /// </summary>
        public const int DetailCanUseToCraft = 261013988;
		/// <summary>
        /// 基础设置
        /// </summary>
        public const int SettingMainBasic = 566632951;
		/// <summary>
        /// 操作设置
        /// </summary>
        public const int SettingMainOp = 1171526772;
		/// <summary>
        /// 灵敏度设置
        /// </summary>
        public const int SettingMainSensitive = 95883849;
		/// <summary>
        /// 声音设置
        /// </summary>
        public const int SettingMainSound = 778913746;
		/// <summary>
        /// Gm指令
        /// </summary>
        public const int SettingMainGM = 1943618913;
		/// <summary>
        /// Gm道具
        /// </summary>
        public const int SettingMainGMItem = 1716725912;
		/// <summary>
        /// Gm刷怪
        /// </summary>
        public const int SettingMainGMMonster = 1909477826;
		/// <summary>
        /// 程序指令
        /// </summary>
        public const int SettingMainGMCode = 2065700360;
		/// <summary>
        /// 队伍
        /// </summary>
        public const int TeamTeam = 1699034403;
		/// <summary>
        /// 好友
        /// </summary>
        public const int TeamFriends = 420012578;
		/// <summary>
        /// 军团
        /// </summary>
        public const int TeamArmy = 1613521068;
		/// <summary>
        /// 陀螺仪
        /// </summary>
        public const int SettingSecondHeadGyro = 1655982892;
		/// <summary>
        /// 辅助瞄准
        /// </summary>
        public const int SettingSecondAimAssist = 1872441929;
		/// <summary>
        /// 物品高亮
        /// </summary>
        public const int SettingSecondItemHighLight = 57940879;
		/// <summary>
        /// 电力自动布线
        /// </summary>
        public const int SettingSecondElecAutoSet = 1794201811;
		/// <summary>
        /// 蓝牙耳机效果增强
        /// </summary>
        public const int SettingSecondGM = 1300905583;
		/// <summary>
        /// 建筑物尺寸
        /// </summary>
        public const int SettingSecondBuildSize = 431297031;
		/// <summary>
        /// 游戏质量
        /// </summary>
        public const int SettingSecondGameQuality = 1116677618;
		/// <summary>
        /// 画面品质
        /// </summary>
        public const int SettingSecondGraphQuality = 438836276;
		/// <summary>
        /// 帧率设置
        /// </summary>
        public const int SettingSecondFrame = 677113240;
		/// <summary>
        /// 抗锯齿（立即生效，提高画面平滑，但会增加发热和耗电）
        /// </summary>
        public const int SettingSecondAntiAlias = 813383081;
		/// <summary>
        /// 阴影（关闭阴影可以降低性能损耗、发热和耗电）
        /// </summary>
        public const int SettingSecondShadow = 778176518;
		/// <summary>
        /// 角色无敌
        /// </summary>
        public const int SettingSecondPlayerGod = 1824866739;
		/// <summary>
        /// 切换视角
        /// </summary>
        public const int SettingSecondSwitchView = 433208087;
		/// <summary>
        /// 平滑移动
        /// </summary>
        public const int SettingSecondSmooth = 240716848;
		/// <summary>
        /// 开启加速
        /// </summary>
        public const int SettingSecondAcceleration = 636805110;
		/// <summary>
        /// 受击音效测试
        /// </summary>
        public const int SettingSecondHitSound = 1104409711;
		/// <summary>
        /// 延迟补偿
        /// </summary>
        public const int SettingSecondDelay = 1072389331;
		/// <summary>
        /// 打点传送
        /// </summary>
        public const int SettingSecondPointTrans = 615360850;
		/// <summary>
        /// 镜头抖动
        /// </summary>
        public const int SettingSecondViewkick = 551963283;
		/// <summary>
        /// 枪械抖动
        /// </summary>
        public const int SettingSecondGunkick = 1335359671;
		/// <summary>
        /// Sway
        /// </summary>
        public const int SettingSecondSway = 575157929;
		/// <summary>
        /// 移动轮盘
        /// </summary>
        public const int SettingSecondJoystick = 1789454880;
		/// <summary>
        /// 开火辅助框
        /// </summary>
        public const int SettingSecondFireAssistBox = 864781738;
		/// <summary>
        /// 目标周围减速框
        /// </summary>
        public const int SettingSecondTarReductionFrame = 807326643;
		/// <summary>
        /// 辅助瞄准工具
        /// </summary>
        public const int SettingSecondAmiAssistTool = 703628502;
		/// <summary>
        /// 使用辅瞄工具数据
        /// </summary>
        public const int SettingSecondUseAimAssistToolData = 124947113;
		/// <summary>
        /// 3cTestDebug
        /// </summary>
        public const int SettingSecond3CTest = 31479254;
		/// <summary>
        /// 伤害Log
        /// </summary>
        public const int SettingSecondDamageLog = 234211384;
		/// <summary>
        /// AI开关
        /// </summary>
        public const int SettingSecondAI = 263561376;
		/// <summary>
        /// 怪物无敌
        /// </summary>
        public const int SettingSecondMonsterInvincible = 2101390056;
		/// <summary>
        /// 显示点位信息
        /// </summary>
        public const int SettingSecondShowPointInfo = 1063941895;
		/// <summary>
        /// 昼夜变化
        /// </summary>
        public const int SettingSecondDayNight = 366778251;
		/// <summary>
        /// 死羊刷新
        /// </summary>
        public const int SettingSecondDeadSheepRefresh = 1391612147;
		/// <summary>
        /// 血条显示非队友角色名
        /// </summary>
        public const int SettingSecondShowOtherName = 89463206;
		/// <summary>
        /// 飞行模式
        /// </summary>
        public const int SettingSecondFlyMode = 313614033;
		/// <summary>
        /// 无限建造
        /// </summary>
        public const int SettingSecondUnlimitedBuild = 190785523;
		/// <summary>
        /// 观察者模式
        /// </summary>
        public const int SettingSecondObserverMode = 121559825;
		/// <summary>
        /// ResAb下载
        /// </summary>
        public const int SettingSecondAbDownload = 1949929461;
		/// <summary>
        /// 破碎性能切换
        /// </summary>
        public const int SettingSecondCrushingSwitch = 769703216;
		/// <summary>
        /// 拟建体碰撞吸附优化
        /// </summary>
        public const int SettingSecondPreBuildAdsorb = 1684390400;
		/// <summary>
        /// 选择出生道具
        /// </summary>
        public const int SettingSecondSelectBirthProp = 1004265742;
		/// <summary>
        /// 开
        /// </summary>
        public const int BtnOn = 966623665;
		/// <summary>
        /// 关
        /// </summary>
        public const int BtnOff = 269171963;
		/// <summary>
        /// 开
        /// </summary>
        public const int BtnAlwaysOn = 1282288092;
		/// <summary>
        /// 常规
        /// </summary>
        public const int BtnRoutine = 1285574099;
		/// <summary>
        /// 中等
        /// </summary>
        public const int BtnMedium = 1684624475;
		/// <summary>
        /// 较大
        /// </summary>
        public const int BtnLarger = 1819303866;
		/// <summary>
        /// 左手移动，右手固定开火
        /// </summary>
        public const int BtnFireLMFloatRFFix = 457332977;
		/// <summary>
        /// 左手移动，右手跟随开火
        /// </summary>
        public const int BtnFireLMFloatRFFloat = 936550288;
		/// <summary>
        /// 左手固定移动，右手固定开火
        /// </summary>
        public const int BtnFireLMFixRFFix = 1307642355;
		/// <summary>
        /// 极低
        /// </summary>
        public const int BtnVeryLowQuality = 1767336793;
		/// <summary>
        /// 低质量
        /// </summary>
        public const int BtnLowQuality = 1027205722;
		/// <summary>
        /// 标准
        /// </summary>
        public const int BtnStandardQuality = 1481442134;
		/// <summary>
        /// 高清
        /// </summary>
        public const int BtnHDQuality = 1088472549;
		/// <summary>
        /// 固定
        /// </summary>
        public const int BtnMoveFixed = 1664281569;
		/// <summary>
        /// 浮动
        /// </summary>
        public const int BtnMoveFloating = 325690961;
		/// <summary>
        /// 动态
        /// </summary>
        public const int BtnMoveDynamic = 503173469;
		/// <summary>
        /// 高
        /// </summary>
        public const int BtnMax = 1969462325;
		/// <summary>
        /// 中
        /// </summary>
        public const int BtnMed = 831238992;
		/// <summary>
        /// 低
        /// </summary>
        public const int BtnMin = 1887042867;
		/// <summary>
        /// 生成一颗树木
        /// </summary>
        public const int BtnSpawnATree = 2101331820;
		/// <summary>
        /// 生成一块矿石
        /// </summary>
        public const int BtnSpawnAOre = 1028015995;
		/// <summary>
        /// 生成可采集物
        /// </summary>
        public const int BtnSpawnCollectables = 141718559;
		/// <summary>
        /// 原地添加火堆
        /// </summary>
        public const int BtnAddFireInPlace = 146021336;
		/// <summary>
        /// 更改服务器时间
        /// </summary>
        public const int BtnChangeSvrTime = 1336643527;
		/// <summary>
        /// 音效测试
        /// </summary>
        public const int BtnSoundTest = 2142962303;
		/// <summary>
        /// 调整舒适度
        /// </summary>
        public const int BtnAdjustComfort = 1573783254;
		/// <summary>
        /// 修改生存属性
        /// </summary>
        public const int BtnModifyAttr = 430019983;
		/// <summary>
        /// 造成伤害
        /// </summary>
        public const int BtnCauseDamage = 289609633;
		/// <summary>
        /// 添加假玩家(只能1次)
        /// </summary>
        public const int BtnAddDummyPlayer = 1583639846;
		/// <summary>
        /// 删除所有假玩家
        /// </summary>
        public const int BtnDeleteDummyPlayer = 2064235066;
		/// <summary>
        /// 角度常数S
        /// </summary>
        public const int BtnAngularConstS = 1229189860;
		/// <summary>
        /// 灵敏度系数AX
        /// </summary>
        public const int BtnSenstiveFactorAX = 1909294489;
		/// <summary>
        /// 灵敏度系数AY
        /// </summary>
        public const int BtnSenstiveFactorAY = 93299817;
		/// <summary>
        /// 陀螺仪系数LX
        /// </summary>
        public const int BtnGyrosCoefficientLX = 1575068479;
		/// <summary>
        /// 陀螺仪系数LY
        /// </summary>
        public const int BtnGyrosCoefficientLY = 1721884517;
		/// <summary>
        /// 权重-W1
        /// </summary>
        public const int BtnWeight1 = 1616580612;
		/// <summary>
        /// 权重-W2
        /// </summary>
        public const int BtnWeight2 = 1256587248;
		/// <summary>
        /// 权重-W3
        /// </summary>
        public const int BtnWeight3 = 1403369829;
		/// <summary>
        /// 修改LerpRate
        /// </summary>
        public const int BtnModifyLerpRate = 359299655;
		/// <summary>
        /// 最小移动距离
        /// </summary>
        public const int BtnMinMoveDis = **********;
		/// <summary>
        /// 水平忽略位移
        /// </summary>
        public const int BtnHorizIgnoreTrans = 216842256;
		/// <summary>
        /// 竖直忽略位移
        /// </summary>
        public const int BtnVertIgnoreTrans = **********;
		/// <summary>
        /// 俯仰限制角度
        /// </summary>
        public const int BtnPitchLimit = 227964471;
		/// <summary>
        /// FOV-正常
        /// </summary>
        public const int BtnFOVNormal = 315887869;
		/// <summary>
        /// FOV-开镜
        /// </summary>
        public const int BtnFOVADS = **********;
		/// <summary>
        /// 渲染延迟
        /// </summary>
        public const int BtnRenderDelay = 159832987;
		/// <summary>
        /// 生成一个尸体
        /// </summary>
        public const int BtnSpawnACorpse = *********;
		/// <summary>
        /// 生成一个宝箱
        /// </summary>
        public const int BtnSpawnABox = **********;
		/// <summary>
        /// 召唤死羊建筑
        /// </summary>
        public const int BtnCreateDeadSheep = *********;
		/// <summary>
        /// 召唤怪物
        /// </summary>
        public const int BtnSummonMonster = *********;
		/// <summary>
        /// 传送到出生点
        /// </summary>
        public const int BtnTravelToSpawnPoint = *********;
		/// <summary>
        /// 尺寸倍率
        /// </summary>
        public const int BtnSizeMult = *********;
		/// <summary>
        /// 夜间亮度调整
        /// </summary>
        public const int SettingSecondNightBright = 1108252846;
		/// <summary>
        /// 第一人称镜头视野
        /// </summary>
        public const int SettingSecondFPView = *********;
		/// <summary>
        /// 主音量
        /// </summary>
        public const int SettingSecondMasterVolume = 1620868219;
		/// <summary>
        /// 麦克风音量
        /// </summary>
        public const int SettingSecondMicVolume = 1860358988;
		/// <summary>
        /// 扬声器音量
        /// </summary>
        public const int SettingSecondSpeakerVolume = 1247716266;
		/// <summary>
        /// 音效音量
        /// </summary>
        public const int SettingSecondBattleSoundEffect = 1745041483;
		/// <summary>
        /// 环境音量
        /// </summary>
        public const int SettingSecondAmbientSound = 636233511;
		/// <summary>
        /// 第一人称人物自由镜头（小眼睛）
        /// </summary>
        public const int SettingSecondFPFreeShot = 197988942;
		/// <summary>
        /// 第一人称不开镜
        /// </summary>
        public const int SettingSecondFPDontOpenCamera = 1279758303;
		/// <summary>
        /// 机瞄、全息
        /// </summary>
        public const int SettingSecondIronSightsHolograms = 453642563;
		/// <summary>
        /// 8倍镜
        /// </summary>
        public const int SettingSecond8xScope = 2013909251;
		/// <summary>
        /// 第三人称人物、载具状态自由镜头（小眼睛）
        /// </summary>
        public const int SettingSecondTPVehFreeCamera = 1533871135;
		/// <summary>
        /// 第三人称不开镜
        /// </summary>
        public const int SettingSecondTPDontOpenCamera = 677858794;
		/// <summary>
        /// 金属瞄准镜
        /// </summary>
        public const int SettingSecondIronSight = 2012175952;
		/// <summary>
        /// 16倍镜
        /// </summary>
        public const int SettingSecond16xScope = 1358635749;
		/// <summary>
        /// FPS
        /// </summary>
        public const int SettingSecondFPS = 1966238847;
		/// <summary>
        /// 填满背包
        /// </summary>
        public const int SettingSecondFillBackpack = 432457784;
		/// <summary>
        /// 脱离卡死
        /// </summary>
        public const int SettingSecondUnstuck = 1030190480;
		/// <summary>
        /// 添加武器
        /// </summary>
        public const int SettingSecondAddWeapons = 394595348;
		/// <summary>
        /// 扩充背包
        /// </summary>
        public const int SettingSecondExpandBackpack = 17253800;
		/// <summary>
        /// 增加300子弹
        /// </summary>
        public const int SettingSecondAddAmmo = 311592051;
		/// <summary>
        /// 召唤空投
        /// </summary>
        public const int SettingSecondSummonAirDrop = 1213359305;
		/// <summary>
        /// 拓扑debug/warning
        /// </summary>
        public const int SettingSecondTopologyDebug = 604780866;
		/// <summary>
        /// 拓扑计算密度数据
        /// </summary>
        public const int SettingSecondTopologyCaculates = 1075402039;
		/// <summary>
        /// 清空死羊建筑
        /// </summary>
        public const int SettingSecondClearDeadSheep = 2147124273;
		/// <summary>
        /// 自身红名
        /// </summary>
        public const int SettingSecondSelfWanted = 162410541;
		/// <summary>
        /// 取消自身红名
        /// </summary>
        public const int SettingSecondCancelWanted = 1004991160;
		/// <summary>
        /// 删除所有建筑
        /// </summary>
        public const int SettingSecondDeleteAllBuilding = 424054513;
		/// <summary>
        /// 删除自己建筑
        /// </summary>
        public const int SettingSecondDeleteMyBuilding = 1738357831;
		/// <summary>
        /// 语言设置
        /// </summary>
        public const int SettingBtnLanSwitch = 102677874;
		/// <summary>
        /// 开发
        /// </summary>
        public const int SettingBtnLanDev = 463662173;
		/// <summary>
        /// 中文
        /// </summary>
        public const int SettingBtnLanCh = 1042037532;
		/// <summary>
        /// 英语
        /// </summary>
        public const int SettingBtnLanEn = 1494452123;
		/// <summary>
        /// 确认切换当前显示语言吗? 这将会返回到登录界面
        /// </summary>
        public const int SettingLanSwitchTips = 808301495;
		/// <summary>
        /// 开关未激活
        /// </summary>
        public const int ActTipsSwitchNotEnable = 673268725;
		/// <summary>
        /// 开关已开启
        /// </summary>
        public const int ActTipsSwitchNotOpen = 161316962;
		/// <summary>
        /// 操作
        /// </summary>
        public const int InteractiveOp = 501568990;
		/// <summary>
        /// 呼叫电梯
        /// </summary>
        public const int InteractiveCallElevator = 870510686;
		/// <summary>
        /// 淘汰点附近重生
        /// </summary>
        public const int NearbyRespawn = 1378128308;
		/// <summary>
        /// 自动拾取
        /// </summary>
        public const int SettingAutoPickup = 766869113;
		/// <summary>
        /// 关闭拾取列表时关闭自动拾取
        /// </summary>
        public const int SettingClosePickup = 906830341;
		/// <summary>
        /// 新的淘汰背包是否显示在最上方
        /// </summary>
        public const int SettingDieBagAtTop = 1321774932;
		/// <summary>
        /// 自动拾取道具类型
        /// </summary>
        public const int SettingAutoPickupType = 125547467;
		/// <summary>
        /// HUD方案
        /// </summary>
        public const int SettingSwitchHud = 70747157;
		/// <summary>
        /// 拾取设置
        /// </summary>
        public const int SettingPickup = 787013776;
		/// <summary>
        /// 冷却({0})
        /// </summary>
        public const int KatyushaCoolDown = 1563902484;
		/// <summary>
        /// 无法发射
        /// </summary>
        public const int KatyushaCannotFire = 1145731430;
		/// <summary>
        /// 准备就绪
        /// </summary>
        public const int KatyushaReady = 1479441088;
		/// <summary>
        /// 发射
        /// </summary>
        public const int KatyushaFire = 1974815829;
		/// <summary>
        /// 校准
        /// </summary>
        public const int KatyushaAiming = 1463467232;
		/// <summary>
        /// 发射中
        /// </summary>
        public const int KatyushaFiring = 873222217;
		/// <summary>
        /// 装载导弹
        /// </summary>
        public const int KatyushaLoadMissiles = 1632097769;
		/// <summary>
        /// 安装模块
        /// </summary>
        public const int KatyushaInsertModule = 391537682;
		/// <summary>
        /// 卸载模块
        /// </summary>
        public const int KatyushaRemoveModule = 621947331;
		/// <summary>
        /// 瞄准模块
        /// </summary>
        public const int KatyushaNoModule = 1576962343;
		/// <summary>
        /// 综合
        /// </summary>
        public const int ItemTypeGenerate = 1854077991;
		/// <summary>
        /// 安装
        /// </summary>
        public const int KatyushaMissileLoad = 1013295272;
		/// <summary>
        /// 卸载
        /// </summary>
        public const int KatyushaMissileUnLoad = 964055211;
		/// <summary>
        /// 拥有:{0}
        /// </summary>
        public const int KatyushaMissileOwned = 1649143684;
		/// <summary>
        /// 剩余资源:{0}
        /// </summary>
        public const int RemainRes = 2086360312;
		/// <summary>
        /// 房间重置中
        /// </summary>
        public const int RoomReseting = 1885270051;
		/// <summary>
        /// 等待中
        /// </summary>
        public const int Waiting = 972620852;
		/// <summary>
        /// 抢夺物资
        /// </summary>
        public const int LootRes = 941460391;
		/// <summary>
        /// 请确认是否放弃
        /// </summary>
        public const int ConfirmGiveUpOrNot = 2141189235;
		/// <summary>
        /// 确认放弃
        /// </summary>
        public const int ConfirmGiveUp = 1182209712;
		/// <summary>
        /// 中途撤离，即将结算
        /// </summary>
        public const int LeaveSettle = 1657261872;
		/// <summary>
        /// 弱防御力 
        /// </summary>
        public const int WeakDefense = 1095685493;
		/// <summary>
        /// 强防御力 
        /// </summary>
        public const int StrongDefense = 1783138396;
		/// <summary>
        /// 点击按钮拆除这个建筑
        /// </summary>
        public const int BuildWaitDestroy = 1424763061;
		/// <summary>
        /// 点击按钮维修这个建筑
        /// </summary>
        public const int BuildWaitRepair = 217708731;
		/// <summary>
        /// 点击按钮拆除电线
        /// </summary>
        public const int BuildWaitClearWire = 1252462982;
		/// <summary>
        /// 点击按钮回收这个建筑
        /// </summary>
        public const int BuildWaitPickup = 789455016;
		/// <summary>
        /// 该建筑已升级到指定材质
        /// </summary>
        public const int UpgradeFull = 31626205;
		/// <summary>
        /// 成功装备{0}
        /// </summary>
        public const int WeaponEquipTips = 391146998;
		/// <summary>
        /// 卸下{0}
        /// </summary>
        public const int WeaponUnloadTips = 1245129699;
		/// <summary>
        /// 申请记录处理成功。
        /// </summary>
        public const int PermExeSuc = 1515749278;
		/// <summary>
        /// 拥有管理权限的玩家可以分配重要权限，请慎重考虑。
        /// </summary>
        public const int PermAdminAddConfirm = 1737965717;
		/// <summary>
        /// 您在领地{0}已被添加为领地管理员。
        /// </summary>
        public const int PermAdminAddSuc2 = 1839283729;
		/// <summary>
        /// 是否确定将{0}移除管理员一职？
        /// </summary>
        public const int PermAdminRemConfirm = 680627376;
		/// <summary>
        /// 您在领地{0}的管理员一职已被移除。
        /// </summary>
        public const int PermRemSuc2 = 942051987;
		/// <summary>
        /// 你已获得领地{0}下的{1}权限，可在工具柜-领地中枢-查看权限中查看详细内容。
        /// </summary>
        public const int PermAccessed = 1229324744;
		/// <summary>
        /// 你已失去队伍相关领地下的所有权限。
        /// </summary>
        public const int PermTeamQuit = 1440337826;
		/// <summary>
        /// 领地范围未检测到工作台，是否跳转制作界面？
        /// </summary>
        public const int PermNoWorkbench = 1078114388;
		/// <summary>
        /// 是否确认申请权限{0}？申请成功后{1}内不可再申请权限。
        /// </summary>
        public const int PermRequestConfirm = 1014383290;
		/// <summary>
        /// 权限申请成功。
        /// </summary>
        public const int PermRequestSuc = 2072850049;
		/// <summary>
        /// 权限申请冷却中，剩余{0}。
        /// </summary>
        public const int PermRequestCoolDown = 1794163137;
		/// <summary>
        /// 您在领地{0}申请的{1}权限已被通过。
        /// </summary>
        public const int PermRequestApproved = 162256934;
		/// <summary>
        /// 您在领地{0}申请的{1}权限已被拒绝。
        /// </summary>
        public const int PermRequestDeclined = 871268470;
		/// <summary>
        /// 我已通过您对{0}权限的请求，欢迎加入领地{1}！
        /// </summary>
        public const int PermWelcome = 1207738813;
		/// <summary>
        /// 非常抱歉，你在领地{0}申请的{1}权限请求已被拒绝。
        /// </summary>
        public const int PermSorry = 491122582;
		/// <summary>
        /// 刷新成功。
        /// </summary>
        public const int PermRefreshSuc = 222083555;
		/// <summary>
        /// [color=#D8D8D8]请输入玩家昵称[/color]
        /// </summary>
        public const int PermNeedID = 731811977;
		/// <summary>
        /// 更名成功。
        /// </summary>
        public const int PermRenamedSuc = 2119215619;
		/// <summary>
        /// 关系组{0}已添加成功。
        /// </summary>
        public const int PermAddGroupSuc = 1206156552;
		/// <summary>
        /// 自定义关系组已满，无法添加新关系。
        /// </summary>
        public const int PermGroupNumFull = 109063406;
		/// <summary>
        /// 您的关系组已被修改或移除，点击退出当前界面。
        /// </summary>
        public const int PermGroupChanged = 148484966;
		/// <summary>
        /// 未命名领地
        /// </summary>
        public const int PermUntitledLand = 453910560;
		/// <summary>
        /// 该玩家已经被赋予关系。
        /// </summary>
        public const int PermPlayerHasPermission = 749671656;
		/// <summary>
        /// 转化率=100% * 数量上限 / 当前总数转化率越高，T0转化为T1的耗时越短当前小队摆件总数：{0}本服小队摆件上限：{1}
        /// </summary>
        public const int ReputationRateTip = 999927908;
		/// <summary>
        /// 每日固定时间进行基于当前情报段位结算局外奖励，奖励将通过邮件发送到局外账号
        /// </summary>
        public const int ReputationSettleTimeTip = 421631361;
		/// <summary>
        /// 每次提交都只会消耗固定数量（段位越高，消耗越大）数量不足则无法提交
        /// </summary>
        public const int ReputationSubmitTip = 1041793844;
		/// <summary>
        /// 移除玩家权限成功。
        /// </summary>
        public const int PermDelSuc = 685136185;
		/// <summary>
        /// 以及道具:{0}
        /// </summary>
        public const int ExtendPackItemListPrefix = 852110971;
		/// <summary>
        /// 背包已满,替换扩容背包后将丢弃旧的扩容背包{0},是否继续?
        /// </summary>
        public const int ExtendPackReplaceTips1 = 2087433397;
		/// <summary>
        /// 背包已满,替换扩容背包后部分道具将一并转移到{0}, 包含:{1}
        /// </summary>
        public const int ExtendPackReplaceTips2 = 1229489003;
		/// <summary>
        /// 背包已满,替换后背包和{0}均无法存放剩余道具
        /// </summary>
        public const int ExtendPackReplaceTips3 = 1154662440;
		/// <summary>
        /// 背包空间不足无法存放
        /// </summary>
        public const int ExtendPackMoveTips1 = 59763511;
		/// <summary>
        /// 背包空间不足, 请清空扩容背包后尝试
        /// </summary>
        public const int ExtendPackMoveTips2 = 1986039876;
		/// <summary>
        /// 背包已满,卸下扩容背包后部分道具将被转移到{0},包含:{1}
        /// </summary>
        public const int ExtendPackUnloadTips1 = 669627571;
		/// <summary>
        /// 背包已满,卸下后背包和{0}均无法存放剩余道具
        /// </summary>
        public const int ExtendPackUnloadTips2 = 200863387;
		/// <summary>
        /// 背包已满,卸下扩容背包后部分道具将被丢弃,包含: {0}
        /// </summary>
        public const int ExtendPackUnloadTips3 = 1322543653;
		/// <summary>
        /// 背包已满,替换扩容背包后将丢弃旧的扩容背包{0},是否继续?
        /// </summary>
        public const int ExtendPackPickTips1 = 347635693;
		/// <summary>
        /// 系统
        /// </summary>
        public const int UiMailSystemSender = 797565173;
		/// <summary>
        /// 公告
        /// </summary>
        public const int UiMailNoticeSender = 2073007413;
		/// <summary>
        /// 您需要删除所有已读邮件吗？
        /// </summary>
        public const int UiMailDeleteAllTip = 931780310;
		/// <summary>
        /// {0}年{1}月{2}日
        /// </summary>
        public const int UiMailDateFormat = 661370402;
		/// <summary>
        /// {0}天
        /// </summary>
        public const int UiMailValidFormat = 222118230;
		/// <summary>
        /// 永久有效
        /// </summary>
        public const int UiMailValidForever = 151881908;
		/// <summary>
        /// 删除
        /// </summary>
        public const int UiMailDelete = 978842823;
		/// <summary>
        /// 领取
        /// </summary>
        public const int UiMailReceive = 1920011462;
		/// <summary>
        /// 电路编辑
        /// </summary>
        public const int ItemUseEditIC = 1336971961;
		/// <summary>
        /// 掉落物
        /// </summary>
        public const int DropItem = 1751837031;
		/// <summary>
        /// 大种植箱
        /// </summary>
        public const int UiBigPlantBox = 1072230056;
		/// <summary>
        /// 小种植箱
        /// </summary>
        public const int UiSmallPlantBox = 27644126;
		/// <summary>
        /// 拾取掉落物
        /// </summary>
        public const int PickDrop = 1238866909;
		/// <summary>
        /// 管理员
        /// </summary>
        public const int Admin = 2074789161;
		/// <summary>
        /// 申请记录
        /// </summary>
        public const int PermApplyRecord = 91495303;
		/// <summary>
        /// 播种
        /// </summary>
        public const int PlantSeed = 541208660;
		/// <summary>
        /// 幼苗期
        /// </summary>
        public const int PlantStageSeed = 612723371;
		/// <summary>
        /// 杂交期
        /// </summary>
        public const int PlantStageHybrid = 1287063909;
		/// <summary>
        /// 成熟期
        /// </summary>
        public const int PlantStageManure = 1627679387;
		/// <summary>
        /// 收获期
        /// </summary>
        public const int PlantStageHarvest = 1698837566;
		/// <summary>
        /// 制作种植箱
        /// </summary>
        public const int PlantCreateBox = 1838315351;
		/// <summary>
        /// 门自动化设置
        /// </summary>
        public const int AutoOpenDoorSetting = 2033724922;
		/// <summary>
        /// 查看权限列表
        /// </summary>
        public const int PermLookOver = 1145710422;
		/// <summary>
        /// 权限管理
        /// </summary>
        public const int PermManage = 1631488107;
		/// <summary>
        /// 新增组
        /// </summary>
        public const int PermAddNewGroup = 1510548376;
		/// <summary>
        /// 删除组
        /// </summary>
        public const int PermDeleteGroup = 590046434;
		/// <summary>
        /// 一键同意
        /// </summary>
        public const int PermAgreeAll = 1706701773;
		/// <summary>
        /// 你的权限已经被更改，需要关闭界面
        /// </summary>
        public const int PermRoleChangeCloseWinTip = 1286391834;
		/// <summary>
        /// 你确定要删除这个权限组？
        /// </summary>
        public const int PermDeleteGroupConfirmTip = 274020429;
		/// <summary>
        /// 距离下次可申请时间
        /// </summary>
        public const int PermNextApplyTime = 1473112582;
		/// <summary>
        /// 申请时间
        /// </summary>
        public const int PermApplyTitleTime = 715327982;
		/// <summary>
        /// 申请组名称
        /// </summary>
        public const int PermApplyGroupName = 1404544011;
		/// <summary>
        /// 申请结果
        /// </summary>
        public const int PermApplyResult = 1607966620;
		/// <summary>
        /// 成功
        /// </summary>
        public const int PermApplySuccessful = 1202036736;
		/// <summary>
        /// 失败
        /// </summary>
        public const int PermApplyFail = 732690165;
		/// <summary>
        /// 选中添加成员的组已经不存在，请重新选择
        /// </summary>
        public const int PermAddNewMemberOnGroupChanged = 1713849121;
		/// <summary>
        /// 自定义组已经满，无法新增
        /// </summary>
        public const int PermAddGroupFullTip = 240996239;
		/// <summary>
        /// 新权限组
        /// </summary>
        public const int PermAddGroupDefaultName = 44038852;
		/// <summary>
        /// 无效的命名
        /// </summary>
        public const int PermInvalidGroupRename = 1497367080;
		/// <summary>
        /// 你未选择任何权限
        /// </summary>
        public const int PermAddGroupNoSelectAnyItem = 290682309;
		/// <summary>
        /// 搜索
        /// </summary>
        public const int PermSearchMemberBtn = 275100292;
		/// <summary>
        /// 查找
        /// </summary>
        public const int PermSearchMemberLabel = 682632136;
		/// <summary>
        /// 队伍
        /// </summary>
        public const int PermTeamMemberLabel = 129781475;
		/// <summary>
        /// 删除
        /// </summary>
        public const int PermMemberItemDelete = 333492118;
		/// <summary>
        /// 修改
        /// </summary>
        public const int PermMemberItemChange = 114216891;
		/// <summary>
        /// 领主
        /// </summary>
        public const int PermLord = 2133295282;
		/// <summary>
        /// 添加
        /// </summary>
        public const int PermMemberItemAdd = 1262585741;
		/// <summary>
        /// 成员已满
        /// </summary>
        public const int PermMemberFull = 575654164;
		/// <summary>
        /// 添加成员
        /// </summary>
        public const int PermAddMember = 1167090282;
		/// <summary>
        /// 没有申请记录
        /// </summary>
        public const int PermNoApplyRecord = 1695747351;
		/// <summary>
        /// 该组内没有组员
        /// </summary>
        public const int PermNoPlayerInThisGroup = 983593723;
		/// <summary>
        /// 未找到玩家
        /// </summary>
        public const int PermSearchPlayerNotFound = 394256046;
		/// <summary>
        /// 不播种
        /// </summary>
        public const int PlantNoSeed = 467363640;
		/// <summary>
        /// 同意
        /// </summary>
        public const int PermMemberItemAgree = 1970690524;
		/// <summary>
        /// 拒绝
        /// </summary>
        public const int PermMemberItemReject = 667929741;
		/// <summary>
        /// 申请权限
        /// </summary>
        public const int PermApply = 1845566199;
		/// <summary>
        /// 修改权限
        /// </summary>
        public const int PermChange = 729803078;
		/// <summary>
        /// 申请
        /// </summary>
        public const int PermApplyOp = 832769111;
		/// <summary>
        /// 无法申请
        /// </summary>
        public const int PermCanNotApplyOp = 35249390;
		/// <summary>
        /// 选择
        /// </summary>
        public const int PermChooseOp = 1753443754;
		/// <summary>
        /// 批量修复成功
        /// </summary>
        public const int BatchRepairSuc = 324231720;
		/// <summary>
        /// 你没有权限进行批量修复
        /// </summary>
        public const int BatchRepairNoPermission = 1454514991;
		/// <summary>
        /// 未找到领地，你不能进行批量修复
        /// </summary>
        public const int BatchRepairTerritoryNotFound = 897817493;
		/// <summary>
        /// 正处于攻击冷却中，你不能进行批量修复
        /// </summary>
        public const int BatchRepairInAttackCd = 416907370;
		/// <summary>
        /// 正处于修复冷却中，你不能进行批量修复
        /// </summary>
        public const int BatchRepairInBatchCd = 252105837;
		/// <summary>
        /// 建筑记录未找到，无法进行批量修复
        /// </summary>
        public const int BatchRepairPartRecordNotFound = 1931755542;
		/// <summary>
        /// 批量修复失败，生命值满
        /// </summary>
        public const int BatchRepairHpFull = 1265825442;
		/// <summary>
        /// 批量修复失败，建筑记录错误
        /// </summary>
        public const int BatchRepairRecordError = 2078341850;
		/// <summary>
        /// 批量修复失败，附近有玩家
        /// </summary>
        public const int BatchRepairPlayerNearby = 782110601;
		/// <summary>
        /// 批量修复失败，材料不足或扣除异常
        /// </summary>
        public const int BatchRepairConsumeItemFail = 462562537;
		/// <summary>
        /// 批量修复失败，创建建筑出错
        /// </summary>
        public const int BatchRepairCreatePartEntityFail = 1940297095;
		/// <summary>
        /// 没有需要批量修复的建筑
        /// </summary>
        public const int BatchRepairNoPart = 1165037687;
		/// <summary>
        /// 无法维修，未收到伤害
        /// </summary>
        public const int RepairFailNotDamage = 664953334;
		/// <summary>
        /// 无法维修，未找到玩家背包
        /// </summary>
        public const int RepairFailNotFindBag = 1483241567;
		/// <summary>
        /// 游戏说明1）选择1个杂交期植株，2个材料植株后，可以进入游戏2）可使用点数调整单个基因在单个顺位上的概率3）进入小游戏后，点击屏幕即可增加已选中的基因的概率4）每个植株只能作为目标植株杂交一次
        /// </summary>
        public const int HybridPlayTips = 1886166853;
		/// <summary>
        /// 附近未检测到种植箱，是否前往制作？
        /// </summary>
        public const int PlantNoPlanter = 1103032715;
		/// <summary>
        /// 是否确认移除植株？
        /// </summary>
        public const int PlantDelConfirm = 1610614726;
		/// <summary>
        /// 天花板
        /// </summary>
        public const int TagFloor = 369002985;
		/// <summary>
        /// 墙壁
        /// </summary>
        public const int TagWall = 967058358;
		/// <summary>
        /// 门窗
        /// </summary>
        public const int TagWindow = 510871066;
		/// <summary>
        /// 楼梯
        /// </summary>
        public const int TagStairs = 159065611;
		/// <summary>
        /// 地基
        /// </summary>
        public const int TagFoundation = 508583454;
		/// <summary>
        /// 供电设备
        /// </summary>
        public const int TagPowerEquip = 1477951350;
		/// <summary>
        /// 链接设备
        /// </summary>
        public const int TagElecLinkDevice = 109407837;
		/// <summary>
        /// 电力组件
        /// </summary>
        public const int TagPowerComponents = 1364761582;
		/// <summary>
        /// 用电设备
        /// </summary>
        public const int TagElecDevice = 474359189;
		/// <summary>
        /// 附近物品
        /// </summary>
        public const int OthersideTitleNearBy = 853157748;
		/// <summary>
        /// {0}的背包
        /// </summary>
        public const int OthersideTitleOfflinePlayer = 447786984;
		/// <summary>
        /// 战利品
        /// </summary>
        public const int OthersideTitleDefault = 1308747984;
		/// <summary>
        /// 未找到吸附点。
        /// </summary>
        public const int BuildNoAdsorption = 1377694080;
		/// <summary>
        /// 该造物需要被放置在墙壁、墙壁框架或者半墙等建筑上。
        /// </summary>
        public const int BuildCoreTips1 = 833436369;
		/// <summary>
        /// 该造物需要被摆放在地面上。
        /// </summary>
        public const int BuildCoreTips2 = 186947115;
		/// <summary>
        /// 该造物需要被放置在三角地基上。
        /// </summary>
        public const int BuildCoreTips3 = 473233647;
		/// <summary>
        /// 该造物需要被放置在地基、天花板等建筑上。
        /// </summary>
        public const int BuildCoreTips4 = 1603233406;
		/// <summary>
        /// 该造物需要被放置在地基、天花板、墙壁等建筑上。
        /// </summary>
        public const int BuildCoreTips5 = 2077630409;
		/// <summary>
        /// 该造物需要被放置在矩形地基、天花板上。
        /// </summary>
        public const int BuildCoreTips6 = 1914323791;
		/// <summary>
        /// 该造物需要被放置在地面或地基、天花板等建筑上。
        /// </summary>
        public const int BuildCoreTips7 = 1338323886;
		/// <summary>
        /// 请尝试摆放在更平缓的区域。
        /// </summary>
        public const int BuildAngleFailed1 = 500530862;
		/// <summary>
        /// 需要向下吸附在天花板上。
        /// </summary>
        public const int BuildAngleFailed2 = 700692992;
		/// <summary>
        /// 该造物需要被放置在墙上。
        /// </summary>
        public const int BuildAngleFailed3 = 231711796;
		/// <summary>
        /// 该造物需要被放置在地基与墙相接处。
        /// </summary>
        public const int BuildAngleFailed4 = 1124123336;
		/// <summary>
        /// 该造物底部需要被完全摆放在地基上。
        /// </summary>
        public const int BuildLayerFailed1 = 1669923435;
		/// <summary>
        /// 该造物底部需要被完全摆放在地面上。
        /// </summary>
        public const int BuildLayerFailed2 = 1874687054;
		/// <summary>
        /// 该造物不可在水中建造。
        /// </summary>
        public const int BuildCantInWater = 416420909;
		/// <summary>
        /// 下方造物上空无法建造天花板。
        /// </summary>
        public const int BuildAirflowFailed = 1080846778;
		/// <summary>
        /// 当前造物不可在地形中建造。
        /// </summary>
        public const int BuildTerrainFailed = 292636558;
		/// <summary>
        /// 当前位置无法建造,底部未处于地面以下。
        /// </summary>
        public const int BuildBotFailed = 2051123408;
		/// <summary>
        /// 有物体阻挡，不可建造。
        /// </summary>
        public const int BuildBlocked = 955060465;
		/// <summary>
        /// 离PVE区域太近，无法建造。
        /// </summary>
        public const int Build2Close2PVE = 218510395;
		/// <summary>
        /// 离道路太近，无法建造。
        /// </summary>
        public const int Build2Close3Road = 1015895274;
		/// <summary>
        /// 离其他建造太近，无法建造。
        /// </summary>
        public const int BuildPreventFailed = 973071174;
		/// <summary>
        /// 请等小木盒消失后再尝试建造。
        /// </summary>
        public const int BuildDebrisBlocked = 638468446;
		/// <summary>
        /// 当前位置支撑力不足，无法建造。
        /// </summary>
        public const int BuildSuppFailed = 249005581;
		/// <summary>
        /// 存在支撑力不足风险,是否确认删除？
        /// </summary>
        public const int BuildSuppFailedWarn = 1449881241;
		/// <summary>
        /// 材料不足，无法建造。
        /// </summary>
        public const int BuildNoMaterial = 1441847953;
		/// <summary>
        /// 超出可建造距离，无法建造。
        /// </summary>
        public const int BuildOutRange = 626787845;
		/// <summary>
        /// 视线被阻挡，无法建造。
        /// </summary>
        public const int BuildSightBlocked = 391917612;
		/// <summary>
        /// 控件引导开关
        /// </summary>
        public const int btnComGuide = 2060143300;
		/// <summary>
        /// 实体信息面板开关
        /// </summary>
        public const int btnInfoBoard = 1328374212;
		/// <summary>
        /// 重置实体面板显示次数
        /// </summary>
        public const int btnResetBoard = 2126817263;
		/// <summary>
        /// 重置引导显示次数
        /// </summary>
        public const int btnResetGuide = 2018444218;
		/// <summary>
        /// 显示引导ID
        /// </summary>
        public const int btnShowGuideId = 802026946;
		/// <summary>
        /// 新手引导
        /// </summary>
        public const int titleGuide = 1537664738;
		/// <summary>
        /// 性能数据采集
        /// </summary>
        public const int btnPerformanceDataCollect = 1629582933;
		/// <summary>
        /// 容器修改
        /// </summary>
        public const int ContainerModify = 114934467;
		/// <summary>
        /// 一键清空
        /// </summary>
        public const int OneClickClear = 1665055645;
		/// <summary>
        /// 视线检测范围显示
        /// </summary>
        public const int btnShowSightDetection = 2145612904;
		/// <summary>
        /// 墙壁动态吸附
        /// </summary>
        public const int WallSnapFix = 815228692;
		/// <summary>
        /// 全部
        /// </summary>
        public const int BtnTabAll = 1380586880;
		/// <summary>
        /// 选择建筑进行修复
        /// </summary>
        public const int SelectToRepair = 748261667;
		/// <summary>
        /// 选择建筑进行升级
        /// </summary>
        public const int SelectToUpgrade = 1166566954;
		/// <summary>
        /// 选择建筑进行删除
        /// </summary>
        public const int SelectToDestroy = 190013032;
		/// <summary>
        /// 选择建筑进行回收
        /// </summary>
        public const int SelectToPickup = 1826078454;
		/// <summary>
        /// 前往研发界面
        /// </summary>
        public const int CraftGoToTechTree = 469451076;
		/// <summary>
        /// 蓝图未学习
        /// </summary>
        public const int CraftNotLearn = 750204014;
		/// <summary>
        /// 设置计时器持续时间
        /// </summary>
        public const int ElectricTimerSettingTitle = 368977286;
		/// <summary>
        /// 秒数
        /// </summary>
        public const int ElectricTimerSettingDescription = 1204443958;
		/// <summary>
        /// 设定持续时间
        /// </summary>
        public const int ElectricTimerSettingConfirm = 1999119552;
		/// <summary>
        /// 是否放弃?
        /// </summary>
        public const int KillSelfTips = 1269114591;
		/// <summary>
        /// 帧率上限
        /// </summary>
        public const int SettingSecondFpsLimit = 1529286302;
		/// <summary>
        /// 是否脱离卡死?
        /// </summary>
        public const int EscapeFromStuckTips = 345694501;
		/// <summary>
        /// 提高帧率，会较大程度加快设备耗电、发热
        /// </summary>
        public const int FpsLimitTip = 1620830776;
		/// <summary>
        /// 帧率太低，是否降低画质为
        /// </summary>
        public const int AdaptiveDecreaseRMQuality = 1875020768;
		/// <summary>
        /// 成员
        /// </summary>
        public const int Member = 547183996;
		/// <summary>
        /// 升级
        /// </summary>
        public const int Upgrade = 404676101;
		/// <summary>
        /// 降级
        /// </summary>
        public const int Downgrade = 1174503567;
		/// <summary>
        /// 该权限移除后，该成员将被移除本领地。
        /// </summary>
        public const int PermAssignWarning = 7262363;
		/// <summary>
        /// 不可采集，请等待火焰熄灭
        /// </summary>
        public const int TooHotToGather = 601218346;
		/// <summary>
        /// 邀请您加入 大厅准备中 {0}模式的队伍，是否现在跳转界面？
        /// </summary>
        public const int LobbyTeamTip1 = 81422342;
		/// <summary>
        /// 邀请您加入 大厅准备中 {0}模式的队伍
        /// </summary>
        public const int LobbyTeamTip2 = 1965831641;
		/// <summary>
        /// 邀请您加入 {0}模式 的 {1}服务器的队伍，是否同意并进入服务器？
        /// </summary>
        public const int LobbyTeamTip3 = 75582408;
		/// <summary>
        /// 当前已对商店做出了修改，保存之后才会正式生效，确定要保存吗？
        /// </summary>
        public const int VendingMachineChangeConfirm = 189480533;
		/// <summary>
        /// 保存
        /// </summary>
        public const int VendingMachineSave = 221128355;
		/// <summary>
        /// 不要保存
        /// </summary>
        public const int VendingMachineNotSave = 1934492169;
		/// <summary>
        /// 装备武器
        /// </summary>
        public const int ItemTipsEquipWeapon = 251521352;
		/// <summary>
        /// 放入快捷栏
        /// </summary>
        public const int ItemTipsEquipBelt = 1838462920;
		/// <summary>
        /// 卸下武器
        /// </summary>
        public const int ItemTipsUnloadWeapon = 1588069928;
		/// <summary>
        /// 存入背包
        /// </summary>
        public const int ItemTipsUnloadBelt = 267042735;
		/// <summary>
        /// 大厅GM
        /// </summary>
        public const int SettingLobbyGM = 47213853;
		/// <summary>
        /// 增加指定货币
        /// </summary>
        public const int AddCurrency = 1221686534;
		/// <summary>
        /// 放入燃油
        /// </summary>
        public const int OilPutIN = 1963233291;
		/// <summary>
        /// 取出燃油
        /// </summary>
        public const int OilPutOut = 1321575723;
		/// <summary>
        /// 请输入道具名称
        /// </summary>
        public const int VendingMachineInputSearch = 737777455;
		/// <summary>
        /// [color=#c7f279]双击[/color]转移
        /// </summary>
        public const int DoubleClickToMove = 1603054956;
		/// <summary>
        /// [color=#c7f279]双击[/color]装备
        /// </summary>
        public const int DoubleClickToEquip = 1834330945;
		/// <summary>
        /// 掉落和交易
        /// </summary>
        public const int ItemDetailDropAndTrade = 360490687;
		/// <summary>
        /// 制作
        /// </summary>
        public const int ItemDetailCraft = 1420865367;
		/// <summary>
        /// 蓝图
        /// </summary>
        public const int ItemDetailBlueprint = 461211810;
		/// <summary>
        /// 分解
        /// </summary>
        public const int ItemDetailDecompose = 1692515519;
		/// <summary>
        /// 可用于制作
        /// </summary>
        public const int ItemDetailBeCrafting = 1608640;
		/// <summary>
        /// [color=#c7f279]双击[/color]卸下
        /// </summary>
        public const int DoubleClickToUnEquip = 720646139;
		/// <summary>
        /// 玩家信息
        /// </summary>
        public const int PlayerInfo = 1697083919;
		/// <summary>
        /// 私聊玩家
        /// </summary>
        public const int PrivateChat = 2095135375;
		/// <summary>
        /// 加为好友
        /// </summary>
        public const int AddFriend = 1403975356;
		/// <summary>
        /// 移出队伍
        /// </summary>
        public const int KickTeam = 1265346370;
		/// <summary>
        /// 打开
        /// </summary>
        public const int OpenOven = 782361927;
		/// <summary>
        /// 该成员身份是管理员，请先移除其管理员身份再取消其最后的权限。
        /// </summary>
        public const int PermAssignWarningAdmin = 856877580;
		/// <summary>
        /// 4米范围内无{0}
        /// </summary>
        public const int ItemDetailNoTarget = 1949239223;
		/// <summary>
        /// 离开队伍
        /// </summary>
        public const int LeaveTeam = 2078619015;
		/// <summary>
        /// 是否退出当前队伍
        /// </summary>
        public const int LeaveTeamTips = 1234367368;
		/// <summary>
        /// 是否将{0}踢出队伍
        /// </summary>
        public const int KickTeamTips = 703575705;
		/// <summary>
        /// 队友不占用权限位置
        /// </summary>
        public const int TeammateNotOccupyPermPosition = 686477873;
		/// <summary>
        /// 等级{0}
        /// </summary>
        public const int LevelTextFormat = 1942394031;
		/// <summary>
        /// {0}点积分解锁
        /// </summary>
        public const int ReputationUnlockAmountFormat = 765346755;
		/// <summary>
        /// 获得蓝图{0}
        /// </summary>
        public const int ReputationGetBluePrintFormat = 66976623;
		/// <summary>
        /// 前往{0}
        /// </summary>
        public const int ItemDetailGoToOven = 2015804810;
		/// <summary>
        /// 是否装备缓存的护甲皮肤
        /// </summary>
        public const int LobbySkinAppleCacheArmorSkin = 1881806419;
		/// <summary>
        /// 解锁方式
        /// </summary>
        public const int ItemDetailUnlockingMethod = 1912112375;
		/// <summary>
        /// 查找手册
        /// </summary>
        public const int SearchManual = 256337543;
		/// <summary>
        /// {0}的商店
        /// </summary>
        public const int VendingMachineName = 670188466;
		/// <summary>
        /// 前往种植
        /// </summary>
        public const int ItemUseSeeding = 2058340554;
		/// <summary>
        /// 无查询结果
        /// </summary>
        public const int NoSearchResult = 66813632;
		/// <summary>
        /// 无成员
        /// </summary>
        public const int NoMember = 1397195465;
		/// <summary>
        /// 组合建筑升级
        /// </summary>
        public const int ComboUpgrade = 804369943;
		/// <summary>
        /// 筛选
        /// </summary>
        public const int Filter = 266378930;
		/// <summary>
        /// 据点
        /// </summary>
        public const int Monument = 1280848439;
		/// <summary>
        /// 重生点位
        /// </summary>
        public const int Respawn = 1905877388;
		/// <summary>
        /// 商店
        /// </summary>
        public const int Shop = 96269016;
		/// <summary>
        /// 生命值     {0}速度上限    {1}km/h体力值     {2}
        /// </summary>
        public const int HorseDesc = 1425274301;
		/// <summary>
        /// 正在加载大厅场景...
        /// </summary>
        public const int LoadingLobbyScene = 290011402;
		/// <summary>
        /// 登录中...
        /// </summary>
        public const int LobbyLogin = 987969622;
		/// <summary>
        /// 大厅
        /// </summary>
        public const int ChatLobby = 246166341;
		/// <summary>
        /// 您已加入大厅服务器聊天频道
        /// </summary>
        public const int ChatLobbyWelcome = 1577581236;
		/// <summary>
        /// 您已加入世界频道聊天服务器
        /// </summary>
        public const int ChatWorldWelcome = 1230330084;
		/// <summary>
        /// 系统消息
        /// </summary>
        public const int ChatSystemMessage = 763695783;
		/// <summary>
        /// 【好友】
        /// </summary>
        public const int ChatIsFriend = 29401346;
		/// <summary>
        /// 匹配新战局失败
        /// </summary>
        public const int FindNewBattleFailed = 38048186;
		/// <summary>
        /// 因本次测试规模所限，同时开启的服务器数量较少，您或您的小队成员已在当前每一个服务器中创建了角色；您可通过“历史战局”界面继续之前的战局
        /// </summary>
        public const int FindNewBattleFailedTip = 604804983;
		/// <summary>
        /// 毫升
        /// </summary>
        public const int WaterMillilitre = 949290164;
		/// <summary>
        /// Gm皮肤
        /// </summary>
        public const int SettingMainGMSkin = 1248291456;
		/// <summary>
        /// 背包
        /// </summary>
        public const int InventoryTitleBackpack = 204742950;
		/// <summary>
        /// 制作
        /// </summary>
        public const int InventoryTitleCraft = 2056586143;
		/// <summary>
        /// 编辑为
        /// </summary>
        public const int BuildModifyTo = 1750417864;
		/// <summary>
        /// {0}的战利品
        /// </summary>
        public const int OthersideTitleDeadPlayer = 700070705;
		/// <summary>
        /// 解锁倒计时
        /// </summary>
        public const int MonumentUIUnlockTime = 1603615240;
		/// <summary>
        /// 重置生存手册解锁状态
        /// </summary>
        public const int btnResetHandbook = 411438016;
		/// <summary>
        /// 解锁所有生存手册
        /// </summary>
        public const int btnUnlockAllHandBook = 1125413810;
		/// <summary>
        /// 解锁指定手册
        /// </summary>
        public const int btnUnlockOneHandbook = 702768517;
		/// <summary>
        /// 开启动画桶
        /// </summary>
        public const int btnAnimationBucket = 2000304826;
		/// <summary>
        /// 远景树的gpu instance
        /// </summary>
        public const int btnGpuInstance = 234683527;
		/// <summary>
        /// 开启角色桶
        /// </summary>
        public const int btnCharacterBucket = 121891924;
		/// <summary>
        /// 开启资源桶
        /// </summary>
        public const int btnResBucket = 839660890;
		/// <summary>
        /// 领地保险柜
        /// </summary>
        public const int TerritorySafe = 539484950;
		/// <summary>
        /// 全部
        /// </summary>
        public const int SkinFilterAll = 70865785;
		/// <summary>
        /// 默认
        /// </summary>
        public const int SkinSortDefault = 2022832750;
		/// <summary>
        /// 品质
        /// </summary>
        public const int SkinSortQuality = 1002378322;
		/// <summary>
        /// 请输入数字
        /// </summary>
        public const int EnterNumber = 1723698530;
		/// <summary>
        /// 取消
        /// </summary>
        public const int CombatMarker_Cancel = 1138129952;
		/// <summary>
        /// 他人确认
        /// </summary>
        public const int CombatMarker_OtherConfirm = 1266750983;
		/// <summary>
        /// 确认
        /// </summary>
        public const int CombatMarker_Confirm = 2010801010;
		/// <summary>
        /// 开启角色性能得分显示
        /// </summary>
        public const int btnCharacterScore = 1378216178;
		/// <summary>
        /// 开启资源性能得分显示
        /// </summary>
        public const int btnResScore = 959549742;
		/// <summary>
        /// 智能助登录手账号验证
        /// </summary>
        public const int btnCloseAiReview = 1041301603;
		/// <summary>
        /// 是否确认解绑床位{0}
        /// </summary>
        public const int confirmCanUnlock = 619923662;
		/// <summary>
        /// 保险柜
        /// </summary>
        public const int SafeBox = 114584537;
		/// <summary>
        /// 暂存保险柜
        /// </summary>
        public const int TempCoffer = 1726053023;
		/// <summary>
        /// 追踪
        /// </summary>
        public const int Track = 142394740;
		/// <summary>
        /// 取消追踪
        /// </summary>
        public const int StopTrack = 1405544455;
		/// <summary>
        /// 氧气瓶开始使用
        /// </summary>
        public const int StartUsingDivingTank = 951201299;
		/// <summary>
        /// 氧气瓶停止使用
        /// </summary>
        public const int StopUsingDivingTank = 15849060;
		/// <summary>
        /// 氧气瓶氧气耗尽
        /// </summary>
        public const int DivingTankIsBroken = 440659173;
		/// <summary>
        /// 正在修复中
        /// </summary>
        public const int UnderRepair = 1193111981;
		/// <summary>
        /// 剩余修复时间{0}
        /// </summary>
        public const int PartRecoverRemainTime = 269378111;
		/// <summary>
        /// 退出游戏
        /// </summary>
        public const int BackToLogin = 1073628376;
		/// <summary>
        /// 接受跳转
        /// </summary>
        public const int AcceptJump = 1691901711;
		/// <summary>
        /// 接受不跳转
        /// </summary>
        public const int AcceptNoJump = 1337572140;
		/// <summary>
        /// {0}在{1}-{2}中向您发起组队邀请
        /// </summary>
        public const int TeamTips = 165259141;
		/// <summary>
        /// 战局
        /// </summary>
        public const int OldBattleServer = 835644117;
		/// <summary>
        /// 当前战局
        /// </summary>
        public const int SameBattleServer = 1484759063;
		/// <summary>
        /// 其他战局
        /// </summary>
        public const int OtherBattleServer = 1142119747;
		/// <summary>
        /// 睡袋接受权限
        /// </summary>
        public const int GivePrivilege = 517682177;
		/// <summary>
        /// 任何人
        /// </summary>
        public const int Anybody = 433046460;
		/// <summary>
        /// 队友
        /// </summary>
        public const int Teammate = 1873709453;
		/// <summary>
        /// 禁止
        /// </summary>
        public const int Forbid = 1998089971;
		/// <summary>
        /// 地上的物品
        /// </summary>
        public const int PickableOnTheGround = 501553;
		/// <summary>
        /// 结构组件
        /// </summary>
        public const int BasicStructures = 852351671;
		/// <summary>
        /// 摆件
        /// </summary>
        public const int FunctionalOrnaments = 934327485;
		/// <summary>
        /// 电力/水利摆件
        /// </summary>
        public const int PowerOrnaments = 1683823409;
		/// <summary>
        /// 随机复活
        /// </summary>
        public const int RandomPointRebon = 397598962;
		/// <summary>
        /// 安全区
        /// </summary>
        public const int SafeArea = 1382865269;
		/// <summary>
        /// [color=#d7482c]威胁概览[/color]
        /// </summary>
        public const int WarningTitle = 1629420988;
		/// <summary>
        /// 敌人×
        /// </summary>
        public const int EnemyNPC = 945801677;
		/// <summary>
        /// 低辐射
        /// </summary>
        public const int RadiationLow = 1417642266;
		/// <summary>
        /// 中辐射
        /// </summary>
        public const int RadiationMid = 2029625020;
		/// <summary>
        /// 高辐射
        /// </summary>
        public const int RadiationHigh = 808960340;
		/// <summary>
        /// 低温
        /// </summary>
        public const int LowTempurature = 2083518874;
		/// <summary>
        /// 存在水下环境
        /// </summary>
        public const int UnderWaterEnv = 1462945778;
		/// <summary>
        /// 存在黑暗区域
        /// </summary>
        public const int DarkArea = 2020141342;
		/// <summary>
        /// 据点基础资源
        /// </summary>
        public const int BasicResourceTitle = 49248644;
		/// <summary>
        /// 绿卡房资源
        /// </summary>
        public const int GreenCardRoomResource = 229316531;
		/// <summary>
        /// 蓝卡房资源
        /// </summary>
        public const int BlueCardRoomResource = 95791562;
		/// <summary>
        /// 红卡房资源
        /// </summary>
        public const int RedCardRoomResource = 1616084520;
		/// <summary>
        /// 完成
        /// </summary>
        public const int SiteDone = 81997318;
		/// <summary>
        /// 请选择重生方式
        /// </summary>
        public const int ChoseRebonWay = 1385216412;
		/// <summary>
        /// 野兽×
        /// </summary>
        public const int WildBeast = 211546049;
		/// <summary>
        /// 据点设备
        /// </summary>
        public const int DeviceTitle = 1532429301;
		/// <summary>
        /// 无连接
        /// </summary>
        public const int ElectricalDataNoConnect = 514099689;
		/// <summary>
        /// 无电力输入
        /// </summary>
        public const int ElectricalDataNoPower = 1712761227;
		/// <summary>
        /// 电力不足
        /// </summary>
        public const int ElectricalDataLowPower = 166009352;
		/// <summary>
        /// 正常工作
        /// </summary>
        public const int ElectricalDataWork = 293213521;
		/// <summary>
        /// 领地
        /// </summary>
        public const int TerritoryCenter = 1089692573;
		/// <summary>
        /// 难度:{0}
        /// </summary>
        public const int TaskDifficulty = 1839669248;
		/// <summary>
        /// 进度:{0}/{1}
        /// </summary>
        public const int TaskProgress = 534622686;
		/// <summary>
        /// 每日任务奖励:{0}/{1}
        /// </summary>
        public const int PointTaskDailyReward = 555412870;
		/// <summary>
        /// 每日任务奖励重置：{0}
        /// </summary>
        public const int PointTaskReset = 1788221651;
		/// <summary>
        /// 开始修理
        /// </summary>
        public const int Repair = 1726972305;
		/// <summary>
        /// {0}秒
        /// </summary>
        public const int RepairRemainSeconds = 1527989089;
		/// <summary>
        /// {0}分{1}秒
        /// </summary>
        public const int RepairRemainMinute = 1764609936;
		/// <summary>
        /// {0}小时{1}分{2}秒
        /// </summary>
        public const int RepairRemainHours = 1843725237;
		/// <summary>
        /// {0}秒后
        /// </summary>
        public const int RebonPointInCd = 1949831526;
		/// <summary>
        /// 采集物
        /// </summary>
        public const int Collectible = 1159898769;
		/// <summary>
        /// 重生
        /// </summary>
        public const int Reborn = 1869214033;
		/// <summary>
        /// 冷却时间
        /// </summary>
        public const int CoolDownTime = 1782568609;
		/// <summary>
        /// {0}m以上
        /// </summary>
        public const int MaxBedDistance = 1120095447;
		/// <summary>
        /// 有锁
        /// </summary>
        public const int LockAttached = 636720586;
		/// <summary>
        /// 在{0}模式中
        /// </summary>
        public const int PlayingInMode = 1745468766;
		/// <summary>
        /// 掉落
        /// </summary>
        public const int Drop = 64966973;
		/// <summary>
        /// {0}的队伍
        /// </summary>
        public const int RecruitDefaultName = 880310512;
		/// <summary>
        /// 欢迎加入我们！
        /// </summary>
        public const int RecruitDefaultCondition = 431155347;
		/// <summary>
        /// {0}的领地
        /// </summary>
        public const int TerritoryCenterBelonging = 1958987071;
		/// <summary>
        /// 工具柜
        /// </summary>
        public const int NormalToolCarbinet = 1128980073;
		/// <summary>
        /// 情报工具柜
        /// </summary>
        public const int IntelligenceToolCarbinet = 2039417744;
		/// <summary>
        /// 可解锁或制作{0}物品
        /// </summary>
        public const int TerritoryTechLevel = 1552203976;
		/// <summary>
        /// 已有{0}权限
        /// </summary>
        public const int TerritoryPrivilege = 1171058598;
		/// <summary>
        /// 领地维护时间>{0}小时
        /// </summary>
        public const int TerritoryProtectHour = 432629489;
		/// <summary>
        /// 领地维护时间>{0}分钟
        /// </summary>
        public const int TerritoryProtectMinute = 323742267;
		/// <summary>
        /// 领地维护时间>{0}小时{1}分钟
        /// </summary>
        public const int TerritoryProtectTime = 974025495;
		/// <summary>
        /// 手工科技
        /// </summary>
        public const int TerritoryTechLevel0 = 467285986;
		/// <summary>
        /// 一级科技
        /// </summary>
        public const int TerritoryTechLevel1 = 105889962;
		/// <summary>
        /// 二级科技
        /// </summary>
        public const int TerritoryTechLevel2 = 158250431;
		/// <summary>
        /// 三级科技
        /// </summary>
        public const int TerritoryTechLevel3 = 1716604546;
		/// <summary>
        /// 【陌生人】
        /// </summary>
        public const int ChatIsStranger = 1356267798;
		/// <summary>
        /// 被玩家淘汰冷却{0}秒
        /// </summary>
        public const int NearByPVPCdHint = 5218906;
		/// <summary>
        /// 使用后将在淘汰点附近随机重生，冷却{0}秒
        /// </summary>
        public const int NearByCdHint = 1597414754;
		/// <summary>
        /// 切换弹药
        /// </summary>
        public const int ItemTipsSwitchClip = 211011093;
		/// <summary>
        /// 卸下弹药
        /// </summary>
        public const int ItemTipsUnloadClip = 950745550;
		/// <summary>
        /// 您当前排队的位置：{0}
        /// </summary>
        public const int GameServerQueueTip = 857885998;
		/// <summary>
        /// 低级零件
        /// </summary>
        public const int LowEnginePart = 129973505;
		/// <summary>
        /// 中级零件
        /// </summary>
        public const int MidEnginePart = 247331872;
		/// <summary>
        /// 高级零件
        /// </summary>
        public const int HighEnginePart = 1127844619;
		/// <summary>
        /// 倒计时结束有 {0}% 概率可自动自救，自动自救概率随水分和饱腹值增长，最高为{1}%
        /// </summary>
        public const int DyingTipText = 1652249751;
		/// <summary>
        /// 消耗所携带的医疗包可进行自救
        /// </summary>
        public const int MedicineTipText = 1384335348;
		/// <summary>
        /// 中心受击UI
        /// </summary>
        public const int SettingSecondCenterDamageUi = 271166299;
		/// <summary>
        /// 雷达图UI
        /// </summary>
        public const int SettingSecondMinimapSoudUi = 1065306578;
		/// <summary>
        /// 声纹UI
        /// </summary>
        public const int SettingSecondSoundPrintUi = 1842426716;
		/// <summary>
        /// 边缘血雾UI
        /// </summary>
        public const int SettingSecondDamageBloodUi = 1187102079;
		/// <summary>
        /// 瞄准镜
        /// </summary>
        public const int PartSlotScope = 821713115;
		/// <summary>
        /// 枪口
        /// </summary>
        public const int PartSlotMuzzle = 1814485168;
		/// <summary>
        /// 下挂
        /// </summary>
        public const int PartSlotUnderBarrel = 969120207;
		/// <summary>
        /// 弹夹
        /// </summary>
        public const int PartSlotMagazine = 191592780;
		/// <summary>
        /// 基因未知
        /// </summary>
        public const int GeneUnknown = 1242472140;
		/// <summary>
        /// 基地升级中
        /// </summary>
        public const int BatchUpgrading = 1483606805;
		/// <summary>
        /// 基地升级
        /// </summary>
        public const int BatchUpgrade = 1874998039;
		/// <summary>
        /// 标准模式
        /// </summary>
        public const int StandardMode = 654943928;
		/// <summary>
        /// 炸家模式
        /// </summary>
        public const int BomberMode = 1528962954;
		/// <summary>
        /// 其他模式
        /// </summary>
        public const int OtherMode = 1549998368;
		/// <summary>
        /// 教学手册
        /// </summary>
        public const int TeachBook = 1276949920;
		/// <summary>
        /// 关闭情报站？
        /// </summary>
        public const int ReputationShutdownNormalTitle = 447108531;
		/// <summary>
        /// 关闭后，您的情报等级不会受到影响，您和您的团队成员都可以将自己的任何工具柜升级为情报站。
        /// </summary>
        public const int ReputationShutdownNormalTips = 1689821923;
		/// <summary>
        /// 关闭情报站失败
        /// </summary>
        public const int ReputationShutdownFaildTitle = 1779359065;
		/// <summary>
        /// 在关闭之前，您必须确保所有情报文件已从此情报站完全移除。
        /// </summary>
        public const int ReputationShutdownFaildTips = 511101770;
		/// <summary>
        /// 屏蔽队友标记
        /// </summary>
        public const int HideTeamCombatMarker = 1216905780;
		/// <summary>
        /// 屏蔽后是否展示确认标记
        /// </summary>
        public const int ShowSelectedTeamCombatMarker = 12617720;
		/// <summary>
        /// 已放置数{0}
        /// </summary>
        public const int PlacedPartNum = 749537783;
		/// <summary>
        /// 游戏中
        /// </summary>
        public const int GameInside = 597256683;
		/// <summary>
        /// 请添加资源
        /// </summary>
        public const int PleaseAddRes = 29682585;
		/// <summary>
        /// 点击输入您的昵称
        /// </summary>
        public const int NameInputHint = 2080991447;
		/// <summary>
        /// 打开保险柜
        /// </summary>
        public const int OpenSafeBox = 1400628452;
		/// <summary>
        /// 改造为保险工具柜
        /// </summary>
        public const int ModifySafeBox = 605796514;
		/// <summary>
        /// 非好友
        /// </summary>
        public const int NonFriend = 794384215;
		/// <summary>
        /// 点击按钮维修这个摆件
        /// </summary>
        public const int DeployRepair = 1529377492;
		/// <summary>
        /// 点击按钮维修这个载具
        /// </summary>
        public const int VehicleRepair = 1507986558;
		/// <summary>
        /// 使用
        /// </summary>
        public const int Use = 866930763;
		/// <summary>
        /// 使用中
        /// </summary>
        public const int InUse = 1910988054;
		/// <summary>
        /// 未拥有
        /// </summary>
        public const int NotOwned = 418782805;
		/// <summary>
        /// 自动自救概率
        /// </summary>
        public const int SelfAidProb = 1076162108;
		/// <summary>
        /// 是否确认删除{0}
        /// </summary>
        public const int ConfirmDeleteFriend = 2111628376;
		/// <summary>
        /// 你学会了一个新蓝图！
        /// </summary>
        public const int SuccessUnlockBlueprint = 1239759618;
		/// <summary>
        /// 耗电量
        /// </summary>
        public const int PowerConsumption = 1618236743;
		/// <summary>
        /// 输出功率
        /// </summary>
        public const int GenerationCapacity = 2048019251;
		/// <summary>
        /// 删除好友
        /// </summary>
        public const int DeleteFriend = 1500432861;
		/// <summary>
        /// 举报玩家
        /// </summary>
        public const int ReportPlayer = 1130921411;
		/// <summary>
        /// 建造组件
        /// </summary>
        public const int BuildingComponents = 1891708918;
		/// <summary>
        /// 可放置
        /// </summary>
        public const int Placeables = 148106178;
		/// <summary>
        /// 官方小屋
        /// </summary>
        public const int OfficialBase = 453623527;
		/// <summary>
        /// 当前功率:{0}/{1}
        /// </summary>
        public const int CurrentPower = 1484095808;
		/// <summary>
        /// 当前流速:{0}/{1}
        /// </summary>
        public const int CurrentFlow = 1222722812;
		/// <summary>
        /// 网络异常，是否尝试重连？
        /// </summary>
        public const int NetworkAbnormal = 207264516;
		/// <summary>
        /// 拥有
        /// </summary>
        public const int Owned = 1575855486;
		/// <summary>
        /// 是否举报该玩家昵称违规
        /// </summary>
        public const int ReportPlayerName = 958099078;
		/// <summary>
        /// 可供电时长
        /// </summary>
        public const int ElectricInfoChargeLeft = 1725293482;
		/// <summary>
        /// 剩余充电时间
        /// </summary>
        public const int ElectricInfoChargeTime = 736868234;
		/// <summary>
        /// 输出功率
        /// </summary>
        public const int ElectricInfoUsage = 1388711285;
		/// <summary>
        /// 储电量
        /// </summary>
        public const int ElectricInfoPower = 231490874;
		/// <summary>
        /// 待连接元件
        /// </summary>
        public const int ElectricInfoPlugPart = 1812959350;
		/// <summary>
        /// 待连接端口
        /// </summary>
        public const int ElectricInfoPlugSlot = 2106537687;
		/// <summary>
        /// 该玩家为你的好友，是否确定屏蔽？
        /// </summary>
        public const int ConfrimBlockFriend = 1502779403;
		/// <summary>
        /// 是否解除对该玩家的屏蔽？
        /// </summary>
        public const int ConfirmUnBlock = 108724877;
		/// <summary>
        /// 屏蔽玩家
        /// </summary>
        public const int BlockPlayer = 1289867449;
		/// <summary>
        /// 解除屏蔽
        /// </summary>
        public const int UnBlock = 507887334;
		/// <summary>
        /// 取消上次打点
        /// </summary>
        public const int ElectrictCancelLastPoint = 1915169554;
		/// <summary>
        /// 清除所有打点
        /// </summary>
        public const int ElectrictClearAllPoints = 1504580900;
		/// <summary>
        /// 穿戴重型套装，无法进行骑乘
        /// </summary>
        public const int ProhibitMountHorse = 2077194582;
		/// <summary>
        /// 断电将会关闭电路盒通电状态，相连的开关将会关闭，读卡器将无法刷卡，是否确认断电？
        /// </summary>
        public const int SwipeCardGameBlackout = 248365868;
		/// <summary>
        /// 开启特效性能得分显示
        /// </summary>
        public const int btnEffectScore = 1452138624;
		/// <summary>
        /// [color=#faf3ec][size=24]距离安全区还有[/size][/color][color=#ff6266][size=24]{0}[/size][/color][color=#faf3ec][size=24]米[/size][/color]
        /// </summary>
        public const int SafetyAreaDistance = 2017592381;
		/// <summary>
        /// 开启特效桶
        /// </summary>
        public const int btnEffectBucket = 1596746529;
		/// <summary>
        /// 维修材料
        /// </summary>
        public const int RepairMaterials = 936019671;
		/// <summary>
        /// [color=#c7f279]双击[/color]取回
        /// </summary>
        public const int DoubleClickToGet = 499962522;
		/// <summary>
        /// 第一人称
        /// </summary>
        public const int FpSwitchText = 1748979899;
		/// <summary>
        /// 第三人称
        /// </summary>
        public const int TpSwitchText = 452992921;
		/// <summary>
        /// 第三人称切换功能
        /// </summary>
        public const int btnTpsSwitch = 646335209;
		/// <summary>
        /// 库存：
        /// </summary>
        public const int Stock = 977642580;
		/// <summary>
        /// 显示水印
        /// </summary>
        public const int ShowWaterMark = 320135923;
		/// <summary>
        /// 生命值
        /// </summary>
        public const int Health = **********;
		/// <summary>
        /// 引擎总功率
        /// </summary>
        public const int Enginetotalpower = **********;
		/// <summary>
        /// 体力上限
        /// </summary>
        public const int Stamina = **********;
		/// <summary>
        /// 最大速度
        /// </summary>
        public const int Maxspeed = 843074029;
		/// <summary>
        /// 每分钟油耗
        /// </summary>
        public const int Fuelconsumption = **********;
		/// <summary>
        /// 存储容量
        /// </summary>
        public const int Storagecapacity = **********;
		/// <summary>
        /// 饥饿程度
        /// </summary>
        public const int Hungerlevel = 414220333;
		/// <summary>
        /// 腐蚀程度
        /// </summary>
        public const int Corrosionconditions = **********;
		/// <summary>
        /// 轻度腐蚀
        /// </summary>
        public const int Mildcorrosion = 1370184104;
		/// <summary>
        /// 中度腐蚀
        /// </summary>
        public const int Moderatecorrosion = 1365350864;
		/// <summary>
        /// 重度腐蚀
        /// </summary>
        public const int Severecorrosion = 596749602;
		/// <summary>
        /// 无
        /// </summary>
        public const int Nocorrosion = 1641586094;
		/// <summary>
        /// 饥饿
        /// </summary>
        public const int Hungercorrodes = 88224157;
		/// <summary>
        /// 饱腹
        /// </summary>
        public const int Satietyerosion = 1894111989;
		/// <summary>
        /// 
        /// </summary>
        public const int Barrel = 1898429779;
		/// <summary>
        /// 音乐音量
        /// </summary>
        public const int MusicSound = 1461040836;
		/// <summary>
        /// 界面音量
        /// </summary>
        public const int WinDowSound = 958600075;
		/// <summary>
        /// 低威胁
        /// </summary>
        public const int LowThreat = 463851355;
		/// <summary>
        /// 中威胁
        /// </summary>
        public const int MidThreat = 137683161;
		/// <summary>
        /// 高威胁
        /// </summary>
        public const int HighThreat = 1566092849;
		/// <summary>
        /// 极度威胁
        /// </summary>
        public const int ExtremeThreat = 2140212036;
		/// <summary>
        /// 错误 541
        /// </summary>
        public const int NoModuleCode = 1992403623;
		/// <summary>
        /// 请先安装模块，再安装导弹
        /// </summary>
        public const int NoModuleResult = 1772388314;
		/// <summary>
        /// 错误 412
        /// </summary>
        public const int InCdCode = 552316680;
		/// <summary>
        /// 多管火箭系统处于冷却时间中，请稍后尝试
        /// </summary>
        public const int InCdResult = 1251716026;
		/// <summary>
        /// 载具放置在安全区，受到轻微的腐蚀伤害
        /// </summary>
        public const int MildcorrosionText = 1722816188;
		/// <summary>
        /// 载具放置在室内，受到较少的腐蚀伤害
        /// </summary>
        public const int ModeratecorrosionText = 988973401;
		/// <summary>
        /// 载具放置在野外，受到较大的腐蚀伤害
        /// </summary>
        public const int SeverecorrosionText = 1834589806;
		/// <summary>
        /// 载具使用中，不会进入腐蚀状态
        /// </summary>
        public const int NocorrosionText = 1156420723;
		/// <summary>
        /// 马匹未进食进入腐蚀状态，生命值持续流失
        /// </summary>
        public const int HungercorrodesText = 1346314650;
		/// <summary>
        /// 马匹饱腹状态
        /// </summary>
        public const int SatietyerosionText = 1124588889;
		/// <summary>
        /// 连接电线
        /// </summary>
        public const int ConnectWire = 2006490934;
		/// <summary>
        /// 放置摆件
        /// </summary>
        public const int Placement = 1239135810;
		/// <summary>
        /// 改造
        /// </summary>
        public const int Modify = 1108830227;
		/// <summary>
        /// 连接水管
        /// </summary>
        public const int ConnectPipe = 1881676513;
		/// <summary>
        /// 建造
        /// </summary>
        public const int Build = 985845760;
		/// <summary>
        /// 拆线
        /// </summary>
        public const int ClearWire = 1603417605;
		/// <summary>
        /// 删除
        /// </summary>
        public const int Destory = 2032930030;
		/// <summary>
        /// 拾取
        /// </summary>
        public const int PickUp = 58401053;
		/// <summary>
        /// {0}的武器/装备
        /// </summary>
        public const int OthersideInventoryWearText = 1729405059;
		/// <summary>
        /// {0}的快捷栏
        /// </summary>
        public const int OthersideInventoryBeltText = 1837784435;
		/// <summary>
        /// {0}的背包
        /// </summary>
        public const int OthersideInventoryMainText = 209025305;
		/// <summary>
        /// 水利接口
        /// </summary>
        public const int WaterInterface = 938164642;
		/// <summary>
        /// 情报补给箱 - {0}专属
        /// </summary>
        public const int ReputationContainerTitle = 1635475306;
		/// <summary>
        /// 游戏中
        /// </summary>
        public const int LobbyGaming = 135200268;
		/// <summary>
        /// 组队中
        /// </summary>
        public const int LobbyTeaming = 961965343;
		/// <summary>
        /// 载具受到轻度腐蚀，生命值持续流失
        /// </summary>
        public const int MildcorrosionIcon = 999466435;
		/// <summary>
        /// 载具受到中度腐蚀，生命值持续流失
        /// </summary>
        public const int ModeratecorrosionIcon = 1351677529;
		/// <summary>
        /// 载具受到重度腐蚀，生命值持续流失
        /// </summary>
        public const int SeverecorrosionIcon = 2079963019;
		/// <summary>
        /// 脚步检测频率桶
        /// </summary>
        public const int btnFootstepBucket = 1674290943;
		/// <summary>
        /// 接受
        /// </summary>
        public const int Accept = 1459767048;
		/// <summary>
        /// 拒绝
        /// </summary>
        public const int Reject = 1396261668;
		/// <summary>
        /// 组队邀请
        /// </summary>
        public const int TeamInvite = 563003098;
		/// <summary>
        /// 流速
        /// </summary>
        public const int WaterVelocity = 596548645;
		/// <summary>
        /// 功率
        /// </summary>
        public const int ElectricPower = 2025636761;
		/// <summary>
        /// 电力端口
        /// </summary>
        public const int ElectricPort = 2007824961;
		/// <summary>
        /// 确认
        /// </summary>
        public const int ResHandleConfirm = 1267475172;
		/// <summary>
        /// 确认添加
        /// </summary>
        public const int ResHandleConfirmAdd = 2040752975;
		/// <summary>
        /// 剩余改造次数
        /// </summary>
        public const int LeftTransformTime = 1818018154;
		/// <summary>
        /// 背包已满,替换扩容背包后将丢弃部分道具,包含道具:{0}
        /// </summary>
        public const int ExtendPackReplaceTips4 = 1678203800;
		/// <summary>
        /// 无法乘坐，已有其他玩家
        /// </summary>
        public const int TargetSeatIsNotEmpty = 186537482;
		/// <summary>
        /// 留在原地
        /// </summary>
        public const int StayInPlace = 1100209837;
		/// <summary>
        /// 丢在地上
        /// </summary>
        public const int ToTheGround = 1281767949;
		/// <summary>
        /// 破译大师
        /// </summary>
        public const int SurprisePlayRankTitle1 = 1434977183;
		/// <summary>
        /// 探索先驱
        /// </summary>
        public const int SurprisePlayRankTitle2 = 1413033172;
		/// <summary>
        /// 通灵者
        /// </summary>
        public const int SurprisePlayRankTitle3 = 593065519;
		/// <summary>
        /// 失意者
        /// </summary>
        public const int SurprisePlayRankTitle4 = 1313763890;
		/// <summary>
        /// 输入的昵称过长
        /// </summary>
        public const int NameCardRuler2 = 672791942;
		/// <summary>
        /// {0}级
        /// </summary>
        public const int Level = 1582017476;
		/// <summary>
        /// 腐蚀保护时间剩余{0}天{1}小时{2}分钟。
        /// </summary>
        public const int ProtectTimeLeft1 = 789704236;
		/// <summary>
        /// 腐蚀保护时间剩余{0}小时{1}分钟。
        /// </summary>
        public const int ProtectTimeLeft2 = 1575792378;
		/// <summary>
        /// 腐蚀保护时间剩余{0}分钟。
        /// </summary>
        public const int ProtectTimeLeft3 = 1577333906;
		/// <summary>
        /// 换弹中...
        /// </summary>
        public const int UsingItem_Reloading = 1244536214;
		/// <summary>
        /// 使用中...
        /// </summary>
        public const int UsingItem_Engaging = 33687199;
		/// <summary>
        /// 马背包
        /// </summary>
        public const int HorseStorage = 1409092471;
		/// <summary>
        /// 木制马甲
        /// </summary>
        public const int WoodenWaistcoat = 1509149273;
		/// <summary>
        /// 右马鞍包
        /// </summary>
        public const int RightSaddleBag = 1318388484;
		/// <summary>
        /// 左马鞍包
        /// </summary>
        public const int LeftSaddleBag = 1385632615;
		/// <summary>
        /// 装备栏
        /// </summary>
        public const int HorseEquipment = 1065316145;
		/// <summary>
        /// 引擎
        /// </summary>
        public const int EngineAccessories = 755670425;
		/// <summary>
        /// 储物模块
        /// </summary>
        public const int StorageModules = 308169820;
		/// <summary>
        /// 储物
        /// </summary>
        public const int StorageOtherVehicles = 381136043;
		/// <summary>
        /// 拖拽枪械道具到该位置
        /// </summary>
        public const int DragWeaponItemHere = 1196005117;
		/// <summary>
        /// [color={0}]\[{1}][/color] {2}: {3}
        /// </summary>
        public const int HudGenericMessage = 1828831687;
		/// <summary>
        /// 当前禁止领地突袭，无法对领地内建筑造成伤害
        /// </summary>
        public const int RaidIsInvalid = 2084517797;
		/// <summary>
        /// 设置
        /// </summary>
        public const int SETTINGS = 1245066397;
		/// <summary>
        /// 目标容器已满，继续拆分将会自动丢弃，是否继续？
        /// </summary>
        public const int ItemSplit_AllFull = 934708303;
		/// <summary>
        /// 战局启动时间
        /// </summary>
        public const int ServerStartTime = 1176452607;
		/// <summary>
        /// 已生存
        /// </summary>
        public const int Survived = 798441309;
		/// <summary>
        /// 天
        /// </summary>
        public const int Day = 1846680964;
		/// <summary>
        /// 领地突袭[color=#d7482c]【允许】[/color]
        /// </summary>
        public const int RaidCurStageTitle1 = 951857865;
		/// <summary>
        /// 领地突袭[color=#93bf71]【禁止】[/color]
        /// </summary>
        public const int RaidCurStageTitle2 = 1937612593;
		/// <summary>
        /// {0}倒计时:
        /// </summary>
        public const int RaidCountDown = 228590703;
		/// <summary>
        /// [color=#d7482c]允许领地突袭[/color]
        /// </summary>
        public const int RaidStage1 = 1218879715;
		/// <summary>
        /// [color=#93bf71]禁止领地突袭[/color]
        /// </summary>
        public const int RaidStage2 = 500440874;
		/// <summary>
        /// 领地突袭时间段：{0}
        /// </summary>
        public const int RaidTimeRange = 1236795236;
		/// <summary>
        /// 拾取
        /// </summary>
        public const int Gather = 499364958;
		/// <summary>
        /// 附近可交互
        /// </summary>
        public const int NearbyInteractables = 540037592;
		/// <summary>
        /// #9ad849
        /// </summary>
        public const int HudChatChannelAppointmentColor = 1923653691;
		/// <summary>
        /// #ff9234
        /// </summary>
        public const int HudChatChannelWorldColor = 754901760;
		/// <summary>
        /// #9ad849
        /// </summary>
        public const int HudChatChannelTeamColor = 981608610;
		/// <summary>
        /// #5ecbd5
        /// </summary>
        public const int HudChatChannelPrivateColor = 450173743;
		/// <summary>
        /// 打点
        /// </summary>
        public const int LayoutPoints = 583594754;
		/// <summary>
        /// 踢出服务器
        /// </summary>
        public const int KickedByServer = 307402181;
		/// <summary>
        /// 踢出服务器的原因是:{0}
        /// </summary>
        public const int KickReason = 431862212;
		/// <summary>
        /// 被淘汰会掉落物资，队友救援或倒计时结束有{0}%概率可自动自救，是否确认放弃
        /// </summary>
        public const int SuicideTip1 = 1829646547;
		/// <summary>
        /// 被淘汰会掉落物资，使用医疗包可进行自救，是否确认放弃
        /// </summary>
        public const int SuicideTip2 = 1352220123;
		/// <summary>
        /// 展示简略信息
        /// </summary>
        public const int SettingDisplayPick = 651940909;
		/// <summary>
        /// 如果你确定删除此战局，请在下方输入[color=#ff8002]【确认删除】[/color]4个字
        /// </summary>
        public const int DeleteConfirm = 1521478594;
		/// <summary>
        /// 确认删除
        /// </summary>
        public const int Delete1 = 1409726284;
		/// <summary>
        /// {0:0.#}小时
        /// </summary>
        public const int TotalBattleTime = 1743573915;
		/// <summary>
        /// {0}后可用
        /// </summary>
        public const int DeleteTimeCD = 1307430071;
		/// <summary>
        /// 与服务器断开连接, 点击尝试重连或返回大厅
        /// </summary>
        public const int SvrDisconnectTimeOut = 1164082127;
		/// <summary>
        /// 您的账号在其他设备登录，请点击确认重新登录。
        /// </summary>
        public const int KickedByLogin = 696841173;
		/// <summary>
        /// 马匹处于轻度饥饿状态，生命值持续流失
        /// </summary>
        public const int HorseMildcorrosionIcon = 935782030;
		/// <summary>
        /// 马匹处于中度饥饿状态，生命值持续流失
        /// </summary>
        public const int HorseModeratecorrosionIcon = 1799410550;
		/// <summary>
        /// 马匹处于重度饥饿状态，生命值持续流失
        /// </summary>
        public const int HorseSeverecorrosionIcon = 1309928917;
		/// <summary>
        /// 背包已满，附件无法领取！
        /// </summary>
        public const int MailReceiveAttachmentTip1 = 1866538495;
		/// <summary>
        /// 当前建筑不存在该等级
        /// </summary>
        public const int NoCurrentLevel = 1936619634;
		/// <summary>
        /// 改名冷却中{0}
        /// </summary>
        public const int ChangeNameCDTime = 1538283141;
		/// <summary>
        /// 北
        /// </summary>
        public const int DirectionNorth = 101457540;
		/// <summary>
        /// 东北
        /// </summary>
        public const int DirectionNorthEast = 107048438;
		/// <summary>
        /// 东
        /// </summary>
        public const int DirectionEast = 626213189;
		/// <summary>
        /// 东南
        /// </summary>
        public const int DirectionSouthEast = 1364308822;
		/// <summary>
        /// 南
        /// </summary>
        public const int DirectionSouth = 611207638;
		/// <summary>
        /// 西南
        /// </summary>
        public const int DirectionSouthWest = 339206971;
		/// <summary>
        /// 西
        /// </summary>
        public const int DirectionWest = 965710373;
		/// <summary>
        /// 西北
        /// </summary>
        public const int DirectionNorthWest = 580945681;
		/// <summary>
        /// 空挡
        /// </summary>
        public const int TrainHUDNeutral = 301409765;
		/// <summary>
        /// 前进
        /// </summary>
        public const int TrainHUDForward = 1697268458;
		/// <summary>
        /// 后退
        /// </summary>
        public const int TrainHUDReverse = 138269221;
		/// <summary>
        /// 1档
        /// </summary>
        public const int TrainHUDLow = 116864590;
		/// <summary>
        /// 2档
        /// </summary>
        public const int TrainHUDMedium = 1034363509;
		/// <summary>
        /// 3档
        /// </summary>
        public const int TrainHUDHigh = 1611691135;
		/// <summary>
        /// 昵称含有不可用字符
        /// </summary>
        public const int IllegalNaming = 810577167;
		/// <summary>
        /// [color=#FF8F2B][size=36]{0}[/size][/color]秒
        /// </summary>
        public const int ReMainTimer = 1295340814;
		/// <summary>
        /// 淘汰点附近重生便于拾取掉落物资，确认不使用淘汰点附近重生吗？
        /// </summary>
        public const int NearbyRespawnNotice = 852127723;
		/// <summary>
        /// 饱腹值:{0}
        /// </summary>
        public const int Satiety = 794879034;
		/// <summary>
        /// 水分:{0}
        /// </summary>
        public const int HungerThirst = 261155896;
		/// <summary>
        /// 远
        /// </summary>
        public const int Far = 126193476;
		/// <summary>
        /// 近
        /// </summary>
        public const int Near = 873358743;
		/// <summary>
        /// 掉落
        /// </summary>
        public const int ItemDetailDrop = 283153872;
		/// <summary>
        /// 交易
        /// </summary>
        public const int ItemDetailTrade = 853104012;
		/// <summary>
        /// 制作队列
        /// </summary>
        public const int CraftQueue = 1244096034;
		/// <summary>
        /// 道具描述
        /// </summary>
        public const int CraftItemDesc = 226270535;
		/// <summary>
        /// 收藏
        /// </summary>
        public const int CraftCollect = 784975839;
		/// <summary>
        /// 附近没有{0}级工作台
        /// </summary>
        public const int CraftWorkbenchLevelNotEnough = 818878828;
		/// <summary>
        /// 所需材料
        /// </summary>
        public const int CraftNeedMat = 899485059;
		/// <summary>
        /// 当前科技等级
        /// </summary>
        public const int CraftCurrentTechLevel = 793984624;
		/// <summary>
        /// 无科技等级
        /// </summary>
        public const int CraftNoTechLevel = 1364218130;
		/// <summary>
        /// 一级科技等级
        /// </summary>
        public const int CraftTechLevel1 = 1641707676;
		/// <summary>
        /// 二级科技等级
        /// </summary>
        public const int CraftTechLevel2 = 251597142;
		/// <summary>
        /// 三级科技等级
        /// </summary>
        public const int CraftTechLevel3 = 819473626;
		/// <summary>
        /// 满足
        /// </summary>
        public const int CraftMatch = 334061166;
		/// <summary>
        /// 不满足
        /// </summary>
        public const int CraftNotMatch = 1886981355;
		/// <summary>
        /// 任务
        /// </summary>
        public const int ItemDetailTask = 1412425794;
		/// <summary>
        /// 任务奖励
        /// </summary>
        public const int ItemDetailTaskReward = 2116666447;
		/// <summary>
        /// 跳转
        /// </summary>
        public const int ItemDetailJumpTo = 1464608775;
		/// <summary>
        /// 请输入由数字，英文组成的账号名，字符数范围5~16。
        /// </summary>
        public const int SignUpPopup = 151240009;
		/// <summary>
        /// 材料不足
        /// </summary>
        public const int CraftMatNotEnough = 695700513;
		/// <summary>
        /// 推荐
        /// </summary>
        public const int BpTypeRecommend = 1417554318;
		/// <summary>
        /// 快捷发言音量
        /// </summary>
        public const int QuickTalkSound = 425572615;
		/// <summary>
        /// 点击 ☆ 可将道具加入收藏
        /// </summary>
        public const int CraftCollectEmptyTip = 1239613425;
		/// <summary>
        /// 暂无符合筛选条件的道具
        /// </summary>
        public const int CraftFilterEmptyTip = 1202081660;
		/// <summary>
        /// 关闭
        /// </summary>
        public const int CloseMsgTip = 765883538;
		/// <summary>
        /// 关闭{0}
        /// </summary>
        public const int CloseMsgTip2 = 1125805244;
		/// <summary>
        /// 编号:{0}
        /// </summary>
        public const int IDText = 1376365189;
		/// <summary>
        /// 已向{0}发送来自{1}-{2}的组队邀请
        /// </summary>
        public const int LobbyTeamTip5 = 1241269506;
		/// <summary>
        /// 制作
        /// </summary>
        public const int CraftGoToCraft = 208686109;
		/// <summary>
        /// 去解锁
        /// </summary>
        public const int CraftGoToUnlock = 1095518482;
		/// <summary>
        /// 情报等级显示
        /// </summary>
        public const int ReputationLevelShow = 112166936;
		/// <summary>
        /// 向后
        /// </summary>
        public const int TowardForward = 199236394;
		/// <summary>
        /// 向前
        /// </summary>
        public const int TowardBack = 1939515943;
		/// <summary>
        /// 向左
        /// </summary>
        public const int TowardLeft = 1713236268;
		/// <summary>
        /// 向右
        /// </summary>
        public const int TowardRight = 737591807;
		/// <summary>
        /// 天
        /// </summary>
        public const int UnitDay = 512944580;
		/// <summary>
        /// 已被永久封禁
        /// </summary>
        public const int BanTimeForever = 1097542937;
		/// <summary>
        /// 解封剩余时间：{0}
        /// </summary>
        public const int BanTimeRemain = 1612415936;
		/// <summary>
        /// 点击确认将离开队伍，是否继续？
        /// </summary>
        public const int RecruitLeaveTeam = 1141244912;
		/// <summary>
        /// 地图包下载失败。
        /// </summary>
        public const int MapDownloadFailed = 1829338420;
		/// <summary>
        /// 未接通
        /// </summary>
        public const int NotConnected = 596864788;
		/// <summary>
        /// 重试
        /// </summary>
        public const int Retry = 1400838440;
		/// <summary>
        /// 放置
        /// </summary>
        public const int Place = 67834699;
		/// <summary>
        /// 手工科技
        /// </summary>
        public const int TechtreePrimaryTab0 = 780567741;
		/// <summary>
        /// 一级科技
        /// </summary>
        public const int TechtreePrimaryTab1 = 111243672;
		/// <summary>
        /// 二级科技
        /// </summary>
        public const int TechtreePrimaryTab2 = 1406619361;
		/// <summary>
        /// 三级科技
        /// </summary>
        public const int TechtreePrimaryTab3 = 68322878;
		/// <summary>
        /// #BDBEBE
        /// </summary>
        public const int ElectricSlotNotConnectedGary = 1964911930;
		/// <summary>
        /// #FF7900
        /// </summary>
        public const int ElectricSlotSelected = 472705608;
		/// <summary>
        /// 剩余:{0}
        /// </summary>
        public const int MapShopDetailLeftNum = 238046311;
		/// <summary>
        /// 生存手册
        /// </summary>
        public const int HandBook = 1158863962;
		/// <summary>
        /// 工具柜
        /// </summary>
        public const int ToolCupBoard = 2068136052;
		/// <summary>
        /// {0}掉落
        /// </summary>
        public const int PickListDropItem = 409839482;
		/// <summary>
        /// 基础
        /// </summary>
        public const int CustomPanelCategoriesBase = 2112782269;
		/// <summary>
        /// 载具
        /// </summary>
        public const int CustomPanelCategoriesVehicle = 1452616559;
		/// <summary>
        /// 建造
        /// </summary>
        public const int CustomPanelCategoriesBuilding = 215278028;
		/// <summary>
        /// 全部
        /// </summary>
        public const int CustomPanelCategoriesAll = 27065841;
		/// <summary>
        /// 输入1
        /// </summary>
        public const int Input1 = 2083496496;
		/// <summary>
        /// 输入2
        /// </summary>
        public const int Input2 = 730738278;
		/// <summary>
        /// 输出
        /// </summary>
        public const int Output = 529233888;
		/// <summary>
        /// 输入
        /// </summary>
        public const int Input = 1571023947;
		/// <summary>
        /// 目标锁定
        /// </summary>
        public const int Targeting = 970977759;
		/// <summary>
        /// 弹量低
        /// </summary>
        public const int LowAmmo = 705154348;
		/// <summary>
        /// 无弹药
        /// </summary>
        public const int NoAmmo = 103574450;
		/// <summary>
        /// 阻断
        /// </summary>
        public const int Block = 298262213;
		/// <summary>
        /// 通电
        /// </summary>
        public const int PassThrough = 725646066;
		/// <summary>
        /// 增量
        /// </summary>
        public const int Increment = 1714688993;
		/// <summary>
        /// 递减
        /// </summary>
        public const int Decrement = 100338454;
		/// <summary>
        /// 清除
        /// </summary>
        public const int Clear = 597155370;
		/// <summary>
        /// 分支输出
        /// </summary>
        public const int BranchOutput = 1981445377;
		/// <summary>
        /// 切换
        /// </summary>
        public const int Toggle = 206939184;
		/// <summary>
        /// 呼叫电梯1
        /// </summary>
        public const int CallEle1 = 2103921023;
		/// <summary>
        /// 呼叫电梯2
        /// </summary>
        public const int CallEle2 = 1940179428;
		/// <summary>
        /// 输入3
        /// </summary>
        public const int Input3 = 739796323;
		/// <summary>
        /// 输出1
        /// </summary>
        public const int Output1 = 1711632555;
		/// <summary>
        /// 输出2
        /// </summary>
        public const int Output2 = 1493764809;
		/// <summary>
        /// 输出3
        /// </summary>
        public const int Output3 = 649546630;
		/// <summary>
        /// 输入端口
        /// </summary>
        public const int PowerIn = 780334136;
		/// <summary>
        /// 入水口
        /// </summary>
        public const int WaterIn = 1498132754;
		/// <summary>
        /// 出水口
        /// </summary>
        public const int WaterOut = 148811241;
		/// <summary>
        /// 设置
        /// </summary>
        public const int Set = 87758059;
		/// <summary>
        /// 重置
        /// </summary>
        public const int Reset = 1537464082;
		/// <summary>
        /// 反相输出
        /// </summary>
        public const int InvertedOutput = 2014567445;
		/// <summary>
        /// 输出4
        /// </summary>
        public const int Output4 = 789750207;
		/// <summary>
        /// 输出5
        /// </summary>
        public const int Output5 = 175695212;
		/// <summary>
        /// 输出6
        /// </summary>
        public const int Output6 = 2030442676;
		/// <summary>
        /// 输出7
        /// </summary>
        public const int Output7 = 192675327;
		/// <summary>
        /// 输出8
        /// </summary>
        public const int Output8 = 1531198981;
		/// <summary>
        /// 输入4
        /// </summary>
        public const int Input4 = 1529241297;
		/// <summary>
        /// 输入5
        /// </summary>
        public const int Input5 = 1433716843;
		/// <summary>
        /// 输入6
        /// </summary>
        public const int Input6 = 1994349937;
		/// <summary>
        /// 输入7
        /// </summary>
        public const int Input7 = 732215040;
		/// <summary>
        /// 输入8
        /// </summary>
        public const int Input8 = 1478624813;
		/// <summary>
        /// 开始
        /// </summary>
        public const int Start = 147090879;
		/// <summary>
        /// 停止
        /// </summary>
        public const int Stop = 694102509;
		/// <summary>
        /// 开启
        /// </summary>
        public const int SwitchOn = 1446239718;
		/// <summary>
        /// 关闭
        /// </summary>
        public const int SwitchOff = 236481191;
		/// <summary>
        /// 强防御
        /// </summary>
        public const int DefenceStrong = 1794665934;
		/// <summary>
        /// 弱防御
        /// </summary>
        public const int DefenceWeak = 1131825346;
		/// <summary>
        /// 方案
        /// </summary>
        public const int CloudArchive = 1917683913;
		/// <summary>
        /// 成功保存至{0}！
        /// </summary>
        public const int ArchiveSaveSuccess = 1136988613;
		/// <summary>
        /// 水管颜色
        /// </summary>
        public const int WaterPipeColor = 1962839533;
		/// <summary>
        /// 管理成员
        /// </summary>
        public const int PermMgrMember = 1082702180;
		/// <summary>
        /// 客户端\服务器资源包版本不一致，请重启游戏更新
        /// </summary>
        public const int ResVersonError = 2001985534;
		/// <summary>
        /// 否则
        /// </summary>
        public const int OtherWise = 716468457;
		/// <summary>
        /// [color=#faf3ec][size=24]否则[/size][/color][color=#ff6266][size=24]{0}[/size][/color][color=#faf3ec][size=24]秒后发起攻击[/size][/color]
        /// </summary>
        public const int HostileAttackTips = 671620528;
		/// <summary>
        /// 立即购买
        /// </summary>
        public const int ShopBuyNow = 938988119;
		/// <summary>
        /// 商品售罄
        /// </summary>
        public const int ShopSoldOut = 55742757;
		/// <summary>
        /// 购买中
        /// </summary>
        public const int ShopBuying = 467458977;
		/// <summary>
        /// 无法购买
        /// </summary>
        public const int ShopCannotBuy = 1485372369;
		/// <summary>
        /// 网络连接失败，请重试
        /// </summary>
        public const int RequestNetworkRetry = 361963763;
		/// <summary>
        /// 奖励
        /// </summary>
        public const int ReputationRewardBtn = 117962188;
		/// <summary>
        /// 匹配中断，正在恢复中，请稍候
        /// </summary>
        public const int ConnectNewServerTimeOutTip = 1750785433;
		/// <summary>
        /// ℃
        /// </summary>
        public const int TemperatureSymbol = 1864283457;
		/// <summary>
        /// 我的商店
        /// </summary>
        public const int BigMapMyShops = 899874450;
		/// <summary>
        /// 其他玩家商店
        /// </summary>
        public const int BigMapOtherPlayerShops = 1469346102;
		/// <summary>
        /// 每日可领取奖励次数{0}/{1}
        /// </summary>
        public const int PointTaskRewardNum = 1170994094;
		/// <summary>
        /// 任务进度:{0}
        /// </summary>
        public const int BoxTaskProgress = 319559291;
		/// <summary>
        /// 已完成
        /// </summary>
        public const int Finished = 1615379603;
		/// <summary>
        /// 你的库存中没有这些物品。
        /// </summary>
        public const int ComHandlePanelEmptyTips1 = 1924495538;
		/// <summary>
        /// 你的输入中没有这些物品。
        /// </summary>
        public const int ComHandlePanelEmptyTips2 = 122185987;
		/// <summary>
        /// 获取patch列表失败
        /// </summary>
        public const int InitResourceManifestFailed = 997365314;
		/// <summary>
        /// 退出
        /// </summary>
        public const int Quit = 1577257897;
		/// <summary>
        /// 重试
        /// </summary>
        public const int RetryDownload = 753271969;
		/// <summary>
        /// 下载资源失败
        /// </summary>
        public const int DownloadResourceFailed = 1942312074;
		/// <summary>
        /// 无网络连接
        /// </summary>
        public const int NoInternetConnection = 418475119;
		/// <summary>
        /// 检测到有新的客户端版本，请更新
        /// </summary>
        public const int ClientNeedUpdate = 1747070536;
		/// <summary>
        /// 当前非Wifi网络，是否继续下载？
        /// </summary>
        public const int CurrentNonWiFiEnvironment = 1438175027;
		/// <summary>
        /// 初始化依赖关系失败，重试中
        /// </summary>
        public const int InitDependencyFailed = 493178676;
		/// <summary>
        /// 开始下载
        /// </summary>
        public const int StartDownloading = 1092572784;
		/// <summary>
        /// 正在下载
        /// </summary>
        public const int DownloadingResPackNow = 1830794359;
		/// <summary>
        /// 下载失败，正在重试  
        /// </summary>
        public const int DownloadResPackFailed = 1233022834;
		/// <summary>
        /// 下载
        /// </summary>
        public const int Download = 280138313;
		/// <summary>
        /// 请安装新版本客户端
        /// </summary>
        public const int PleaseInstallApk = 663199912;
		/// <summary>
        /// 下载失败，请检查网络后重试。错误码：
        /// </summary>
        public const int DownloadResFailedWithErrorCode = 1467387310;
		/// <summary>
        /// 正在加载资源(不消耗流量)
        /// </summary>
        public const int PreloadingResPacks = 1085473000;
		/// <summary>
        /// 跳过更新
        /// </summary>
        public const int SkipUpdate = 1948451909;
		/// <summary>
        /// 初始化资源列表
        /// </summary>
        public const int InitResPackList = 1163966170;
		/// <summary>
        /// 存储空间不足
        /// </summary>
        public const int InsufficientDiskSpace = 637638323;
		/// <summary>
        /// 检查版本中
        /// </summary>
        public const int CheckVersion = 1595045676;
		/// <summary>
        /// 资源对比中
        /// </summary>
        public const int SourceAnalyseDiff = 1744959617;
		/// <summary>
        /// 正在解压(不消耗流量)
        /// </summary>
        public const int SourceExtract = 1908961347;
		/// <summary>
        /// 正在修复损坏的资源文件
        /// </summary>
        public const int FixSource = 2078385254;
		/// <summary>
        /// 正在准备资源
        /// </summary>
        public const int PrepareSource = 587324650;
		/// <summary>
        /// {0}分钟前
        /// </summary>
        public const int MinOffsetDesc = 563101056;
		/// <summary>
        /// {0}小时前
        /// </summary>
        public const int HourOffsetDesc = 1188400749;
		/// <summary>
        /// {0}天前
        /// </summary>
        public const int DayOffsetDesc = 1202441610;
		/// <summary>
        /// 30天前
        /// </summary>
        public const int MonthOffsetDesc = 1722596403;
		/// <summary>
        /// 开镜按钮穿透滑屏
        /// </summary>
        public const int SettingOpenMirrorTouchThrough = 399833646;
		/// <summary>
        /// 跳跃按钮穿透滑屏
        /// </summary>
        public const int SettingJumpTouchThrough = 531662795;
		/// <summary>
        /// 下蹲按钮穿透滑屏
        /// </summary>
        public const int SettingCrouchTouchThrough = 205080044;
		/// <summary>
        /// 游戏好友：{0}/{1}
        /// </summary>
        public const int FriendNumsDesc = 761417140;
		/// <summary>
        /// 新手关
        /// </summary>
        public const int SettingDynamicBattle = 1078427915;
		/// <summary>
        /// 进入
        /// </summary>
        public const int SettingStartDynamicBattle = 413666194;
		/// <summary>
        /// <img src='ui://CommonGlobal/Techtree_img_qgzt' width='30' height='30'/>附近需要一级工作台
        /// </summary>
        public const int TechTreeTips1 = 1873277045;
		/// <summary>
        /// <img src='ui://CommonGlobal/Techtree_img_qgzt' width='30' height='30'/>附近需要二级工作台
        /// </summary>
        public const int TechTreeTips2 = 807588477;
		/// <summary>
        /// <img src='ui://CommonGlobal/Techtree_img_qgzt' width='30' height='30'/>附近需要三级工作台
        /// </summary>
        public const int TechTreeTips3 = 1909437657;
		/// <summary>
        /// {0}秒后自动关闭
        /// </summary>
        public const int AutoCloseDoorHint = 1904052748;
		/// <summary>
        /// 仅自动关门
        /// </summary>
        public const int SettingAutoCloseDoor = 1516146728;
		/// <summary>
        /// 自动关门时间设置
        /// </summary>
        public const int SettingAutoCloseDoorTime = 1558408793;
		/// <summary>
        /// 需要进食
        /// </summary>
        public const int HorseWeakTitle = 128391570;
		/// <summary>
        /// 马匹生命值较低或处于饥饿状态，需要尽快投喂食物
        /// </summary>
        public const int HorseWeakContent = 272406306;
		/// <summary>
        /// 攻击
        /// </summary>
        public const int RouletteTextAttack = 452347577;
		/// <summary>
        /// 防御
        /// </summary>
        public const int RouletteTextDefense = 635602472;
		/// <summary>
        /// 发现敌人
        /// </summary>
        public const int RouletteTextFoundEnemy = 1520506312;
		/// <summary>
        /// 敌人经过
        /// </summary>
        public const int RouletteTextEnemyPassed = 633933377;
		/// <summary>
        /// 暂无睡袋
        /// </summary>
        public const int BigMapEmptyBed = 85901578;
		/// <summary>
        /// 暂无领地
        /// </summary>
        public const int BigMapEmptyTerritory = 1115456316;
		/// <summary>
        /// 顺方向开门
        /// </summary>
        public const int SettingOpenDoorAutoDir = 1468518481;
		/// <summary>
        /// 友好度代表您与好友互动的多寡，当您与好友合作游戏时，友好度会不断增加。在未来友好度可解锁更多的玩法。
        /// </summary>
        public const int IntimacyDesc = 1841882565;
		/// <summary>
        /// 与好友在对局内组队，随共同在线时长增加(每次离开对局结算)
        /// </summary>
        public const int IntimacyGet = 665921795;
		/// <summary>
        /// 友好度
        /// </summary>
        public const int IntimacyTitle = 1357683320;
		/// <summary>
        /// 临时好友
        /// </summary>
        public const int TempFriendTitle = 907749580;
		/// <summary>
        /// 野外剩余摆放数量：[color=#FD5252]{0}/[/color]{1}
        /// </summary>
        public const int RemainCount1 = 451037701;
		/// <summary>
        /// 野外剩余摆放数量：[color=#FFFFFF]{0}/[/color]{1}
        /// </summary>
        public const int RemainCount2 = 1448543927;
		/// <summary>
        /// 拥有：[color=#D8D8D8]{0}[/color]
        /// </summary>
        public const int HaveCount = 210566797;
		/// <summary>
        /// 组合建筑
        /// </summary>
        public const int CombineDesc = 1647906650;
		/// <summary>
        /// 核心建筑
        /// </summary>
        public const int CoreDesc = 1257862488;
		/// <summary>
        /// 蓝图由声望系统获取，声望界面在领地柜打开
        /// </summary>
        public const int BlueprintTipDes = 296592593;
		/// <summary>
        /// 显示Hud按钮热区
        /// </summary>
        public const int ShowHudEffectArea = 323870723;
		/// <summary>
        /// 一
        /// </summary>
        public const int Num1 = 38759662;
		/// <summary>
        /// 二
        /// </summary>
        public const int Num2 = 20757721;
		/// <summary>
        /// 三
        /// </summary>
        public const int Num3 = 1261837976;
		/// <summary>
        /// 四
        /// </summary>
        public const int Num4 = 654846324;
		/// <summary>
        /// 五
        /// </summary>
        public const int Num5 = 114872939;
		/// <summary>
        /// 六
        /// </summary>
        public const int Num6 = 510782063;
		/// <summary>
        /// 七
        /// </summary>
        public const int Num7 = 650274705;
		/// <summary>
        /// 八
        /// </summary>
        public const int Num8 = 2021277909;
		/// <summary>
        /// 九
        /// </summary>
        public const int Num9 = 973751857;
		/// <summary>
        /// 附近需要{0}级工作台
        /// </summary>
        public const int TechTreeNeedWorkBench = 1716088040;
		/// <summary>
        /// 耐久
        /// </summary>
        public const int LiftingPlatformDurability = 2140751062;
		/// <summary>
        /// 速度
        /// </summary>
        public const int LiftingPlatformSpeed = 1076630992;
		/// <summary>
        /// 防护
        /// </summary>
        public const int LiftingPlatformDefense = 1619269132;
		/// <summary>
        /// 功能
        /// </summary>
        public const int LiftingPlatformFunction = 1752386177;
		/// <summary>
        /// 槽位占用 [color=#D3D3D3]{0}[/color]
        /// </summary>
        public const int LiftingPlatformSoltOccupy = 161611020;
		/// <summary>
        /// 耐久度 [color=#D3D3D3]{0}[/color]
        /// </summary>
        public const int LiftingPlatformSoltDurability = 1546662605;
		/// <summary>
        /// 安装
        /// </summary>
        public const int LiftingPlatformInstall = 90379450;
		/// <summary>
        /// 卸下
        /// </summary>
        public const int LiftingPlatformUninstall = 1032602522;
		/// <summary>
        /// 替换
        /// </summary>
        public const int LiftingPlatformReplace = 826554372;
		/// <summary>
        /// 制造并安装
        /// </summary>
        public const int LiftingPlatformCraftAndInstall = 2084506492;
		/// <summary>
        /// 去解锁
        /// </summary>
        public const int LiftingPlatformUnlock = 2058117766;
		/// <summary>
        /// 材料不足
        /// </summary>
        public const int LiftingPlatformCannotCraft = 1465579851;
		/// <summary>
        /// 槽位不足
        /// </summary>
        public const int LiftingPlatformSlotNotEnough = 187070203;
		/// <summary>
        /// 重置当前布局?
        /// </summary>
        public const int ConfirmResetCustomHudLayout = 970977607;
		/// <summary>
        /// [color=#98d14e]投喂[/color] {0}
        /// </summary>
        public const int HorseFeed = 820156077;
		/// <summary>
        /// 缺少投喂食物
        /// </summary>
        public const int HorseFoodEmpty = 1364742344;
		/// <summary>
        /// (包含{0}个前置科技)
        /// </summary>
        public const int TechtreeInfoTip = 537187917;
		/// <summary>
        /// 移动
        /// </summary>
        public const int Move = 860329841;
		/// <summary>
        /// 摆放
        /// </summary>
        public const int Deploy = 1912314148;
		/// <summary>
        /// 组队
        /// </summary>
        public const int TeamTitle = 464954592;
		/// <summary>
        /// 茅草
        /// </summary>
        public const int TwigGrade = 99705803;
		/// <summary>
        /// 木头
        /// </summary>
        public const int WoodGrade = 2118090720;
		/// <summary>
        /// 石头
        /// </summary>
        public const int StoneGrade = 209620086;
		/// <summary>
        /// 金属
        /// </summary>
        public const int MetalGrade = 1383992498;
		/// <summary>
        /// 钢
        /// </summary>
        public const int ToptierGrade = 947253104;
		/// <summary>
        /// 默认建造材质
        /// </summary>
        public const int ConstructionDefaultGrade = 287696338;
		/// <summary>
        /// 摆放中
        /// </summary>
        public const int CampingTentCountdownTip = 1520827297;
		/// <summary>
        /// 最近解锁
        /// </summary>
        public const int LatelyUnLockSurvival = 1259733863;
		/// <summary>
        /// [color=#FD6F32]退出视为失败，受到生命值扣除惩罚[/color]
        /// </summary>
        public const int SwipeCardGameGiveUp = 1565064490;
		/// <summary>
        /// 正在帐篷中睡觉
        /// </summary>
        public const int CampingTentSleepTips = 192697052;
		/// <summary>
        /// 保护时长剩余：[color=#ff7900]{0}[/color]
        /// </summary>
        public const int CampingTentProtectTips = 1426104936;
		/// <summary>
        /// 已解锁，前往建造
        /// </summary>
        public const int TechTreeJumpBuildTip = 750495123;
		/// <summary>
        /// 卸下配件
        /// </summary>
        public const int LiftingPlatformUninstallParts = 1554640339;
		/// <summary>
        /// 清空物品
        /// </summary>
        public const int LiftingPlatformClearItems = 680460704;
		/// <summary>
        /// 维修以下模块:
        /// </summary>
        public const int LiftingPlatformRepairModule = 1598084524;
		/// <summary>
        /// 维修所需材料:
        /// </summary>
        public const int LiftingPlatformRepairMat = 1848717040;
		/// <summary>
        /// 维修材料不足
        /// </summary>
        public const int LiftingPlatformRepairMatNotEnough = 2022067460;
		/// <summary>
        /// 保护时长：[color=#ff7900]{0}[/color]
        /// </summary>
        public const int PlayTipDefaultText = 142126791;
		/// <summary>
        /// 带工具柜
        /// </summary>
        public const int BlueprintTipDes1 = 1220388103;
		/// <summary>
        /// 不带工具柜
        /// </summary>
        public const int BlueprintTipDes2 = 1743841786;
		/// <summary>
        /// 快捷邀请
        /// </summary>
        public const int UiTeamTabQuickInvite = 357237309;
		/// <summary>
        /// 大厅招募
        /// </summary>
        public const int UiTeamTabTeamHall = 1760145455;
		/// <summary>
        /// 推荐队友
        /// </summary>
        public const int UiTeamTabRecommendFriend = 1514394794;
		/// <summary>
        /// 邀请信息
        /// </summary>
        public const int UiTeamTabInviteInfo = 1942851069;
		/// <summary>
        /// 我的小队
        /// </summary>
        public const int UiTeamTabMyTeam = 1947326971;
		/// <summary>
        /// 夺宝行动
        /// </summary>
        public const int SettingTreasureTask = 492944848;
		/// <summary>
        /// 化油器
        /// </summary>
        public const int EnginePartSlotCarburator = 1657486070;
		/// <summary>
        /// 曲轴
        /// </summary>
        public const int EnginePartSlotCrankshafts = 1217237265;
		/// <summary>
        /// 活塞
        /// </summary>
        public const int EnginePartSlotPistons = 547783901;
		/// <summary>
        /// 火花塞
        /// </summary>
        public const int EnginePartSlotSpark = 715219363;
		/// <summary>
        /// 气阀
        /// </summary>
        public const int EnginePartSlotValves = 1247362119;
		/// <summary>
        /// 取消制作
        /// </summary>
        public const int CancelCraft = 1589336293;
		/// <summary>
        /// 当前正在制作队列中等待制作
        /// </summary>
        public const int CraftInQueue = 1675985187;
		/// <summary>
        /// 当前正在制作中
        /// </summary>
        public const int Crafting = 694345054;
		/// <summary>
        /// 等待制作中
        /// </summary>
        public const int WaitCraft = 326682626;
		/// <summary>
        /// 制作完成待领取
        /// </summary>
        public const int CraftCompleteWaitGet = 385760979;
		/// <summary>
        /// 前往领取
        /// </summary>
        public const int GoToGet = 1211257792;
		/// <summary>
        /// 当前一直在完成，在制作队列中等待领取
        /// </summary>
        public const int CraftCompleteInQueueWaitGet = 223461311;
		/// <summary>
        /// 模式：{0}
        /// </summary>
        public const int UiTeamInviteItemGameModel = 618446194;
		/// <summary>
        /// 来源：{0}
        /// </summary>
        public const int UiTeamInviteItemSource = 1799301110;
		/// <summary>
        /// 内含配件
        /// </summary>
        public const int LiftingPlatformContainerParts = 1132389031;
		/// <summary>
        /// 内含物品
        /// </summary>
        public const int LiftingPlatformContainerItems = 940738548;
		/// <summary>
        /// 该摆件尚未解锁
        /// </summary>
        public const int DeployLock = 425346426;
		/// <summary>
        /// 制作中{0}
        /// </summary>
        public const int CraftingCd = 2069070913;
		/// <summary>
        /// 大厅
        /// </summary>
        public const int UiTeamFriendStateOnline = 1502632448;
		/// <summary>
        /// 大厅队伍
        /// </summary>
        public const int UiTeamFriendStateLobbyTeam = 942873628;
		/// <summary>
        /// 游戏中
        /// </summary>
        public const int UiTeamFriendStateModeGame = 1376718437;
		/// <summary>
        /// 开启语音
        /// </summary>
        public const int UiTeamOpenMicPopTitle = 2042928270;
		/// <summary>
        /// 加入队伍需要先开启语音，是否开启？
        /// </summary>
        public const int UiTeamOpenMicPopMsg = 1462857535;
		/// <summary>
        /// 赋予大厅队友邀请其他人的权限，当您通过【开始游戏】进入战局后队友邀请其他人的权限将会被收回
        /// </summary>
        public const int UiTeamGivePermission = 416263615;
		/// <summary>
        /// 不是当前连接的目标设备
        /// </summary>
        public const int CheckWireHint1 = 1977692852;
		/// <summary>
        /// 请将视线对准场景中需要查看的水利设备
        /// </summary>
        public const int CheckWireHint2 = 320461029;
		/// <summary>
        /// 请将视线对准场景中需要查看的电利设备
        /// </summary>
        public const int CheckWireHint3 = 1647745709;
		/// <summary>
        /// 默认排序
        /// </summary>
        public const int SortTitleDefault = 1042771490;
		/// <summary>
        /// 获得时间
        /// </summary>
        public const int SortTitleGainTime = 1177665150;
		/// <summary>
        /// 耗水量
        /// </summary>
        public const int WaterConsumption = 1971304928;
		/// <summary>
        /// 大厅登录失败
        /// </summary>
        public const int LobbyLoginFail = 1418950677;
		/// <summary>
        /// 历史战局数量:{0}
        /// </summary>
        public const int OldBattleServerNum = 1982009464;
		/// <summary>
        /// 未通电
        /// </summary>
        public const int NoConnected = 1912057833;
		/// <summary>
        /// 运行中
        /// </summary>
        public const int InOperation = 449230312;
		/// <summary>
        /// 电流不足
        /// </summary>
        public const int AbnormalPower = 731962748;
		/// <summary>
        /// 无电流
        /// </summary>
        public const int NoPower = 525852872;
		/// <summary>
        /// 阶段持续时间:{0}
        /// </summary>
        public const int StoryStageChangeCD = 1818457350;
		/// <summary>
        /// 创建队伍
        /// </summary>
        public const int UiTeamCreateTitle = 416526184;
		/// <summary>
        /// 载具残骸
        /// </summary>
        public const int VehicleCorpse = 536247723;
		/// <summary>
        /// 奖励预览
        /// </summary>
        public const int RewardPreview = 1915519912;
		/// <summary>
        /// 解锁并领取
        /// </summary>
        public const int UnlockAndGet = 1694638390;
		/// <summary>
        /// 战局奖励
        /// </summary>
        public const int InGameReward = 39961773;
		/// <summary>
        /// 账号奖励
        /// </summary>
        public const int OutGameReward = 858422194;
		/// <summary>
        /// {0}级可获得
        /// </summary>
        public const int CanGetLevel = 213949891;
		/// <summary>
        /// 当前准心目标已超出最大线缆长度，请缩减移动范围
        /// </summary>
        public const int WireTipStateTooFar = 447055826;
		/// <summary>
        /// 请将视线移动到可以布线的区域
        /// </summary>
        public const int WireTipStateNoSurface = 479863806;
		/// <summary>
        /// 该设备无可连接接口
        /// </summary>
        public const int WireTipStateNoAvailableSlot = 1877578966;
		/// <summary>
        /// 发布招募
        /// </summary>
        public const int UiTeamPublishRecruit = 1907903423;
		/// <summary>
        /// 修改招募
        /// </summary>
        public const int UiTeamModifyRecruit = 2027506611;
		/// <summary>
        /// 创建队伍并发布招募
        /// </summary>
        public const int UiTeamCreateTeamAndModifyRecruit = 171675527;
		/// <summary>
        /// 领地剩余摆放数量：[color=#FD5252]{0}/[/color]{1}
        /// </summary>
        public const int RemainCount3 = 1696331880;
		/// <summary>
        /// 领地剩余摆放数量：[color=#FFFFFF]{0}/[/color]{1}
        /// </summary>
        public const int RemainCount4 = 1952125537;
		/// <summary>
        /// 大厅队伍申请列表
        /// </summary>
        public const int UiTeamApplyListTitle = 1429153023;
		/// <summary>
        /// 请将视线对准场景中需要编辑的对象
        /// </summary>
        public const int BuildEditNoTarget = 2083069453;
		/// <summary>
        /// 请将视线对准场景中的电力设备
        /// </summary>
        public const int BuildWireNoElectricTarget1 = 947450951;
		/// <summary>
        /// 请将视线对准场景中需要连接的目标电力设备
        /// </summary>
        public const int BuildWireNoElectricTarget2 = 1861829739;
		/// <summary>
        /// 请将视线对准场景中的水利设备
        /// </summary>
        public const int BuildWireNoWaterTarget1 = 2036634984;
		/// <summary>
        /// 请将视线对准场景中需要连接的目标水利设备
        /// </summary>
        public const int BuildWireNoWaterTarget2 = 1497604794;
		/// <summary>
        /// 保持视线对准在该设备上，即将进入自动连线...
        /// </summary>
        public const int IntelligentWireLockTarget = 635625497;
		/// <summary>
        /// 移开准心可取消自动连接
        /// </summary>
        public const int IntelligentWireCancelConnect = 815711850;
		/// <summary>
        /// 目标接口已被占用，是否替换已有连接？
        /// </summary>
        public const int IntelligentWireReplace = 100977018;
		/// <summary>
        /// 自动连接中...
        /// </summary>
        public const int IntelligentWireConnecting = 1125750964;
		/// <summary>
        /// 等待确认操作中...
        /// </summary>
        public const int IntelligentWireWaitConfirm = 1397707837;
		/// <summary>
        /// 自动接线模式下无法手动切换接口，是否切换为手动接线？
        /// </summary>
        public const int IntelligentWireSwitchHandMode = 270226448;
		/// <summary>
        /// 切换手动接线
        /// </summary>
        public const int SwitchHandWireMode = 1979897644;
		/// <summary>
        /// 跳过
        /// </summary>
        public const int NewbieLevelSkip = 49571851;
		/// <summary>
        /// 是否跳过新手关(仅开发模式)
        /// </summary>
        public const int NewbieLevelSkipDesc = 520245376;
		/// <summary>
        /// 为了更好游戏体验，我们已经将帧率降为合适的帧率。
        /// </summary>
        public const int SettingAdjustFPS = 1240867524;
		/// <summary>
        /// 为了更好游戏体验，我们已经将画质降为合适的画质。
        /// </summary>
        public const int SettingAdjustQuality = 221530307;
		/// <summary>
        /// 共享冷却中
        /// </summary>
        public const int ShareInCD = 1887311557;
		/// <summary>
        /// 冷却中
        /// </summary>
        public const int InCD = 1466738005;
		/// <summary>
        /// 领地摧毁倒计时:{0}
        /// </summary>
        public const int DeadSheepAttackRemainTime = 709446685;
		/// <summary>
        /// {0}后可占领
        /// </summary>
        public const int DeadSheepCaptureRemainTime = 1127025717;
		/// <summary>
        /// 掠夺核心层战利品箱{0}/{1}
        /// </summary>
        public const int DeadSheepInnerSpoils = 1311497393;
		/// <summary>
        /// 掠夺外围战利品箱{0}/{1}
        /// </summary>
        public const int DeadSheepOutterSpoils = 1090161416;
		/// <summary>
        /// 死羊任务目标
        /// </summary>
        public const int DeadSheepTaskTitle = 790141615;
		/// <summary>
        /// 可占领
        /// </summary>
        public const int CanCaptured = 1988482432;
		/// <summary>
        /// 不可占领
        /// </summary>
        public const int CannotCaptured = 2075632567;
		/// <summary>
        /// 摧毁领地柜
        /// </summary>
        public const int DestroyTerritoryConstruction = 1330984225;
		/// <summary>
        /// 目标接口已被占用，你可以替换连接，或者切换为其他接口
        /// </summary>
        public const int WireReplace = 1679025530;
		/// <summary>
        /// 替换连接
        /// </summary>
        public const int BtnWireReplaceDesc = 1882072128;
		/// <summary>
        /// 正在寻找战局请稍等
        /// </summary>
        public const int Matching = 1784238465;
		/// <summary>
        /// 好友请求
        /// </summary>
        public const int FriendRequest = 778378329;
		/// <summary>
        /// {0}级解锁
        /// </summary>
        public const int UnlockLevel = 1589034729;
		/// <summary>
        /// 徽章
        /// </summary>
        public const int BadgeTypeStr = 1853951893;
		/// <summary>
        /// 进入历史战局您将退出当前大厅的组队，是否进入？
        /// </summary>
        public const int ExitTeamToHistoryBattle = 688439721;
		/// <summary>
        /// 当前对象已升至最高等级且无法再进行编辑改造
        /// </summary>
        public const int TargetIsTopGradeCantChange = 2048288788;
		/// <summary>
        /// 接受组队您将直接跳转到大厅队伍界面，请确保战局安全再加入队伍
        /// </summary>
        public const int InviteMatchTip = 1041214018;
		/// <summary>
        /// 情报工具柜（保险柜）
        /// </summary>
        public const int IntelligenceToolCarbinetSafety = 1109467272;
		/// <summary>
        /// 工具柜（保险柜）
        /// </summary>
        public const int IntelligenceToolSafety = 1694655987;
		/// <summary>
        /// 槽位
        /// </summary>
        public const int LiftingPlatformSlot = 2011991463;
		/// <summary>
        /// 每天{0}点允许领地突袭，其余时间领地内建筑无敌
        /// </summary>
        public const int RaidRule = 1346638161;
		/// <summary>
        /// (已关闭)
        /// </summary>
        public const int CabinetClose = 1096711782;
		/// <summary>
        /// 皮肤
        /// </summary>
        public const int CraftSkin = 638149425;
		/// <summary>
        /// 已领取
        /// </summary>
        public const int Received = 1967339992;
		/// <summary>
        /// 前往
        /// </summary>
        public const int GoTo = 1944217366;
		/// <summary>
        /// 未开始
        /// </summary>
        public const int NotStart = 1457828192;
		/// <summary>
        /// 进行中
        /// </summary>
        public const int Doing = 1278080384;
		/// <summary>
        /// 领取
        /// </summary>
        public const int Receive = 1850036728;
		/// <summary>
        /// 全天开放
        /// </summary>
        public const int OpenByForever = 2001389707;
		/// <summary>
        /// {0}后开放
        /// </summary>
        public const int OpenByStable = 91470505;
		/// <summary>
        /// {0}后关闭
        /// </summary>
        public const int CloseByStable = 2073878905;
		/// <summary>
        /// 事件
        /// </summary>
        public const int MapEvent = 1398133049;
		/// <summary>
        /// 小时
        /// </summary>
        public const int Hour = 2062092001;
		/// <summary>
        /// 分钟
        /// </summary>
        public const int Minute = 1303299173;
		/// <summary>
        /// 情报碟片
        /// </summary>
        public const int InfoDisk = 2070161600;
		/// <summary>
        /// 情报碟片接收中。。。
        /// </summary>
        public const int InfoDiskReceive = 267788411;
		/// <summary>
        /// 全局剩余摆放数量[color=#FD5252]{0}/[/color]{1}
        /// </summary>
        public const int GlobalRemainCount1 = 60204881;
		/// <summary>
        /// 全局剩余摆放数量[color=#FFFFFF]{0}/[/color]{1}
        /// </summary>
        public const int GlobalRemainCount2 = 40211967;
		/// <summary>
        /// 阶段
        /// </summary>
        public const int StoryStage = 393888519;
		/// <summary>
        /// 情报
        /// </summary>
        public const int StoryInformation = 755357411;
		/// <summary>
        /// 附近可拾取
        /// </summary>
        public const int NearBy = 1981213636;
		/// <summary>
        /// 埋枪中...
        /// </summary>
        public const int BuriedGun = 217031076;
		/// <summary>
        /// 安装发信器中...
        /// </summary>
        public const int InstallTransmitter = 75460684;
		/// <summary>
        /// 月
        /// </summary>
        public const int Month = 550418599;
		/// <summary>
        /// 日
        /// </summary>
        public const int Noon = 21075631;
		/// <summary>
        /// 情报柜升级情报转化率+{0}%情报存储上限+{1}
        /// </summary>
        public const int CabinetLevelUp = 301484450;
		/// <summary>
        /// 此等级等于：小队中最高的情报等级；此等级影响：转化效率 和 存储上限；
        /// </summary>
        public const int CabinetLevelDesc = 1487285909;
		/// <summary>
        /// 转化效率将影响情报文件破译时长：###转化效率越高，每个情报文件破译所用时长越短；###标准情况下，初级6秒钟/中级9分钟/高级90分钟；###转化效率由情报柜等级决定，逐步提升；###情报碟片达到存储上限时，转化效率减半；[color=#FF6600] 50%[/color]：破译耗时为标准情况的一倍（初级=12秒）。[color=#969696]100%[/color]：标准情况[color=#848D5A]200%[/color]：破译耗时为标准情况的一半（初级=3秒）。
        /// </summary>
        public const int CabinetEfficientDesc = 972400269;
		/// <summary>
        /// 情报队员信息
        /// </summary>
        public const int CabinetMemberInfo = 2118770228;
		/// <summary>
        /// 情报柜等级{0}级
        /// </summary>
        public const int CabinetLevel = 474731161;
		/// <summary>
        /// 背包中没有这个物品
        /// </summary>
        public const int PackageNoItem = 544939934;
		/// <summary>
        /// 放入情报柜
        /// </summary>
        public const int CabinetInPut = 503234754;
		/// <summary>
        /// 转化效率{0}%
        /// </summary>
        public const int CabinetEfficient = 911192054;
		/// <summary>
        /// 暂无情报物品
        /// </summary>
        public const int NoInfoItem = 1932437191;
		/// <summary>
        /// 情报提交确认
        /// </summary>
        public const int InfoCommitConfirm = 2145523518;
		/// <summary>
        /// 转化时间{0}
        /// </summary>
        public const int ConversionTime = 1569957894;
		/// <summary>
        /// 破译获得
        /// </summary>
        public const int CarckGet = 810559554;
		/// <summary>
        /// {numD=1}日{numH=2}时{numM=3}分
        /// </summary>
        public const int MaintenanceTimeDetail = 1948159328;
		/// <summary>
        /// 维护时间
        /// </summary>
        public const int MaintenanceTime = 2075437417;
		/// <summary>
        /// 锁定
        /// </summary>
        public const int Lock = 444957765;
		/// <summary>
        /// 预约锁定
        /// </summary>
        public const int AppointmentLock = 850505482;
		/// <summary>
        /// 开服预览
        /// </summary>
        public const int PrepareLaunchPreview = 526785871;
		/// <summary>
        /// 预约服务器
        /// </summary>
        public const int ReserveServer = 216010195;
		/// <summary>
        /// 您确认要预约【{0}】的服务器吗？距离服务器开始还有{1}
        /// </summary>
        public const int ReserveServerTips = 2066624569;
		/// <summary>
        /// 取消匹配
        /// </summary>
        public const int CancelMatching = 1693682324;
		/// <summary>
        /// 建筑举报
        /// </summary>
        public const int SettingReportBuilding = 1185772669;
		/// <summary>
        /// {0}模式已开启!
        /// </summary>
        public const int StartMatching = 1058413305;
		/// <summary>
        /// {0}可进入
        /// </summary>
        public const int StartMatchingTime = 497009241;
		/// <summary>
        /// 木质建筑维护中
        /// </summary>
        public const int InUpkeepWooden = 2002529852;
		/// <summary>
        /// 木质建筑腐蚀中
        /// </summary>
        public const int InCorruptWooden = 1246383960;
		/// <summary>
        /// 石质建筑维护中
        /// </summary>
        public const int InUpkeepStone = 1508888908;
		/// <summary>
        /// 石质建筑腐蚀中
        /// </summary>
        public const int InCorruptStone = 2129776776;
		/// <summary>
        /// 铁质建筑维护中
        /// </summary>
        public const int InUpkeepMetal = 933658225;
		/// <summary>
        /// 铁质建筑腐蚀中
        /// </summary>
        public const int InCorruptMetal = 31068092;
		/// <summary>
        /// 钢质建筑维护中
        /// </summary>
        public const int InUpkeepSteel = 1696025769;
		/// <summary>
        /// 钢质建筑腐蚀中
        /// </summary>
        public const int InCorruptSteel = 923568327;
		/// <summary>
        /// 是否退出预约？
        /// </summary>
        public const int IsCancelAppointment = 1874277164;
		/// <summary>
        /// 预约活动已过期，点击确认以返回
        /// </summary>
        public const int AppointmentTimeoutTip = 1026824167;
		/// <summary>
        /// 备战中
        /// </summary>
        public const int PrepareForBattle = 188627297;
		/// <summary>
        /// 开始游戏
        /// </summary>
        public const int GameStart = 2031978967;
		/// <summary>
        /// 物资
        /// </summary>
        public const int BagItemType = 633016248;
		/// <summary>
        /// 种子
        /// </summary>
        public const int BagSeedType = 1273491910;
		/// <summary>
        /// 即将结束
        /// </summary>
        public const int EventEnding = 899265918;
		/// <summary>
        /// 存在剩余情报
        /// </summary>
        public const int HaveInfoItem = 991377545;
		/// <summary>
        /// 有队友短时间内多次进行组队匹配，暂时无法进行组队活动，剩余时间【{0}】
        /// </summary>
        public const int AppointmentFailedTip = 32232589;
		/// <summary>
        /// 即将开始
        /// </summary>
        public const int EventComing = 1970193381;
		/// <summary>
        /// {0}后结束
        /// </summary>
        public const int EventEndingCountDown = 536436283;
		/// <summary>
        /// {0}后开始
        /// </summary>
        public const int EventComingCountDown = 216766497;
		/// <summary>
        /// 战局结束时间
        /// </summary>
        public const int ServerEndTime = 621365895;
		/// <summary>
        /// 观察者指令
        /// </summary>
        public const int SettingMainObserver = 1487808706;
		/// <summary>
        /// 设置坐标
        /// </summary>
        public const int ObserverSetTransform = 1785430322;
		/// <summary>
        /// 传送到玩家旁
        /// </summary>
        public const int ObserverTransport = 840675171;
		/// <summary>
        /// （茅草）
        /// </summary>
        public const int Grade1 = 1230431568;
		/// <summary>
        /// （木头）
        /// </summary>
        public const int Grade2 = 1743520840;
		/// <summary>
        /// （石头）
        /// </summary>
        public const int Grade3 = 1476531871;
		/// <summary>
        /// （铁质）
        /// </summary>
        public const int Grade4 = 835158791;
		/// <summary>
        /// （钢质）
        /// </summary>
        public const int Grade5 = 1468043682;
		/// <summary>
        /// 确认
        /// </summary>
        public const int TreasureTaskQuit = 1585468442;
		/// <summary>
        /// 我再想想
        /// </summary>
        public const int TreasureTaskBack = 1024676784;
		/// <summary>
        /// 工具柜
        /// </summary>
        public const int TerritoryToolcupBoard = 1750533150;
		/// <summary>
        /// 保险柜
        /// </summary>
        public const int TerritorySafeBox = 471370770;
		/// <summary>
        /// 批量升级
        /// </summary>
        public const int TerritoryBatchUpgrade = 2065965206;
		/// <summary>
        /// 批量修复
        /// </summary>
        public const int TerritoryBatchRecover = 1564813984;
		/// <summary>
        /// 无效数据，断开连接
        /// </summary>
        public const int InvalidReceived = 2057522260;
		/// <summary>
        /// 无法升级或改造为低于当前材质或与当前目标相同的对象
        /// </summary>
        public const int InvalidOperation = 1831432122;
		/// <summary>
        /// 更新完成，请点击按钮重启程序。
        /// </summary>
        public const int ClientNeedReboot = 525747364;
		/// <summary>
        /// 刷新次数，每天会补充至上限刷新任务时消耗1
        /// </summary>
        public const int TreasureMissionRefreshExplanation = 1048158528;
		/// <summary>
        /// 线索数量，每天会补充1个直到上限接取任务时消耗1
        /// </summary>
        public const int TreasureMissionPropExplanation = 1836483095;
		/// <summary>
        /// (打点总数:{0})
        /// </summary>
        public const int WirePointCount = 1314077655;
		/// <summary>
        /// 拆除保险柜
        /// </summary>
        public const int DestroySafeBox = 1107992269;
		/// <summary>
        /// 无法拆除
        /// </summary>
        public const int CantDestroySafeBox = 444966847;
		/// <summary>
        /// 情报柜等级{0}级
        /// </summary>
        public const int CabinetLv = 346722147;
		/// <summary>
        /// {0}后提升至{1}级
        /// </summary>
        public const int NextWorldLevel = 1201766800;
		/// <summary>
        /// 情报徽章({0}/{1})
        /// </summary>
        public const int InfoBadgeNum = 956955018;
		/// <summary>
        /// 情报破译
        /// </summary>
        public const int InfoCarck = 1358011452;
		/// <summary>
        /// 存储上限
        /// </summary>
        public const int DiskSaveLimit = 1596800833;
		/// <summary>
        /// 队友信息
        /// </summary>
        public const int MemberInfo = 431660034;
		/// <summary>
        /// 查看信息
        /// </summary>
        public const int ViewInfo = 213848223;
		/// <summary>
        /// 兑换
        /// </summary>
        public const int Exchange = 545220369;
		/// <summary>
        /// 仅限队长兑换
        /// </summary>
        public const int OnlyCaptainCanExchange = 1059395073;
		/// <summary>
        /// 风险记录
        /// </summary>
        public const int RiskRecord = 1502683705;
		/// <summary>
        /// 模拟器检测标题
        /// </summary>
        public const int EmulatorCheckTitle = 1312682859;
		/// <summary>
        /// 模拟器检测提示内容
        /// </summary>
        public const int EmulatorCheckMsg = 815920107;
		/// <summary>
        /// 确定
        /// </summary>
        public const int EmulatorCheckConfirm = 1482982419;
		/// <summary>
        /// [color=#FF7132]【预约】[/color]
        /// </summary>
        public const int UiTeamAppointment = 891431699;
		/// <summary>
        /// 不消耗任何材料
        /// </summary>
        public const int NoConsume = 820560593;
		/// <summary>
        /// 退出预约
        /// </summary>
        public const int QuitReservationMode = 1037321756;
		/// <summary>
        /// 预约时间：
        /// </summary>
        public const int UiTeamAppointmentTime = 377506044;
		/// <summary>
        /// 退出游戏
        /// </summary>
        public const int ExitGame = 1211129722;
		/// <summary>
        /// 更新
        /// </summary>
        public const int UpdateGame = 548424652;
		/// <summary>
        /// 建筑腐蚀中
        /// </summary>
        public const int ToolcupBoradDecaying = 2097763721;
		/// <summary>
        /// 维护时间
        /// </summary>
        public const int ToolcupBoradUpkeep = 1170692297;
		/// <summary>
        /// 退出队伍
        /// </summary>
        public const int QuitTeam = 623958904;
		/// <summary>
        /// 踢出队伍
        /// </summary>
        public const int KickOut = 1992720133;
		/// <summary>
        /// 邀请权限
        /// </summary>
        public const int InvitePermission = 120046438;
		/// <summary>
        /// 放入{0}小时
        /// </summary>
        public const int ToolcupBoardHourPut = 883453148;
		/// <summary>
        /// 将基地内的所有建筑升级到选中材质
        /// </summary>
        public const int BatchUpgradeAllDesc = 947766003;
		/// <summary>
        /// 根据背包和领地柜内的材料智能计算可升级的建筑
        /// </summary>
        public const int BatchUpgradeMaxDesc = 1998468839;
		/// <summary>
        /// 可以自定义选择你想要升级的建筑体
        /// </summary>
        public const int BatchUpgradeCustomDesc = 500533208;
		/// <summary>
        /// 修复领地下所有受损的子建筑、结构造物和摆件残骸
        /// </summary>
        public const int BatchRecoverAllDesc = 263001166;
		/// <summary>
        /// 可部分选择子建筑或者摆件残骸进行单独修复
        /// </summary>
        public const int BatchRecoverBaseDesc = 834563812;
		/// <summary>
        /// [color=#ba5427]请输入名字，限制4-16字符[/color]
        /// </summary>
        public const int CreateRoleHintNameInput = 733339518;
		/// <summary>
        /// 男
        /// </summary>
        public const int SexMan = 1975295857;
		/// <summary>
        /// 女
        /// </summary>
        public const int SexWoman = 1801990054;
		/// <summary>
        /// 此版本已不可用
        /// </summary>
        public const int ClientNotAvailable = 319869114;
		/// <summary>
        /// 研究台
        /// </summary>
        public const int Blueprint = 440333856;
		/// <summary>
        /// 修理台
        /// </summary>
        public const int RepairBench = 1263459701;
		/// <summary>
        /// 已拥有:{0}
        /// </summary>
        public const int HaveItemAmount = 1652370180;
		/// <summary>
        /// 怪物
        /// </summary>
        public const int GlobalRepairMonster = 519417413;
		/// <summary>
        /// 载具
        /// </summary>
        public const int GlobalRepairVehicle = 494107265;
		/// <summary>
        /// 敌对玩家
        /// </summary>
        public const int GlobalRepairPlayer = 1469370107;
		/// <summary>
        /// 队伍暂无贡献记录
        /// </summary>
        public const int NoRiskRecord = 1000668244;
		/// <summary>
        /// {name=多管火箭弹} [color=#FFFFFF]{changeSign=-}{changeNum= 123}[/color]
        /// </summary>
        public const int ItemSplitDefaultTxt = 138206539;
		/// <summary>
        /// 我再想想
        /// </summary>
        public const int CreateRoleCancel = 1282446323;
		/// <summary>
        /// 已进入据点中，开始追踪据点任务
        /// </summary>
        public const int EnterMonumentTips = 89106284;
		/// <summary>
        /// 新任务
        /// </summary>
        public const int NewTaskTips = 684956533;
		/// <summary>
        /// 呵护双眼 请您休息一下
        /// </summary>
        public const int AddictionTips1 = 83020688;
		/// <summary>
        /// 依据国家新闻出版署《关于防止未成年人沉迷网络游戏工作的通知》与《关于进一步严格管理切实防止未成年人沉迷网络游戏的通知》的相关规定，未成年人用户除周五、周六、周日及法定节假日的20时至21时外其他时间均不可登录游戏。
        /// </summary>
        public const int AddictionTips2 = 277407656;
		/// <summary>
        /// 依据国家新闻出版署《关于防止未成年人沉迷网络游戏工作的通知》与《关于进一步严格管理 切实防止未成年人沉迷网络游戏的通知》的相关规定，今日在线时间已超过时限，当前无法进入游戏。请合理安排时间，享受健康生活。
        /// </summary>
        public const int AddictionTips3 = 1970812242;
		/// <summary>
        /// 依据国家新闻出版署《关于防止未成年人沉迷网络游戏工作的通知》与《关于进一步严格管理切实防止未成年人沉迷网络游戏的通知》的相关规定，今日在线时间已超过时限，当前无法进入游戏。请合理安排时间，享受健康生活。
        /// </summary>
        public const int AddictionTips4 = 1079135280;
		/// <summary>
        /// 您今日已累计在线{0}小时，请休息15分钟，合理安排时间，享受健康生活。
        /// </summary>
        public const int AddictionTips5 = 1034787956;
		/// <summary>
        /// 您已被纳入防沉迷系统，依据相关规定，该账号不能付费充值。
        /// </summary>
        public const int AddictionTips6 = 2082741413;
		/// <summary>
        /// 您已被纳入防沉迷系统，依据相关规定，该账号单次充值金额不得超过50元人民币，每月充值金额累计不得超过200元人民币。
        /// </summary>
        public const int AddictionTips7 = 445660720;
		/// <summary>
        /// 您已被纳入防沉迷系统，依据相关规定，该账号单次充值金额不得超过100元人民币，每月充值金额累计不得超过400元人民币。
        /// </summary>
        public const int AddictionTips8 = 1855433496;
		/// <summary>
        /// 预约时间已失效，已无法通过预约队伍进入战局。点击【确定】会自动退出当前队伍。
        /// </summary>
        public const int ReservationModeTimeOut = 1914795237;
		/// <summary>
        /// 更改容器名字
        /// </summary>
        public const int ChangeContainerName = 1560652390;
		/// <summary>
        /// 每日任务重置:{0}时{1}分{2}秒
        /// </summary>
        public const int DailyTaskResetTime = 1921368940;
		/// <summary>
        /// 已满级
        /// </summary>
        public const int CabinetMaxLevel = 918188708;
		/// <summary>
        /// [color=#53e1ff]德拉科[/color]：您的[color=#ff9f4d]新档案[/color]已创建完毕，请使用此身份立即执行潜入任务。
        /// </summary>
        public const int CreateRoleDialogue01 = 1668685342;
		/// <summary>
        /// 事件未开放
        /// </summary>
        public const int BeeBuzzTaskNotOpen = 460572401;
		/// <summary>
        /// 鼓掌
        /// </summary>
        public const int DanceClapping = 137773401;
		/// <summary>
        /// 点赞
        /// </summary>
        public const int DanceLike = 688882097;
		/// <summary>
        /// 摊手
        /// </summary>
        public const int DanceShrugginghands = 1300493745;
		/// <summary>
        /// 装备
        /// </summary>
        public const int BagEquipType = 1006514910;
		/// <summary>
        /// 生存道具
        /// </summary>
        public const int BagSurvivalItemType = 156853285;
		/// <summary>
        /// 快捷栏
        /// </summary>
        public const int BagShortcutBarType = 1770200669;
		/// <summary>
        /// 扩容背包
        /// </summary>
        public const int BagExtendInventoryType = 613216820;
		/// <summary>
        /// 远程武器
        /// </summary>
        public const int BagRangedWeaponType = 676710890;
		/// <summary>
        /// 全天保护：全天领地内建筑无敌
        /// </summary>
        public const int MainMapRaidDefenseTips = 563949861;
		/// <summary>
        /// 全天抄家：全天允许领地突袭
        /// </summary>
        public const int MainMapRaidAttackTips = 1811590361;
		/// <summary>
        /// 全天
        /// </summary>
        public const int MainMapRaidDayTips = 113389518;
		/// <summary>
        /// 情报等级{0}级
        /// </summary>
        public const int ReputationLevel = 1199864338;
		/// <summary>
        /// 您将跳过精彩剧情，是否继续？
        /// </summary>
        public const int NewbieLevelSkipVideoDesc = 923377884;
		/// <summary>
        /// 是
        /// </summary>
        public const int CommonTrue = 475165862;
		/// <summary>
        /// 否
        /// </summary>
        public const int CommonFalse = 1032295179;
		/// <summary>
        /// 已兑换
        /// </summary>
        public const int BeeBuzzHasExchanged = 727136894;
		/// <summary>
        /// 打开Proxima
        /// </summary>
        public const int OpenProxima = 1165020508;
		/// <summary>
        /// {0:N0}时 {1:N0}分 {2:N0}秒
        /// </summary>
        public const int ElectricInfoHMS = 1327076346;
		/// <summary>
        /// {0:N0}分 {1:N0}秒
        /// </summary>
        public const int ElectricInfoMS = 927717070;
		/// <summary>
        /// {0:N0}秒
        /// </summary>
        public const int ElectricInfoS = 1765264923;
		/// <summary>
        /// {0}/{1} 瓦分钟
        /// </summary>
        public const int ElectricInforWm = 1229391626;
		/// <summary>
        /// 前往查看
        /// </summary>
        public const int GoToView = 1989936803;
		/// <summary>
        /// 更新失败({0})
        /// </summary>
        public const int DolphinUpdateFailed = 436681846;
		/// <summary>
        /// 自动售货机
        /// </summary>
        public const int VendingMachine = 1599508233;
		/// <summary>
        /// 道具
        /// </summary>
        public const int LobbyStashTabProp = 301351189;
		/// <summary>
        /// 最近获得
        /// </summary>
        public const int LobbyStashTabNewly = 1996642545;
		/// <summary>
        /// 限时道具
        /// </summary>
        public const int LobbyStashTabLimitProp = 1629381589;
		/// <summary>
        /// 礼包
        /// </summary>
        public const int LobbyStashTabGift = 264583629;
		/// <summary>
        /// 皮肤
        /// </summary>
        public const int LobbyStashTabSkin = 1624901576;
		/// <summary>
        /// 局内
        /// </summary>
        public const int LobbyStashTabBattle = 1467784067;
		/// <summary>
        /// 使用后获得下列资源
        /// </summary>
        public const int LobbyStashUsePropTip1 = 1940754736;
		/// <summary>
        /// 使用后从下列资源中随机获得
        /// </summary>
        public const int LobbyStashUsePropTip2 = 1526885736;
		/// <summary>
        /// 道具详情
        /// </summary>
        public const int ItemDetail = 2080106500;
		/// <summary>
        /// 游戏模式
        /// </summary>
        public const int GameMode = 503976671;
		/// <summary>
        /// 科技树
        /// </summary>
        public const int TechTree = 1614140463;
		/// <summary>
        /// ？？
        /// </summary>
        public const int LobbyStashRandomTip = 1201211380;
		/// <summary>
        /// 状态
        /// </summary>
        public const int PlayerViewer = 1043149693;
		/// <summary>
        /// 大厅
        /// </summary>
        public const int MailTabTitle1 = 1217761369;
		/// <summary>
        /// 战局
        /// </summary>
        public const int MailTabTitle2 = 198501868;
		/// <summary>
        /// 邮件数：{0}/{1}
        /// </summary>
        public const int MailNums = 1856859533;
		/// <summary>
        /// 商店管理
        /// </summary>
        public const int StoreManager = 607272922;
		/// <summary>
        /// 睡袋赠予
        /// </summary>
        public const int GiveBed = 56612774;
		/// <summary>
        /// 情报徽章
        /// </summary>
        public const int ReputationBadge = 804861029;
		/// <summary>
        /// 载具改装
        /// </summary>
        public const int LiftingPlatform = 88438917;
		/// <summary>
        /// 个人信息
        /// </summary>
        public const int NameCard = 507397995;
		/// <summary>
        /// 建筑&摆件
        /// </summary>
        public const int Building = 1628515266;
		/// <summary>
        /// 商城
        /// </summary>
        public const int Market = 796883416;
		/// <summary>
        /// 提交奖励
        /// </summary>
        public const int ReputationRecord = 734861860;
		/// <summary>
        /// 多管火箭系统
        /// </summary>
        public const int Katyusha = 1309575681;
		/// <summary>
        /// 邮箱
        /// </summary>
        public const int MailBox = 1252655140;
		/// <summary>
        /// 历史战局
        /// </summary>
        public const int HistoryBattle = 300174856;
		/// <summary>
        /// 电路盒
        /// </summary>
        public const int SwipeCardGame = 27096280;
		/// <summary>
        /// 惊喜玩法
        /// </summary>
        public const int SurprisePlay = 934050352;
		/// <summary>
        /// 收起
        /// </summary>
        public const int LobbyStashFold = 1994859703;
		/// <summary>
        /// 更多
        /// </summary>
        public const int LobbyStashMore = 37639085;
		/// <summary>
        /// 手动卸货
        /// </summary>
        public const int ManualDischarge = 1637562037;
		/// <summary>
        /// 忽略所有UI锁定
        /// </summary>
        public const int IgnoreAllUILockSwitch = 1447700511;
		/// <summary>
        /// 车厢货物折损50%
        /// </summary>
        public const int ManualDischargeDes = 130455610;
		/// <summary>
        /// 完整卸货
        /// </summary>
        public const int PermissionCardDes = 1282420323;
		/// <summary>
        /// 可额外获得车厢内物品
        /// </summary>
        public const int HighPermissionCardDes = 1328542246;
		/// <summary>
        /// 目标距离：{0}m
        /// </summary>
        public const int LockMissileDistance = 541388909;
		/// <summary>
        /// 多语言更新测试202503201758
        /// </summary>
        public const int SocTextUpdateTest = 4538537;
		/// <summary>
        /// 修复中......
        /// </summary>
        public const int GameInFix = 1032052298;
		/// <summary>
        /// 已过期
        /// </summary>
        public const int LobbyStashTimeOut = 461446969;
		/// <summary>
        /// {0}不足
        /// </summary>
        public const int InsufficientCoin = 1790647984;
		/// <summary>
        /// 使用{0}兑换{1}
        /// </summary>
        public const int CurrencyExchange = 1633793743;
		/// <summary>
        /// 时装
        /// </summary>
        public const int CostumeName = 7623294;
		/// <summary>
        /// 开启录屏模式
        /// </summary>
        public const int StartScreenCap = 1224276681;
		/// <summary>
        /// 补齐{0}
        /// </summary>
        public const int CompleteCurrency = 1535096137;
		/// <summary>
        /// 前往充值
        /// </summary>
        public const int GoToRecharge = 722814870;
		/// <summary>
        /// 以下奖励自动发放至账户
        /// </summary>
        public const int UiCommonRewardTips1 = 1755697089;
		/// <summary>
        /// 前往官网
        /// </summary>
        public const int GoToOfficialWeb = 1995607006;
		/// <summary>
        /// 自行解锁科技
        /// </summary>
        public const int TechTreeUnLockTitle = 572344038;
		/// <summary>
        /// 队友：{0}
        /// </summary>
        public const int TechTreeUnLockTip = 492587931;
		/// <summary>
        /// 信号传输中...
        /// </summary>
        public const int SignalAtHeightsChanneling = 74692718;
		/// <summary>
        /// 打捞中...
        /// </summary>
        public const int SalvageChanneling = 1551276850;
		/// <summary>
        /// 挖掘中...
        /// </summary>
        public const int ExploreTheCaveChanneling = 970662898;
		/// <summary>
        /// 种植
        /// </summary>
        public const int Plant = 1145667942;
		/// <summary>
        /// 杂交
        /// </summary>
        public const int Hybrid = 1573506269;
		/// <summary>
        /// 是否批量使用
        /// </summary>
        public const int LobbyStashBatchUsage = 2090563667;
		/// <summary>
        /// 添加弹药
        /// </summary>
        public const int AddAmmo = 723068048;
		/// <summary>
        /// 添加燃料
        /// </summary>
        public const int AddFuel = 618749260;
		/// <summary>
        /// 第{0}节车厢
        /// </summary>
        public const int TrainCargoIndex = 2038226307;
		/// <summary>
        /// (已上锁)
        /// </summary>
        public const int Locked = 1674026128;
		/// <summary>
        /// 等级达到{0}级开启
        /// </summary>
        public const int LobbyLevelLimit = 1651520108;
		/// <summary>
        /// 社群
        /// </summary>
        public const int TribeTitle = 160395354;
		/// <summary>
        /// 我的社群
        /// </summary>
        public const int TribeMine = 1160221320;
		/// <summary>
        /// 寻找社群
        /// </summary>
        public const int TribeSearch = 716292987;
		/// <summary>
        /// 创建社群
        /// </summary>
        public const int CreateTribe = 1214288173;
		/// <summary>
        /// 社群标签({0}/{1})
        /// </summary>
        public const int TribeTagNum = 1959715730;
		/// <summary>
        /// 邀请成员({0}/{1})
        /// </summary>
        public const int TribeInviteMembersNum = 1381598204;
		/// <summary>
        /// 分解时间:
        /// </summary>
        public const int RecycleTime = 961411631;
		/// <summary>
        /// 概率获得
        /// </summary>
        public const int ProbabilityGet = 1039937454;
		/// <summary>
        /// 剩余刷新次数：{0}
        /// </summary>
        public const int AvailableRefreshCount = 569068617;
		/// <summary>
        /// {0:D2}:{1:D2}:{2:D2}后恢复{3}条线索
        /// </summary>
        public const int TimeAfterNextClue = 981620363;
		/// <summary>
        /// 普通社群
        /// </summary>
        public const int TribeSimple = 1355479667;
		/// <summary>
        /// 超级社群
        /// </summary>
        public const int TribeSuper = 392611014;
		/// <summary>
        /// 社群名称({0}-{1}个字符)
        /// </summary>
        public const int TribeNameLimit = 320626577;
		/// <summary>
        /// 请输入社群名称
        /// </summary>
        public const int TribeNameInputTips = 1706422875;
		/// <summary>
        /// 保存穿搭
        /// </summary>
        public const int SaveToPlan = 921621626;
		/// <summary>
        /// 堆肥机
        /// </summary>
        public const int NameComposter = 635800825;
		/// <summary>
        /// 添加燃料x{0}
        /// </summary>
        public const int AddFuelDesc = 788495072;
		/// <summary>
        /// 添加弹药x{0}
        /// </summary>
        public const int AddAmmoDesc = 1651271112;
		/// <summary>
        /// 本次改名需要消耗{0}张<img src='{1}' width='39' height='39'/>[color=#ff7132]{2}[/color]
        /// </summary>
        public const int ChangeNameCost = 1398639396;
		/// <summary>
        /// 无法修改，下次修改日期{0}
        /// </summary>
        public const int ChangeNameCDDesc = 948458282;
		/// <summary>
        /// 主页
        /// </summary>
        public const int PersonalHomepageMain = 2055371776;
		/// <summary>
        /// 历史战绩
        /// </summary>
        public const int PersonalHomepageHistoryRecord = 1015167320;
		/// <summary>
        /// 卸载导弹
        /// </summary>
        public const int KatyushaUnloadMissiles = 723913978;
		/// <summary>
        /// 燃料
        /// </summary>
        public const int FuelDesc = 1901227101;
		/// <summary>
        /// 弹药
        /// </summary>
        public const int AmmoDesc = 1779669363;
		/// <summary>
        /// 更换社群头像
        /// </summary>
        public const int TribeChooseAvatarTitle = 1107369645;
		/// <summary>
        /// 社群标签
        /// </summary>
        public const int TribeChooseTagTitle = 561591711;
		/// <summary>
        /// 自定义标签
        /// </summary>
        public const int TribeCustomTagTitle = 1313236589;
		/// <summary>
        /// 保存
        /// </summary>
        public const int Save = 592905495;
		/// <summary>
        /// 请输入标签名（长度2-8）
        /// </summary>
        public const int TribeCustomTagLengthLimit = 976914749;
		/// <summary>
        /// 选择你喜欢的标签(已选{0}/{1})
        /// </summary>
        public const int TribeChooseTagNum = 1015982891;
		/// <summary>
        /// 社群创建成功后自动向所选目标人员发出邀请，对方同意后即可加入；对方是否同意不影响社群创建。
        /// </summary>
        public const int TribeInviteMemberTips = 1458285136;
		/// <summary>
        /// 我的队伍
        /// </summary>
        public const int LobbyTeamTitle = 2068880492;
		/// <summary>
        /// 我的预约
        /// </summary>
        public const int LobbyTeamAppointTitle = 920516218;
		/// <summary>
        /// [size=32]社群系统[/size] [size=10]  [/size]    玩家可以创建属于自己的社群并邀请朋友们加入，也可以加入其他人或是系统创建的社群。每个群有自己专属的频道，您可以更方便地与朋友们聊天；还能通过队伍分享等功能更便捷地招募队友。[size=10]  [/size]    以下是各类群的介绍：1.系统群：账号等级升到[color=#FF7132]2级[/color]时自动加入。2.自建群：账号等级[color=#FF7132]5级[/color]以上，消耗一定的资源就可以创建属于自己的社群。3.超级群：在[color=#FF7132]运营活动中可获取创建资格[/color]，超级群能容纳更多的成员，未来还会开放灯牌等更丰富功能。
        /// </summary>
        public const int TribeMainHelpTips = 384643486;
		/// <summary>
        /// {0}预约开启
        /// </summary>
        public const int LobbyTeamAppointmentTip = 1089319306;
		/// <summary>
        /// 当前据点活动的小队过多，尝试清除后再进行召唤
        /// </summary>
        public const int MonumentAlreadyHaveMonsterTips = 562800108;
		/// <summary>
        /// 头像
        /// </summary>
        public const int PlayerAvatar = 294816787;
		/// <summary>
        /// 头像框
        /// </summary>
        public const int PlayerAvatarFrame = 99646786;
		/// <summary>
        /// 名片
        /// </summary>
        public const int PlayerNameCard = 258231616;
		/// <summary>
        /// 聊天气泡
        /// </summary>
        public const int PlayerChatBubble = 1993109387;
		/// <summary>
        /// 选择合适的时间段进行游戏，大厅队伍仅可有一个，同时即将开服的时段可以选择一个进行预约
        /// </summary>
        public const int LobbyTeamAppointmentTip1 = 792635363;
		/// <summary>
        /// 研究时间:
        /// </summary>
        public const int ResearchTime = 1070142752;
		/// <summary>
        /// 活动已结束
        /// </summary>
        public const int MallCountDownFinished = 320478003;
		/// <summary>
        /// 接水中...
        /// </summary>
        public const int ContactWater = 796852966;
		/// <summary>
        /// 悬赏
        /// </summary>
        public const int IntegrationPage = 933121810;
		/// <summary>
        /// 取出
        /// </summary>
        public const int RepairOutPut = 430985242;
		/// <summary>
        /// 无法修理
        /// </summary>
        public const int RepairTip1 = 1276055137;
		/// <summary>
        /// 材料不足
        /// </summary>
        public const int RepairTip2 = 195560174;
		/// <summary>
        /// 踢出社群
        /// </summary>
        public const int TribeKickoutMate = 1002298327;
		/// <summary>
        /// 确认将{0}移出社群
        /// </summary>
        public const int TribeKickoutConfirmTip = 1130831626;
		/// <summary>
        /// 全部同意
        /// </summary>
        public const int TribeAllAgree = 1541141221;
		/// <summary>
        /// 全部拒绝
        /// </summary>
        public const int TribeAllRefuse = 832009572;
		/// <summary>
        /// 待处理邀请
        /// </summary>
        public const int TribeInviteTitle = 1286967741;
		/// <summary>
        /// {0}的加入申请
        /// </summary>
        public const int TribeAuditTitle = 1615395389;
		/// <summary>
        /// {0}毫升/{1}毫升
        /// </summary>
        public const int ContainerInfoValue1 = 1917789183;
		/// <summary>
        /// {0}毫升/分
        /// </summary>
        public const int ContainerInfoValue2 = 491064991;
		/// <summary>
        /// {0}秒/次
        /// </summary>
        public const int ContainerInfoValue3 = 1570727338;
		/// <summary>
        /// 当射频发射器与射频接收器频率一致时，可触发接收器的功能
        /// </summary>
        public const int TransmitterTip = 69171204;
		/// <summary>
        /// 当前频率:
        /// </summary>
        public const int CurFrequency = 694276466;
		/// <summary>
        /// 放入柴油
        /// </summary>
        public const int PutInDieselOil = 616855291;
		/// <summary>
        /// 未添加弹药
        /// </summary>
        public const int NoHaveAmmo = 1249650361;
		/// <summary>
        /// 未装配武器
        /// </summary>
        public const int NoEquipWeapon = 1836294308;
		/// <summary>
        /// 请先装配武器
        /// </summary>
        public const int NoEquipWeaponTips = 2056624043;
		/// <summary>
        /// 模板
        /// </summary>
        public const int PhotoTemplate = 674905715;
		/// <summary>
        /// 设置
        /// </summary>
        public const int PhotoSetting = 1349926654;
		/// <summary>
        /// 动作
        /// </summary>
        public const int PhotoGesture = 665721136;
		/// <summary>
        /// 滤镜
        /// </summary>
        public const int PhotoFilter = 134963968;
		/// <summary>
        /// 路径
        /// </summary>
        public const int PhotoRoute = 1004182833;
		/// <summary>
        /// 概率刷出
        /// </summary>
        public const int RandomRefresh = 41265402;
		/// <summary>
        /// 卸货
        /// </summary>
        public const int DisCharge = 576211618;
		/// <summary>
        /// 已拥有
        /// </summary>
        public const int MallBundleSoldOut = 1313708045;
		/// <summary>
        /// 即将上架
        /// </summary>
        public const int MallBundleNotStart = 30476117;
		/// <summary>
        /// 装配武器
        /// </summary>
        public const int EquipWeapon = 605468728;
		/// <summary>
        /// 替换武器
        /// </summary>
        public const int ChangeWeapon = 454622215;
		/// <summary>
        /// 主要
        /// </summary>
        public const int GameStyleMain = 15146907;
		/// <summary>
        /// 次要
        /// </summary>
        public const int GameStyleSecondary = 1488206023;
		/// <summary>
        /// 个性标签
        /// </summary>
        public const int PsersonalTag = 1201329707;
		/// <summary>
        /// 游戏风格
        /// </summary>
        public const int GameStyle = 398634252;
		/// <summary>
        /// 举报语音
        /// </summary>
        public const int ReportVoice = 1139100741;
		/// <summary>
        /// 举报建筑
        /// </summary>
        public const int ReportBuildings = 28860967;
		/// <summary>
        /// 每日{0:D2}:{1:D2}新增{2}条线索
        /// </summary>
        public const int RefreshClueTime = 1589505180;
		/// <summary>
        /// 购买确认
        /// </summary>
        public const int MallPayConfirmTitle = 402512614;
		/// <summary>
        /// 拴马桩
        /// </summary>
        public const int NameHitchPost = 2069170999;
		/// <summary>
        /// 日常
        /// </summary>
        public const int DailyMissionTab = 560735906;
		/// <summary>
        /// 前哨站重生
        /// </summary>
        public const int OutpostRebornPoint = 1118821696;
		/// <summary>
        /// 使用冷却{0}秒
        /// </summary>
        public const int OutpostCdHint = 806932048;
		/// <summary>
        /// 倒计时模式
        /// </summary>
        public const int TimerModeDesc = 550086814;
		/// <summary>
        /// 遥控模式
        /// </summary>
        public const int RFModeDesc = 768159674;
		/// <summary>
        /// 捆绑包
        /// </summary>
        public const int MallBuyTitleBundle = 970951479;
		/// <summary>
        /// 商品
        /// </summary>
        public const int MallBuyTitleMallItem = 318261240;
		/// <summary>
        /// 发送
        /// </summary>
        public const int TeamApplyBtnText = 1355046957;
		/// <summary>
        /// 我想和你一起组队，请看我的个人信息
        /// </summary>
        public const int TeamApplyInputText = 796438936;
		/// <summary>
        /// 当前弹药与该武器不匹配，自动炮塔将无法正常运行。是否更换弹药？
        /// </summary>
        public const int AmmoNoMatchMsg = 465462028;
		/// <summary>
        /// 弹药不匹配
        /// </summary>
        public const int AmmoNoMatchTitle = 403587428;
		/// <summary>
        /// 预约招募
        /// </summary>
        public const int UiTeamTabAppointment = 1925966274;
		/// <summary>
        /// 创建大厅队伍
        /// </summary>
        public const int CreateLobbyTeam = 1195741648;
		/// <summary>
        /// 创建预约队伍
        /// </summary>
        public const int CreateAppointmentTeam = 1248236792;
		/// <summary>
        /// 暂无大厅队伍
        /// </summary>
        public const int NoLobbyTeam = 1935271077;
		/// <summary>
        /// 暂无预约队伍
        /// </summary>
        public const int NoAppointmentTeam = 693117220;
		/// <summary>
        /// 暂未发布到组队大厅
        /// </summary>
        public const int UnPublishLobbyTeam = 900921301;
		/// <summary>
        /// 暂未发布到预约大厅
        /// </summary>
        public const int UnPublishAppointmentTeam = 35342175;
		/// <summary>
        /// 包含{0}件商品
        /// </summary>
        public const int MallBuyItemCountTips = 226266750;
		/// <summary>
        /// 礼包
        /// </summary>
        public const int MallBuyTitleFixPack = 1063119257;
		/// <summary>
        /// 已达购买上限
        /// </summary>
        public const int MallBuyMallItemSoldOut = 1996304954;
		/// <summary>
        /// 创建预约队伍并发布招募
        /// </summary>
        public const int UiTeamCreateAppointmentTeamAndModifyRecruit = 1059671219;
		/// <summary>
        /// 不限
        /// </summary>
        public const int UiTeamNoLimit = 2020842210;
		/// <summary>
        /// 更多操作
        /// </summary>
        public const int MoreOperation = 993738722;
		/// <summary>
        /// 收起
        /// </summary>
        public const int ExpandList = 965350534;
		/// <summary>
        /// 发布大厅队伍
        /// </summary>
        public const int PublishLobbyTeam = 1216516124;
		/// <summary>
        /// 发布预约队伍
        /// </summary>
        public const int PublishAppointmentTeam = 847879524;
		/// <summary>
        /// 队友倾向
        /// </summary>
        public const int RecruitPlayerTagTitle1 = 1839279506;
		/// <summary>
        /// 擅长职业
        /// </summary>
        public const int RecruitPlayerTagTitle2 = 2077440495;
		/// <summary>
        /// 预约申请列表
        /// </summary>
        public const int UiAppointmentTeamApplyListTitle = 1801532299;
		/// <summary>
        /// 分享
        /// </summary>
        public const int PhotoShareTitle = 156931495;
		/// <summary>
        /// 风格
        /// </summary>
        public const int PersonalTagType1 = 1965479469;
		/// <summary>
        /// 个性
        /// </summary>
        public const int PersonalTagType2 = 1799074243;
		/// <summary>
        /// 称号
        /// </summary>
        public const int PersonalTagType3 = 1386141581;
		/// <summary>
        /// 分类4
        /// </summary>
        public const int PersonalTagType4 = 1065955776;
		/// <summary>
        /// 选择你喜欢的标签（已选[color=#ff7132]{0}[/color]/{1}）
        /// </summary>
        public const int PersonalTagSelectNum = 841195862;
		/// <summary>
        /// 战局招募
        /// </summary>
        public const int UiTeamTabGameHall = 1892634481;
		/// <summary>
        /// 群聊
        /// </summary>
        public const int UiTeamChatSource = 1210839798;
		/// <summary>
        /// 来源：{0}
        /// </summary>
        public const int UiRecruitSource = 1484085263;
		/// <summary>
        /// 切换为计时引爆模式
        /// </summary>
        public const int C4TimerModeTips = 415052393;
		/// <summary>
        /// 切换为遥控引爆模式
        /// </summary>
        public const int C4RemoteControlModeTips = 403153207;
		/// <summary>
        /// 挖掘机
        /// </summary>
        public const int DigName = 932557932;
		/// <summary>
        /// 其它社群
        /// </summary>
        public const int OtherTribe = 171094437;
		/// <summary>
        /// 已下架
        /// </summary>
        public const int MallBuySellEnd = 44129210;
		/// <summary>
        /// 键鼠设置
        /// </summary>
        public const int SettingKeyMouse = 1489398939;
		/// <summary>
        /// 由于更换队长，是否重新发布当前队伍到{0}？
        /// </summary>
        public const int RePublishRecruit = 312865597;
		/// <summary>
        /// 修改预约招募
        /// </summary>
        public const int UiTeamModifyAppointmentRecruit = 139877994;
		/// <summary>
        /// 发布预约招募
        /// </summary>
        public const int UiTeamPublishAppointmentRecruit = 605663734;
		/// <summary>
        /// 退出大厅队伍
        /// </summary>
        public const int QuitTeamTitle = 760719422;
		/// <summary>
        /// 退出预约队伍
        /// </summary>
        public const int QuitAppointmentTeamTitle = 240925959;
		/// <summary>
        /// 进入预约战局
        /// </summary>
        public const int EnterAppointmentTeamTitle = 1096565736;
		/// <summary>
        /// 是否直接进入该战局，或您可以通过历史战局进入{0}游戏
        /// </summary>
        public const int EnterAppointmentTeamContent = 529848950;
		/// <summary>
        /// 修改形象
        /// </summary>
        public const int PlayerChangeShowAvatar = 1870074824;
		/// <summary>
        /// 创建角色
        /// </summary>
        public const int CreateRole = 186315467;
		/// <summary>
        /// 收集进度{0}/{1}
        /// </summary>
        public const int CollectProgress = 1983293043;
		/// <summary>
        /// 默认头像
        /// </summary>
        public const int DefaultPortrait = 397969853;
		/// <summary>
        /// {0}秒
        /// </summary>
        public const int Seconds = 1207506339;
		/// <summary>
        /// 你的预约情报模式还有{0}分钟开启，请尽快返回队伍！
        /// </summary>
        public const int AppointmentTeamOpenWarnTip = 1205104711;
		/// <summary>
        /// 有
        /// </summary>
        public const int HaveSignal = 195529361;
		/// <summary>
        /// 无
        /// </summary>
        public const int NoSignal = 247570728;
		/// <summary>
        /// 0
        /// </summary>
        public const int Zero = 275261001;
		/// <summary>
        /// 归还中枢
        /// </summary>
        public const int TempCofferSafeBox = 1709281669;
		/// <summary>
        /// 武器装备
        /// </summary>
        public const int SafetyBoxWeapon = 978947962;
		/// <summary>
        /// 防御装备
        /// </summary>
        public const int SafetyBoxBelt = 548569110;
		/// <summary>
        /// 通用
        /// </summary>
        public const int SafetyBoxUniverse = 1640054236;
		/// <summary>
        /// 最多可放入：{0}
        /// </summary>
        public const int MaxCanPutInDesc = 1553570077;
		/// <summary>
        /// 最多可取出：{0}
        /// </summary>
        public const int MaxCanPutOutDesc = 1313288827;
		/// <summary>
        /// 自动连线说明
        /// </summary>
        public const int AutoConnectWireTips = 947027667;
		/// <summary>
        /// 确定要退出社群吗？[color=#FF7132]退出后在群内的活跃度等数据将被清除，无法恢复[/color]
        /// </summary>
        public const int QuitTribeConfirmTips = 1459602480;
		/// <summary>
        /// 确认后会为您重新分配一个系统社群。[color=#FF7132]每24小时只能更换1次，操作完成后将无法撤销[/color]
        /// </summary>
        public const int ReplaceTribeConfirmTips = 496912680;
		/// <summary>
        /// 是否确认消耗材料创建超级群？
        /// </summary>
        public const int CreateSuperTribeConfirmTips = 379800981;
		/// <summary>
        /// 选择同频道具
        /// </summary>
        public const int SelectSameFreTitle = 282062202;
		/// <summary>
        /// 输入文字
        /// </summary>
        public const int DismissInputTips = 1458456088;
		/// <summary>
        /// 确认解散
        /// </summary>
        public const int DismissConfirm = 75150225;
		/// <summary>
        /// 确认解散{0}吗？
        /// </summary>
        public const int DismissConfirm2 = 443953106;
		/// <summary>
        /// 时段选择
        /// </summary>
        public const int LobbyTeamAppointmentBookingTitle = 538458846;
		/// <summary>
        /// 护甲
        /// </summary>
        public const int ArmorTitle = 507943805;
		/// <summary>
        /// 武器
        /// </summary>
        public const int WeaponTitle = 1320674065;
		/// <summary>
        /// {0}/{1}字符
        /// </summary>
        public const int CharLimit = 1490471119;
		/// <summary>
        /// 暂无{0}可转换
        /// </summary>
        public const int DigNoFuel = 1126808832;
		/// <summary>
        /// 预览
        /// </summary>
        public const int PreViewTitle = 414368101;
		/// <summary>
        /// 请选择装配武器
        /// </summary>
        public const int PleaseSelectWeapon = 1541758496;
		/// <summary>
        /// 请放入埋入的武器
        /// </summary>
        public const int BuryWeaponTip = 325284872;
		/// <summary>
        /// 没有可装配的武器
        /// </summary>
        public const int NoWeaponTip = 1568518134;
		/// <summary>
        /// 机型检测
        /// </summary>
        public const int DeviceCheck = 1390777132;
		/// <summary>
        /// 很抱歉，您的设备目前还未能覆盖，敬请期待。设备型号：{0}  GPU型号：{1}
        /// </summary>
        public const int UnSupportDevice = 716654226;
		/// <summary>
        /// 申请备注
        /// </summary>
        public const int UiTeamApplyDescTitle = 1954484768;
		/// <summary>
        /// 礼包
        /// </summary>
        public const int GiftPackage = 1774863577;
		/// <summary>
        /// 请选择奖励
        /// </summary>
        public const int SelectReward = 2067606848;
		/// <summary>
        /// 种植管理
        /// </summary>
        public const int PlantMainTitle = 1277043813;
		/// <summary>
        /// 每{0}天可修改一次
        /// </summary>
        public const int UiChangeNameTimeTip = 1277222576;
		/// <summary>
        /// 有道具待领取
        /// </summary>
        public const int TerritoryHaveItem = 1510892377;
		/// <summary>
        /// 等级达到{0}级开放
        /// </summary>
        public const int FactionLevelDetail = 1655054482;
		/// <summary>
        /// 招募局内玩家
        /// </summary>
        public const int PublishGameRecruitTitle = 1109047920;
		/// <summary>
        /// 详情
        /// </summary>
        public const int Detail = 215866344;
		/// <summary>
        /// [color=#FF6600]{0}[/color]秒后可用
        /// </summary>
        public const int SecondsToUse = 416470919;
		/// <summary>
        /// 床位使用后，附近的床位会一同进入冷却时间
        /// </summary>
        public const int UseSleepBag2RespawnHint = 407113852;
		/// <summary>
        /// 使用后会在淘汰点附近重生
        /// </summary>
        public const int UseNearBy2RespawnHint = 961858645;
		/// <summary>
        /// 使用后会在随机位置重生，离淘汰点较远
        /// </summary>
        public const int UseRandom2RespawnHint = 2142751312;
		/// <summary>
        /// 使用后会在前哨站安全区内重生
        /// </summary>
        public const int UseOutpost2RespawnHint = 1854281680;
		/// <summary>
        /// 由于交战状态被淘汰，暂时无法使用附近复活
        /// </summary>
        public const int NearbyRespawnCdHint = 119728374;
		/// <summary>
        /// 战局结算倒计时:{0}
        /// </summary>
        public const int StorySettlementCD = 918501241;
		/// <summary>
        /// 成功加入队伍
        /// </summary>
        public const int ApplyEnterGameTeamResultTitle = 304264550;
		/// <summary>
        /// 您的局内招募申请已通过可以通过历史战局加入游戏，现在是否直接跳转进入战局？
        /// </summary>
        public const int ApplyEnterGameTeamResultTips = 1770631566;
		/// <summary>
        /// 行动倒计时:{0}天{1}时{2}分{3}秒
        /// </summary>
        public const int StoryActionPlanCD = 1263251134;
		/// <summary>
        /// 大厅队伍
        /// </summary>
        public const int ChatRecruitType0 = 1888008134;
		/// <summary>
        /// 预约队伍
        /// </summary>
        public const int ChatRecruitType1 = 1792634116;
		/// <summary>
        /// 局内队伍
        /// </summary>
        public const int ChatRecruitType2 = 517503490;
		/// <summary>
        /// 任务
        /// </summary>
        public const int MainQuestMissionTab = 2123651704;
		/// <summary>
        /// {0}秒后自动选择
        /// </summary>
        public const int TeamSelectorAutoSelect = 1656112280;
		/// <summary>
        /// 尚未开放
        /// </summary>
        public const int SelectPlayModeTipNoOpenTip = 1803853087;
		/// <summary>
        /// 匹配
        /// </summary>
        public const int ServerVersionInfoMatch = 586545021;
		/// <summary>
        /// 部分匹配
        /// </summary>
        public const int ServerVersionInfoPartMatch = 676012624;
		/// <summary>
        /// 不可登录
        /// </summary>
        public const int ServerVersionInfoNotLogin = 458475934;
		/// <summary>
        /// 当前服务器信息：NetworkVersion:{0}  RpcVersion:{1}  RpcCustomTypeVersion:{2}  EntityVersionVerifyCode:{3}  ResourceVersion:{4}  CompatibilityLoginLevel:{5}  DisableClientResourceVersionCheck:{6}
        /// </summary>
        public const int ServerVersionInfoDesc = 190794943;
		/// <summary>
        /// 校验服务器信息：NetworkVersion:{0}  RpcVersion:{1}  RpcCustomTypeVersion:{2}  EntityVersionVerifyCode:{3}  ResourceVersion:{4}
        /// </summary>
        public const int ServerCheckVersionInfoDesc = 362272700;
		/// <summary>
        /// 未知
        /// </summary>
        public const int ServerVersionInfoUnKnown = 1129826049;
		/// <summary>
        /// 首席情报官
        /// </summary>
        public const int ChiefIntelligenceOfficer = 506486157;
		/// <summary>
        /// 精英渗透者
        /// </summary>
        public const int ElitePenetrator = 1402712085;
		/// <summary>
        /// 优异执行人
        /// </summary>
        public const int OutstandingExecutor = 1896471121;
		/// <summary>
        /// 活跃观察员
        /// </summary>
        public const int ActiveObserver = 1387656167;
		/// <summary>
        /// 不限时
        /// </summary>
        public const int NoLimitTime = 170152456;
		/// <summary>
        /// 发布
        /// </summary>
        public const int TeamRecruitPublishCondition1 = 2039092254;
		/// <summary>
        /// 暂不发布
        /// </summary>
        public const int TeamRecruitPublishCondition2 = 1670950823;
		/// <summary>
        /// 建议开启
        /// </summary>
        public const int TeamRecruitMicCondition1 = 574591675;
		/// <summary>
        /// 可关闭
        /// </summary>
        public const int TeamRecruitMicCondition2 = 1866583110;
		/// <summary>
        /// 需要申请
        /// </summary>
        public const int TeamRecruitApplyCondition1 = 503676020;
		/// <summary>
        /// 直接加入
        /// </summary>
        public const int TeamRecruitApplyCondition2 = 1839572038;
		/// <summary>
        /// 确定要停止匹配吗？
        /// </summary>
        public const int TeamCancelMatching = 1324720870;
		/// <summary>
        /// 技巧
        /// </summary>
        public const int TalentSkill = 1156040162;
		/// <summary>
        /// 掠夺
        /// </summary>
        public const int Looting = 1978532998;
		/// <summary>
        /// 申请列表
        /// </summary>
        public const int UiGameTeamApplyListTitle = 577033525;
		/// <summary>
        /// 等待开服
        /// </summary>
        public const int LobbyTeamPlayTip1 = 869337291;
		/// <summary>
        /// 进入游戏
        /// </summary>
        public const int LobbyTeamPlayTip2 = 1282735792;
		/// <summary>
        /// 等待队长开始
        /// </summary>
        public const int LobbyTeamPlayTip3 = 1058818068;
		/// <summary>
        /// 准备
        /// </summary>
        public const int LobbyTeamPlayTip4 = 1538116387;
		/// <summary>
        /// 取消准备
        /// </summary>
        public const int LobbyTeamPlayTip5 = 1412270022;
		/// <summary>
        /// 停止匹配
        /// </summary>
        public const int LobbyTeamPlayTip6 = 2103441280;
		/// <summary>
        /// 是否取消准备？
        /// </summary>
        public const int TeamCancelReady = 1153679411;
		/// <summary>
        /// 拥有数量:{0}
        /// </summary>
        public const int OwnNum = 81519610;
		/// <summary>
        /// 拆分丢弃
        /// </summary>
        public const int SplitAbandon = 1790030292;
		/// <summary>
        /// 返回登录
        /// </summary>
        public const int ReturnLogin = 106942848;
		/// <summary>
        /// 附近
        /// </summary>
        public const int OthersideTabTitleNearBy = 1300426769;
		/// <summary>
        /// 巡逻车队
        /// </summary>
        public const int BigMapCarShops = 870607224;
		/// <summary>
        /// 后不可编辑
        /// </summary>
        public const int EditLimitTimeDesc = 1864223021;
		/// <summary>
        /// [size=32]天赋系统帮助文本占位[/size]
        /// </summary>
        public const int TalentMainHelpTips = 947238367;
		/// <summary>
        /// 天赋系统大标题占位
        /// </summary>
        public const int TalentMainHelpTipsTitle = 756870865;
		/// <summary>
        /// 天赋系统副标题占位
        /// </summary>
        public const int TalentMainHelpTipsSubTitle = 570928441;
		/// <summary>
        /// 淘汰了
        /// </summary>
        public const int WeedOut = 936460605;
		/// <summary>
        /// 击倒了
        /// </summary>
        public const int KnockDown = 1381337419;
		/// <summary>
        /// {0}导致{1}{2}
        /// </summary>
        public const int BattleReportLog2 = 191006375;
		/// <summary>
        /// {0}使用{1}{2}{3}
        /// </summary>
        public const int BattleReportLog1 = 1273767235;
		/// <summary>
        /// {0}使用{1}摧毁了{2}
        /// </summary>
        public const int BattleReportLog4 = 922264456;
		/// <summary>
        /// 战斗数据
        /// </summary>
        public const int BattleData = 1634763766;
		/// <summary>
        /// 建筑摧毁
        /// </summary>
        public const int BuildDestory = 2106506967;
		/// <summary>
        /// 物资损失一览
        /// </summary>
        public const int ListOfAllMaterialLosses = 513605962;
		/// <summary>
        /// 建筑摧毁一览
        /// </summary>
        public const int ListOfAllDestroyedBuildings = 735055562;
		/// <summary>
        /// 爆炸物使用清单
        /// </summary>
        public const int ListOfExplosive = 39762174;
		/// <summary>
        /// 维修材料清单
        /// </summary>
        public const int ListOfServicingMaterial = 382539330;
		/// <summary>
        /// 建筑摧毁清单
        /// </summary>
        public const int ListOfDestroyedBuildings = 369144755;
		/// <summary>
        /// 摆件摧毁清单
        /// </summary>
        public const int ListOfDestroyedDecoration = 1432262475;
		/// <summary>
        /// 突袭
        /// </summary>
        public const int BattleReportAttack = 1097965570;
		/// <summary>
        /// 防守
        /// </summary>
        public const int BattleReportDefend = 1136240105;
		/// <summary>
        /// 成功
        /// </summary>
        public const int BattleReportSuccess = 1129625766;
		/// <summary>
        /// 失败
        /// </summary>
        public const int BattleReportFail = 801184184;
		/// <summary>
        /// 总耗时
        /// </summary>
        public const int TotalDuration = 1479665003;
		/// <summary>
        /// 淘汰敌人
        /// </summary>
        public const int KillTimes = 1828182465;
		/// <summary>
        /// 被淘汰
        /// </summary>
        public const int DeathTimes = 975391994;
		/// <summary>
        /// 输出伤害
        /// </summary>
        public const int OutputDamage = 1409796838;
		/// <summary>
        /// 摧毁建筑
        /// </summary>
        public const int DestroyedBuilding = 1345697387;
		/// <summary>
        /// 爆炸物材料消耗
        /// </summary>
        public const int ExplosiveCost = 555129092;
		/// <summary>
        /// 防守{0}
        /// </summary>
        public const int DefendResult = 1884698550;
		/// <summary>
        /// 占领{0}
        /// </summary>
        public const int AttackResult = 1974822177;
		/// <summary>
        /// 突袭时间
        /// </summary>
        public const int AttackTime = 1406191089;
		/// <summary>
        /// 突袭领地
        /// </summary>
        public const int AttackTerritory = 524327053;
		/// <summary>
        /// 遇袭时间
        /// </summary>
        public const int DefendTime = 91742573;
		/// <summary>
        /// 遇袭领地
        /// </summary>
        public const int DefendTerritory = 1676891800;
		/// <summary>
        /// 突袭记录
        /// </summary>
        public const int AttackRecord = 1294382707;
		/// <summary>
        /// 遇袭记录
        /// </summary>
        public const int DefendRecord = 2114362186;
		/// <summary>
        /// 已完成{0}势力任务
        /// </summary>
        public const int FactionMissionCompletedTips = 759196237;
		/// <summary>
        /// 已完成每日简报
        /// </summary>
        public const int DailyMissionCompletedTips = 20323461;
		/// <summary>
        /// 激活[{0}]的[{1}]个天赋({2}/{3})
        /// </summary>
        public const int TalentRewardTask = 2088693199;
		/// <summary>
        /// 突袭战报
        /// </summary>
        public const int AttackBattleReport = 770400111;
		/// <summary>
        /// 遇袭战报
        /// </summary>
        public const int DefendBattleReport = 913823771;
		/// <summary>
        /// 流水详情
        /// </summary>
        public const int FlowDetail = 36666904;
		/// <summary>
        /// 购买{0}级{1}
        /// </summary>
        public const int BuySomeThing = 164001549;
		/// <summary>
        /// 是否需要花费<img src='{0}' width='44' height='44'/>[color=#ff7132]{1}[/color]学习
        /// </summary>
        public const int BuyTalentLevel = 1456017890;
		/// <summary>
        /// 切换势力
        /// </summary>
        public const int ForceSwitch = 1596732030;
		/// <summary>
        /// {0}通过支撑破碎毁坏了{1}
        /// </summary>
        public const int BattleReportLog5 = 1380384216;
		/// <summary>
        /// 势力等级{0}级
        /// </summary>
        public const int RequireForceLevel = 1359782888;
		/// <summary>
        /// 基础
        /// </summary>
        public const int AdminBasic = 390074566;
		/// <summary>
        /// 道具
        /// </summary>
        public const int AdminItem = 503662132;
		/// <summary>
        /// 创建
        /// </summary>
        public const int AdminCreate = 1772700205;
		/// <summary>
        /// 学习
        /// </summary>
        public const int Learn = 296449525;
		/// <summary>
        /// 已完成遗迹任务
        /// </summary>
        public const int MonumentMissionCompletedTips = 871900150;
		/// <summary>
        /// 已完成死羊任务
        /// </summary>
        public const int DeadSheepMissionCompletedTips = 2127401040;
		/// <summary>
        /// 已完成夺宝任务
        /// </summary>
        public const int TreasureMissionCompletedTips = 1321155751;
		/// <summary>
        /// 剩余完成次数:{0}
        /// </summary>
        public const int FactionMissionRoundTips = 1249812091;
		/// <summary>
        /// 存档点重生
        /// </summary>
        public const int ArchiveRespawn = 1324013470;
		/// <summary>
        /// 使用后会在存档点附近重生
        /// </summary>
        public const int UseArchive2RespawnHint = 1472727710;
		/// <summary>
        /// 存档点cd中
        /// </summary>
        public const int ArchiveRespawnCdHint = 610471473;
		/// <summary>
        /// 预计部署完成时间
        /// </summary>
        public const int NewbieBlockPopTip1 = 888448897;
		/// <summary>
        /// 前方依旧拥堵，准备再次尝试
        /// </summary>
        public const int NewbieBlockPopTip2 = 656266438;
		/// <summary>
        /// 部署完成,准备登机
        /// </summary>
        public const int NewbieBlockPopTip3 = 528110428;
		/// <summary>
        /// 应用外观
        /// </summary>
        public const int EquipSkin = 195188093;
		/// <summary>
        /// 已应用外观
        /// </summary>
        public const int EquipSkined = 1198178341;
		/// <summary>
        ///  物资收获一览
        /// </summary>
        public const int ListOfAllMaterialHarvest = 664910730;
		/// <summary>
        /// 1份初级情报文件计为1<br/>1份中级情报文件计为100<br/>1份高级情报文件计为1000
        /// </summary>
        public const int DetailedDataTip = 2083422600;
		/// <summary>
        /// 容器被摧毁时，会根据剧本规则保护物资，需要在残骸消失时间内取回
        /// </summary>
        public const int StorageDebrisDescTip = 1959438494;
		/// <summary>
        /// 消失时间
        /// </summary>
        public const int StorageDebrisDestroyTime = 267207296;
		/// <summary>
        /// [color=#ff7132]{0}[/color]销毁
        /// </summary>
        public const int StorageDebrisDestroyTime2 = 190272349;
		/// <summary>
        /// 拖动摇杆
        /// </summary>
        public const int GuideNewbieLevelJoystick = 245879915;
		/// <summary>
        /// 点击跳跃
        /// </summary>
        public const int GuideNewbieLevelJump = 1154264327;
		/// <summary>
        /// 武器穿透等级大于目标护甲等级为增伤；小于目标等级为减伤。数值差异越大伤害变化越剧烈
        /// </summary>
        public const int PenetratingShowContext = 1228780300;
		/// <summary>
        /// 局外等级[color=#cb4821]{0}[/color]解锁
        /// </summary>
        public const int LobbyLevelLimitUnlock = 433967093;
		/// <summary>
        /// 领地蓝图
        /// </summary>
        public const int TerritoryBlueprint = 713033480;
		/// <summary>
        /// 当前领地
        /// </summary>
        public const int CurrentTerritory = 1676706013;
		/// <summary>
        /// {0}天{1}时
        /// </summary>
        public const int DayTimeFormat = 164241961;
		/// <summary>
        /// 解锁进阶通行证
        /// </summary>
        public const int BattlePassUnlockPremium = 1139155337;
		/// <summary>
        /// 升级典藏通行证
        /// </summary>
        public const int BattlePassUnlockCollector = 1661693582;
		/// <summary>
        /// 高级
        /// </summary>
        public const int BattlePassPremium = 747446482;
		/// <summary>
        /// 免费
        /// </summary>
        public const int BattlePassFree = 1584323502;
		/// <summary>
        /// {0}分钟
        /// </summary>
        public const int MinutesTimeFormat = 1925510462;
		/// <summary>
        /// {0}/{1}毫升
        /// </summary>
        public const int UnitWaterFormat = 771899703;
		/// <summary>
        /// 活动时间剩余{0}
        /// </summary>
        public const int BattlePassTimeRemain = 1240003764;
		/// <summary>
        /// {0}%经验加成
        /// </summary>
        public const int BattlePassExpAddDesc = 591657557;
		/// <summary>
        /// 无额外经验加成
        /// </summary>
        public const int BattlePassExpNoAddDesc = 986611645;
		/// <summary>
        /// 解锁进阶通行证
        /// </summary>
        public const int BattlePassUpgradeToPremium = 974877419;
		/// <summary>
        /// 升级典藏通行证
        /// </summary>
        public const int BattlePassUpgradeToCollector = 598615719;
		/// <summary>
        /// 通行证说明
        /// </summary>
        public const int BattlePassDescTitle = 256780917;
		/// <summary>
        /// 当前领地未保存
        /// </summary>
        public const int CurrentTerritoryNoSave = 1075310940;
		/// <summary>
        /// 蓝图保存中...
        /// </summary>
        public const int CustomBlueprintSaving = 576085653;
		/// <summary>
        /// 模型加载中...
        /// </summary>
        public const int ModelLoading = 1152711508;
		/// <summary>
        /// 等级不足
        /// </summary>
        public const int InsufficientLevel = 666622335;
		/// <summary>
        /// 世界情报等级修正:x{0}%
        /// </summary>
        public const int WorldInfoLevelBuffDesc = 67576080;
		/// <summary>
        /// 科技未解锁
        /// </summary>
        public const int BatchRecoverTechLocked = 1640013344;
		/// <summary>
        /// 存在无法修复
        /// </summary>
        public const int BatchRecoverDebrisConflict = 1802621820;
		/// <summary>
        /// 大厅招募申请
        /// </summary>
        public const int LobbyRecruitApply = 2011662484;
		/// <summary>
        /// 队长申请
        /// </summary>
        public const int CaptainApply = 1732482996;
		/// <summary>
        /// 请输入蓝图名称
        /// </summary>
        public const int PleaseBlueprintName = 1815863674;
		/// <summary>
        /// 限{0}字符({1}/{2})
        /// </summary>
        public const int InputTextLimitTip = 222318716;
		/// <summary>
        /// 组队大厅
        /// </summary>
        public const int TeamBattleHallTopTitle = 445308302;
		/// <summary>
        /// 前往战局保存
        /// </summary>
        public const int GoToSaveIngame = 1329358347;
		/// <summary>
        /// {0}/{1}
        /// </summary>
        public const int ReportCountDesc1 = 955737234;
		/// <summary>
        /// [color=#D6622F]{0}[/color]/{1}
        /// </summary>
        public const int ReportCountDesc2 = 446118897;
		/// <summary>
        /// 发布招募到当前战局
        /// </summary>
        public const int PublishRecruitToBattleGame = 1955359089;
		/// <summary>
        /// 暂未发布当前战局招募
        /// </summary>
        public const int NoPublishRecruitToBattleGame = 597683215;
		/// <summary>
        /// 该招募仅大厅玩家可查看，且无法撤销
        /// </summary>
        public const int PublishGameToLobbyRecruitTips = 2024749293;
		/// <summary>
        /// 该招募仅当前战局玩家可查看（发布{0}小时后失效）
        /// </summary>
        public const int PublishGameRecruitTips = 151106161;
		/// <summary>
        /// 呼叫上线
        /// </summary>
        public const int CallBackToBattle = 788971553;
		/// <summary>
        /// 招募大厅玩家
        /// </summary>
        public const int PublishGameToLobbyRecruitTitle = 1556411987;
		/// <summary>
        /// 当前已无合法下车点此时下车会立即重生，是否选择重生？
        /// </summary>
        public const int DismountFailHint = 775808527;
		/// <summary>
        /// 附身
        /// </summary>
        public const int Possession = 1770923902;
		/// <summary>
        /// 战局招募申请
        /// </summary>
        public const int InGameRecruitApply = 1800097383;
		/// <summary>
        /// 预约招募申请
        /// </summary>
        public const int AppointmentRecruitApply = 1174563844;
		/// <summary>
        /// 暂无队友
        /// </summary>
        public const int BattleTeamGListNone = 500052515;
		/// <summary>
        /// 玩家列表
        /// </summary>
        public const int ObserverModePlayer = 1441494887;
		/// <summary>
        /// 领地列表
        /// </summary>
        public const int ObserverModeTerritoryCenter = 1495182450;
		/// <summary>
        /// 切换到预约
        /// </summary>
        public const int SwitchToAppointmentTeam = 1657624972;
		/// <summary>
        /// 切换到组队
        /// </summary>
        public const int SwitchToNormalTeam = 1807210369;
		/// <summary>
        /// 喷漆设置
        /// </summary>
        public const int SpraySet = 1205414628;
		/// <summary>
        /// 喷漆展示
        /// </summary>
        public const int SprayView = 1458891401;
		/// <summary>
        /// 手势设置
        /// </summary>
        public const int GestureSet = 543436282;
		/// <summary>
        /// 手势展示
        /// </summary>
        public const int GestureView = 494917499;
		/// <summary>
        /// 确认进入
        /// </summary>
        public const int EnterMsgTip = 621531814;
		/// <summary>
        /// 混服
        /// </summary>
        public const int RecruitPlatformPc = 546220876;
		/// <summary>
        /// 手游
        /// </summary>
        public const int RecruitPlatformMobile = 924758186;
		/// <summary>
        /// 关闭({0})
        /// </summary>
        public const int CloseMsgTip3 = 979609096;
		/// <summary>
        /// 退队后将会失去队友给予的领地权限，是否确认退出
        /// </summary>
        public const int LeaveTeamTip1 = 1440170862;
		/// <summary>
        /// 退出队伍后，当前队友将失去您给予的领地相关权限，是否确认操作
        /// </summary>
        public const int LeaveTeamTip2 = 1488788109;
		/// <summary>
        /// 踢出该玩家将会失去该玩家领地内的相关权限，是否确认操作
        /// </summary>
        public const int KickOutTeamTip1 = 1195298601;
		/// <summary>
        /// 该玩家领地内有队伍声望柜，踢出该玩家将会失去该玩家领地内的相关权限，是否确认操作
        /// </summary>
        public const int KickOutTeamTip2 = 1274543118;
		/// <summary>
        /// 删除成员
        /// </summary>
        public const int RemoveMember = 1720838317;
		/// <summary>
        /// 升为管理员
        /// </summary>
        public const int UpgradeAdmin = 1115559878;
		/// <summary>
        /// 降为成员
        /// </summary>
        public const int DowngradeAdmin = 211699167;
		/// <summary>
        /// 宣誓友好勾选此项以添加豁免炮塔类摆件攻击的权限。领地操作权勾选此项以添加操作门、容器、工具柜、炮塔以及其他摆件的权限。领地建造权勾选此项以添加摆放建筑的权限。领地编辑权勾选此项以添加编辑造物的权限，包括升级、维修、回收、删除、改造与旋转。
        /// </summary>
        public const int PermissionDesc = 651282504;
		/// <summary>
        /// 建筑管理
        /// </summary>
        public const int BuildingManaer = 1984838835;
		/// <summary>
        /// 已解锁
        /// </summary>
        public const int BattlePassUnLocked = 1198989864;
		/// <summary>
        /// 进阶版通行证
        /// </summary>
        public const int BattlePassPremiumName = 627354199;
		/// <summary>
        /// 典藏版通行证
        /// </summary>
        public const int BattlePassCollectorName = 1998363108;
		/// <summary>
        /// 解锁{0}
        /// </summary>
        public const int BattlePassUnLock = 1916089481;
		/// <summary>
        /// 教学
        /// </summary>
        public const int Teaching = 1334448942;
		/// <summary>
        /// 全部取出
        /// </summary>
        public const int TakeOffAll = 1820559622;
		/// <summary>
        /// {0}技巧[color=#cb4821]{1}[/color]解锁
        /// </summary>
        public const int TalentUnlockTip = 2042015167;
		/// <summary>
        /// 更改名称
        /// </summary>
        public const int ChangeNickname = 1106979664;
		/// <summary>
        /// 常用
        /// </summary>
        public const int RouletteCommon = 425622319;
		/// <summary>
        /// 手势
        /// </summary>
        public const int RouletteGesture = 1925369164;
		/// <summary>
        /// 喷漆
        /// </summary>
        public const int RouletteSpray = 751914756;
		/// <summary>
        /// 查看招募
        /// </summary>
        public const int ToGameRecruitHall = 1521632649;
		/// <summary>
        /// 您可以向其他在大厅的玩家发布大厅招募，同时其他玩家也可以通过该招募加入您的大厅队伍
        /// </summary>
        public const int LobbyRecruitDesc = 1406623284;
		/// <summary>
        /// 您可以向其他在大厅的玩家发布预约招募，同时其他玩家也可以通过该招募加入您的预约队伍
        /// </summary>
        public const int LobbyAppointmentRecruitDesc = 744389977;
		/// <summary>
        /// 您可以向其他在当前战局的玩家发布招募，同时其他玩家也可以通过该招募加入您在当前战局的队伍
        /// </summary>
        public const int GameRecruitDesc = 1409607541;
		/// <summary>
        /// 您可以向其他在大厅的玩家发布招募，当前在大厅的玩家可以通过该招募加入您在当前战局的队伍
        /// </summary>
        public const int GameToLobbyRecruitDesc = 97637513;
		/// <summary>
        /// 生存
        /// </summary>
        public const int TalentTags1 = 626421888;
		/// <summary>
        /// 探索
        /// </summary>
        public const int TalentTags2 = 66734121;
		/// <summary>
        /// 战斗
        /// </summary>
        public const int TalentTags3 = 1850682043;
		/// <summary>
        /// 抄家
        /// </summary>
        public const int TalentTags4 = 1912457721;
		/// <summary>
        /// 采集
        /// </summary>
        public const int TalentTags5 = 1986237675;
		/// <summary>
        /// 经营
        /// </summary>
        public const int TalentTags6 = 1125652354;
		/// <summary>
        /// 渗透者新人
        /// </summary>
        public const int TalentTitles1 = 17559773;
		/// <summary>
        /// 渗透者老兵
        /// </summary>
        public const int TalentTitles2 = 1286501532;
		/// <summary>
        /// 渗透者教官
        /// </summary>
        public const int TalentTitles3 = 515332061;
		/// <summary>
        /// 科伯特新人
        /// </summary>
        public const int TalentTitles4 = 59221948;
		/// <summary>
        /// 科伯特老兵
        /// </summary>
        public const int TalentTitles5 = 60307128;
		/// <summary>
        /// 科伯特教官
        /// </summary>
        public const int TalentTitles6 = 1021260144;
		/// <summary>
        /// 前哨站新人
        /// </summary>
        public const int TalentTitles7 = 1290783573;
		/// <summary>
        /// 前哨站老兵
        /// </summary>
        public const int TalentTitles8 = 713739404;
		/// <summary>
        /// 科伯特教官
        /// </summary>
        public const int TalentTitles9 = 1818868026;
		/// <summary>
        /// 由于队长{0}小时未上线，队员可申请为队长
        /// </summary>
        public const int BattleTeamImpeachTitle = 256604828;
		/// <summary>
        /// 玩家{0}申请成为队长
        /// </summary>
        public const int BattleTeamVoteTitle = 1421278654;
		/// <summary>
        /// 修改局内招募
        /// </summary>
        public const int UiTeamModifyGameRecruit = 855005443;
		/// <summary>
        /// 一共获得了[color=#ff7132]{0}[/color]个武器外观，快来并肩作战吧！
        /// </summary>
        public const int ShareSkinCountDesc = 176717171;
		/// <summary>
        /// 战局持续时间：{0}
        /// </summary>
        public const int MainQuestMissionTime = 1476179233;
		/// <summary>
        /// 想要成为一名合格的渗透者，需要在生存与探索，战斗与突袭，建造与工程各方面拥有熟练的技能水平才能在实验岛上生存下来。1、选择与切换：你可以选择任意一条势力特训，在特训过程中也可以随时切换任务线。2、战局时间：[color=#ff9234]战局持续期间，被淘汰，基地被摧毁都不会强制离开战局，直到战局持续时间结束或主动从实验岛撤离[/color]。   战局结束后将从实验岛撤离，撤离后将无法再返回该实验岛。3、跳过特训：跳过特训依然可以领取所有的势力特训任务，并在后续的行动中完成特训任务。
        /// </summary>
        public const int MainQuestMissionTrainingDesc = 605403495;
		/// <summary>
        /// 您当前正处于{0}中，是否确定切换到{1}中？
        /// </summary>
        public const int MainQuestMissionConfirmSelect = 258075737;
		/// <summary>
        /// {0:D2}/{1:D2} {2:D2}:{3:D2}
        /// </summary>
        public const int UiRecruitTimeDesc = 1909496409;
		/// <summary>
        /// 已开服
        /// </summary>
        public const int UiRecruitTimeOpened = 53301631;
		/// <summary>
        /// 开服
        /// </summary>
        public const int UiRecruitTimeOpen = 326111809;
		/// <summary>
        /// 批量设置自动开关门
        /// </summary>
        public const int AutoOpenDoorBtns = 833046990;
		/// <summary>
        /// 您可通过此设置将领地下所有门类全部设置为开启或者关闭。
        /// </summary>
        public const int AutoOpenDoorBtnsTips = 1178306606;
		/// <summary>
        /// 全部开启
        /// </summary>
        public const int AutoOpenDoorAllOpen = 1110416243;
		/// <summary>
        /// 全部关闭
        /// </summary>
        public const int AutoOpenDoorAllClose = 1660732220;
		/// <summary>
        /// 开启设置后，在您打开领地下的门时，它们将朝你所处的反方向打开。
        /// </summary>
        public const int SettingOpenDoorAutoDirTips = 371628860;
		/// <summary>
        /// 支持您根据个人喜好在自动开关门和仅自动关门之间切换。切换为仅自动关门后，门将不会自动打开，而是在您手动打开后自动关闭。
        /// </summary>
        public const int AutoOpenDoorTips = 643713516;
		/// <summary>
        /// 一共获得了[color=#ff7132]{0}[/color]个外观，快来并肩作战吧！
        /// </summary>
        public const int ShareDressSkinCountDesc = 387572935;
		/// <summary>
        /// 一共获得了[color=#ff7132]{0}[/color]个建筑外观，快来并肩作战吧！
        /// </summary>
        public const int ShareBuildingSkinCountDesc = 1611172202;
		/// <summary>
        /// 一共获得了[color=#ff7132]{0}[/color]个摆件外观，快来并肩作战吧！
        /// </summary>
        public const int ShareItemSkinCountDesc = 1628867260;
		/// <summary>
        /// 提交
        /// </summary>
        public const int Commit = 2062174980;
		/// <summary>
        /// 详细信息
        /// </summary>
        public const int DetailPage = 1664093932;
		/// <summary>
        /// 勋章
        /// </summary>
        public const int MedalPage = 94388995;
		/// <summary>
        /// 势力特训
        /// </summary>
        public const int MainQuestTrainingConfirmTitle = 711612043;
		/// <summary>
        /// 永久限购
        /// </summary>
        public const int BuyLimitForever = 1800256969;
		/// <summary>
        /// 赛季限购
        /// </summary>
        public const int BuyLimitSeason = 920483167;
		/// <summary>
        /// 本月限购
        /// </summary>
        public const int BuyLimitMonth = 272458743;
		/// <summary>
        /// 本周限购
        /// </summary>
        public const int BuyLimitWeekly = 1886651100;
		/// <summary>
        /// 本日限购
        /// </summary>
        public const int BuyLimitDay = 585100751;
		/// <summary>
        /// 无效
        /// </summary>
        public const int InvalidDamageNumbers = 1018407326;
		/// <summary>
        /// 暂时没有邀请
        /// </summary>
        public const int TribeInviteEmptyTitle = 11062681;
		/// <summary>
        /// 活跃度规则1.成员在社群频道发言即可增加活跃度，每人每天可提供的活跃度有限制。2.社群活跃度等于最近7天内所有成员的活跃度增量之和，每天0点更新。3.活跃度度达到[color=#FF7132]10000[/color] 即可被加入推荐列表。4.超级群如果连续多日活跃度低于[color=#FF7132]3000[/color] 则会被限制推荐。
        /// </summary>
        public const int TribeActiveDesc = 109094045;
		/// <summary>
        /// 低活跃度提醒1.因活跃度持续低迷，群已被[color=#FF7132]限制推荐[/color]（无法被推荐，无法通过标签搜索到）；其余群功能不受影响；2.活跃度增加到[color=#FF7132]5000[/color]后即可解除限制，群活跃度每天0点更新。3.成员在社群频道发言即可增加活跃度，每人每天可提供的活跃度有限制。
        /// </summary>
        public const int TribeActiveFrozenDesc = 703676307;
		/// <summary>
        /// 未加入任何社群
        /// </summary>
        public const int TribeMyTribeEmptyTitle = 1153896159;
		/// <summary>
        /// 创建数量已达上限
        /// </summary>
        public const int CreateTribeCountLimit = 1208578772;
		/// <summary>
        /// {0}级可创建社群
        /// </summary>
        public const int CreateTribeLevelLimit = 1447467163;
		/// <summary>
        /// {0}级可创建超级社群
        /// </summary>
        public const int CreateSuperTribeLevelLimit = 382505120;
		/// <summary>
        /// 暂未保存蓝图
        /// </summary>
        public const int NotSaveBlueprint = 65961298;
		/// <summary>
        /// 开始特训
        /// </summary>
        public const int MainQuestMissionConfirmFirstSelectTitle = 2113982077;
		/// <summary>
        /// 离开实验岛
        /// </summary>
        public const int MainQuestMissionConfirmLeaveTitle = 632536010;
		/// <summary>
        /// 突袭遗弃领地
        /// </summary>
        public const int MainQuestMissionConfirmNotFirstTitle = 1127007564;
		/// <summary>
        /// 注意:
        /// </summary>
        public const int MainQuestMissionConfirmNotice = 548463592;
		/// <summary>
        /// 保留:
        /// </summary>
        public const int MainQuestMissionConfirmReserve = 1287024915;
		/// <summary>
        /// 遗失:
        /// </summary>
        public const int MainQuestMissionConfirmLoss = 440491976;
		/// <summary>
        /// 开始特训后，[color=#ce5b46]将解除新手保护，遭遇其他幸存者的攻击[/color]，务必做好准备。
        /// </summary>
        public const int MainQuestMissionConfirmNoticeDesc = 1368098205;
		/// <summary>
        /// 撤离后，战局内获得的账号奖励（账号经验，皮肤，情报奖章等）将永久保留。
        /// </summary>
        public const int MainQuestMissionConfirmReserveDesc = 1771317629;
		/// <summary>
        /// 在实验岛上获得的装备，道具，材料，科技，情报等级，以及基地都不会被带走。
        /// </summary>
        public const int MainQuestMissionConfirmLossDesc = 771963134;
		/// <summary>
        /// 自定义升级
        /// </summary>
        public const int BatchUpgradeCustom = 946939874;
		/// <summary>
        /// 高级
        /// </summary>
        public const int MedalLevelAdvance = 1991819147;
		/// <summary>
        /// 中级
        /// </summary>
        public const int MedalLevelMiddle = 830451200;
		/// <summary>
        /// 普通
        /// </summary>
        public const int MedalLevelNormal = 508990073;
		/// <summary>
        /// 自定义修复
        /// </summary>
        public const int BatchRecoverCustom = 856822448;
		/// <summary>
        /// 离开后，将直接前往[color=#ce5b46]标准战局[/color]，无法再返回[color=#ce5b46]新手实验岛[/color]，确定离开？
        /// </summary>
        public const int MainQuestMissionConfirmLeaveConfirm = 475817671;
		/// <summary>
        /// 跳过特训可以领取所有势力任务线并在后续的行动中完成
        /// </summary>
        public const int MainQuestMissionConfirmLeaveConfirm2 = 1075632561;
		/// <summary>
        /// 添加燃料
        /// </summary>
        public const int VehicleAddFuel = 712366445;
		/// <summary>
        /// 拥有数量：{0}
        /// </summary>
        public const int VehicleOwnNum = 325781733;
		/// <summary>
        /// 最多可放入：{0}
        /// </summary>
        public const int VehicleInputMax = 2026561285;
		/// <summary>
        /// 取出燃料
        /// </summary>
        public const int VehicleTakeFuel = 77195699;
		/// <summary>
        /// 装载数量：{0}
        /// </summary>
        public const int VehicleLoadedNum = 1769958857;
		/// <summary>
        /// 最多可取出：{0}
        /// </summary>
        public const int VehicleTakeMax = 1516699135;
		/// <summary>
        /// 低品质燃油 +{0}
        /// </summary>
        public const int VehicleFuelNum = 1031493547;
		/// <summary>
        /// 登录授权已过期，是否重新授权登录？
        /// </summary>
        public const int LoginExpired = 1315030333;
		/// <summary>
        /// 开启后赋予全部成员邀请他人、审核入群申请的权限
        /// </summary>
        public const int TribeInvitePermissionDesc = 1068771991;
		/// <summary>
        /// 群名过短
        /// </summary>
        public const int TribeNameShortTip = 584726343;
		/// <summary>
        /// 群名含有不可用字符
        /// </summary>
        public const int TribeNameIllegalTip = 1891924740;
		/// <summary>
        /// 【用途】：领取情报账号奖励时，需消耗对应[color=#969696]疲劳值[/color]；【限制】：[color=#969696]疲劳值[/color]为0时将无法领取奖励；【重置】：每日凌晨5点将自动将[color=#969696]疲劳值[/color]重置到上限；【升级】：提升账号等级，可以提升[color=#969696]疲劳值[/color]上限；
        /// </summary>
        public const int ReputationFatigueDesc = 1040102095;
		/// <summary>
        /// {0}的群
        /// </summary>
        public const int TribeDefaultName = 1434784268;
		/// <summary>
        /// {0}{1}解锁
        /// </summary>
        public const int TalentUnlockDesc = 1235441730;
		/// <summary>
        /// 人数
        /// </summary>
        public const int TeamChooseLimitNum = 601724793;
		/// <summary>
        /// 平台
        /// </summary>
        public const int TeamChoosePlatform = 948136392;
		/// <summary>
        /// 区域
        /// </summary>
        public const int TeamChooseArea = 564377228;
		/// <summary>
        /// 手游服
        /// </summary>
        public const int TeamChoosePlatformMobile = 2145446200;
		/// <summary>
        /// 混合服
        /// </summary>
        public const int TeamChoosePlatformPC = 714218787;
		/// <summary>
        /// 柜子{0}
        /// </summary>
        public const int LockerSolt = 1813564629;
		/// <summary>
        /// 有待阅读邮件
        /// </summary>
        public const int HasMailToRead = 1152466366;
		/// <summary>
        /// 进阶群可加入
        /// </summary>
        public const int TribeGraduate = 519068121;
		/// <summary>
        /// 生存学徒Ⅰ
        /// </summary>
        public const int RankName100101 = 1481893306;
		/// <summary>
        /// 生存学徒Ⅱ
        /// </summary>
        public const int RankName100102 = 866112613;
		/// <summary>
        /// 生存学徒Ⅲ
        /// </summary>
        public const int RankName100103 = 841827025;
		/// <summary>
        /// 荒野行者Ⅰ
        /// </summary>
        public const int RankName100104 = 1825654152;
		/// <summary>
        /// 荒野行者Ⅱ
        /// </summary>
        public const int RankName100105 = 1913688257;
		/// <summary>
        /// 荒野行者Ⅲ
        /// </summary>
        public const int RankName100106 = 726984021;
		/// <summary>
        /// 求生专家Ⅰ
        /// </summary>
        public const int RankName100107 = 678863295;
		/// <summary>
        /// 求生专家Ⅱ
        /// </summary>
        public const int RankName100108 = 1216500201;
		/// <summary>
        /// 求生专家Ⅲ
        /// </summary>
        public const int RankName100109 = 465861014;
		/// <summary>
        /// 自然之子Ⅰ
        /// </summary>
        public const int RankName100110 = 1385757221;
		/// <summary>
        /// 自然之子Ⅱ
        /// </summary>
        public const int RankName100111 = 335865639;
		/// <summary>
        /// 自然之子Ⅲ
        /// </summary>
        public const int RankName100112 = 1966513075;
		/// <summary>
        /// 万物共生Ⅰ
        /// </summary>
        public const int RankName100113 = 943992708;
		/// <summary>
        /// 万物共生Ⅱ
        /// </summary>
        public const int RankName100114 = 1384565924;
		/// <summary>
        /// 万物共生Ⅲ
        /// </summary>
        public const int RankName100115 = 267990233;
		/// <summary>
        /// 衣柜
        /// </summary>
        public const int Locker = 1798954620;
		/// <summary>
        /// 留在系统群
        /// </summary>
        public const int StayNewbieTribe = 1070441640;
		/// <summary>
        /// 加入进阶群
        /// </summary>
        public const int LeaveNewbieTribe = 1356637568;
		/// <summary>
        /// 当前队伍人数未达到小队上限，请确认是否开始匹配？
        /// </summary>
        public const int CheckAndPromptStartMatch = 862438669;
		/// <summary>
        /// 切换失败，{0}使用的非移动端设备，无法切换到手游模式
        /// </summary>
        public const int ChoosePlanformFailed = 1198747070;
		/// <summary>
        /// 暂无人申请
        /// </summary>
        public const int TribeApplyListEmpty = 2002655009;
		/// <summary>
        /// 切换失败
        /// </summary>
        public const int ChoosePlanformFailedTitle = 1591713842;
		/// <summary>
        /// 暂无物品
        /// </summary>
        public const int LobbyStashEmpty = 2139444450;
		/// <summary>
        /// 最近遭遇
        /// </summary>
        public const int EncounteredFriendTitle = 207683938;
		/// <summary>
        /// 邀请入群
        /// </summary>
        public const int TribeInviteSomebody = 1816115924;
		/// <summary>
        /// 已邀请
        /// </summary>
        public const int TribeHasInvitedSomebody = 580137523;
		/// <summary>
        /// 编辑您的领地名称
        /// </summary>
        public const int ChangeTerritoryName = 1607162596;
		/// <summary>
        /// 推荐社群
        /// </summary>
        public const int TribeRecommend = 442228792;
		/// <summary>
        /// 搜索结果
        /// </summary>
        public const int TribeSearchResult = 657120541;
		/// <summary>
        /// 双排模式
        /// </summary>
        public const int RankModeTwo = 1683754460;
		/// <summary>
        /// 四排模式
        /// </summary>
        public const int RankModeFour = 1893027569;
		/// <summary>
        /// 解锁条件
        /// </summary>
        public const int UnlockCondition = 1458659812;
		/// <summary>
        /// 完成下列[color=#da622f]任意一项[/color]任务完成解锁勋章
        /// </summary>
        public const int MedalUnlockTips1 = 1565479676;
		/// <summary>
        /// 积分差异
        /// </summary>
        public const int MedalUnlockTips2 = 1764815216;
		/// <summary>
        /// 勋章记录
        /// </summary>
        public const int MedalHistory = 1537073864;
		/// <summary>
        /// 下半场开始
        /// </summary>
        public const int RaidHalfTimeTip = 1450268488;
		/// <summary>
        /// 还剩一分钟
        /// </summary>
        public const int RaidHalf1MinTip = 1391153825;
		/// <summary>
        /// 阵营选择
        /// </summary>
        public const int RaidTeamChooseTitle = 245625900;
		/// <summary>
        /// 进攻
        /// </summary>
        public const int RaidTeamNameAtk = 1977396126;
		/// <summary>
        /// 防守
        /// </summary>
        public const int RaidTeamNameDef = 1342526034;
		/// <summary>
        /// 进攻方人数过多
        /// </summary>
        public const int RaidTooManyAtk = 764571350;
		/// <summary>
        /// 防守方人数过多
        /// </summary>
        public const int RaidTooManyDef = 1999371627;
		/// <summary>
        /// 队伍满了
        /// </summary>
        public const int RaidTeamFull = 1727167322;
		/// <summary>
        /// 队伍已锁定
        /// </summary>
        public const int RaidTeamLocked = 2141306405;
		/// <summary>
        /// 游戏开始
        /// </summary>
        public const int RaidTargetChangeAtkInYard1 = 1254784749;
		/// <summary>
        /// 进攻敌方领地
        /// </summary>
        public const int RaidTargetChangeAtkInYard2 = 626860173;
		/// <summary>
        /// 游戏开始
        /// </summary>
        public const int RaidTargetChangeDefInYard1 = 1331514009;
		/// <summary>
        /// 保护我方领地
        /// </summary>
        public const int RaidTargetChangeDefInYard2 = 1668954314;
		/// <summary>
        /// 正在争夺庭院
        /// </summary>
        public const int RaidBattleInYard = 1844862421;
		/// <summary>
        /// 正在争夺核心层
        /// </summary>
        public const int RaidBattleInCore = 938986338;
		/// <summary>
        /// 拆除工具柜
        /// </summary>
        public const int RaidMainTargetAtk = 1977417588;
		/// <summary>
        /// 保护工具柜
        /// </summary>
        public const int RaidMainTargetDef = 1915856120;
		/// <summary>
        /// 正在拆除工具柜
        /// </summary>
        public const int RaidTcOnHitAtk = 1585564361;
		/// <summary>
        /// 工具柜受到攻击
        /// </summary>
        public const int RaidTcOnHitDef = 1797033964;
		/// <summary>
        /// 敌人已进入核心层
        /// </summary>
        public const int RaidTargetChangeAtkInCore1 = 1543541385;
		/// <summary>
        /// 保护工具柜
        /// </summary>
        public const int RaidTargetChangeAtkInCore2 = 1120494335;
		/// <summary>
        /// 已攻入核心层
        /// </summary>
        public const int RaidTargetChangeDefInCore1 = 379556354;
		/// <summary>
        /// 拆除工具柜
        /// </summary>
        public const int RaidTargetChangeDefInCore2 = 1392926189;
		/// <summary>
        /// 获得火焰喷射器
        /// </summary>
        public const int RaidHalfTimeItemGetAtk = 1889233064;
		/// <summary>
        /// 获得钉枪和长剑
        /// </summary>
        public const int RaidHalfTimeItemGetDef = 738967372;
		/// <summary>
        /// 计分板
        /// </summary>
        public const int RaidScoreBoardTitle = 590651458;
		/// <summary>
        /// K
        /// </summary>
        public const int RaidScoreBoardKill = 1891156894;
		/// <summary>
        /// D
        /// </summary>
        public const int RaidScoreBoardDie = 19694169;
		/// <summary>
        /// 占领成功
        /// </summary>
        public const int RaidResultDestroyTcAtkTitle = 1873148084;
		/// <summary>
        /// 工具柜被拆除
        /// </summary>
        public const int RaidResultDestroyTcAtkDescribe = 1341322827;
		/// <summary>
        /// 领地失守
        /// </summary>
        public const int RaidResultDestroyTcDefTitle = 1466446834;
		/// <summary>
        /// 工具柜被拆除
        /// </summary>
        public const int RaidResultDestroyTcDefDescribe = 1819235208;
		/// <summary>
        /// 占领失败
        /// </summary>
        public const int RaidResultTimeoutAtkTitle = 1020375112;
		/// <summary>
        /// 未能拆除工具柜
        /// </summary>
        public const int RaidResultTimeoutAtkDescribe = 792154258;
		/// <summary>
        /// 战斗胜利
        /// </summary>
        public const int RaidResultTimeoutDefTitle = 1267307076;
		/// <summary>
        /// 成功保卫领地
        /// </summary>
        public const int RaidResultTimeoutDefDescribe = 1779290606;
		/// <summary>
        /// 社群改名
        /// </summary>
        public const int TribeChangeNameTitle = 345445375;
		/// <summary>
        /// 每{0}{1}可修改一次
        /// </summary>
        public const int TribeChangeNameTimeTips = 392911188;
		/// <summary>
        /// 限制{0}-{1}字符
        /// </summary>
        public const int TribeChangeNameLengthLimitTips = 1861458983;
		/// <summary>
        /// 本次改名需要消耗{0}
        /// </summary>
        public const int TribeChangeNameCostTips = 215537840;
		/// <summary>
        /// {0}张[color=#da622f]{1}[/color]<img src='{2}' width='39' height='39'/>
        /// </summary>
        public const int TribeChangeNameCostTips1 = 525079352;
		/// <summary>
        /// 无法修改，下次修改日期{0}
        /// </summary>
        public const int TribeChangeNameTimeLimitTips = 2072032594;
		/// <summary>
        /// 与原有名称相同
        /// </summary>
        public const int TribeChangeNameEqualTips = 1641161500;
		/// <summary>
        /// 守护
        /// </summary>
        public const int TXProtect = 994371668;
		/// <summary>
        /// 客服
        /// </summary>
        public const int TXSupport = 700886483;
		/// <summary>
        /// {0} {1}[color=#98938D]/{2}[/color]
        /// </summary>
        public const int RecruitTeamTitle = 855811521;
		/// <summary>
        /// 迎战敌人
        /// </summary>
        public const int RaidMaptargetBeatEnemy = 151289420;
		/// <summary>
        /// 获得物资补给
        /// </summary>
        public const int RaidHalftimeitemgetAtkArmed = 1503811235;
		/// <summary>
        /// 拆除
        /// </summary>
        public const int RaidMaptargetAtkTc = 1632288176;
		/// <summary>
        /// 保护
        /// </summary>
        public const int RaidMaptargetDefTc = 860300766;
		/// <summary>
        /// 幸运抽奖
        /// </summary>
        public const int MallGacha = 2141695489;
		/// <summary>
        /// 暂无私聊玩家
        /// </summary>
        public const int ChatChannelEmpty_Private = 1981925491;
		/// <summary>
        /// 暂无队伍
        /// </summary>
        public const int ChatChannelEmpty_Team = 290238009;
		/// <summary>
        /// 重置路径
        /// </summary>
        public const int PhotoPathReset = 552906679;
		/// <summary>
        /// 放置观察点
        /// </summary>
        public const int PhotoPlaceLookAtPoint = 709476788;
		/// <summary>
        /// 吸附观察点
        /// </summary>
        public const int PhotoAttachLookAtPoint = 1734763540;
		/// <summary>
        /// 放置路点
        /// </summary>
        public const int PhotoPlaceWayPoint = 1903400584;
		/// <summary>
        /// 拿起路点
        /// </summary>
        public const int PhotoTakeUpWayPoint = 1073300039;
		/// <summary>
        /// 删除路点
        /// </summary>
        public const int PhotoRemoveWayPoint = 935701311;
		/// <summary>
        /// 我的战局{0}/{1}
        /// </summary>
        public const int WarSituationTitle = 1738802473;
		/// <summary>
        /// 输入社群名称/编号或选择一个标签搜索
        /// </summary>
        public const int TribeSearchPromptText = 1312918407;
		/// <summary>
        /// 每日简报
        /// </summary>
        public const int DailyMissionTitle = 534560970;
		/// <summary>
        /// 是否开启{0}特训？
        /// </summary>
        public const int MainQuestUnlockDesc = 963801915;
		/// <summary>
        /// 解锁保险箱
        /// </summary>
        public const int UnlockTheSafe = 596295566;
		/// <summary>
        /// 退出游戏视为开锁失败，扣除的钥匙耐久值不返还
        /// </summary>
        public const int UnlockTheSafeNotice = 1055027139;
		/// <summary>
        /// 取消
        /// </summary>
        public const int UnlockTheSafeCancel = 1253272798;
		/// <summary>
        /// 确认
        /// </summary>
        public const int UnlockTheSafeConfirm = 1124933150;
		/// <summary>
        /// 重置
        /// </summary>
        public const int TreasureHuntGameReset = 1204994711;
		/// <summary>
        /// 成功
        /// </summary>
        public const int StorySettlementSuccessed = 1447221051;
		/// <summary>
        /// 失败
        /// </summary>
        public const int StorySettlementFailed = 1254191418;
		/// <summary>
        /// 击杀
        /// </summary>
        public const int DetailedDataKilled = 689538258;
		/// <summary>
        /// 摧毁建筑
        /// </summary>
        public const int DetailedDataDestroyBuilding = 2079093231;
		/// <summary>
        /// 获取废料
        /// </summary>
        public const int DetailedDataObtainWaste = 2084752142;
		/// <summary>
        /// 获取情报文件
        /// </summary>
        public const int DetailedDataObtainDocuments = 346532495;
		/// <summary>
        /// 建造和升级建筑
        /// </summary>
        public const int DetailedDataBuildAndUpgradeBuildings = 753481717;
		/// <summary>
        /// 在线时长
        /// </summary>
        public const int DetailedDataOnlineDuration = 1114949073;
		/// <summary>
        /// 未知玩家
        /// </summary>
        public const int DetailedDataUnknownPlayer = 927495469;
		/// <summary>
        /// 详细战绩
        /// </summary>
        public const int DetailedDataBattleRecords = 1674074892;
		/// <summary>
        /// 破译{0}
        /// </summary>
        public const int WarSituationApocalypseAgent = 488318730;
		/// <summary>
        /// 情报
        /// </summary>
        public const int WarSituationIntelligenceDecryption = 814952319;
		/// <summary>
        /// 用时
        /// </summary>
        public const int WarSituationSettlementTime = 471524848;
		/// <summary>
        /// {0}天 {1}: {2}: {3}
        /// </summary>
        public const int WarSituationTimeSpent = 1191491010;
		/// <summary>
        /// 行动目标：提升情报等级
        /// </summary>
        public const int StoryPlanBoxActionObjective = 1775207316;
		/// <summary>
        /// 生存手册
        /// </summary>
        public const int StoryPlanBoxSurvivalGuide = 981690680;
		/// <summary>
        /// 情报等级{0}级
        /// </summary>
        public const int StorySettlementLevel = 1683254816;
		/// <summary>
        /// 完成下列任务解锁勋章
        /// </summary>
        public const int MedalUnlockTips3 = 916610640;
		/// <summary>
        /// 与
        /// </summary>
        public const int And = 1508218324;
		/// <summary>
        /// SKJH
        /// </summary>
        public const int SavePicFolderName = 2123231388;
		/// <summary>
        /// 我创建的社群
        /// </summary>
        public const int ChatTribeCreated = 1919871473;
		/// <summary>
        /// 我加入的社群
        /// </summary>
        public const int ChatTribeJoined = 1540639861;
		/// <summary>
        /// 返回
        /// </summary>
        public const int CreateRoleEscTitle = 1135595470;
		/// <summary>
        /// 好伙伴一起来战斗！
        /// </summary>
        public const int ChatTeamRecruitTitle = 2082655392;
		/// <summary>
        /// 接通讯号
        /// </summary>
        public const int TreasureHuntGameTitle = 1439636729;
		/// <summary>
        /// 科技等级
        /// </summary>
        public const int TechLevel = 1509616378;
		/// <summary>
        /// 自动开关门
        /// </summary>
        public const int AutoOpenDoor = 1853992647;
		/// <summary>
        /// 添加好友
        /// </summary>
        public const int UiAddFriend = 1188712251;
		/// <summary>
        /// 黑名单
        /// </summary>
        public const int BlackList = 2024941354;
		/// <summary>
        /// 社群
        /// </summary>
        public const int ChatTribe = 1679627378;
		/// <summary>
        /// 退出
        /// </summary>
        public const int PhotoPCEsc = 716988118;
		/// <summary>
        /// 第一人称
        /// </summary>
        public const int PhotoPCFP = 1856698379;
		/// <summary>
        /// 第三人称
        /// </summary>
        public const int PhotoPCTP = 1051765122;
		/// <summary>
        /// 隐藏界面
        /// </summary>
        public const int PhotoPCHideUI = 259765173;
		/// <summary>
        /// 显示界面
        /// </summary>
        public const int PhotoPCShowUI = 618909455;
		/// <summary>
        /// 重置相机
        /// </summary>
        public const int PhotoPCResetAll = 778522308;
		/// <summary>
        /// {0}加入社群
        /// </summary>
        public const int TribeChatMsgNewMemberJoin = 22553810;
		/// <summary>
        /// 按照路点改变速度时，越稀疏的地方越快，越密集的地方越慢
        /// </summary>
        public const int PathCameraSpeedTip = 359634364;
		/// <summary>
        /// 相对屏幕中心偏移的百分比，0代表屏幕中心
        /// </summary>
        public const int PathCameraScreenMoveTip = 1790069147;
		/// <summary>
        /// 概率
        /// </summary>
        public const int GachaProbabilityTip = 790924408;
		/// <summary>
        /// 概率一览
        /// </summary>
        public const int GachaProbabilityTitle = 248945281;
		/// <summary>
        /// 规则
        /// </summary>
        public const int GachaRuleTip = 520997269;
		/// <summary>
        /// 当前存在正在进行的勋章对战，无法开启
        /// </summary>
        public const int RankMatchTips1 = 453752724;
		/// <summary>
        /// 是否将生存评级结算切换至本场战局？
        /// </summary>
        public const int RankMatchTips2 = 2061851333;
		/// <summary>
        /// 切换列表
        /// </summary>
        public const int InteractiveShortcutTitleSwitchList = 567266915;
		/// <summary>
        /// 手动填弹
        /// </summary>
        public const int InteractiveShortcutTitleReload = 1161582505;
		/// <summary>
        /// 自动门：开
        /// </summary>
        public const int InteractiveShortcutTitleAutoOpenDoorTure = 417004508;
		/// <summary>
        /// 自动门：关
        /// </summary>
        public const int InteractiveShortcutTitleAutoOpenDoorFalse = 1548764537;
		/// <summary>
        /// 切换食物
        /// </summary>
        public const int InteractiveShortcutTitleSwitchFood = 2115969260;
		/// <summary>
        /// 全屏拾取
        /// </summary>
        public const int InteractiveShortcutTitleFullScreen = 31824690;
		/// <summary>
        /// 取消喂食
        /// </summary>
        public const int InteractiveShortcutTitleCancelFood = 1470628961;
		/// <summary>
        /// 清档结算
        /// </summary>
        public const int SettlementClear = 1830117268;
		/// <summary>
        /// 下个阶段
        /// </summary>
        public const int NextStage = 583886992;
		/// <summary>
        /// 无物资掠夺记录
        /// </summary>
        public const int NoLootingRecord = 1813918341;
		/// <summary>
        /// 无建筑摧毁记录
        /// </summary>
        public const int NoBuildingRecord = 126486258;
		/// <summary>
        /// 开启生存评级
        /// </summary>
        public const int OpenMedalRankScore = 1326708254;
		/// <summary>
        /// 是否将本场战局生存评级结算删除？
        /// </summary>
        public const int SwitchMedalRankScoreTip = 158529694;
		/// <summary>
        /// 是否将生存评级结算切换至本场战局?切换冷却时间为{0}小时
        /// </summary>
        public const int DeleteMedalRankScoreTip = 775278213;
		/// <summary>
        /// 生存评级切换冷却{0}
        /// </summary>
        public const int MedalRankScoreCDTime = 893253768;
		/// <summary>
        /// 无勋章积分，是否开启游戏
        /// </summary>
        public const int NoMedalRankScoreTip = 544027568;
		/// <summary>
        /// 奖池内容
        /// </summary>
        public const int GachaPoolPreviewTabTitle = 564604401;
		/// <summary>
        /// {0}天{1}小时
        /// </summary>
        public const int GachaTimeDayHour = 1919672812;
		/// <summary>
        /// {0}小时{1}分钟
        /// </summary>
        public const int GachaTimeHourMinute = 1472238017;
		/// <summary>
        /// 0时{0}分
        /// </summary>
        public const int GachaTimeMinute = 1752636121;
		/// <summary>
        /// 上一页
        /// </summary>
        public const int LastPage = 1344897795;
		/// <summary>
        /// 下一页
        /// </summary>
        public const int NextPage = 59909296;
		/// <summary>
        /// 未设置标签
        /// </summary>
        public const int TribeTagListEmptyTip = 1013519979;
		/// <summary>
        /// 战局持续时间：[color=#da622f]{0}[/color]
        /// </summary>
        public const int NewbieServerTime = 86716656;
		/// <summary>
        /// 配件预览
        /// </summary>
        public const int WeaponPartPreview = 81820069;
		/// <summary>
        /// Lv.{0}
        /// </summary>
        public const int WarSituationReputationLevel = 676161551;
		/// <summary>
        /// 重要设置需自主输入快捷键
        /// </summary>
        public const int ImportantHotKeyTip = 203977284;
		/// <summary>
        /// 该按键不可修改
        /// </summary>
        public const int HotKeyCannotModify = 581257248;
		/// <summary>
        /// 已锁死（{0}s）
        /// </summary>
        public const int BoxGameLocked = 56189786;
		/// <summary>
        /// 未搜索到玩家
        /// </summary>
        public const int NoSearchPlayer = 1342646029;
		/// <summary>
        /// 没有屏蔽玩家
        /// </summary>
        public const int NoBlackPlayer = 158633971;
		/// <summary>
        /// 已在此群
        /// </summary>
        public const int TribeInThisTribe = 2008442520;
		/// <summary>
        /// 当前战局招募
        /// </summary>
        public const int CurrentBattleRecruitment = 1425479734;
		/// <summary>
        /// 下次信号回传：{0}
        /// </summary>
        public const int NextMedalSyncTime = 163107312;
		/// <summary>
        /// 当前疲劳值：
        /// </summary>
        public const int CurrFatigueValueTxt = 1288309822;
		/// <summary>
        /// 保险柜激活失败，请先将暂存物品取出
        /// </summary>
        public const int OpenSafeBoxBlock = 445180300;
		/// <summary>
        /// 拾荒新人Ⅰ
        /// </summary>
        public const int RankName100201 = 89599930;
		/// <summary>
        /// 拾荒新人Ⅱ
        /// </summary>
        public const int RankName100202 = 198104668;
		/// <summary>
        /// 拾荒新人Ⅲ
        /// </summary>
        public const int RankName100203 = 1110516685;
		/// <summary>
        /// 资源猎手Ⅰ
        /// </summary>
        public const int RankName100204 = 1600881091;
		/// <summary>
        /// 资源猎手Ⅱ
        /// </summary>
        public const int RankName100205 = 943511158;
		/// <summary>
        /// 资源猎手Ⅲ
        /// </summary>
        public const int RankName100206 = 1163345018;
		/// <summary>
        /// 开采专家Ⅰ
        /// </summary>
        public const int RankName100207 = 400565017;
		/// <summary>
        /// 开采专家Ⅱ
        /// </summary>
        public const int RankName100208 = 1835376346;
		/// <summary>
        /// 开采专家Ⅲ
        /// </summary>
        public const int RankName100209 = 1917875984;
		/// <summary>
        /// 掠食达人Ⅰ
        /// </summary>
        public const int RankName100210 = 19348248;
		/// <summary>
        /// 掠食达人Ⅱ
        /// </summary>
        public const int RankName100211 = 108607311;
		/// <summary>
        /// 掠食达人Ⅲ
        /// </summary>
        public const int RankName100212 = 259823980;
		/// <summary>
        /// 丰饶之主Ⅰ
        /// </summary>
        public const int RankName100213 = 17611524;
		/// <summary>
        /// 丰饶之主Ⅱ
        /// </summary>
        public const int RankName100214 = 1206442155;
		/// <summary>
        /// 丰饶之主Ⅲ
        /// </summary>
        public const int RankName100215 = 1151691450;
		/// <summary>
        /// 荒野新兵Ⅰ
        /// </summary>
        public const int RankName100301 = 85988060;
		/// <summary>
        /// 荒野新兵Ⅱ
        /// </summary>
        public const int RankName100302 = 667427404;
		/// <summary>
        /// 荒野新兵Ⅲ
        /// </summary>
        public const int RankName100303 = 1425403721;
		/// <summary>
        /// 战场老兵Ⅰ
        /// </summary>
        public const int RankName100304 = 2051776354;
		/// <summary>
        /// 战场老兵Ⅱ
        /// </summary>
        public const int RankName100305 = 1059015465;
		/// <summary>
        /// 战场老兵Ⅲ
        /// </summary>
        public const int RankName100306 = 1286325434;
		/// <summary>
        /// 百战精锐Ⅰ
        /// </summary>
        public const int RankName100307 = 2070601851;
		/// <summary>
        /// 百战精锐Ⅱ
        /// </summary>
        public const int RankName100308 = 1115676898;
		/// <summary>
        /// 百战精锐Ⅲ
        /// </summary>
        public const int RankName100309 = 1431269055;
		/// <summary>
        /// 战争大师Ⅰ
        /// </summary>
        public const int RankName100310 = 280854207;
		/// <summary>
        /// 战争大师Ⅱ
        /// </summary>
        public const int RankName100311 = 1104971810;
		/// <summary>
        /// 战争大师Ⅲ
        /// </summary>
        public const int RankName100312 = 1677814901;
		/// <summary>
        /// 废土主宰Ⅰ
        /// </summary>
        public const int RankName100313 = 1544802459;
		/// <summary>
        /// 废土主宰Ⅱ
        /// </summary>
        public const int RankName100314 = 963762398;
		/// <summary>
        /// 废土主宰Ⅲ
        /// </summary>
        public const int RankName100315 = 373059230;
		/// <summary>
        /// 初级工匠Ⅰ
        /// </summary>
        public const int RankName100401 = 1685460859;
		/// <summary>
        /// 初级工匠Ⅱ
        /// </summary>
        public const int RankName100402 = 111607047;
		/// <summary>
        /// 初级工匠Ⅲ
        /// </summary>
        public const int RankName100403 = 86301232;
		/// <summary>
        /// 工程学徒Ⅰ
        /// </summary>
        public const int RankName100404 = 110496903;
		/// <summary>
        /// 工程学徒Ⅱ
        /// </summary>
        public const int RankName100405 = 380736727;
		/// <summary>
        /// 工程学徒Ⅲ
        /// </summary>
        public const int RankName100406 = 963242552;
		/// <summary>
        /// 基建专家Ⅰ
        /// </summary>
        public const int RankName100407 = 2071165382;
		/// <summary>
        /// 基建专家Ⅱ
        /// </summary>
        public const int RankName100408 = 1664283883;
		/// <summary>
        /// 基建专家Ⅲ
        /// </summary>
        public const int RankName100409 = 52118528;
		/// <summary>
        /// 建筑大师Ⅰ
        /// </summary>
        public const int RankName100410 = 2145339982;
		/// <summary>
        /// 建筑大师Ⅱ
        /// </summary>
        public const int RankName100411 = 124439523;
		/// <summary>
        /// 建筑大师Ⅲ
        /// </summary>
        public const int RankName100412 = 1703001517;
		/// <summary>
        /// 奇观造主Ⅰ
        /// </summary>
        public const int RankName100413 = 1324999907;
		/// <summary>
        /// 奇观造主Ⅱ
        /// </summary>
        public const int RankName100414 = 1740448173;
		/// <summary>
        /// 奇观造主Ⅲ
        /// </summary>
        public const int RankName100415 = 819695685;
		/// <summary>
        /// 边缘行者Ⅰ
        /// </summary>
        public const int RankName100501 = 2025322096;
		/// <summary>
        /// 边缘行者Ⅱ
        /// </summary>
        public const int RankName100502 = 1916065987;
		/// <summary>
        /// 边缘行者Ⅲ
        /// </summary>
        public const int RankName100503 = 1373096492;
		/// <summary>
        /// 遗迹猎手Ⅰ
        /// </summary>
        public const int RankName100504 = 484117583;
		/// <summary>
        /// 遗迹猎手Ⅱ
        /// </summary>
        public const int RankName100505 = 1636253686;
		/// <summary>
        /// 遗迹猎手Ⅲ
        /// </summary>
        public const int RankName100506 = 827058429;
		/// <summary>
        /// 开拓先驱Ⅰ
        /// </summary>
        public const int RankName100507 = 888758131;
		/// <summary>
        /// 开拓先驱Ⅱ
        /// </summary>
        public const int RankName100508 = 2019335587;
		/// <summary>
        /// 开拓先驱Ⅲ
        /// </summary>
        public const int RankName100509 = 865637079;
		/// <summary>
        /// 荒野先锋Ⅰ
        /// </summary>
        public const int RankName100510 = 703252570;
		/// <summary>
        /// 荒野先锋Ⅱ
        /// </summary>
        public const int RankName100511 = 644984347;
		/// <summary>
        /// 荒野先锋Ⅲ
        /// </summary>
        public const int RankName100512 = 2110887525;
		/// <summary>
        /// 行者无疆Ⅰ
        /// </summary>
        public const int RankName100513 = 463560189;
		/// <summary>
        /// 行者无疆Ⅱ
        /// </summary>
        public const int RankName100514 = 540601068;
		/// <summary>
        /// 行者无疆Ⅲ
        /// </summary>
        public const int RankName100515 = 1548389543;
		/// <summary>
        /// 装备&时装
        /// </summary>
        public const int LobbyFashionTitle = 883521033;
		/// <summary>
        /// 武器&战备
        /// </summary>
        public const int LobbyWeaponTitle = 490391039;
		/// <summary>
        /// 全部拾取
        /// </summary>
        public const int PickAll = 73753196;
		/// <summary>
        /// 更换社群
        /// </summary>
        public const int ChangeTribe = 1187761131;
		/// <summary>
        /// 快捷使用
        /// </summary>
        public const int QuickUse = 1128945386;
		/// <summary>
        /// 快捷装备
        /// </summary>
        public const int QuickEquip = 367366518;
		/// <summary>
        /// 您没在队伍中
        /// </summary>
        public const int NotInAnyTeam = 1974495549;
    }
}