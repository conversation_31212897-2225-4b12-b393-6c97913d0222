using System;
using System.Collections.Generic;
using System.Net;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Config;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocWorld.Component;
using WizardGames.Soc.SocWorld.Lobby;
using WizardGames.Soc.SocWorld.NodeSystem;
using WizardGames.SocConst.Soc.Const;
using WizardGames.Soc.SocWorld.ClassImpl.Task;

namespace WizardGames.Soc.Test
{
    public class MedalTest : TestCaseBase
    {
        PlayerEntity player;
        MedalTaskContainer medalTaskContainer;
        public override void Run(ArraySegment<string> args)
        {
            player = CreatePlayerWithInventory();
            player.AddComponent(new PlayerDataStatisticComponent());
            AddEntityOnlyInitComponents(player);
            player.LifeCycleFlags |= LifeFlags.Alive;
            ServerConfig.Instance.ClosePushToLobby = 1;
            GlobalInfoSyncEntity.CreateInstance();
            InitMedalTaskContainer();
            TestCaloriesNum();
            TestSyncDeltaMedalInfo();
            TestAcceptNonPreCountTask();
            Test制作上等矿茶任务计数();
        }

        private void SetupMedalReconstructData(long taskId, int medalId, int medalLevel, int styleId, int points)
        {
            GlobalInfoSyncEntity.Instance.MedalReconstructData ??= [];
            GlobalInfoSyncEntity.Instance.MedalReconstructData.Add(taskId, new MedalInfo(medalId, styleId, medalLevel, points));
        }

        private void InitMedalTaskContainer()
        {
            FunctionConst.Task.EnableDynamicSwitch();
            FunctionSwitchComponent.Instance.SetEnable(FunctionConst.Task, false);
            player.AddComponent(new PlayerTaskComponent());

            SetupCommonConditionConfigs();

            medalTaskContainer = new MedalTaskContainer();
            player.ComponentTask.AddTaskContainer(medalTaskContainer);
            medalTaskContainer.Init();
            medalTaskContainer.PostInit(true);
        }

        private static void SetupCommonConditionConfigs()
        {
            SetupTaskConditionConfig(151, 5, 1, 0);   // For task 2020023 and similar
            SetupTaskConditionConfig(234, 50, 1, 0);  // For test tasks
            SetupTaskConditionConfig(1, 1, 1, 0);     // Common condition ID
            SetupTaskConditionConfig(2, 1, 1, 0);     // Common condition ID
            SetupTaskConditionConfig(3, 1, 1, 0);     // Common condition ID
        }

        private void TestCaloriesNum()
        {
            SetupMedalTaskConfig(100001, 234, [50], 10);
            SetupMedalReconstructData(100001, 1, 1, 1, 0);
            using var ctx = NodeOpContext.GetNew("test");
            var opCode = player.ComponentTask.SystemRoot.Input(new NodeOpByBizId(100001, 1), ctx);
            Print($"opCode: {opCode}");
            // 增量累计
            player.Calories = 10;
            Print(medalTaskContainer.ToTreeString(0));
            player.Calories = 30;
            Print(medalTaskContainer.ToTreeString(0));
            // 减少
            player.Calories = 0;
            Print(medalTaskContainer.ToTreeString(0));
            // 非存活状态
            player.LifeCycleFlags = ByteUtil.RemoveFlag(player.LifeCycleFlags, LifeFlags.Alive);
            player.Calories = 10;
            Print(medalTaskContainer.ToTreeString(0));
            player.LifeCycleFlags = ByteUtil.AddFlag(player.LifeCycleFlags, LifeFlags.Alive);
            player.Calories = 30;
            FlipTimewheel(1);
            Print(medalTaskContainer.ToTreeString(0));
        }

        private void TestAcceptNonPreCountTask()
        {
            player.ComponentDataStatistic.SystemRoot.ClearChildren();
            SetupMedalTaskConfig(1000021, 234, [50], 10);
            SetupMedalTaskConfig(1000022, 234, [100], 10);
            SetupMedalReconstructData(1000021, 1, 1, 1, 10);
            SetupMedalReconstructData(1000022, 1, 2, 1, 20);
            medalTaskContainer.AcceptTask(1000021, false);
            Print(medalTaskContainer.ToTreeString(0));
            var taskNode = medalTaskContainer.GetTaskNode(TaskNodeIndex.InProgress, 1000021) as MedalTaskNode;
            medalTaskContainer.CompleteTask(taskNode);
            Print(medalTaskContainer.ToTreeString(0));
        }

        /// <summary>
        /// 测试制作上等矿茶任务计数功能
        /// </summary>
        private void Test制作上等矿茶任务计数()
        {
            Print("=== 开始测试制作上等矿茶任务计数功能 ===");

            // 清理之前的数据统计
            player.ComponentDataStatistic.SystemRoot.ClearChildren();

            // 上等矿茶物品ID
            const int 上等矿茶物品ID = 23030005;
            // 消耗品制作类型ID
            const int 消耗品制作类型 = 23;
            // 制作条件ID（ITEM_MANUFACTURING_ADD_ID）
            const int 制作条件ID = -6;

            // 设置制作上等矿茶的任务配置
            // 任务ID: 3000001, 条件ID: -6 (制作条件), 参数: [3, 23] (制作3个消耗品类型23)
            long 制作矿茶任务ID = 3000001;
            SetupMedalTaskConfig(制作矿茶任务ID, 制作条件ID, [3, 消耗品制作类型], 10);
            SetupMedalReconstructData(制作矿茶任务ID, 2, 1, 1, 15);

            // 设置任务条件配置
            SetupTaskConditionConfig(制作条件ID, 3, 1, 0);

            Print("任务配置完成，接取制作上等矿茶任务");

            // 接取制作上等矿茶任务
            using var ctx = NodeOpContext.GetNew("test_制作矿茶");
            var opCode = player.ComponentTask.SystemRoot.Input(new NodeOpByBizId(制作矿茶任务ID, 1), ctx);
            Print($"接取任务操作码: {opCode}");
            Print("任务接取后状态:");
            Print(medalTaskContainer.ToTreeString(0));

            // 模拟制作上等矿茶操作
            Print("开始模拟制作上等矿茶操作...");

            // 第一次制作1个上等矿茶
            模拟制作物品(上等矿茶物品ID, 1);
            Print("制作1个上等矿茶后的任务状态:");
            Print(medalTaskContainer.ToTreeString(0));

            // 第二次制作1个上等矿茶
            模拟制作物品(上等矿茶物品ID, 1);
            Print("制作2个上等矿茶后的任务状态:");
            Print(medalTaskContainer.ToTreeString(0));

            // 第三次制作1个上等矿茶，应该完成任务
            模拟制作物品(上等矿茶物品ID, 1);
            Print("制作3个上等矿茶后的任务状态:");
            Print(medalTaskContainer.ToTreeString(0));

            // 验证任务是否完成
            var 任务节点 = medalTaskContainer.GetTaskNode(TaskNodeIndex.InProgress, 制作矿茶任务ID);
            if (任务节点 == null)
            {
                // 检查已完成任务
                任务节点 = medalTaskContainer.GetTaskNode(TaskNodeIndex.CompletedAndNotGetReward, 制作矿茶任务ID);
                if (任务节点 != null)
                {
                    Print("✓ 任务已自动完成！");
                }
                else
                {
                    Print("✗ 任务状态异常，既不在进行中也不在已完成列表");
                }
            }
            else
            {
                Print("✗ 任务仍在进行中，计数功能可能存在问题");
            }

            Print("=== 制作上等矿茶任务计数功能测试完成 ===");
        }

        /// <summary>
        /// 模拟制作物品操作
        /// </summary>
        /// <param name="物品ID">要制作的物品ID</param>
        /// <param name="数量">制作数量</param>
        private void 模拟制作物品(int 物品ID, int 数量)
        {
            Print($"模拟制作物品 ID:{物品ID}, 数量:{数量}");

            // 使用正确的制作标记来触发物品添加事件
            var itemAddEvent = new ItemAdd(物品ID, 数量, OpContextConst.COMMON_COMPOSE);

            // 通过静态回调触发事件处理
            EntityStaticCallback<PlayerEntity>.InvokeStaticCallback(player, itemAddEvent);

            // 手动推进时间轮，确保事件处理完成
            FlipTimewheel(1);
        }

        private void TestSyncDeltaMedalInfo()
        {
            ServerInstanceEntity.Instance.OnSendSettleStyleRankPointsToLobbyCallback(null, System.Net.HttpStatusCode.OK, 0);
            Print(medalTaskContainer.ToTreeString(0));
        }

        private static void SetupMedalConfig(int medalId, int level, int styleId, int points, int nextLevelTaskId = 0)
        {
            var medalJson = @"{
      ""medalID"": " + medalId + @",
      ""level"": " + level + @",
      ""type"": 1,
      ""styleID"": " + styleId + @",
      ""task"": [
        [
          2,
          " + nextLevelTaskId + @"
        ]
      ],
      ""styleRankPoints"": [
        [
          1,
          " + points + @"
        ]
      ],
      ""rankID"": 1,
      ""shouldPreCount"": true
    }";
            McCommon.Tables.TBMedal.Update(SimpleJSON.JSONNode.Parse(medalJson));
        }

        private static void SetupMedalTaskConfig(long taskId, int endCondition, long[] endConditionParameter = null, int taskType = 10, int endConditionMode = 0, int isSubTask = 0, int[] subTasks = null)
        {
            var taskJson = @"{
      ""id"": " + taskId + @",
      ""type"": " + taskType + @",
      ""taskId"": " + (taskId > int.MaxValue ? 0 : (int)taskId) + @",
      ""isSubTask"": " + isSubTask + @",
      ""taskPhaseEndCondition"": " + endCondition + @",
      ""autoGrant"": false,";

            // Only add endConditionParameter if it has values
            if (endConditionParameter != null && endConditionParameter.Length > 0)
                taskJson += @"""endConditionParameter"": [" + string.Join(",", endConditionParameter) + @"],";
            else
                taskJson += @"""endConditionParameter"": [],";

            taskJson += @"""endConditionMode"": " + endConditionMode + @",";

            // Only add subTasks if it has values
            if (subTasks != null && subTasks.Length > 0)
                taskJson += @"""subTasks"": [" + string.Join(",", subTasks) + @"]";
            else
                taskJson += @"""subTasks"": []";

            taskJson += @"}";

            McCommon.Tables.TbQuestPhase.Update(SimpleJSON.JSONNode.Parse(taskJson));
        }

        private static void SetupTaskConditionConfig(int conditionId, int targetData, int compareType, int counterChange)
        {
            var conditionJson = @"{
      ""id"": " + conditionId + @",
      ""compareType"": " + compareType + @",
      ""targetData"": " + targetData + @",
      ""counterChange"": " + counterChange + @"
    }";
            McCommon.Tables.TbQuestCondition.Update(SimpleJSON.JSONNode.Parse(conditionJson));
        }

        public override void Cleanup()
        {
            base.Cleanup();
        }
    }
}