using System;
using System.Collections.Generic;
using System.Net;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Config;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocWorld.Component;
using WizardGames.Soc.SocWorld.Lobby;
using WizardGames.Soc.SocWorld.NodeSystem;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.Test
{
    public class MedalTest : TestCaseBase
    {
        PlayerEntity player;
        MedalTaskContainer medalTaskContainer;
        public override void Run(ArraySegment<string> args)
        {
            player = CreatePlayerWithInventory();
            player.AddComponent(new PlayerDataStatisticComponent());
            AddEntityOnlyInitComponents(player);
            player.LifeCycleFlags |= LifeFlags.Alive;
            ServerConfig.Instance.ClosePushToLobby = 1;
            GlobalInfoSyncEntity.CreateInstance();
            InitMedalTaskContainer();
            TestCaloriesNum();
            TestSyncDeltaMedalInfo();
            TestAcceptNonPreCountTask();
        }

        private void SetupMedalReconstructData(long taskId, int medalId, int medalLevel, int styleId, int points)
        {
            GlobalInfoSyncEntity.Instance.MedalReconstructData ??= [];
            GlobalInfoSyncEntity.Instance.MedalReconstructData.Add(taskId, new MedalInfo(medalId, styleId, medalLevel, points));
        }

        private void InitMedalTaskContainer()
        {
            FunctionConst.Task.EnableDynamicSwitch();
            FunctionSwitchComponent.Instance.SetEnable(FunctionConst.Task, false);
            player.AddComponent(new PlayerTaskComponent());

            SetupCommonConditionConfigs();

            medalTaskContainer = new MedalTaskContainer();
            player.ComponentTask.AddTaskContainer(medalTaskContainer);
            medalTaskContainer.Init();
            medalTaskContainer.PostInit(true);
        }

        private static void SetupCommonConditionConfigs()
        {
            SetupTaskConditionConfig(151, 5, 1, 0);   // For task 2020023 and similar
            SetupTaskConditionConfig(234, 50, 1, 0);  // For test tasks
            SetupTaskConditionConfig(1, 1, 1, 0);     // Common condition ID
            SetupTaskConditionConfig(2, 1, 1, 0);     // Common condition ID
            SetupTaskConditionConfig(3, 1, 1, 0);     // Common condition ID
        }

        private void TestCaloriesNum()
        {
            SetupMedalTaskConfig(100001, 234, [50], 10);
            SetupMedalReconstructData(100001, 1, 1, 1, 0);
            using var ctx = NodeOpContext.GetNew("test");
            var opCode = player.ComponentTask.SystemRoot.Input(new NodeOpByBizId(100001, 1), ctx);
            Print($"opCode: {opCode}");
            // 增量累计
            player.Calories = 10;
            Print(medalTaskContainer.ToTreeString(0));
            player.Calories = 30;
            Print(medalTaskContainer.ToTreeString(0));
            // 减少
            player.Calories = 0;
            Print(medalTaskContainer.ToTreeString(0));
            // 非存活状态
            player.LifeCycleFlags = ByteUtil.RemoveFlag(player.LifeCycleFlags, LifeFlags.Alive);
            player.Calories = 10;
            Print(medalTaskContainer.ToTreeString(0));
            player.LifeCycleFlags = ByteUtil.AddFlag(player.LifeCycleFlags, LifeFlags.Alive);
            player.Calories = 30;
            FlipTimewheel(1);
            Print(medalTaskContainer.ToTreeString(0));
        }

        private void TestAcceptNonPreCountTask()
        {
            player.ComponentDataStatistic.SystemRoot.ClearChildren();
            SetupMedalTaskConfig(1000021, 234, [50], 10);
            SetupMedalTaskConfig(1000022, 234, [100], 10);
            SetupMedalReconstructData(1000021, 1, 1, 1, 10);
            SetupMedalReconstructData(1000022, 1, 2, 1, 20);
            medalTaskContainer.AcceptTask(1000021, false);
            Print(medalTaskContainer.ToTreeString(0));
            var taskNode = medalTaskContainer.GetTaskNode(TaskNodeIndex.InProgress, 1000021) as MedalTaskNode;
            medalTaskContainer.CompleteTask(taskNode);
            Print(medalTaskContainer.ToTreeString(0));
        }

        private void TestSyncDeltaMedalInfo()
        {
            ServerInstanceEntity.Instance.OnSendSettleStyleRankPointsToLobbyCallback(null, System.Net.HttpStatusCode.OK, 0);
            Print(medalTaskContainer.ToTreeString(0));
        }

        private static void SetupMedalConfig(int medalId, int level, int styleId, int points, int nextLevelTaskId = 0)
        {
            var medalJson = @"{
      ""medalID"": " + medalId + @",
      ""level"": " + level + @",
      ""type"": 1,
      ""styleID"": " + styleId + @",
      ""task"": [
        [
          2,
          " + nextLevelTaskId + @"
        ]
      ],
      ""styleRankPoints"": [
        [
          1,
          " + points + @"
        ]
      ],
      ""rankID"": 1,
      ""shouldPreCount"": true
    }";
            McCommon.Tables.TBMedal.Update(SimpleJSON.JSONNode.Parse(medalJson));
        }

        private static void SetupMedalTaskConfig(long taskId, int endCondition, long[] endConditionParameter = null, int taskType = 10, int endConditionMode = 0, int isSubTask = 0, int[] subTasks = null)
        {
            var taskJson = @"{
      ""id"": " + taskId + @",
      ""type"": " + taskType + @",
      ""taskId"": " + (taskId > int.MaxValue ? 0 : (int)taskId) + @",
      ""isSubTask"": " + isSubTask + @",
      ""taskPhaseEndCondition"": " + endCondition + @",
      ""autoGrant"": false,";

            // Only add endConditionParameter if it has values
            if (endConditionParameter != null && endConditionParameter.Length > 0)
                taskJson += @"""endConditionParameter"": [" + string.Join(",", endConditionParameter) + @"],";
            else
                taskJson += @"""endConditionParameter"": [],";

            taskJson += @"""endConditionMode"": " + endConditionMode + @",";

            // Only add subTasks if it has values
            if (subTasks != null && subTasks.Length > 0)
                taskJson += @"""subTasks"": [" + string.Join(",", subTasks) + @"]";
            else
                taskJson += @"""subTasks"": []";

            taskJson += @"}";

            McCommon.Tables.TbQuestPhase.Update(SimpleJSON.JSONNode.Parse(taskJson));
        }

        private static void SetupTaskConditionConfig(int conditionId, int targetData, int compareType, int counterChange)
        {
            var conditionJson = @"{
      ""id"": " + conditionId + @",
      ""compareType"": " + compareType + @",
      ""targetData"": " + targetData + @",
      ""counterChange"": " + counterChange + @"
    }";
            McCommon.Tables.TbQuestCondition.Update(SimpleJSON.JSONNode.Parse(conditionJson));
        }

        public override void Cleanup()
        {
            base.Cleanup();
        }
    }
}