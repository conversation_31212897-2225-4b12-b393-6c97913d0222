﻿using SfTools.Editor.Report;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using UnityEditor;
using UnityEditor.Compilation;
using UnityEngine;
using WizardGames.Soc.SocBuild.Editor;
using Debug = UnityEngine.Debug;

namespace WizardGames.Soc.SocSimulator.Editor
{
    public class Build
    {
        /// <summary>
        /// Jenkins打包
        /// </summary>
        public static void BuildPlayerServer()
        {
            /*// 重置csc.rsp文件
            ResetCscRspByCrashSight();
            ResetCscRspByOversea();*/

            var cmdArgs = new CommandLineArgs();
            // 修改csc.rsp文件
            bool isCrashSightEnabled = PlayerBuilder.SetRspDefineSymbolsEnable("ENABLE_CRASHSIGHT", true);
            bool isOverseaEnabled = PlayerBuilder.SetRspDefineSymbolsEnable("OVERSEA", cmdArgs.IsBoolEquals("-oversea", true));
            if (isCrashSightEnabled || isOverseaEnabled)
            {
                AssetDatabase.Refresh();
                UnityEngine.Debug.LogFormat("[HotUpdateBuild] is Compiling Scripts({0})", EditorApplication.isCompiling);
            }
            
            Action buildDHAO = null;
#if !DISABLE_CODEUPDATE
            // 支持服务端hotfix，需要打包热更新首包
            buildDHAO = BuildServerHybridCLR(cmdArgs);
#endif
            BuildCustomType.BuildCustomTypeAb(PlayerBuilder.GetBuildTarget(cmdArgs));

            var builder = new PlayerBuilder();
            builder.GenerateConfig(cmdArgs);
            var report = builder.Build();
            buildDHAO?.Invoke();
            if (report)
            {
                CopyEnginerSymbol(cmdArgs.IsBoolEquals("-isDebug", true));
            }
            CopyExtFile();
            PrepareLinuxAsanEnv();
        }
        static void CopyEnginerSymbol(bool isdev)
        {
            string il2cpppath = isdev ? "linux64_server_development_il2cpp" : "linux64_server_nondevelopment_il2cpp";
            // 获取当前进程
            Process currentProcess = Process.GetCurrentProcess();

            // 获取当前进程的路径
            string processPath = currentProcess.MainModule.FileName;

            // 检查路径中是否包含 "Unity.exe"
            if (processPath.EndsWith("Unity.exe", StringComparison.OrdinalIgnoreCase))
            {
                string processPath1 = processPath.Replace("Unity.exe", $"Data\\PlaybackEngines\\LinuxStandaloneSupport\\Variations\\{il2cpppath}\\LinuxPlayer");
                string processPath2 = processPath.Replace("Unity.exe", $"Data\\PlaybackEngines\\LinuxStandaloneSupport\\Variations\\{il2cpppath}\\LinuxPlayer.debug");
                string processPath3 = processPath.Replace("Unity.exe", $"Data\\PlaybackEngines\\LinuxStandaloneSupport\\Variations\\{il2cpppath}\\LinuxPlayer_s.debug");
                string processPath4 = processPath.Replace("Unity.exe", $"Data\\PlaybackEngines\\LinuxStandaloneSupport\\Variations\\{il2cpppath}\\UnityPlayer.debug");
                File.Copy(processPath1, $"{Application.dataPath}/../BuildTemp/LinuxPlayer", true);
                File.Copy(processPath2, $"{Application.dataPath}/../BuildTemp/LinuxPlayer.debug", true);
                File.Copy(processPath3, $"{Application.dataPath}/../BuildTemp/LinuxPlayer_s.debug", true);
                File.Copy(processPath3, $"{Application.dataPath}/../BuildTemp/UnityPlayer.debug", true);
            }
        }

        private static readonly string cscRspFile = $"{Application.dataPath}/csc.rsp";
        private static readonly string CrashSightEnableDefine = "-define:ENABLE_CRASHSIGHT";
        private static readonly string OverSeaDefine= "-define:OVERSEA";
#if !DISABLE_CODEUPDATE
        private static Action BuildServerHybridCLR(CommandLineArgs args)
        {
            HybridCLRBuildArgs buildArgs = new HybridCLRBuildArgs();
            buildArgs.InitFromCmdArgs(args);
            buildArgs.SyncToEditorUserBuildSettings();
            UnityEngine.Debug.Log($"[HotUpdateBuild] HybridCLRBuildArgs '{buildArgs}'");
            
            if (EditorUserBuildSettings.activeBuildTarget != buildArgs.Target)
                EditorUserBuildSettings.SwitchActiveBuildTarget(BuildPipeline.GetBuildTargetGroup(buildArgs.Target), buildArgs.Target);
            
            float startTime = Time.realtimeSinceStartup;
            HybridCLRBuilder builder = HybridCLRBuilder.BuildCode(ref buildArgs);
            if (builder == null)
            {
                throw new System.Exception("[HotUpdateBuild] BuildDHAs failed");
            }

            float endTime = Time.realtimeSinceStartup;
            UnityEngine.Debug.Log($"[HotUpdateBuild] BuildServerHybridCLR成功, 耗时:{endTime - startTime}秒");
#if USE_HYBRIDCLR_8_1_0
            // 注意：HybridCLR版本8.0.0后，生成MetaVersion或DHAO必须在BuildPlayer之后
            return builder.BuildDHAO;
#else
            return null;
#endif
        }
  #endif
        
        private static void ResetCscRspByCrashSight()       
        {
            if (!File.Exists(cscRspFile)) return;

            var allLines = File.ReadAllLines(cscRspFile);
            var exist = false;
            if (0 < allLines.Length)
            {
                for (var idx = 0; idx < allLines.Length; ++idx)
                {
                    var lineTxt = allLines[idx].Trim();
                    if (string.IsNullOrEmpty(lineTxt)) continue;

                    exist = CrashSightEnableDefine.Equals(lineTxt);
                    if (exist) break;
                }
            }

            if (exist) return;

            var allContent = File.ReadAllText(cscRspFile);
            allContent = $"{allContent}\n{CrashSightEnableDefine}";
            File.WriteAllText(cscRspFile, allContent);
        }
        private static void ResetCscRspByOversea()
        {
            if (!File.Exists(cscRspFile)) return;

            var allLines = File.ReadAllLines(cscRspFile);
            var exist = false;
            if (0 < allLines.Length)
            {
                for (var idx = 0; idx < allLines.Length; ++idx)
                {
                    var lineTxt = allLines[idx].Trim();
                    if (string.IsNullOrEmpty(lineTxt)) continue;

                    exist = OverSeaDefine.Equals(lineTxt);
                    if (exist) break;
                }
            }

            if (exist) return;

            var allContent = File.ReadAllText(cscRspFile);
            bool isoversea = IsOversea();
            if (isoversea)
                allContent = $"{allContent}\n{OverSeaDefine}";
            File.WriteAllText(cscRspFile, allContent);
        }
        public static void UpLoadSymbol()
        {
            List<string> filestr = new List<string>();
            var buildpath = $"{Application.dataPath}/../BuildTemp";
            if (!Directory.Exists(buildpath))
            {
                return;
            }
            var files = Directory.GetFiles(buildpath, "*.*", SearchOption.AllDirectories);
            bool hasGameAssemblydebug = false;
            foreach (var file in files)
            {
                if (file.EndsWith("GameAssembly.debug"))
                {
                    hasGameAssemblydebug = true;
                }
                if (file.EndsWith(".so") || file.EndsWith(".x86_64") || file.EndsWith("_s.debug"))
                {
                    filestr.Add(file);
                }
            }
            var appVersion = /*"trunk-0.1.0"; //*/Application.version;

            var path = $"{Application.dataPath}/Editor/CrashSightTool/crashSightSymbolTool.jar";
            var symbolsFolder = $"{Application.dataPath}/../Symbols";

            if (Directory.Exists(symbolsFolder))
            {
                Directory.Delete(symbolsFolder, true);
            }
            Directory.CreateDirectory(symbolsFolder);

            foreach (var file in filestr)
            {
                var fileName = Path.GetFileName(file);
                if (fileName.EndsWith("_s.debug"))
                {
                    fileName = fileName.Replace("_s.debug", ".debug");
                }
                else if (hasGameAssemblydebug)
                {
                    if (file.EndsWith("GameAssembly.so"))
                    {
                        continue;
                    }
                    if (fileName.EndsWith("GameAssembly.debug"))
                    {
                        fileName = fileName.Replace(".debug", ".so");
                    }
                }
                File.Copy(file, $"{symbolsFolder}\\{fileName}", true);
            }

            //java -jar crashSightSymbolTool.jar -i simulatorcrash/symbol -version 0.0.2 -p linux -u -url https://crashsight.qq.com/openapi/file/upload/symbol -cfi -stif -id ef21bbe25e -key 31fb4ce2-8dfc-4635-8385-ab8743abc15d
            // 上传CrashSight
            bool isoversea = IsOversea();
            var command =
                $@"java -jar {path} -i {symbolsFolder} -version {appVersion} -p linux -u -url https://crashsight.qq.com/openapi/file/upload/symbol -cfi -stif -id ef21bbe25e -key 31fb4ce2-8dfc-4635-8385-ab8743abc15d";
            if (isoversea)
            {
                command =
                $@"java -jar {path} -i {symbolsFolder} -version {appVersion} -p linux -u -url https://crashsight.wetest.net/openapi/file/upload/symbol -cfi -stif -id 58b68e1d21 -key 505a0df1-79df-43dc-bfa9-f6cf15186a03";
            }
            UnityEngine.Debug.Log($"Execute command: {command}");

            var processInfo = new ProcessStartInfo("cmd.exe", "/c " + command);
            processInfo.CreateNoWindow = true;
            processInfo.UseShellExecute = false;
            // Redirect the output
            processInfo.RedirectStandardOutput = true;

            var process = Process.Start(processInfo);
            // Read the streams
            string output = process.StandardOutput.ReadToEnd();

            process.WaitForExit();
            process.Close();
        }
        public static bool IsOversea()
        {
            string[] cmdArgs = Environment.GetCommandLineArgs();

            for (int i = 0; i < cmdArgs.Length - 1; i++)
            {
                var arg = cmdArgs[i];
                if (arg.StartsWith("-oversea"))
                {
                    var value = cmdArgs[i + 1];
                    if (!value.StartsWith("-"))
                    {
                        if(value == "true")
                            return true;
                    }
                }
            }
            return false;
        }

        public static void BuildLinux()
        {
            UnityEngine.Debug.Log("Build Linux");

            if (EditorUserBuildSettings.standaloneBuildSubtarget != StandaloneBuildSubtarget.Server)
                EditorUserBuildSettings.standaloneBuildSubtarget = StandaloneBuildSubtarget.Server;


            if (EditorUserBuildSettings.activeBuildTarget != BuildTarget.StandaloneLinux64)
                EditorUserBuildSettings.SwitchActiveBuildTarget(BuildTargetGroup.Standalone, BuildTarget.StandaloneLinux64);


            List<EditorBuildSettingsScene> sceneList = new List<EditorBuildSettingsScene>();

            EditorBuildSettingsScene scene = new EditorBuildSettingsScene();

            foreach (EditorBuildSettingsScene e in EditorBuildSettings.scenes)
            {
                if (e != null && e.enabled)
                {
                    UnityEngine.Debug.Log(e.path);
                    sceneList.Add(e);
                }
            }

            DirectoryInfo dir = new DirectoryInfo("..\\Linux");
            if(!dir.Exists)
                dir.Create();
            

            string outPath = "..\\Linux\\SocSimulator";
            BuildPipeline.BuildPlayer(sceneList.ToArray(), outPath, BuildTarget.StandaloneLinux64, BuildOptions.None);
        }

        private static void PrepareLinuxAsanEnv()
        {
            var     flag        = new String("SOC_ENABLE_ASAN");
            var     env_val     = Environment.GetEnvironmentVariable(flag);
            var     enabled     = env_val != null && string.Equals(env_val, "true", StringComparison.OrdinalIgnoreCase);
            Debug.Log($"[djz][ASAN]PrepareLinuxAsanEnv {flag}={enabled}");

            do
            {
                if(!enabled)
                    break;

                Debug.Log($"[djz][ASAN]begin copy");

                var     top_dir         = Path.Combine(Application.dataPath, @"../../../");
                var     build_output    = Path.Combine(top_dir, "SocServer/SocSimulator/BuildTemp");
                var     dll_dir         = Path.Combine(top_dir, @"SocCommon/Soc.ABFastBuild/Editor/ABFastBuild");
                var     dlls            = new string[] {
                    "libclang_rt.asan-x86_64.so",
                };

                foreach(var cur in  dlls)
                {
                    var     dll_path    = Path.Combine(dll_dir, cur);
                    File.Copy(dll_path, Path.Combine(build_output, cur), true);
                    Debug.Log($"[djz][ASAN]Copy {Path.GetFullPath(dll_path)} to {Path.GetFullPath(Path.Combine(build_output, cur))}");
                }
                
                var  llvm_path = Path.Combine(top_dir, "SocTools/llvm-symbolizer");
                if (!Directory.Exists(Path.Combine(build_output, "llvm-symbolizer")))
                {
                    Directory.CreateDirectory(Path.Combine(build_output, "llvm-symbolizer"));
                }
                if (Directory.Exists(llvm_path))
                {
                    var     llvm_files = Directory.GetFiles(llvm_path);
                    foreach (var cur in llvm_files)
                    {
                        var     file_name   = Path.GetFileName(cur);
                        var     dst_path    = Path.Combine(build_output, "llvm-symbolizer", file_name);
                        File.Copy(cur, dst_path, true);
                    }
                }
                else
                {
                    Debug.LogError($"[djz][ASAN]llvm-symbolizer path not found: {llvm_path}");
                }

                var     unity_path      = Environment.GetEnvironmentVariable("UNITY_EXE");
                var     unity_dir       = Path.GetDirectoryName(unity_path);
                var     asan_dir        = Path.Combine(unity_dir, "Data/PlaybackEngines/LinuxStandaloneSupport/Variations/linux64_server_nondevelopment_il2cpp/asan");
                var     asan_files      = Directory.GetFiles(asan_dir);
                foreach (var cur in asan_files)
                {
                    if (cur.EndsWith(".txt"))
                        continue;

                    var     file_name   = Path.GetFileName(cur);
                    var     dst_path    = Path.Combine(build_output, file_name);
                    if(string.Equals(file_name, "LinuxPlayer_PatchForAsan", StringComparison.OrdinalIgnoreCase))
                        dst_path    = Path.Combine(build_output, "SocSimulator.x86_64");

                    File.Copy(cur, dst_path, true);
                    Debug.Log($"[djz][ASAN]Copy {Path.GetFullPath(cur)} to {Path.GetFullPath(dst_path)}");
                }

                var     backupFolder    = Path.Combine(build_output, "SocSimulator_BackUpThisFolder_ButDontShipItWithYourGame");
                var     sourceFile      = Path.Combine(backupFolder, "GameAssembly.debug");
                var     destFile        = Path.Combine(build_output, "GameAssembly.debug");                
                if (!File.Exists(sourceFile))
                {
                    Debug.Log($"[djz][ASAN]GameAssembly.debug not found at {Path.GetFullPath(sourceFile)}, skipping copy");
                }
                else
                {
                    File.Copy(sourceFile, destFile, true);
                    Debug.Log($"[djz][ASAN]Copy GameAssembly.debug from {Path.GetFullPath(sourceFile)} to {Path.GetFullPath(destFile)}");
                }

                Debug.Log($"[djz][ASAN]done copy");
            } while (false);
        }

        private static void CopyExtFile()
        {
            var rootDir= Path.Combine(Application.dataPath, @"../../../");
            var outputDir= Path.Combine(rootDir, "SocServer/SocSimulator/BuildTemp");
            var copyFiles = new string[]
            {
                Path.Combine(rootDir, @"SocTools/ContainerCpu/libcontainer_cpu.so")
            };

            foreach(var filePath in copyFiles)
            {
                // 判断文件是否存在
                if (!File.Exists(filePath))
                {
                    Debug.LogError($"CopyExtFile File not found: {filePath}");
                    continue;
    }
                File.Copy(filePath, Path.Combine(outputDir, Path.GetFileName(filePath)), true);
                Debug.Log($"CopyExtFile Copy {filePath} to {outputDir}");
            }
        }
    }
}