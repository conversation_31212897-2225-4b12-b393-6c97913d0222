using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Profiling;
using UnityEngine.SceneManagement;
using Wwise.Wooduan.BVH;
using Wwise.Wooduan.BVH.Adapter;
using Wwise.Wooduan.Components;
using Wwise.Wooduan.Tools;

namespace Wwise.Wooduan.Core
{
    public enum HRTFMode
    {
        // Auro,
        Resonance,
        None
    }
    
    
    public static class SpatialAudioManager
    {
        private static bool IsInitialized;
        
        public static void Initialize()
        {
            _buildings = new List<AkBuilding>();
            _scatteredRooms = new List<AkRoom>();
            _reverbZones = new List<AkReverbZone>();
            _scatteredRoomsBVH = new BVH<AkRoom>(new BVHAkRoomAdapter(), null, 1);
            IsInitialized = true;
        }

        internal static void Terminate()
        {
            _buildings = null;
            _scatteredRooms = null;
            _reverbZones = null;
            _scatteredRoomsBVH = null;
            IsInitialized = false;
        }

        #region Building
        private static List<AkBuilding> _buildings;

        internal static void RegisterBuilding(AkBuilding building)
        {
            if (IsInitialized && !_buildings.Contains(building))
                _buildings.Add(building);
        }

        internal static void UnregisterBuilding(AkBuilding building)
        {
            if (IsInitialized && _buildings.Contains(building))
                _buildings.Remove(building);
        }
        #endregion Building
        
        #region Cull
        public static bool CullByEmitter = false;
        private static int _currentBuildingIndex, _currentRoomIndex;

        internal static void Update()
        {
            if (!IsInitialized)
                return;
            //暂时去掉Profiler
            //Profiler.BeginSample("SpatialAudioManager.Update");
            var listenerPosition = ListenerManager.GetListenerLocation();
            if (listenerPosition == Vector3.zero)
                return;
            
            if (_currentBuildingIndex < _buildings.Count)
            {
                var building = _buildings[_currentBuildingIndex];
                if (building)
                    building.TryCull(listenerPosition);
                _currentBuildingIndex++;
            }
            else
                _currentBuildingIndex = 0;

            if (_currentRoomIndex < _scatteredRooms.Count)
            {
                var room = _scatteredRooms[_currentRoomIndex];
                if (room)
                    room.TryCull(listenerPosition);
                _currentRoomIndex++;
            }
            else
                _currentRoomIndex = 0;
            //Profiler.EndSample();
        }
        #endregion Cull

        #region Room

        public static BVH<AkRoom> _scatteredRoomsBVH;
        private static List<AkRoom> _scatteredRooms;
        private static List<AkReverbZone> _reverbZones;
        public static AkRoomPortal ClosestPortalToOutdoor;
        
        internal static void RegisterScatteredRoom(AkRoom room)
        {
            if (IsInitialized && !_scatteredRooms.Contains(room))
            {
                _scatteredRooms.Add(room);
                _scatteredRoomsBVH.Add(room);
            }
        }

        internal static void UnregisterScatteredRoom(AkRoom room)
        {
            if (IsInitialized && _scatteredRooms.Contains(room))
            {
                _scatteredRooms.Remove(room);
                _scatteredRoomsBVH.Remove(room);
            }
        }
        
        internal static void RegisterReverbZone(AkReverbZone reverbZone)
        {
            if (IsInitialized && !_reverbZones.Contains(reverbZone))
                _reverbZones.Add(reverbZone);
        }

        internal static void UnregisterReverbZone(AkReverbZone reverbZone)
        {
            if (IsInitialized && _reverbZones.Contains(reverbZone))
                _reverbZones.Remove(reverbZone);
        }

        internal static bool ContainsKeyword(string str, string[] keywords)
        {
            foreach (string keyword in keywords)
            {
                if (str.Contains(keyword))
                    return true;
            }

            return false;
        }

        static readonly string[] KeyWords = { "Player", "AmbienceEmitter" };
        public static void UpdateSpatialAudioRoom(AkGameObj akGameObj)
        {
            if (!RoomEnabled || !akGameObj)
                return;
#if UNITY_EDITOR && AUDIO_BVH_DEBUG
           AkRoomBVHDebug.Draw();
#endif
            
            Profiler.BeginSample("UpdateSpatialAudioRoom");
            var position = akGameObj.GetPosition();

            var newSpace = FindSpaceAtPosition(position,
                (akGameObj.IsOwnedByListener && AkGameObj.SelfEmitter)
                    ? AkGameObj.SelfEmitter.gameObject
                    : akGameObj.GameObject);
            // listener has more detailed transition between rooms
            if (newSpace && akGameObj.IsOwnedByListener)
            {
                // detect if listener is inside a portal
                var portal = newSpace.FindPortalAtPosition(position, PortalType.All, 0f);
                SetListenerInPortal(portal, akGameObj);
                if (newSpace is AkBuilding)
                {
                    if (portal && !portal.IsConnectingToOutdoor())
                    {
                        Profiler.EndSample();
                        return;
                    }
                }
            }
            var oldRoom = akGameObj.CurrentRoom;
            var oldSpatialAudioLevel = akGameObj.currentSpatialAudioLevel;
            akGameObj.CurrentSpace = newSpace;
            if (akGameObj.IsOwnedByListener && AkGameObj.SelfEmitter)
                AkGameObj.SelfEmitter.CurrentSpace = akGameObj.CurrentSpace;
            var newRoom = akGameObj.CurrentRoom;
            if (newRoom != oldRoom || (oldRoom == null && oldSpatialAudioLevel > 0))
            {
                string[] keywords = KeyWords;
                // // No new room, set to outdoor
                if (!newRoom)
                {
                    if (akGameObj.IsOwnedByListener && AkGameObj.SelfEmitter && ContainsKeyword(AkGameObj.SelfEmitter.gameObject.name, keywords))
                    {
                        AkSoundEngine.SetGameObjectInRoom(AkGameObj.SelfEmitter.gameObject, AkRoom.INVALID_ROOM_ID);
                        AkSoundEngine.SetRTPCValue("SpatialAudio_Level", 0, AkGameObj.SelfEmitter.gameObject);
                        AkGameObj.SelfEmitter.currentSpatialAudioLevel = 0;
                    }
                    else if (ContainsKeyword(akGameObj.gameObject.name, keywords))
                    {
                        AkSoundEngine.SetGameObjectInRoom(akGameObj.gameObject, AkRoom.INVALID_ROOM_ID);
                        AkSoundEngine.SetRTPCValue("SpatialAudio_Level", 0, akGameObj.gameObject);
                    }

                    akGameObj.currentSpatialAudioLevel = 0;

                    AkWooduanHelper.DebugToConsole(AudioLogVerbosity.Notification, AudioObjectType.Room, AudioTriggerSource.Code, 
                        AudioAction.SetValue, "No Room", akGameObj.gameObject);
                    
                }
                else
                {
                    AkSoundEngine.SetRTPCValue("SpatialAudio_Level", newRoom.spatialAudioLevel, newRoom.gameObject);
                    if (akGameObj.IsOwnedByListener && AkGameObj.SelfEmitter && ContainsKeyword(AkGameObj.SelfEmitter.gameObject.name, keywords))
                    {
                        AkSoundEngine.SetGameObjectInRoom(AkGameObj.SelfEmitter.gameObject, newRoom.GetID());
                        AkSoundEngine.SetRTPCValue("SpatialAudio_Level", newRoom.spatialAudioLevel, AkGameObj.SelfEmitter.gameObject);
                        AkGameObj.SelfEmitter.currentSpatialAudioLevel = newRoom.spatialAudioLevel;
                    }
                    else if(ContainsKeyword(akGameObj.gameObject.name, keywords))
                    {
                        AkSoundEngine.SetGameObjectInRoom(akGameObj.gameObject, newRoom.GetID());
                        AkSoundEngine.SetRTPCValue("SpatialAudio_Level", newRoom.spatialAudioLevel, akGameObj.gameObject);
                    }

                    if (newRoom.roomEvent.IsValid() && IsPlayer(akGameObj))
                    {
                        if (newRoom.eventPlayingID == 0)
                            newRoom.eventPlayingID = AkSoundEngine.PostEventOnRoom(newRoom.roomEvent.Id, newRoom.GetID());
                        else
                            AudioManager.ResumeEvent(newRoom.roomEvent.Name, newRoom.gameObject);
                    }
                    
                    akGameObj.currentSpatialAudioLevel = newRoom.spatialAudioLevel;

                    AkWooduanHelper.DebugToConsole(AudioLogVerbosity.Notification, AudioObjectType.Room, AudioTriggerSource.Code, 
                        AudioAction.SetValue, newRoom.name, akGameObj.gameObject, "Bus=" + newRoom.reverbAuxBus?.Name);
                }

                if (oldRoom != null && IsPlayer(akGameObj))
                {
                    AudioManager.PauseEvent(oldRoom.roomEvent.Name, oldRoom.gameObject);
                }
            }
            Profiler.EndSample();
        }

        internal static bool IsPlayer(AkGameObj akGameObj)
        {
            return (akGameObj.IsOwnedByListener && AkGameObj.SelfEmitter && AkGameObj.SelfEmitter.gameObject.name.Contains("Player")) || akGameObj.gameObject.name.Contains("Player");
        }

        internal static void UpdateReverbZone(AkGameObj akGameObj)
        {
            var position = akGameObj.GetPosition();
            var found = false;
            foreach (var reverbZone in _reverbZones)
            {
                if (reverbZone.IsPositionWithinSpace(position))
                {
                    found = true;
                    if (akGameObj.CurrentReverbZone != reverbZone)
                        reverbZone.OnEmitterEntered(akGameObj);
                }
            }
            if (!found && akGameObj.CurrentReverbZone)
                akGameObj.CurrentReverbZone.OnEmitterExited(akGameObj);
        }

        private static AkSpace FindSpaceAtPosition(Vector3 position,GameObject emitter)
        {
            AkSpace resultSpace = AkOutdoor.GetInstance();
            if (IsInitialized)
            {
                // search buildings
                foreach (var building in _buildings)
                {
                    var space = building.FindSpaceAtPosition(position);
                    if (space)
                        return space;
                }
                // search scattered rooms
                var highestPriority = -1;
                AkSpace foundSpace = null;
                var scatteredRooms = FindSpaceAtPosition(position);
                foreach (var room in scatteredRooms)
                {
                    var space = room.FindSpaceAtPosition(position);
                    if (space && room.priority > highestPriority)
                    {
                        foundSpace = space;
                        highestPriority = room.priority;
                    }
                }
                // foreach (var room in _scatteredRooms)
                // {
                //     var space = room.FindSpaceAtPosition(position);
                //     if (space && room.priority > highestPriority)
                //     {
                //         foundSpace = space;
                //         highestPriority = room.priority;
                //     }
                // }
                if (foundSpace)
                    return foundSpace;
            }
            return resultSpace;
        }

        private static List<AkRoom> tmpRooms = new List<AkRoom>();
        private static List<AkRoom> FindSpaceAtPosition(Vector3 position)
        {
            tmpRooms.Clear();
            var nodes = _scatteredRoomsBVH.Traverse(BVHHelper.NodeTraversalPosition(position));
            foreach (var node in nodes)
            {
                if (node.Objects != null)
                    tmpRooms.AddRange(node.Objects);
            }

            return tmpRooms;
        }
        
        internal static AkSpatialAudioRoom FindRoomAtPosition(Vector3 position)
        {
            var space = FindSpaceAtPosition(position,null);
            if (space)
                return space.Room;
            AkSpatialAudioRoom resultRoom = AkOutdoor.GetInstance();
#if UNITY_EDITOR
            if (!Application.isPlaying)
            {
                var highestPriority = -1;
                var colliders = UnityEngine.Physics.OverlapSphere(position, 0);
                foreach (var collider in colliders)
                {
                    space = collider.gameObject.GetComponent<AkSpace>();
                    if (space)
                    {
                        var room = space.Room as AkRoom;
                        if (room && room.priority > highestPriority)
                        {
                            resultRoom = room;
                            highestPriority = room.priority;
                        }
                    }
                }
            }
#endif
            return resultRoom;
        }
        
        internal static void CalculateListenerDistanceToOutdoor(AkSpatialAudioRoom listenerRoom, Transform listenerTransform)
        {
            if (!listenerRoom)
                return;
            if (!listenerRoom.IsOutdoor)
            {
                var angleToOutdoor = 180f;
                ClosestPortalToOutdoor = listenerRoom.FindNearestPortalOpenedToOutdoor(listenerTransform.position, out var distance);
                if (ClosestPortalToOutdoor)
                {
                    var portalTransform = ClosestPortalToOutdoor.transform;
                    angleToOutdoor = distance < 1f ? Vector3.Angle(listenerTransform.forward, -portalTransform.forward) : 90f;
                }
                AudioManager.SetRTPCValue("Distance_To_Outdoor", distance);
                AudioManager.SetRTPCValue("Camera_Angle_To_Outdoor", angleToOutdoor);
            }
            else
            {
                ClosestPortalToOutdoor = null;
                AudioManager.SetRTPCValue("Distance_To_Outdoor", 0f);
                AudioManager.SetRTPCValue("Camera_Angle_To_Outdoor", 0f);
            }
        }

        internal static int CalculateFloorDifference(AkGameObj emitter, AkAudioListener listener)
        {
            if (!listener)
                return 0;
            if (!listener.CurrentSpace || !emitter.CurrentSpace)
                return 0;
            if (listener.CurrentSpace is AkOutdoor || emitter.CurrentSpace is AkOutdoor)
                return 0;
            if (listener.CurrentSpace is AkBuilding || emitter.CurrentSpace is AkBuilding)
                return 0;
            var listenerFloor = listener.GetFloor();
            var emitterFloor = emitter.GetFloor();
            return emitterFloor - listenerFloor;
        }
        
        
        
        #endregion Room
        
        #region Portal
        private static AkRoomPortal _listenerInPortal;
        private static void SetListenerInPortal(AkRoomPortal newPortal, AkGameObj listener)
        {
            if (newPortal == _listenerInPortal)
                return;
            if (_listenerInPortal)
                _listenerInPortal.OnListenerExited(listener);
            if (newPortal)
                newPortal.OnListenerEntered(listener);
            _listenerInPortal = newPortal;
        }
        
        public static void TogglePortalAtLocation(Vector3 location, bool toggle, PortalType portalType)
        {
            var foundPortal = FindPortalAtPosition(location, portalType);
            if (foundPortal)
                foundPortal.TogglePortal(toggle);
            else
                AkWooduanHelper.DebugToConsole(AudioLogVerbosity.Error, AudioObjectType.Portal, AudioTriggerSource.Code, AudioAction.Activate, "", null, "Portal not found");
        }

        private static AkRoomPortal FindPortalAtPosition(Vector3 position, PortalType portalType, float tolerance = 0.2f)
        {
            if (!IsInitialized)
                return null;
            
            foreach (var building in _buildings)
            {
                if (building.IsPositionWithinSpace(position))
                    return building.FindPortalAtPosition(position, portalType, tolerance);
            }
        
            foreach (var room in _scatteredRooms)
            {
                if (room.IsCulled)
                {
                    if (room.GetDistanceToPositionByXZ(position) > room.cullDistance)
                        continue;
                }
                var portal = room.FindPortalAtPosition(position, portalType, tolerance);
                if (portal)
                    return portal;
            }
            
            return null;
        }
        #endregion Portal
        
        #region OcclusionObstruction

        public static bool OcclusionEnabled = false;
        public static bool ObstructionEnabled = false;
        private const float ObstructionStartAngle = 60f;
        public static LayerMask OcclusionObstructionLayer => LayerMask.GetMask("NoCollisionWithEntity", "Player");

        internal static void UpdateOcclusionObstruction(AkGameObj emitter, out float occValue, out float obsValue)
        {
            if (!OcclusionEnabled)
            {
                occValue = obsValue = 0f;
                return;
            }

            Profiler.BeginSample("SpatialAudioManager.UpdateOcclusionObstruction");
		    var listenerPosition = ListenerManager.GetListenerLocation();
            var emitterPosition = emitter.GetPosition();
            var difference = listenerPosition - emitterPosition;
		    var distEmitterListener = difference.magnitude;
            occValue = CastOcclusionRays(emitterPosition, difference, distEmitterListener);
            if (occValue > 0 && ObstructionEnabled && distEmitterListener < 50)
            {
                var propagateDirection = difference / distEmitterListener;
                var maxNumOfRays = Mathf.RoundToInt(60 / Mathf.Clamp(distEmitterListener, 10, 30));
                var angleDecrementEmitter = ObstructionStartAngle / maxNumOfRays;
                var obsAngleEmitter = CastObstructionRaysFromEmitter(emitterPosition, propagateDirection,
                                          ObstructionStartAngle, angleDecrementEmitter, distEmitterListener,
                                          out var axis) + angleDecrementEmitter / 2f;
                var startAngleListener = Mathf.Min(90 - obsAngleEmitter, ObstructionStartAngle);
                var angleDecrementListener = startAngleListener / maxNumOfRays;
                var obsAngleListener = CastObstructionRayFromListener(listenerPosition, -propagateDirection, axis,
                                           obsAngleEmitter, startAngleListener, angleDecrementListener,
                                           distEmitterListener) + angleDecrementListener / 2f;
                obsValue = obsAngleEmitter + obsAngleListener;
#if UNITY_EDITOR
                if (AkWwiseEditorSettings.Instance.showOccObsRays)
                {
                    var localAxis = Vector3.Cross(-propagateDirection, axis);
                    var newDirection = Quaternion.AngleAxis(obsAngleListener, localAxis) * -propagateDirection;
                    var magnitude =
                        CalculateListenerSideRayLength(distEmitterListener, obsAngleEmitter, obsAngleListener);
                    var intersectPoint = listenerPosition + newDirection * magnitude;
                    Debug.DrawRay(emitterPosition, intersectPoint - emitterPosition, Color.magenta, 1f);
                    Debug.DrawRay(listenerPosition, intersectPoint - listenerPosition, Color.magenta, 1f);
                }
#endif
            }
            else
                obsValue = 0f;
            Profiler.EndSample();
        }
	    
	    private static float CastOcclusionRays(Vector3 startPosition, Vector3 difference, float magnitude)
        {
            var direction = difference / magnitude;
		    var hit = new RaycastHit();
		    var result = Physics.Raycast(startPosition, direction, out hit, magnitude, ~OcclusionObstructionLayer);
#if UNITY_EDITOR
            if (AkWwiseEditorSettings.Instance.showOccObsRays)
                Debug.DrawRay(startPosition, direction * magnitude, result ? Color.yellow : Color.green, 1f);
#endif
		    if (!result) 
			    return 0;
            if (magnitude > 40)
                return Mathf.Min(magnitude * 2, 100);

            var hitPointFromEmitter = hit.point;
            startPosition += difference;
            result = Physics.Raycast(startPosition, -direction, out hit, magnitude, ~OcclusionObstructionLayer);

            if (!result) 
                return 0;
      
            var hitPointFromListener = hit.point;
            var obstacleThickness = Vector3.Distance(hitPointFromEmitter ,hitPointFromListener);
            return Mathf.Clamp(obstacleThickness * 5f, 0f, 100f);
        }
	    
	    private static float CastObstructionRaysFromEmitter(Vector3 startPoint, Vector3 direction, float angleEmitter, float angleDecrement, float distEmitterListener, out Vector3 axis)
        {
            axis = Vector3.up;
            var angleUp = CastObstructionRayFromEmitter(startPoint, direction, Vector3.up, angleEmitter, angleDecrement, distEmitterListener);
            var angleLeft = CastObstructionRayFromEmitter(startPoint, direction, Vector3.left, angleUp, angleDecrement, distEmitterListener);
            if (angleLeft < angleUp)
                axis = Vector3.left;
            var angleRight = CastObstructionRayFromEmitter(startPoint, direction, Vector3.right, angleLeft, angleDecrement, distEmitterListener);
            if (angleRight < angleLeft)
                axis = Vector3.right;
            return Mathf.Min(angleRight, ObstructionStartAngle);
	    }

        private static float CastObstructionRayFromEmitter(Vector3 startPoint, Vector3 rayCastDirection, Vector3 rotateAxis, float angleEmitter, float angleDecrement, float distEmitterListener)
        {
            if (angleEmitter < 5)
                return 0;
            
            var magnitude = CalculateEmitterSideRayLength(distEmitterListener, angleEmitter);
            var localAxis = Vector3.Cross(rayCastDirection, rotateAxis);
            var newDirection = Quaternion.AngleAxis(angleEmitter, localAxis) * rayCastDirection;
            var blocked = Physics.Raycast(startPoint, newDirection, magnitude, ~OcclusionObstructionLayer);
#if UNITY_EDITOR
		    if (AkWwiseEditorSettings.Instance.showOccObsRays)
			    Debug.DrawRay(startPoint, newDirection * magnitude, blocked ? Color.blue : Color.cyan, 1f);
#endif
            if (blocked) 
                return angleEmitter;
            return CastObstructionRayFromEmitter(startPoint, rayCastDirection, rotateAxis, angleEmitter - angleDecrement, angleDecrement, distEmitterListener);
        }
        
        private static float CastObstructionRayFromListener(Vector3 startPoint, Vector3 rayCastDirection, Vector3 rotateAxis, float angleEmitter, float angleListener, float angleDecrement, float distEmitterListener)
        {
            if (angleEmitter < 5)
                return 0;

            var magnitude = CalculateListenerSideRayLength(distEmitterListener, angleEmitter, angleListener);
            var localAxis = Vector3.Cross(rayCastDirection, rotateAxis);
            var newDirection = Quaternion.AngleAxis(angleListener, localAxis) * rayCastDirection;
            var blocked = Physics.Raycast(startPoint, newDirection, magnitude, ~OcclusionObstructionLayer);
#if UNITY_EDITOR
            if (AkWwiseEditorSettings.Instance.showOccObsRays)
                Debug.DrawRay(startPoint, newDirection * magnitude, blocked ? Color.blue : Color.cyan, 1f);
#endif
            if (blocked) 
                return angleListener;
            return CastObstructionRayFromListener(startPoint, rayCastDirection, rotateAxis, angleEmitter, angleListener - angleDecrement, angleDecrement, distEmitterListener);
        }

        private static float CalculateListenerSideRayLength(float distEmitterListener, float angleEmitter, float angleListener)
        {
            var angleRadEmitter = Mathf.Deg2Rad * angleEmitter;
            var angleRadListener = Mathf.Deg2Rad * angleListener;
            return distEmitterListener * Mathf.Tan(angleRadEmitter) / (Mathf.Tan(angleRadEmitter) + Mathf.Tan(angleRadListener)) / Mathf.Cos(angleRadListener);
        }

        private static float CalculateEmitterSideRayLength(float distEmitterListener, float angleEmitter)
        {
            return distEmitterListener * Mathf.Cos(Mathf.Deg2Rad * angleEmitter);
        }
        #endregion OcclusionObstruction

        #region Settings
        public static bool RoomEnabled = true;
        public static bool GeometryEnabled = true;
        
        public static void ToggleSpatialAudio(bool toggle)
        {
            AudioManager.SetState("SpatialAudio", toggle ? "On" : "Off");
            ToggleOcclusion(toggle);
            //ToggleObstruction(toggle);
            ToggleGeometry(toggle);
            ToggleRoom(toggle);
        }

        public static void ToggleRoom(bool toggle)
        {
            if (RoomEnabled != toggle)
            {
                RoomEnabled = toggle;
                RefreshAll();
            }
        }
        
        public static void ToggleOcclusion(bool toggle)
        {
            OcclusionEnabled = toggle;
        }
        
        public static void ToggleObstruction(bool toggle)
        {
            ObstructionEnabled = toggle;
        }

        public static void ToggleGeometry(bool toggle)
        {
            if (GeometryEnabled != toggle)
            {
                GeometryEnabled = toggle;
                RefreshAll();
            }
        }

        private static void RefreshAll()
        {
            if (_buildings == null)
                return;
            foreach (var building in _buildings)
            {
                building.Refresh(true);
            }
        }

        public static void SwitchHRTFMode(HRTFMode newMode)
        {
            switch (newMode)
            {
                // case HRTFMode.Auro:
                // {
                    // AkChannelConfig config = new AkChannelConfig();
                    // config.SetStandard(AkSoundEngine.AK_SPEAKER_SETUP_AURO_13POINT1_751);
                    // AudioManager.SetBusConfig("Binaural", config);
                    // AudioManager.SetBusEffect("Binaural", "SSJJ_Auro_Headphone", 0);
                    // break;
                // }
                case HRTFMode.Resonance:
                {
                    AkChannelConfig config = new AkChannelConfig();
                    config.SetAmbisonic(16);
                    AudioManager.SetBusConfig("Binaural", config);
                    AudioManager.SetBusEffect("Binaural", "SOC_Resonance_Audio_Renderer", 0);
                    break;
                }
                default:
                {
                    AkChannelConfig config = new AkChannelConfig();
                    config.Clear();
                    AudioManager.SetBusConfig("Binaural", config);
                    AudioManager.ClearBusEffect("Binaural", 0);
                    break;
                }
            }
        }
        #endregion Settings
    }
}