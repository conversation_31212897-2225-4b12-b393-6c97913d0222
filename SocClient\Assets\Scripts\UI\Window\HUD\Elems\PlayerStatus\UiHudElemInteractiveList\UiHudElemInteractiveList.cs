﻿using Cysharp.Text;
using FairyGUI;
using SharedUnity;
using Sirenix.Utilities;
using Soc.Vehicle.Util;
using Soc.Vehicle.Util.Extension;
using SocVehicle.Scripts.Entity.Vehicle;
using System;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Character;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Data.IOInteractiveEnum;
using WizardGames.Soc.Common.Data.Logic;
using WizardGames.Soc.Common.Data.system;
using WizardGames.Soc.Common.Data.units;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.Part;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Common.Profile;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.Config;
using WizardGames.Soc.Common.Unity.Construction;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Go;
using WizardGames.Soc.Common.Unity.Main;
using WizardGames.Soc.Common.Unity.Soc.Common.Unity.Runtime.Safety;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Procedural;
using WizardGames.Soc.SDK;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocClient.Audio;
using WizardGames.Soc.SocClient.ClientItem;
using WizardGames.Soc.SocClient.Construction.BuildMode;
using WizardGames.Soc.SocClient.Control;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Go;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Plant;
using WizardGames.Soc.SocClient.Ui.Utils;
using WizardGames.Soc.SocClient.Utility;
using WizardGames.Soc.SocClient.Water;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.SocClient.Ui
{
    /// <summary>
    /// 交互列表
    /// </summary>
    public partial class UiHudElemInteractiveList : UiHudElem
    {
        private static SocLogger Log = LogHelper.GetLogger(typeof(UiHudElemInteractiveList));

        /// <summary>
        /// 交互Id检测
        /// </summary>
        public InteractiveIdListChecker InteractiveIdListChecker { get; private set; }

        /// <summary>
        /// cd按钮字典
        /// </summary>
        private Dictionary<int, UiInteractiveBtnCdType> uiInteractiveBtnCdTypeDic;

        /// <summary>
        /// 根节点
        /// </summary>
        private GComponent interactiveRoot;

        private Controller doorSwitchBtnController;

        /// <summary>
        /// 标题
        /// </summary>
        private GTextField titleText;

        /// <summary>
        /// 标题控制器
        /// </summary>
        private Controller titleController;

        /// <summary>
        /// 返回按钮
        /// </summary>
        private GButton btnReturn;

        /// <summary>
        /// 交互列表
        /// </summary>
        private GList interactiveBtnList;

        /// <summary>
        /// 交互列表的状态控制器
        /// </summary>
        private Controller stateCtrl;
        /// <summary>
        /// 显示列表数量
        /// </summary>
        private Controller itemCountCtrl;
        /// <summary>
        /// 根据玩家状态来控制交互列表的显示
        /// </summary>
        private bool isCloseInteractiveList = false;

        /// <summary>
        /// 当前掠夺对象的ID
        /// </summary>
        private long curLootEntityId = -1;

        private Collider[] tempColliders = new Collider[128];

        /// <summary>
        /// 是否结束掠夺
        /// </summary>
        public static bool isCloseLooting = true;

        /// <summary>
        /// 记录上一个交互Id
        /// </summary>
        private int lastBoxId = -1;

        /// <summary>
        /// 刷卡回调
        /// </summary>
        public static Action<IOGo, Vector3> OnCardSwiped;

        private Dictionary<GObject, GObject> uiGObjectPool = new Dictionary<GObject, GObject>();

        /// <summary>
        /// 更新列表的时间轮Id
        /// </summary>
        private long updateListId = -1;

        private long timerId = 0;

        #region UI的一些位置常量

        // 列表Item默认高度
        private readonly int ITEM_HEIGHT = 92;

        // 展开的列表（默认为准星）
        private List<int> expandInteractiveIdList = new List<int>();

        // 用于检测交互列表是否有变化
        private List<int> oldExpandInteractiveIdList = new List<int>();

        // 范围交互列表
        private List<int> nearbyInteractiveIdList = new List<int>();

        // 范围交互列表类型
        private Dictionary<EInteractionType, int> nearbyInteractiveType = new Dictionary<EInteractionType, int>();

        private int curEyeEntityTypeId = -1;
        // 当前注视的实体ID
        private long curEyeEntityId = -1;

        // 当前从附近交互列表中选中的展开交互种类
        private EInteractionType curSelectInteractionType = EInteractionType.None;

        // 交互列表的默认排序
        private EInteractionType[] defaultInteractionSort = new EInteractionType[]
            {
                EInteractionType.Entity,
                EInteractionType.Vehicle,
                EInteractionType.CollectableEntity,
                EInteractionType.Water,
                EInteractionType.RangeInteractiveBox,
            };

        // 自动开关门等服务端回复
        private bool waitResponeFlag = false;

        // 当前操作的那个门EnityId
        private long curDoorEntityId = -1;

        // 标题去重集合
        private HashSet<string> titleSet = new HashSet<string>();

        // 用以区分交互ID
        private int INTERACTIVE_BASE_INDEX = 10000;

        // 各个列表是否能滑动
        private bool expandListCanScroll = false;
        private bool nearbyListCanScroll = false;
        private bool pickListCanScroll = false;
        private bool horseFoodListCanScroll = false;

        #endregion


        private bool skipFramePre = false;
        private bool lockFramePre = false;

        /// <summary>
        /// 恢复子建筑所需材料
        /// </summary>
        private List<ItemCostAmount> recoverChildPartCost = new ();

        private HotKeyAction wheelUpAction;
        private HotKeyAction wheelDownAction;
        private int lastNum = 0;

        /// <summary>
        /// 是否需要播放拾取列表打开时的音效
        /// </summary>
        private bool needPlayPickUpListShowSoundEffect = false;

        /// <summary>
        /// 控制器的名称
        /// </summary>
        private readonly string SHOW_INTERACTIVE = "ShowInteractive";
        private readonly string SHOW_PICKUP = "ShowPickup";
        private readonly string SHOW_NEARBY = "ShowNearby";
        private readonly string SHOW_HORSEFOOD = "ShowHorseFood";
        private readonly string SHOW_PICKUP_SWITCH = "ShowPickupSwitch";

        private bool canFeedHorse = false;      // 是否可以喂马

        public UiHudElemInteractiveList()
        {
            InteractiveIdListChecker = new InteractiveIdListChecker();
            uiInteractiveBtnCdTypeDic = new Dictionary<int, UiInteractiveBtnCdType>();

            CreatePickUiData();
        }

        protected override void OnBoardInit()
        {
            base.OnBoardInit();

            InteractiveIdListChecker.OnInit();
            InteractiveIdListChecker.EyeEntityIdChangeAction += EyeEntityIdChangeAction;
            InteractiveIdListChecker.EyeGameObjectChangeAction += EyeGameobjectChangeAction;
            InteractiveIdListChecker.InteractiveIdListChangeAction += InteractiveIdListChangeAction;
            InteractiveIdListChecker.UpdateDetectionAction += UpdateDetectionAction;

            OnBoardInit_PickList();
            OnBoardInit_Nearby();
            OnBoardInit_PickSwitch();
            OnBoardInit_Platform();
        }


        protected override void OnBoardDestroy()
        {
            base.OnBoardDestroy();

            InteractiveIdListChecker.OnDestory();
            InteractiveIdListChecker.EyeEntityIdChangeAction -= EyeEntityIdChangeAction;
            InteractiveIdListChecker.EyeGameObjectChangeAction -= EyeGameobjectChangeAction;
            InteractiveIdListChecker.InteractiveIdListChangeAction -= InteractiveIdListChangeAction;
            InteractiveIdListChecker.UpdateDetectionAction -= UpdateDetectionAction;

            OnBoardDestroy_PickList();
        }

        protected override void OnCreate(GComponent node)
        {
            base.OnCreate(node);

            stateCtrl = node.GetController("State");
            itemCountCtrl = node.GetController("itemCount");
            interactiveRoot = node.GetChild("interactiveList").asCom;
            titleText = interactiveRoot.GetChild("title").asTextField;
            titleController = interactiveRoot.GetController("titleState");

            btnReturn = interactiveRoot.GetChild("btnReturn").asButton;
            btnReturn.onClick.Set(OnBtnReturnClick);
            interactiveBtnList = interactiveRoot.GetChild("interactiveBtnList").asList;
            interactiveBtnList.itemRenderer = OnRenderInteractiveBtn;
            SetExpandListItemNum(0);

            Mc.Msg.AddListener<int>(EventDefine.PlayerStateChange, PlayerStateAction);
            Mc.Msg.AddListener<long>(EventDefine.UpdatePlantWater, RefreshInteractiveList);
            Mc.Msg.AddListener(EventDefine.UiInventoryMainUpdate, OnInventoryUpdate);
            Mc.Msg.AddListener(EventDefine.UiInventoryBeltUpdate, OnInventoryUpdate);
            Mc.Msg.AddListener<bool>(EventDefine.InterruptPickListMsg, OnInterruptPickListMsg);
            
            OnCreate_PickSwitch(node);
            OnCreate_PickList(node);
            OnCreate_NearbyList(node);
            OnCreate_HorseFoodList(node);
            base.GetStageBlockArea = GetBlockArea;
            node.onTouchBegin.Set((context) => { context.StopPropagation(); });

            node.InitKeyTips("selectKey", ActionName.Pick);
            node.InitKeyTips("scrollKey", ActionName.ListWheel);
        }
        protected override EHotKeyLayer HotKeyGroup { get { return EHotKeyLayer.Interactive; } }
        protected override ActionName HotKey { get { return ActionName.OpenPickList; } }
        protected override void OnHotKeyAction()
        {
            if (OpenPickList)
            {
                OnPickSwitchCloseClick();
            }
            else
            {
                OnPickSwitchClick();
            }
        }
        private void OnSelectHotKey()
        {
            if (stateCtrl.selectedIndex == 1)
            {
                if (interactiveBtnList.selectedIndex >= 0)
                {
                    var idList = GetExpandInteractiveList();
                    if (idList != null && idList.Count > interactiveBtnList.selectedIndex)
                    {
                        OnClickButtonByGuide(idList[interactiveBtnList.selectedIndex]);
                    }
                }
            }
            else if (stateCtrl.selectedIndex == 2)
            {
                if (pickItemList.selectedIndex >= 0)
                {
                    // 死亡盒子则打开拾取背包
                    /*for (int i = 0; i < pickDataIdList.Count; ++i)
                    {
                        var res = GetDeadPlayerItemsByCollectionId(pickDataIdList[i]);
                        if (res.Contains(pickRenderList[pickItemList.selectedIndex]))
                        {
                            OpenBag();
                            return;
                        }
                    }*/
                    if (pickRenderList != null && pickRenderList.Count > pickItemList.selectedIndex)
                        DoPickItem(pickRenderList[pickItemList.selectedIndex]);
                }
            }else if (stateCtrl.selectedIndex == 3)
            {
                if (nearbyList.selectedIndex >= 0)
                {
                    if (nearbyItemList != null && nearbyItemList.Count> nearbyList.selectedIndex)
                    {
                        OnNearbyItemClick(nearbyList.selectedIndex);
                    }
                }
            }else if (stateCtrl.selectedIndex == 4)
            {
                if (horseFoodList.selectedIndex >= 0)
                {
                    if (curHorseFoods!=null&& curHorseFoods.Count> horseFoodList.selectedIndex)
                    {
                        ClickFood(horseFoodList.selectedIndex);
                    }
                }
            }
        }
        /// <summary>
        /// list的item必须是GButton
        /// </summary>
        /// <returns></returns>
        private GList GetList()
        {
            if (stateCtrl.selectedIndex == 1)
            {
                return interactiveBtnList;
            }
            else if (stateCtrl.selectedIndex == 2)
            {
                return pickItemList;
            }
            else if (stateCtrl.selectedIndex == 3)
            {
                return nearbyList;
            }
            else if (stateCtrl.selectedIndex == 4)
            {
                return horseFoodList;
            }
            return null;
        }


        protected override void OnDestroy()
        {
            base.OnDestroy();

            Mc.Msg.RemoveListener<int>(EventDefine.PlayerStateChange, PlayerStateAction);
            Mc.Msg.RemoveListener<long>(EventDefine.UpdatePlantWater, RefreshInteractiveList);
            Mc.Msg.RemoveListener(EventDefine.UiInventoryMainUpdate, OnInventoryUpdate);
            Mc.Msg.RemoveListener(EventDefine.UiInventoryBeltUpdate, OnInventoryUpdate);
            Mc.Msg.RemoveListener<bool>(EventDefine.InterruptPickListMsg, OnInterruptPickListMsg);
        }

        public override void Show()
        {
            if (PlayHelper.IsNewbie && Mc.Mission.IsStoryMode)
            {
                return;
            }

            if (IsInEdit)
            {
                base.Show();
                return;
            }
            if (this.HideByArea)
            {
                return;
            }
            if (!Mc.Ui.IsInGame)
            {
                return;
            }

            bool canShowInteractiveIdList = CanShowInteractiveIdList();
            if (canShowInteractiveIdList || CanShowPickList())
            {
                if (!IsElemEnable)
                {
                    needPlayPickUpListShowSoundEffect = true;
                }

                base.Show();

                if (canShowInteractiveIdList)
                {
                    if (curEyeEntityId != -1)
                    {
                        SetDragEdgesEffect(curEyeEntityId);
                    }
                    else
                    {
                        SetDragEdgesEffect();
                    }
                }

                UpdateUIStyle();
            }
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            OnEnable_PickList();
            var node = TryGetNode();
            node.SetKeyTipsAction("selectKey", OnSelectHotKey);
        }
        protected override void OnDisable()
        {
            base.OnDisable();
            OnDisable_PickList();

            curSelectInteractionType = EInteractionType.None;
            isInHorseFoodList = false;
            if (pickSwitchCtrl != null)
            {
                pickSwitchCtrl.selectedIndex = 1;
            }

            var node = TryGetNode();
            node.ClearKeyTipsAction("selectKey");
            HotKeyUtils.HideSideGuide(EHotKeyGuideGroup.PickList);
            HotKeyUtils.RemoveHotKey(ref wheelUpAction);
            HotKeyUtils.RemoveHotKey(ref wheelDownAction);
            lastNum = 0;

            OnDisable_Platform();
        }

        private bool CanShowInteractiveIdList()
        {
            var hasInteractiveIdList = null != InteractiveIdListChecker
                && null != InteractiveIdListChecker.InteractiveIdSortList
                && InteractiveIdListChecker.InteractiveIdSortList.Count > 0;

            if (hasInteractiveIdList && Mc.GatherItemPickable.IsPickListLooting())
            {
                return false;
            }

            return hasInteractiveIdList;
        }

        /// <summary>
        /// 拾取列表是否需要展开显示
        /// </summary>
        /// <returns></returns>
        private bool CanShowPickList()
        {
            return needShowPickList;
        }

        // 交互列表重新分类
        private void Reclassify()
        {
            expandInteractiveIdList.Clear();
            nearbyInteractiveIdList.Clear();
            nearbyInteractiveType.Clear();

            var interactionType = InteractiveIdListChecker.InteractionTypeSet;
            // 检查一下当前选择的交互类型是否还存在
            if (curSelectInteractionType != EInteractionType.None && !interactionType.Contains(curSelectInteractionType))
            {
                curSelectInteractionType = EInteractionType.None;
            }

            // 当前选择的交互类型
            if (curSelectInteractionType != EInteractionType.None)
            {
                switch (curSelectInteractionType)
                {
                    case EInteractionType.Vehicle:
                        SelectedInteractiveList(EInteractionType.Vehicle);
                        break;
                    case EInteractionType.Water:
                        SelectedInteractiveList(EInteractionType.Water);
                        break;
                    case EInteractionType.LandMine:
                        SelectedInteractiveList(EInteractionType.LandMine);
                        break;
                    case EInteractionType.Entity:
                        DefaultInteractiveList(true);
                        break;
                    default:
                        break;
                }

                return;
            }

            DefaultInteractiveList();
        }

        private void DefaultInteractiveList(bool selected = false)
        {
            // 一键回收的处理
            if (selected)
            {
                var lst = InteractiveIdListChecker?.InteractiveIdSortList;
                if (lst != null && lst.Count > 0)
                {
                    for (int i = 0; i < lst.Count; i++)
                    {
                        if (lst[i] == EntityInteractiveIdHandle.Recovery)
                        {
                            expandInteractiveIdList.Add(lst[i]);
                            return;
                        }
                    }
                }
            }

            if (curEyeEntityId > 0)
            {
                HandleWithCrosshair();
            }
            else
            {
                HandleWithoutCrosshair();
            }
        }

        /// <summary>
        /// 交互列表的显示优先级：准星交互件 > 拾取交互件（死亡盒子>掉落物） > 范围交互件（载具>采集物>水）
        /// 基于上述规则，当准星对准交互件时，优先显示该交互件的交互列表（以及显示描边）
        /// </summary>
        private void HandleWithCrosshair()
        {
            bool isVehicle, isWater = false, isCollectable = false;
            long mountableEntityId = Mc.Vehicle?.InteractionMountableEntityId ?? 0;
            if (mountableEntityId > 0)
            {
                var entity = Mc.Entity.GetEntity(mountableEntityId);
                isVehicle = VehicleMountableUtil.CanMountable(entity?.EntityType ?? 0);
            }
            else
            {

                isVehicle = VehicleMountableUtil.CanMountable(curEyeEntityTypeId);
            }
            
            if (!isVehicle)
            {
                long entityId = Mc.GatherItemPickable?.ClosestCollectableEntityId ?? 0;
                isCollectable = (curEyeEntityId == entityId || Mc.GatherItemPickable.IsInCollectableSet(curEyeEntityId));
                if (!isCollectable)
                {
                    isWater = Mc.Water.playerInWaterState != MgrWater.EPlayerInWaterState.Ground;
                }
            }

            var lst = InteractiveIdListChecker?.InteractiveIdSortList;
            if (lst == null || lst.Count == 0) return;

            foreach (var id in lst)
            {
                bool add = true;
                bool idIsVehicle = IdIsVehicle(id);
                bool idIsWater = IdIsWater(id);
                bool idIsCollectable = IdIsCollectable(id);
                bool idIsRecovery = id == EntityInteractiveIdHandle.Recovery;
                if (!isVehicle && idIsVehicle)
                {
                    add = false;
                }

                if (!isWater && idIsWater)
                {
                    add = false;
                }

                if (!isCollectable && idIsCollectable)
                {
                    add = false;
                }

                if (idIsRecovery)
                {
                    add = false;
                }

                if (add)
                {
                    expandInteractiveIdList.Add(id);
                }
                else
                {
                    nearbyInteractiveIdList.Add(id);
                    ProcessNearbyInteractiveId(id);
                }
            }
        }

        private void HandleWithoutCrosshair()
        {
            var lst = InteractiveIdListChecker?.InteractiveIdSortList;
            if (lst == null) return;

            var typeList = InteractiveIdListChecker.InteractionTypeSet;
            if (typeList.Count == 1)
            {
                var t = EInteractionType.None;
                foreach (var i in typeList)
                {
                    t = i;
                }
                if (t == EInteractionType.RangeInteractiveBox ||
                    t == EInteractionType.LandMine ||
                    t == EInteractionType.AnimalTrap||
                    t == EInteractionType.RFC4Entity||
                    t == EInteractionType.Vehicle)
                {
                    SelectedInteractiveList(t);
                    return;
                }
            }

            foreach (var id in lst)
            {
                nearbyInteractiveIdList.Add(id);
                ProcessNearbyInteractiveId(id);
            }
        }

        private void ProcessNearbyInteractiveId(int id)
        {
            EInteractionType t = EInteractionType.None;

            if (IdIsVehicle(id)) t = EInteractionType.Vehicle;
            else if (IdIsWater(id)) t = EInteractionType.Water;
            else if (IdIsCollectable(id)) t = EInteractionType.CollectableEntity;
            else if (id == EntityInteractiveIdHandle.Recovery) t = EInteractionType.Entity;
            else if (id == EntityInteractiveIdHandle.ANIMAL_TRAP) t = EInteractionType.AnimalTrap;

            if (t != EInteractionType.None)
            {
                if (nearbyInteractiveType.ContainsKey(t))
                {
                    ++nearbyInteractiveType[t];
                }
                else
                {
                    // 万以上表示交互ID，万以下表示数量
                    int num = INTERACTIVE_BASE_INDEX * id + 1;
                    nearbyInteractiveType[t] = num;
                }
            }
        }

        private void SelectedInteractiveList(EInteractionType type)
        {
            var lst = InteractiveIdListChecker?.InteractiveIdSortList;
            if (lst == null || lst.Count == 0)
            {
                return;
            }

            foreach (var id in lst)
            {
                if (type == EInteractionType.Vehicle && IdIsVehicle(id))
                {
                    expandInteractiveIdList.Add(id);
                }
                else if (type == EInteractionType.Water && IdIsWater(id))
                {
                    expandInteractiveIdList.Add(id);
                }
                else if (type == EInteractionType.CollectableEntity && IdIsCollectable(id))
                {
                    expandInteractiveIdList.Add(id);
                }
                else if (type == EInteractionType.RangeInteractiveBox && IdIsRangeInteractive(id))
                {
                    expandInteractiveIdList.Add(id);
                }
                else if (type == EInteractionType.AnimalTrap && IdIsAnimalTrapInteractive(id))
                {
                    expandInteractiveIdList.Add(id);
                }
                else if (type == EInteractionType.LandMine && IdIsLandMine(id))
                {
                    expandInteractiveIdList.Add(id);
                }
                else if (type == EInteractionType.RFC4Entity && IdIsRFC4(id))
                {
                    expandInteractiveIdList.Add(id);
                }
                else
                {
                    nearbyInteractiveIdList.Add(id);
                }
            }

            var t = EInteractionType.Entity;
            foreach (var id in nearbyInteractiveIdList)
            {
                if (IdIsVehicle(id))
                {
                    t = EInteractionType.Vehicle;
                }
                else if (IdIsWater(id))
                {
                    t = EInteractionType.Water;
                }
                else if (IdIsCollectable(id))
                {
                    t = EInteractionType.CollectableEntity;
                }
                else if (IdIsRangeInteractive(id))
                {
                    t = EInteractionType.RangeInteractiveBox;
                }
                else
                {
                    t = EInteractionType.Entity;
                }

                if (nearbyInteractiveType.ContainsKey(t))
                {
                    ++nearbyInteractiveType[t];
                }
                else
                {
                    // 万以上表示交互ID，万以下表示数量
                    int num = INTERACTIVE_BASE_INDEX * id + 1;
                    nearbyInteractiveType[t] = num;
                }
            }

        }

        // 是否是载具ID
        private bool IdIsVehicle(int id)
        {
            return InteractiveIdListChecker.IdIsVehicle(id);
        }

        // 是否是水
        private bool IdIsWater(int id)
        {
            return InteractiveIdListChecker.IdIsWater(id);
        }

        // 是否是采集物
        private bool IdIsCollectable(int id)
        {
            return InteractiveIdListChecker.IdIsCollectable(id);
        }

        private bool IdIsRangeInteractive(int id)
        {
            return Mc.GatherItemPickable.IdIsRangeInteractive(id);
        }

        private bool IdIsAnimalTrapInteractive(int id)
        {
            return id == EntityInteractiveIdHandle.ANIMAL_TRAP;
        }

        private bool IdIsLandMine(int id)
        {
            return id == EntityInteractiveIdHandle.LandMineInteractiveId;
        }

        private bool IdIsRFC4(int id)
        {
            return id == EntityInteractiveIdHandle.RFC4InteractiveId;
        }

        // 准星交互列表
        private List<int> GetExpandInteractiveList()
        {
            return expandInteractiveIdList;
        }

        // 范围交互列表
        // 水、载具、采集物 (绿卡、蓝卡、红卡)
        private List<int> GetNearbyInteractiveIdList()
        {
            return nearbyInteractiveIdList;
        }

        private void UpdateUIStyle()
        {
            if (IsInEdit) return;
            expandListCanScroll = false;
            nearbyListCanScroll = false;
            pickListCanScroll = false;

            bool canShowPickList = CanShowPickList();
            bool canShowInteractiveIdList = CanShowInteractiveIdList();

            if (!canShowPickList && !canShowInteractiveIdList)
            {
                Hide();
                return;
            }

            oldExpandInteractiveIdList.Clear();
            oldExpandInteractiveIdList.AddRange(expandInteractiveIdList);

            if (needShowPickList && !canShowInteractiveIdList)
            {
                ClearInteractiveLists();
            }
            else
            {
                Reclassify();
            }

            bool expandListChanged = IsExpandInteractiveIdChanged();
            var expandList = GetExpandInteractiveList();
            var nearbyList = GetNearbyInteractiveIdList();

            bool needShowPickSwitch = canShowPickList;

            var page = SHOW_INTERACTIVE;
            var titleStatePage = "normal";
            var pickListTitleStatePage = "normal";

            if (ShouldShowExpandListOnly(expandList, nearbyList, canShowPickList))
            {
                ShowExpandList(expandList, expandListChanged);
                page = SHOW_INTERACTIVE;
                needShowPickSwitch = false;
            }
            else if (ShouldShowPickListOnly(expandList, nearbyList, canShowPickList))
            {
                if (OpenPickList)
                {
                    ShowPickListOnly();
                    page = SHOW_PICKUP;
                }
                else
                {
                    page = SHOW_PICKUP_SWITCH;
                }
            }
            else if (ShouldTransferNearbyToExpand(expandList, canShowPickList && OpenPickList))
            {
                TransferNearbyToExpand(expandList, expandListChanged);
                page = SHOW_INTERACTIVE;
                ShowExpandList(expandList, expandListChanged);
            }
            else if (isInHorseFoodList)
            {
                //打开喂食列表时，如果马匹因为其它原因饱了，交互列表不显示喂食按钮了，则喂食列表也不显示了
                if (InteractiveIdListChecker != null && !InteractiveIdListChecker.InteractiveIdSortList.Contains(EntityInteractiveIdHandle.HorseFeed))
                {
                    isInHorseFoodList = false;
                }
                else
                {
                    ShowHorseFoodListOnly();
                    page = SHOW_HORSEFOOD;
                }
            }
            // 交互列表和拾取列表都有内容时，需要根据拾取列表的显示状态来判断
            else if (canShowPickList && canShowInteractiveIdList)
            {
                if (OpenPickList)
                {
                    page = SHOW_PICKUP;
                    pickListTitleStatePage = "canClick";
                }
                else
                {
                    // 当前没有指向任何交互物
                    if (nearbyList.Count > 0)
                    {
                        // 如果玩家当前没有选择交互类型，且准星有交互物，显示列表。
                        if (curEyeEntityId > 0 && !needShowNearbyList && expandList.Count > 0)
                        {
                            page = SHOW_INTERACTIVE;
                            titleStatePage = "canClick";
                            ShowExpandList(expandList, expandListChanged);
                        }
                        else
                        {
                            page = SHOW_NEARBY;
                            UpdateNearbyStyle();
                        }
                    }
                    else
                    {
                        page = SHOW_INTERACTIVE;
                        needShowPickSwitch = true;
                        ShowExpandList(expandList, expandListChanged);
                    }
                }
            }
            else if (nearbyList.Count > 0)
            {
                // 如果玩家当前没有选择交互类型，且准星有交互物，显示列表。
                if (((curEyeEntityId > 0) || 
                    (curSelectInteractionType != EInteractionType.None &&
                    curSelectInteractionType != EInteractionType.Entity &&
                    expandList.Count > 0)) && 
                    !needShowNearbyList)
                {
                    page = SHOW_INTERACTIVE;
                    titleStatePage = "canClick";
                    ShowExpandList(expandList, expandListChanged);
                }
                else
                {
                    page = SHOW_NEARBY;
                    UpdateNearbyStyle();
                }
            }

            SafeUtil.SafeSetVisible(pickComRoot, page == SHOW_PICKUP);
            stateCtrl.SetSelectedPage(page);
            titleController.SetSelectedPage(titleStatePage);
            SetPickSwitchComRootVisible(needShowPickSwitch);
            pickListTitleController.SetSelectedPage(pickListTitleStatePage);

            SetPickSwitchState(!OpenPickList);

            var showPickup = page == "ShowPickup";
            // 为了支持引导，需要在设置控制器之后，再使用FGUI的方法设置一下Visible属性
            SafeUtil.SafeSetVisible(interactiveRoot, page == SHOW_INTERACTIVE);
            SafeUtil.SafeSetVisible(pickComRoot, showPickup);
            SafeUtil.SafeSetVisible(nearbyComRoot, page == SHOW_NEARBY);
            SafeUtil.SafeSetVisible(horseFoodListCom, page == SHOW_HORSEFOOD);

            if (showPickup)
            {
                Mc.GatherItemPickable.TryReportNearbyBoxes();
                HotKeyUtils.ShowSideGuide(EHotKeyGuideGroup.PickList);

                if (needPlayPickUpListShowSoundEffect)
                {
                    needPlayPickUpListShowSoundEffect = false;
                    MgrAudio.PlayAudioEventAsync("UI_Click_14", null, null);
                }
            }
            else
            {
                HotKeyUtils.HideSideGuide(EHotKeyGuideGroup.PickList);
            }

            UpdateUIStyle_Platform();
        }

        private void ClearInteractiveLists()
        {
            expandInteractiveIdList.Clear();
            nearbyInteractiveIdList.Clear();
        }

        private void ShowExpandList(List<int> expandList, bool expandListChanged)
        {
            SetExpandListItemNum(expandList.Count);
            if (expandListChanged)
            {
                TriggerGuide();
            }

            AdjustInteractiveBtnListSize(expandList);
            SafeUtil.SafeSetText(titleText, GetExpandInteractiveTitleStr(curEyeEntityId));
        }

        private void ShowPickListOnly()
        {
            RefreshBtnState();
            bool refreshList = false;
            if (!pickComRoot.visible)
            {
                refreshList = true;
            }
            int itemNum = pickRenderList.Count;
            if (refreshList || (pickItemList.numItems != itemNum))
            {
                SetItemListNum(itemNum);
            }
            pickListCanScroll = itemNum > 4;
            SetPickTitleText();
        }

        private void ShowHorseFoodListOnly()
        {
            bool refreshList = false;
            if (!horseFoodListCom.visible)
            {
                refreshList = true;
                horseFoodListCom.visible = true;
            }
            if (refreshList)
            {
                horseFoodListCom.GetTransition("appear").Play();
                RefreshHorseFoodList();
            }
        }

        private void TransferNearbyToExpand(List<int> expandList, bool expandListChanged)
        {
            expandList.AddRange(nearbyInteractiveIdList);
            nearbyInteractiveIdList.Clear();
            nearbyInteractiveType.Clear();

            /*interactiveRoot.visible = true;
            SetExpandListItemNum(expandList.Count);
            if (expandListChanged)
            {
                TriggerGuide();
            }
            interactiveBtnList.ResizeToFit();
            expandListCanScroll = expandList.Count > 4;
            string titleStr = GetTitleForNearbyType();
            SafeUtil.SafeSetText(titleText, titleStr);*/
        }

        private string GetTitleForNearbyType()
        {
            if (nearbyInteractiveType.ContainsKey(EInteractionType.CollectableEntity))
                return InteractiveIdListChecker.GetCollectableEntityNameById(-1);
            if (nearbyInteractiveType.ContainsKey(EInteractionType.Vehicle))
                return InteractiveIdListChecker.VehicleName;
            if (nearbyInteractiveType.ContainsKey(EInteractionType.Water))
                return InteractiveIdListChecker.WaterName;
            if (nearbyInteractiveType.ContainsKey(EInteractionType.Entity))
                return Mc.GatherItemPickable.GetRecoveryEntityName();
            if (nearbyInteractiveType.ContainsKey(EInteractionType.LandMine))
                return InteractiveIdListChecker.LandMineName;
            return GetExpandInteractiveTitleStr(curEyeEntityId);
        }

        private bool IsExpandInteractiveIdChanged()
        {
            if (oldExpandInteractiveIdList.Count != expandInteractiveIdList.Count)
            {
                return true;
            }

            var cnt = expandInteractiveIdList.Count;
            for (int i = 0; i < cnt; i++)
            {
                if (oldExpandInteractiveIdList[i] != expandInteractiveIdList[i])
                {
                    return true;
                }
            }
            return false;
        }

       
        private void RefreshInteractiveList(long collectionId)
        {
            var cdBtnNum = uiInteractiveBtnCdTypeDic.Count;
            ClearCdTypeDic();
            SetExpandListItemNum(GetExpandInteractiveList().Count);

            if (cdBtnNum == 0 && uiInteractiveBtnCdTypeDic.Count > 0)
            {
                // 新出现cd按钮
                OnInteractiveCdBtnChange(true);
            }

            if (cdBtnNum > 0 && uiInteractiveBtnCdTypeDic.Count == 0)
            {
                // cd按钮消失
                OnInteractiveCdBtnChange(false);
            }

            RefreshNearbyItemList();
        }


        private void OnRenderInteractiveBtn(int index, GObject obj)
        {
            var interactiveIdList = GetExpandInteractiveList();
            if (interactiveIdList == null || index >= interactiveIdList.Count || obj == null)
            {
                return;
            }

            var interactiveId = interactiveIdList[index];
            var data = Mc.Tables.TbInteraction.GetOrDefault(interactiveId);
            if (data == null)
            {
                return;
            }

            var com = obj.asCom;
            var btn = com.asButton;
            btn.UseCustomSelectEffect = true;
            var leftBtn = com.GetChild("interactiveBtn").asCom;
            var interactiveBtn = com.GetChild("interactiveCom").asButton;
            interactiveBtn.UseCustomSelectEffect = true;
            var ctrlShowLeftBtn = com.GetController("showInteractiveBtn");
            // 设置交互项大小
            com.SetSize(com.width, ITEM_HEIGHT);

            var iconLoader = interactiveBtn.GetChild("icon").asLoader;
            var iconUrlArray = data.Icon.Split(';');
            var iconPath = string.Empty;
            var nameStr = data.Name;
            if (iconUrlArray != null)
            {
                if (iconUrlArray.Length > 0)
                {
                    iconPath = iconUrlArray[0];
                }
            }
            var btnJumpToHorseFood = interactiveBtn.GetChild("BtnJumpToHorseFood").asButton;
            var textNum = interactiveBtn.GetChild("textNum").asTextField;
            btnJumpToHorseFood.visible = false;
            textNum.visible = false;
            if (interactiveId == EntityInteractiveIdHandle.GetInDriverSeat)
            {
                bool full = false;
                if (Mc.Go.GetGo(Mc.Vehicle.InteractionMountableEntityId) is IMountable mountable)
                {
                    full = mountable.IsSeatTypeFull(SeatType.Driver);
                }

                SetBtnType(interactiveBtn, full ? 1 : 0);
            }
            else if (interactiveId == EntityInteractiveIdHandle.GetInPassengerSeat)
            {
                bool full = false;
                if (Mc.Go.GetGo(Mc.Vehicle.InteractionMountableEntityId) is IMountable mountable)
                {
                    full = mountable.IsSeatTypeFull(SeatType.Passenger);
                }

                SetBtnType(interactiveBtn, full ? 1 : 0);
            }
            else if (interactiveId == EntityInteractiveIdHandle.SwitchVehicleSeat)
            {
                bool full = false;
                if (Mc.Go.GetGo(Mc.Vehicle.InteractionMountableEntityId) is IMountable mountable)
                {
                    full = mountable.GetEmptySeatCount() <= 0;
                }

                SetBtnType(interactiveBtn, full ? 1 : 0);
            }

            else if (interactiveId == EntityInteractiveIdHandle.PlantSystemWater)
            {
                PlantBoxData curPlantBox = Mc.Plant?.GetPlantBoxData(GetCurRayEntityId());
                if (curPlantBox == null)
                {
                    return;
                }

                var curWater = curPlantBox.Water;
                var maxWater = curPlantBox.plantBoxCfg.WaterLimit;

                SetBtnType(interactiveBtn, 4);
                if (!uiGObjectPool.TryGetValue(interactiveBtn, out var needObj))
                {
                    needObj = interactiveBtn.GetChild("textWater")?.asTextField;
                    if (needObj == null)
                    {
                        return;
                    }

                    uiGObjectPool.Add(interactiveBtn, needObj);
                }

                if (uiGObjectPool.TryGetValue(interactiveBtn, out needObj))
                {
                    if (needObj is GTextField textWater)
                    {
                        textWater.text = $"X {SafeUtil.SafeStringFormat(LanguageManager.GetTextConst(LanguageConst.UnitWater), $"{curWater} / {maxWater}")}";
                    }
                }
            }
            else if (interactiveId == EntityInteractiveIdHandle.OpenBatchUpgrade)
            {
                var terrId = GetTerrIdByCurRayPart();
                var terrEnt = TerritoryManagerEntity.Instance.GetTerritoryEntity(terrId);
                SetBtnType(interactiveBtn, 0);
            }
            else if (interactiveId == EntityInteractiveIdHandle.PermRepairAll)
            {
                var terrId = GetTerrIdByCurRayPart();
                var terrEnt = TerritoryManagerEntity.Instance.GetTerritoryEntity(terrId);
                if (terrEnt != null)
                {
                    if (terrEnt.BaseComp.IsBatchUpgrading())
                    {
                        //一键升级时，要置灰一键修复按钮
                        SetBtnType(interactiveBtn, 1);
                    }
                    else
                    {
                        SetBtnType(interactiveBtn, 0);
                    }
                }
            }
            else if (interactiveId == EntityInteractiveIdHandle.HorseFeed)
            {
                bool canFeed = true;
                var ownedFoods = GetAllHorseFoodsInBag();
                if (ownedFoods.Count > 1)
                {
                    btnJumpToHorseFood.visible = true;
                    btnJumpToHorseFood.SetSoundClick(Mc.Tables.TbUiAudio.UniversalButton, ctx => {
                        ctx.StopPropagation();
                        isInHorseFoodList = true;
                        ShowHorseFoodListOnly();
                    });
                }
                textNum.visible = ownedFoods.Count > 0;
                if (ownedFoods.Count > 0)
                {
                    iconPath = ownedFoods[0].ItemConfig.Icon;
                    nameStr = ZString.Format(LanguageManager.GetTextConst(LanguageConst.HorseFeed), ownedFoods[0].ItemConfig.Name);
                    textNum.text = FairyGuiUtil.WrapNum(ownedFoods[0].Amount) + "/ 1";
                }
                if (Mc.Entity.GetEntity(Mc.Vehicle.InteractionMountableEntityId) is HorseEntity horseEntity)
                {
                    canFeed = horseEntity.CanFeed(Mc.MyPlayer.MyEntityLocal);
                    var foodEmpty = ownedFoods.Count == 0;
                    canFeed = canFeed && !foodEmpty;
                    if (foodEmpty)
                    {
                        iconPath = FGUIResPathDef.GAME_HUD_BTN_QSSW_W;
                        nameStr = LanguageManager.GetTextConst(LanguageConst.HorseFoodEmpty);
                    }
                }
                btnJumpToHorseFood.touchable = canFeed;
                canFeedHorse = canFeed;
                SetBtnType(interactiveBtn, canFeed ? 5 : 1);
            }
            else if (interactiveId == EntityInteractiveIdHandle.BuildRepair || interactiveId == EntityInteractiveIdHandle.VehicleRepair)
            {
                var res = SetRepaireInteractiveBtnStyle(com, interactiveBtn, data.BgType, interactiveId == EntityInteractiveIdHandle.BuildRepair);
                if (!res)
                {
                    SetBtnType(interactiveBtn, data.BgType);
                }
            }
            else if (interactiveId == EntityInteractiveIdHandle.RecoverSinglePartInteractiveId)
            {
                var res = SetRecoverChildPartBtnStyle(com, interactiveBtn);
                if (!res)
                {
                    SetBtnType(interactiveBtn, data.BgType);
                }
            }
            else if (interactiveId ==  EntityInteractiveIdHandle.DebrisRepair)
            {
                var res = SetStoargeDebrisBtnStyle(com, interactiveBtn);
                if (!res)
                {
                    SetBtnType(interactiveBtn, data.BgType);
                }
            }
            else
            {
                if (Mc.Entity.GetEntity(GetCurRayEntityId()) is BoxEntity entity &&
                    entity.TryGetComponent(EComponentIdEnum.BoxGame, out BoxGameComponent boxGame)) // 获取当前实体
                {
                    long nextOpenTs = boxGame.NextOpenTs;
                    if (nextOpenTs > TimeStampUtil.GetNowSec())
                    {
                        long remainTime = nextOpenTs - TimeStampUtil.GetNowSec();
                        remainTime = Unity.Mathematics.math.max(0, remainTime);
                        nameStr = ZString.Format(LanguageManager.GetTextConst(LanguageConst.BoxGameLocked), remainTime);
                    }
                }

                SetBtnType(interactiveBtn, data.BgType);
            }

            if (data.BgType == 2 || data.BgType == 3)
            {
                // cd类型，需要缓存，更新进度条
                var cdBtn = Pool.Get<UiInteractiveBtnCdType>();
                cdBtn.Init(interactiveBtn);

                if (!uiInteractiveBtnCdTypeDic.ContainsKey(interactiveId))
                {
                    uiInteractiveBtnCdTypeDic.Add(interactiveId, cdBtn);
                }

                UpdateBtnCd(interactiveId);
            }
            
            var interactiveExtendData = InteractiveIdListChecker.GetInteractiveExtendData(interactiveId);
            if (interactiveExtendData != null)
            {
                var replaceIcon = interactiveExtendData.ReplaceIcon;
                var replaceName = interactiveExtendData.ReplaceName;
                if (!string.IsNullOrEmpty(replaceIcon))
                {
                    iconPath = replaceIcon;
                }
                if (!string.IsNullOrEmpty(replaceName))
                {
                    nameStr = replaceName;
                }

                SetBtnType(interactiveBtn, interactiveExtendData.Enable ? 0 : 1);
            }

            iconLoader.icon = iconPath;
            var nameText = interactiveBtn.GetChild("name").asTextField;
            nameText.text = nameStr;

#if POCO
            interactiveBtn.PocoRegister(ZString.Format("InteractiveButton{0},InteractiveButtonIndex{1}", interactiveId, index));
            textNum.PocoRegister(ZString.Format("InteractiveTitle{0},InteractiveTitleIndex{1}", interactiveId, index));
            nameText.PocoRegister(ZString.Format("InteractiveNameId{0},InteractiveNameIndex{1}", interactiveId, index));
#endif
            if (interactiveExtendData != null && interactiveExtendData.OnClick != null)
            {
                interactiveBtn.onClick.Set(() => { interactiveExtendData.OnClick?.Invoke(); });
            }
            else
            {
                interactiveBtn.onClick.Set(() => { OnClickButtonByGuide(interactiveId); });
            }
            interactiveBtn.onTouchBegin.Set(() => { OnTouchBegin(); });
            interactiveBtn.onTouchEnd.Set(() => { OnTouchEnd(); });

            // 左侧可以点击的按钮
            var showIndex = 0;
            if (interactiveExtendData != null && !string.IsNullOrEmpty(interactiveExtendData.LeftIcon))
            {
                var leftBtnIcon = leftBtn.GetChild("icon").asLoader;
                leftBtnIcon.url = interactiveExtendData.LeftIcon;
                showIndex = 1;
                if (interactiveExtendData.LeftBtnClick != null)
                {
                    leftBtn.onClick.Set(() => { interactiveExtendData.LeftBtnClick?.Invoke(); });
                }
            }
            ctrlShowLeftBtn.selectedIndex = showIndex;
        }
        
        private bool SetRecoverChildPartBtnStyle(GComponent com, GButton interactiveBtn)
        {
            var entityId = GetCurRayEntityId();
            if (entityId == 0) return false;
            
            var itemCostDic = Mc.Construction.GetVirtualPartRecoverConsume();
            if (itemCostDic == null || itemCostDic.Count == 0) return false;
            recoverChildPartCost.Clear();
            foreach (var item in itemCostDic)
            {
                recoverChildPartCost.Add(new ItemCostAmount(item.Key, item.Value));
            }
            if (recoverChildPartCost.Count > 0)
            {
                SetInteractiveBtnCostStyle(com, interactiveBtn, recoverChildPartCost);
                return true;
            }
            return false;
        }

        private bool SetStoargeDebrisBtnStyle(GComponent com, GButton interactiveBtn)
        {
            var entityId = GetCurRayEntityId();
            if (entityId == 0) return false;

            var storageDebrisEntity = Mc.Entity.GetEntity(entityId) as StorageDebrisEntity;
            if (storageDebrisEntity == null) return false;

            List<ItemCostAmount> itemsToTake = new List<ItemCostAmount>();
            var expectToConsume = storageDebrisEntity.DebrisComponent.GetRepairConsume();
            if (expectToConsume.Count > 0)
            {
                itemsToTake = new List<ItemCostAmount>();
                foreach (var item in expectToConsume)
                {
                    itemsToTake.Add(new ItemCostAmount(item.Key, item.Value));
                }
            }

            if (itemsToTake != null && itemsToTake.Count > 0)
            {
                SetInteractiveBtnCostStyle(com, interactiveBtn, itemsToTake);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 设置维修按钮的样式
        /// </summary>
        /// <param name="interactiveBtn">按钮</param>
        /// <param name="type">类型</param>
        /// <returns>是否成功</returns>
        private bool SetRepaireInteractiveBtnStyle(GComponent com, GButton interactiveBtn, int type, bool isBuilding)
        {
            var entityId = GetCurRayEntityId();
            if (entityId <= 0)
            {
                entityId = Mc.Vehicle?.InteractionMountableEntityId ?? 0;
            }
            if (entityId <= 0) return false;
            
            List<ItemCostAmount> itemsToTake = null;

            if (isBuilding)
            {
                var partEntity = Mc.Entity.GetPartEntity(entityId);
                if (partEntity == null) return false;
                // 维修要显示维修材料
                if (!ConstructionConsumeUtils.CalcRepairHP(partEntity, out var healthMissing, out var healthMissingFraction))
                {
                    return false;
                }

                if (healthMissing <= 0.0f || healthMissingFraction <= 0.0f)
                {
                    return false;
                }

                itemsToTake = ConstructionConsumeUtils.RepairCost(healthMissingFraction, partEntity.TemplateId, partEntity.Grade);
            }
            else
            {
                var vehicleEntityGo = Mc.Go.GetGo(entityId) as BaseVehicleGo;
                if (vehicleEntityGo == null) return false;
                var expectToConsume = vehicleEntityGo.GetRepairConsume(out var recoverHp, out var totalItems);
                if (expectToConsume.Count > 0)
                {
                    itemsToTake = new List<ItemCostAmount>();
                    foreach (var item in expectToConsume)
                    {
                        itemsToTake.Add(new ItemCostAmount(item.Key, item.Value));
                    }
                }
            }

            if (itemsToTake != null && itemsToTake.Count > 0)
            {
                SetInteractiveBtnCostStyle(com, interactiveBtn, itemsToTake);
                return true;
            }
            return false;
        }

        private void SetInteractiveBtnCostStyle(GComponent com, GButton interactiveBtn, List<ItemCostAmount> itemsToTake)
        {
            if (itemsToTake != null && itemsToTake.Count > 0)
            {
                SetBtnType(interactiveBtn, 6);
                var matCom = interactiveBtn.GetChild("matList").asCom;
                var list = matCom.GetChild("list").asList;
                for (int i = 0; i < 4; i++)
                {
                    var matItem = list.GetChild(ZString.Format("Item{0}", i + 1)).asCom;
                    var matIcon = matItem.GetChild("icon").asLoader;
                    var matNum = matItem.GetChild("num").asTextField;
                    var matColor = matItem.GetController("color");

                    if (i < itemsToTake.Count)
                    {
                        matIcon.visible = true;
                        matNum.visible = true;
                        var item = itemsToTake[i];
                        var itemConfig = Mc.Tables.TbItemConfig.GetOrDefault(item.templateId);
                        if (itemConfig != null)
                        {
                            matIcon.url = itemConfig.Icon;
                            int needAmount = (int)item.amount;
                            int amount = Mc.CollectionItem.GetAmount(item.templateId);
                            string numerator = FairyGuiUtil.WrapNum(amount);
                            string denominator = FairyGuiUtil.WrapNum(needAmount);
                            matColor.selectedIndex = amount < needAmount ? 1 : 0;
                            matNum.SetVar("numerator", numerator).SetVar("denominator", denominator).FlushVars();
                        }
                    }
                    else
                    {
                        matIcon.visible = false;
                        matNum.visible = false;
                    }
                }
                list.ResizeToFit(itemsToTake.Count);
                matCom.EnsureBoundsCorrect();
                // matCom.SetSize(list.width, list.height);
                // 设置交互项大小
                //interactiveBtn.SetSize(interactiveBtn.width, ITEM_HEIGHT + matCom.height);
                com.SetSize(com.width, ITEM_HEIGHT + matCom.height);
            }
        }
        
        /// <summary>
        /// 设置按钮的类型。 0：usable ，1：unusable ，2：cd，3：build ，4：water, 5: horseFeed
        /// </summary>
        /// <param name="btn"></param>
        /// <param name="type"></param>
        private void SetBtnType(GButton btn, int type)
        {
            var typeCtrl = btn.GetController("type");
            typeCtrl.SetSelectedIndex(type);
            btn.grayed = (type == 1 || type == 2);
        }


        private long GetTerrIdByCurRayPart()
        {
            var rayEntityId = GetCurRayEntityId();
            var partGo = Mc.Entity.GetPartEntity(rayEntityId);
            return partGo.TerritoryId;
            /*  if (partGo == null)
              {
                  var virtualPartGo = Mc.Construction.CurVirtualPartGo?.EntityId == rayEntityId
                      ? Mc.Construction.CurVirtualPartGo
                      : null;
                  return virtualPartGo?.PartEntity.TerritoryId ?? 0;
              }
              else
              {
              }*/
        }

        // 由于未知情况太多，暂时改回了之前检测频率
        private void WaitRespone()
        {
            timerId = Mc.TimerWheel.AddTimerSpecifiedCount(0, 15, 200, OnRespone, "WaitRespone");
        }

        private void OnRespone(long timerId, object data = null, bool delete = false)
        {
            UpdateUIStyle();
        }

        private void CancelTimer()
        {
            if (timerId != 0)
            {
                Mc.TimerWheel.CancelTimer(timerId);
            }
            timerId = 0;
        }

        /// <summary>
        /// 根据当前的交互ID展开交互界面
        /// </summary>
        /// <param name="id">id为HUD编号</param>
        private void OnClick(int id)
        {
            // 播放点击音效
            OnUIClickAudio(id);

            // 获取交互数据，如果为空直接返回
            var interactionData = Mc.Tables.TbInteraction.GetOrDefault(id);
            if (interactionData == null) return;

            // 本地数据埋点
            if (interactionData.TagGroupId.Contains(0) || interactionData.TagGroupId.Contains(2) || interactionData.TagGroupId.Contains(3))
            {
                Mc.LocalLog.AddLog(EButtonId.InteractveListBtn, EButtonGroup.ConstructionGroup5);
            }
            // 检查是否是可挂载对象的点击处理
            if (IdIsVehicle(id))
            {
                MountableOnClick(id);
                return;
            }
            
            // 检查交互背景类型，处理特殊交互逻辑
            if (interactionData.BgType == 1)
            {
                HandleUnavailableInteraction(id, interactionData.TipsId);
                return;
            }

            if (interactionData.BgType == 2 && HandleZiplineInteraction(id)) return;

            // 处理水源交互逻辑
            if (id == Mc.Water.RiverInteractiveId)
            {
                HandleWaterInteraction();
                return;
            }

            // 处理收集物交互
            if (id == EntityInteractiveIdHandle.GatherCollectableEntity)
            {
                HandleGatherItemClick();
                return;
            }

            // 处理其他特定交互类型
            if (HandleSpecialInteractions(id))
            {
                return;
            }

            // 处理范围box交互
            if (IdIsRangeInteractive(id))
            {
                HandleRangeInteractive(id);
                return;
            }

            // 处理捕兽夹任务交互
            if (IdIsAnimalTrapInteractive(id))
            {
                HandleTrapAnimalInteraction(id, InteractiveIdListChecker.CurTrapAnimalEntityId);
                return;
            }

            // 处理地雷拆除
            if (IdIsLandMine(id))
            {
                HandleLandMineInteraction();
                return;
            }

            if (IdIsRFC4(id))
            {
                HandleTrapAnimalInteraction();
                return;
            }

            // 尝试获取战利品实体
            if (TryGetLootEntity(out var lootEntity))
            {
                //极限情况下这里拿到的实体会是PlayerEntity，需要判断一下对应Entity的状态是否可以开始掠夺，做一个保底拦截
                if (lootEntity.EntityType == EntityTypeId.PlayerEntity && !CheckPlayerEntityCanStartLooting(lootEntity))
                {
                    return;
                }
                // 处理特定实体交互
                HandleEntityInteraction(id, lootEntity);
                return;
            }
        }

        private bool CheckPlayerEntityCanStartLooting(IEntity lootEntity)
        {
            bool canLoot = false;
            var playerEntity = (PlayerEntity)lootEntity;
            canLoot = playerEntity.IsWounded || playerEntity.UnAliveState == PlayerUnAliveStateEnum.Sleep || playerEntity.UnAliveState == PlayerUnAliveStateEnum.GoSleep;
            //对方是帐篷安全下线，不可以掠夺
            if (canLoot && playerEntity.SafeOfflineComp != null && playerEntity.SafeOfflineComp.CampingTentEntityId > 0)
            {
                canLoot = false;
            }
            if (canLoot)
            {
                //在安全区外，才可以掠夺
                var playerPos = new Vector3(playerEntity.PosX_Smooth, playerEntity.PosY_Smooth, playerEntity.PosZ_Smooth);
                var safeInfo = SafeAreaFuns.GetNearestSafeAreaInfo(playerPos);
                canLoot = safeInfo.isInSafe == ESafeAreaType.Outside;
            }
            return canLoot;
        }

        /// <summary>
        /// 地雷拆除
        /// </summary>
        private void HandleLandMineInteraction()
        {
            var request = GetPickUpRequest(InteractiveIdListChecker.LandMineEntityId);
            if (request != null)
            {
                Mc.MyPlayer.MyEntityServer.RemoteCallDefuse(ERpcTarget.Simulator, request);
            }
        }

        /// <summary>
        /// 拆除遥控C4
        /// </summary>
        private void HandleTrapAnimalInteraction()
        {
            var thrownEntityGo = Mc.Go.GetGo<ClientThrownEntityGo>(InteractiveIdListChecker.CurEyeEntityId);
            if (thrownEntityGo != null)
            {
                var request = GetPickUpRequest(thrownEntityGo.GetEntity.EntityId);
                if (request != null)
                {
                    Mc.MyPlayer.MyEntityServer.RemoteCallPickItem(ERpcTarget.Simulator, request);
                }
            }
        }

        /// <summary>
        /// 处理不可用的交互类型
        /// </summary>
        /// <param name="id">HUD编号</param>
        /// <param name="tipsId">提示ID</param>
        private void HandleUnavailableInteraction(int id, int tipsId)
        {
            if (id == Mc.Water.SeaInteractiveId)
            {
                // 显示实时弱提示
                Mc.MsgTips.ShowRealtimeWeakTip(tipsId);
            }
        }

        /// <summary>
        /// 处理滑索交互
        /// </summary>
        /// <param name="id">HUD编号</param>
        /// <returns>是否处理成功</returns>
        private bool HandleZiplineInteraction(int id)
        {
            if (id == EntityInteractiveIdHandle.ZiplineInteractiveId && !Mc.MyPlayer.MyEntityLocal.IsOnMount)
            {
                var interactionGo = Mc.Go.GetGo(InteractiveIdListChecker.CurEyeEntityId) as InteractionGo;
                if (interactionGo == null) return true;

                var costTime = (Mc.Time.ServerWorldTime - interactionGo.InterEntity.LastInterStartTime) / 1000.0f;
                if (costTime < interactionGo.InteractiveCd)
                {
                    Mc.MsgTips.ShowRealtimeWeakTip(Mc.Tables.TbInteraction.GetOrDefault(id).FailTipsId);
                }
                else
                {
                    Mc.MsgTips.ShowRealtimeWeakTip(Mc.Tables.TbInteraction.GetOrDefault(id).TipsId);
                    var zipId = TerrainMeta.GetZiplineId(interactionGo.InterEntity.MonumentId, interactionGo.InteractType);
                    if (zipId == -1)
                    {
                        Log.ErrorFormat("can't find zipline MonumentId{0},InteractiveTypeId{1}", interactionGo.InterEntity.MonumentId, interactionGo.InteractType);
                    }
                    Mc.MyPlayer.MyEntityServer.RemoteCallWantsMountZipline(ERpcTarget.Simulator,
                        TerrainMeta.GetZiplineId(interactionGo.InterEntity.MonumentId, interactionGo.InteractType),
                        interactionGo.EntityId);
                    SetElemVisible(false); // 隐藏交互列表
                }

                return true;
            }

            return false;
        }

        private SimulatorPickUpRequest GetPickUpRequest(long entityId)
        {
            if (entityId <= 0)
            {
                return null;
            }

            var entity = EntityManager.Instance.GetEntity(entityId);
            if (entity == null)
            {
                return null;
            }
            var rootNode = entity.GetComponent<RootNodeComponent>(EComponentIdEnum.RootNodeComponent);
            var sceneItemDirectoryNode = rootNode.GetNodeById(entityId) as PickableDirectoryNode;
            if (sceneItemDirectoryNode == null)
            {
                return null;
            }
            var itemIds = sceneItemDirectoryNode.GetItemIds();
            long itemId = 0;
            foreach (var itemIdItem in itemIds)
            {
                itemId = itemIdItem;
            }
            SimulatorPickUpRequest request = new SimulatorPickUpRequest()
            {
                EntityId = entityId,
                ItemNodeId = itemId,
                TargetPath = MgrCollectionItem.AnywhereInPlayerInventory,
                WithConfirm = false,
            };
            return request;
        }

        /// <summary>
        /// 处理水源交互
        /// </summary>
        private void HandleWaterInteraction()
        {
            if (Time.time - Mc.Water.lastDrinkTime > 1f)
            {
                Mc.MyPlayer.MyEntityServer.RemoteCallDrink(ERpcTarget.Simulator, Mc.Water.seeRiverPos.x, Mc.Water.seeRiverPos.y, Mc.Water.seeRiverPos.z);
                Mc.MsgTips.ShowRealtimeWeakTip(20000);
                Mc.Water.lastDrinkTime = Time.time;
                Mc.Audio.Add2DAudioByConfig("Svl_Drink");
            }
        }

        /// <summary>
        /// 处理收集物交互点击
        /// </summary>
        private void HandleGatherItemClick()
        {
            OnGatherItemClick();
        }

        private void HandleRangeInteractive(int id)
        {
            var rangeInteractiveBoxList = Mc.GatherItemPickable.GetRangeInteractiveBoxList();
            if (rangeInteractiveBoxList == null || rangeInteractiveBoxList.Count == 0)
            {
                return;
            }
            foreach (var boxEntity in rangeInteractiveBoxList)
            {
                var config = Mc.Tables.TbTreasureBox.GetOrDefault(boxEntity.TemplateId);
                if (config != null && config.InteractiveId.Count > 0 && config.InteractiveId.Contains(id))
                {
                    HandleEntityInteraction(id, boxEntity);
                    return;
                }
            }
        }

        /// <summary>
        /// 处理特定交互类型
        /// </summary>
        /// <param name="id">HUD编号</param>
        /// <returns>是否结束</returns>
        private bool HandleSpecialInteractions(int id)
        {
            if (id == EntityInteractiveIdHandle.CureOther)
            {
                Mc.Control.ExecuteActionAtName(ActionName.Fire2);
                return true;
            }
            else if (id == EntityInteractiveIdHandle.ManagingPrivileges || id == EntityInteractiveIdHandle.BuildRepair)
            {
                Mc.Audio.Add2DAudioByConfig(Mc.Tables.TbUiAudio.UniversalButton);
            }
            else if (id == EntityInteractiveIdHandle.RecoverSinglePartInteractiveId)
            {
                if (Mc.Construction.IsInAttackedState)
                {
                    Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.CantRecoverChildPart);
                }
                else
                {
                    Mc.Msg.FireMsg(EventDefine.RecoverVirtualPart);
                }
                return true;
            }
            else if (id == EntityInteractiveIdHandle.Recovery)
            {
                Mc.GatherItemPickable.Recovery();
                return true;
            }
            return false;
        }

        /// <summary>
        /// 处理实体交互
        /// </summary>
        /// <param name="id">HUD编号</param>
        /// <param name="lootEntity">交互实体</param>
        private void HandleEntityInteraction(int id, IEntity lootEntity)
        {
            var interactionData = Mc.Tables.TbInteraction.GetOrDefault(id);

            switch (lootEntity.EntityType)
            {
                case EntityTypeId.IOEntity:
                    if (HandleIOEntityInteraction(id, lootEntity))
                    {
                        return;
                    }
                    break;
                case EntityTypeId.ElevatorEntity:
                    HandleElevatorInteraction(id, lootEntity);
                    break;
                case EntityTypeId.KatyushaEntity:
                    HandleKatyushaInteraction(id, lootEntity);
                    return;
                case EntityTypeId.CaveLiftEntity:
                    HandleCaveLiftInteraction(id, lootEntity);
                    return;
                case EntityTypeId.NPCEntity:
                    Mc.MyPlayer.MyEntityServer.ShopComponent.RemoteCallStartShopping(ERpcTarget.World, lootEntity.EntityId);
                    return;
                case EntityTypeId.ShopEntity:
                    Mc.MyPlayer.MyEntityServer.ShopComponent.RemoteCallStartShopping(ERpcTarget.World, lootEntity.EntityId);
                    return;
                case EntityTypeId.DigEntity:
                    if (HandleDigEntityInteraction(id, lootEntity)) return;
                    break;
                case EntityTypeId.PartEntity:
                    if (HandlePartEntityInteraction(id, lootEntity)) return;
                    break;
                case EntityTypeId.BoxEntity:
                case EntityTypeId.AirdropEntity:
                    if (HandleBoxEntityInteraction(id, lootEntity)) return;
                    break;
                case EntityTypeId.InteractionEntity:
                    if (HandleInteractionEntityInteraction(id, lootEntity)) return;
                    break;
                case EntityTypeId.TrainCarEntity:
                    if (HandleTrainCarEntityInteraction(id, lootEntity)) return;
                    break;
                case EntityTypeId.StorageDebrisEntity:
                    if (HandleStorageDebrisEntityInteraction(id, lootEntity)) return;
                    break;
                default:
                    break;
            }
            OpenInteractView(interactionData, lootEntity);

            lastBoxId = id;
        }

        /// <summary>
        /// 处理IO实体的交互逻辑
        /// </summary>
        /// <param name="id">HUD编号</param>
        /// <param name="lootEntity">交互实体</param>
        /// <returns>是否结束</returns>
        private bool HandleIOEntityInteraction(int id, IEntity lootEntity)
        {
            if (id == EntityInteractiveIdHandle.TRAIN_UNLOAD)
            {
                return false;
            }
            var ioEntity = (IOEntity)lootEntity;

            var consume = ioEntity.GetComponent<ConsumeItemComponent>(EComponentIdEnum.ConsumedItem);
            if (consume != null)    //消耗道具
            {
                var itemnum = Mc.CollectionItem.GetAmount(consume.ConsumeId);
                if (itemnum == 0 && consume.Times < 1)   //不存在这个道具
                {
                    Mc.MsgTips.ShowRealtimeWeakTip(23174);
                }
                else
                {
                    //发RPC给Word，开门
                    Log.Info("发RPC给World,开门并消耗耐久");
                    consume.RemoteCallTryInteract(ERpcTarget.World);
                }
                return true;
            }
            else
            {
                var ioEntityConfig = Mc.Tables.TbIOInteractive.GetOrDefault(ioEntity.TemplateId);
                if (ioEntityConfig != null)
                {
                    // 处理刷卡逻辑
                    if (HandleCardSwipe(id, ioEntityConfig, ioEntity)) return true;
                    // 处理实体状态
                    if (HandleEntityState(id, ioEntityConfig, ioEntity)) return true;

                    // 触发交互
                    Mc.MyPlayer.MyEntityServer.RemoteCallOnTriggerInteract(ERpcTarget.Simulator, lootEntity.EntityId);

                    // 处理门交互
                    if (ioEntityConfig.IOType == IOType.ElectricDoor || ioEntityConfig.IOType == IOType.InteractiveDoor)
                    {
                        // InteractionUtil.OpenDoor();
                        // InteractionUtil.TryOpenDoor(ioEntity.EntityId);
                        InteractionUtil.TryInteract(PlayerInteractiveId.OpenDoor,ioEntity.EntityId);
                    }
                    var interactiveConfig = Mc.Tables.TbInteraction.GetOrDefault(id);
                    if (interactiveConfig != null && string.IsNullOrEmpty(interactiveConfig.UiName))
                    {
                        return true;
                    }
                }
                return false;
            }
        }

        /// <summary>
        /// 处理刷卡逻辑
        /// </summary>
        /// <returns>是否结束</returns>
        private bool HandleCardSwipe(int id, IOInteractive ioEntityConfig, IOEntity ioEntity)
        {
            long itemId = 0;
            if (ioEntityConfig.InteractiveItemID is { Length: > 0 }) itemId = ioEntityConfig.InteractiveItemID[0];

            if (itemId != 0)
            {
                var itemnum = Mc.CollectionItem.GetAmount(itemId);
                if (itemnum == 0)
                {
                    Mc.MsgTips.ShowRealtimeWeakTip(ioEntityConfig.InteractFailMessage);
                    return true;
                }

                if (ioEntityConfig.IOType == IOType.GreenCardReader ||
                    ioEntityConfig.IOType == IOType.RedCardReader ||
                    ioEntityConfig.IOType == IOType.BlueCardReader)
                {
                    // Mc.MyPlayer.SwipeCard((int)itemId);
                    // InteractionUtil.TrySwipeCard(ioEntity.EntityId);
                    InteractionUtil.TryInteract(PlayerInteractiveId.SwipeCard,ioEntity.EntityId);
                    var ioGo = Mc.Go.GetGo(ioEntity.EntityId) as IOGo;
                    var ioPos = new Vector3(ioEntity.PosX, ioEntity.PosY + 1.3f, ioEntity.PosZ);
                    OnCardSwiped?.Invoke(ioGo, ioPos); // 卡片滑动事件
                }
            }
            return false;
        }

        /// <summary>
        /// 处理实体状态
        /// </summary>
        /// <returns>是否结束</returns>
        private bool HandleEntityState(int id, IOInteractive ioEntityConfig, IOEntity ioEntity)
        {
            var stateCfg = Mc.Tables.TbIOInteractiveState.GetOrDefault(ioEntity.StateId);
            if (stateCfg != null)
            {
                if (stateCfg.Activation == (int)MgrIOEntity.EIOActivationStatus.Activated) //已经处于激活状态
                {
                    var tipsConfig = Mc.Tables.TbCommonTip.GetOrDefault(ioEntityConfig.InteractFailMessage3);
                    if (tipsConfig != null)
                    {
                        Mc.MsgTips.ShowRealtimeWeakTip(ioEntityConfig.InteractFailMessage3);
                        return true;
                    }
                }
                else if (stateCfg.Activation != (int)MgrIOEntity.EIOActivationStatus.CanActivate) //处于不可激活的状态
                {

                    var tipsConfig = Mc.Tables.TbCommonTip.GetOrDefault(ioEntityConfig.InteractFailMessage2);
                    if (tipsConfig != null)
                    {
                        Mc.MsgTips.ShowRealtimeWeakTip(ioEntityConfig.InteractFailMessage2);
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 处理电梯实体的交互逻辑
        /// </summary>
        /// <param name="id">HUD编号</param>
        /// <param name="lootEntity">交互实体</param>
        private void HandleElevatorInteraction(int id, IEntity lootEntity)
        {
            var elevatorEntity = (ElevatorEntity)lootEntity;
            if (Mc.Go.GetGo(elevatorEntity.EntityId) is ElevatorGo elevatorGo)
            {
                elevatorGo.OnUiBtnClicked(id);
            }

            if (Mc.Go.GetGo(elevatorEntity.EntityId) is ClientConstructionLiftGo clientConstructionLiftGo)
            {
                clientConstructionLiftGo.OnUiBtnClicked(id);
            }
        }

        /// <summary>
        /// 处理Katyusha(卡秋莎火箭炮)实体的交互逻辑
        /// </summary>
        /// <param name="id">HUD编号</param>
        /// <param name="lootEntity">交互实体</param>
        private void HandleKatyushaInteraction(int id, IEntity lootEntity)
        {
            var katyushaEntity = (KatyushaEntity)lootEntity;
            if (Mc.Go.GetGo(katyushaEntity.EntityId) is KatyushaGo katyushaGo)
            {
                katyushaGo.OnUiBtnClicked(id);
            }
        }
        /// <summary>
        /// 处理矿洞电梯实体的交互逻辑
        /// </summary>
        /// <param name="id">HUD编号</param>
        /// <param name="lootEntity">交互实体</param>
        private void HandleCaveLiftInteraction(int id, IEntity lootEntity)
        {
            var caveLiftEntity = (CaveLiftEntity)lootEntity;
            if (Mc.Go.GetGo(caveLiftEntity.EntityId) is CaveLiftGo caveLiftGo)
            {
                caveLiftGo.OnUiBtnClicked(id);
            }
        }

        /// <summary>
        /// 处理挖掘实体的交互逻辑
        /// </summary>
        /// <param name="id">HUD编号</param>
        /// <param name="lootEntity">交互实体</param>
        /// <returns>是否结束</returns>
        private bool HandleDigEntityInteraction(int id, IEntity lootEntity)
        {
            var digEntity = (DigEntity)lootEntity;
            if (Mc.Go.GetGo(digEntity.EntityId) is DigGo digGo)
            {
                digGo.OnUiBtnClicked(id);
            }

            if (id == McCommon.Tables.TbDigProperty.GetOrDefault(digEntity.TemplateId).EngineInteractiveMethod[0]
                || id == McCommon.Tables.TbDigProperty.GetOrDefault(digEntity.TemplateId).EngineInteractiveMethod[1])
            {
                // 点击发动机不打开界面
                return true;
            }
            return false;
        }

        /// <summary>
        /// 处理交互实体的交互逻辑：埋枪点、发射器、破译器、滑索交互
        /// </summary>
        /// <param name="id">HUD编号</param>
        /// <param name="lootEntity">交互实体</param>
        /// <returns>是否结束</returns>
        private bool HandleInteractionEntityInteraction(int id, IEntity lootEntity)
        {
            var interactionEntity = (InteractionEntity)lootEntity;
            if (Mc.Go.GetGo(interactionEntity.EntityId) is InteractionGo interactionGo)
            {
                interactionGo.OnUiBtnClicked(id);
            }
            return false;
        }
        
        private bool HandleTrainCarEntityInteraction(int id, IEntity lootEntity)
        {
            var trainCarEntity = (TrainCarEntity)lootEntity;
            if (Mc.Go.GetGo(trainCarEntity.EntityId) is TrainCarGo trainCarGo)
            {
                trainCarGo.OnUiBtnClicked(id);
            }
            return false;
        }

        private void HandleTrapAnimalInteraction(int id, long entityId)
        {
            var monsterEntity = Mc.Entity.GetMonsterEntity(entityId);
            if (monsterEntity is not { Type: (int)MonsterCategory.Animal })
            {
                return;
            }

            var player = Mc.MyPlayer.MyEntityServer;
            if (player != null)
            {
                player.TaskComponent.RemoteCallSummonPoiTaskTrap(ERpcTarget.World);
            }
        }

        /// <summary>
        /// 残骸交互
        /// </summary>
        /// <param name="id"></param>
        /// <param name="lootEntity"></param>
        /// <returns></returns>
        private bool HandleStorageDebrisEntityInteraction(int id, IEntity lootEntity)
        {
            var storageDebrisEntity = lootEntity as StorageDebrisEntity;
            if (storageDebrisEntity == null)
            {
                return true;
            }
            storageDebrisEntity.DebrisComponent?.OnUiBtnClicked(id);
            return true;
        }

        /// <summary>
        /// 处理部件实体的交互逻辑
        /// </summary>
        /// <param name="id">HUD编号</param>
        /// <param name="lootEntity">交互实体</param>
        /// <returns>是否结束</returns>
        private bool HandlePartEntityInteraction(int id, IEntity lootEntity)
        {
            var partEntity = (PartEntity)lootEntity;
            var interactiveConfig1 = Mc.Tables.TbInteraction.GetOrDefault(id);

            bool permissionCheckRst = false;
            if (partEntity.InteractiveComponent != null)
            {
                partEntity.InteractiveComponent.OnUiBtnClicked(id, out permissionCheckRst);
            }

            // 取出剩余情报
            if (permissionCheckRst && id == EntityInteractiveIdHandle.FETCH_INTELLIGENCE)
            {
                var collectionID = curEyeEntityId;
                var config = Mc.Tables.TbInteraction.GetOrDefault(id);

                Mc.LootCollection.OnStartLootingAck = res =>
                {
                    if (!res) return;
                    lastBoxId = id;
                    curLootEntityId = lootEntity.EntityId;
                    OpenPickList = true;
                    Mc.GatherItemPickable.StartPickListLooting(collectionID);
                    Mc.GatherItemPickable.LootingName = config?.Name;

                    Mc.GatherItemPickable.FilterProcess = container =>
                    {
                        var id = container.Impl.ConfigId;
                        if (id == ContainerConst.ReputationConverterInput ||
                        id == ContainerConst.ReputationConverterOutput)
                        {
                            return true;
                        }
                        return false;
                    };
                };

                Mc.MyPlayer.MyEntityServer.RemoteCallStartLooting(ERpcTarget.Simulator, Mc.Construction.ViewRayPos, Mc.UserCmd.NowCmd.Yaw, Mc.UserCmd.NowCmd.Pitch, collectionID);
                return true;
            }

            // 取出剩余保险柜遗留
            if (permissionCheckRst && id == EntityInteractiveIdHandle.GETLAST_SAFETYBOX_TEMPORARSTORAGE_ITEM)
            {
                var collectionID = curEyeEntityId;
                var config = Mc.Tables.TbInteraction.GetOrDefault(id);

                Mc.LootCollection.OnStartLootingAck = res =>
                {
                    if (!res) return;
                    lastBoxId = id;
                    curLootEntityId = lootEntity.EntityId;
                    OpenPickList = true;
                    Mc.GatherItemPickable.StartPickListLooting(collectionID);
                    Mc.GatherItemPickable.LootingName = config?.Name;

                    Mc.GatherItemPickable.FilterProcess = container =>
                    {
                        var id = container.Impl.ConfigId;
                        if (id == ContainerConst.SAFETY_BOX_TEMPORAR_STORAGE)
                        {
                            return true;
                        }
                        return false;
                    };
                };

                Mc.MyPlayer.MyEntityServer.RemoteCallStartLooting(ERpcTarget.Simulator, Mc.Construction.ViewRayPos, Mc.UserCmd.NowCmd.Yaw, Mc.UserCmd.NowCmd.Pitch, collectionID);
                return true;
            }

            // 当交互Item中的UiName为空，或点击的权限检测未通过，不会打开绑定的UiName界面；否则，执行下去打开UiName界面
            if (string.IsNullOrEmpty(interactiveConfig1.UiName) || !permissionCheckRst)
            {
                return true;
            }
            return false;
        }

        /// <summary>
        /// 处理箱子实体的交互逻辑
        /// </summary>
        /// <param name="id">HUD编号</param>
        /// <param name="lootEntity">交互实体</param>
        /// <returns>密码箱&&倒计时返回True,否则返回false。true代表没有成功，false代表成功打开</returns>
        private bool HandleBoxEntityInteraction(int id, IEntity lootEntity)
        {
            var boxEntity = (BoxEntity)lootEntity;
            var consume = boxEntity.GetComponent<ConsumeItemComponent>(EComponentIdEnum.ConsumedItem);
            if (consume != null)
            {
                var itemnum = Mc.CollectionItem.GetAmount(consume.ConsumeId);
                if (itemnum == 0 && consume.Times < 1)   //不存在这个道具
                {
                    Mc.MsgTips.ShowRealtimeWeakTip(23174);
                }
                else  //存在这个道具或者不需要消耗道具
                {
                    //发RPC给Word，开箱子
                    Log.Info("发RPC给World,开箱子并消耗耐久");
                    consume.RemoteCallTryInteract(ERpcTarget.World);
                }
                return true;
            }
            else
            {
                var config = Mc.Tables.TbTreasureBox.GetOrDefault(boxEntity.TemplateId);
                // 判断是否走宝箱优化流程（即把里面的物品放到交互列表里面显示）
                var isHudDisplay = config.HudDisplay;
                if (isHudDisplay)
                {
                    var collectionID = lootEntity.EntityId;

                    Mc.LootCollection.OnStartLootingAck = res =>
                    {
                        if (!res) return;
                        lastBoxId = id;
                        curLootEntityId = lootEntity.EntityId;
                        OpenPickList = true;
                        Mc.GatherItemPickable.StartPickListLooting(collectionID);
                        Mc.GatherItemPickable.LootingName = config.Name;
                    };
                    Mc.LootCollection.OnStopLootingAck = () =>
                    {
                        if (curLootEntityId == -1) return;
                        PlayStopLootingSound(curLootEntityId);
                    };
                    Mc.MyPlayer.MyEntityServer.RemoteCallStartLooting(ERpcTarget.Simulator, Mc.Construction.ViewRayPos, Mc.UserCmd.NowCmd.Yaw, Mc.UserCmd.NowCmd.Pitch, collectionID);
                }

                var audioConfig = Mc.Tables.TbBoxAudio.GetOrDefault(config.BoxType);
                if (audioConfig != null)
                    Mc.Audio.Add2DAudio(audioConfig.SoundEvent[0]);
                if (config.Type == BoxType.Password)
                {
                    if (boxEntity.BoxStateID == 1) //倒计时阶段
                    {
                        Mc.MsgTips.ShowRealtimeWeakTip(22029);
                        return true;
                    }
                }

                if (isHudDisplay)
                {
                    return true;
                }

                return false;
            }
        }


        public void OnClickButtonByGuide(int id)
        {
            OnClick(id);
            OnClickButtonFirst();
        }

        public void OnClickButtonByTriggerBtn(int id)
        {
            OnClick(id);
        }

        private void OnGatherItemClick()
        {
            var entityId = Mc.GatherItemPickable?.ClosestCollectableEntityId ?? 0;
            if (Mc.GatherItemPickable.IsInCollectableSet(curEyeEntityId))
            {
                entityId = curEyeEntityId;
            }

            if (entityId <= 0)
            {
                return;
            }

            var entity = EntityManager.Instance.GetEntity(entityId);
            if (entity == null) return;
            var rootNode = entity.GetComponent<RootNodeComponent>(EComponentIdEnum.RootNodeComponent);
            if (rootNode == null)
            {
                Log.ErrorFormat("[OnGatherItemClick] has no RootNodeComponent {0}", entityId);
                return;
            }
            var sceneItemDirectoryNode = rootNode.GetNodeById(entityId) as PickableDirectoryNode;
            if (sceneItemDirectoryNode == null)
            {
                Log.ErrorFormat("[OnGatherItemClick] has no PickableDirectoryNode {0}", entityId);
                return;
            }

            var itemIds = sceneItemDirectoryNode.GetItemIds();
            foreach (var itemId in itemIds)
            {
                var itemInfo = rootNode.GetNodeById(itemId) as BaseItemNode;
                if (itemInfo == null)
                {
                    Log.ErrorFormat("[OnGatherItemClick] has no BaseItemNode {0}", itemId);
                    return;
                }

                Mc.GatherItemPickable.PickItem(itemInfo, itemId, entityId);

                if (Mc.GatherItemPickable.GetIsInventoryFull())
                {
                    break;
                }
            }
        }

        private void OnTouchBegin()
        {
            TryUnlockUpdate();
        }

        private void OnTouchEnd()
        {
            TryResumeLockUpdate();
        }

        private void TryUnlockUpdate()
        {
            var container = interactiveRoot?.container?.parent;
            if (container != null)
            {
                skipFramePre = container.SkipFrameUpdate;
                lockFramePre = container.IsLocked;
                if (skipFramePre)
                {
                    container.SkipFrameUpdate = false;
                }

                if (lockFramePre)
                {
                    container.UnlockUpdate();
                }
            }
        }

        private void TryResumeLockUpdate()
        {
            var container = interactiveRoot?.container?.parent;
            if (container != null)
            {
                if (skipFramePre)
                {
                    container.SkipFrameUpdate = true;
                }

                if (lockFramePre)
                {
                    container.RelockUpdateAfterFrame();
                }
            }
        }

        /// <summary>
        /// 通用交互的按钮点击音效,走通用交互表的ClickAudio
        /// </summary>
        /// <param name="id"></param>
        private void OnUIClickAudio(int id)
        {
            var data = Mc.Tables.TbInteraction.GetOrDefault(id);
            if (null == data || string.IsNullOrEmpty(data.ClickAudio))
            {
                return;
            }

            Mc.Audio.PlayAudioEvent(null, data.ClickAudio);
        }


        public static bool TryGetLootEntity(out IEntity lootEntity, long entityId = -1)
        {
            entityId = entityId == -1 ? InteractiveIdListChecker.CurEyeEntityId : entityId;
            var entity = Mc.Entity.GetEntity(entityId);
            lootEntity = entity;
            if (entity == null) return false;
            if (entity is not VehicleModuleCustom vmc) return true;
            var modularCar = Mc.Entity.GetEntity(vmc.ModularCarId);
            lootEntity = modularCar;
            return modularCar != null;
        }


        private void SendWantsPush(PlayerEntity playerEntity, long mountableId)
        {
            if (!playerEntity.CanOperateMount)
            {
                return;
            }

            Mc.MyPlayer.MyEntityServer.RemoteCallWantsPush(ERpcTarget.Simulator, mountableId);
        }

        private void SendHorseFollow(PlayerEntity playerEntity, long mountableId, bool followOrCancel)
        {
            if (!playerEntity.CanOperateMount)
            {
                return;
            }

            Mc.MyPlayer.MyEntityServer.RemoteCallHorseFollow(ERpcTarget.Simulator, mountableId, followOrCancel);
        }

        private void FeedHorseBtnClick(long mountableId)
        {
            var ownedFoods = GetAllHorseFoodsInBag();
            if (ownedFoods.Count == 0)
            {
                Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.HorseFoodEmpty);
            }
            else
            {
                SendHorseFeed(Mc.MyPlayer.MyEntityLocal, mountableId, ownedFoods[0].BizId);
            }
        }

        private void SendHorseFeed(PlayerEntity playerEntity, long mountableId, long foodTemplateId)
        {
            var horseEntity = Mc.Entity.GetEntity(mountableId) as HorseEntity;
            if (horseEntity == null)
            {
                return;
            }

            if (horseEntity.CanFeed(playerEntity) == false)
            {
                if (horseEntity.Seat1Id > 0 || horseEntity.Seat2Id > 0)
                {
                    Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.CannotFeedWhenMounted);
                }
                else if (horseEntity.InEat)
                {
                    Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.CannotFeedWhenEatting);
                }
                return;
            }
            Mc.MyPlayer.MyEntityServer.RemoteCallHorseFeed(ERpcTarget.Simulator, mountableId, foodTemplateId);
        }

        /// <summary>
        /// 点击载具的交互选项
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        private void MountableOnClick(int id)
        {
            var mountableEntityId = Mc.Vehicle.InteractionMountableEntityId;
            if (mountableEntityId <= 0)
            {
                mountableEntityId = Mc.Vehicle.RaycastMountableEntityId;
            }

            var go = Mc.Go.GetGo(mountableEntityId);
            if (go is not IMountable mountable)
            {
                return;
            }
            var entity = Mc.Entity.GetEntity(mountableEntityId) as IBaseMountableEntity;
            if (VehicleMountableUtil.IsForbidOnModularCar(entity, id))
            {
                return;
            }

            switch (VehicleMountableUtil.GetPermission(entity))
            {
                case VehiclePermission.None:
                    return;
                case VehiclePermission.InteractiveList:
                    Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.SecondsToHaveAuthority, entity.RemainLimitedAuthorityTs.ToString());
                    return;
            }
            
            switch (id)
            {
                case EntityInteractiveIdHandle.OpenVehicleLight:
                    if (go is BaseVehicleGo vehicleGo)
                    {
                        var isOpen = !vehicleGo.VehicleEntity.LightsAreOn;
                        Mc.MyPlayer.MyEntityServer.RemoteCallOpenVehicleLight(ERpcTarget.Simulator, isOpen);
                    }

                    break;
                // case EntityInteractiveIdHandle.VehicleLock:
                //     Mc.Vehicle.TryLockVehicle(go.EntityId);
                //     return true;
                case EntityInteractiveIdHandle.VehicleHasLocked:
                    Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.NotInSameTeam);
                    return;
                /* 马匹跟随*/
                case EntityInteractiveIdHandle.HorseFollow:
                    SendHorseFollow(Mc.MyPlayer.MyEntityLocal, mountableEntityId, true);
                    return;
                case EntityInteractiveIdHandle.CancelHorseFollow:
                    SendHorseFollow(Mc.MyPlayer.MyEntityLocal, mountableEntityId, false);
                    return;
                case EntityInteractiveIdHandle.HorseFeed:
                    FeedHorseBtnClick(mountableEntityId);
                    return;
                /* 推车*/
                case EntityInteractiveIdHandle.WantsPushVehicle:
                case EntityInteractiveIdHandle.WantsFlipVehicle:
                    SendWantsPush(Mc.MyPlayer.MyEntityLocal, mountableEntityId);
                    return;
                case EntityInteractiveIdHandle.ModularCarEngine:
                    if (TryGetLootEntity(out var lootEntity, mountableEntityId))
                    {
                        OpenLootingOtherSide(lootEntity, (int)EUiHalfOthersideType.ModularCarEngine);
                    }

                    return;
                /* 上驾驶位*/
                case EntityInteractiveIdHandle.GetInDriverSeat:
                    if (entity is HorseEntity)
                    {
                        Mc.Audio.AddAudio("Mon_Horse_Mount", new Vector3(entity.PosX, entity.PosY, entity.PosZ));
                    }
                    SendWantsMount(Mc.MyPlayer.MyEntityLocal, SeatType.Driver, mountable);
                    /*
                    if (entity is HorseEntity { SpawnType: SpawnTypeDefine.SpawnShop } horseEntity)
                    {
                        if (horseEntity.BelongEntityId == Mc.MyPlayer.MyEntityId || horseEntity.BelongEntityId == -1)
                        {
                            Mc.Audio.AddAudio("Mon_Horse_Mount", new Vector3(horseEntity.PosX, horseEntity.PosY, horseEntity.PosZ));
                            SendWantsMount(Mc.MyPlayer.MyEntityLocal, SeatType.Driver, mountable);
                        }
                        else if (horseEntity.BelongEntityId == 0)
                        {
                            Mc.MsgTips.ShowRealtimeWeakTip(1200);
                        }
                        else
                        {
                            Mc.MsgTips.ShowRealtimeWeakTip(1210);
                        }
                    }
                    else if (entity is VehicleEntity { SpawnType: SpawnTypeDefine.SpawnShop } vvehicleEntity) //停机坪的飞机
                    {
                        if (vvehicleEntity.BelongEntityId == Mc.MyPlayer.MyEntityId
                            || vvehicleEntity.BelongEntityId == -1
                            || Mc.TeamClient.IsTeamMateViaEntityId(vvehicleEntity.BelongEntityId))
                        {
                            SendWantsMount(Mc.MyPlayer.MyEntityLocal, SeatType.Driver, mountable);
                        }
                        else
                        {
                            Mc.MsgTips.ShowRealtimeWeakTip(1202);
                        }
                    }
                    else
                    {
                        if (entity is HorseEntity)
                        {
                            Mc.Audio.AddAudio("Mon_Horse_Mount", new Vector3(entity.PosX, entity.PosY, entity.PosZ));
                        }

                        SendWantsMount(Mc.MyPlayer.MyEntityLocal, SeatType.Driver, mountable);
                    }*/

                    return;
                /*修理*/
                case EntityInteractiveIdHandle.VehicleRepair:
                    if (entity is { EntityType: EntityTypeId.TrainCarEntity })
                    {
                        if (entity is TrainCarEntity trainCarEntity)
                        {
                            Dictionary<long, TrainCarEntity> trainCarEntities = Mc.Entity.GetEntitiesViaType<TrainCarEntity>();
                            using Dictionary<long, TrainCarEntity>.Enumerator enumerator = trainCarEntities.GetEnumerator();
                            List <long> trainCarEntityIds = new();
                            while (enumerator.MoveNext())
                            {
                                if (trainCarEntity.HeadEntityId <= 0 || trainCarEntity.HeadEntityId != enumerator.Current.Value.HeadEntityId)
                                    continue;

                                if (enumerator.Current.Value.TryGetIHitable(out IHitableEntity hittable) && hittable.Hp < hittable.MaxHp) // 需要修理
                                    trainCarEntityIds.Add(enumerator.Current.Key);
                            }
                            Mc.Construction.TryRepair(trainCarEntityIds);
                        }
                    }
                    else
                        Mc.Construction.TryRepair(mountableEntityId);
                    return;
                /* 上乘客位*/
                case EntityInteractiveIdHandle.GetInPassengerSeat:
                    // if (entity is HorseEntity)
                    // {
                    //     Mc.Audio.AddAudio("Mon_Horse_Mount", new Vector3(entity.PosX, entity.PosY, entity.PosZ));
                    // }
                    //
                    // if (entity is VehicleEntity vehicleEntity
                    //     && vehicleEntity.BelongEntityId != Mc.MyPlayer.MyEntityId
                    //     && vehicleEntity.BelongEntityId != -1
                    //     && !Mc.TeamClient.IsTeamMateViaEntityId(vehicleEntity.BelongEntityId))
                    // {
                    //     Mc.MsgTips.ShowRealtimeWeakTip(1202);
                    //     return true;
                    // }
                    if (entity is HorseEntity)
                    {
                        Mc.Audio.AddAudio("Mon_Horse_Mount", new Vector3(entity.PosX, entity.PosY, entity.PosZ));
                    }
                    SendWantsMount(Mc.MyPlayer.MyEntityLocal, SeatType.Passenger, mountable);
                    return;
                /* 下车*/
                case EntityInteractiveIdHandle.GetOutVehicle:
                    if (entity is HorseEntity)
                    {
                        Mc.Audio.AddAudio("Mon_Horse_Dismount", new Vector3(entity.PosX, entity.PosY, entity.PosZ));
                    }

                    SendWantsDismount(Mc.MyPlayer.MyEntityLocal, mountable);
                    return;
                /* 切换座位*/
                case EntityInteractiveIdHandle.SwitchVehicleSeat:
                    SendSwitchMountSeat();
                    return;

                case EntityInteractiveIdHandle.VehicleStorage:
                    //UiOtherSideVehicleStorage.OpenWinLoot(mountableEntityId);
                    return;
                case EntityInteractiveIdHandle.HorseEquipStorage:
                    if (TryGetLootEntity(out var e, mountableEntityId))
                    {
                        OpenLootingOtherSide(e, (int)EUiHalfOthersideType.HorseEquip);
                        /* 打开马匹装备时，会激活马匹的觅食状态*/
                        Mc.MyPlayer.MyEntityServer.RemoteCallUpdateForagingState(ERpcTarget.Simulator, mountableEntityId);
                    }

                    return;
                case EntityInteractiveIdHandle.VehicleOp:
                    if (TryGetLootEntity(out var mountableEntity, mountableEntityId))
                    {
                        OpenLootingOtherSide(mountableEntity, (int)EUiHalfOthersideType.VehicleOp);
                        /* 打开马匹装备时，会激活马匹的觅食状态*/
                        Mc.MyPlayer.MyEntityServer.RemoteCallUpdateForagingState(ERpcTarget.Simulator, mountableEntityId);
                    }

                    return;
            }
        }

        private void SendWantsMount(PlayerEntity playerEntity, SeatType seatType, IMountable mountable)
        {
            if (!playerEntity.CanOperateMount)
            {
                return;
            }

            if (Mc.Vehicle.InteractionMountableEntityId > 0)
            {
                var entityId = Mc.Vehicle.InteractionMountableEntityId;
                if (mountable.IsSeatTypeFull(seatType))
                {
                    if (playerEntity.MountableId != entityId)
                    {
                        Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.TargetSeatIsNotEmpty);
                    }
                    return;
                }

                if (playerEntity.ProhibitMountHorse && mountable is BaseSocHorseGo)
                {
                    Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.ProhibitMountHorse);
                    return;
                }
                
                if (mountable is BaseVehicleModuleGo vehicleModuleGo)
                {
                    if (vehicleModuleGo.IsSeatTypeFullViaModule(seatType) && vehicleModuleGo.GetSeatCount(seatType) > 0 || vehicleModuleGo.GetSeatCount(seatType) <= 0)
                    {
                        foreach (var (_, vmg) in vehicleModuleGo.ModularCarGo.VehicleModuleGoes)
                        {
                            if (vmg.IsSeatTypeFullViaModule(seatType) == false && vmg.GetSeatCount(seatType) > 0)
                            {
                                entityId = vmg.EntityId;
                            }
                        }
                    }
                }

                var entity = Mc.Entity.GetEntity(entityId);
                if (entity == null)
                {
                    return;
                }
                var raycastEntity = Mc.Entity.GetEntity(Mc.Vehicle.RaycastMountableEntityId);
                if (raycastEntity != null && raycastEntity.RootParent == entity.RootParent)
                {
                    if (NoBlockBetweenEyeAndMountable(entity) == false)
                    {
                        Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.BlockBetweenEyeAndMountable);
                        return;
                    }
                }
                Mc.MyPlayer.MyEntityServer.RemoteCallWantsMount(ERpcTarget.Simulator, entityId, (int)seatType, -1);
            }
        }

        private bool NoBlockBetweenEyeAndMountable(IEntity targetMountable)
        {
            if (InteractiveIdListChecker.CurAimHitObjData == null)
            {
                return targetMountable != null;
            }

            var hitPoint = InteractiveIdListChecker.CurAimHitObjData.raycastHit.point;
            var cameraPos = Mc.MyPlayer.GetMyCamera.transform.position;
            var count = Physics.OverlapCapsuleNonAlloc(cameraPos, hitPoint, Mc.Tables.TbGlobalConfig.MountEyeCheckRadius,
                tempColliders, MgrConfigPhysicsLayer.MaskVehicleBlockCheck, QueryTriggerInteraction.Ignore);
            for (int i = 0; i < count; i++)
            {
                var collider = tempColliders[i];
                var entity = collider.GetEntity();
                if (entity == null)
                {
                    Log.InfoFormat("上车检测到了碰撞:{0}", collider.name);
                    return false;
                }

                if (entity.EntityId == Mc.MyPlayer.MyEntityId)
                {
                    continue;
                }

                if (ColliderExtension.IsSameEntityOrSameParent(targetMountable, entity) == false)
                {
                    Log.InfoFormat("上车检测到了entity:{0}，碰撞:{1}", entity.EntityId, collider.name);
                    return false;
                }
            }

            return true;
        }

        private void SendWantsDismount(PlayerEntity playerEntity, IMountable mountable)
        {
            if (playerEntity.IsOnMount)
            {
                Mc.MyPlayer.MyEntityServer.RemoteCallWantsDismount(ERpcTarget.Simulator);
            }
        }


        public void SendSwitchMountSeat()
        {
            Mc.MyPlayer.MyEntityServer.RemoteCallSwitchMountSeat(ERpcTarget.Simulator, 0);
        }


        /// <summary>
        /// 查看需要打开的界面类型，例如UiInventory
        /// </summary>
        /// <param name="data">通用交互表数据</param>
        /// <param name="lootEntity">看见的实体数据</param>f
        private void OpenInteractView(Interaction data, IEntity lootEntity)
        {
            if (data == null)
            {
                return;
            }

            curLootEntityId = lootEntity.EntityId;

            switch (data.UiName)
            {
                case "UiInventory":
                    OpenLootingWithStyle(data.Type, lootEntity);
                    break;
                case "UiInventoryOtherside":
                    OpenLootingInventoryOtherside(lootEntity, data.Type);
                    break;

                case "UiOtherSide":
                    OpenLootingOtherSide(lootEntity, data.Type);
                    break;

                case "UiOtherSideNew":
                    OpenLootingOtherSideNew(lootEntity, data.Type);
                    break;
                case "UiTechTree":
                    UiTechTree.Open(data.Type);
                    break;

                case "UiInputCode":
                    //UiInputCode.OpenWindow(data.Type, lootEntity);
                    break;

                case "UiElectricalBranch":
                    //Mc.Construction.OpenCommonDataSetting(lootEntity.EntityId);
                    UiElectricalBranch.OpenWindow(lootEntity.EntityId);
                    break;

                case "UiShopPlayer":
                    UiShopPlayer.Open(lootEntity.EntityId);
                    break;

                case "UiShopUpload":
                    UiShopUpload.Open(lootEntity.EntityId);
                    break;

                case "UiWaterCatcher":
                    UiWaterCatcher.OpenWindow(lootEntity.EntityId);
                    break;

                case "UiPlantMain":
                    UiPlantMain.RemoteCallStartLooting(curLootEntityId, isSucess =>
                    {
                        if (isSucess)
                        {
                            var uiPlantMian = UiPlantMain.OpenWindow();
                            uiPlantMian.OpenDefaultPlantOperationSubPage(curLootEntityId,(EPageType)data.Type);
                        }
                    });
                    break;

                case "UiSwipCardGame":
                    OpenSwipCardGame(lootEntity, data.Id);
                    break;
                case "UiSurprisePlay":
                    // Mc.MyPlayer.MyEntityServer.SurpriseComp.RemoteCallOpenDecipher(ERpcTarget.World);
                    break;
                case "UiTaskInteracting":
                    break;  //防止StartLooting调用
                case "UiTerritoryCenter":
                    UiTerritoryCenter.OpenWindow(lootEntity.EntityId);
                    break;
                case "UiTerritoryCenterEdit":
                    UiTerritoryEdit.OpenWindow(lootEntity.EntityId);
                    break;
                case "UiTrainDischarge":
                    UiTrainDischarge uiTrainDischarge = Mc.Ui.OpenWindowT<UiTrainDischarge>("UiTrainDischarge");
                    uiTrainDischarge.lootEntity = lootEntity;
                    uiTrainDischarge.HasTreasuretTaskTracked();
                    break;
                case "UiTurret":
                    UiTurret uiTurret = Mc.Ui.OpenWindowT<UiTurret>("UiTurret");
                    uiTurret.RemoteCallStartLooting(lootEntity.EntityId);
                    break;
                case "UiResearch":
                    UiResearch.OpenWindow(lootEntity.EntityId);
                    break;
                case "UiRepair":
                    UiRepair.OpenWindow(lootEntity.EntityId);
                    break;
                case "UiDig":
                    UiDig.OpenWindow(lootEntity.EntityId);
                    break;
                case "UiManger":
                    UiManger.OpenWindow(lootEntity.EntityId);
                    break;
                default:
                    // 目前默认随便打开了一个异端容器
                    UiInventoryOtherside.OpenAsOtherside(UiOthersideType.None, lootEntity.EntityId, CloseLootView);
                    break;
            }
        }


        /// <summary>
        /// 打开指定交互界面类型
        /// </summary>
        /// <param name="style">指定界面类型</param>
        /// <param name="lootEntity">看见的实体数据</param>
        private void OpenLootingWithStyle(int style, IEntity lootEntity)
        {
            var type = UiOthersideType.None;

            var isUsingByOthers = false;
            if (lootEntity is PartEntity)
            {
                isUsingByOthers = PartMoudleStateHelper.GetMoudleState(((PartEntity)lootEntity).ModuleState, PartMoudleStateHelper.Using);
            }

            switch (style)
            {
                case 1:
                case 2:
                    type = UiOthersideType.NearBy;
                    break;

                case 3:
                    type = UiOthersideType.Fuse;
                    break;

                case 5:
                    type = UiOthersideType.Decompose;
                    if (lootEntity.EntityType == EntityTypeId.PartEntity)
                    {
                        var decomposeEntity = Mc.Entity.GetPartEntity(lootEntity.EntityId);
                        if (decomposeEntity != null)
                        {
                            if (isUsingByOthers)
                            {
                                Mc.MsgTips.ShowRealtimeWeakTip(11015);
                                return;
                            }
                        }
                    }

                    break;

                case 6:
                    type = UiOthersideType.Blueprint;
                    if (lootEntity.EntityType == EntityTypeId.PartEntity)
                    {
                        var researchEntity = Mc.Entity.GetPartEntity(lootEntity.EntityId);
                        if (researchEntity != null)
                        {
                            if (isUsingByOthers)
                            {
                                Mc.MsgTips.ShowRealtimeWeakTip(11028);
                                return;
                            }
                        }
                    }

                    break;

                case 7:
                    // 打开领地柜，需要获取当前保护材料
                    type = UiOthersideType.ToolCupboard;
                    break;

                case 8:
                    if (isUsingByOthers)
                    {
                        Mc.MsgTips.ShowRealtimeWeakTip(11027);
                        return;
                    }

                    type = UiOthersideType.RepairBench;
                    break;

                case 9:
                    if (isUsingByOthers)
                    {
                        Mc.MsgTips.ShowRealtimeWeakTip(11023);
                        return;
                    }

                    UiModerator.Open(lootEntity.EntityId);
                    return;

                case 10:
                    type = UiOthersideType.AutoTurret;
                    break;
                case 13:
                    type = UiOthersideType.Composter;
                    if (lootEntity.EntityType == EntityTypeId.PartEntity)
                    {
                        var composterEntity = Mc.Entity.GetPartEntity(lootEntity.EntityId);
                        if (composterEntity != null)
                        {
                            if (isUsingByOthers)
                            {
                                Mc.MsgTips.ShowRealtimeWeakTip(11015);
                                return;
                            }
                        }
                    }

                    break;
                case 14:
                    // 归还中枢
                    type = UiOthersideType.SafeBox;

                    // 没有激活 无法打开
                    if (Mc.MyPlayer.MyEntityServer.SafetyBoxEntityId == 0 || Mc.CollectionItem.SafetyBoxEntity == null)
                    {
                        Mc.MsgTips.ShowRealtimeWeakTip(24210);
                        return;
                    }

                    // 非暂存状态 
                    if (Mc.CollectionItem.PlayerSafetyBoxState != SafetyBoxState.Temporary)
                    {
                        UiInventoryOtherside.OpenAsTempSafeBox(type, Mc.MyPlayer.MyEntityServer.SafetyBoxEntityId, CloseLootView);
                        return;
                    }

                    UiInventoryOtherside.OpenAsOtherside(type, Mc.MyPlayer.MyEntityServer.SafetyBoxEntityId, CloseLootView);
                    return;
                case 15:
                    type = UiOthersideType.HitchPost;
                    break;
                case 28:
                    type = UiOthersideType.Locker;
                    break;
                default:
                    break;
            }

            if (lootEntity.EntityType != EntityTypeId.BoxEntity && lootEntity.EntityType != EntityTypeId.AirdropEntity)
            {
                UiInventoryOtherside.OpenAsOtherside(type, lootEntity.EntityId, CloseLootView);
            }
            else
            {
                var boxGo = Mc.Go.GetGo(lootEntity.EntityId) as BoxGo;
                if (boxGo != null && boxGo.IsShowInventory())
                {
                    UiInventoryOtherside.OpenAsOtherside(type, lootEntity.EntityId, CloseLootView);
                }
            }
        }


        /// <summary>
        /// 打开指定交互界面类型
        /// </summary>
        /// <param name="lootEntity">看见的实体数据</param>
        /// <param name="style">指定界面类型</param>
        public void OpenLootingOtherSide(IEntity lootEntity, int style)
        {
            var type = EUiHalfOthersideType.None;

            switch (style)
            {
                case 4:
                    var lootPartEntity = lootEntity as PartEntity;
                    if (lootPartEntity == null)
                    {
                        return;
                    }

                    switch ((PartType)lootPartEntity.TemplateId)
                    {
                        case PartType.Furnace:
                            type = EUiHalfOthersideType.OvenFurnace;
                            break;

                        case PartType.OilRefinery:
                        case PartType.OilRefinery_v1:
                            type = EUiHalfOthersideType.OvenOilRefinery;
                            break;

                        case PartType.Campfire:
                            type = EUiHalfOthersideType.OvenCampfire;
                            break;

                        case PartType.FurnaceLarge:
                            type = EUiHalfOthersideType.OvenFurnaceLarge;
                            break;

                        case PartType.HoboBarrel:
                            type = EUiHalfOthersideType.OvenHoboBarrel;
                            break;

                        case PartType.Barbeque:
                            type = EUiHalfOthersideType.OvenBarbeque;
                            break;

                        case PartType.StoneFireplace:
                            type = EUiHalfOthersideType.OvenStoneFireplace;
                            break;

                        default:
                            break;
                    }

                    break;

                case 11:
                    type = EUiHalfOthersideType.Dig;
                    break;

                case 12:
                    Mc.Audio.Add2DAudioByConfig("Int_Reputation_ToolBox_Transfer_Open");
                    type = EUiHalfOthersideType.Reputation;
                    break;

                case 13:
                    type = EUiHalfOthersideType.Fertilizer;
                    break;
                case 15:
                    type = EUiHalfOthersideType.HitchPost;
                    break;
                case 16:
                    type = EUiHalfOthersideType.Shotgun;
                    break;
                case 17:
                    type = EUiHalfOthersideType.FlameTurret;
                    break;
                case 18:
                    type = EUiHalfOthersideType.ModularCarEngine;
                    break;
                case 21:
                    type = EUiHalfOthersideType.HorseEquip;
                    break;
                case 26:
                    type = EUiHalfOthersideType.VehicleOp;
                    break;
                default:
                    break;
            }

            if (lootEntity.EntityType != EntityTypeId.BoxEntity && lootEntity.EntityType != EntityTypeId.AirdropEntity)
            {
                UiOtherSide.OpenOtherSideWin(type, lootEntity.EntityId, OnCloseView, OnOpenView);
            }
            else
            {
                var boxGo = Mc.Go.GetGo(lootEntity.EntityId) as BoxGo;
                if (boxGo != null && boxGo.IsShowInventory())
                {
                    UiOtherSide.OpenOtherSideWin(type, lootEntity.EntityId, OnCloseView, OnOpenView);
                }
            }
        }

        /// <summary>
        /// 打开指定交互界面类型
        /// </summary>
        /// <param name="lootEntity">看见的实体数据</param>
        /// <param name="style">指定界面类型</param>
        public void OpenLootingOtherSideNew(IEntity lootEntity, int style)
        {
            UiOtherSideNew.OpenOtherSideWin(lootEntity.EntityId);
        }

        public void OpenLootingInventoryOtherside(IEntity lootEntity, int style)
        {
            var type = UiOthersideType.None;

            switch (style)
            {
                case 1:
                case 2:
                    type = UiOthersideType.NearBy;
                    break;
            }

            if (type == UiOthersideType.None)
            {
                return;
            }

            UiInventoryOtherside.OpenAsMulitOtherside(type, lootEntity.EntityId, style, CloseLootView);
        }

        /// <summary>
        /// 打开刷卡游戏
        /// </summary>
        /// <param name="lootEntity"></param>
        /// <param name="interactionId"></param>
        private void OpenSwipCardGame(IEntity lootEntity, int interactionId)
        {
            var ioEntity = lootEntity as IOEntity;
            if (ioEntity != null)
            {
                var ioCfg = Mc.Tables.TbIOInteractive.GetOrDefault(ioEntity.TemplateId);
                if (ioCfg.IOType == IOType.FuseBox)
                {
                    if (ioEntity.IsUsed)
                    {
                        Mc.MsgTips.ShowRealtimeWeakTip(11138);
                        return;
                    }

                    var resetLeftTime = 0; //如果激活需要显示剩余重置时间 毫秒
                    bool isActivation = ioEntity.Activation == 2; //已激活
                    if (isActivation)
                    {
                        if (Mc.Time.ServerWorldTime < ioEntity.GameResetTimeStamp)
                        {
                            resetLeftTime = (int)(ioEntity.GameResetTimeStamp - Mc.Time.ServerWorldTime);
                        }
                    }
                    else
                    {
                        var myPlayer = Mc.MyPlayer.MyEntityLocal;
                        var gameCfg = Mc.Tables.TbPowerGameData.GetOrDefault(ioEntity.GameGraphId);
                        float hp = myPlayer.TryGetComponent(EComponentIdEnum.Damageable, out DamageableComponent dc) ? dc.Hp : 0;
                        if (hp <= gameCfg.Faildamage)
                        {
                            Mc.MsgTips.ShowRealtimeWeakTip(11139);
                            return;
                        }
                    }

                    Mc.Ui.OpenWindowT<UiSwipeCardGame>("UiSwipeCardGame", (win) =>
                    {
                        ioEntity.RemoteCallOnPlayerOpenFuseBoxGame(ERpcTarget.Simulator);
                        Mc.Audio.Add2DAudioByConfig("Int_MysteryBox_Open");
                        int state = (int)EGameState.UNCONNECTED;
                        IOInteractiveState stateConfig = McCommon.Tables.TbIOInteractiveState.GetOrDefault(ioEntity.StateId);
                        if (stateConfig is { Activation: 0 })
                            /*EIOActivationStatusEnum.Default*/ state = (int)EGameState.DEFAULT;
                        if (ioEntity.Activation == 2/*EIOActivationStatusEnum.Activation*/) state = (int)EGameState.CONNECTED;
                        // 2: 代表IO已激活
                        win.Init(ioEntity.GameGraphId
                            , state
                            , resetLeftTime
                            , ioEntity.EntityId
                            , ioEntity.LeftGuaranteedNum
                            , ioEntity.GameSuccessPath);
                    });
                }
            }
        }

        /// <summary>
        /// 关闭掠夺界面回调
        /// </summary>
        /// <param name="lootingEntityId"></param>
        private void CloseLootView(long lootingEntityId)
        {
            if (!isCloseLooting)
            {
                return;
            }

            curLootEntityId = -1;
            //Mc.MyPlayer.MyEntityServer.RemoteCallStopLooting();
            PlayStopLootingSound(lootingEntityId);
        }

        private void PlayStopLootingSound(long lootingEntityId)
        {
            var boxGo = Mc.Go.GetGo(lootingEntityId) as BoxGo;
            if (boxGo != null)
            {
                var audioConfig = Mc.Tables.TbBoxAudio.GetOrDefault(boxGo.boxData.BoxType);
                if (audioConfig != null && audioConfig.SoundEvent.Length > 1)
                    Mc.Audio.Add2DAudio(audioConfig.SoundEvent[1]);
            }
        }
        private void OnOpenView(EUiHalfOthersideType mode, long lootingEntityId)
        {
            if (mode == EUiHalfOthersideType.VehicleOp)
            {
                OnOpenEngine(lootingEntityId);
            }
        }


        private void OnCloseView(UiOthersideType mode, long lootingEntityId)
        {
            CloseLootView(lootingEntityId);
        }


        private void OnCloseView(EUiHalfOthersideType mode, long lootingEntityId)
        {
            if (mode == EUiHalfOthersideType.VehicleOp)
            {
                OnCloseEngine(lootingEntityId);
            }

            CloseLootView(lootingEntityId);
        }


        void OnCloseEngine(long lootingEntityId)
        {
            var my_player = Mc.MyPlayer;
            if (my_player == null)
            {
                return;
            }

            var mountGo = my_player.GetMountableGo();
            if (mountGo == null)
            {
                Mc.Go.Gos.TryGetValue(lootingEntityId, out var entityGo);
                mountGo = entityGo as BaseMountableGo;
                if (mountGo is BaseVehicleModuleGo baseVehicleModuleGo)
                {
                    mountGo = baseVehicleModuleGo.ModularCarGo;
                }
            }

            if (mountGo == null)
            {
                return;
            }

            VehicleSoundConf default_sound_conf = null;
            var audio_config = PlayerLoader.AudioConfig;
            if (audio_config != null && audio_config.DefaultSoundConf != null)
            {
                default_sound_conf = audio_config.DefaultSoundConf;
            }

            //播放自定义音效
            var vehicle_audio = mountGo.MainGo.GetComponent<GroundVehicleAudio>();
            if (vehicle_audio && vehicle_audio.SoundConf != null && vehicle_audio.SoundConf.HoodCloseSoundDef.IsValid())
            {
                vehicle_audio.SoundConf.HoodCloseSoundDef.PlaySound(mountGo.MainGo.transform,mountGo.EntityId,mountGo.MountableEntity.DriverId);
                return;
            }

            //播放默认
            if (default_sound_conf != null)
            {
                default_sound_conf.HoodCloseSoundDef.PlaySound(mountGo.MainGo.transform,mountGo.EntityId,mountGo.MountableEntity.DriverId);
            }
        }


        void OnOpenEngine(long lootingEntityId)
        {
            var my_player = Mc.MyPlayer;
            if (my_player == null)
            {
                return;
            }

            var mountGo = my_player.GetMountableGo();
            if (mountGo == null)
            {
                Mc.Go.Gos.TryGetValue(lootingEntityId, out var entityGo);
                mountGo = entityGo as BaseMountableGo;
                if (mountGo is BaseVehicleModuleGo baseVehicleModuleGo)
                {
                    mountGo = baseVehicleModuleGo.ModularCarGo;
                }
            }

            if (mountGo == null)
            {
                return;
            }

            VehicleSoundConf default_sound_conf = null;
            var audio_config = PlayerLoader.AudioConfig;
            if (audio_config != null && audio_config.DefaultSoundConf != null)
            {
                default_sound_conf = audio_config.DefaultSoundConf;
            }

            //播放自定义音效
            var vehicle_audio = mountGo.MainGo.GetComponent<GroundVehicleAudio>();
            if (vehicle_audio && vehicle_audio.SoundConf != null && vehicle_audio.SoundConf.HoodOpenSoundDef.IsValid())
            {
                vehicle_audio.SoundConf.HoodOpenSoundDef.PlaySound(mountGo.MainGo.transform,mountGo.EntityId,mountGo.MountableEntity.DriverId);
                return;
            }

            //播放默认
            if (default_sound_conf != null)
            {
                default_sound_conf.HoodOpenSoundDef.PlaySound(mountGo.MainGo.transform,mountGo.EntityId,mountGo.MountableEntity.DriverId);
            }
        }


        /// <summary>
        /// 玩家状态事件监听
        /// </summary>
        /// <param name="stateId"></param>
        private void PlayerStateAction(int stateId)
        {
            isCloseInteractiveList = stateId != 0;

            if (stateId == 0)
            {
                // 是否关闭拾取列表
                UpdatePickableItemsList();
            }
            else
            {
                // 击倒或者击杀状态
                // 如果拾取列表是打开的，关闭拾取列表
                // OpenPickList = false;
            }
        }

        /// <summary>
        /// 玩家背包更新
        /// </summary>
        private void OnInventoryUpdate()
        {
            // 更新马的食物列表
            RefreshHorseFoodList();

            // 尝试快速使用物品
            TryQuickUseItem();
        }

        private void OnInterruptPickListMsg(bool isInterrupt)
        {
            if (!statusTarget.visible) return;

            if (!isInterrupt)
            {
                // 记录之前的状态
                isOpenListBefore = OpenPickList;
                OpenPickList = false;
            }
            else
            {
                // 恢复之前的状态
                OpenPickList = isOpenListBefore;
            }
        }

        /// <summary>
        /// 当前注视对象的EntityId变化回调
        /// </summary>
        /// <param name="curEyeEntityId"></param>
        /// <param name="lastEyeEntityId"></param>
        private void EyeEntityIdChangeAction(long curEyeEntityId, long lastEyeEntityId)
        {
            // 设置描边
            if (curEyeEntityId != -1)
            {
                SetDragEdgesEffect(curEyeEntityId);
            }
            else
            {
                SetDragEdgesEffect();
            }

            OnCurEyeEntityIdChange(curEyeEntityId);
        }


        /// <summary>
        /// 当前注视对象的Gameobject变化回调
        /// </summary>
        /// <param name="curEyeEntityId"></param>
        /// <param name="lastEyeEntityId"></param>
        private void EyeGameobjectChangeAction(long curEyeEntityId, GameObject go)
        {
            // 设置描边
            if (curEyeEntityId != -1)
            {
                SetDragEdgesEffect(curEyeEntityId, go);
            }
            else
            {
                SetDragEdgesEffect();
            }

            OnCurEyeEntityIdChange(curEyeEntityId);
        }

        private void OnCurEyeEntityIdChange(long curEyeEntityId)
        {
            // 当前准星交互ID
            var id = InteractiveIdListChecker.CanInteractCrosshairEntityId;
            if (this.curEyeEntityId != curEyeEntityId && id == curEyeEntityId && id != -1)
            {
                if (Mc.GatherItemPickable.IsPickListLooting() && curEyeEntityId == curLootEntityId)
                {
                    // 如果视线又回到了当前正在掠夺的实体上，不做任何处理
                }
                else
                {
                    isOpenListBefore = OpenPickList;
                    // 准星交互对象变化（关闭交互列表的显示）
                    OpenPickList = false;
                } 
            }
            else if (this.curEyeEntityId != curEyeEntityId)
            {
                if (!Mc.GatherItemPickable.IsRangeInteractiveEntity(curLootEntityId) &&
                    !Mc.Construction.IsInBuildMode)
                {
                    // 恢复之前的状态
                    OpenPickList = isOpenListBefore;
                }    
            }

            this.curEyeEntityId = curEyeEntityId;
            var entity = Mc.Entity.GetEntity(curEyeEntityId);
            curEyeEntityTypeId = entity?.EntityType ?? 0;
        }

        /// <summary>
        /// 交互列表显示状态变化
        /// </summary>
        /// <param name="isShowList"></param>
        private void InteractiveIdListChangeAction(bool isShowList, long curEyeEntityId, bool isForceUpdateVisible = false, GameObject hitGo = null)
        {
            // 设置描边
            if (curEyeEntityId != -1)
            {
                SetDragEdgesEffect(curEyeEntityId, hitGo);
            }
            else
            {
                SetDragEdgesEffect();
            }

            OnCurEyeEntityIdChange(curEyeEntityId);

            if (isShowList)
            {
                Mc.Msg.FireMsgAtOnce(EventDefine.Interactive2LabelShow, false, curEyeEntityId);
                SetElemVisible(true, isForceUpdateVisible);
                //SafeUtil.SafeSetText(titleText, GetExpandInteractiveTitleStr(curEyeEntityId));
            }
            else
            {
                Mc.Msg.FireMsgAtOnce(EventDefine.Interactive2LabelShow, hitGo != null, curEyeEntityId);
                var myPlayer = Mc.MyPlayer;
                // 当前交互列表是否还有可以显示的内容
                if ((GetPickableItemListCount() == 0 && Mc.GatherItemPickable.IsOpenSceneItemList) ||
                    (myPlayer.MyEntityLocal.IsDead || myPlayer.MyEntityLocal.IsDying || myPlayer.MyEntityLocal.IsWounded))
                {
                    SetElemVisible(false);
                }
            }
        }

        private string GetExpandInteractiveTitleStr(long curEyeEntityId = -1)
        {
            titleSet.Clear();
            var titleStr = string.Empty;
            var interactionTypeSet = InteractiveIdListChecker.InteractionTypeSet;

            foreach (var interactionType in interactionTypeSet)
            {
                if ((curSelectInteractionType != EInteractionType.None
                    && curSelectInteractionType != EInteractionType.Entity
                    && interactionType != curSelectInteractionType)
                    || nearbyInteractiveType.ContainsKey(interactionType))
                {
                    continue;
                }

                string name = interactionType switch
                {
                    EInteractionType.Entity => InteractiveIdListChecker.CurEyeEntityName,
                    EInteractionType.Water => InteractiveIdListChecker.WaterName,
                    EInteractionType.Vehicle => InteractiveIdListChecker.VehicleName,
                    EInteractionType.CollectableEntity => InteractiveIdListChecker.GetCollectableEntityNameById(curEyeEntityId),
                    EInteractionType.RangeInteractiveBox => Mc.GatherItemPickable.RangeInteractiveBoxName,
                    EInteractionType.LandMine => InteractiveIdListChecker.LandMineName,
                    EInteractionType.RFC4Entity => InteractiveIdListChecker.GetRFC4EntityTitleName(),
                    _ => string.Empty
                };

                if (interactionType == EInteractionType.Entity && name.IsNullOrWhitespace())
                {
                    name = Mc.GatherItemPickable.GetRecoveryEntityName();
                }

                if (!name.IsNullOrWhitespace())
                {
                    if (titleSet.Contains(name))
                    {
                        continue;
                    }
                    else
                    {
                        titleSet.Add(name);
                    }

                    titleStr = titleStr.IsNullOrWhitespace() ? name : ZString.Concat(titleStr, "/", name);
                }
            }

            var expandList = GetExpandInteractiveList();
            if (expandList.Count == 1 && expandList[0] == EntityInteractiveIdHandle.Recovery)
            {
                titleStr = Mc.GatherItemPickable.GetRecoveryEntityName();
            }
            else
            {
                if (titleStr.IsNullOrWhitespace())
                {
                    titleStr = InteractiveIdListChecker.CurEyeEntityName;
                }
            }

            return titleStr;
        }


        /// <summary>
        /// 检测交互Id更新逻辑
        /// </summary>
        /// <param name="curEyeEntityId"></param>
        private void UpdateDetectionAction(long curEyeEntityId)
        {
            if (curLootEntityId > 0 && curLootEntityId != curEyeEntityId)
            {
                var data = Mc.Tables.TbInteraction.GetOrDefault(lastBoxId);
                if (!Mc.GatherItemPickable.IsRangeInteractiveEntity(curLootEntityId))
                {
                    //if (Mc.GatherItemPickable.IsPickListLooting())
                    //{
                    //    logger.InfoFormat($"StopLooting by UpdateDetectionAction. curLootEntityId:{curLootEntityId}, curEyeEntityId:{curEyeEntityId}");
                    //}

                    CloseInteractView(data);
                }
            }
        }


        /// <summary>
        /// 设置可交互物品的描边
        /// </summary>
        /// <param name="entityId"></param>
        /// <param name="hitGo"></param>
        private void SetDragEdgesEffect(long entityId = 0, GameObject hitGo = null)
        {
            // 获取实体对象
            var entity = Mc.Entity.GetEntity(entityId);

            // 检查是否需要启用描边逻辑（如实体可交互或需要高亮）
            if (!ShouldEnableOutline(entityId))
            {
                HandleDisableOutline(entityId);
                return;
            }

            // 怪物尸体需要指向其怪物Entity (战斗那边为了解决表现问题)
            entity = GetMonsterCorpseEntity(entity);

            if (entity == null || entity.EntityId <= 0)
            {
                Mc.EnhancedDisplayTip?.DisableOutline();
                return;
            }

            // 检查是否需要跳过玩家描边逻辑（如倒地队友）
            if (ShouldSkipPlayerOutline(entity))
            {
                return;
            }

            // 检查是否需要禁用载具描边逻辑（如玩家正在乘坐的载具）
            if (ShouldDisableMountOutline(entity))
            {
                Mc.EnhancedDisplayTip?.DisableOutline();
                return;
            }

            // 获取描边实体的ID（如怪物尸体或可交互实体）
            long outlineEntityId = GetOutlineEntityId(entity, hitGo);

            // 如果描边实体ID有效，启用描边逻辑
            if (outlineEntityId > 0)
            {
                EnableOutlineForEntity(outlineEntityId);
            }
        }

        /// <summary>
        /// 怪物尸体 指向怪物本身的Entity
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        private IEntity GetMonsterCorpseEntity(IEntity entity)
        {
            if (entity is CorpseEntity corpseEntity && corpseEntity.CorpseType == ECorpseTypeEnum.Monster)
            {
                entity = Mc.Entity.GetEntity(corpseEntity.HostEntityId);
            }
            return entity;
        }

        /// <summary>
        /// 检查是否需要跳过玩家描边逻辑（如倒地队友）
        /// </summary>
        private bool ShouldSkipPlayerOutline(IEntity entity)
        {
            if (entity is PlayerEntity playerEntity)
            {
                return playerEntity.IsWounded
                       && !playerEntity.IsDead
                       && playerEntity.EntityId != Mc.MyPlayer.MyEntityId
                       && TeamEntity.Instance?.MemberDic?.ContainsKey(playerEntity.RoleId) == true;
            }
            return false;
        }

        /// <summary>
        /// 检查是否需要禁用载具描边逻辑（如玩家正在乘坐的载具）
        /// </summary>
        private bool ShouldDisableMountOutline(IEntity entity)
        {
            if (!(Mc.MyPlayer.MyEntityLocal.IsOnMount &&
                entity is VehicleEntity &&
                entity is CarEntity &&
                entity is ModularCarEntity &&
                entity is HorseEntity &&
                entity is TrainCarEntity))
            {
                return false;
            }

            var mountableId = Mc.MyPlayer.MyEntityLocal.MountableId;

            // 如果实体是当前玩家乘坐的载具，禁用描边
            if (entity.EntityId == mountableId)
            {
                return true;
            }

            // 检查模块化载具的描边逻辑
            var vehicleModuleGo = Mc.Go.GetGo(mountableId) as ClientVehicleModuleGo;
            if (vehicleModuleGo?.VehicleModuleCustom != null && entity.EntityId != vehicleModuleGo.VehicleModuleCustom.ModularCarId)
            {
                return true;
            }

            // 检查火车载具的描边逻辑
            var trainCarGo = Mc.Go.GetGo(mountableId) as TrainCarGo;
            return trainCarGo?.BaseVehicle?.VehicleEntity != null && entity.EntityId != trainCarGo.BaseVehicle.VehicleEntity.EntityId;
        }

        /// <summary>
        /// 检查是否需要启用描边逻辑（如实体可交互或需要高亮）
        /// </summary>
        private bool ShouldEnableOutline(long entityId)
        {
            if (!Mc.EnhancedDisplayTip.IsOutlineEnable()) return false;
            
            bool canInteractive = InteractiveIdListChecker.InteractiveIdSortList.Count > 0
                                  && entityId > 0
                                  && (Mc.Vehicle.RaycastMountableEntityId == entityId
                                      || InteractiveIdListChecker.CanInteractCrosshairEntityId == entityId);

            bool isHighlight = !canInteractive && InteractiveIdListChecker.IsAimHighlight(entityId);

            return canInteractive || isHighlight;
        }

        /// <summary>
        /// 处理需要禁用描边的情况（如构建模式或过滤条件满足）
        /// </summary>
        private void HandleDisableOutline(long entityId)
        {
            if (!Mc.Construction.IsBuildWireMode)
            {
                Mc.EnhancedDisplayTip?.DisableOutline();
                return;
            }

            var buildWireMode = Mc.Construction.BuildWireMode;

            // 在建造模式下，检查是否需要过滤描边
            if (buildWireMode.CurEWireState == EWireState.CheckWire
                && (buildWireMode.FilterOutline(entityId)
                    || (Mc.EnhancedDisplayTip.GetOutlineData() is var outlineData
                        && outlineData != null
                        && buildWireMode.FilterOutline(outlineData.entityId))))
            {
                return;
            }

            Mc.EnhancedDisplayTip?.DisableOutline();
        }

        /// <summary>
        /// 获取描边实体的ID（如怪物尸体或可交互实体）
        /// </summary>
        private long GetOutlineEntityId(IEntity entity, GameObject hitGo)
        {
            // 处理特殊实体（如挖掘实体）的描边逻辑
            if (hitGo != null && entity.EntityId != -1 && entity.EntityType == EntityTypeId.DigEntity)
            {
                Mc.EnhancedDisplayTip?.EnableOutlineForGo(entity.EntityId, hitGo.transform?.parent.gameObject);
                return -1;
            }

            // 处理怪物尸体的描边逻辑
            if (entity is MonsterEntity monsterEntity && monsterEntity.Hp <= 0 && monsterEntity.CorpseEntityId > 0)
            {
                return monsterEntity.CorpseEntityId;
            }

            // 默认返回实体的ID
            return entity.EntityId;
        }

        /// <summary>
        /// 启用描边逻辑，考虑构建模式的状态
        /// </summary>
        private void EnableOutlineForEntity(long outlineEntityId)
        {
            if (!Mc.Construction.IsBuildWireMode)
            {
                Mc.EnhancedDisplayTip?.EnableOutline(outlineEntityId);
                return;
            }

            var buildWireMode = Mc.Construction.BuildWireMode;

            // 在建造模式下，检查是否需要过滤描边
            if (buildWireMode.CurEWireState == EWireState.StartWire
                || buildWireMode.CurEWireState == EWireState.CheckWire
                || buildWireMode.CurEWireState == EWireState.ClearWire)
            {
                if (!buildWireMode.FilterOutline(outlineEntityId))
                {
                    Mc.EnhancedDisplayTip?.EnableOutline(outlineEntityId);
                }
            }
        }

        /// <summary>
        /// 交互列表的打开和关闭
        /// </summary>
        /// <param name="visible"></param>
        private void SetElemVisible(bool visible, bool isForceUpdateVisible = false)
        {
            if (Mc.Ui.IsInGame)
            {
                Mc.EnhancedDisplayTip.UpdateInteractiveListEnable(visible);
            }

            if (UiHud.IsInEdit)
            {
                return;
            }

            // 当前如果是隐藏状态了，不需要再次隐藏
            if (!IsElemEnable && !visible)
            {
                return;
            }

            var isVisible = visible;
            if (isCloseInteractiveList)
            {
                if (InteractiveIdListChecker.PlayerStateCheck())
                {
                    isCloseInteractiveList = false;
                }
                else
                {
                    isVisible = false;
                }    
            }

            var interactiveIdList = InteractiveIdListChecker.InteractiveIdSortList;
            if (interactiveIdList == null || interactiveIdList.Count <= 0)
            {
                isVisible = false;
            }

            var cdBtnNum = uiInteractiveBtnCdTypeDic.Count;
            ClearCdTypeDic();

            bool open = false;
            if (IsElemEnable != isVisible || isForceUpdateVisible)
            {
                if (isVisible)
                {
                    Show();
                    open = true;
                }
                else
                {
                    Hide();
                }

                Mc.Msg.FireMsg(EventDefine.OnUiInteractiveListVisibleChange, isVisible);
            }

            if (isVisible)
            {
                if (!open)
                {
                    UpdateUIStyle();
                }
            }

            if (cdBtnNum == 0 && uiInteractiveBtnCdTypeDic.Count > 0)
            {
                // 新出现cd按钮
                OnInteractiveCdBtnChange(true);
            }

            if (cdBtnNum > 0 && uiInteractiveBtnCdTypeDic.Count == 0)
            {
                // cd按钮消失
                OnInteractiveCdBtnChange(false);
            }
        }

        /// <summary>
        /// 关闭需要打开的界面类型
        /// </summary>
        /// <param name="data">通用交互表数据</param>
        private void CloseInteractView(Interaction data)
        {
            if (data == null)
            {
                return;
            }

            if (Mc.GatherItemPickable.IsPickListLooting())
            {
                Mc.GatherItemPickable.TryStopPickListLooting();
                Mc.LootCollection.LootingComponent.RemoteCallStopLooting(ERpcTarget.World);
                // logger.InfoFormat("StopLooting by CloseInteractView");
                CloseLootView(curLootEntityId);
            }

            // 退出界面交互
            curLootEntityId = -1;

            switch (data.UiName)
            {
                case "UiInventory":
                    UiInventory.CloseInventory();
                    break;
                case "UiInventoryOtherside":
                    UiInventory.CloseInventory();
                    break;

                case "UiTechTree":
                    UiTechTree.Close();
                    break;

                case "UiInputCode":
                    // UiInputCode.CloseWindow();
                    break;

                case "UiElectricalBranch":
                    UiElectricalBranch.CloseWindow();
                    break;

                case "UiCommonDataSetting":
                    UiCommonDataSetting.CloseWindow();
                    break;

                case "UiShop":
                    UiShop.Close();
                    break;

                case "UiShopPlayer":
                    UiShopPlayer.Close();
                    break;

                case "UiShopUpload":
                    UiShopUpload.Close();
                    break;

                case "UiWaterCatcher":
                    UiWaterCatcher.CloseWindow();
                    break;

                case "UiReputation":
                    Mc.Control.RotationControl.IsCloseGyroSelf = false;
                    Mc.Control.IsCloseUIViewCtlSelf = false;

                    Mc.Ui.RemoveWindow("UiReputation");
                    break;

                case "UiReputationRecord":
                    Mc.Control.RotationControl.IsCloseGyroSelf = false;
                    Mc.Control.IsCloseUIViewCtlSelf = false;

                    Mc.Ui.RemoveWindow("UiReputationRecord");
                    break;

                case "UiReputationReward":
                    Mc.Control.RotationControl.IsCloseGyroSelf = false;
                    Mc.Control.IsCloseUIViewCtlSelf = false;

                    Mc.Ui.RemoveWindow("UiReputationReward");
                    break;

                case "UiPlantMain":
                    UiPlantMain.CloseWindow();
                    break;

                case "UiSwipeCardGame":
                    Mc.Control.RotationControl.IsCloseGyroSelf = false;
                    Mc.Control.IsCloseUIViewCtlSelf = false;

                    Mc.Ui.RemoveWindow("UiSwipeCardGame");
                    break;

                default:
                    UiInventory.CloseInventory();
                    break;
            }
        }


        /// <summary>
        /// 当前视点瞄准entity的EntityId（不一定可交互）
        /// </summary>
        /// <returns></returns>
        public long GetCurRayEntityId()
        {
            if (InteractiveIdListChecker != null)
            {
                return InteractiveIdListChecker.CurRayEntityId;
            }

            return 0;
        }

        protected override void ChangeElemAnchorPoint(GObject target, int anchor)
        {
            SetFourAnchorRelation(target, 1);
        }


        /// <summary>
        /// PC端点击执行
        /// </summary>
        public static void PCDirectClick()
        {
            var elem = GetInteractiveListElem();
            if (null == elem)
            {
                return;
            }

            elem.PCDirectClickInternal();
        }

        public static UiHudElemInteractiveList GetInteractiveListElem()
        {
            return UiHud.GetElem<UiHudElemInteractiveList>(282);
        }

        private Rect GetBlockArea()
        {
            var targetUI = statusTarget;
            if (targetUI == null) return Rect.zero;

            // 获取组件左上角坐标点
            var inStagePos = targetUI.LocalToStage(Vector2.zero);
            var targetPosX = inStagePos.x - (targetUI.pivotAsAnchor ? targetUI.width * targetUI.scaleX * targetUI.pivot.x : 0);
            var targetPosY = inStagePos.y - (targetUI.pivotAsAnchor ? targetUI.height * targetUI.scaleY * targetUI.pivot.y : 0);

            var pageName = stateCtrl.selectedPage;

            float width = targetUI.width;
            float height = 56;      // 标题
            if (pageName.Equals("ShowInteractive"))
            {
                width = interactiveBtnList.width;
                height += interactiveBtnList.height;
            }
            else if (pageName.Equals("ShowPickup"))
            {
                width = pickItemList.width;
                height += (pickItemList.height + quickPickBtn.height);
            }
            else if (pageName.Equals("ShowNearby"))
            {
                width = nearbyList.width;
                height += nearbyList.height;
            }
            else if (pageName.Equals("ShowHorseFood"))
            {
                width = horseFoodList.width;
                height += horseFoodList.height;
            }
            return new Rect(targetPosX, targetPosY, width, height);
        }


        /// <summary>
        /// PC端点击执行
        /// </summary>
        private void PCDirectClickInternal()
        {
            if (InteractiveIdListChecker == null)
            {
                return;
            }

            var interactiveIdList = InteractiveIdListChecker.InteractiveIdSortList;
            var boxId = -1;

            try
            {
                if (Mc.MyPlayer.MyEntityLocal.IsWounded || Mc.MyPlayer.MyEntityLocal.IsDead)
                {
                    return;
                }

                if (interactiveIdList == null || interactiveIdList.Count == 0)
                {
                    return;
                }

                boxId = interactiveIdList[0];

                OnClickButtonByGuide(boxId);
            }
            catch (Exception e)
            {
                logger.Error(e);
            }
        }

        // 帐篷交互
        public static void TentOnClick(int id)
        {
            Mc.MyPlayer.MyEntityServer?.RemoteCallEnterCampingTent(ERpcTarget.Simulator, id);
            ProcessEntity.Instance.RemoteCallLogout(ERpcTarget.World);
            Mc.LoginStep.StartLogoutFlow();
        }

        /// <summary>
        /// 点击返回按钮
        /// </summary>
        public void OnBtnReturnClick()
        {
            needShowNearbyList = true;
        }

        #region CDType

        private void ClearCdTypeDic()
        {
            if (uiInteractiveBtnCdTypeDic == null)
            {
                return;
            }

            foreach (var uiInteractiveBtnCdType in uiInteractiveBtnCdTypeDic)
            {
                uiInteractiveBtnCdType.Value.ReturnPool();
                Pool.Release(uiInteractiveBtnCdType.Value);
            }

            uiInteractiveBtnCdTypeDic.Clear();
        }


        /// <summary>
        /// 交互列表中，cd类型按钮变化
        /// </summary>
        /// <param name="isExist"></param>
        private void OnInteractiveCdBtnChange(bool isExist)
        {
            if (isExist)
            {
                updateListId = Mc.TimerWheel.AddTimerRepeat(100, 100, UpdateBtnCds, "UpdateBtnCds");
            }
            else
            {
                if (updateListId == -1)
                {
                    return;
                }

                Mc.TimerWheel.CancelTimer(updateListId);
                updateListId = -1;
            }
        }


        private void UpdateBtnCds(long timerId, object callbackData, bool needDelete)
        {
            ProfilerApi.BeginSample(EProfileFunc.Timer_UI_InteractiveList_UpdateBtnCds);
            foreach (var (key, _) in uiInteractiveBtnCdTypeDic)
            {
                UpdateBtnCd(key);
            }
            ProfilerApi.EndSample(EProfileFunc.Timer_UI_InteractiveList_UpdateBtnCds);
        }

        private void CDFinishCallback(GButton btn)
        {
            SetBtnType(btn, 0);
        }

        private void UpdateBtnCd(int interactiveId)
        {
            UiInteractiveBtnCdType uiCd;
            switch (interactiveId)
            {
                case EntityInteractiveIdHandle.ZiplineInteractiveId:
                    var interGo = Mc.Go.GetGo(InteractiveIdListChecker.CurEyeEntityId) as InteractionGo;
                    if (interGo != null
                        && uiInteractiveBtnCdTypeDic.TryGetValue(interactiveId, out uiCd)
                        && uiCd != null)
                    {
                        var costTime = (Mc.Time.ServerWorldTime - interGo.InterEntity.LastInterStartTime) / 1000.0f;
                        // 滑索禁用

                        SetBtnType(uiCd.btn, costTime >= interGo.InteractiveCd ? 0 : 1);
                        //uiCd.SetProgress(costTime, interGo.InteractiveCd, CDFinishCallback);
                    }

                    break;
                case (int) EBuildEditInteractiveType.DeployMove:
                    var partGo = Mc.Entity.GetPartEntity(InteractiveIdListChecker.CurEyeEntityId);
                    if (partGo != null
                        && uiInteractiveBtnCdTypeDic.TryGetValue(interactiveId, out uiCd)
                        && uiCd != null)
                    {
                        var partConfig = McCommon.Tables.TbBuildingCore.GetOrDefault(partGo.TemplateId);
                        if (partConfig == null)
                        {
                            break;
                        }

                        var deltaTime = TimeStampUtil.GetNowTimeStampSec() - partGo.LastMovedSec;
                        uiCd.SetProgress(deltaTime, partConfig.MoveCD, CDFinishCallback);
                    }

                    break;
                case PermissionHubComponent.TERRITORY_CENTER_CAPTURING:
                    //todo begin @zjyuan @sfl
                    //var hubGo = Mc.Entity.GetPartEntity(InteractiveIdListChecker.CurEyeEntityId);
                    //if (hubGo != null)
                    //{
                    //    var hub = hubGo.GetComponent<PermissionHubComponent>(EComponentIdEnum.PermissionHub);
                    //    if (hub != null && hub.TerritoryEnt != null && uiInteractiveBtnCdTypeDic.TryGetValue(interactiveId, out uiCd) && uiCd != null)
                    //    {
                    //        var playComp = hub.TerritoryEnt.PlayComp;
                    //        var state = playComp.GetState();
                    //        var costTime = 0;
                    //        var startTime = 0L;
                    //        var currentTime = 0L;
                    //        if (state == TerritoryState.CAPTURING)
                    //        {
                    //            var capturingData = playComp.StateData as TerritoryStateCapturingData;
                    //            costTime = capturingData?.CaptureCostMS ?? 0;
                    //            startTime = capturingData?.StateTimeSinceStartup ?? 0;
                    //            currentTime = McCommon.Time.ServerWorldTime;
                    //        }

                    //        uiCd.SetProgress((currentTime - startTime) / 1000, costTime / 1000, CDFinishCallback);
                    //    }
                    //}
                    //todo end @zjyuan @sfl
                    break;
            }
        }


        /// <summary>
        /// cd类型按钮
        /// </summary>
        public class UiInteractiveBtnCdType
        {
            // 进度条
            private GImage progressImg;

            // 时间文本
            private GTextField timeTextField;

            // cd组件控制器
            private Controller cdCtrl;

            // Btn
            public GButton btn;

            public void Init(GButton cdBtn)
            {
                var cdCom = cdBtn.GetChild("cd").asCom;
                progressImg = cdCom.GetChild("progress").asImage;
                timeTextField = cdCom.GetChild("time").asTextField;
                cdCtrl = cdCom.GetController("state");
                btn = cdBtn;
            }


            /// <summary>
            /// 倒计时进度条
            /// </summary>
            /// <param name="costTime"></param>
            /// <param name="cdTime"></param>
            public void SetProgress(float costTime, float cdTime, Action<GButton> finishCallback = null)
            {
                float progress = 0;
                string str = "00:00";
                int ctrlIndex = 1;
                if (cdTime > 0 && costTime >= 0)
                {
                    if (cdTime > costTime)
                    {
                        ctrlIndex = 0;
                        progress = costTime / cdTime;
                        str = TimeUtil.FormatTimeSmall((long)(cdTime - costTime));
                    }
                    else
                    {
                        progress = 0;
                        ctrlIndex = 1;
                        finishCallback?.Invoke(btn);
                    }
                }
                else
                {
                    progress = 0;
                    ctrlIndex = 1;
                    finishCallback?.Invoke(btn);
                }

                cdCtrl.SetSelectedIndex(ctrlIndex);
                if (progressImg != null)
                {
                    progressImg.fillAmount = 1 - Math.Clamp(progress, 0, 1);
                }
                SafeUtil.SafeSetText(timeTextField, str);
            }


            public void ReturnPool()
            {
                cdCtrl.SetSelectedIndex(1);

                progressImg = null;
                timeTextField = null;
                cdCtrl = null;
            }
        }

        #endregion
    }
}
