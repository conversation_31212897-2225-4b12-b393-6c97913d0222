﻿using Cysharp.Text;
using System.Collections.Generic;
using System.Diagnostics;
using WizardGames.Soc.Common.Algorithm;
using WizardGames.Soc.Common.Const;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Extension;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.Framework.Aoi;

namespace WizardGames.Soc.Common.Component
{
    [HotfixClass]
    public static partial class TerritoryBaseComponentHotfix
    {
        static internal ConstHashSet<CreatePartReason> ignoreCreateLog = new ConstHashSet<CreatePartReason>(
                                                            CreatePartReason.RecoverPart,
                                                            CreatePartReason.CreateFromDeadSheep,
                                                            CreatePartReason.CreateFromGMLoadPartGroup,
                                                            CreatePartReason.CreateFromBoomHomeLoadPartGroup,
                                                            CreatePartReason.CreateFromConstructionBlueprint,
                                                            CreatePartReason.CreateByTask,
                                                            CreatePartReason.CreateByGraphNode);
        static TerritoryBaseComponentHotfix()
        {
            PropertyStaticCallback.SubscribePropertyChange<int>(ConstructionCoreComponent.CLASS_HASH, ConstructionCoreComponent.PropertyIds.GRADE, OnPartGradeChange);
        }
        [Hotfix]
        public static void Init(this TerritoryBaseComponent self)
        {
            self.LocList = new();
            self.NormalPartSet = new();
            self.DebrisEntityIds = new();
            self.PartsDestroyWithToolcupBoard = new();
        }

        [Hotfix]
        public static void PostInit(this TerritoryBaseComponent self, bool isLoadFromDb)
        {
            self.PartLimitInfo = new();
            self.PostInitDecay();
        }
        [Hotfix]
        public static void Cleanup(this TerritoryBaseComponent self)
        {
            self.SafeCancelTimer(ref self.decayTimer);
        }

        public static void AddStorageDebrisEntity(this TerritoryBaseComponent self, long entityId)
        {
            self.DebrisEntityIds.Add(entityId);
            self.Logger.Info($"add deris entity suc {entityId}");
        }

        public static void RemoveStorageDerisEntity(this TerritoryBaseComponent self, StorageDebrisEntity debrisEntity)
        {
            self.DebrisEntityIds.Remove(debrisEntity.EntityId);
            self.Logger.Info($"remove deris entity suc {debrisEntity.EntityId}");
            if (debrisEntity.removeReason != EDebrisRemoveReason.Repair)
                self.TryAutoRemove();
        }
        public static void RemoveAllStorageDerisEntity(this TerritoryBaseComponent self, bool isEntityRemove)
        {
            self.Logger.Info($"RemoveAllStorageDerisEntity isEntityRemove {isEntityRemove} {self.DebrisEntityIds.Count}");
            foreach (var entityId in self.DebrisEntityIds)
            {
                EntityManager.Instance.RemoveEntity(entityId);
            }
            if (!isEntityRemove)
                self.TryAutoRemove();
        }

        public static bool CheckCanCreate(this TerritoryBaseComponent self, int createCount)
        {
            if (self.NormalPartCount + createCount > ConstructionConst.MAX_TERRITORY_PART_COUNT)
            {
                return false;
            }
            return true;
        }

        public static void GetNonCorePartEntity(this TerritoryBaseComponent self, long templateId, List<PartEntity> result)
        {
            foreach (var (partEntityId, partType) in self.NoneCorePartDict)
            {
                if (partType == templateId)
                {
                    var partEntity = EntityManager.Instance.GetEntity<PartEntity>(partEntityId);
                    if (partEntity == null) self.Logger.Error($"PartEntity {partEntityId} inside TerritoryCenter {self.ParentId} is null");
                    else result.Add(partEntity);
                }
            }
        }

        public static PartEntity FindOneNonCorePartEntity(this TerritoryBaseComponent self, long templateId)
        {
            foreach (var (id, partType) in self.NoneCorePartDict)
            {
                if (partType == templateId)
                {
                    var partEntity = EntityManager.Instance.GetEntity<PartEntity>(id);
                    if (partEntity == null) self.Logger.Error($"PartEntity {id} inside TerritoryCenter {self.ParentId} is null");
                    else return partEntity;
                }
            }
            return null;
        }

        public static void OnCreateTerritory(this TerritoryBaseComponent self, PartEntity partEntity, CreatePartReason reason, List<PartEntity> managedParts)
        {
            self.OnPartCreate(partEntity, reason);
            self.AddManagedPartList(managedParts, true, self.CreatorRoleId);
        }

        #region 建筑数据统计
        // 摆件超上限皮肤重置
        public static void AddManagedPartList(this TerritoryBaseComponent self, List<PartEntity> managedParts, bool fromWild, ulong opeRoleId)
        {
            if (managedParts == null || managedParts.Count == 0)
            {
                return;
            }
            var preTotalCount = self.NormalPartCount;
            var selfEnt = self.TerrEnt;
            var selfSkinComp = selfEnt.SkinComp;
            int partCount = 0;

            Dictionary<long, PartEntity> deployWithNewSkin = new(64);
            List<long> gapEntityIds = new(32);
            Dictionary<long, List<PartEntity>> templateDict = new(32);
            Dictionary<ulong, List<PartEntity>> playerWildPartDict = new(4);

            // 核心建筑未拥有皮肤重置
            BasicTypeList<long> resetSkinParts = new();

            foreach (var partEntity in managedParts)
            {
                if (self.NormalPartSet.Contains(partEntity.EntityId))
                {
                    self.Logger.Error($"AddManagedPartList repeat record. partId {partEntity.EntityId}, terr {self.ParentId}");
                    continue;
                }
                if (fromWild && partEntity.TerritoryId != 0)
                {
                    self.Logger.Error($"AddManagedPartList wild part is in terr. {partEntity.EntityId} {partEntity.TerritoryId}");
                    continue;
                }
                if (fromWild)
                {
                    var wildEnt = TerritoryManagerEntity.Instance.GetOrCreateWildByPart(partEntity);
                    wildEnt.OutsideComp.OnPartRemove(partEntity);
                    if (partEntity.OwnerId != 0)
                    {
                        var playerParts = playerWildPartDict.GetValueOrCreate((ulong)partEntity.OwnerId);
                        playerParts.Add(partEntity);
                    }
                }
                partCount++;

                self.AddNormalPartRecordInner(partEntity);

                if (self.CheckGapEntity(partEntity))
                {
                    gapEntityIds.Add(partEntity.EntityId);
                }
                if (selfSkinComp != null)
                {
                    if (partEntity.CoreComponent == null)
                    {
                        var skinId = partEntity.SkinId;
                        if (partEntity.SkinId > 0)
                        {
                            if (selfSkinComp.AllDeploySkinIds.Contains(skinId))
                            {
                                // 出现过的皮肤，不会影响皮肤种类上限，可以直接添加
                                selfSkinComp.AddSkinCount(partEntity.TemplateId, skinId);
                            }
                            else
                            {
                                // 出现新的皮肤，可能会超过上限，要延迟处理
                                deployWithNewSkin.Add(partEntity.EntityId, partEntity);
                            }
                        }
                    }
                    else
                    {
                        if (!selfEnt.CheckPartSkinMatch(partEntity))
                        {
                            resetSkinParts.Add(partEntity.EntityId);
                        }
                    }
                }

                var tmpList = templateDict.GetValueOrCreate(partEntity.TemplateId);
                tmpList.Add(partEntity);
            }

            self.AddGridPartCount(partCount);

            self.OnManagedPartChanged();

            List<PartEntity> overLimitList = null;
            var creatorPlayer = UserManagerEntity.Instance.GetPlayerEntity(self.CreatorRoleId);
            if (creatorPlayer != null && !creatorPlayer.ConstructionComp.IsPartLimitClosed())
            {
                self.PartLimitInfo.AddWildOrTerritoryPartCount(templateDict, false, out overLimitList);
            }

            foreach (var (roleId, playerParts) in playerWildPartDict)
            {
                var playerEnt = UserManagerEntity.Instance.GetPlayerEntity(roleId);
                playerEnt.ConstructionComp.RemoveWildParts(playerParts);
            }
            // 数量超上限的建筑销毁
            if (overLimitList != null && overLimitList.Count > 0)
            {
                Dictionary<long, long> overLimitInfo = new(overLimitList.Count);
                foreach (var partEntity in overLimitList)
                {
                    overLimitInfo.Add(partEntity.EntityId, partEntity.TemplateId);
                    deployWithNewSkin.Remove(partEntity.EntityId);
                }
                self.SetOverLimitParts(overLimitInfo);
            }
            // 通知检查美缝数据
            if (gapEntityIds.Count > 0)
            {
                selfEnt.RemoteCallCheckGapEntityCount(ERpcTarget.Simulator, gapEntityIds);
            }
            if (selfSkinComp != null)
            {
                // 重置核心建筑皮肤
                if (resetSkinParts.Count > 0)
                {
                    selfSkinComp.ResetPartListSkin(resetSkinParts);
                }
                // 摆件皮肤数量超上限处理
                if (deployWithNewSkin.Count > 0)
                {
                    selfSkinComp.AddDeploySkins(deployWithNewSkin, opeRoleId);
                }
            }
            self.Logger.Info($"AddManagedPartList preTotalCount {preTotalCount}, addCount {managedParts.Count}, nowTotalCount {self.NormalPartCount}");
        }


        public static void OnPartCreate(this TerritoryBaseComponent self, PartEntity partEntity, CreatePartReason reason)
        {
            if (!self.CheckCanCreate(1))
            {
                self.Logger.Warn($"OnPartCreate record full. terr: {self.ParentId}, count: {self.NormalPartSet.Count}");
            }
            if (self.NormalPartSet.Contains(partEntity.EntityId))
            {
                self.Logger.Warn($"ignore on load part entity {self.ParentId} {partEntity.EntityId}");
                return;
            }
            self.AddNormalPartRecord(partEntity);
            if (!ignoreCreateLog.Contains(reason))
            {
                self.TerrEnt.TLogPartCreate(partEntity);
            }
            self.Logger.Info($"OnPartCreate. x: {partEntity.PosX}, y: {partEntity.PosY} z: {partEntity.PosZ}, terr: {self.ParentId}, part: {partEntity.EntityId}, count: {self.NormalPartSet.Count}");
        }

        public static void OnPartDelete(this TerritoryBaseComponent self, PartEntity partEntity, EDestroyPartReason reason)
        {
            self.Logger.Info($"OnPartDelete part: {partEntity.EntityId}, terr: {self.ParentId}");
            self.TerrEnt.DeadSheepComp?.ResetAutoDestroyTimer();
            self.RemoveNormalPartRecord(partEntity, false, reason);
        }

        public static void RecheckManagedParts(this TerritoryBaseComponent self)
        {
            self.Logger.Info($"RecheckManagedParts {self.NormalPartSet.Count}");
            self.AddGridPartCount(-self.NormalPartSet.Count);

            Dictionary<long, List<PartEntity>> terrPartDict = new();
            Dictionary<ulong, List<PartEntity>> playerWildPartDict = new();
            foreach (var partId in self.NormalPartSet)
            {
                var partEntity = EntityManager.Instance.GetEntity(partId) as PartEntity;
                if (partEntity == null)
                {
                    //可能还没来得及删除记录
                    self.Logger.Debug($"RecheckManagedParts record part not found. part: {partId}");
                    continue;
                }
                partEntity.TerritoryId = 0;
                var terrEnt = TerritoryManagerEntity.Instance.GetInChargeTerritoryEntity(new Vector3(partEntity.PosX, partEntity.PosY, partEntity.PosZ));
                if (terrEnt == null)
                {
                    var wildEnt = TerritoryManagerEntity.Instance.GetOrCreateWildByPart(partEntity);
                    wildEnt.OutsideComp.OnPartAdd(partEntity);
                    if (partEntity.OwnerId != 0)
                    {
                        var wildPartList = playerWildPartDict.GetValueOrCreate((ulong)partEntity.OwnerId);
                        wildPartList.Add(partEntity);
                    }
                }
                else
                {
                    var terrPartList = terrPartDict.GetValueOrCreate(terrEnt.EntityId);
                    terrPartList.Add(partEntity);
                }
            }
            self.NormalPartSet.Clear();

            foreach (var (terrId, terrPartList) in terrPartDict)
            {
                var terrEnt = TerritoryManagerEntity.Instance.GetTerritoryEntity(terrId);
                var baseComp = terrEnt.BaseComp;
                baseComp.AddManagedPartList(terrPartList, false, baseComp.CreatorRoleId);
            }

            foreach (var (roleId, wildPartList) in playerWildPartDict)
            {
                var player = UserManagerEntity.Instance.GetPlayerEntity(roleId);
                if (player == null)
                {
                    self.Logger.Error($"RecheckManagedParts roleId {roleId} player not found");
                    continue;
                }
                player.ConstructionComp.AddWildParts(wildPartList);
            }
        }

        private static void SetOverLimitParts(this TerritoryBaseComponent self, Dictionary<long, long> overLimitParts)
        {
            TerritoryOverLimitRecord record = null;
            var player = UserManagerEntity.Instance.GetPlayerEntity(self.CreatorRoleId);
            if (player != null)
            {
                record = new TerritoryOverLimitRecord();
                record.RoleId = self.CreatorRoleId;
                record.GiveBackItems = new();
                player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, CommonTipConst.PartCountExceed);
            }
            else if (self.CreatorRoleId != 0)
            {
                self.Logger.Error($"SetOverLimitParts player not found {self.CreatorRoleId}");
            }
            self.Logger.Info($"SetOverLimitParts role {self.CreatorRoleId}, count {overLimitParts.Count}, overLimitParts {overLimitParts.Print()}");
            ConstructionManagerEntity.Instance.EnqueueRemovePartList(overLimitParts.Keys, record, EDestroyPartReason.TerritoryOverLimit);
        }

        private static void AddNormalPartRecord(this TerritoryBaseComponent self, PartEntity partEntity)
        {
            self.AddNormalPartRecordInner(partEntity);
            self.AddGridPartCount(1);
            self.OnManagedPartChanged();
            var creatorPlayer = UserManagerEntity.Instance.GetPlayerEntity(self.CreatorRoleId);
            if (creatorPlayer != null)
            {
                int overLimitCount = 0;
                if (!creatorPlayer.ConstructionComp.IsPartLimitClosed())
                {
                    self.PartLimitInfo.ModifyWildOrTerritoryPartCount(partEntity.TemplateId, 1, false, out overLimitCount);
                }

                if (overLimitCount > 0)
                {
                    self.Logger.Info($"AddNormalPartRecord overlimit {partEntity.EntityId} {partEntity.TemplateId}");
                    partEntity.BaseComponent.StartAutoDeductHp();
                }
                if (partEntity.CoreComponent == null)
                {
                    var ret = self.TerrEnt.SkinComp.CheckAddPartWithSkin(partEntity.TemplateId, partEntity.SkinId);
                    if (ret != SkinCountLimitType.NO_LIMIT)
                    {
                        partEntity.BaseComponent.SkinId = 0;
                    }
                    else
                    {
                        self.TerrEnt.SkinComp.AddSkinCount(partEntity.TemplateId, partEntity.SkinId);
                    }
                }
            }
        }

        private static void AddNormalPartRecordInner(this TerritoryBaseComponent self, PartEntity partEntity)
        {
            var selfEnt = self.TerrEnt;
            partEntity.TerritoryId = self.ParentId;
            self.NormalPartSet.Add(partEntity.EntityId);
            self.NormalPartCount = self.NormalPartSet.Count;
            if (partEntity.TryGetComponent<DestroyWithToolcupBoardComponent>(EComponentIdEnum.DestroyWithToolcupBoard, out var comp))
            {
                self.PartsDestroyWithToolcupBoard.Add(partEntity.EntityId);
            }
            selfEnt.BatchRecover?.RecordAddedPart(partEntity);
            selfEnt.GetComponent<TerritoryOutsideComponent>(EComponentIdEnum.OutsideDataSet)?.OnPartAdd(partEntity);
            self.AddExtraSet(partEntity);

            var playerEntity = UserManagerEntity.Instance.GetPlayerEntity(self.CreatorRoleId);
            if (playerEntity != null && self.NormalPartCount > playerEntity.CombatStat.HistoryMaxTerritoryPartCount)
            {
                playerEntity.CombatStat.HistoryMaxTerritoryPartCount = self.NormalPartCount;
            }
        }

        private static void RemoveNormalPartRecord(this TerritoryBaseComponent self, PartEntity partEntity, bool isMoveout, EDestroyPartReason reason)
        {
            var selfEnt = self.TerrEnt;
            if (selfEnt.PlayComp.State != ETerritoryState.Deserted)
            {
                selfEnt.DeadSheepComp?.CheckMissionProgress(partEntity.EntityId);
                selfEnt.BatchRecover?.RecordRemovedPart(partEntity, isMoveout, reason);
                if (partEntity.CoreComponent == null)
                {
                    selfEnt.SkinComp?.RemoveSkinCount(partEntity.TemplateId, partEntity.SkinId);
                }
            }
            var ret = self.NormalPartSet.Remove(partEntity.EntityId);
            if (!ret)
            {
                self.Logger.Warn($"RemoveNormalPartRecord entity record not exist. terr: {self.ParentId}, part: {partEntity.EntityId}");
            }
            self.NormalPartCount = self.NormalPartSet.Count;

            selfEnt.GetComponent<TerritoryOutsideComponent>(EComponentIdEnum.OutsideDataSet)?.OnPartRemove(partEntity.EntityId);
            self.RemoveExtraSet(partEntity);
            partEntity.TerritoryId = 0;
            self.AddGridPartCount(-1);
            self.OnManagedPartChanged();
            var creatorPlayer = UserManagerEntity.Instance.GetPlayerEntity(self.CreatorRoleId);
            if (creatorPlayer != null && !creatorPlayer.ConstructionComp.IsPartLimitClosed())
                self.PartLimitInfo.ModifyWildOrTerritoryPartCount(partEntity.TemplateId, -1, false, out var _);
            self.TryAutoRemove();
        }

        private static void TryAutoRemove(this TerritoryBaseComponent self)
        {
            if (self.NormalPartCount == 0 && self.DebrisEntityIds.Count == 0)
            {
                self.Logger.Info($"TryAutoRemove suc, wait remove");
                self.DelayExecute(() =>
                {
                    self.Logger.Info($"TryAutoRemove suc, remove now");
                    EntityManager.Instance.RemoveEntity(self.ParentId);
                });
            }
        }

        private static void AddGridPartCount(this TerritoryBaseComponent self, int addCount)
        {
            var gridEntity = EntityManager.Instance.GetEntity(self.GridId) as GridEntity;
            gridEntity.TerritoryPartCount += addCount;
            self.Logger.Debug($"AddGridPartCount terr {self.ParentId} grid {self.GridId} {addCount} {gridEntity.TerritoryPartCount}");
        }

        private static void OnManagedPartChanged(this TerritoryBaseComponent self)
        {
            var terrPartEnt = EntityManager.Instance.GetEntity(self.PartEntityId) as PartEntity;
            if (terrPartEnt == null)
                return;
            var comp = terrPartEnt.GetComponent<TerritoryBatchUpgradeComponent>(EComponentIdEnum.TerritroyBatchUpgrade);
            if (comp == null)
                return;
            comp.OnPartChanged();
        }
        #endregion

        #region 额外的建筑信息，摆件、腐蚀模块等

        public static void InitExtraSet(this TerritoryBaseComponent self)
        {
            Dictionary<long, List<PartEntity>> templateCount = new();
            var terrId = self.ParentId;
            foreach (var partId in self.NormalPartSet)
            {
                var partEntity = EntityManager.Instance.GetEntity(partId) as PartEntity;
                if (partEntity == null)
                {
                    self.Logger.Error($"InitExtraSet {partId} not found in terr {terrId}");
                    continue;
                }
                if (partEntity.TerritoryId != terrId)
                {
                    self.Logger.Error($"InitExtraSet {partId} terrId not match. {partEntity.TerritoryId} {terrId}");
                }
                self.AddExtraSet(partEntity);
                if (partEntity.CoreComponent == null)
                    self.TerrEnt.SkinComp?.AddSkinCount(partEntity.TemplateId, partEntity.SkinId);
                if (!templateCount.TryGetValue(partEntity.TemplateId, out var partList))
                {
                    partList = new();
                    templateCount[partEntity.TemplateId] = partList;
                }
                partList.Add(partEntity);
            }
            if (self.CreatorRoleId != 0)
            {
                self.PartLimitInfo.AddWildOrTerritoryPartCount(templateCount, false, out var _);
            }
            if (self.GridId != 0)
            {
                var gridEntity = EntityManager.Instance.GetEntity(self.GridId) as GridEntity;
                gridEntity.TerritoryPartCount += self.NormalPartSet.Count;
                self.Logger.Debug($"InitExtraSet terr {self.ParentId} grid {self.GridId} {gridEntity.TerritoryPartCount}");
            }
            self.Logger.Info($"InitExtraSet terr: {self.ParentId} core count: {self.CorePartSet.Count}, none core count: {self.NoneCorePartDict.Count}, decay count: {self.DecayPartSet.Count}");
        }

        private static void AddExtraSet(this TerritoryBaseComponent self, PartEntity partEntity)
        {
            if (ConstructionManagerEntity.Instance.IsCoreConstruction(partEntity.TemplateId))
            {
                self.CorePartSet.Add(partEntity.EntityId);
                self.Logger.Debug($"AddExtraSet terr: {self.ParentId}, part: {partEntity.EntityId} CorePartSet count: {self.CorePartSet.Count}");
            }
            else
            {
                self.NoneCorePartDict.Add(partEntity.EntityId, partEntity.TemplateId);
                self.Logger.Debug($"AddExtraSet terr: {self.ParentId}, part: {partEntity.EntityId} NoneCorePartSet count: {self.NoneCorePartDict.Count}");
            }
            if (IsFoundation(partEntity.TemplateId))
                self.FoundationCount++;

            var partGo = partEntity.BaseComponent;
            if (partGo != null)
            {
                if (partEntity.GetComponent(EComponentIdEnum.Decay) is DecayComponent decayComponent)
                {
                    self.DecayPartSet.Add(partEntity.EntityId);
                    self.AddToDecayUpkeepDic(decayComponent);
                    self.Logger.Debug($"AddExtraSet terr: {self.ParentId}, part: {partEntity.EntityId} DecayPartSet count: {self.DecayPartSet.Count}");
                }
            }
            else
            {
                self.Logger.Error($"AddExtraSet {partEntity.EntityId} obj not found.");
            }
            self.TerrEnt.PlayComp.OnUpdateMaxWorkBenchLevel(partEntity.TemplateId);
        }

        private static void RemoveExtraSet(this TerritoryBaseComponent self, PartEntity partEntity)
        {
            self.CorePartSet.Remove(partEntity.EntityId);
            self.NoneCorePartDict.Remove(partEntity.EntityId);
            if (self.DecayPartSet.Remove(partEntity.EntityId))
            {
                self.RemoveFromDecayUpkeepDic(partEntity);
            }
            if (IsFoundation(partEntity.TemplateId))
                self.FoundationCount--;
            if (self.FoundationCount < 0)
            {
                self.Logger.Error($"RemoveExtraSet FoundationCount {self.FoundationCount} too less");
                self.FoundationCount = 0;
            }
            self.TerrEnt.PlayComp.TryRefreshMaxWorkbenchLevel(partEntity.TemplateId);
        }
        private static bool IsFoundation(long templateId)
        {
            return templateId == (long)PartType.Foundation || templateId == (long)PartType.FoundationTriangle;
        }
        public static void ChangeTerritoryCenterDoorsAutoOpen(this TerritoryBaseComponent self, bool isAutoOpen)
        {
            foreach (var (id, partType) in self.NoneCorePartDict)
            {
                var partEntity = EntityManager.Instance.GetEntity<PartEntity>(id);
                if (partEntity != null)
                {
                    var doorComp = partEntity.GetComponent<ConstructionDoorComponent>(EComponentIdEnum.ConstructionDoor);
                    if (doorComp != null)
                    {
                        doorComp.SetAutoOpenDoorInternal(isAutoOpen);
                        self.Logger.InfoFormat(">>>ChangeTerritoryCenterDoorsAutoOpen door {0} change state to {1}", id, isAutoOpen);
                    }
                }
            }
        }

        /// <summary>
        /// 设置领地中所有门的自动开关状态需要检测权限 (开/关)
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="isAutoOpen"></param>
        public static void ChangeTerritoryCenterDoorsAutoOpenWithCheckPermission(this TerritoryBaseComponent self, ulong roleId, bool isAutoOpen)
        {
            foreach (var (id, partType) in self.NoneCorePartDict)
            {
                var partEntity = EntityManager.Instance.GetEntity<PartEntity>(id);
                if (partEntity != null)
                {
                    var doorComp = partEntity.GetComponent<ConstructionDoorComponent>(EComponentIdEnum.ConstructionDoor);
                    if (doorComp != null)
                    {
                        doorComp.SetAutoOpenDoorWithCheckPermission(roleId, isAutoOpen);
                        self.Logger.InfoFormat(">>>ChangeTerritoryCenterDoorsAutoOpenWithCheckPermission door {0} change state to {1}", id, isAutoOpen);
                    }
                }
            }
        }

        /// <summary>
        /// 设置领地中所有门的自动开关状态需要检测权限 (自动开关 / 自动关)
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="autoOpenState"></param>
        public static void ChangeTerritoryCenterDoorsAutoOpenOnlyCloseStateWithCheckPermission(this TerritoryBaseComponent self, ulong roleId, int autoOpenState)
        {
            foreach (var (id, partType) in self.NoneCorePartDict)
            {
                var partEntity = EntityManager.Instance.GetEntity<PartEntity>(id);
                if (partEntity != null)
                {
                    var doorComp = partEntity.GetComponent<ConstructionDoorComponent>(EComponentIdEnum.ConstructionDoor);
                    if (doorComp != null)
                    {
                        doorComp.SetAutoOpenDoorStateWithCheckPermission(roleId, autoOpenState);
                        self.Logger.InfoFormat(">>>ChangeTerritoryCenterDoorsAutoOpenOnlyCloseStateWithCheckPermission door {0} change state to {1}", id, autoOpenState);
                    }
                }
            }
        }
        

        #endregion


        #region 建筑数量管控

        public static bool CheckGridCanCreate(this TerritoryBaseComponent self, int addCount)
        {
            var gridEntity = EntityManager.Instance.GetEntity(self.GridId) as GridEntity;
            if (gridEntity == null)
            {
                self.Logger.Error($"CheckGridPartCountLimit grid not found. terr: {self.ParentId}, grid: {self.GridId}");
                return false;
            }
            return gridEntity.CheckCanCreate(addCount);
        }
        #endregion

        #region 移动摆件
        public static void MoveTerritoryEntity(this TerritoryBaseComponent self, PartEntity partEntity, List<PartEntity> newManagedParts, ulong opeRoleId)
        {
            var selfEnt = self.TerrEnt;
            self.Logger.Info($"MoveTerritoryEntity from {self.ParentId} {self.PartEntityId}");

            var oldLoc = new TerritoryCenterLocationInfo();
            oldLoc.PosX = selfEnt.PosX;
            oldLoc.PosY = selfEnt.PosY;
            oldLoc.PosZ = selfEnt.PosZ;
            oldLoc.Ts = selfEnt.LocTs;

            selfEnt.PosX = partEntity.PosX;
            selfEnt.PosY = partEntity.PosY;
            selfEnt.PosZ = partEntity.PosZ;
            selfEnt.LocTs = ProcessEntity.Instance.NowTs;

            var grid = LogicGridManager.Instance.GetGridByPos(partEntity.PosX, partEntity.PosZ);
            if (grid == null)
            {
                self.Logger.Error($"MoveTerritoryEntity terr {self.ParentId}, part {partEntity.EntityId} new grid not found. {partEntity.PosX} {partEntity.PosZ}");
                return;
            }
            // 处理grid上的数据
            var gridEntityNew = grid.GetGridEntity();
            var gridEntityOld = EntityManager.Instance.GetEntity(self.GridId) as GridEntity;
            if (gridEntityOld.EntityId != gridEntityNew.EntityId)
            {
                self.AddGridPartCount(-1);
                self.GridId = gridEntityNew.EntityId;
                self.AddGridPartCount(1);
            }

            self.AddManagedPartList(newManagedParts, true, opeRoleId);

            selfEnt.TLogToopCupboardMoved(newManagedParts);
            self.OnLocationChange(oldLoc);

            self.Logger.Info($"MoveTerritoryEntity terr: {self.ParentId} moved suc. from grid {gridEntityOld.EntityId} to grid {gridEntityNew.EntityId}, count: {self.NormalPartSet.Count}");
        }

        private static bool CheckGapEntity(this TerritoryBaseComponent self, PartEntity partEntity)
        {
            // 有美缝的建筑，移入到领地内
            var gapComp = partEntity.GapComponent;
            if (gapComp == null)
                return false;
            if (gapComp.TargetEntityIdLeft > 0 || gapComp.TargetEntityIdRight > 0)
            {
                return true;
            }
            return false;
        }

        private static void OnLocationChange(this TerritoryBaseComponent self, TerritoryCenterLocationInfo oldLoc)
        {
            var selfEnt = self.TerrEnt;
            var selfBase = self;
            var selfPlay = selfEnt.PlayComp;

            self.LocList.Add(oldLoc);
            if (selfBase.LocList.Count > ConstructionConst.MAX_HISTORY_POS_COUNT && ConstructionConst.MAX_HISTORY_POS_COUNT > 0)
            {
                selfBase.LocList.RemoveAt(0);
                self.Logger.WarnFormat("OnLocationChange LocList too long. {0} {1}", selfEnt.EntityId, selfBase.LocList.Count);
            }

            TerritoryManagerEntity.Instance.RemoveTerritory(self.ParentId);
            TerritoryManagerEntity.Instance.AddTerritory(self.ParentId, new Vector3(selfEnt.PosX, selfEnt.PosY, selfEnt.PosZ));

            if (selfBase.CreatorRoleId <= 0)
                return;
            foreach (var roleId in selfPlay.GetShowBriefInfoPrivilegeRoleId())
            {
                var playerEnt = UserManagerEntity.Instance.GetPlayerEntity(roleId);
                if (playerEnt == null)
                {
                    self.Logger.Error($"{selfEnt.EntityId} 通知玩家更新领地位置，但是玩家找不到了... {roleId}");
                    continue;
                }
                playerEnt.ConstructionComp.UpdateToolCupboardPosition(selfEnt.EntityId, selfEnt.PosX, selfEnt.PosY, selfEnt.PosZ);
            }
        }

        //非领地柜建筑移动
        public static void OnPartMoved(TerritoryEntity newTerrEnt, PartEntity partEntity, Vector3 oldPos, Vector3 oldRot)
        {
            if (partEntity.TemplateId == (long)PartType.ToolCupboard)
            {
                partEntity.Logger.Error($"OnPartMoved {partEntity.EntityId} {partEntity.TemplateId} cannot do this.");
                return;
            }
            var player = UserManagerEntity.Instance.GetPlayerEntity((ulong)partEntity.OwnerId);
            var newTerrId = newTerrEnt?.EntityId ?? 0;
            var oldTerrId = partEntity.TerritoryId;
            var oldTerrEnt = TerritoryManagerEntity.Instance.GetTerritoryEntity(partEntity.TerritoryId);
            if (partEntity.TerritoryId != 0 && oldTerrEnt == null)
            {
                partEntity.Logger.Error($"OnPartMoved old terr {partEntity.EntityId} {partEntity.TerritoryId} not found.");
            }

            if (oldTerrEnt == null)
            {
                if (newTerrEnt != null)
                {
                    // 野外移到领地内
                    newTerrEnt.BaseComp.AddNormalPartRecord(partEntity);
                    player.ConstructionComp.RemoveWildPart(partEntity);
                }
            }
            else if (oldTerrEnt != null)
            {
                if (newTerrEnt != null)
                {
                    if (oldTerrEnt == newTerrEnt)
                    {
                        // 领地内移动
                        var outsideComp = oldTerrEnt.GetComponent<TerritoryOutsideComponent>(EComponentIdEnum.OutsideDataSet);
                        outsideComp.OnPartMove(partEntity.EntityId);
                    }
                    else
                    {
                        // 换新领地
                        oldTerrEnt.BaseComp.RemoveNormalPartRecord(partEntity, true, EDestroyPartReason.Normal);
                        newTerrEnt.BaseComp.AddNormalPartRecord(partEntity);
                    }
                }
                else
                {
                    // 移到野外
                    oldTerrEnt.BaseComp.RemoveNormalPartRecord(partEntity, true, EDestroyPartReason.Normal);
                    player.ConstructionComp.AddWildPart(partEntity);
                }
            }
            partEntity.Logger.Info($"OnPartMoved from old terr {oldTerrId} to new terr {newTerrId}");
        }
        #endregion

        #region 腐蚀
        public static void PostInitDecay(this TerritoryBaseComponent self)
        {
            self.SetupDecayUpkeepDic();
            if (self.decayTimer <= 0)
            {
                self.decayUnit.Setup(self.ParentId);
                self.decayTimer = self.AddTimerRepeat((uint)RandomUtil.Instance.Next(100, self.decayUnit.batchInterval), (uint)self.decayUnit.batchInterval, UpdateDecayUnitHotfix);
            }
        }
        [Hotfix(TimerCallback = true)]
        private static void UpdateDecayUnit(this TerritoryBaseComponent self, long _)
        {
            self.decayUnit.Update();
        }

        public static void SetupDecayUpkeepDic(this TerritoryBaseComponent self, bool reset = false)
        {
            if (!reset && self.fullDecayUpkeepDic != null) { return; }

            //thisTimeDecayUpkeepDic = new();
            self.fullDecayUpkeepDic = new();

            foreach (var partId in self.DecayPartSet)
            {
                var decayPart = EntityManager.Instance.GetEntity(partId) as PartEntity;
                var decayComp = decayPart.GetComponent(EComponentIdEnum.Decay) as DecayComponent;
                if (decayComp == null)
                {
                    self.Logger.Error($"part without decayModule of part {partId} in terr {self.ParentId} decaySet");
                    continue;
                }
                self.AddToDecayUpkeepDic(decayComp, false);
            }
            self.ReportNowVal("Setup");
        }
        [Conditional("ljzFuncTest")]
        private static void ReportNowVal(this TerritoryBaseComponent self, string str = "")
        {
            var zsb = ZString.CreateStringBuilder();
            zsb.Append($"[Decay] {str} DecayUpkeepDic: ");
            foreach (var (key, value) in self.fullDecayUpkeepDic)
            {
                zsb.Append($"{key} - {value} ");
            }
            self.Logger.Error(zsb.ToString());
        }

        private static void AddToDecayUpkeepDic(this TerritoryBaseComponent self, DecayComponent comp, bool report = true)
        {
            if (comp == null || self.fullDecayUpkeepDic == null) { return; }
            //TODO: 状态切换时，需要计算差量
            //if (!comp.ShouldDecay()) { continue; }
            TerritoryBaseComponent.tempDic.Clear();
            comp.CalculateUpkeepCostAmounts(TerritoryBaseComponent.tempDic, 1);
            //MergeDecayItem(tempDic, thisTimeDecayUpkeepDic);
            self.MergeDecayItem(TerritoryBaseComponent.tempDic, self.fullDecayUpkeepDic);
            TerritoryBaseComponent.tempDic.Clear();
            self.decayUnit.TryAddDecayUnit(comp);
            if (report) { self.ReportNowVal("Add"); }
        }

        private static void RemoveFromDecayUpkeepDic(this TerritoryBaseComponent self, PartEntity entity)
        {
            if (self.fullDecayUpkeepDic == null) { return; }
            var comp = entity?.GetComponent<DecayComponent>(EComponentIdEnum.Decay);
            if (comp == null) { return; }
            //TODO: 状态切换时，需要计算差量
            //if (!comp.ShouldDecay()) { continue; }
            TerritoryBaseComponent.tempDic.Clear();
            comp.CalculateUpkeepCostAmounts(TerritoryBaseComponent.tempDic, 1);
            self.SplitDecayItem(TerritoryBaseComponent.tempDic, self.fullDecayUpkeepDic);
            TerritoryBaseComponent.tempDic.Clear();
            ConstructionManagerEntity.Instance.decayUnit.TryAddDecayUnit(comp);
            self.ReportNowVal("Remove");
        }

        private static void OnPartGradeChange(this TerritoryBaseComponent self, PartEntity partEntity, int newGrade, int oldGrade)
        {
            var decayComp = partEntity.GetComponent<DecayComponent>(EComponentIdEnum.Decay);
            if (decayComp == null) { return; }
            TerritoryBaseComponent.tempDic.Clear();
            DecayUtil.CalculateUpkeepCostAmounts(partEntity.TemplateId, oldGrade, TerritoryBaseComponent.tempDic, 1);
            self.MergeDecayItem(TerritoryBaseComponent.tempDic, self.fullDecayUpkeepDic);
            TerritoryBaseComponent.tempDic.Clear();
            DecayUtil.CalculateUpkeepCostAmounts(partEntity.TemplateId, newGrade, TerritoryBaseComponent.tempDic, 1);
            self.SplitDecayItem(TerritoryBaseComponent.tempDic, self.fullDecayUpkeepDic);
            TerritoryBaseComponent.tempDic.Clear();
            self.ReportNowVal("Upgrade");
        }

        private static void MergeDecayItem(this TerritoryBaseComponent self, Dictionary<long, float> mergedDic, Dictionary<long, float> mergeToDic)
        {
            foreach (var (key, value) in mergedDic)
            {
                if (mergeToDic.ContainsKey(key))
                {
                    mergeToDic[key] += value;
                }
                else
                {
                    mergeToDic[key] = value;
                }
            }
        }

        private static void SplitDecayItem(this TerritoryBaseComponent self, Dictionary<long, float> mergedDic, Dictionary<long, float> mergeToDic)
        {
            foreach (var (key, value) in mergedDic)
            {
                if (mergeToDic.ContainsKey(key))
                {
                    mergeToDic[key] -= value;
                    if (mergeToDic[key] < -1e-5f)
                    {
                        self.Logger.Error($"[Decay] Minus Result is lower than -1e-5. id: {key}");
                    }
                    else if (mergeToDic[key] <= 1e-5f)
                    {
                        mergeToDic.Remove(key);
                    }
                }
                else
                {
                    self.Logger.Error($"[Decay] Minus item but not have. id: {key}");
                }
            }
        }
        #endregion


        private static void OnPartGradeChange(CustomTypeBase ctb, int newGrade, int oldGrade)
        {
            var coreComp = ctb as ConstructionCoreComponent;
            var partEntity = coreComp?.PartEntity;
            if (partEntity == null) { return; }
            if (partEntity.TerritoryId == 0) { return; }
            var terrEnt = EntityManager.Instance.GetEntity(partEntity.TerritoryId) as TerritoryEntity;
            if (terrEnt == null) { return; }
            terrEnt.BaseComp.OnPartGradeChange(partEntity, newGrade, oldGrade);
            terrEnt.BatchRecover?.RecordUpgradePart(partEntity);
        }

        public static void SetHasDebris(this TerritoryBaseComponent self, bool hasDebris)
        {
            if (self.ParentEntity is not TerritoryEntity territoryEntity || territoryEntity.PermissionComp == null)
                return;
            if (UserManagerEntity.Instance.GetPlayerEntity(territoryEntity.BaseComp.CreatorRoleId) is PlayerEntity creatorPlayer)
            {
                creatorPlayer.ConstructionComp.SetPlayerDebrisFlag(territoryEntity.EntityId, hasDebris);
            }
            foreach (var roleId in territoryEntity.PermissionComp.GetAllMemebers())
            {
                if (UserManagerEntity.Instance.GetPlayerEntity(roleId) is var player)
                {
                    player.ConstructionComp.SetPlayerDebrisFlag(territoryEntity.EntityId, hasDebris);
                }
            }
        }
    }
}
