﻿using MessagePack;
using Share.Common.ObjPool;
using System.Reflection;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Config;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.Network;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.ClassDef;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.Event;
using WizardGames.Soc.SocWorld.Framework.Threads;
using WizardGames.Soc.SocWorld.Main;
using WizardGames.Soc.SocWorld.Managers;
using WizardGames.Soc.SocWorld.NodeSystem;
using WizardGames.Soc.SocWorld.Share.Utility;
using WizardGames.Soc.SocWorld.TestFramework;

namespace WizardGames.Soc.Test
{
    internal class Program
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(Program));

        private static void Init()
        {
            IdGenerator.Init();
            RandomUtil.Init(1024);
            _ = new ServerConfig();
            CommonLogHelper.Init(ServerConfig.Instance.DefaultLoggerName);
            CustomTypeHelper.InitCustomTypeHelper();
            PropArrayPoolInitializer.Init();
            PlayHelper.BattleId = ServerConfig.Instance.BattleId;
            // 因为Common里的代码各端必须保持一致，所以暂时借用McCommon放一下这个变量
            // 这里在处理表数据
            if (File.Exists(ServerConfig.Instance.HotfixWatchFile))
            {
                McCommon.Tables = new MgrWorldTables(ServerConfig.Instance.OverrideDataPath);
            }
            else
            {
                McCommon.Tables = new MgrWorldTables("Data");
            }
            McCommon.Tables.UpdatePlayId(PlayHelper.GetPlayId());
            RpcParamsAutoGenerate.Init();

            // Entity线程相关
            SocWorld.Network.RpcParamsAutoGenerateTest.Init();
            CustomTypeAutoGenerateTest.Init();
            CustomTypeAutoGenerate.Init();
            MergeClassMetaInfo();

            _ = new LogicThread();
            EntityBase.EntityTimerWheel = LogicThread.Instance.TimerWheel;
            LogicThread.Instance.TimerWheel.Start();
            var processEntity = new ProcessEntity();
            HotfixEntry.FirstLoad();
            EventTypeIdAutoGenerate.Register();
            HandWriteJsonTable.Init();

            var ent = new WarZoneServerControlEntity();
            TestUtility.SetStaticProperty(typeof(ServerControlEntity), "Instance", ent);

            var serverInstance = new ServerInstanceEntity()
            {
                Archive = new()
            };
            serverInstance.AssignStaticInstance();
            var serverStartInfo = new ServerInfo(ServerConfig.Instance.MapId, ServerConfig.Instance.GameMode, ServerConfig.Instance.DefaultTeamSize, 0, ServerConfig.Instance.BattleId);
            ServerInstanceEntity.Instance.InitServerInfo(serverStartInfo);
            processEntity.SetArchive(ServerInstanceEntity.Instance.Archive);
            processEntity.archive.LastWorldTime = 0;
            processEntity.SetGlobalIdParam(ServerInstanceEntity.Instance);
            EntityManager.Instance.Init(MapConfig.Instance.XSize, MapConfig.Instance.ZSize);
            EntityManager.Instance.AddEntity(RpcEntity.Instance);
            EntityManager.Instance.AddEntity(ServerInstanceEntity.Instance);
            EntityManager.Instance.AddEntity(processEntity);
            ServerInstanceEntity.Instance.AddComponent(new FunctionSwitchComponent());
            JsonHelperConstructor.Init();

            TimeStampUtil.FlipedTime = 0;
            TimeStampUtil.ResetServerOffset();
            ProcessEntity.Instance.StartTime();
        }

        private static void MergeClassMetaInfo()
        {
            Dictionary<int, ESyncRange> syncRangeDict = (Dictionary<int, ESyncRange>)WorldClassMetaInfo.SyncRangeDict;
            foreach (var pr in ClassMetaInfoTest.SyncRangeDict)
            {
                if (syncRangeDict.ContainsKey(pr.Key))
                {
                    logger.Error($"ClassMetaInfoTest.SyncRangeDict already contains key {pr.Key}");
                }
                else
                {
                    syncRangeDict.Add(pr.Key, pr.Value);
                }
            }
            Dictionary<int, int> lodDict = (Dictionary<int, int>)WorldClassMetaInfo.LodDict;
            foreach (var pr in ClassMetaInfoTest.LodDict)
            {
                if (lodDict.ContainsKey(pr.Key))
                {
                    logger.Error($"ClassMetaInfoTest.LodDict already contains key {pr.Key}");
                }
                else
                {
                    lodDict.Add(pr.Key, pr.Value);
                }
            }
            Dictionary<int, int> componentIdDict = (Dictionary<int, int>)WorldClassMetaInfo.ComponentHashToId;
            foreach (var pr in ClassMetaInfoTest.ComponentHashToId)
            {
                if (componentIdDict.ContainsKey(pr.Key))
                {
                    logger.Error($"ClassMetaInfoTest.ComponentHashToId already contains key {pr.Key}");
                }
                else
                {
                    componentIdDict.Add(pr.Key, pr.Value);
                }
            }
            Dictionary<int, WorldClassMetaInfo.DeserializeCustomContainerFunc> DeserializeCustomContainerFuncs =
                (Dictionary<int, WorldClassMetaInfo.DeserializeCustomContainerFunc>)WorldClassMetaInfo.DeserializeCustomContainerFuncs;

            foreach (var pr in ClassMetaInfoTest.DeserializeCustomContainerFuncs)
            {
                if (DeserializeCustomContainerFuncs.ContainsKey(pr.Key))
                {
                    logger.Error($"ClassMetaInfoTest.DeserializeCustomContainerFuncs already contains key {pr.Key}");
                }
                else
                {
                    DeserializeCustomContainerFuncs.Add(
                        pr.Key,
                        (ref MessagePackReader reader, int propertyId, long entityId) => pr.Value(ref reader, propertyId, entityId));
                }
            }

        }
        private static void Main(string[] args)
        {
            Init();
            if (args.Length == 0)
            {
                RunAll();
            }
            else
            {
                Run(args);
            }
            Console.ReadLine();
        }

        public static void Run(string[] args)
        {
            var testName = args[0];
            var type = Type.GetType($"WizardGames.Soc.Test.{testName}");
            if (type == null)
            {
                type = Type.GetType($"WizardGames.Soc.Test.TestCase.{testName}");
                if (type == null)
                {
                    Console.WriteLine($"找不到测试类 {testName}");
                    return;
                }
            }

            var testCase = (TestCaseBase)Activator.CreateInstance(type);
            Console.ForegroundColor = ConsoleColor.Green;
            IdGenerator.Init(10000, 20000);
            RandomUtil.Init(1024);
            TimeStampUtil.FlipedTime = 0;
            TimeStampUtil.ResetServerOffset();
            ProcessEntity.Instance.archive.LastWorldTime = 0;
            LogicThread.Instance.TimerWheel.SetTickedCount(0);
            ProcessEntity.Instance.StartTime();
            Console.WriteLine($"测试开始 {testName}");
            TestUtility.RunTestInner(testCase, args);
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine($"测试结束 {testName}");
        }

        public static void RunAll()
        {
            List<string> failedTestCase = new();
            Assembly assembly = Assembly.GetExecutingAssembly();
            var testCases = assembly.GetTypes()
                .Where(t => t.IsClass && !t.IsAbstract && typeof(TestCaseBase).IsAssignableFrom(t) && !Attribute.IsDefined(t, typeof(FrameworkTestAttribute)));
            foreach (var testCaseClass in testCases)
            {
                IdGenerator.Init(10000, 20000);
                RandomUtil.Init(1024);
                TimeStampUtil.FlipedTime = 0;
                TimeStampUtil.ResetServerOffset();
                ProcessEntity.Instance.archive.LastWorldTime = 0;
                LogicThread.Instance.TimerWheel.SetTickedCount(0);
                ProcessEntity.Instance.StartTime();

                var testName = testCaseClass.Name;
                string[] args = [testName];
                var testCase = Activator.CreateInstance(testCaseClass) as TestCaseBase;
                Console.WriteLine($"测试开始 {testName}");
                TestUtility.IsRunAll = true;
                if (TestUtility.RunTestInner(testCase, args) == false) //todo: args 读个配置文件
                {
                    failedTestCase.Add(testName);
                }
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine($"测试结束 {testName}");
            }

            if (failedTestCase.Count > 0)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine("以下测试失败:");
                foreach (var failedTest in failedTestCase)
                {
                    Console.WriteLine(failedTest);
                }
            }
            else
            {
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine("所有测试通过");
            }
        }
    }
}

