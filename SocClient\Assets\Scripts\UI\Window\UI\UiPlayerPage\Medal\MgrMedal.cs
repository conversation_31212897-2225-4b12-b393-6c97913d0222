using Assets.Scripts.MicroServiceClient;
using Cysharp.Text;
using SimpleJSON;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data.medal;
using WizardGames.Soc.Common.Data.Play;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocClient.Http;
using WizardGames.Soc.SocClient.Ui;
using WizardGames.Soc.SocClient.Ui.Utils;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.SocClient.Manager
{
    /// <summary>
    /// 天梯勋章
    /// </summary>
    public class MgrMedal : MgrBase
    {
        private static SocLogger log = LogHelper.GetLogger(typeof(MgrMedal));
        Dictionary<string, string> requestParam = new();
        //每个具体勋章次数
        private Dictionary<int, Dictionary<int, int>> allMedalCountMap = new();

        //每个风格里的每个具体勋章的最高等级
        private Dictionary<int, Dictionary<int, int>> allMedalMaxLevelMap = new(5);
        private MedalTaskContainer medalContainer;
        private List<long> inProgressMedalTaskLst = new();
        private HashSet<long> notGetMedalTaskLst = new();
        private List<long> completeMedalTaskLst = new();
        //任务进度记录
        private Dictionary<long, int> inProgressAppearMedalTaskCountMap = new();
        //private List<long> completeAppearMedalTaskList = new();

        //任务=>勋章
        private Dictionary<long, MedalInfo> recordMedalDic = new();
        //勋章ID=>任务List
        private Dictionary<int, List<long>> medalTaskMap = new();
        //完成的任务
        List<long> completeMedalTaskIds = new List<long>();

        private Dictionary<int, int> styleRankMap = new();
        private Dictionary<int, int> styleScoreMap = new();

        public string openBattleServerID;
        public long lastSwitchTime;
        public bool HasRankPoint;

        public bool ConditionUnlock
        {
            get { return Mc.ConditionMgr?.CheckCompentsUnlockCondition("player_homepage_medal_101") ?? false;}
        }
        // 进度阈值
        private float[] thresholds = { 0.3f, 0.5f, 0.7f };


        public override void CleanUp()
        {
            base.CleanUp();
            requestParam.Clear();
            allMedalCountMap.Clear();
            allMedalMaxLevelMap.Clear();
            recordMedalDic.Clear();
            medalTaskMap.Clear();
            inProgressMedalTaskLst.Clear();
            notGetMedalTaskLst.Clear();
            completeMedalTaskLst.Clear();
            styleRankMap.Clear();
            styleScoreMap.Clear();
        }

        public override void OnAccountLogout()
        {
            requestParam.Clear();
            allMedalCountMap.Clear();
            allMedalMaxLevelMap.Clear();
            recordMedalDic.Clear();
            medalTaskMap.Clear();

            inProgressMedalTaskLst.Clear();
            notGetMedalTaskLst.Clear();
            completeMedalTaskLst.Clear();
            styleRankMap.Clear();
            styleScoreMap.Clear();
        }

        public override Task OnExitWorld()
        {
            recordMedalDic.Clear();
            medalTaskMap.Clear();

            inProgressMedalTaskLst.Clear();
            notGetMedalTaskLst.Clear();
            completeMedalTaskLst.Clear();
            styleRankMap.Clear();
            styleScoreMap.Clear();
            inProgressAppearMedalTaskCountMap.Clear();
         
            return base.OnExitWorld();
        }

        public override void OnAccountLogined()
        {
            GetSettleStyleRankPointsSwitch();
        }

        //获取指定风格的勋章的最高记录
        public void GetMedalMaxLevel(EHttpReqModule reqModule, int styleId, string roleIdName = "", Action<Dictionary<int, int>> callback = null)
        {
            requestParam.Clear();
            requestParam["styleID"] = styleId.ToString();
            //他人的个人信息才需要传roleID
            if (!string.IsNullOrEmpty(roleIdName))
            {
                requestParam["roleID"] = roleIdName;
            }
            MicroServiceClient.Instance.CallLobbyGet("playergrowth/medal/maxlevel", reqModule, requestParam, (res) =>
            {
                log.InfoFormat("GetMedalMaxLevel res:{0}", res);
                JSONNode mdealMaxLevelStr = res["medalMaxLevelMap"];
                string roleIDStr = res["roleID"].Value;
                Dictionary<int, int> medalMap = new();
                foreach (var kvp in mdealMaxLevelStr)
                {
                    if (int.TryParse(kvp.Key, out int medalId) && int.TryParse(kvp.Value, out int maxLevel))
                    {
                        medalMap[medalId] = maxLevel;
                    }
                }
                // 只缓存自己的信息
                if (roleIDStr.Equals(Mc.Config.roleId))
                {
                    allMedalMaxLevelMap[styleId] = medalMap;
                    Mc.Msg.FireMsgAtOnce(EventDefine.MedalMaxLevel, styleId);
                }
                else
                {
                    callback?.Invoke(medalMap);
                }
            });
        }

        //获取具体勋章的各个等级获取次数
        public void GetMedalLevelCount(EHttpReqModule reqModule, int medalID, string roleId = "", Action<Dictionary<int, int>> callback = null)
        {
            requestParam.Clear();
            requestParam["medalID"] = medalID.ToString();
            //他人的个人信息才需要传roleID
            if (!string.IsNullOrEmpty(roleId))
            {
                requestParam["roleID"] = roleId;
            }      
            MicroServiceClient.Instance.CallLobbyGet("playergrowth/medal/levelcount", reqModule, requestParam, (res) =>
            {
                var mdealMaxLevelData = res["medalLevelCountMap"];
                string roleIDStr = res["roleID"].Value;
                Dictionary<int, int> medalMap = new();
                foreach (var kvp in mdealMaxLevelData)
                {
                    if (int.TryParse(kvp.Key, out int level) && int.TryParse(kvp.Value, out int medalCount))
                    {
                        medalMap[level] = medalCount;
                    }
                }
                // 只缓存自己的信息
                if (roleIDStr.Equals(Mc.Config.roleId))
                {
                    allMedalCountMap[medalID] = medalMap;
                    Mc.Msg.FireMsgAtOnce(EventDefine.MedalLevelCount, medalID);
                }
                else
                {
                    callback?.Invoke(medalMap);
                }                             
            });
        }

        //获取最近所有对局的5维风格段位积分数据
        public void GetRecent5DStyleRankPoints(EHttpReqModule reqModule, Action callback = null)
        {
            requestParam.Clear();
            MicroServiceClient.Instance.CallLobbyGet("playergrowth/style/recent5dstylescore", reqModule, requestParam, (res) =>
            {
                var data = res["gameStyleRankPointsList"];
                Mc.Msg.FireMsg(EventDefine.RecentStyleScore);
            });
        }
        // 获取战局积分结算开关信息
        public void GetSettleStyleRankPointsSwitch()
        {
            MicroServiceClient.Instance.CallLobbyGet("playergrowth/style/settleswitch", EHttpReqModule.Lobby, null, (res) =>
            {
                openBattleServerID = res["openBattleServerID"].Value;
                log.DebugFormat("GetSettleStyleRank openBattleServerID:{0}", openBattleServerID);
                lastSwitchTime = res["lastSwitchTime"].AsLong;
                Mc.Msg.FireMsg(EventDefine.GetSettleStyleRankPointsSwitch);
            });
        }
        // 设置战局积分结算开关-在没有战局开启结算且在新匹配完成时 或 切换开启积分结算战局时使用
        public void SetSettleStyleRankPointsSwitch(string openBattleServerID)
        {
            JSONObject param = new();
            param.Add("openBattleServerID", openBattleServerID);
            //log.InfoFormat("ReqSendMsg param:{0}", param);
            MicroServiceClient.Instance.CallLobbyPost("playergrowth/style/setsettleswitch", EHttpReqModule.Lobby, (res)=>
            { 
                this.openBattleServerID = openBattleServerID;
                log.DebugFormat("SetSettleStyle openBattleServerID:{0}", openBattleServerID);
                if (res["lastSwitchTime"] != null)
                {
                    lastSwitchTime = res["lastSwitchTime"].AsLong;
                }           
                Mc.Msg.FireMsg(EventDefine.SetSettleSwitchSuccess);
            }, param); 
        }
        // 删除战局积分结算开关-仅当删除已开启积分结算的战局时使用
        public void DelSettleStyleRankPointsSwitch(string openBattleServerID)
        {
            JSONObject param = new();
            param.Add("openBattleServerID", openBattleServerID);
            MicroServiceClient.Instance.CallLobbyPost("playergrowth/style/delsettleswitch", EHttpReqModule.Lobby, (res)=>
            {
                this.openBattleServerID = "";
                log.DebugFormat("DelSettleStyle openBattleServerID:{0}", openBattleServerID);
                Mc.Msg.FireMsg(EventDefine.DeleteSettleSwitchSuccess);
            }, param);
        }
        public void UpdateMedalRecordInfo(ECustomDictOpType opType, long taskId, MedalInfo medalInfo)
        {
            recordMedalDic[taskId] = medalInfo;
            if (medalInfo == null)
            {
                log.InfoFormat("Medal UpdateMedalRecordInfo:taskId:{0}", taskId);
            }
            // switch (opType)
            // {
            //     case ECustomDictOpType.Clear:
            //         break;
            //     case ECustomDictOpType.InsertOrOverride:
            //         recordMedalDic[taskId] = info;
            //         break;
            //     case ECustomDictOpType.Remove:
            //         break;
            //     case ECustomDictOpType.Modify:
            //         recordMedalDic[taskId] = info;
            //         break;
            //     case ECustomDictOpType.BeforeClear:
            //         break;
            // }
        }

        
        public void CreateMedalReconstructData()
        {
            var medalDic = GlobalInfoSyncEntity.Instance.MedalReconstructData;
            medalTaskMap.Clear();
            foreach (var (taskId, medalInfo) in medalDic)
            {
                recordMedalDic[taskId] = medalInfo;
                int medalId = medalInfo.MedalId;
                if (medalTaskMap.TryGetValue(medalId, out List<long> medalList))
                {
                    medalList.Add(taskId);
                    medalTaskMap[medalId] = medalList;
                }
                else
                {
                    List<long> newMedalList = new()
                    {
                        taskId,
                    };
                    medalTaskMap[medalId] = newMedalList;
                }
            }
        }
        public MedalInfo GetMedalInfoInGame(long taskId)
        {
            return recordMedalDic[taskId];
        }

        public long GetMealTaskId(int medalId, int level)
        {
            foreach (var (taskId, medalInfo) in recordMedalDic)
            {
                if (medalInfo != null && medalInfo.MedalId == medalId && medalInfo.Level == level)
                {  
                    return taskId;
                }
            }
            return 0;
        }

        public void InitMedalTask(SystemRootNode systemRoot)
        {
            if (systemRoot == null) return;
            var medalRoot = systemRoot.GetChildNode(PlayerTaskContainerIndex.Medal);
            if (medalRoot == null) return;

            medalContainer = medalRoot as MedalTaskContainer;
            styleRankMap.Clear();
            foreach (var (styleId, rank) in medalContainer.StyleToRank)
            {
                styleRankMap.Add(styleId, rank);
            }
            styleScoreMap.Clear();
            foreach (var (styleId, score) in medalContainer.StyleToScore)
            {
                styleScoreMap.Add(styleId, score);
            }
            medalContainer.StyleToRank.SubscribeAnyUpdateCallback(RefreshMedalStyleRankChanged);
            medalContainer.StyleToScore.SubscribeAnyUpdateCallback(RefreshMedalStyleScoreChanged);

            var inProgressNode = medalRoot.GetChildNode(TaskNodeIndex.InProgress) as DirectoryNode;
            inProgressNode.SubscribeAnyUpdateCallback(RefreshInProgressMedalTask);
            inProgressMedalTaskLst.Clear();
            foreach (var (id, node) in inProgressNode)
            {
                if (node is not TaskNode taskNode) continue;
                var cfg = Mc.Tables.TbQuestPhase.GetOrDefault(taskNode.BizId);
                if (cfg == null) continue;
                inProgressMedalTaskLst.Add(taskNode.BizId);
            }

            var notGetRewardNode = medalRoot.GetChildNode(TaskNodeIndex.CompletedAndNotGetReward) as DirectoryNode;
            notGetRewardNode.SubscribeAnyUpdateCallback(RefreshNotGetMedalTask);
            notGetMedalTaskLst.Clear();
            foreach (var (id, node) in notGetRewardNode)
            {
                if (node is not TaskNode taskNode) continue;
                var cfg = Mc.Tables.TbQuestPhase.GetOrDefault(taskNode.BizId);
                if (cfg == null) continue;
                notGetMedalTaskLst.Add(taskNode.BizId);
            }
            
            medalContainer.CompletedTaskIds.SubscribeAnyUpdateCallback(RefreshCompleteMedalTask);
            completeMedalTaskLst.Clear();
            foreach (var taskId in medalContainer.CompletedTaskIds)
            {
                var cfg = Mc.Tables.TbQuestPhase.GetOrDefault(taskId);
                if (cfg == null) continue;
                completeMedalTaskLst.Add(taskId);
            }
            GetMedalCompletedTaskInfo();
        }
        
        public EDailyMissionState GetMedalTaskState(long id) 
        {
            if (inProgressMedalTaskLst.Contains(id)) 
            {
                return EDailyMissionState.InProgress;
            }
            else if (notGetMedalTaskLst.Contains(id)) 
            {
                return EDailyMissionState.NotGetReward;
            }
            else if (completeMedalTaskLst.Contains(id)) 
            {
                return EDailyMissionState.Complete;
            }
            return EDailyMissionState.Invalid;
        }
        public void GetMedalCompletedTaskInfo()
        {          
            completeMedalTaskIds.Clear();
            if (notGetMedalTaskLst.Count > 0)
            {
                completeMedalTaskIds.AddRange(notGetMedalTaskLst);
            }
            if (completeMedalTaskLst.Count > 0)
            {
                completeMedalTaskIds.AddRange(completeMedalTaskLst);
            }        
        }
        //获取游戏内当前勋章获得的最大等级
        public int GetMedalMaxLevelInGame(int medalId)
        {
            if (medalTaskMap.TryGetValue(medalId, out List<long> medalList))
            {
                int maxLevel = 0;
                foreach (var taskId in medalList)
                {
                    //任务完成
                    if (completeMedalTaskIds.Contains(taskId))
                    {
                        if (recordMedalDic.TryGetValue(taskId, out MedalInfo medalInfo))
                        {
                            if (medalInfo.Level > maxLevel)
                            {
                                maxLevel = medalInfo.Level;
                            }
                        }
                    }                   
                }
                return maxLevel;
            }
            return 0;
        }

        //获取游戏内当前勋章获得的所有等级
        public Dictionary<int, int> GetMedalLevelCountMapInGame(int medalId)
        {
            Dictionary<int, int> maxLevelMap = new();
            if (medalTaskMap.TryGetValue(medalId, out List<long> medalList))
            {
                foreach (var taskId in medalList)
                {
                    //任务完成
                    if (completeMedalTaskIds.Contains(taskId))
                    {
                        if (recordMedalDic.TryGetValue(taskId, out MedalInfo medalInfo))
                        {
                            //局内只能获取一次
                            maxLevelMap.Add(medalInfo.Level, 1);
                        }
                    }       
                }
            }
            return maxLevelMap;
        }
        public int GetStyleScoreInGame(int styleId)
        {
            if (medalContainer != null)
            {
                if (medalContainer.StyleToScore.TryGetValue(styleId, out int score))
                {
                    return score;
                }
            }
            return 0;
        }

        public int GetStyleRankInGame(int styleId)
        {
            if (medalContainer != null)
            {
                if (medalContainer.StyleToRank.TryGetValue(styleId, out int rank))
                { 
                    return rank;
                }           
            }
            return 0;
        }
        //升段时候才弹
        private void RefreshMedalStyleRankChanged()
        {
            if (Mc.MyPlayer.MyRootNode == null)
                return;
            if (medalContainer == null) return;
            foreach (var (styleId, rankNum) in medalContainer.StyleToRank)
            {
                if (styleRankMap.TryGetValue(styleId, out int oldRank))
                {
                    if (rankNum > oldRank)
                    {
                        Mc.MsgTips.ShowMedalRankStyleTip(styleId, 0, true);
                    }
                }
                else
                {
                    Mc.MsgTips.ShowMedalRankStyleTip(styleId, 0, true);
                }
                styleRankMap[styleId] = rankNum;
            }
        }

        private void RefreshMedalStyleScoreChanged()
        {
            if (Mc.MyPlayer.MyRootNode == null)
                return;
            if (medalContainer == null) return;
            foreach (var (styleId, scoreNum) in medalContainer.StyleToScore)
            {
                if (styleScoreMap.TryGetValue(styleId, out int oldScore))
                {
                    if (scoreNum > oldScore)
                    {
                        Mc.MsgTips.ShowMedalRankStyleTip(styleId, scoreNum - oldScore);
                    }
                }
                else
                {
                    Mc.MsgTips.ShowMedalRankStyleTip(styleId, scoreNum);
                }
                styleScoreMap[styleId] = scoreNum;
            }
        }
        private void RefreshInProgressMedalTask()
        {
            if (Mc.MyPlayer.MyRootNode == null)
                return;
            var inProgressNode = Mc.MyPlayer.MyRootNode?.GetNodeByPath(NodeSystemType.TaskSystem, PlayerTaskContainerIndex.Medal, TaskNodeIndex.InProgress) as DirectoryNode;
            inProgressMedalTaskLst.Clear();
            foreach (var (id, node) in inProgressNode)
            {
                if (node is not TaskNode taskNode) continue;
                var cfg = Mc.Tables.TbQuestPhase.GetOrDefault(taskNode.BizId);
                if (cfg == null) continue;
                inProgressMedalTaskLst.Add(taskNode.BizId);
                if (recordMedalDic.TryGetValue(taskNode.BizId, out MedalInfo medalInfo))
                {
                    long taskId = taskNode.BizId;
                    var taskConfig = Mc.Tables.TbQuestPhase.GetOrDefault(taskId);
                    var totalNum = taskConfig.EndConditionParameter[0];
                    var taskCount = Mc.Mission.GetTaskCount(taskId);
                    float value = taskCount / (float)totalNum;
                  
                    if (!inProgressAppearMedalTaskCountMap.TryGetValue(taskId, out int lastThresholdIndex))
                    {
                        lastThresholdIndex = -1;
                    }

                    int currentThresholdIndex = -1;
                    for (int i = thresholds.Length - 1; i >= 0; i--)
                    {
                        if (value >= thresholds[i])
                        {
                            currentThresholdIndex = i;
                            break;
                        }
                    }
                    if (currentThresholdIndex > lastThresholdIndex)
                    {
                        inProgressAppearMedalTaskCountMap[taskId] = currentThresholdIndex;
                        log.InfoFormat("RefreshInProgressMedalTask BizId:{0}, MedalId:{1}, Level:{2}, Point:{3}", taskNode.BizId, medalInfo.MedalId, medalInfo.Level, medalInfo.Point);
                        Mc.MsgTips.ShowMedalRankBadgeTip(medalInfo.MedalId, medalInfo.Level, medalInfo.Point, taskNode.BizId);
                    }            
                }
            }          
        }
        private void RefreshNotGetMedalTask()
        {
            if (Mc.MyPlayer.MyRootNode == null)
                return;
            var notGetRewardNode = Mc.MyPlayer.MyRootNode?.GetNodeByPath(NodeSystemType.TaskSystem, PlayerTaskContainerIndex.Medal, TaskNodeIndex.CompletedAndNotGetReward) as DirectoryNode;
            foreach (var (id, node) in notGetRewardNode)
            {
                if (node is not TaskNode taskNode) continue;
                var cfg = Mc.Tables.TbQuestPhase.GetOrDefault(taskNode.BizId);
                if (cfg == null) continue;
                bool isSuccess = notGetMedalTaskLst.Add(taskNode.BizId);
                if (isSuccess)
                {
                    if (recordMedalDic.TryGetValue(taskNode.BizId, out MedalInfo medalInfo))
                    {
                        log.InfoFormat("RefreshNotGetMedalTask BizId:{0}, MedalId:{1}, Level:{2}, Point:{3}", taskNode.BizId, medalInfo.MedalId, medalInfo.Level, medalInfo.Point);
                        Mc.MsgTips.ShowMedalRankBadgeTip(medalInfo.MedalId, medalInfo.Level, medalInfo.Point, taskNode.BizId);
                    }
                }
            }
            GetMedalCompletedTaskInfo();
        }
        private void RefreshCompleteMedalTask()
        {
            if (Mc.MyPlayer.MyRootNode == null)
                return;
            var taskContainer = Mc.MyPlayer.MyRootNode?.GetNodeByPath(NodeSystemType.TaskSystem, PlayerTaskContainerIndex.Medal) as TaskContainer;
            completeMedalTaskLst.Clear();
            foreach (var taskId in taskContainer.CompletedTaskIds)
            {
                var cfg = Mc.Tables.TbQuestPhase.GetOrDefault(taskId);
                if (cfg == null) continue;
                completeMedalTaskLst.Add(taskId);
            }
            GetMedalCompletedTaskInfo();
        }

        public Dictionary<int, int> GetMedalMaxLevelById(int styleID)
        {
            if (allMedalMaxLevelMap.TryGetValue(styleID, out Dictionary<int, int> medalCountMap))
            {
                return medalCountMap;
            }
            return null;
        }
        public Dictionary<int, int> GetMedalLevelCountById(int medalID)
        {
            if (allMedalCountMap.TryGetValue(medalID, out Dictionary<int, int> medalCountMap))
            {
                return medalCountMap;
            }
            return null;
        }

        //根据积分计算段位
        public OBJStyleRank GetMedalRankId(int point)
        {
            var ranList = Mc.Tables.TBStyleRank.DataList;
            for (int i = 0; i < ranList.Count; i++)
            {
                var rank = ranList[i];
                if (point >= rank.MinPoints && point <= rank.MaxPoints)
                {
                    return rank;
                }
            }
            return null;
        }

        public List<OBJMedal> GetRankMedalListData(int medalID)
        {
            List<OBJMedal> medalList = new();
            var list = Mc.Tables.TBMedal.DataList;
            for (int i = 0; i < list.Count; i++)
            {
                if (list[i].MedalID == medalID)
                {
                    medalList.Add(list[i]);
                }
            }
            return medalList;
        }

        public string GetBJStyleRankPoint(int styleId)
        {
            var list = Mc.Tables.TBStyleRankPoints.DataList;
            for (int i = 0; i < Mc.Tables.TBStyleRankPoints.DataList.Count; i++)
            {
                if (list[i].StyleID == styleId)
                {
                    return list[i].StyelDes;
                }
            }
            return string.Empty;
        }

        public List<OBJGroupMedal> GetRankMealsByStyleId(int styleId)
        {
            var list = Mc.Tables.TBMedal.DataList;
            Dictionary<ENUMType, List<int>> medalMap = new();
            for (int i = 0; i < list.Count; i++)
            {
                if (list[i].StyleID == styleId)
                {

                    medalMap.TryGetValue(list[i].Type, out List<int> medalList);
                    if (medalList == null)
                    {
                        medalList = new List<int>();
                        medalMap[list[i].Type] = medalList;
                    }
                    if (!medalList.Contains(list[i].MedalID))
                    {
                        medalList.Add(list[i].MedalID);
                    }
                }
            }
            List<OBJGroupMedal> medalsList = new();
            foreach (var kvp in medalMap)
            {
                OBJGroupMedal groupMedal = new OBJGroupMedal(kvp.Key, kvp.Value);
                medalsList.Add(groupMedal);
            }
            return medalsList;
        }

        //taskID加SubTasks才是总任务
        public List<long> GetAllTasks(long taskId)
        {
            List<long> list = new() { taskId };
            QuestPhase phaseCfg = McCommon.Tables.TbQuestPhase.GetOrDefault(taskId);
            if (phaseCfg.SubTasks.Length > 0)
            {
                for (int i = 0; i < phaseCfg.SubTasks.Length; i++)
                {
                    list.Add(phaseCfg.SubTasks[i]);
                }
            }
            return list;
        }

        //获取下一个勋章同步时间
        public string GetLeftMedalSyncTime()
        {
            long startTimeOffset = ProcessEntity.Instance.ServerTimeSinceLogicStart / 1000;
            int intervalNum = Mathf.CeilToInt(startTimeOffset / (float)(Mc.Tables.TBConsts.MedalSyncIntervalMinutes * 60));
            long offsetTime = intervalNum * Mc.Tables.TBConsts.MedalSyncIntervalMinutes * 60 - startTimeOffset;
            return CommonNumberFormatUtils.LeftTimeFormat(LanguageConst.NextMedalSyncTime, offsetTime);
        }

        public bool IsRankScoreSwitchValid(out long cdTime)
        {
            cdTime = 0;
            if (lastSwitchTime > 0)
            {
                long targetTime = Mc.Tables.TBConsts.ChangeRankMatchCd * 60 + lastSwitchTime;
                if (Mc.LobbyTime.TryGetCurSvrTimeStamp(out long curTime))
                {
                    var offsetTime = targetTime - curTime;
                    if (offsetTime > 0)
                    {
                        cdTime = offsetTime;
                        return false;
                    }
                    return true;
                }
                return false;
            }
            return true;
        }

        public void OnPushMedalLevelChange(JSONNode push)
        {
            var oldStylePoint = push["oldStyleRankPoint"];
            Dictionary<int, int> oldStylePointMap = new();
            foreach (var (key, value) in oldStylePoint)
            {
                int styleId = int.Parse(key);
                oldStylePointMap[styleId] = value.AsInt;
            }

            var newStylePoint = push["newstyleRankPoint"];
            Dictionary<int, int> newStylePointMap = new();
            foreach (var (key, value) in newStylePoint)
            {
                int styleId = int.Parse(key);
                int newScore = value.AsInt;
                int curLv = GetMedalRankId(newScore)?.RankID ?? 1;
                newStylePointMap[styleId] = value.AsInt;
                if (oldStylePointMap.TryGetValue(styleId, out int score))
                {
                    if (newScore > score)
                    {
                        int oldLv = GetMedalRankId(score)?.RankID ?? 1;                       
                        Mc.Config.AppendUpgradeInfo(ELevelUpgradeStyle.MedalLevel, oldLv, score, curLv, newScore, styleId);
                    }
                }
                else
                {
                   Mc.Config.AppendUpgradeInfo(ELevelUpgradeStyle.MedalLevel, 1, 0, curLv, newScore, styleId);
                }
            }
            Mc.Ui.OpenWindow("UiLevelUpgrade");
        }
        //保存已播放动效的勋章id
        public bool IsNeedPlayUnlocedMedalTrans(int medalId)
        {
            var saveKey = ZString.Format("{0}_{1}", "MedalUnLock", medalId);
            var saveValue = ClientPlayerPrefs.GetInt(saveKey, 0);
            return saveValue == 0;
            //return true;
        }

        public void SavenlocedMedalTrans(int medalId)
        {
            var saveKey = ZString.Format("{0}_{1}", "MedalUnLock", medalId);
            ClientPlayerPrefs.SetInt(saveKey, 1);
            ClientPlayerPrefs.Save();
        }

        public void TestCheckLevelUpPopup()
        {
            Dictionary<int, int> oldStylePointMap = new();
            oldStylePointMap.Add(1001, 100);
            oldStylePointMap.Add(1002, 150);
            
            //var newStylePoint = push["newstyleRankPoint"];
            Dictionary<int, int> newStylePointMap = new();
            newStylePointMap.Add(1001, 300);
            newStylePointMap.Add(1002, 400);
            foreach (var (styleId, newScore) in newStylePointMap)
            {
                int curLv = GetMedalRankId(newScore)?.RankID ?? 1;
                if (oldStylePointMap.TryGetValue(styleId, out int score))
                {
                    if (newScore > score)
                    {
                        int oldLv = GetMedalRankId(score)?.RankID ?? 1;
                        Mc.Config.AppendUpgradeInfo(ELevelUpgradeStyle.MedalLevel, oldLv, score, curLv, newScore, styleId);
                    }
                }
                else
                {
                    Mc.Config.AppendUpgradeInfo(ELevelUpgradeStyle.MedalLevel, 1, 0, curLv, newScore, styleId);
                }
            }
            Mc.Ui.OpenWindow("UiLevelUpgrade");
        }
    }
    public class OBJGroupMedal
    {
        public ENUMType type;
        public List<int> medalsList = new();
        public OBJGroupMedal(ENUMType type, List<int> medalsList)
        {
            this.type = type;
            this.medalsList = medalsList;
        }
    }
    public enum EMedalTaskMode
    {

        //双排
        Two = 2,
        //四排
        Four = 4,
    }
}
