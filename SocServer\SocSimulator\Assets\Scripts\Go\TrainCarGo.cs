﻿using System;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Const;
using WizardGames.Soc.Common.Contexts;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data.DataItem;
using WizardGames.Soc.Common.Data.Play;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Synchronization;
using WizardGames.Soc.Common.TimerWheel;
using WizardGames.Soc.Common.Train;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.Config;
using WizardGames.Soc.Common.Unity.Go;
using WizardGames.Soc.Common.Unity.GoLoader;
using WizardGames.Soc.Common.Unity.Utility;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Vehicle;
using WizardGames.Soc.Procedural;
using WizardGames.Soc.SocAI.Utility;
using WizardGames.Soc.SocLogic.Go;
using WizardGames.Soc.SocSimulator.Manager;
using WizardGames.Soc.SocSimulator.Utility;
using WizardGames.Soc.SocSimulator.WorldResource;
using WizardGames.SocConst.Soc.Const;
using static WizardGames.Soc.Procedural.TrainTrackSpline;
using TrackSelection = WizardGames.Soc.Procedural.TrainTrackSpline.TrackSelection;


namespace WizardGames.Soc.SocSimulator.Go
{
    /// <summary>
    /// Simulator Train Go
    /// </summary>
    public partial class TrainCarGo : BaseTrainCarGo, ITrainTrackUser
    {
        #region Field
        public Vector3 Position
        {
            get
            {
                return MainGo.transform.position;
            }
        }
        public float FrontWheelSplineDist { get; private set; }
        public float RearWheelSplineDist { get; private set; }
        private TrackSelection kDefaultTrackSelect = TrackSelection.Right;
        private TrackSelection mLocalTrackSelection;
        public TrackSelection LocalTrackSelection
        {
            get
            {
                return mLocalTrackSelection;
            }
        }
        public enum TrainCarType { Wagon, Engine }
        public virtual TrainCarType mCarType => TrainCarType.Wagon;

        // Track section the front wheels are on (if any)
        TrainTrackSpline mFrontTrackSection;
        public static float TRAINCAR_MAX_SPEED = 25f;
        // Track section the rear wheels are on (if any)
        public TrainTrackSpline RearTrackSection { get; private set; }
        public TrainTrackSpline FrontTrackSection
        {
            get
            {
                return mFrontTrackSection;
            }
            private set
            {
                if (mFrontTrackSection != value)
                {
                    if (mFrontTrackSection != null)
                    {
                        mFrontTrackSection.DeregisterTrackUser(this);
                    }
                    mFrontTrackSection = value;
                    if (mFrontTrackSection != null)
                    {
                        // Use the front wheel pos to register this train as
                        // currently on the specified piece of track.
                        // Using both the front and back wheels would cause trouble;
                        // e.g. One wheel deregistering while the other is still no the spline
                        mFrontTrackSection.RegisterTrackUser(this);
                    }
                }
            }
        }
        private Transform mFrontBogiePivot;
        private Transform mRearBogiePivot;
        private Transform mCentreOfMassTransform;
        private Rigidbody mRigidBody;
        public Rigidbody Rigidbody
        {
            get
            {
                return mRigidBody;
            }
        }
        private float mDistFrontToBackWheel;
        private Vector3 mFrontBogieLocalOffset;
        private Vector3 mRearBogieLocalOffset;

        private float mFrontBogieYRot; // Local space
        private float mRearBogieYRot;
        // CompleteTrain will be a shared class instance for all connected TrainCars
        private CompleteTrain mCompleteTrain;
        public CompleteTrain CompleteTrain
        {
            get => mCompleteTrain;
            set => mCompleteTrain = value;
        }

        private TrainEngineStateEnum mCurEngineState = TrainEngineStateEnum.Off;
        private int mEngineStartupTime = 1000;//引擎启动时间 ms
        public EngineSpeeds CurThrottleSetting { get; protected set; } = EngineSpeeds.Zero;
        private bool isInJunctionRange = false;
        private float maxSpeed
        {
            get
            {
                var tb = Mc.Tables.TbTrainCarProperty.GetOrDefault(TrainCarEntity.TemplateId);
                if (tb != null)
                    return tb.ThrottleSpeed;
                return 15.0f;
            }
        }
        private float engineForce
        {
            get
            {
                var tb = Mc.Tables.TbTrainCarProperty.GetOrDefault(TrainCarEntity.TemplateId);
                if (tb != null)
                    return tb.EngineForce;
                return 1000.0f;
            }
        }
        private TrainCar mTrainCarCom;
        public TrainCar TrainCarCom => mTrainCarCom;
        private long destroyTimeDuration
        {
            get
            {
                var tb = Mc.Tables.TbTrainCarProperty.GetOrDefault(TrainCarEntity.TemplateId);
                if (tb != null)
                    return tb.DestroyTime * 1000;
                return 60000;
            }
        }

        public const int HAZARD_CHECK_EVERY = 1000; //ms
        // Metres away. Furthest dist to warn of a hazard ahead
        private float hazardDistMax
        {
            get
            {
                var tb = Mc.Tables.TbTrainCarProperty.GetOrDefault(TrainCarEntity.TemplateId);
                if (tb != null)
                    return tb.HazardMaxDist;
                return 325f;
            }
        }
        // Metres away. Shortest dist to warn of a hazard ahead
        // We don't want the hazard warning to play all the time when e.g. shunting another train
        private float hazardDistMin
        {
            get
            {
                var tb = Mc.Tables.TbTrainCarProperty.GetOrDefault(TrainCarEntity.TemplateId);
                if (tb != null)
                    return tb.HazardMinDist;
                return 40f;
            }
        }
        public const float HAZARD_SPEED_MIN = 4.5f; // m/s
        private bool mHazardAhead = false;
        private long mCheckHazardTimer = -1;
        TriggerTrainCollisions mFrontCollisionTrigger;
        TriggerTrainCollisions mRearCollisionTrigger;
        private long mLastDestroyTimeStamp = -1;
        private long mDestroyLeftTime = -1;
        private bool mStartDestroyTimeCount = false;
        private bool mPauseDestroyTimeCount = false;
        private long mDestroyTimerID = -1;
        private bool mIsCanDestroy = false;//销毁倒计时结束  如果符合销毁条件则立马销毁
        private long _coupledToFrontTimerId = -1;
        private float mTrainDestroyDis
        {
            get
            {
                if (TrainCarEntity != null)
                {
                    var tb = Mc.Tables.TbTrainCarProperty.GetOrDefault(TrainCarEntity.TemplateId);
                    if (tb != null)
                        return tb.DestroyTrainNoPeopleInRange;
                }
                return 30;
            }
        }
        private bool mIsDead = false;
        private float derailCollisionForce
        {
            get
            {
                if (TrainCarEntity != null)
                {
                    var tb = Mc.Tables.TbTrainCarProperty.GetOrDefault(TrainCarEntity.TemplateId);
                    if (tb != null)
                        return tb.DerailCollisionForce;
                }
                return 200000f;
            }
        }
        private float collisionDamageDivide
        {
            get
            {
                if (TrainCarEntity != null)
                {
                    var tb = Mc.Tables.TbTrainCarProperty.GetOrDefault(TrainCarEntity.TemplateId);
                    if (tb != null)
                        return tb.CollisionDamageDivide;
                }
                return 100000;
            }
        }
        private int mTrainCollisonPart = (int)CollisionTrainPart.None;//火车碰撞位置
        public TriggerTrainCollisions FrontCollisionTrigger
        {
            get
            {
                return mFrontCollisionTrigger;
            }
        }

        public TriggerTrainCollisions RearCollisionTrigger
        {
            get
            {
                return mRearCollisionTrigger;
            }
        }
        #endregion
        public TrainCarGo() : base() { }

        private static SocLogger mLogger { get; } = LogHelper.GetLogger(typeof(TrainCarGo));

        public static IEntityGo CreateTrainCarGo(IEntity entity, GameObject root)
        {
            throw new System.NotImplementedException();
        }

        public enum TrainPosType
        {
            Front,
            Middle,
            Rear
        }

        public override void Spawn(IEntity entity, GameObject root, List<EntityAsset> assets)
        {
            TrainCarEntity trainCarEntity = entity as TrainCarEntity;
            long headEntityId = trainCarEntity.HeadEntityId;

            if (headEntityId > 0)
            {
                if (!FixPointSpawner.Instance.TrainCarEntities.TryGetValue(headEntityId,
                        out LinkedList<TrainCarEntity> trainCarEntities))
                {
                    FixPointSpawner.Instance.TrainCarEntities[headEntityId] = new LinkedList<TrainCarEntity>();
                }

                trainCarEntities = FixPointSpawner.Instance.TrainCarEntities[headEntityId];
                int slotIndex = trainCarEntity.SlotIndex;
                LinkedListNode<TrainCarEntity> node = trainCarEntities.First;
                while (node != null)
                {
                    if (node.Value.SlotIndex > slotIndex)
                    {
                        trainCarEntities.AddBefore(node, trainCarEntity);
                        break;
                    }

                    node = node.Next;
                }

                if (node == null)
                {
                    trainCarEntities.AddLast(trainCarEntity);
                }
            }

            OnInit(entity, root);

            GameObject mainGo = assets[0].Asset as GameObject;
            SetMainGo(mainGo);
            OnAfterInit();
        }

        public override void WakeUp(MountableWakeupReason reason)
        {
            logger.InfoFormat("Wakeup: type:{0}, id:{1}, reason:{2}", Entity.EntityType, Entity.EntityId, reason);
            IsSleep = false;
            MountableEntity.IsSleep = false;
            LastTransformChangedTime = McCommon.Time.ServerWorldTime;
            RefreshFuelConsumption();
        }

        protected new void SetMainGo(GameObject mainGo)
        {
            base.SetMainGo(mainGo);
            InitOnSpawn();
            mStartDestroyTimeCount = false;
            mPauseDestroyTimeCount = false;
            mDestroyTimerID = -1;

            TrainCarEntity.RecordTrainUnloadCount();            // 记录卸货的数量
            _currentUnloadPercentage = GetUnloadPercentage(TrainCarEntity);
            TrainCarEntity.TrainUnloadPercentage = _currentUnloadPercentage;
            if (TrainCarCom != null) TrainCarCom.UpdateOrePlane(_currentUnloadPercentage * 0.01f);
        }

        private float GetUnloadPercentage(TrainCarEntity trainCarEntity)
        {
            float targetUnloadPercentage = 0;
            if (1 <= trainCarEntity.TrainUnloadCount)
            {
                targetUnloadPercentage = (float)trainCarEntity.TrainCom.CalculateTrainUnloadCount() / trainCarEntity.TrainUnloadCount;
                targetUnloadPercentage = Mathf.Clamp(targetUnloadPercentage, 0f, 1f) * 100f;
            }
            return targetUnloadPercentage;
        }

        protected new void OnAfterInit()
        {
            base.OnAfterInit();
            BaseFuelComponent = new FuelComponent(this);
        }

        public override bool IgnoreSleep()
        {
            TrainCarEntity trainHead = GetTrainHead();            // 火车头不休眠
            if (trainHead == null)
            {
                return base.IgnoreSleep();
            }

            if (EntityId != trainHead.EntityId) // 不是自己
            {
                if (Mc.Go.GetGo(trainHead.EntityId) is TrainCarGo { IsSleep: false })
                    return true;
            }

            return trainHead.TrainUnloadState == TrainUnloadStateEnum.AutoParking || base.IgnoreSleep(); // 自动泊车
        }

        public TrainCarEntity GetTrainHead()
        {
            FixPointSpawner.Instance.TrainCarEntities.TryGetValue(TrainCarEntity.HeadEntityId,
                out LinkedList<TrainCarEntity> trainCarEntities);

            LinkedListNode<TrainCarEntity> node = trainCarEntities?.First;

            return node?.Value;
        }

        private void RefreshHurtTrigger()
        {
            if (mTrainCarCom == null || mTrainCarCom.hurtTriggers == null)
            {
                return;
            }

            var posType = TrainPosType.Middle;
            if (TrainCarEntity.EntityId == TrainCarEntity.HeadEntityId)
            {
                posType = TrainPosType.Front;
            }
            else if (TrainCarEntity.SlotIndex == TrainCarEntity.TrainLength - 1)
            {
                posType = TrainPosType.Rear;
            }

            foreach (var triggerHurt in mTrainCarCom.hurtTriggers)
            {
                if (triggerHurt.HurtTriggerMinSpeed == 0)
                {
                    continue;
                }

                var collider = triggerHurt.GetComponent<BoxCollider>();
                if (collider == null)
                {
                    continue;
                }

                var isNormalSize = triggerHurt.HurtTriggerMinSpeed > 0 && posType == TrainPosType.Front
                                 || triggerHurt.HurtTriggerMinSpeed < 0 && posType == TrainPosType.Rear;
                var center = collider.center;
                var size = collider.size;
                if (TrainCarEntity.TrainLength == 1)
                {
                    isNormalSize = true;
                }
                if (isNormalSize)
                {
                    center.y = 1.32f;
                    size.y = 2.4f;
                }
                else
                {
                    center.y = 0.25f;
                    size.y = 0.5f;
                }
                collider.center = center;
                collider.size = size;
            }
        }

        private void InitOnSpawn()
        {
            mIsDead = false;
            mTrainCarCom = MainGo.GetComponent<TrainCar>();
            mTrainCarCom.ServerInit(Entity, this);
            mTrainCarCom.InitShared();
            RefreshHurtTrigger();
            mLocalTrackSelection = kDefaultTrackSelect;
            if (mTrainCarCom.FrontCollisionTrigger != null)
            {
                mFrontCollisionTrigger = mTrainCarCom.FrontCollisionTrigger.GetComponent<TriggerTrainCollisions>();
                if (mFrontCollisionTrigger == null) mFrontCollisionTrigger = mTrainCarCom.FrontCollisionTrigger.AddComponent<TriggerTrainCollisions>();
                mFrontCollisionTrigger.location = TriggerTrainCollisions.Location.Front;
                mFrontCollisionTrigger.Owner = this;
                mFrontCollisionTrigger.TrainHead = this; // default to head
                mFrontCollisionTrigger.triggerCollider = mTrainCarCom.FrontCollisionTrigger.GetComponent<BoxCollider>();
            }

            if (mTrainCarCom.RearCollisionTrigger != null)
            {
                mRearCollisionTrigger = mTrainCarCom.RearCollisionTrigger.GetComponent<TriggerTrainCollisions>();
                if (mRearCollisionTrigger == null) mRearCollisionTrigger = mTrainCarCom.RearCollisionTrigger.AddComponent<TriggerTrainCollisions>();
                mRearCollisionTrigger.location = TriggerTrainCollisions.Location.Rear;
                mRearCollisionTrigger.Owner = this;
                mRearCollisionTrigger.TrainHead = this; // default to head
                mRearCollisionTrigger.triggerCollider = mTrainCarCom.RearCollisionTrigger.GetComponent<BoxCollider>();
            }

            mFrontBogiePivot = mTrainCarCom.FrontBogiePivot;
            mRearBogiePivot = mTrainCarCom.RearBogiePivot;
            mCentreOfMassTransform = mTrainCarCom.CentreOfMassTransform;
            mFrontBogieLocalOffset = MainGo.transform.InverseTransformPoint(mFrontBogiePivot.position);
            mRearBogieLocalOffset = MainGo.transform.InverseTransformPoint(mRearBogiePivot.position);
            mDistFrontToBackWheel = Vector3.Distance(GetFrontWheelPos(), GetRearWheelPos());
            mRigidBody = BaseVehicle.rigidBody;
            mRigidBody.centerOfMass = mCentreOfMassTransform.localPosition;

            Vector3 frontPointLocalOffset = MainGo.transform.InverseTransformPoint(GetFrontOfTrainPos());
            Vector3 rearPointLocalOffset = MainGo.transform.InverseTransformPoint(GetRearOfTrainPos());
            Vector3 frontBogieLocalOffset = frontPointLocalOffset - mFrontBogieLocalOffset;
            frontBogieLocalOffset.y = 0;
            Vector3 rearBogieLocalOffset = rearPointLocalOffset - mFrontBogieLocalOffset;
            rearBogieLocalOffset.y = 0;
            DistFrontWheelToFrontCoupling = frontBogieLocalOffset.magnitude;
            DistFrontWheelToBackCoupling = rearBogieLocalOffset.magnitude;

            UpdateCompleteTrain();

            Vector3 position = MainGo.transform.position;
            // Look for a track spline nearby to attach to
            if (TryFindTrackNear(position, 15.0f, out TrainTrackSpline trackSpline, out float splineDist,
                    MgrConfigPhysicsLayer.TrainTrackLayer))
            {
                FrontWheelSplineDist = splineDist;
                if (2 == trackSpline.hierarchy && EntityId == TrainCarEntity.HeadEntityId) // 双轨
                {
                    int maxCount = 1;
                    int[] spawnTrainCountInSplie4X4 = McCommon.Tables.TbGlobalConfig.SpawnTrainCountInSplie4x4;
                    if (spawnTrainCountInSplie4X4 is { Length: > 1 }) maxCount = Mathf.Max(spawnTrainCountInSplie4X4[1], 1);

                    float length = trackSpline.GetLength();
                    float trackDist = length / maxCount; // 将length分成maxCount段
                    for (int i = 0; i < maxCount; i++)
                    {
                        float trackDistStart = i * trackDist;
                        float trackDistEnd = (i + 1) * trackDist;
                        if (trackDistStart <= splineDist && splineDist < trackDistEnd)
                        {
                            FrontWheelSplineDist = trackDistStart + (trackDistEnd - trackDistStart) * 0.75f; // 75%位置
                            logger.InfoFormat("-- TryFindTrackNear -- MainGo: {0} position:{1} trackSpline:{2} FrontWheelSplineDist:{3} ",
                                MainGo.name, position, trackSpline, FrontWheelSplineDist);
                            break;
                        }
                    }
                }

                Vector3 pos = trackSpline.GetPositionAndTangent(FrontWheelSplineDist, MainGo.transform.forward,
                    out Vector3 tangent);
                var forceForward = TrainCarEntity.SpawnType != SpawnType.SpawnGroup; // 不是火车组生成的，强制正向
                SetTheRestFromFrontWheelData(ref trackSpline, pos, tangent, mLocalTrackSelection, null, instantMove: true, forceForward: forceForward); // Use instantMove so we can check SpaceIsClear() right away
                FrontTrackSection = trackSpline;
                SyncEntityTransform();
            }
            else
            {
                logger.InfoFormat("-- TryFindTrackNear -- TrainCarGo: {0} position:{1} no track found ",
                    TrainCarEntity.EntityId, position);
            }

            _coupledToFrontTimerId = Mc.TimerWheel.AddTimerRepeat(0, 100, CoupledToFront);
            mDestroyLeftTime = destroyTimeDuration;
        }

        private void CoupledToFront(long timerid, object data, bool delete)
        {
            if (TrainCarEntity == null || TrainCarEntity.SlotIndex == 0)
                return;

            FixPointSpawner.Instance.TrainCarEntities.TryGetValue(TrainCarEntity.HeadEntityId, out LinkedList<TrainCarEntity> trainCarEntities);             // 1. 找到前一个车厢
            if (trainCarEntities == null)
                return;

            TrainCarEntity frontTrainCar = null;
            LinkedListNode<TrainCarEntity> node = trainCarEntities.Find(TrainCarEntity);
            if (node is { Previous: not null }) frontTrainCar = node.Previous.Value;

            if (frontTrainCar == null)
                return;
            
            UpdateCompleteTrain(trainCarEntities);

            if (McSimulator.Go.GetGo(frontTrainCar.EntityId) is TrainCarGo frontTrainCarGo)            // 3. 将当前车厢挂载到前一个车厢上
            {
                if (frontTrainCarGo.TrainCarCom.coupling.frontCoupling == null)
                    return;

                // 当前是链接状态，断开链接
                if (frontTrainCarGo.TrainCarCom.coupling.frontCoupling.IsCoupled || TrainCarEntity.SlotIndex == 1)
                {
                    if (mTrainCarCom.coupling.TryCouple(frontTrainCarGo.mTrainCarCom, Location.Front))
                    {
                        Vector3 forward = frontTrainCarGo.MainTransform.forward;
                        Vector3 position = frontTrainCarGo.mTrainCarCom.coupling.rearCoupling.couplingPoint.position;
                        MainTransform.forward = forward;
                        float distance = Vector3.Distance(GetFrontOfTrainPos(), GetFrontWheelPos()); // 前挂载点到前轮的距离
                        position -= forward * distance;
#if UNITY_EDITOR
                        DebugDraw.Sphere(position, 0.2f, Color.green, 60 * 10f);
#endif
                        if (TryFindTrackNear(position, 15.0f, out TrainTrackSpline trackSpline, out float splineDist, MgrConfigPhysicsLayer.TrainTrackLayer))
                        {
                            FrontWheelSplineDist = splineDist;
                            Vector3 pos = trackSpline.GetPositionAndTangent(FrontWheelSplineDist, MainTransform.forward, out Vector3 tangent);
#if UNITY_EDITOR
                            DebugDraw.Sphere(pos, 0.2f, Color.red, 60 * 10f);
#endif
                            var forceForward = TrainCarEntity.SpawnType != SpawnType.SpawnGroup; // 不是火车组生成的，强制正向
                            SetTheRestFromFrontWheelData(ref trackSpline, pos, tangent, mLocalTrackSelection, null, instantMove: true, forceForward: forceForward); // Use instantMove so we can check SpaceIsClear() right away
                            FrontTrackSection = trackSpline;
#if UNITY_EDITOR
                            DebugDraw.Sphere(MainGo.transform.position, 0.2f, Color.yellow, 60 * 10f);
#endif
                            SyncEntityTransform();
                        }
                        else
                        {
                            logger.InfoFormat("TryFindTrackNear TrainCarGo: {0} position:{1} no track found ",
                                TrainCarEntity.EntityId, position);
                        }
                    }
                }
            }
        }

        private void UpdateCompleteTrain(LinkedList<TrainCarEntity> trainCarEntities)
        {
            {
                LinkedListNode<TrainCarEntity> trainHead = trainCarEntities.First;
                if (trainHead != null && Mc.Go.GetGo(trainHead.Value.EntityId) is TrainCarGo trainCarGo)
                {
                    if (mCompleteTrain != null)
                        return;

                    mCompleteTrain = trainCarGo.CompleteTrain;
                    if (mCompleteTrain != null)
                    {
                        mCompleteTrain.AddTrainCar(this);
                        mCompleteTrain.RegisterTrainCollisionCallBack(OnCollisionTrainCallBack);
                    }
                }
            }
        }

        private void SyncEntityTransform()
        {
            TrainCarEntity.PosX = MainGo.transform.position.x;
            TrainCarEntity.PosY = MainGo.transform.position.y;
            TrainCarEntity.PosZ = MainGo.transform.position.z;
            TrainCarEntity.RotateX = MainGo.transform.eulerAngles.x;
            TrainCarEntity.RotateY = MainGo.transform.eulerAngles.y;
            TrainCarEntity.RotateZ = MainGo.transform.eulerAngles.z;
            TrainCarEntity.TrackSelection = (int)mLocalTrackSelection;
        }

        public bool IsTrainHead()
        {
            return 0 == TrainCarEntity.SlotIndex;
        }

        private void UpdateCompleteTrain()
        {
            FixPointSpawner.Instance.TrainCarEntities.TryGetValue(TrainCarEntity.HeadEntityId,
                out LinkedList<TrainCarEntity> trainCarEntities);
            if (trainCarEntities == null)
            {
                if (mCompleteTrain != null) mCompleteTrain.RegisterTrainCollisionCallBack(OnCollisionTrainCallBack); // 如果没有火车头，说明是孤立的火车车厢
                return;
            }

            if (IsTrainHead())
            {
                if (mCompleteTrain == null)
                {
                    mCompleteTrain = new CompleteTrain(this);
                    mCompleteTrain.RegisterTrainCollisionCallBack(OnCollisionTrainCallBack); // 注册碰撞回调
                }

                foreach (TrainCarEntity trainCarEntity in trainCarEntities)
                {
                    if (Mc.Go.GetGo(trainCarEntity.EntityId) is TrainCarGo trainCarGo)
                    {
                        mCompleteTrain.AddTrainCar(trainCarGo);
                        if (trainCarGo.CompleteTrain == null) trainCarGo.CompleteTrain = mCompleteTrain;
                    }
                }
            }
        }
        private long junctionRangeCheckSpan = 1000;//1s检查一次
        private long lastjunctionRangeCheckTime = 0;
        public override void FixedUpdate(int dt)
        {
            var fixedDeltaTime = dt / 1000.0f;
            base.FixedUpdate(dt);

            if (mCompleteTrain == null || IsDead())
                return;

            // If we're not the front car, we don't need to do anything
            mTrainCollisonPart = (int)CollisionTrainPart.None;

            if (0 == TrainCarEntity.SlotIndex)
            {
                mCompleteTrain.UpdateTick(fixedDeltaTime);
            }

            CheckEngineState();

            SetEntityData();

            if (IsEngineStarted(mCurEngineState))
            {
                // Use less fuel at idle, more fuel with more throttle
                float fuelPerSec = BaseFuelComponent.GetFuelConsume(Mathf.Abs(GetThrottleFraction()));
                VehicleEntity.FuelConsumption = fuelPerSec;
                BaseFuelComponent.TryUseFuel(fixedDeltaTime, fuelPerSec);
                //TrainCarEntity.Fuel = BaseFuelComponent.PendingFuel;
            }
            else
            {
                //引擎熄火 通知客户端关闭灯光
                if (VehicleEntity.LightsAreOn)
                {
                    VehicleEntity.LightsAreOn = false;
                }
                VehicleEntity.FuelConsumption = 0;
            }

            float speed = GetTrackSpeed();
            if (speed != 0 && (Mc.Time.ServerWorldTime - lastjunctionRangeCheckTime) > junctionRangeCheckSpan)
            {
                var config = Mc.Tables.TbTrainCarProperty.GetOrDefault(TrainCarEntity.TemplateId);
                var dis = InJunctionRange(config.SwitchMaxDist);
                TrainCarEntity.ChangeTrailDis = Mathf.RoundToInt(dis);
            }
        }

        private void SetEntityData()
        {
            TrainCarEntity.FrontBogieY = mFrontBogieYRot;
            TrainCarEntity.RearBogieY = mRearBogieYRot;
            var velocity = GetWorldVelocity();
            // TrainCarEntity.VelocityX = velocity.x;
            // TrainCarEntity.VelocityY = velocity.y;
            // TrainCarEntity.VelocityZ = velocity.z;
            TrainCarEntity.ThrottleFraction = (int)CurThrottleSetting;
            TrainCarEntity.TrainCollisionState = mTrainCollisonPart;
            TrainCarEntity.HasHazards = mHazardAhead;
            TrainCarEntity.EngineSpeeds = GetTrackSpeed();
            TrainCarEntity.SpeedInt = (int)TrainCarEntity.EngineSpeeds;
            TrainCarEntity.TrackSelection = (int)mLocalTrackSelection;
            TrainCarEntity.EngineState = mCurEngineState;
        }

        private void CheckEngineState()
        {
            if (mCurEngineState != TrainEngineStateEnum.Off && (!MeetsEngineRequirements() || !BaseFuelComponent.HasFuelWhenWorldResponse()))
            {
                StopEngine();
            }
        }

        public void TryStartEngine()
        {
            if (mCurEngineState != TrainEngineStateEnum.Off)
                return;

            if (!MeetsEngineRequirements())
            {
                OnEngineStartFailed();
                return;
            }
            BaseFuelComponent.CheckFuelEnough();
            mCurEngineState = TrainEngineStateEnum.Starting;
            Mc.TimerWheel.AddTimerOnce(mEngineStartupTime, FinishEngineStart, "TryStartEngine");
        }

        private void FinishEngineStart(long timerId, object data = null, bool delete = false)
        {

            if (BaseFuelComponent == null || !BaseFuelComponent.HasFuelWhenWorldResponse())
            {
                mCurEngineState = TrainEngineStateEnum.Off;
                startEnginePlayerEntity?.RemoteCallPopMessageWithNoParams(Common.Framework.Network.ERpcTarget.OwnClient, 0, 22054);
            }
            else
            {
                mCurEngineState = TrainEngineStateEnum.On;

                CheckDecay();

                if (mCheckHazardTimer != -1)
                {
                    Mc.TimerWheel.CancelTimer(mCheckHazardTimer);
                    mCheckHazardTimer = -1;
                }

                mCheckHazardTimer =
                    Mc.TimerWheel.AddTimerRepeat(HAZARD_CHECK_EVERY, HAZARD_CHECK_EVERY, CheckForHazards);
            }
        }

        private void OnEngineStartFailed()
        {
            //todo
        }

        public void StopEngine()
        {
            CurThrottleSetting = EngineSpeeds.Zero;
            if (mCheckHazardTimer != -1)
            {
                Mc.TimerWheel.CancelTimer(mCheckHazardTimer);
                mCheckHazardTimer = -1;
            }

            var oldState = mCurEngineState;
            mCurEngineState = TrainEngineStateEnum.Off;
            if (IsEngineStarted(oldState)) // 启动失败不需要刷新腐蚀状态
            {
                CheckDecay();
            }
        }

        // 立即停止引擎
        public void StopEngineImmediate()
        {
            StopEngine();
            mCompleteTrain?.StopEngineImmediate();
        }

        /*
         * We don't save or move the rear wheel spline pos like we do with the front wheels.
         * Instead, we take the front wheel pos and move it backward by the distance between the front and rear wheels.
         * This avoids any possibility of the front and rear wheel position calculations ending up the wrong distance apart.
         * 
         * There is a potential issue:
         * Say we're heading upward on a junction like this:
         *				 |
         *				/|
         * Imagine the front wheels are now on the top portion but the rear wheels are on the left-side track before the junction.
         * Since the rear wheels back-calculate from the front wheel position they'll end up snapping to the wrong track;
         * the straight one, which will be the default for no input.
         * 
         * To handle that, we pass in our preferred alternate track, the current one that the rear wheels are on.
         * 
         * On top of that, 'TrainTrackSpline additionalAlt' handles a train that's being pulled backwards by another train car.
         */
        void SetTheRestFromFrontWheelData(ref TrainTrackSpline frontTS, Vector3 targetFrontWheelPos, Vector3 targetFrontWheelTangent,
            TrackSelection trackSelection, TrainTrackSpline additionalAlt, bool instantMove, bool forceForward = false)
        {
            TrainTrackSpline rearTS;
            RearWheelSplineDist = frontTS.GetSplineDistAfterMove(FrontWheelSplineDist, MainGo.transform.forward, -mDistFrontToBackWheel,
                trackSelection, out rearTS, out bool rearAtEndOfLine, RearTrackSection, additionalAlt, forceForward);

            Vector3 targetRearWheelPos = rearTS.GetPositionAndTangent(RearWheelSplineDist, MainGo.transform.forward, out Vector3 targetRearWheelTangent);
            if (rearAtEndOfLine)
            {
                // Reacquire the correct front wheel position since the rear wheels are past the end of the line
                FrontWheelSplineDist = rearTS.GetSplineDistAfterMove(RearWheelSplineDist, MainGo.transform.forward, mDistFrontToBackWheel,
                    trackSelection, out frontTS, out _, rearTS, additionalAlt, forceForward);
                targetFrontWheelPos = frontTS.GetPositionAndTangent(FrontWheelSplineDist, MainGo.transform.forward, out targetFrontWheelTangent);
            }
            RearTrackSection = rearTS;
            Vector3 targetForwardDirection = (targetFrontWheelPos - targetRearWheelPos).normalized;
            Vector3 targetPosition = targetFrontWheelPos - (Quaternion.LookRotation(targetForwardDirection) * mFrontBogieLocalOffset);
            if (instantMove)
            {
                // Move instantly if needed (e.g. to check something about the new position right away)
                MainGo.transform.position = targetPosition;

                if (targetForwardDirection.magnitude == 0)
                {
                    MainGo.transform.rotation = Quaternion.identity;
                }
                else
                {
                    MainGo.transform.rotation = Quaternion.LookRotation(targetForwardDirection);
                }
            }
            else
            {
                // Move next physics tick
                mRigidBody.MovePosition(targetPosition);
                if (targetForwardDirection.magnitude == 0)
                {
                    mRigidBody.MoveRotation(Quaternion.identity);
                    mRigidBody.transform.SetPositionAndRotation(targetPosition, Quaternion.identity);
                }
                else
                {
                    var rot = Quaternion.LookRotation(targetForwardDirection);
                    mRigidBody.MoveRotation(rot);
                    mRigidBody.transform.SetPositionAndRotation(targetPosition, rot);
                }

            }

            // Now that we've updated the train's angle, set the local bogie wheel angles
            mFrontBogieYRot = Vector3.SignedAngle(MainGo.transform.forward, targetFrontWheelTangent, MainGo.transform.up);
            mRearBogieYRot = Vector3.SignedAngle(MainGo.transform.forward, targetRearWheelTangent, MainGo.transform.up);
        }

        public void HandleTrainUnload(ulong interactPlayerRoleId, long unloadPipe, ETrainUnloadType unloadType)
        {
            _interactPlayerRoleId = interactPlayerRoleId;
            Dictionary<long, InteractionEntity> interactionEntities = Mc.Entity.GetEntitiesViaType<InteractionEntity>();

            using (Dictionary<long, InteractionEntity>.Enumerator enumerator = interactionEntities.GetEnumerator())
            {
                while (enumerator.MoveNext())
                {
                    InteractionEntity interactionEntity = enumerator.Current.Value;
                    if (interactionEntity.TemplateId != unloadPipe)
                    {
                        continue;
                    }

                    Mc.Go.Gos.TryGetValue(interactionEntity.EntityId, out IEntityGo go);
                    if (go != null)
                    {
                        _interactionGo = go as InteractionGo;
                    }

                    break;
                }
            }

            _timerTrainDischarge = Mc.TimerWheel.AddTimerRepeat(0, McCommon.LOGIC_FRAME_20_TIME, HandleTrainUnloadCallback);
            _unloadType = unloadType;
        }

        private void SetTrainUnload(TrainCarEntity trainUnload)
        {
            _trainUnload = trainUnload;
        }

        private void HandleTrainUnloadCallback(long timerId, object data, bool delete)
        {
            FixPointSpawner.Instance.TrainCarEntities.TryGetValue(EntityId,
                out LinkedList<TrainCarEntity> trainCarEntities);

            if (trainCarEntities == null)
                return;

            switch (TrainCarEntity.TrainUnloadState)
            {
                case TrainUnloadStateEnum.None:
                    {
                         TrainCarEntity trainUnload = GetTrainUnload(trainCarEntities);
                         SetTrainUnload(trainUnload);
                         if (_trainUnload != null)
                         {
                             if (_trainUnload.TrainCom.Root.GetNodeById(NodeConst.TrainContainerNodeId) is ItemContainerNode itemContainer)
                             {
                                 if (itemContainer.GetChild(0) is BaseItemNode itemNode)
                                 {
                                     ItemConfig itemConfig = itemNode.ItemConfig;
                                     IReadOnlyDictionary<long, TrainCarUnload>
                                         dataMap = McCommon.Tables.TbTrainCarUnload.DataMap;
                                    _trainUnload.TrainCom.TryGetTrainCarUnloadData(dataMap, itemConfig.Id, _unloadType, out int _,
                                         out float _,
                                         out long unloadCrate);

                                     if (!TrainCarUtil.GetBoxUnloadByTemplateId(unloadCrate, out BoxEntity boxUnload))
                                         return;

                                     float hp = GetTrainCarHp();
                                     float maxHp = GetTrainCarMaxHp();
                                     float bloodPercentage;
                                     if (maxHp <= 0)
                                     {
                                         logger.Info("maxHp must be greater than zero.");
                                         bloodPercentage = 0f;
                                     }
                                     else
                                         bloodPercentage = Mathf.Clamp(hp / maxHp, 0f, 1f);
                                     
                                    _trainUnload.TrainCom.RemoteCallHandleTrainUnloadRpc(ERpcTarget.World, (int)_unloadType, boxUnload.EntityId, _interactPlayerRoleId, bloodPercentage * 100);
                                     logger.InfoFormat("TrainCarGo: {0} unloadType: {1} unloadCrate: {2} bloodPercentage: {3}", EntityId, _unloadType, unloadCrate, bloodPercentage * 100);
                                 }
                             }

                             _trainUnloadDot = 0;
                             TrainCarEntity.TrainUnloadState = TrainUnloadStateEnum.AutoParking;
                         }

                         break;
                    }
                case TrainUnloadStateEnum.AutoParking:
                    {
                        LinkedListNode<TrainCarEntity> node = trainCarEntities.First;
                        while (node != null)
                        {
                            TrainCarEntity trainCarEntity = node.Value;
                            if (McSimulator.Go.GetGo(trainCarEntity.EntityId) is TrainCarGo { IsSleep: true } trainCarGo)
                                trainCarGo.WakeUp(MountableWakeupReason.AutoParking);
                            node = node.Next;
                        }

                        Vector3 forward = MainTransform.forward;                        // 当前火车头的方向
                        Vector3 position = _interactionGo.MainTransform.position;
                        if (Mc.Go.Gos.TryGetValue(_trainUnload.EntityId, out IEntityGo go))
                        {
                            if (go is TrainCarGo trainCarGo)
                            {
                                Transform trainUnloadPos = trainCarGo.TrainCarCom.TrainUnloadPos;
                                Vector3 direction = (position - trainUnloadPos.position).ClearY();
                                CurThrottleSetting = Vector3.Dot(forward, direction.normalized) > 0
                                    ? EngineSpeeds.Fwd_Lo
                                    : EngineSpeeds.Rev_Lo;
                                if (0 > _trainUnloadDot * Vector3.Dot(forward, direction.normalized))
                                {
                                    StopEngineImmediate();
                                    TrainCarEntity.TrainUnloadState = TrainUnloadStateEnum.Unloading;
                                    _currentUnloadPercentage = GetUnloadPercentage(_trainUnload);

                                    int trainUnloadType = 0;
                                    var tb = McCommon.Tables.TbTrainCarProperty.GetOrDefault(_trainUnload.TemplateId);
                                    if (tb != null)
                                    {
                                        trainUnloadType = tb.TrainType;
                                    }
                                    int trainUnloadCount = _trainUnload.TrainCom.CalculateTrainUnloadCount();
                                    _interactionGo.UpdateTrainUnload(trainUnloadCount, trainUnloadType);

                                    if (_timerStopDischarge != TimerWheelConst.INVALID_TIMER_ID)
                                    {
                                        Mc.TimerWheel.CancelTimer(_timerStopDischarge);
                                        _timerStopDischarge = TimerWheelConst.INVALID_TIMER_ID;
                                    }
                                }
                                _trainUnloadDot = Vector3.Dot(forward, direction.normalized);
                            }
                        }
                        break;
                    }
                case TrainUnloadStateEnum.Unloading:
                    {
                        int trainUnloadCount = _trainUnload.TrainCom.CalculateTrainUnloadCount();
                        float targetUnloadPercentage = 0;
                        if (1 <= _trainUnload.TrainUnloadCount)
                        {
                            targetUnloadPercentage = (float)trainUnloadCount / _trainUnload.TrainUnloadCount;
                            targetUnloadPercentage = Mathf.Clamp(targetUnloadPercentage, 0f, 1f) * 100f;
                        }
                        _currentUnloadPercentage = Mathf.Lerp(_currentUnloadPercentage, targetUnloadPercentage, 0.1f); // 使用线性插值让当前卸货进度逐渐接近目标值
                        UpdateOrePlane();
                        if (trainUnloadCount <= 0)
                        {
                            if (_timerStopDischarge == TimerWheelConst.INVALID_TIMER_ID)
                            {
                                _interactionGo.UpdateTrainUnload(0, 0);
                                _timerStopDischarge = Mc.TimerWheel.AddTimerOnce(5000, StopDischarge);
                            }
                        }
                    }
                    break;
            }
        }

        private void StopDischarge(long timerid, object data, bool delete)
        {
            TrainCarEntity.TrainUnloadState = TrainUnloadStateEnum.None;
            SetTrainUnload(null);
            StopEngine();
        }

        private void UpdateOrePlane()
        {
            try
            {
                _trainUnload.TrainUnloadPercentage = _currentUnloadPercentage; // 更新卸货进度
                if (Mc.Go.Gos.TryGetValue(_trainUnload.EntityId, out IEntityGo go))
                {
                    if (go is TrainCarGo trainCarGo && trainCarGo.TrainCarCom != null) trainCarGo.TrainCarCom.UpdateOrePlane(_currentUnloadPercentage * 0.01f);
                }
            }
            catch
            {
                // ignored
            }
        }

        // 获取火车组血量
        public float GetTrainCarHp()
        {
            FixPointSpawner.Instance.TrainCarEntities.TryGetValue(EntityId,
                out LinkedList<TrainCarEntity> trainCarEntities);
            if (trainCarEntities == null)
            {
                return 0;
            }
            float hp = 0;
            foreach (TrainCarEntity trainCarEntity in trainCarEntities)
            {
                hp += trainCarEntity.Hp;
            }
            return hp;
        }

        // 获取火车组最大血量
        public float GetTrainCarMaxHp()
        {
            FixPointSpawner.Instance.TrainCarEntities.TryGetValue(EntityId,
                out LinkedList<TrainCarEntity> trainCarEntities);
            if (trainCarEntities == null)
            {
                return 0;
            }
            float maxHp = 0;
            foreach (TrainCarEntity trainCarEntity in trainCarEntities)
            {
                maxHp += trainCarEntity.MaxHp;
            }
            return maxHp;
        }

        private TrainCarEntity GetTrainUnload(LinkedList<TrainCarEntity> trainCarEntities)
        {
            LinkedListNode<TrainCarEntity> node = trainCarEntities.First;
            if (node == null)
            {
                return null;
            }

            node = node.Next;
            while (node != null)
            {
                if (node.Value.TrainCom != null && node.Value.TrainCom.CalculateTrainUnloadCount() > 0)
                    return node.Value;

                node = node.Next;
            }

            return null;
        }

        public Vector3 GetWorldVelocity()
        {
            return MainGo.transform.forward * GetTrackSpeed();
        }

        public float GetTrackSpeed()
        {
            if (mCompleteTrain == null)
                return 0f;

            return mCompleteTrain.GetTrackSpeedFor(this);
        }

        Vector3 GetFrontWheelPos()
        {
            return MainGo.transform.position + MainGo.transform.rotation * mFrontBogieLocalOffset;
        }

        Vector3 GetRearWheelPos()
        {
            return MainGo.transform.position + MainGo.transform.rotation * mRearBogieLocalOffset;
        }

        public float GetForces()
        {
            float totalForces = 0f;

            // Gravity on sloped track
            float angle = MainGo.transform.localEulerAngles.x;
            if (angle > 180)
                angle -= 360;
            totalForces += angle / 90 * -Physics.gravity.y * RealisticMass;

            totalForces += GetThrottleForce();

            return totalForces;
        }

        protected float GetThrottleForce()
        {
            if (IsDead())
                return 0f;

            float result = 0f;
            // Apply engine forces
            float throttleFraction = GetThrottleFraction();
            float desiredSpeed = maxSpeed * throttleFraction;
            // Allow health level to reduce train top speed, without reducing lower-speed settings
            float curTopSpeed = GetCurTopSpeed();
            desiredSpeed = Mathf.Clamp(desiredSpeed, -curTopSpeed, curTopSpeed);
            float trackSpeed = GetTrackSpeed();
            if (throttleFraction > 0 && trackSpeed < desiredSpeed)
            {
                result += GetCurEngineForce();
            }
            else if (throttleFraction < 0 && trackSpeed > desiredSpeed)
            {
                result -= GetCurEngineForce();
            }
            return result;
        }

        public float GetThrottleFraction()
        {
            switch (CurThrottleSetting)
            {
                case EngineSpeeds.Rev_Hi:
                    return -1.0f;
                case EngineSpeeds.Rev_Med:
                    return -0.5f;
                case EngineSpeeds.Rev_Lo:
                    return -0.2f;
                case EngineSpeeds.Zero:
                    return 0.0f;
                case EngineSpeeds.Fwd_Lo:
                    return 0.2f;
                case EngineSpeeds.Fwd_Med:
                    return 0.5f;
                case EngineSpeeds.Fwd_Hi:
                    return 1.0f;
            }
            return 0.0f;
        }

        float GetCurTopSpeed()
        {
            float result = maxSpeed * GetEnginePowerMultiplier(0.5f);

            return result;
        }

        public float GetPrevTrackSpeed()
        {
            if (mCompleteTrain == null)
                return 0f;

            return mCompleteTrain.GetPrevTrackSpeedFor(this);
        }

        float GetCurEngineForce()
        {
            return engineForce * GetEnginePowerMultiplier(0.75f);
        }

        float GetEnginePowerMultiplier(float minPercent)
        {
            const float START = 0.4f;
            if (healthFraction > START)
            {
                return 1.0f;
            }
            return Mathf.Lerp(minPercent, 1.0f, healthFraction / START);
        }

        private float healthFraction
        {
            get { return 1.0f; }
        }

        private bool IsDead()
        {
            return mIsDead;
        }

        public float RealisticMass
        {
            get
            {
                if (mRigidBody != null)
                    return mRigidBody.mass;
                return 0;

            }
        }

        public void OtherTrainCarTick(TrainTrackSpline theirTrackSpline, float prevSplineDist, float distanceOffset)
        {
            MoveFrontWheelsAlongTrackSpline(theirTrackSpline, prevSplineDist, distanceOffset, FrontTrackSection, TrackSelection.Default);
        }

        public void FrontTrainCarTick(TrackSelection trackSelection, float dt)
        {
            float distToMove = GetTrackSpeed() * dt;
            // If the rear wheels have already gone across to a new spline section,
            // force the front wheels to "select" that track piece instead of letting the player choose
            // left/right, which could snap the rear wheels to the wrong piece if the front wheels aren't across yet.
            // Similar to the problem described in the comments on SetTheRestFromFrontWheelData, but
            // with the train reversing down from the top instead.
            TrainTrackSpline preferredAltTrack = RearTrackSection != FrontTrackSection ? RearTrackSection : null;

            MoveFrontWheelsAlongTrackSpline(FrontTrackSection, FrontWheelSplineDist, distToMove, preferredAltTrack, trackSelection);
        }

        void MoveFrontWheelsAlongTrackSpline(TrainTrackSpline trackSpline, float prevSplineDist, float distToMove, TrainTrackSpline preferredAltTrack, TrackSelection trackSelection)
        {
            if (trackSpline == null || MainGo == null)
                return;
            TrainTrackSpline frontTS;
            FrontWheelSplineDist = trackSpline.GetSplineDistAfterMove(prevSplineDist, MainGo.transform.forward, distToMove, trackSelection, out frontTS, out bool _, preferredAltTrack, null);
            Vector3 targetFrontWheelPos = frontTS.GetPositionAndTangent(FrontWheelSplineDist, MainGo.transform.forward, out Vector3 targetFrontWheelTangent);

            SetTheRestFromFrontWheelData(ref frontTS, targetFrontWheelPos, targetFrontWheelTangent, trackSelection, trackSpline, instantMove: false);
            FrontTrackSection = frontTS;
        }

        public bool AnyPlayersOnTrainCar()
        {
            if (TrainCarEntity == null)
                return false;
            if (HasDriver())
                return true;
            var data = TrainCarEntity.GetChildrenList();
            if (data == null)
                return false;

            return data.Count > 0;
        }

        public float ApplyCollisionDamage(float forceMagnitude)
        {
            float damage;
            if (forceMagnitude > derailCollisionForce)
            {
                // Big collision, just destroy the train (which will also derail it)
                damage = float.MaxValue;
            }
            else
            {
                // Curve damage upwards rather than have it linear
                damage = Mathf.Pow(forceMagnitude, 1.3f) / collisionDamageDivide;
            }

            Hurt(damage);

            return damage;
        }
        private void Hurt(float damage)
        {
            var hitRequest = HitRequest.Generate(0, TrainCarEntity);
            hitRequest.AddDamage(DamageType.Collision, damage);
            McCommon.SystemRequestMgr.AddRequest(ESystemRequest.HitRequestSet, hitRequest);
        }
        public override void Remove()
        {
            StopEngine();
            if (mDestroyTimerID != -1)
            {
                Mc.TimerWheel.CancelTimer(mDestroyTimerID);
                mDestroyTimerID = -1;
            }
            if (mFrontTrackSection != null)
                mFrontTrackSection.DeregisterTrackUser(this);//把当前火车从当前铁轨上移除

            if (_coupledToFrontTimerId != -1)
            {
                _coupledToFrontTimerId = -1;
                Mc.TimerWheel.CancelTimer(_coupledToFrontTimerId);
            }

            if (_timerTrainDischarge != TimerWheelConst.INVALID_TIMER_ID)
            {
                Mc.TimerWheel.CancelTimer(_timerTrainDischarge);
                _timerTrainDischarge = TimerWheelConst.INVALID_TIMER_ID;
            }

            if (_timerStopDischarge != TimerWheelConst.INVALID_TIMER_ID)
            {
                Mc.TimerWheel.CancelTimer(_timerStopDischarge);
                _timerStopDischarge = TimerWheelConst.INVALID_TIMER_ID;
            }

            mCompleteTrain = null;
            base.Remove();
        }

        #region override

        protected override PlayerEntity GetPlayerEntity(long playerId)
        {
            return Mc.Entity.GetPlayerEntity(playerId);
        }

        public override void MountPlayer(long playerId, SeatType seatType, int seatIndex, bool reload = false)
        {
            base.MountPlayer(playerId, seatType, seatIndex);
            try
            {
                if (IsTrainHead())
                {
                    FixPointSpawner.Instance.TrainCarEntities.TryGetValue(TrainCarEntity.HeadEntityId,
                        out LinkedList<TrainCarEntity> trainCarEntities);

                    if (trainCarEntities != null)
                    {
                        LinkedListNode<TrainCarEntity> node = trainCarEntities.First;
                        node = node.Next; // Skip the first node, which is the head train car

                        while (node != null)
                        {
                            TrainCarEntity trainCarEntity = node.Value;
                            if (McSimulator.Go.GetGo(trainCarEntity.EntityId) is TrainCarGo
                                {
                                    IsSleep: true
                                } trainCarGo)
                            {
                                trainCarGo.WakeUp(MountableWakeupReason.GotDriver);
                            }

                            node = node.Next;
                        }
                    }
                }
            }
            catch
            {
                // ignored
            }

            TryStartEngine();
        }

        protected override void GetInVehicle(PlayerEntity playerEntity, long vehicleEntityId, Vector3 inPos, int seatIndex = 0)
        {
            MountableUtil.GetInVehicle(playerEntity, EntityId, inPos, seatIndex);
        }

        protected override void GetOutVehicle(IMountVehicleEntity vehicleEntity, Vector3 outPos, bool isInsideMount)
        {
            MountableUtil.GetOutVehicle(vehicleEntity, outPos, isInsideMount);
        }

        public override void Update(Context context, IEntity entity, UserCmd cmd, IChangeWorld changeWorld,
            int deltaTime, bool isFrameFiller = false, PlayerLogicParams playerLogicParams = null, bool hasOtherMountableOrPlayerNearby = false)
        {
            base.Update(context, entity, cmd, changeWorld, deltaTime, isFrameFiller, playerLogicParams, hasOtherMountableOrPlayerNearby);
            if (mCompleteTrain != null)
            {
                HandlePlayerInputCmd(cmd, entity as PlayerEntity);
            }
        }
        private long mInputHandleSpan = 300;//1s 处理一个次
        private long mLastHandleInputTimeStamp = 0;
        private PlayerEntity startEnginePlayerEntity = null;
        public float DistFrontWheelToFrontCoupling;
        public float DistFrontWheelToBackCoupling;

        private long _timerTrainDischarge = TimerWheelConst.INVALID_TIMER_ID;
        private long _timerStopDischarge = TimerWheelConst.INVALID_TIMER_ID;
        private TrainCarEntity _trainUnload;
        private InteractionGo _interactionGo;
        private float _trainUnloadDot;
        private ulong _interactPlayerRoleId;
        private ETrainUnloadType _unloadType;
        private float _currentUnloadPercentage;

        void HandlePlayerInputCmd(UserCmd cmd, PlayerEntity playerEntity)
        {
            if (!cmd.MoveForward && !cmd.MoveBackward && !cmd.MoveLeft && !cmd.MoveRight)
            {
                return;
            }
            var time = Mc.Time.ServerWorldTime;
            if (time - mLastHandleInputTimeStamp <= mInputHandleSpan)
                return;
            mLastHandleInputTimeStamp = time;

            StartDestroyTimeCount();

            if (mCurEngineState == TrainEngineStateEnum.Off)
            {
                startEnginePlayerEntity = playerEntity;
                TryStartEngine();
            }
            else
            {
                if (cmd.MoveForward)
                {
                    if (CurThrottleSetting < EngineSpeeds.Fwd_Hi)
                    {
                        if (mCurEngineState == TrainEngineStateEnum.On)
                        {// 启动
                            OnEngineRunning(playerEntity);
                        }
                        CurThrottleSetting = CurThrottleSetting + 1;
                        SocUtility.Logger.InfoFormat("[HandlePlayerInputCmd]挡位： {0}", CurThrottleSetting);
                    }
                }

                if (cmd.MoveBackward)
                {
                    if (CurThrottleSetting > (int)EngineSpeeds.Rev_Hi)
                    {
                        if (mCurEngineState == TrainEngineStateEnum.On)
                        {// 启动
                            OnEngineRunning(playerEntity);
                        }
                        CurThrottleSetting = CurThrottleSetting - 1;
                        SocUtility.Logger.InfoFormat("[HandlePlayerInputCmd]挡位： {0}", CurThrottleSetting);
                    }
                }

                if (cmd.MoveLeft)
                {
                    if (mLocalTrackSelection != TrackSelection.Left)
                        mLocalTrackSelection = TrackSelection.Left;
                    SocUtility.Logger.InfoFormat("[HandlePlayerInputCmd]轨道选择： {0}", mLocalTrackSelection);
                }
                else if (cmd.MoveRight)
                {
                    if (mLocalTrackSelection != TrackSelection.Right)
                        mLocalTrackSelection = TrackSelection.Right;
                    SocUtility.Logger.InfoFormat("[HandlePlayerInputCmd]轨道选择： {0}", mLocalTrackSelection);
                }
            }
        }

        // 引擎启动
        private bool IsEngineStarted(TrainEngineStateEnum engineState)
        {
            return engineState == TrainEngineStateEnum.On || engineState == TrainEngineStateEnum.Running;
        }

        private void OnEngineRunning(PlayerEntity playerEntity)
        {
            try
            {
                mCurEngineState = TrainEngineStateEnum.Running;
                if (playerEntity != null)
                {
                    playerEntity.RemoteCallStartTheEngineOfSubwayTrain(ERpcTarget.World, EntityId);
                    logger.InfoFormat("TrainCarGo: {0} OnEngineStart", EntityId);
                }
                else
                    logger.InfoFormat("TrainCarGo: {0} OnEngineStart without player", EntityId);
            }
            catch (Exception e)
            {
                logger.ErrorFormat("TrainCarGo: {0} OnEngineStart error: {1}", EntityId, e);
            }
        }

        public bool MeetsEngineRequirements()
        {
            // Let the engine run with no driver in the seat, but stop it automatically if they leave with the throttle set to zero (save fuel from idling)
            if (!HasDriver() && CurThrottleSetting == EngineSpeeds.Zero)
            {
                return false;
            }

            if (TrainCarEntity.TrainUnloadState != TrainUnloadStateEnum.None)
            {
                startEnginePlayerEntity?.RemoteCallShowTips(ERpcTarget.OwnClient, 24183);
                return false;
            }

            var ret = mCompleteTrain.AnyPlayersOnTrain();
            if (!ret)
                Debug.Log("[TriggerTrainPlatform]没有人在平台上");
            return ret;
        }

        void CheckForHazards(long timerId, object data = null, bool delete = false)
        {
            float trackSpeed = GetTrackSpeed();
            // Check for hazards ahead //检查前方的危险信息
            if (trackSpeed > HAZARD_SPEED_MIN || trackSpeed < -HAZARD_SPEED_MIN)
            {
                // Base max hazard warning distance off the train's current speed
                float maxHazardDist = Mathf.Lerp(hazardDistMin, hazardDistMax, Mathf.Abs(trackSpeed) * 0.05f);
                // Always using the front wheel pos, since that's what we use to set which spline this train is currently on
                mHazardAhead = HasValidHazardWithin(FrontWheelSplineDist, hazardDistMin, maxHazardDist, mLocalTrackSelection, trackSpeed, RearTrackSection, null);
            }
            else
            {
                mHazardAhead = false;
            }
        }

        public bool HasValidHazardWithin(float askerSplineDist, float minHazardDist,
            float maxHazardDist, TrackSelection trackSelection, float trackSpeed, TrainTrackSpline preferredAltA, TrainTrackSpline preferredAltB)
        {

            if (mFrontTrackSection == null || MainGo == null)
                return false;

            Vector3 askerForward = trackSpeed >= 0 ? MainGo.transform.forward : -MainGo.transform.forward;

            bool movingForward = mFrontTrackSection.IsForward(askerForward, askerSplineDist);

            var result = HasValidHazardWithin(this, mFrontTrackSection, askerForward, askerSplineDist, minHazardDist, maxHazardDist,
                trackSelection, movingForward, preferredAltA, preferredAltB);
            return result;
        }

        public bool HasValidHazardWithin(ITrainTrackUser asker, TrainTrackSpline trainTrackSpline, Vector3 askerForward, float askerSplineDist, float minHazardDist,
      float maxHazardDist, TrackSelection trackSelection, bool movingForward, TrainTrackSpline preferredAltA, TrainTrackSpline preferredAltB)
        {
            if (trainTrackSpline == null)
                return false;

            WorldSplineData data = trainTrackSpline.GetData();

            if (data == null)
                return false;

            // Is another train coming up ahead, if we continue on our current path?
            foreach (ITrainTrackUser trackUser in trainTrackSpline.trackUsers)
            {
                if (trackUser == null || trackUser == asker)
                    continue;
                // Rather than doing complex distance calc using spline distance data,
                // just check the actual distance to them
                Vector3 vectorToThem = trackUser.Position - asker.Position;
                bool inFrontOfUs = Vector3.Dot(askerForward, vectorToThem) >= 0;
                // Is the other train in the direction we're looking?
                if (inFrontOfUs)
                {
                    float distance = vectorToThem.magnitude;
                    if (distance > minHazardDist && distance < maxHazardDist)
                    {
                        // Helk's special check. Ignore hazards that are moving away from us (in absolute terms, not relative).
                        // Intention is to allow for chasing someone in another train without a hazard warning going off the whole time.
                        // TODO: Should be able to check TrackSpeed here, but need to account for spline directions.
                        Vector3 userVel = trackUser.GetWorldVelocity();
                        const float MIN_IGNORE_VEL = 2.0f;
                        const float MIN_IGNORE_VEL_SQR = MIN_IGNORE_VEL * MIN_IGNORE_VEL;
                        if (userVel.sqrMagnitude < MIN_IGNORE_VEL_SQR || Vector3.Dot(userVel, vectorToThem) < 0)
                        {
                            // Hazard found!
                            return true;
                        }
                    }
                }
            }

            // We've checked this spline, now check recursively through connected splines until we're at maxHazardDist
            float newMinDist = movingForward ? askerSplineDist + minHazardDist : askerSplineDist - minHazardDist;
            float newMaxDist = movingForward ? askerSplineDist + maxHazardDist : askerSplineDist - maxHazardDist;
            if (newMaxDist < 0)
            {
                // Check the previous track section
                if (trainTrackSpline.HasPrevTrack)
                {
                    ConnectedTrackInfo prevTrack = trainTrackSpline.GetTrackSelection(trainTrackSpline.prevTracks, trainTrackSpline.straightestPrevIndex, trackSelection,
                        nextTrack: false, movingForward, preferredAltA, preferredAltB);

                    if (prevTrack == null)
                        return false;

                    // Set prev pos to the start of the new piece of track,
                    // which will either be the start or the end of it depending on the orientation it's in relative to us
                    if (prevTrack.orientation == TrackOrientation.Same)
                    {
                        if (prevTrack.track != null)
                            askerSplineDist = prevTrack.track.GetLength();
                    }
                    else
                    {
                        askerSplineDist = 0;
                        movingForward = !movingForward;
                    }

                    float minDistPastEnd = Mathf.Max(-newMinDist, 0);
                    float maxDistPastEnd = -newMaxDist;
                    // Call recursively into the next track section
                    return HasValidHazardWithin(asker, prevTrack.track, askerForward, askerSplineDist, minDistPastEnd, maxDistPastEnd,
                        trackSelection, movingForward, preferredAltA, preferredAltB);
                }
            }
            else if (newMaxDist > data.Length)
            {
                // Check the next track section
                if (trainTrackSpline.HasNextTrack)
                {
                    ConnectedTrackInfo nextTrack = trainTrackSpline.GetTrackSelection(trainTrackSpline.nextTracks, trainTrackSpline.straightestNextIndex, trackSelection,
                        nextTrack: true, movingForward, preferredAltA, preferredAltB);

                    if (nextTrack == null)
                        return false;

                    if (nextTrack.orientation == TrackOrientation.Same)
                    {
                        askerSplineDist = 0;
                    }
                    else
                    {
                        if (nextTrack.track != null)
                            askerSplineDist = nextTrack.track.GetLength();
                        movingForward = !movingForward;
                    }
                    float minDistPastEnd = Mathf.Max(newMinDist - data.Length, 0);
                    float maxDistPastEnd = newMaxDist - data.Length;
                    // Call recursively into the next track section
                    return HasValidHazardWithin(asker, nextTrack.track, askerForward, askerSplineDist, minDistPastEnd, maxDistPastEnd,
                        trackSelection, movingForward, preferredAltA, preferredAltB);
                }
            }

            return false;
        }

        private float InJunctionRange(float rangeDis)
        {
            float trackSpeed = GetTrackSpeed();//正向 > 0 反向 < 0 停止 = 0
            if (trackSpeed == 0)
                return -1;
            var trackDis = trackSpeed >= 0 ? FrontWheelSplineDist : RearWheelSplineDist;
            var trackSpline = trackSpeed >= 0 ? FrontTrackSection : RearTrackSection;

            Vector3 askerForward = trackSpeed >= 0 ? MainGo.transform.forward : -MainGo.transform.forward;

            bool movingForward = trackSpline.IsForward(askerForward, trackDis);
            var length = trackSpline.GetLength();

            bool isFindJunction = false;
            float dis2NextJunk = 0;
            if (movingForward)
            {
                dis2NextJunk = length - trackDis;
                while (dis2NextJunk < rangeDis)
                {
                    if (trackSpline.nextTracks == null || trackSpline.nextTracks.Count == 0)
                        break;
                    if (trackSpline.nextTracks.Count == 1)
                    {
                        dis2NextJunk += trackSpline.nextTracks[0].track.GetLength();
                        trackSpline = trackSpline.nextTracks[0].track;
                    }
                    else
                    {
                        isFindJunction = true;
                        break;
                    }
                }
            }
            else
            {
                dis2NextJunk = trackDis;
                while (dis2NextJunk < rangeDis)
                {
                    if (trackSpline.prevTracks == null || trackSpline.prevTracks.Count == 0)
                        break;
                    if (trackSpline.prevTracks.Count == 1)
                    {
                        dis2NextJunk += trackSpline.prevTracks[0].track.GetLength();
                        trackSpline = trackSpline.prevTracks[0].track;
                    }
                    else
                    {
                        isFindJunction = true;
                        break;
                    }
                }
            }
            if (isFindJunction)
                return dis2NextJunk;
            return -1;
        }

        public Vector3 GetRearOfTrainPos()
        {
            return mTrainCarCom.RearPoint.transform.position;
        }

        public Vector3 GetFrontOfTrainPos()
        {
            return mTrainCarCom.FrontPoint.transform.position;
        }

        private void UpdateDestoryState(long timerId, object data = null, bool delete = false)
        {
            if (IsDead())
                return;



            if (mIsCanDestroy)
            {
                if (!IsExistPlayerInTargetRange(mTrainDestroyDis))
                {
                    DestroySelf();
                }
                return;
            }

            if (!mStartDestroyTimeCount)
                return;

            var existPlayers = AnyPlayersOnTrainCar();

            if (existPlayers)
            {
                mPauseDestroyTimeCount = true;
                return;
            }
            else
            {
                if (mPauseDestroyTimeCount)
                {
                    mLastDestroyTimeStamp = Mc.Time.ServerWorldTime;
                    mPauseDestroyTimeCount = false;
                }
            }

            if (!mPauseDestroyTimeCount)
            {
                if (mDestroyLeftTime <= 0)
                {
                    mStartDestroyTimeCount = false;
                    mIsCanDestroy = true;
                    return;
                }
                var pastTime = Mc.Time.ServerWorldTime - mLastDestroyTimeStamp;
                mDestroyLeftTime -= pastTime;
                mLastDestroyTimeStamp = Mc.Time.ServerWorldTime;
            }
        }

        private void DestroySelf()
        {
            mIsDead = true;

            FixPointSpawner.Instance.TrainCarEntities.TryGetValue(EntityId, out LinkedList<TrainCarEntity> trainCarEntities); // EntityId为火车头的ID
            if (trainCarEntities == null)
            {
                logger.InfoFormat("DestroySelf trainCarEntities is null");
                return;
            }

            try
            {
                TrainCarUtil.ShowTrainDestroyTips(_interactPlayerRoleId);
                OnTrainDischargeInterrupt();
            }
            catch (Exception e)
            {
                logger.InfoFormat("DestroySelf OnTrainDischargeInterrupt error: {0}", e);
            }

            foreach (var trainCarEntity in trainCarEntities)
            {
                if (trainCarEntity == null)
                    continue;
                ChangeWorldUtility.DeleteEntity(trainCarEntity);
            }
            FixPointSpawner.Instance.TrainCarEntities.Remove(EntityId);
            logger.InfoFormat("DestroySelf trainCarEntities count: {0}, EntityId: {1}", trainCarEntities.Count, EntityId);
        }

        private void StartDestroyTimeCount()
        {
            if (mStartDestroyTimeCount)
                return;
            mLastDestroyTimeStamp = Mc.Time.ServerWorldTime;
            mDestroyTimerID = Mc.TimerWheel.AddTimerRepeat(1000, 1000, UpdateDestoryState);
            mStartDestroyTimeCount = true;
            mPauseDestroyTimeCount = false;
        }

        private bool IsExistPlayerInTargetRange(float range)
        {
            Dictionary<long, PlayerEntity> players = Mc.Entity.PlayerEntities;

            if (players == null || players.Count == 0)
                return false;

            if (MainTransform == null)
                return false;

            Vector3 trainPos = MainTransform.position;

            foreach (var data in players)
            {
                var player = data.Value;
                if (player != null && player is { IsDead: false })
                {
                    var playerPos = new Vector3(player.PosX, player.PosY, player.PosZ);
                    if (Vector3.Distance(playerPos, trainPos) < range)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        private void OnCollisionTrainCallBack(bool isFront)
        {
            if (isFront)
            {
                mTrainCollisonPart = (int)CollisionTrainPart.Front;
            }
            else
            {
                mTrainCollisonPart = (int)CollisionTrainPart.Rear;
            }

            Mc.Go.Gos.TryGetValue(TrainCarEntity.HeadEntityId, out var go);
            if (go == null)
            {
                logger.InfoFormat("OnTrainDischargeInterrupt go is null");
                return;
            }

            if (go is TrainCarGo trainCarGo)
            {
                TrainCarUtil.ShowTrainHitTips(_interactPlayerRoleId);
                trainCarGo.OnTrainDischargeInterrupt();
            }
            else
            {
                logger.InfoFormat("OnTrainDischargeInterrupt go is not TrainCarGo");
            }
        }

        public void OnTrainDischargeInterrupt()
        {
            if (_trainUnload != null)
            {
                _trainUnload.TryGetComponent(EComponentIdEnum.TrainComponent, out TrainComponent com);
                com?.RemoteCallHandleInterruptUnloadRpc(ERpcTarget.World);
            }

            DischargeInterrupt();
        }

        public void DischargeInterrupt()
        {
            if (_timerTrainDischarge != TimerWheelConst.INVALID_TIMER_ID)
            {
                Mc.TimerWheel.CancelTimer(_timerTrainDischarge);
                _timerTrainDischarge = TimerWheelConst.INVALID_TIMER_ID;
            }

            if (_timerStopDischarge != TimerWheelConst.INVALID_TIMER_ID)
            {
                Mc.TimerWheel.CancelTimer(_timerStopDischarge);
                _timerStopDischarge = TimerWheelConst.INVALID_TIMER_ID;
            }

            _interactionGo?.UpdateTrainUnload(0, 0);
            TrainCarEntity.TrainUnloadState = TrainUnloadStateEnum.None;
            SetTrainUnload(null);
        }


        public override void OpenVehicleLight(bool isOpen)
        {
            if (!HasAnyMounted())
                return;
            if (TrainCarEntity.EngineState == TrainEngineStateEnum.Off)
                return;
            VehicleEntity.LightsAreOn = isOpen;
        }
        #endregion

        protected override bool IsEngineOn => IsEngineStarted(mCurEngineState);

        public override bool IgnoreDecay()
        {
            return TrainCarEntity.GroupTotalUnloadCount > 0;
        }

        public void OnDischargeInterrupt(int code)
        {
            TrainCarUtil.ShowTrainFullTips(_interactPlayerRoleId);
            DischargeInterrupt();
        }

        public override float OnHurt(IEntity source, float damage, HitRequest hitRequest)
        {
            var result = base.OnHurt(source, damage, hitRequest);
            if (FixPointSpawner.Instance != null 
                && FixPointSpawner.Instance.TrainCarEntities != null 
                && FixPointSpawner.Instance.TrainCarEntities.TryGetValue(MountableEntity.EntityId, out var group))
            {
                foreach (var entity in group)
                {
                    entity.VehicleLastAttackedTime = MountableEntity.VehicleLastAttackedTime;
                }
            }
            return result;
        }
    }
}