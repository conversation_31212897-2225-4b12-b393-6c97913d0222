using FairyGUI;
using System;
using System.Collections.Generic;
using WizardGames.Soc.SocClient.Manager;
#if POCO
using Cysharp.Text;
#endif

namespace WizardGames.Soc.SocClient.Ui
{    
    /// <summary>
    /// 页签红点类型
    /// </summary>
    public enum ENavTabRedDotType
    {
        Dot,//圆点
        Exclamation,//感叹号
        Num,//数字
        custom,//自定义
    }
    public class NavBarItemBinder
    {
        public GComponent Root { get; private set; }
        public Controller ShowIconCtr { get; private set; }
        public GLoader BigIcon { get; private set; }
        public Controller TriangleCtrl { get; private set; }
        public Controller ButtonCtrl { get; private set; }
        public Controller TranslucentCtrl { get; private set; }
        public Controller BtnStyleCtr { get; private set; }
        public ComRedDot ComRedDot { get; private set; }
        public GTextField Num { get; private set; }
        public Controller LockCtrl { get; private set; }
        public Controller UnfoldCtrl { get; private set; }
        public Controller IsFinalCtrl { get; private set; }
        public NavBarItemBinder(GComponent root)
        {
            Root = root;
            ShowIconCtr = root.GetController("showIcon");
            BigIcon = root.GetChild("bigIcon")?.asLoader;
            TriangleCtrl = root.GetController("triangle");
            ButtonCtrl = root.GetController("button");
            TranslucentCtrl = root.GetController("translucent");
            BtnStyleCtr = root.GetController("btnStyle");
            ComRedDot = root.GetChild("redDot") as ComRedDot;
            Num = root.GetChild("num")?.asTextField;
            LockCtrl = root.GetController("lock");
            UnfoldCtrl = root.GetController("unfold");
            IsFinalCtrl = root.GetController("isFinal");
        }
    }

    public partial class ComCommonNavBar
    {
        /// <summary>
        /// 是否总是展开。设定为true后，点击已展开一级标签不会收起(暂时没这个需求)
        /// </summary>
        public bool AlwaysUnfold { get; set; } = true;
        /// <summary>
        /// 一级页签列表
        /// </summary>
        protected GList listCom;
        /// <summary>
        /// 一级页签列表数据
        /// </summary>
        protected List<NavBarData> showNavBarDataList;
        /// <summary>
        /// 一级页签选中索引
        /// </summary>
        protected int selectedIndex = -1;
        /// <summary>
        /// 二级页签列表
        /// </summary>
        protected GList listCom2;
        /// <summary>
        /// 二级页签列表数据
        /// </summary>
        protected List<NavBarData> showNavBarDataList2;
        /// <summary>
        /// 二级页签选中索引
        /// </summary>
        protected int selectedIndex2 = -1;

        #region 临时变量
        private List<NavBarData> tempDataList;
        private List<NavBarData> tempDataList2;
        #endregion
        /// <summary>
        /// 第二个参数表示是否为手动点击触发回调
        /// </summary>
        protected Action<NavBarData, bool> onClickBack;
        protected NavBarData lastSelectPrimary = null;
        protected NavBarData lastSelectSecondary = null;
        private NavBarData realClickedData = null;
        private Dictionary<GObject, NavBarData> obj2DataDic = new();
        /// <summary>
        /// 所有页签节点的根节点
        /// </summary>
        protected NavBarData navDataRoot; 
        /// <summary>
        /// 红点类型。如果设置为数字类型，只有一级页签会显示数字
        /// </summary>
        protected ENavTabRedDotType redDotStyle;
        /// <summary>
        ///是否可展开类型Tab，即有二级页签
        /// </summary>
        protected bool hasSecondTab = false;
        /// <summary>
        /// tab是否虚拟列表
        /// </summary>
        protected bool isVirtual = false;
        /// <summary>
        /// 项目binder字典
        /// </summary>
        protected ComBinderDictionary<NavBarItemBinder> itemBinders;
        /// <summary>
        /// 是否每次点击必执行回调，即时已经选中状态下
        /// </summary>
        public bool IsCallbackEveryClick { get; set; } = false;
        /// <summary>
        /// 是否隐藏掉一级页签的红点
        /// </summary>
        public bool IsHideFirstTabRedDot { set; get; } = false;
        /// <summary>
        /// navbar组件根节点
        /// </summary>
        public GComponent Root { get; private set; }
        public GComponent SecRoot { get; private set; }
        /// <summary>
        /// item数量变化时的回调
        /// </summary>
        private Action numItemsChangedAction;
        public bool lockCanSelect = true;

        /// <summary>
        /// 是否使用默认的音效
        /// </summary>
        public bool UseDefaultSound = true;
        /// <summary>
        /// 一级页签的默认音效
        /// </summary>
        private readonly string defaultPrimarySound = "UI_Click_04"; 
        /// <summary>
        /// 二级页签的默认音效
        /// </summary>
        private readonly string defaultSecondarySound = "UI_Click_03";
        /// <summary>
        /// 一级页签的替换音效
        /// </summary>
        private string replacePrimarySound = string.Empty;
        /// <summary>
        /// 二级页签的替换音效
        /// </summary>
        private string replaceSecondarySound = string.Empty;
        
        private string redDotCustomUrl = string.Empty;


        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="root"></param>
        /// <param name="hasSecondTab">是否有二级tab</param>
        /// <param name="secRoot">独立GList的二级tab</param>
        public virtual void Init(GComponent root, bool hasSecondTab = false, GComponent secRoot = null)
        {
            Root = root;
            SecRoot = secRoot;
            lockCanSelect = true;
            this.hasSecondTab = secRoot!= null || hasSecondTab;
            listCom = root?.GetChild("list") as GList;
            if(listCom!=null)listCom.itemRenderer = OnItemRender;
            listCom2 = secRoot?.GetChild("list") as GList;
            if (listCom2 != null) listCom2.itemRenderer = OnItemRender2;
            showNavBarDataList = new();
            showNavBarDataList2 = new();
            tempDataList = new();
            tempDataList2 = new();
            navDataRoot = new NavBarData("Root", 0);
            navDataRoot.SetNavType(ETabStyle.Primary);
            InitItemBinders();
        }

        public virtual void LinkTopBar(ComTopBar topBar, bool autoTransToPc = false)
        {
            numItemsChangedAction = topBar.SetHotKeyPosition;

            if (!autoTransToPc)
            {
                //指定要使用topbar中的tab，tab需要提前加载好
                listCom = topBar.FirstNavList;
                listCom.itemRenderer = OnItemRender;
                if (hasSecondTab)
                {
                    listCom2 = topBar.SecondNavList;
                    if (listCom2 != null)
                    {
                        listCom2.itemRenderer = OnItemRender2;
                    }
                }
            }
            else
            {
                LinkTopBarForPlatform(topBar);
            }
        }
        /// <summary>
        /// 创建项目节点
        /// </summary>
        /// <param name="selectedIndex"></param>
        /// <param name="selectedIndex2"></param>
        protected virtual void CreateItem()
        {
            listCom.numItems = showNavBarDataList.Count;
            listCom.ClearSelection();
            listCom.selectedIndex = selectedIndex;
            for (int i = 0; i < listCom.numChildren; i++)
            {
                var obj = listCom.GetChildAt(i);
                if (obj2DataDic.TryGetValue(obj, out var data))
                {
                    if (data != realClickedData)
                    {
                        foreach (var tran in obj.asCom.Transitions)
                        {
                            tran.Stop();
                        }
                    }
                }
            }
            if (listCom2 != null)
            {
                listCom2.numItems = showNavBarDataList2.Count;
                listCom2.selectedIndex = selectedIndex2;
                for (int i = 0; i < listCom2.numChildren; i++)
                {
                    var obj = listCom2.GetChildAt(i);
                    if (obj2DataDic.TryGetValue(obj, out var data))
                    {
                        if (data != realClickedData)
                        {
                            foreach (var tran in obj.asCom.Transitions)
                            {
                                tran.Stop();
                            }
                        }
                    }
                }
            }
            else if (selectedIndex2 > -1)
            {
                listCom.AddSelection(selectedIndex + selectedIndex2 + 1, false);
            }

            //实表替换分割线
            if (!isVirtual)
            {
                //实体表替换子项，如果数据表的项目类型发生变化，需要先清空list再重新赋值
                for (int i = 0; i < showNavBarDataList.Count; i++)
                {
                    if (showNavBarDataList[i].TabStyle == ETabStyle.Separator &&
                        !listCom.GetChildAt(i).packageItem.name.StartsWith("NavLine"))
                    {
                        listCom.RemoveChildToPoolAt(i);
                        listCom.AddChildAt(UIPackage.CreateObjectFromURL(GetLineUrl()), i);
                    }
                }

                if (listCom2 != null)
                {
                    for (int i = 0; i < showNavBarDataList2.Count; i++)
                    {
                        if (showNavBarDataList2[i].TabStyle == ETabStyle.Separator &&
                            !listCom2.GetChildAt(i).packageItem.name.StartsWith("NavLine"))
                        {
                            listCom2.RemoveChildToPoolAt(i);
                            listCom2.AddChildAt(UIPackage.CreateObjectFromURL(GetLineUrl()), i);
                        }
                    }
                }
            }
        }
        protected virtual void InitItemBinders()
        {
            itemBinders = new(c =>
            {
                return new(c);
            });
        }

        /// <summary>
        /// 对每个一级页签进行操作
        /// </summary>
        /// <param name="action"></param>
        public void ForEveryTab(Action<NavBarData, GComponent> action)
        {
            if (!isVirtual)
            {
                for (int i = 0; i < showNavBarDataList.Count; i++)
                {
                    if (showNavBarDataList[i].TabStyle != ETabStyle.Separator)
                    {
                        action(showNavBarDataList[i], listCom.GetChildAt(i).asCom);
                    }
                }
            }
        }
        /// <summary>
        /// 实体表的子项才可以生效list.foldInvisibleItems，要在LinkTopBarIfNeed之后调用
        /// </summary>
        public void SetVirtual()
        {
            isVirtual = true;
            listCom.SetVirtual();
            listCom2?.SetVirtual();
        }
        /// <summary>
        /// 连接到一个有tab的TopBar
        /// </summary>
        /// <param name="topBar"></param>
        /// <param name="autoTransToPc">是否根据平台自动选择横竖tab</param>
        
        private void HideRoot()
        {
            Root.visible = false;
            if (SecRoot != null) SecRoot.visible = false;
        }

        private void ShowRoot()
        {
            Root.visible = true;
            if (SecRoot != null) SecRoot.visible = true;
        }
        /// <summary>
        /// 如果link了topBar，HideRoot不会隐藏topBar中的tab
        /// </summary>
        public void HideTab()
        {
            HideRoot();
            if (listCom != null && listCom.parent != null) listCom.parent.visible = false;
            if (listCom2 != null && listCom2.parent != null) listCom2.parent.visible = false;
        }

        public void ShowTab()
        {
            ShowRoot();
            if (listCom != null && listCom.parent != null) listCom.parent.visible = true;
            if (listCom2 != null && listCom2.parent != null) listCom2.parent.visible = true;
        }

        /// <summary>
        /// 添加tab数据
        /// mainTabId不为-1时，表示添加到指定的一级页签下
        /// </summary>
        /// <param name="navData"></param>
        /// <param name="mainTabId"></param>
        public void AddTabData(NavBarData navData, int mainTabId = -1)
        {
            if (mainTabId == -1 || !hasSecondTab)
            {
                SetChild(navDataRoot, navData);
                if (navData.TabStyle != ETabStyle.Separator)
                    navData.SetNavType(hasSecondTab ? ETabStyle.Primary : ETabStyle.Normal);
            }
            else
            {
                SetChild(navDataRoot.ChildDic[mainTabId], navData);
                if (navData.TabStyle != ETabStyle.Separator) navData.SetNavType(ETabStyle.Secondary);
            }
        }
        /// <summary>
        /// 获取选中的一级页签id
        /// </summary>
        /// <returns></returns>
        public int GetCurSelectFirstTabId()
        {
            if (lastSelectPrimary != null)
            {
                return lastSelectPrimary.TabId;
            }

            return -1;
        }
        /// <summary>
        /// 获取选中的二级页签id
        /// </summary>
        /// <returns></returns>
        public int GetCurSelectSecTabId()
        {
            if (lastSelectSecondary != null)
            {
                return lastSelectSecondary.TabId;
            }

            return -1;
        }
        /// <summary>
        /// 删除tab数据
        /// </summary>
        /// <param name="navData"></param>
        /// <param name="mainTabId"></param>
        public void RemoveTabData(NavBarData navData, int mainTabId = -1)
        {

            if (mainTabId == -1)
            {
                navDataRoot.ChildDic.Remove(navData.TabId);
                navDataRoot.ChildList.Remove(navData);
            }
            else
            {
                navDataRoot.ChildDic[mainTabId].ChildList.Remove(navData);
                navDataRoot.ChildDic[mainTabId].ChildDic.Remove(navData.TabId);
            }

            navData.Reset();
        }
        /// <summary>
        /// 清空tab数据
        /// </summary>
        public void ClearTabData()
        {
            foreach (var navData in navDataRoot.ChildList)
            {
                if(navData.ChildList!=null)
                {
                    foreach (var secData in navData.ChildList)
                    {
                        secData.Reset();
                    }
                    navData.ChildList.Clear();
                    navData.ChildDic.Clear();
                }
                navData.Reset();
            }
            navDataRoot.ChildList.Clear();
            navDataRoot.ChildDic.Clear();
        }
        /// <summary>
        /// 获取tab数据
        /// </summary>
        /// <param name="mainId"></param>
        /// <param name="subId"></param>
        /// <returns></returns>
        public NavBarData GetTabData(int mainId, int subId = -1)
        {
            if (subId == -1)
            {
                if (navDataRoot.ChildDic.TryGetValue(mainId, out var data))
                {
                    return data;
                }
            }
            else
            {
                if (navDataRoot.ChildDic.TryGetValue(mainId, out var data))
                {
                    if (data.ChildDic.TryGetValue(subId, out var secData))
                    {
                        return secData;
                    }
                }
            }

            return null;
        }
        /// <summary>
        /// 设置项目点击回调
        /// </summary>
        /// <param name="clickBack">是否为手动点击触发的回调</param>
        public void SetClickBack(Action<NavBarData, bool> clickBack)
        {
            onClickBack = clickBack;
        }
        /// <summary>
        /// 设置红点类型
        /// </summary>
        /// <param name="redStyle"></param>
        public void SetRedType(ENavTabRedDotType redStyle = ENavTabRedDotType.Dot)
        {
            redDotStyle = redStyle;
        }

        public void SetRedCustomUrl(string url)
        {
            redDotCustomUrl = url;
        }
        /// <summary>
        /// 设置页签数据
        /// </summary>
        /// <param name="parent"></param>
        /// <param name="child"></param>
        private void SetChild(NavBarData parent, NavBarData child)
        {
            parent.ChildList.Add(child);
            parent.ChildDic[child.TabId] = child;
            child.Parent = parent;
        }

        /// <summary>
        /// 刷新界面显示，如果界面显示相关数据变更需要调用此函数刷新
        /// </summary>
        public void Refresh()
        {
            InternalRefresh(true);
        }
        protected void InternalRefresh(bool force = false)
        {
            NavBarData tempSelectedPrimary = null;
            NavBarData tempSelectedSeccondary = null;
            int tempSelectedIndex = 0;
            int tempSelectedIndex2 = -1;
            tempDataList.Clear();
            tempDataList2.Clear();

            for (int i = 0; i < navDataRoot.ChildList.Count; i++)
            {
                tempDataList.Add(navDataRoot.ChildList[i]);
                if (navDataRoot.ChildList[i].IsSelect)
                {
                    tempSelectedIndex = i;

                    if (navDataRoot.ChildList[i].TabStyle == ETabStyle.Primary ||
                        navDataRoot.ChildList[i].TabStyle == ETabStyle.Normal)
                    {//发现使用时在外部设置好IsSelect状态直接Refresh了，缺少了lastSelectPrimary设置
                        tempSelectedPrimary = navDataRoot.ChildList[i];
                    }
                }
                if (navDataRoot.ChildList[i].ChildList != null && navDataRoot.ChildList[i].IsShowChild)
                {
                    if (listCom2 != null)
                    {
                        tempDataList2.AddRange(navDataRoot.ChildList[i].ChildList);
                    }
                    else
                    {
                        tempDataList.AddRange(navDataRoot.ChildList[i].ChildList);
                        //需要多选模式
                        listCom.selectionMode = ListSelectionMode.Multiple;
                    }
                    for (int j = 0; j < navDataRoot.ChildList[i].ChildList.Count; j++)
                    {
                        if (navDataRoot.ChildList[i].ChildList[j].IsSelect)
                        {
                            tempSelectedIndex2 = j;

                            if (navDataRoot.ChildList[i].ChildList[j].TabStyle == ETabStyle.Secondary)
                            {//发现使用时在外部设置好IsSelect状态直接Refresh了，缺少了lastSelectSecondary设置
                                tempSelectedSeccondary = navDataRoot.ChildList[i].ChildList[j];
                            }
                            break;
                        }
                    }
                }
            }

            //判断是否需要刷新
            if (force || tempSelectedIndex != selectedIndex || tempSelectedIndex2 != selectedIndex2 ||
                lastSelectPrimary != tempSelectedPrimary || lastSelectSecondary != tempSelectedSeccondary||
                showNavBarDataList.Count != tempDataList.Count || showNavBarDataList2.Count != tempDataList2.Count)
            {
                selectedIndex = tempSelectedIndex;
                selectedIndex2 = tempSelectedIndex2;
                lastSelectPrimary = tempSelectedPrimary;
                lastSelectSecondary = tempSelectedSeccondary;
                showNavBarDataList.Clear();
                showNavBarDataList.AddRange(tempDataList);
                showNavBarDataList2.Clear();
                showNavBarDataList2.AddRange(tempDataList2);
                obj2DataDic.Clear();
                CreateItem();
                numItemsChangedAction?.Invoke();
            }
        }
        
        /// <summary>
        /// 渲染一级页签
        /// </summary>
        /// <param name="index"></param>
        /// <param name="obj"></param>
        private void OnItemRender(int index, GObject obj)
        {
            UpdateItem(obj, showNavBarDataList[index], index == showNavBarDataList.Count - 1);
        }
        /// <summary>
        /// 渲染二级页签
        /// </summary>
        /// <param name="index"></param>
        /// <param name="obj"></param>
        private void OnItemRender2(int index, GObject obj)
        {
            UpdateItem(obj, showNavBarDataList2[index], index == showNavBarDataList2.Count - 1);
        }
        /// <summary>
        /// 渲染节点
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="navData"></param>
        /// <param name="isFinal">最后一个</param>
        protected virtual void UpdateItem(GObject obj, NavBarData navData, bool isFinal)
        {
            obj2DataDic[obj] = navData;

            var itemBinder = itemBinders.Get(obj.asCom);
            obj.UnlockKey = navData.UnlockKey;
            Mc.Ui.GetWindow(navData.WinName)?.AddNeedCheckUnlockCtrl(obj);

            obj.visible = navData.Visible;
            if (navData.TabStyle == ETabStyle.Separator)
            {
                return;
            }

            GButton com = obj.asButton;
            com.title = navData.Title;

            if(itemBinder.Num!=null)itemBinder.Num.text = navData.Num>1?navData.Num.ToString():"";
#if POCO
            if (!string.IsNullOrEmpty(navData.TextPocoKeyFormat))
            {
                com.GetChild("title")?.PocoRegister(ZString.Format(navData.TextPocoKeyFormat, navData.TabId));
            }
            if (!string.IsNullOrEmpty(navData.ButtonPocoKeyFormat))
            {
                if(navData.ButtonPocoKeyFormat.IndexOf("{0}")>-1)
                    obj.PocoRegister(ZString.Format(navData.ButtonPocoKeyFormat, navData.TabId));
                else
                    obj.PocoRegister(navData.ButtonPocoKeyFormat);
            }
#endif
            switch (navData.IconType)
            {
                case ENavIconType.BigIcon:
                    if (navData.Icon != null)
                    {
                        if (itemBinder.BigIcon != null) itemBinder.BigIcon.url = navData.Icon;
                        itemBinder.ShowIconCtr?.SetSelectedPage("bigIcon");
                    }

                    break;
                case ENavIconType.SmallIcon:
                    if (navData.Icon != null)
                    {
                        itemBinder.ShowIconCtr?.SetSelectedPage("smallIcon");
                        com.icon = navData.Icon;
                    }

                    break;
                case ENavIconType.NoIcon:
                    itemBinder.ShowIconCtr?.SetSelectedPage("no");
                    break;
            }

            int childCount = navData.ChildList?.Count ?? 0;
            bool showTriangle = navData.IsFirstTabShowTriangle && childCount > 0;
            itemBinder.TriangleCtrl?.SetSelectedIndex(showTriangle ? 0 : 1);

            UpdateButtonStyle(com, navData);

            // 走的闭包，原代码，不太好新增后减，先通过手动结束引导来处理
            com.onClick.Set(() =>
            {
                //切换动画，转移到UI控制器播放
                //代码通用多个UI，导致动画播放混乱
                realClickedData = navData;
                ClickItemBack(navData, true, true);
                if (navData.FinishGuideId > 0)
                {
                    var (currentGuideId, currentStepId) = Mc.Guide.GetCurrentGuideAndStepId();
                    if(currentGuideId == navData.FinishGuideId && currentStepId == navData.FinishGuideStepId)
                    {
                        Mc.Guide.FinishViewGuideStep(navData.FinishGuideId, navData.FinishGuideStepId);
                        if (navData.NxtGuideStepId != 0)
                        {
                            Mc.Msg.FireMsg(Common.Unity.Event.EventDefine.TryGuideInterface, navData.FinishGuideId, navData.NxtGuideStepId);
                        }
                    }
                }
            });

            itemBinder.TranslucentCtrl?.SetSelectedIndex(navData.IsTranslucent && !navData.IsSelect ? 0 : 1);
            
            UpdateRedDot(itemBinder, navData);

            itemBinder.Root.grayed = navData.IsGray;
            itemBinder.LockCtrl?.SetSelectedPage(navData.Locked ? "yes" : "no");
            itemBinder.UnfoldCtrl?.SetSelectedPage(navData.IsShowChild ? "true" : "false");
            itemBinder.IsFinalCtrl?.SetSelectedIndex(isFinal ? 1 : 0);
        }

        protected virtual void UpdateRedDot(NavBarItemBinder itemBinder, NavBarData navData)
        {
            if (navData.RedDotType == RedDotType.Invalid)
            {//手动更新红点
                if (itemBinder.ComRedDot != null)
                {
                    int num = 0;
                    if (navData.TabStyle == ETabStyle.Primary && !IsHideFirstTabRedDot)
                    {
                        num = RefreshRed(navData.TabId);
                    }
                    itemBinder.ComRedDot.visible = navData.IsShowRedDot;
                    if (navData.IsShowRedDot)
                    {
                        if (redDotStyle == ENavTabRedDotType.Dot)
                        {
                            itemBinder.ComRedDot.SetDotStyle();
                        }
                        else if (redDotStyle == ENavTabRedDotType.Num)
                        {
                            if (navData.TabStyle == ETabStyle.Primary)
                            {
                                itemBinder.ComRedDot.SetNumStyle();
                                itemBinder.ComRedDot.SetNum(num);
                            }
                            else
                            {
                                itemBinder.ComRedDot.SetDotStyle();
                            }
                        }
                        else if (redDotStyle == ENavTabRedDotType.Exclamation)
                        {
                            itemBinder.ComRedDot.SetStyle(ERedDotStyle.Exclamation);
                        }
                        else if (redDotStyle == ENavTabRedDotType.custom)
                        {
                            itemBinder.ComRedDot.SetStyle(ERedDotStyle.Custom);
                            itemBinder.ComRedDot.SetCustomValue(redDotCustomUrl);
                        }
                    }
                }
            }else
            {
                //设置红点类型，走红点系统
                itemBinder.ComRedDot?.SetData(navData.RedDotType, true);
            }
        }
        /// <summary>
        /// 更新按钮样式
        /// </summary>
        /// <param name="com"></param>
        /// <param name="navData"></param>
        protected virtual void UpdateButtonStyle(GComponent com, NavBarData navData)
        {
            var itemBinder = itemBinders.Get(com);
            var index = (int)navData.TabStyle;
            if (index >= 0 && index < itemBinder.BtnStyleCtr?.pageCount)
            {
                itemBinder.BtnStyleCtr?.SetSelectedIndex(index);
            }
        }
        /// <summary>
        /// 根据二级节点计算一级节点红点刷新
        /// </summary>
        /// <param name="tabId"></param>
        private int RefreshRed(int tabId)
        {
            NavBarData navData = navDataRoot.ChildDic[tabId];
            int num = 0;
            if (navData.ChildList != null && navData.ChildList.Count > 0)
            {
                for (int i = 0; i < navData.ChildList.Count; i++)
                {
                    if (navData.ChildList[i].IsShowRedDot)
                    {
                        num++;
                    }
                }
                navData.IsShowRedDot = num>0;
            }
            else
            {
                navData.IsShowRedDot = false;
            }
            return num;
        }

        public void RefreshRedDot()
        {
            for (int i = 0; i < listCom.numItems; i++)
            {
                var obj = listCom.GetChildAt(i);
                var itemBinder = itemBinders.Get(obj);
                var index = listCom.ChildIndexToItemIndex(i);
                if (index < showNavBarDataList.Count)
                {
                    var data = showNavBarDataList[index];
                    UpdateRedDot(itemBinder, data);
                }
            }

            if (listCom2 != null)
            {
                for (int i = 0; i < listCom2.numItems; i++)
                {
                    var obj = listCom2.GetChildAt(i);
                    var itemBinder = itemBinders.Get(obj);
                    var index = listCom2.ChildIndexToItemIndex(i);
                    if (index < showNavBarDataList2.Count)
                    {
                        var data = showNavBarDataList2[index];
                        UpdateRedDot(itemBinder, data);
                    }
                }
            }
        }
        /// <summary>
        /// 滑动并选中指定组件,会自动处理展开与收起逻辑，不需要提前展开对应一级标签
        /// </summary>
        public void ScrollAndSelect(int mainId, int subId = -1)
        {
            if (navDataRoot.ChildDic.TryGetValue(mainId, out var navData))
            {
                if (subId != -1)
                {
                    if (navData.ChildDic.TryGetValue(subId, out var secData))
                    {
                        navData = secData;
                    }
                }

                if (navData.TabStyle == ETabStyle.Primary)
                {
                    ClickItemBack(navData);
                    return;
                }
                else if (navData.TabStyle == ETabStyle.Secondary &&
                         (lastSelectPrimary == null || mainId != lastSelectPrimary.TabId))
                {
                    //int selectParent = showNavBarDataList.IndexOf(navData.Parent);
                    //listCom.ScrollToView(selectParent);
                    ClickItemBack(navData.Parent, false);
                }

                int selectIndex = showNavBarDataList.IndexOf(navData);
                  
                if (selectIndex != -1)
                {
                    listCom.ScrollToView(selectIndex);
                }
                ClickItemBack(navData);
            }
        }
        /// <summary>
        /// 点击后选中页签不在视野内，可以调用这个滑动到视野内。如果是二级页签，必须保证一级页签已展开
        /// </summary>
        /// <param name="mainId"></param>
        /// <param name="subId"></param>
        public void OnlyScroll(int mainId, int subId = -1)
        {
            if (navDataRoot.ChildDic.TryGetValue(mainId, out var navData))
            {
                if (subId != -1)
                {
                    if (navData.ChildDic.TryGetValue(subId, out var secData))
                    {
                        navData = secData;
                    }
                }

                if (navData.TabStyle == ETabStyle.Secondary &&
                    (lastSelectPrimary == null || mainId != lastSelectPrimary.TabId))
                {
                    int selectParent = showNavBarDataList.IndexOf(navData.Parent);
                    listCom.ScrollToView(selectParent);
                    if (listCom2 != null)
                    {
                        int selectIndex2 = showNavBarDataList2.IndexOf(navData);
                        listCom2.ScrollToView(selectIndex2);
                    }

                    return;
                }

                int selectIndex = showNavBarDataList.IndexOf(navData);
                listCom.ScrollToView(selectIndex);
            }
        }
        /// <summary>
        /// 只设置点击选中二级页签的UI表现状态
        /// </summary>
        /// <param name="mainId"></param>
        /// <param name="subId"></param>
        public void SetSelectSubTab(int mainId, int subId)
        {
            if (mainId == -1) return;
            NavBarData navData;
            if (subId != -1)
            {
                navData = navDataRoot.ChildDic[mainId].ChildDic[subId];
            }
            else
            {
                navData = navDataRoot.ChildDic[mainId];
            }

            if (navData == lastSelectSecondary)
            {
                navData.IsSelect = true;
            }
            else
            {
                if (lastSelectPrimary != null)
                {
                    lastSelectPrimary.IsShowChild = false;
                    lastSelectPrimary.IsSelect = false;
                }
                lastSelectPrimary = navData.Parent;
                lastSelectPrimary.IsShowChild = true;
                lastSelectPrimary.IsSelect = true;

                if (lastSelectSecondary != null)
                {
                    lastSelectSecondary.IsSelect = false;
                }
                lastSelectSecondary = navData;
                lastSelectSecondary.IsSelect = true;
            }

            InternalRefresh();
        }
        /// <summary>
        /// 点击页签的数据处理
        /// </summary>
        /// <param name="data"></param>
        /// <param name="autoSelectSecTab">是否自动那个选中第一个二级页签，只有一级页签会生效</param>
        /// <param name="isBtnClicked">是否是手动按钮点击调用</param>
        private void ClickItemBack(NavBarData data, bool autoSelectSecTab = true, bool isBtnClicked = false)
        {
            if (data.IsGray)
            {
                return;
            }
            if (data.Locked && !lockCanSelect)
            {
                InternalRefresh();
                onClickBack?.Invoke(data, isBtnClicked);
                return;
            }
            var clickBack = false;
            if (data.TabStyle == ETabStyle.Primary) //一级页签
            {
                if (data == lastSelectPrimary)
                {
                    if (IsCallbackEveryClick)
                    {
                        clickBack = true;
                    }

                    if (AlwaysUnfold)
                    {
                        data.IsShowChild = true;
                    }
                    else
                    {
                        clickBack = false;//这种情况下会执行两次点击回调，这里规避一下
                        OnClickSamePrimary(data, autoSelectSecTab, isBtnClicked);
                    }
                }
                else
                {
                    OnClickDiffPrimary(data, autoSelectSecTab, isBtnClicked);
                }

                InternalRefresh();
                OnlyScroll(data.TabId);

                //if (isBtnClicked)
                //{
                //    PlayPrimaryTabSound();
                //}
            }
            else if (data.TabStyle == ETabStyle.Secondary)
            {
                if (data == lastSelectSecondary)
                {
                    if (IsCallbackEveryClick)
                    {
                        clickBack = true;
                    }
                    data.IsSelect = true;
                }
                else
                {
                    data.IsSelect = true;
                    if (lastSelectSecondary != null)
                    {
                        lastSelectSecondary.IsSelect = false;
                    }

                    lastSelectSecondary = data;
                    clickBack = true;
                }

                InternalRefresh();

                //if (isBtnClicked)
                //{
                //    PlaySecondaryTabSound();
                //}
            }
            else
            {
                if (data == lastSelectPrimary)
                {
                    data.IsSelect = true;
                    // data.IsSelect = !data.IsSelect;
                    // if (!data.IsSelect)
                    // {
                    //     lastSelectPrimary = null;
                    // }
                }
                else
                {
                    if (lastSelectPrimary != null)
                    {
                        lastSelectPrimary.IsSelect = false;
                    }

                    lastSelectPrimary = data;
                    data.IsSelect = true;
                }

                clickBack = true;
                InternalRefresh();
            }

            if (clickBack)
            {
                onClickBack?.Invoke(data, isBtnClicked);
            }
        }

        /// <summary>
        /// 再次点击相同一级页签处理
        /// </summary>
        /// <param name="data"></param>
        /// <param name="autoSelectSecTab"></param>
        /// <param name="isBtnClicked"></param>
        private void OnClickSamePrimary(NavBarData data, bool autoSelectSecTab, bool isBtnClicked)
        {
            if (data.IsGray)
            {
                return;
            }
            if (data.Locked && !lockCanSelect)
            {
                onClickBack?.Invoke(data, isBtnClicked);
                return;
            }

            data.IsSelect = true;//!data.IsSelect;
            data.IsShowChild = !data.IsShowChild;//data.IsSelect;
            if (data.IsSelect)
            {
                lastSelectPrimary = data;
                onClickBack?.Invoke(data, isBtnClicked);
                if (data.ChildList.Count > 0 && autoSelectSecTab) //打开一级页签，自动选中第一个二级页签
                {
                    ClickItemBack(data.ChildList[0]);
                }
            }
            else
            {
                lastSelectPrimary = null;
                if (data.ChildList.Count > 0 && lastSelectSecondary != null)
                {
                    lastSelectSecondary.IsSelect = false;
                    lastSelectSecondary = null;
                }
            }
        }

        /// <summary>
        /// 点击不同一级页签处理
        /// </summary>
        /// <param name="data"></param>
        /// <param name="autoSelectSecTab"></param>
        /// <param name="isBtnClicked"></param>
        private void OnClickDiffPrimary(NavBarData data, bool autoSelectSecTab, bool isBtnClicked)
        {
            if (data.IsGray)
            {
                return;
            }
            if (data.Locked && !lockCanSelect)
            {
                onClickBack?.Invoke(data, isBtnClicked);
                return;
            }
            data.IsShowChild = true;
            data.IsSelect = true;

            if (lastSelectPrimary != null)
            {
                lastSelectPrimary.IsShowChild = false;
                lastSelectPrimary.IsSelect = false;
            }

            lastSelectPrimary = data;
            onClickBack?.Invoke(data, isBtnClicked);
            if (null != data.ChildList && data.ChildList.Count > 0 && autoSelectSecTab) //打开一级页签自动选中第一个二级页签
            {
                ClickItemBack(data.ChildList[0]);
            }
        }
        /// <summary>
        /// 
        /// </summary>
        public void ResetAll()
        {
            showNavBarDataList.Clear();
            lastSelectPrimary = null;
            lastSelectSecondary = null;
            navDataRoot.ChildList.Clear();
            navDataRoot.ChildDic.Clear();
        }
        /// <summary>
        /// 取消一级页签选中状态
        /// </summary>
        public void CancelPrimarySelect()
        {
            if (lastSelectPrimary == null)
            {
                return;
            }

            lastSelectPrimary.IsSelect = false;
            lastSelectPrimary = null;
            if (isVirtual)
                listCom.RefreshVirtualList();
            else
                listCom.numItems = listCom.numItems;
            numItemsChangedAction?.Invoke();
        }

        /// <summary>
        /// 播放一级页签音效
        /// </summary>
        private void PlayPrimaryTabSound()
        {
            if (UseDefaultSound)
            {
                Mc.Ui.PlaySocAudio(defaultPrimarySound);
                return;
            }

            if (!string.IsNullOrEmpty(replacePrimarySound))
            {
                Mc.Ui.PlaySocAudio(replacePrimarySound);
            }
        }

        /// <summary>
        /// 播放二级页签音效
        /// </summary>
        private void PlaySecondaryTabSound()
        {
            if (UseDefaultSound)
            {
                Mc.Ui.PlaySocAudio(defaultSecondarySound);
                return;
            }
            if (!string.IsNullOrEmpty(replaceSecondarySound))
            {
                Mc.Ui.PlaySocAudio(replaceSecondarySound);
            }
        }

        /// <summary>
        /// 不使用默认音效时，设置替换音效
        /// </summary>
        /// <param name="primarySound">一级页签音效</param>
        /// <param name="secondarySound">二级页签音效</param>
        public void SetReplaceSound(string primarySound, string secondarySound = "")
        {
            replacePrimarySound = primarySound;
            replaceSecondarySound = secondarySound;
        }
    }

    public class NavBarData
    {
        /// <summary>
        /// 页签id
        /// </summary>
        public int TabId { private set; get; }
        /// <summary>
        /// 展示的文本
        /// </summary>
        public string Title { set; get; }
        /// <summary>
        /// 展示数量
        /// </summary>
        public int Num { get; set; }
        /// <summary>
        /// 展示的icon，如果没用可以忽略
        /// </summary>
        public string Icon { private set; get; }
        /// <summary>
        /// 页签类型
        /// </summary>
        public ETabStyle TabStyle { set; get; }

        public int FinishGuideId { get; set; }
        public int FinishGuideStepId { get; set; }
        public int NxtGuideStepId { get; set; }
        /// <summary>
        /// 一级页签的子节点，只有一级节点有
        /// </summary>
        public List<NavBarData> ChildList { set; get; }

        public Dictionary<int, NavBarData> ChildDic { set; get; }

        //当前节点的父节点，只有二级节点有
        public NavBarData Parent { set; get; }

        //是否展示子节点，只有一级页签才生效
        public bool IsShowChild { set; get; }
        //锁定状态
        public bool Locked { get; set; }
        //置灰状态
        public bool IsGray { get; set; } = false;
        /// <summary>
        /// 红点数据类型,设置此类型后走通用红点逻辑
        /// </summary>
        public RedDotType RedDotType { set; get; } = RedDotType.Invalid;

        /// <summary>
        /// 直接设置是否显示红点，不走通用红点逻辑。只有NavTabRedDotType=invalid时才生效
        /// </summary>
        public bool IsShowRedDot { set; get; }

        private bool _isSelect = false;
        public bool IsSelect { get; set; } = false;

        public bool IsFirstTabShowTriangle { get; set; } = true;
        /// <summary>
        /// 是否半透明
        /// </summary>
        public bool IsTranslucent { get; set; } = false; 
        public bool Visible { get; set; } = true; //是否可见
        /// <summary>
        /// 锁标记
        /// </summary>
        public string UnlockKey;
        /// <summary>
        /// 锁标记所在win的名称
        /// </summary>
        public string WinName;
        public ENavIconType IconType { set; get; } = ENavIconType.NoIcon;
#if POCO
            public string TextPocoKeyFormat { get; set; }
            public string ButtonPocoKeyFormat { get; set; }
#endif
        public NavBarData(string title, int tabId, string icon = null, ENavIconType iconType = ENavIconType.NoIcon)
        {
            IsShowChild = false;
            Title = title;
            Icon = icon;
            IsShowRedDot = false;
            TabId = tabId;
            IconType = iconType;
        }

        public void SetNavType(ETabStyle navType)
        {
            TabStyle = navType;
            if (navType == ETabStyle.Primary)
            {
                ChildList = new();
                ChildDic = new();
            }
        }
        public void UpdateData(string title,string icon=null)
        {
            Title = title;
            Icon = icon;
        }

        public void SetFirstTabShowTriangle(bool isShow)
        {
            IsFirstTabShowTriangle = isShow;
        }
        /// <summary>
        /// 
        /// </summary>
        public void Reset()
        {
            ChildList = null;
            ChildDic = null;
            Parent = null;
            IsShowChild = false;
            Icon = null;
            FinishGuideId = 0;
            FinishGuideStepId = 0;
            NxtGuideStepId = 0;
        }
    }

    /// <summary>
    /// 按钮样式枚举，下标和btnstyle控制器对应
    /// </summary>
    public enum ETabStyle
    {
        Primary = 0, // 一级页签
        Secondary, // 二级页签
        Normal, //普通页签，即无法展开的一级页签
        Separator, //分割线
    }
    /// <summary>
    /// icon样式
    /// </summary>
    public enum ENavIconType
    {
        NoIcon,
        BigIcon,
        SmallIcon,
    }
}
