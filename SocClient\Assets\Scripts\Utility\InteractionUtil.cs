#define USE_NEW_INTERACTIVE
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Ui;

namespace WizardGames.Soc.SocClient.Utility
{
	public static class InteractionUtil
	{
        public static bool CanCollectWater()
        {
            var interactiveTb = McCommon.Tables.TbPlayerInteractiveState.GetOrDefault(Mc.MyPlayer.MyEntityLocal.InteractiveId);
            bool collecting = interactiveTb != null && interactiveTb.InteractiveType == PlayerInteractiveType.CollectWater;
            if (collecting)
            {
                StopInteract();
                // InteractionUtil.TryCollectWater(false);
                return false;
            }

            if (!Mc.MyPlayer.CanMakeWaterCmd())
            {
                return false;
            }
            
            if (Mc.CollectionItem.PlayerRoot.GetNodeById(Mc.MyPlayer.MyEntityLocal.CurrentWeaponId) is WaterBottleItemNode bottle && bottle.IsFull)
            {
                Mc.MsgTips.ShowRealtimeWeakTip(3019);
                return false;
            }

            return true;
        }
        
        public static bool TryInteract(PlayerInteractiveId interactiveId, long targetId = 0)
        {
            if (interactiveId >0)
            {
                if (InteractiveStateController.CanInteractive(Mc.MyPlayer.MyEntityLocal, (int)interactiveId))
                {
                    Mc.UserCmd.NowCmd.InteractiveID = (int)interactiveId;
                    Mc.UserCmd.NowCmd.InteractiveTargetID = targetId;
                    return true;
                }
                if (interactiveId == PlayerInteractiveId.Inspection)
                {
                    Mc.MsgTips.ShowRealtimeWeakTip(2001);
                }
            }

            return false;
        }

        //打断当前交互
        public static void StopInteract(PlayerInteractiveId interactive = PlayerInteractiveId.Stop)
        {
            if (interactive == PlayerInteractiveId.Stop || Mc.MyPlayer.MyEntityLocal.InteractiveId == (int)interactive)
            {
                Mc.UserCmd.NowCmd.InteractiveID = (int)PlayerInteractiveId.Stop;
            }
        }


        public static void TryShowInteractiveProgress(IHeldItemEntity helditem, int ms, PlayerInteractiveId currentId = PlayerInteractiveId.Stop)
        {
            if (helditem == null) return;
            var itemConfig = McCommon.Tables.TbItemConfig.GetOrDefault(helditem.TableId);
            if (itemConfig == null) return;
            var info = LanguageManager.Instance.GetText(LanguageConst.UsingItem_Engaging);
            var bagProgress = Mc.Ui.GetWindow("UiHudUsingItem") as UiHudUsingItem;
            if (!Mc.Ui.IsWindowOpen("UiInventory") && bagProgress != null && !bagProgress.node.visible)
            {
                Mc.HudCommon.ShowCountDown(ms, info, null, () => StopInteract(currentId), null, itemConfig.Icon, false);
            }
            else
            {
                if (bagProgress == null || !bagProgress.node.visible)
                {
                    Mc.MyPlayer.MyEntityLocal.ShowUseLoading(helditem, ms / 1000f);
                }
            }
        }
    }
}