using Cysharp.Text;
using FairyGUI;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.SocClient.Manager;

namespace WizardGames.Soc.SocClient.Ui

{
    public class UiRuntimeTools : WindowComBase
    {
#if UNITY_EDITOR
        private const string ResFixedPath = "Assets/SocClientRes/";
        private const double ICON_SIZE_MIN = 76;
        private const double ICON_SIZE_MAX = 500;

        struct ImgData
        {
            public PackageItem imgItem;
            public Vector2 size;
            public int frame;
        }

        struct ListData
        {
            public PackageItem atlasItem;
            public ImgData subItem;
        }

        private Dictionary<PackageItem, List<ImgData>> atlasInfoMap = new();
        private List<ListData> atlasInfos = new();
        private GList listInfos;
        private GLoader loaderPreview;
        private GLabel labelPreview;
        private GTextInput inputPackName;
        private GTextInput inputAtlasName;
        private GSlider sliderScaler;
        private UIPackage curLoadPack;
        private bool isCurPackLoad;
        private double curIconSize = ICON_SIZE_MIN;

        protected override void OnInit()
        {
            base.OnInit();

            var btnBack = ComRoot.GetChild("btnBack").asButton;
            btnBack.onClick.Set(()=> RemoveSelf());
            var comContent = ComRoot.GetChild("content").asCom;
            listInfos = comContent.GetChild("infoList").asList;
            listInfos.SetVirtual();
            listInfos.itemRenderer = OnRenderListItem;
            labelPreview = comContent.GetChild("preview").asLabel;
            loaderPreview = labelPreview.GetChild("icon").asLoader;
            labelPreview.visible = false;
            inputPackName = comContent.GetChild("packName").asTextInput;
            inputAtlasName = comContent.GetChild("atlasName").asTextInput;
            inputPackName.onSubmit.Set(OnClickSearch);
            inputAtlasName.onSubmit.Set(OnClickSearch);
            sliderScaler = comContent.GetChild("scaler").asSlider;
            sliderScaler.min = ICON_SIZE_MIN;
            sliderScaler.max = ICON_SIZE_MAX;
            sliderScaler.value = ICON_SIZE_MIN;
            sliderScaler.onChanged.Set(OnScalerValChanged);
            var btnSearch = comContent.GetChild("btnSearch").asButton;
            btnSearch.onClick.Set(OnClickSearch);
        }

        private void OnScalerValChanged()
        {
            curIconSize = sliderScaler.value;
            listInfos.RefreshVirtualList();
        }

        private void OnRenderListItem(int index, GObject item)
        {
            var lableItem = item.asLabel;
            if (null == lableItem || index >= atlasInfos.Count) return;
            var curData = atlasInfos[index];
            var ctrl = lableItem.GetController("style");
            bool isAtlas = null != curData.atlasItem;
            ctrl.SetSelectedPage(isAtlas ? "atlas": "sub");
            var iconLoader = lableItem.GetChild("icon").asLoader;
            var textSize = lableItem.GetChild("size").asTextField;
            Vector2 textureSize = Vector2.zero;
            var imgItem = curData.subItem.imgItem;
            if (isAtlas)
            {
                var atlasTitle = lableItem.GetChild("atlasTitle").asTextField;
                atlasTitle.text = "图集";
                lableItem.title = curData.atlasItem.file.Replace(ResFixedPath, string.Empty);
                curData.atlasItem.Load();
                iconLoader.texture = curData.atlasItem.texture;
                textureSize = curData.atlasItem.texture.originalSize;
                iconLoader.onRollOver.Set(() => OnIconRollOver(null, iconLoader.texture));
            }
            else if (imgItem.type == PackageItemType.MovieClip)
            {
                int frameIndex = curData.subItem.frame;
                lableItem.title = ZString.Format("{0} [{1}/{2}]", imgItem.name, frameIndex + 1, imgItem.frames?.Length ?? 0);
                imgItem.Load();
                NTexture frameTexture = null;
                if (frameIndex >= 0 && frameIndex < imgItem.frames.Length)
                {
                    frameTexture = imgItem.frames[frameIndex].texture;
                }
                if (null == frameTexture)
                {
                    textureSize = curData.subItem.size;
                    lableItem.icon = ZString.Format("{0}{1}{2}", UIPackage.URL_PREFIX, imgItem.owner.id, imgItem.id);
                    iconLoader.onRollOver.Set(() => OnIconRollOver(iconLoader.url));
                }
                else
                {
                    textureSize = frameTexture.originalSize;
                    iconLoader.texture = frameTexture;
                    textureSize = frameTexture.originalSize;
                    iconLoader.onRollOver.Set(() => OnIconRollOver(null, iconLoader.texture));
                }
            }
            else
            {
                lableItem.title = imgItem.name;
                textureSize = curData.subItem.size;
                lableItem.icon = ZString.Format("{0}{1}{2}", UIPackage.URL_PREFIX, imgItem.owner.id, imgItem.id);
                iconLoader.onRollOver.Set(() => OnIconRollOver(iconLoader.url));
            }
            textSize.text = ZString.Format("{0}x{1}", (int)textureSize.x, (int)textureSize.y);
            iconLoader.onRollOut.Set(OnIconRollOut);
            iconLoader.SetSize((float)curIconSize, (float)curIconSize);
        }

        private void OnIconRollOver(string url = null, NTexture texture = null)
        {
            if (!string.IsNullOrEmpty(url))
            {
                loaderPreview.url = url;
            }
            else if (texture != null)
            {
                loaderPreview.texture = texture;
            }
            labelPreview.visible = true;
        }

        private void OnIconRollOut()
        {
            loaderPreview.url = null;
            labelPreview.visible = false;
        }

        /// <summary>
        /// 收集当前包体内所有的图集信息
        /// </summary>
        public void CollectAtlasPackItems(string atlasName)
        {
            if (null == curLoadPack) return;
            atlasInfoMap.Clear();
            if (curLoadPack.sprites.Count == 0) return;
            foreach (var pair in curLoadPack.sprites)
            {
                var atlasItem = pair.Value.atlas;
                if (null == atlasItem) continue;
                string itemID = pair.Key;
                int clipSignIndex = itemID.IndexOf("_");   // 序列帧动画后面会接上帧数
                int clipIndex = 0;
                if (clipSignIndex >= 0)
                {
                    int.TryParse(itemID.Substring(clipSignIndex+1), out clipIndex);
                    itemID = itemID.Substring(0, clipSignIndex);
                }
                var curItem = curLoadPack.GetItem(itemID, true);
                if (null == curItem) continue;
                if (!atlasInfoMap.TryGetValue(atlasItem, out var list))
                {
                    list = new List<ImgData>();
                    atlasInfoMap[atlasItem] = list;
                }
                list.Add(new() { imgItem = curItem, size = pair.Value.originalSize, frame = clipIndex });
            }
            atlasInfos.Clear();
            foreach (var pair in atlasInfoMap)
            {
                if (!string.IsNullOrEmpty(atlasName) && !pair.Key.file.Contains(atlasName)) continue;
                atlasInfos.Add(new() { atlasItem = pair.Key });
                if (pair.Value.Count > 0 && pair.Value[0].imgItem.type == PackageItemType.MovieClip)
                {
                    pair.Value.Sort((a, b)=> a.frame - b.frame);
                }
                foreach (var sub in pair.Value)
                {
                    atlasInfos.Add(new() { subItem = sub });
                }
            }
        }

        private void OnClickSearch()
        {
            string packName = inputPackName.text;
            if (null == curLoadPack || curLoadPack.name != packName)
            {
                if (isCurPackLoad && null != curLoadPack) Mc.Ui.ReleasePackage(curLoadPack.name);
                if (!string.IsNullOrEmpty(packName))
                {
                    curLoadPack = UIPackage.GetByName(packName);
                    isCurPackLoad = null == curLoadPack;
                    try
                    {
                        if (isCurPackLoad) curLoadPack = Mc.Ui.AddPackage(packName);
                    }
                    catch (System.Exception ex)
                    {
                        Mc.MsgTips.ShowDebugRealtimeWeakTip("加载包体异常");
                        logger.ErrorFormat("加载包体异常: {0}", ex);
                        curLoadPack = null;
                        isCurPackLoad = false;
                    }
                }
            }
            if (null == curLoadPack)
            {
                Mc.MsgTips.ShowDebugRealtimeWeakTip("无效包体");
                isCurPackLoad = false;
                listInfos.numItems = 0;
                return;
            }
            CollectAtlasPackItems(inputAtlasName.text);
            listInfos.numItems = atlasInfos.Count;
        }

        public override void OnDestroy()
        {
            base.OnDestroy();

            if (null != curLoadPack && isCurPackLoad)
            {
                Mc.Ui.ReleasePackage(curLoadPack.name);
            }
        }
#endif
    }
}