using Unity.Mathematics;
using UnityEngine;
using WizardGames.Soc.Common.Character;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Unity.Character;

namespace WizardGames.Soc.SocClient.Player.Animation
{
    /// <summary>
    /// 控制locomotionLayer层行为
    /// </summary>
    public partial struct TpAnimationJob
    {
        /// <summary>
        /// 更新移动层权重
        /// </summary>
        /// <param name="jobData"></param>
        /// <param name="resultJobData"></param>
        /// <param name="animParams"></param>
        /// <param name="constData"></param>
        private void UpdateLocomotionWeightLayer(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData, ref AnimParametersTp animParams,in TpAniConstData constData)
        {
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            //如果切枪了则刷新一遍目标曲线值
            if (playerLocalData.LogicFrame)
            {
                OnSwitchWeaponLocomotionWeight(ref jobData, ref resultJobData, ref animParams, constData);
            }
            //获取行为移动权重曲线
            if (playerLocalData.RenderFrame)
            {
                GetActionLocomotionWeight(ref jobData, ref resultJobData, ref animParams, constData);
                UpdateLocomotionMaskWeight(ref jobData, ref resultJobData, TpMaskWeightRuntimeDataGroup.FullCapacity, MaskArray,
                    ref resultJobData.PlayerLocalData.LocoBoneWeightGroup, AnimParametersTp.LocomotionLayer_LayerIndex,
                    AnimParametersTp.LocomotionLayerDynamics_LayerIndex);
            }
        }

        private void OnSwitchWeaponLocomotionWeight(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData, ref AnimParametersTp animParams,in TpAniConstData constData)
        {
            ref var heldData = ref jobData.HeldItemData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            if (heldData.CurrentWeaponTableId != playerLocalData.TpAniWeaponId)
            {
                //读取到为0时，那就认为默认读取移动层原始的权重
                TpAniBoneMask boneMask = new TpAniBoneMask();
                bool defaultWeight = false;
                var inLadder = InLocomotionLadder(playerLocalData.ELocomotionLayer);
                var inMantle = InLocomotionMantle(playerLocalData.ELocomotionLayer);
                var inIdle = InLocomotionIdle(playerLocalData.ELocomotionLayer);
                var inJog = InLocomotionJog(playerLocalData.ELocomotionLayer);
                var inSprint = InLocomotionSprint(playerLocalData.ELocomotionLayer);
                var inAir = InLocomotionAir(playerLocalData.ELocomotionLayer);
                var inSwimIdle = InLocomotionSwimIdle(playerLocalData.ELocomotionLayer);
                var inSwimJog = InLocomotionSwimJog(playerLocalData.ELocomotionLayer);
                var inSwimSprint = InLocomotionSwimSprint(playerLocalData.ELocomotionLayer);
                var inRiderLocomotion = InLocomotionRiderLocomotion(playerLocalData.ELocomotionLayer);
                var inRiderJump = InLocomotionRiderJump(playerLocalData.ELocomotionLayer);
                if (inLadder)
                {
                    boneMask = GetLocomotionLayerBone(ref jobData, ref resultJobData, playerLocalData.ELocomotionLayer);
                }
                else if (inMantle)
                {
                    boneMask = GetLocomotionLayerBone(ref jobData, ref resultJobData, playerLocalData.ELocomotionLayer);
                }
                else if (inIdle)
                {
                    boneMask = GetLocomotionLayerBone(ref jobData, ref resultJobData, playerLocalData.ELocomotionLayer);
                }
                else if (inJog)
                {
                    boneMask =GetLocomotionLayerBone(ref jobData, ref resultJobData, playerLocalData.ELocomotionLayer);
                }
                else if (inSprint)
                {
                    boneMask = GetLocomotionLayerBone(ref jobData, ref resultJobData, playerLocalData.ELocomotionLayer);
                }
                else if (inAir)
                {
                    boneMask = GetLocomotionLayerBone(ref jobData, ref resultJobData, playerLocalData.ELocomotionLayer);
                }
                else if (inSwimIdle)
                {
                    boneMask = GetLocomotionLayerBone(ref jobData, ref resultJobData, playerLocalData.ELocomotionLayer);
                }
                else if (inSwimJog)
                {
                    boneMask = GetLocomotionLayerBone(ref jobData, ref resultJobData, playerLocalData.ELocomotionLayer);
                }
                else if (inSwimSprint)
                {
                    boneMask = GetLocomotionLayerBone(ref jobData, ref resultJobData, playerLocalData.ELocomotionLayer);
                }
                else if (inRiderLocomotion || inRiderJump)
                {
                    boneMask = GetLocomotionLayerBone(ref jobData, ref resultJobData, playerLocalData.ELocomotionLayer);
                }
                else
                {
                    defaultWeight = true;
                }
                SetLocomotionLayerWeightByState(ref jobData, ref resultJobData, boneMask, 0, defaultWeight, true);
            }
        }

        /// <summary>
        /// 获取行为移动权重曲线
        /// </summary>
        /// <param name="jobData"></param>
        /// <param name="resultJobData"></param>
        /// <param name="animParams"></param>
        /// <param name="constData"></param>
        private void GetActionLocomotionWeight(ref TpAnimationJobData jobData, ref TpAnimationResultJobData resultJobData, ref AnimParametersTp animParams,in TpAniConstData constData)
        {
            ref var playerStateData = ref jobData.PlayerStateData;
            ref var heldData =ref jobData.HeldItemData;
            ref var playerLocalData = ref resultJobData.PlayerLocalData;
            //读取到为0时，那就认为默认读取移动层原始的权重
            var actionDynamicWeight = 0f;
            var actionSpineWeight = 0f;
            var actionSpine1Weight = 0f;
            var actionSpine2Weight = 0f;
            var actionLClavicleWeight = 0f;
            var actionHeadWeight = 0f;
            var actionRClavicleWeight = 0f;
            var actionWeaponWeight = 0f;
            var actionLeftArmWeight = 0f;
            var actionRightArmWeight = 0f;
            var animatorInstanceId = jobData.AnimatorInstanceId;
            var optimizationAnimatorParameterFlushMem = Animator.GetFlushMemUnSafe(animatorInstanceId);

            var inLadder = InLocomotionLadder(playerLocalData.ELocomotionLayer);
            var inJog = InLocomotionJog(playerLocalData.ELocomotionLayer);
            var inSprint = InLocomotionSprint(playerLocalData.ELocomotionLayer);
            var inAir = InLocomotionAir(playerLocalData.ELocomotionLayer);
            var inSwimIdle = InLocomotionSwimIdle(playerLocalData.ELocomotionLayer);
            var inSwimJog = InLocomotionSwimJog(playerLocalData.ELocomotionLayer);
            var inSwimSprint = InLocomotionSwimSprint(playerLocalData.ELocomotionLayer);
            if (inLadder)
            {
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.LadderDynamicWeightCurve,
                    out actionDynamicWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.LadderSpineWeightCurve,
                    out actionSpineWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.LadderSpine1WeightCurve,
                    out actionSpine1Weight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.LadderSpine2WeightCurve,
                    out actionSpine2Weight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.LadderLClavicleWeightCurve,
                    out actionLClavicleWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.LadderHeadWeightCurve, out actionHeadWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.LadderRClavicleWeightCurve,
                    out actionRClavicleWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.LadderWeaponWeightCurve,
                    out actionWeaponWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.LadderLeftArmDynamicWeightCurve,
                    out actionLeftArmWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.LadderRightArmDynamicWeightCurve,
                    out actionRightArmWeight);
            }
            else if (inJog)
            {
                if (playerStateData.PoseState == PlayerPoseStateEnum.Crouch)
                {
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchDynamicWeightCurve,
                        out actionDynamicWeight);
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchSpineWeightCurve,
                        out actionSpineWeight);
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchSpine1WeightCurve,
                        out actionSpine1Weight);
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchSpine2WeightCurve,
                        out actionSpine2Weight);
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchLClavicleWeightCurve,
                        out actionLClavicleWeight);
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchHeadWeightCurve,
                        out actionHeadWeight);
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchRClavicleWeightCurve,
                        out actionRClavicleWeight);
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchWeaponWeightCurve,
                        out actionWeaponWeight);
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchLeftArmDynamicWeightCurve,
                        out actionLeftArmWeight);
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchRightArmDynamicWeightCurve,
                        out actionRightArmWeight);
                }
                else
                {
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.JogDynamicWeightCurve,
                        out actionDynamicWeight);
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.JogSpineWeightCurve,
                        out actionSpineWeight);
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.JogSpine1WeightCurve,
                        out actionSpine1Weight);
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.JogSpine2WeightCurve,
                        out actionSpine2Weight);
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.JogLClavicleWeightCurve,
                        out actionLClavicleWeight);
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.JogHeadWeightCurve,
                        out actionHeadWeight);
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.JogRClavicleWeightCurve,
                        out actionRClavicleWeight);
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.JogWeaponWeightCurve,
                        out actionWeaponWeight);
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.JogLeftArmDynamicWeightCurve,
                        out actionLeftArmWeight);
                    optimizationAnimatorParameterFlushMem.ReadFloat(constData.JogRightArmDynamicWeightCurve,
                        out actionRightArmWeight);
                }
            }
            else if (inSprint)
            {
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SprintDynamicWeightCurve,
                    out actionDynamicWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SprintSpineWeightCurve,
                    out actionSpineWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SprintSpine1WeightCurve,
                    out actionSpine1Weight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SprintSpine2WeightCurve,
                    out actionSpine2Weight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SprintLClavicleWeightCurve,
                    out actionLClavicleWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SprintHeadWeightCurve, out actionHeadWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SprintRClavicleWeightCurve,
                    out actionRClavicleWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SprintWeaponWeightCurve,
                    out actionWeaponWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SprintLeftArmDynamicWeightCurve,
                    out actionLeftArmWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SprintRightArmDynamicWeightCurve,
                    out actionRightArmWeight);
            }
            else if (inAir)
            {
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.JumpDynamicWeightCurve,
                    out actionDynamicWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.JumpSpineWeightCurve, out actionSpineWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.JumpSpine1WeightCurve,
                    out actionSpine1Weight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.JumpSpine2WeightCurve,
                    out actionSpine2Weight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.JumpLClavicleWeightCurve,
                    out actionLClavicleWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.JumpHeadWeightCurve, out actionHeadWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.JumpRClavicleWeightCurve,
                    out actionRClavicleWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.JumpWeaponWeightCurve,
                    out actionWeaponWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.JumpLeftArmDynamicWeightCurve,
                    out actionLeftArmWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.JumpRightArmDynamicWeightCurve,
                    out actionRightArmWeight);
            }
            else if (inSwimIdle)
            {
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimIdleDynamicWeightCurve,
                    out actionDynamicWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimIdleSpineWeightCurve,
                    out actionSpineWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimIdleSpine1WeightCurve,
                    out actionSpine1Weight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimIdleSpine2WeightCurve,
                    out actionSpine2Weight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimIdleLClavicleWeightCurve,
                    out actionLClavicleWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimIdleHeadWeightCurve,
                    out actionHeadWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimIdleRClavicleWeightCurve,
                    out actionRClavicleWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimIdleWeaponWeightCurve,
                    out actionWeaponWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimIdleLeftArmDynamicWeightCurve,
                    out actionLeftArmWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimIdleRightArmDynamicWeightCurve,
                    out actionRightArmWeight);
            }
            else if (inSwimJog || inSwimSprint)
            {
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimDynamicWeightCurve,
                    out actionDynamicWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimSpineWeightCurve, out actionSpineWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimSpine1WeightCurve,
                    out actionSpine1Weight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimSpine2WeightCurve,
                    out actionSpine2Weight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimLClavicleWeightCurve,
                    out actionLClavicleWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimHeadWeightCurve, out actionHeadWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimRClavicleWeightCurve,
                    out actionRClavicleWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimWeaponWeightCurve,
                    out actionWeaponWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimLeftArmDynamicWeightCurve,
                    out actionLeftArmWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.SwimRightArmDynamicWeightCurve,
                    out actionRightArmWeight);
            }
            else if (playerStateData.PoseState == PlayerPoseStateEnum.Crouch)
            {
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchDynamicWeightCurve,
                    out actionDynamicWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchSpineWeightCurve,
                    out actionSpineWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchSpine1WeightCurve,
                    out actionSpine1Weight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchSpine2WeightCurve,
                    out actionSpine2Weight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchLClavicleWeightCurve,
                    out actionLClavicleWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchHeadWeightCurve, out actionHeadWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchRClavicleWeightCurve,
                    out actionRClavicleWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchWeaponWeightCurve,
                    out actionWeaponWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchLeftArmDynamicWeightCurve,
                    out actionLeftArmWeight);
                optimizationAnimatorParameterFlushMem.ReadFloat(constData.CrouchRightArmDynamicWeightCurve,
                    out actionRightArmWeight);
            }
            else
            {
                actionDynamicWeight = 0;
                actionSpineWeight = 0;
                actionSpine1Weight = 0;
                actionSpine2Weight = 0;
                actionLClavicleWeight = 0;
                actionHeadWeight = 0;
                actionRClavicleWeight = 0;
                actionWeaponWeight = 0;
                actionLeftArmWeight = 0;
                actionRightArmWeight = 0;
            }
                    
            actionDynamicWeight = math.clamp(actionDynamicWeight, 0f,1f);
            actionSpineWeight = math.clamp(actionSpineWeight, 0f,1f);
            actionSpine1Weight = math.clamp(actionSpine1Weight, 0f,1f);
            actionSpine2Weight = math.clamp(actionSpine2Weight, 0f,1f);
            actionLClavicleWeight = math.clamp(actionLClavicleWeight, 0f,1f);
            actionHeadWeight = math.clamp(actionHeadWeight, 0f,1f);
            actionRClavicleWeight = math.clamp(actionRClavicleWeight, 0f,1f);
            actionWeaponWeight = math.clamp(actionWeaponWeight, 0f,1f);
            actionLeftArmWeight =math.clamp(actionLeftArmWeight, 0f,1f);
            actionRightArmWeight = math.clamp(actionRightArmWeight, 0f,1f);

            for (int i = 0; i < TpMaskWeightRuntimeDataGroup.DefaultGroup.Capacity; ++i)
            {
                ref var weightRuntimeData = ref resultJobData.PlayerLocalData.LocoBoneWeightGroup.defaultGroup.Get(i);
                SetOverrideTargetWeightTp(ref weightRuntimeData, actionDynamicWeight);
            }
            for (int i = 0; i < TpMaskWeightRuntimeDataGroup.SpineGroup.Capacity; ++i)
            {
                ref var weightRuntimeData = ref resultJobData.PlayerLocalData.LocoBoneWeightGroup.spineGroup.Get(i);
                SetOverrideTargetWeightTp(ref weightRuntimeData, actionSpineWeight);
            }
            for (int i = 0; i < TpMaskWeightRuntimeDataGroup.Spine1Group.Capacity; ++i)
            {
                ref var weightRuntimeData = ref resultJobData.PlayerLocalData.LocoBoneWeightGroup.spine1Group.Get(i);
                SetOverrideTargetWeightTp(ref weightRuntimeData, actionSpine1Weight);
            }
            for (int i = 0; i < TpMaskWeightRuntimeDataGroup.Spine2Group.Capacity; ++i)
            {
                ref var weightRuntimeData = ref resultJobData.PlayerLocalData.LocoBoneWeightGroup.spine2Group.Get(i);
                SetOverrideTargetWeightTp(ref weightRuntimeData, actionSpine2Weight);
            }
            for (int i = 0; i < TpMaskWeightRuntimeDataGroup.LCaviGroup.Capacity; ++i)
            {
                ref var weightRuntimeData = ref resultJobData.PlayerLocalData.LocoBoneWeightGroup.lcaviGroup.Get(i);
                SetOverrideTargetWeightTp(ref weightRuntimeData, actionLClavicleWeight);
            }
            for (int i = 0; i < TpMaskWeightRuntimeDataGroup.HeadGroup.Capacity; ++i)
            {
                ref var weightRuntimeData = ref resultJobData.PlayerLocalData.LocoBoneWeightGroup.headGroup.Get(i);
                SetOverrideTargetWeightTp(ref weightRuntimeData, actionHeadWeight);
            }
            for (int i = 0; i < TpMaskWeightRuntimeDataGroup.RCaviGroup.Capacity; ++i)
            {
                ref var weightRuntimeData = ref resultJobData.PlayerLocalData.LocoBoneWeightGroup.rcaviGroup.Get(i);
                SetOverrideTargetWeightTp(ref weightRuntimeData, actionRClavicleWeight);
            }
            for(int i =0; i< TpMaskWeightRuntimeDataGroup.WeaponGroup.Capacity; ++i)
            {
                ref var weightRuntimeData = ref resultJobData.PlayerLocalData.LocoBoneWeightGroup.weaponGroup.Get(i);
                SetOverrideTargetWeightTp(ref weightRuntimeData, actionWeaponWeight);
            }
            for (int i = 0; i < TpMaskWeightRuntimeDataGroup.LeftArmGroup.Capacity; ++i)
            {
                ref var weightRuntimeData = ref resultJobData.PlayerLocalData.LocoBoneWeightGroup.leftArmGroup.Get(i);
                SetOverrideTargetWeightTp(ref weightRuntimeData, actionLeftArmWeight);
            }
            for (int i = 0; i < TpMaskWeightRuntimeDataGroup.RightArmGroup.Capacity; ++i)
            {
                ref var weightRuntimeData = ref resultJobData.PlayerLocalData.LocoBoneWeightGroup.rightArmGroup.Get(i);
                SetOverrideTargetWeightTp(ref weightRuntimeData, actionRightArmWeight);
            }
        }
        
        private void SetOverrideTargetWeightTp(ref TpMaskWeightRuntimeData weightRuntime, float targetWeight)
        {
            if (Approximately(targetWeight, 0.0f))
            {
                //代表没有配，就读取默认的移动层的权重
                weightRuntime.MaskOverrideTargetWeightTp = weightRuntime.MaskTargetWeightTp;
            }
            else
            {
                targetWeight = math.clamp(1 - targetWeight, 0f, 1f);
                weightRuntime.MaskOverrideTargetWeightTp = targetWeight;
            }
            var time = 0f;
            weightRuntime.MaskWeightSpeedTp = Approximately(time, 0f)
                ? (weightRuntime.MaskOverrideTargetWeightTp - weightRuntime.MaskNowWeightTp) * 1000
                : (weightRuntime.MaskOverrideTargetWeightTp - weightRuntime.MaskNowWeightTp) / time;
        }
    }
}