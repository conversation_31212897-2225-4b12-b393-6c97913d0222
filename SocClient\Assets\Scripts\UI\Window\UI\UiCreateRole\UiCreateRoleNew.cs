using DG.Tweening;
using FairyGUI;
using NodaTime;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data.common;
using WizardGames.Soc.Common.Data.DataItem;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.Combat;
using WizardGames.Soc.Common.Unity.Config;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Loader.CrashSight;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.SDK;
using WizardGames.Soc.SocClient.Audio;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.GoLoader;
using WizardGames.Soc.SocClient.Http;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Ui.Binder.LobbyCreateRole;
using WizardGames.Soc.SocClient.Utility;

namespace WizardGames.Soc.SocClient.Ui
{
    /// <summary>
    /// 创角界面
    /// </summary>
    public partial class UiCreateRoleNew : WindowComBase
    {
        private SocLogger logger = LogHelper.GetLogger(typeof(UiCreateRoleNew));
        protected Dictionary<int,List<int>> faceInfo = new Dictionary<int,List<int>>();
        protected Dictionary<int, List<int>> hairInfo = new Dictionary<int, List<int>>();
        private Dictionary<int, List<int>> hairColorInfo = new Dictionary<int, List<int>>();
        private List<string> tabTitle = new List<string>() { LanguageManager.GetTextConst(LanguageConst.SexMan), LanguageManager.GetTextConst(LanguageConst.SexWoman) };
        private List<string> tabIconUrl = new List<string>() { "ui://2ganiy4ir2xl3w", "ui://2ganiy4ir2xl3u" };
        private List<bool> lockType = new List<bool>() { false, false };
        protected UiCreateRoleNewBinder binder;
        private PlayerAppearanceInfo info;
        private ComUiPlayerModel uiPlayerModel;
        private ComInputBox inputBox;
        public DisplayModel displayModel;
        private Vector2 lastTouchPos;
        private bool IsTouchingModel;
        private ComStateBtn finishBtn;

        protected int selectedTabIndex = 0;
        protected int createRoleStep = 0;
        protected override void OnInit()
        {
            base.OnInit();
            InitUIComponent();
            faceInfo.Clear();
            InitData();
            InitDefaultData();
            createRoleStep = 0;
         
            InitPlayerModel();
            InitPanelType();
            SetCreateRoleStep(true);

            // 加载玩家头像包体
            if (null == UIPackage.GetByName("AssetAvatar")) Mc.Ui.AddPackage("AssetAvatar");
            Mc.Msg.AddListener<int>(EventDefine.ChangeNameError, ChangeNameError);
        }

        private void ChangeNameError(int errorId)
        {
            if(errorId == 11005)
            {
                System.Random random = new System.Random();

                // 生成 1 到 1000 之间的随机整数
                int randomNumber = random.Next(1, 1001);
                binder.Root.Content.RoleInfo.InputName.InputName.InputName.text = binder.Root.Content.RoleInfo.InputName.InputName.InputName.text + randomNumber;
                OnInputNameChanged();
            }
        }

        public override void MakeFullScreen()
        {
            base.MakeFullScreen();
            SetCamreaTrack(cameraTrack);
        }

        public virtual void InitPanelType()
        {
            binder.Root.Content.RoleInfo.CtrlType.selectedIndex = 0;
        }

        protected int defaultFaceIndex = 0;
        public virtual void InitDefaultData()
        {

            defaultFaceIndex = 0;
            selectFaceIndex = defaultFaceIndex;
            selectedTabIndex = 0;
        }

        private void InitUIComponent()
        {
            binder = new UiCreateRoleNewBinder(ContentPane);
            inputBox = binder.Root.Content.RoleInfo.InputName.InputName.InputName as ComInputBox;
            binder.Root.Content.SelectIdentity.BtnNext.onClick.Set((_) =>
            {
                if (createRoleStep != 0) return;
                //MgrAudio.PlayAudioEventAsync("UI_Common_Open");
                createRoleStep++;
                SetCreateRoleStep();
             
            });

            var changefinishBtn = binder.Root.Content.RoleInfo.FinishBtn as ComStateBtn;
            changefinishBtn.onClick.Set((_) => {
                Mc.Lobby.ReqChangeAppearance(EHttpReqModule.CreateRole, info, () =>
                {
                    if (createRoleStep != 1) return;
                    Mc.Config.lobbyRoleInfo.faceID = info.FaceId;
                    Mc.Config.lobbyRoleInfo.sex = info.Sex;
                    Mc.Config.lobbyRoleInfo.hairID = info.HairID;
                    Mc.Config.lobbyRoleInfo.hairColorID = info.HairColorID;
                    createRoleStep++;
                    SetCreateRoleStep();
                  
                });

            });

            finishBtn = binder.Root.Content.RoleInfo.BtnNext as ComStateBtn;
            finishBtn.onClick.Set((_) =>
            {
                if (!IsInputNameLegal())
                {
                    MgrAudio.PlayAudioEventAsync("UI_Click_Fail");
                    PlayInputBoxEffect();
                    return;
                }
                var msgBoxData = Mc.Tables.TbMsgBoxConfig.GetOrDefault(158);
                MsgBoxInfo boxInfo = new MsgBoxInfo(158, new() { null,() =>{
                    binder.BinderRoot.touchable = false;
                    ReqChangeName(inputBox.text);
                }});
                boxInfo.Msg = string.Format(msgBoxData.Text, inputBox.text);
                UiMsgBox.ShowMsgBox(boxInfo);
            });
            //binder.BtnBack.SetSoundClick(Mc.Tables.TbUiAudio.UniversalMainMenuTab, ctx => OnClickClose());
            (binder.Root.TopBar.BinderRoot as ComTopBar)?.SetBackButtonClicked((c)=>OnClickClose());
            var tabList = binder.Root.Content.NavBar.GetChild("list").asList;
            tabList.itemRenderer = RenderTabItem;
            tabList.onClickItem.Set((context) =>
            {
                var index = tabList.GetChildIndex((GObject)context.data);
                if (selectedTabIndex == index) return;
                MgrAudio.PlayAudioEventAsync("UI_Click_01");
                if (lockType[index])
                {
                    Mc.MsgTips.ShowRealtimeWeakTip(24072);
                    return;
                }
                selectedTabIndex = index;
                var children = tabList.GetChildren();
                foreach (GObject child in children)
                {
                    child.asButton.selected = false;
                }
                tabList.GetChildAt(selectedTabIndex).asButton.selected = true;
                ShowRoleSelect(selectedTabIndex);
            });
            tabList.selectedIndex = selectedTabIndex;
            var hairList = binder.Root.Content.RoleInfo.HairList;
            hairList.itemRenderer = RenderHairItem;
            hairList.onClickItem.Set((context) =>
            {
                MgrAudio.PlayAudioEventAsync("UI_Click_05");
                var index = hairList.GetChildIndex((GObject)context.data);
                int dataIndex = hairList.ChildIndexToItemIndex(index);
                selectHairIndex = dataIndex;
                hairList.RefreshVirtualList();
                var list = hairInfo[selectedTabIndex];
                info.HairID = list[selectHairIndex];
                ChangeAppearance(info);
            });
            var hairColorList = binder.Root.Content.RoleInfo.ColorList;
            hairColorList.itemRenderer = RenderHairColorItem;
            hairColorList.onClickItem.Set((context) =>
            {
                MgrAudio.PlayAudioEventAsync("UI_Click_05");
                var index = hairColorList.GetChildIndex((GObject)context.data);
                int dataIndex = hairColorList.ChildIndexToItemIndex(index);
                selectHairColorIndex = dataIndex;
                hairColorList.RefreshVirtualList();
                var list = hairColorInfo[selectedTabIndex];
                info.HairColorID = list[selectHairColorIndex];
                ChangeAppearance(info);
            });

            inputBox.SetHintText(LanguageManager.GetTextConst(LanguageConst.CreateRoleHintNameInput));
            inputBox.SetChangedCallback(OnInputNameChanged);
            inputBox.SetHeadShowCtrl("orangeHide");
            tabList.numItems = 2;
            binder.Root.Content.TouchLoader.touchable = true;
            binder.Root.Content.TouchLoader.onTouchBegin.Set(OnModeTouchBegin);
            binder.Root.Content.TouchLoader.onTouchMove.Set(OnModeTouchMove);
            binder.Root.Content.TouchLoader.onTouchEnd.Set(OnModeTouchEnd);

            binder.Root.Content.FileVX.BinderRoot.GetTransition("show_anim").SetHook("LoadScene", () =>
            {
                UiLiteLoading.ShowLoading();
                //Mc.LoginStep.CloseCreateRoleScene();
                //RemoveSelf();
                LoginStepHelper.RequestGuideInfo((int)EPlayerStatus.OnlineStatusOffline);

            });
            binder.Root.Content.FileVX.BinderRoot.GetTransition("show_anim").SetHook("PlaySoundVOD", () =>
            {
                MgrAudio.PlayAudioEventAsync("VO_D_01_01");
            });
            binder.Root.Content.FileVX.NpcTips.GetChild("npcTips").asTextField.text = LanguageManager.GetTextConst(LanguageConst.CreateRoleDialogue01);
            SetPlaySound();

            InitListAvatar();

            //binder.Root.Content.SelectIdentity.AvatarList.ListAvatar.scrollPane.onScrollEnd.Add(DoSpecialEffect);
#if MSDK
            
            binder.Root.Content.RoleInfo.InputName.InputName.InputName.text = Mc.Config.nameSDK;
            OnInputNameChanged();
#endif

        }


        private Dictionary<GObject, Dictionary<string, GObject>> coms = new Dictionary<GObject, Dictionary<string, GObject>>();


        Vector2 itemSize;
        void DoSpecialEffect()
        {
            float midX = binder.Root.Content.SelectIdentity.AvatarList.ListAvatar.scrollPane.posX + binder.Root.Content.SelectIdentity.AvatarList.ListAvatar.viewWidth *3/8;
            int cnt = binder.Root.Content.SelectIdentity.AvatarList.ListAvatar.numChildren;
           
            for (int i = 0; i < cnt; i++)
            {
                GObject obj = binder.Root.Content.SelectIdentity.AvatarList.ListAvatar.GetChildAt(i);
                float dist = Mathf.Abs(midX - obj.x - obj.width /2);
                if (dist > obj.width)
                {
                    obj.SetScale(1, 1);
                    obj.asCom.GetChild("norIcon").alpha = 1 ;
                    obj.asCom.GetChild("selIcon").alpha = 0;
                } 

                else
                {
                    float ss = 1 + (1 - dist / obj.width) * 0.1f;
                    obj.SetScale(ss, ss);
                    obj.asCom.GetChild("norIcon").alpha = 1 - (1 - dist / obj.width);
                    obj.asCom.GetChild("selIcon").alpha =  (1 - dist / obj.width);
                    obj.asCom.GetChild("glow").alpha = (1 - dist / obj.width)*0.2f;
                    if (dist< obj.width)
                    {
                        int index = ((binder.Root.Content.SelectIdentity.AvatarList.ListAvatar.GetFirstChildInView() ) % binder.Root.Content.SelectIdentity.AvatarList.ListAvatar.numItems);

                        if (obj.x + obj.width / 2 > midX)
                          index = ((binder.Root.Content.SelectIdentity.AvatarList.ListAvatar.GetFirstChildInView() + 1) % binder.Root.Content.SelectIdentity.AvatarList.ListAvatar.numItems);
                        binder.Root.Content.SelectIdentity.PointList.selectedIndex = index;
                    }
                }
            }
           
        }

        public void ReqChangeNameError()
        {
            binder.BinderRoot.touchable = true;
        }

        private void SetPlaySound()
        {
            binder.Root.Content.RoleInfo.Mask.GetTransition("namemask").SetHook("PlaySoundCreatePaint", () =>
            {
                MgrAudio.PlayAudioEventAsync("UI_Create_Paint");
            });
            binder.Root.Content.SelectIdentity.AvatarList.TransShow_anim.SetHook("PlaySoundCreatePhoto", () =>
            {
                MgrAudio.PlayAudioEventAsync("UI_Create_Photo");
            });
            binder.Root.Content.BinderRoot.GetTransition("swich_02").SetHook("PlaySoundCreateStamp", () =>
            {
                MgrAudio.PlayAudioEventAsync("UI_Create_Stamp");
            });
        }

        public void PlayInputBoxEffect()
        {
            binder.Root.Content.RoleInfo.InputName.TransShake.Play();
        }
        private void InitData()
        {
            faceInfo[0] = Mc.Tables.TBCharacterCfg.MaleFace.ToList();
            faceInfo[1] = Mc.Tables.TBCharacterCfg.FemaleFace.ToList();
            hairInfo[0] = Mc.Tables.TBCharacterCfg.MaleHairStyle.ToList();
            hairInfo[1] = Mc.Tables.TBCharacterCfg.FemaleHairStyle.ToList();
            hairColorInfo[0] = Mc.Tables.TBCharacterCfg.MaleHairColor.ToList();
            hairColorInfo[1] = Mc.Tables.TBCharacterCfg.FemaleHairColor.ToList();

        }

        public Transform playerLoader;
        public Transform transLobbyPlayer;
        public PlayerDisplayData displayData;
      
        public virtual void InitPlayerModel()
        {
            var lobbyModelPoint = UnityEngine.Object.FindObjectOfType<LobbyCreateRoleSceneModelPoint>();
            if (lobbyModelPoint == null)
            {
                //Log.Error("LobbySceneModelPoint is null");
                return;
            }
            var loaderRoot = lobbyModelPoint.playerLoaderMain;
            sceneCamrea = lobbyModelPoint.GetComponentInChildren<Camera>();
            if (loaderRoot == null) return;
            playerLoader = loaderRoot.transform;
            playerLoader.rotation = Quaternion.Euler(0, -7, 0);
            if (playerLoader != null && displayModel == null)
            {
                var objPlayerModel = PlayerLoader.GetSkeletonWithHead();
                transLobbyPlayer = objPlayerModel.transform;
                transLobbyPlayer.SetParent(playerLoader.transform);
                transLobbyPlayer.localPosition = Vector3.zero;
                transLobbyPlayer.localScale = Vector3.one;
                transLobbyPlayer.localEulerAngles = Vector3.zero;
                var objPlayerModelPoint = objPlayerModel.AddComponent<ObjectPointComponent>();
                objPlayerModelPoint.InitData();

                displayData = new PlayerDisplayData();

                displayModel = new DisplayModel(new PlayerData(displayData), objPlayerModelPoint, ModelType.Lb, MgrConfigPhysicsLayer.LayerDefault, true);
                displayModel.CreateAnimator(Mc.Tables.TbChracterParameter.UiAnimatorTp);
                displayModel.ModelAnimator.cullingMode = AnimatorCullingMode.AlwaysAnimate;
                displayModel.ModelAnimator.Update(0);
                displayModel.Show(true);
                displayModel.CheckBackWeapon();
                PutOn(Mc.Tables.TBConsts.CreatRoleEquip.ToList());
            }

        }

        public void PutOn(List<int> itemIds, List<int> skinIds = null)
        {
            displayData.EquipmentDisplayDatas.Clear();
            for (int i = 0; i < itemIds.Count; i++)
            {
                displayData.EquipmentDisplayDatas.Add(new EquipmentDisplayData() { TableId = itemIds[i], SkinId = skinIds != null ? skinIds[i] : 0 });
            }
            displayModel.UpdateAllPart();
        }

        public virtual void ChangeAppearance(PlayerAppearanceInfo info)
        {
            if (displayData == null) return;
            displayData.FaceId = info.FaceId;
            displayData.Sex = info.Sex;
            displayData.HairId = info.HairID;
            displayData.HairColorId = info.HairColorID;
            displayModel.RefreshCustomize();
        }

        public virtual void RealseDisplayModel()
        {
            displayModel?.Release();
            displayModel = null;
            transLobbyPlayer = null;
            displayData = null;
            playerLoader = null;

        }


        private int selectHairColorIndex = 0;
        private int selectHairIndex = 0;

        /// <summary>
        /// 简单的客户端本地昵称合规检测
        /// </summary>
        private bool IsInputNameLegal()
        {
            var curName = inputBox.text;
            return Mc.Lobby.IsPlayerNameLegal(curName, true);
        }

        /// <summary>
        /// 请求修改玩家名称
        /// </summary>
        private void ReqChangeName(string newName)
        {
            logger.ReleaseCriticalFormat("Start to request change name: {0}", newName);
            Mc.Lobby.ReqChangeName(EHttpReqModule.CreateRole, newName, () =>
            {
                CrashSightWrapper.AddSceneData("playerName", Mc.Config.nickName);
                RefreshChangeNameSuccess();
                Mc.Msg.FireMsg(EventDefine.UserInfoUpdate);
            });
        }

        private void RefreshChangeNameSuccess()
        {
            logger.ReleaseCritical("Change Name Success");
            //Mc.MsgTips.ShowRealtimeWeakTip(23080);
            // 以首次起名作为创角成功的标记
            Mc.Config.isNewUser = false;

            Mc.Lobby.ReqChangeAppearance(EHttpReqModule.CreateRole, info, () =>
            {
                if (createRoleStep != 1) return;
                Mc.Msg.FireMsg(EventDefine.UserInfoUpdate);
                createRoleStep++;
#if MSDK

                Mc.Lobby.ReqChangeAvatar(EHttpReqModule.CreateRole, Mc.Config.pictureUrl,false, () =>
                {

                });

#endif


                SetCreateRoleStep();
            });
          

        }

        private void RenderHairColorItem(int index, GObject item)
        {
            var com = item.asCom;
            var list = hairColorInfo[selectedTabIndex];
            var cfg = Mc.Tables.TBCharacter.GetOrDefault(list[index]);
            var image = com.GetChild("color").asLoader;
            image.url = cfg.IconNormal;
            var ctrl = com.GetController("selected");
            ctrl.SetSelectedPage(selectHairColorIndex == index ? "true" : "false");
        }


        private void RenderHairItem(int index, GObject item)
        {
            var com = item.asCom;
            var list = hairInfo[selectedTabIndex];
            var cfg = Mc.Tables.TBCharacter.GetOrDefault(list[index]);
            var image =  com.GetChild("image").asLoader;
            image.url = cfg.IconNormal;
            var ctrl = com.GetController("selected");
            ctrl.SetSelectedPage(selectHairIndex == index ? "true" : "false");
        }

        protected void SetCreateRoleStep(bool first = false)
        {
         
          
            if(createRoleStep == 0)
            {
                binder.Root.CtrlStep.SetSelectedIndex(createRoleStep);
                ShowRoleSelect(selectedTabIndex);
                if (first)
                {
                    binder.Root.Content.SelectIdentity.TransShow_anim.Play();
                }
                else
                {
                    binder.Root.Content.SelectIdentity.TransShow_anim02.Play();
                    binder.Root.Content.RoleInfo.TransHide_anim.Play();
                    binder.Root.Content.SelectIdentity.AvatarList.BinderRoot
                        .GetTransition("show_anim02_" + (selectFaceIndex % 4 + 1)).Play();
                }
                RefreshSelectRole();
                SetCamreaTrack(0);
            }
            else if(createRoleStep == 1)
            {
                binder.Root.CtrlStep.SetSelectedIndex(createRoleStep);
                //binder.BinderRoot.touchable = false;
                binder.Root.Content.SelectIdentity.TransHide_anim.Play();
                binder.Root.Content.RoleInfo.AvatarList.TransHead02.Play();
                binder.Root.Content.RoleInfo.TransShow_anim.Play();
                binder.Root.TransHide_anim02.Play();
                RefreshRoleInfo();
                SetCamreaTrack(1);
            }
            else if(createRoleStep == 2)
            {
                ShowEnd();
            }
        }

        public virtual void ShowEnd()
        {
            binder.Root.CtrlStep.SetSelectedIndex(createRoleStep);
            MgrUploadLog.UploadClientLogByWebRequest("PersonalProfileCreated", "");
            RefreshResultInfo();
            binder.Root.TransSwich_02.Play();
            //注册成功埋点
            WeGameSDKWrapper.ReportEvent(EWeGameSDKEvent.rail_register_character, EWeGameSDKEventStatus.success);
        }

        long timerId ;
        private void RefreshResultInfo()
        {
            var list = faceInfo[selectedTabIndex];
            var cfg = Mc.Tables.TBCharacter.GetOrDefault(list[selectFaceIndex]);
            binder.Root.Content.FileVX.Paper.Avatar.Image.url = cfg.IconSelected;
            binder.Root.Content.FileVX.Paper.NameText.text = inputBox.text;
            binder.Root.Content.FileVX.Paper.CodeNameText.text = cfg.IdentityCode;
            binder.Root.Content.FileVX.Paper.InitialIdentityText.text = cfg.IdentityOrigin;
            binder.Root.Content.FileVX.Paper.RiskText.text = cfg.IdentityRisk;
            binder.Root.Content.FileVX.Paper.PsychologyTag.text = cfg.IdentityMental;
            binder.Root.Content.FileVX.Paper.PersonalInformationText.text = cfg.IdentityDetails;
        }

        private void RefreshRoleInfo()
        {
            var list = faceInfo[selectedTabIndex];
            var cfg = Mc.Tables.TBCharacter.GetOrDefault(list[selectFaceIndex]);
            binder.Root.Content.RoleInfo.Avatar.Image.url = cfg.IconSelected;
            if(binder.Root.Content.RoleInfo.CtrlType.selectedIndex == 0)
                binder.Root.Content.RoleInfo.NameText.text = cfg.IdentityName;
            else
                binder.Root.Content.RoleInfo.NameText.text = Mc.Config.lobbyRoleInfo.nickName;
            binder.Root.Content.RoleInfo.CodeNameText.text = cfg.IdentityCode;
            binder.Root.Content.RoleInfo.InitialIdentityText.text = cfg.IdentityOrigin;
            binder.Root.Content .RoleInfo.RiskText.text = cfg.IdentityRisk;
            binder.Root .Content.RoleInfo.PsychologyTag.text = cfg.IdentityMental;
            var hairList = hairInfo[selectedTabIndex];
            binder.Root.Content.RoleInfo.HairList.numItems = hairList.Count;
            var hairColorList = hairColorInfo[selectedTabIndex];
            binder.Root.Content.RoleInfo.ColorList.numItems = hairColorList.Count;
            OnInputNameChanged();
        }

        protected int selectFaceIndex = 0;
        //private List<GComponent> roleHead;
        private void ShowRoleSelect(int selectedTabIndex)
        {
            if (info == null)
            {
                info = new PlayerAppearanceInfo();
             
            }
            if (info.Sex != selectedTabIndex)
            {
                selectFaceIndex = defaultFaceIndex;
                defaultFaceIndex = 0;
                binder.Root.Content.SelectIdentity.AvatarList.ListAvatar.numItems = faceInfo[selectedTabIndex].Count;
                
                AvatarScrollToView(selectedTabIndex);
                
                binder.Root.Content.SelectIdentity.PointList.itemRenderer = (a,b) => { };
                binder.Root.Content.SelectIdentity.PointList.numItems = faceInfo[selectedTabIndex].Count;
                binder.Root.Content.SelectIdentity.PointList.selectedIndex = selectFaceIndex;
                binder.Root.Content.SelectIdentity.PointList.ResizeToFit();
                var faceList = faceInfo[selectedTabIndex];
                var cfg = Mc.Tables.TBCharacter.GetOrDefault(faceList[selectFaceIndex]);
                binder.Root.Content.RoleInfo.AvatarList.AvatarSelected.url = cfg.IconSelected;
            }
            info.Sex = selectedTabIndex;
            var facelist = faceInfo[selectedTabIndex];
            info.FaceId = facelist[selectFaceIndex];
            var facecfg = Mc.Tables.TBCharacter.GetOrDefault(info.FaceId);
            var hairList = hairInfo[selectedTabIndex];

            info.HairID = facecfg.IdentityDefaultHair>0 ? facecfg.IdentityDefaultHair: hairList[0];
            selectHairIndex = 0;
            for (int i = 0; i < hairList.Count; i++)
            {
                if (hairList[i] == facecfg.IdentityDefaultHair)
                {
                    selectHairIndex = i;break;
                }
            }

            var hairColorList = hairColorInfo[selectedTabIndex];
            info.HairColorID = facecfg.IdentityDefaultHairColor>0? facecfg.IdentityDefaultHairColor: hairColorList[0];
            selectHairColorIndex = 0;
            for (int i = 0; i < hairColorList.Count; i++)
            {
                if (hairColorList[i] == facecfg.IdentityDefaultHairColor)
                {
                    selectHairColorIndex = i; 
                    break;
                }
            }
            ChangeAppearance(info);
          
        }

        private void RefreshSelectRole()
        {
            RefreshSelctRoleInfo();
            ShowRoleSelect(selectedTabIndex);
        }

        private void RefreshSelctRoleInfo()
        {
            var list = faceInfo[selectedTabIndex];
            var cfg = Mc.Tables.TBCharacter.GetOrDefault(list[selectFaceIndex]);
            binder.Root.Content.SelectIdentity.IdName.text = cfg.IdentityName;
            binder.Root.Content.SelectIdentity.AgelText.text = cfg.IdentityAge;
            binder.Root.Content.SelectIdentity.DescText.GetChild("descText").asTextField.text = cfg.IdentityDesc;
        }

        protected override void OnEnable()
        {
            Input.multiTouchEnabled = false;
            PlayCreateRoleMusic();
            var tabList = binder.Root.Content.NavBar.GetChild("list").asList;
            tabList.selectedIndex = selectedTabIndex;
            var children = tabList.GetChildren();
            foreach (GObject child in children)
            {
                child.asButton.selected = false;
            }
            tabList.GetChildAt(selectedTabIndex).asButton.selected = true;
            if (UiFullScreenVideoMono.Instance != null) UiFullScreenVideoMono.Instance.HideVideo(false);
            SetTitle();
            MakeFullScreen();
            (binder.Root.TopBar.BinderRoot as ComTopBar)?.OnEnable();
        }

        public virtual void SetTitle()
        {
            var topBar = binder.Root.TopBar.BinderRoot as ComTopBar;
            topBar.Title = LanguageManager.GetTextConst(LanguageConst.CreateRole);
            topBar.SetEscTitle(LanguageManager.GetTextConst(LanguageConst.CreateRoleEscTitle));
        }

        protected override void OnDisable()
        {
            (binder.Root.TopBar.BinderRoot as ComTopBar)?.OnDisable();
            Input.multiTouchEnabled = true;
           
            if (timerId>0)
                Mc.TimerWheel.CancelTimer(timerId);
            if (UiFullScreenVideoMono.Instance != null)
            {
                UiFullScreenVideoMono.Instance.StopAndDestory();
            }
            UnLoadMusic();
        }

        public virtual void PlayCreateRoleMusic()
        {
            HallAudioManager.InitPlayHallMusic("Music_Hall", "Create");
        }

        public virtual void UnLoadMusic()
        {
            HallAudioManager.UnLoadMusic();
        }
        public override void OnDestroy()
        {
            Input.multiTouchEnabled = true;
            RealseDisplayModel();
        }
        protected Camera sceneCamrea;
        private int cameraTrack;
        public virtual void SetCamreaTrack(int track)
        {
            cameraTrack = track;
            var cameraTrackData = Mc.Tables.TBLobbyCamreaTrack.GetOrDefault(2);
            if (cameraTrackData == null || sceneCamrea == null)
            {
                return;
            }
            uiPlayerModel?.StopCameraTween();
            float duration = cameraTrackData.Duration[track];
            Ease ease = (Ease)cameraTrackData.Ease;

            int indexPos = Array.IndexOf(cameraTrackData.PosIndexList, track);
            Vector3 pos = Vector3.zero;
            if (indexPos != -1)
            {
                BeansVector3 beansVector3Pos = cameraTrackData.PosList[indexPos];
                pos = new(beansVector3Pos.X, beansVector3Pos.Y, beansVector3Pos.Z);
               sceneCamrea.transform.DOLocalMove(pos, duration).SetEase(ease);
            }
            int indexRot = Array.IndexOf(cameraTrackData.RotIndexList, track);
            Vector3 rot = Vector3.zero;
            if (indexRot != -1)
            {
                BeansVector3 beansVector3Rot = cameraTrackData.RotList[indexRot];
                rot = new(beansVector3Rot.X, beansVector3Rot.Y, beansVector3Rot.Z);
                sceneCamrea.transform.DOLocalRotate(rot, duration).SetEase(ease);
            }
            float curRatio = Stage.inst.width / (float)Stage.inst.height;
            float designRatio = Mc.Ui.DesignW / (float)Mc.Ui.DesignH;
            float factor = designRatio / curRatio;
            sceneCamrea.DOFieldOfView(cameraTrackData.FieldOfView[track] * Mathf.Max(1, factor), duration);


        }

        public void OnClose()
        {
            logger.ReleaseCritical("Cancel role-creating, logout lobby");
            Mc.LoginStep.StartLobbyLogout();
            HideSelf();
        }

        private void RenderTabItem(int index, GObject obj)
        {
            var item = obj.asCom;
            item.GetChild("title").asTextField.text = tabTitle[index];
            item.GetChild("icon").asLoader.url = tabIconUrl[index];
            //item.touchable = !lockType[index];
            item.GetController("lock").SetSelectedIndex(lockType[index]?1:0);
            item.asButton.selected = selectedTabIndex == index;
        }
  

        public virtual void OnClickClose()
        {

            if (createRoleStep == 1)
            {
                MgrAudio.PlayAudioEventAsync("UI_Common_Open");
                createRoleStep--;
                SetCreateRoleStep();
                return;
            }
            MsgBoxInfo boxInfo = new MsgBoxInfo(157, new() { () =>{ ReturnLogin();
            },null});
            boxInfo.SetCenter = true;
            UiMsgBox.ShowMsgBox(boxInfo);
        }

        private void ReturnLogin()
        {
            logger.ReleaseCritical("Cancel role-creating, logout lobby");
            Mc.LoginStep.StartLobbyLogout();
            HideSelf();
        }

        public void OnInputNameChanged()
        {
            bool isHasWihiteSpace = inputBox.text.Contains(" ");
            if (isHasWihiteSpace)
            {
                inputBox.text = inputBox.text.Replace(" ", ""); // 替换空格为空字符串
            }
            var name = inputBox.text;
            int len = 0;
            foreach (char c in name)
            {
                len += 1;
                // 如果字符是中文或者是全角字符，增加2到长度  
                if (StringCheckUtility.IsChineseChar(c) || StringCheckUtility.IsFullWidthSymbols(c))
                {
                    len += 1;
                }
            }
            int minLen = Mc.Tables.TBConsts.PlayerNameLengthMin;
            int maxLen = Mc.Tables.TBConsts.PlayerNameLengthMax;
            //btnColorCtrl.SetSelectedIndex(1);
            //btnInputName.touchable = false;
            if (len > maxLen)
            {
                int targetLen = 0;
                int pos = 0;
                for (int i = 0; i < name.Length; i++)
                {
                    char c = name[i];
                    targetLen += 1;
                    // 如果字符是中文或者是全角符号，增加2到长度
                    if (StringCheckUtility.IsChineseChar(c) || StringCheckUtility.IsFullWidthSymbols(c))
                    {
                        targetLen += 1;
                    }
                    if (targetLen > maxLen)
                    {
                        pos = i;
                        //裁切至最大长度
                        inputBox.text = name.Substring(0, pos);
                        break;
                    }
                }
            }
            else if (len < minLen)
            {
                if(len<=0)
                    binder.Root.Content.RoleInfo.InputName.InputName.CtrlNameType.SetSelectedIndex(0);
                else
                    binder.Root.Content.RoleInfo.InputName.InputName.CtrlNameType.SetSelectedIndex(1);
                finishBtn.Enable = false;
            }
            var inputNameLength = inputBox.text.Length;
            if ((inputNameLength >= minLen && inputNameLength <= maxLen) || (len >= minLen && inputNameLength <= maxLen))
            {
                if (!StringCheckUtility.IsLegalKeyboardCharacterByStr(inputBox.text))
                {
                    finishBtn.Enable = false;
                    binder.Root.Content.RoleInfo.InputName.InputName.CtrlNameType.SetSelectedIndex(2);
                }
                else
                {
                    binder.Root.Content.RoleInfo.InputName.InputName.CtrlNameType.SetSelectedIndex(0);
                    finishBtn.Enable = true;
                }
            }
        }

        #region TouchRotate
        private void OnModeTouchBegin(EventContext ctx)
        {
            ctx.CaptureTouch();
            lastTouchPos = ctx.inputEvent.position;
            IsTouchingModel = true;
        }

        private void OnModeTouchMove(EventContext ctx)
        {
            if (null == playerLoader) return;
            var curPos = ctx.inputEvent.position;
            var deltaPos = (curPos - lastTouchPos) * 0.5f;
            playerLoader.transform.Rotate(Vector3.down, deltaPos.x);
            lastTouchPos = curPos;
        }

        private void OnModeTouchEnd(EventContext ctx)
        {
            IsTouchingModel = false;
        }
        #endregion


    }
}
