using Cysharp.Text;
using FairyGUI;
using UnityEngine;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Ui.Binder.GameStory;
using WizardGames.Soc.SocClient.Ui.Utils;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.SocClient.Ui
{
    public class UiStoryTaskItem : IComBinderDestroy
    {
        private ComBinderDictionary<ComStoryTaskRewardItem01Binder> rewardBinders = new(c=>new(c));

        private ComStoryTaskItemBinder binder;
        private ComStateBtn button;
        public long Data { get; private set; }
        public UiStoryTaskItem(GComponent root)
        {
            binder = new(root);
            button = binder.Btn as ComStateBtn;
            button.SetSoundClick(Mc.Tables.TbUiAudio.UniversalButtonClick, OnButtonClicked);
        }

        public void RefreshItemRenderer()
        {
            binder.RewardList.itemRenderer = OnItemRenderer;
        }

        private void OnButtonClicked(EventContext context)
        {
            if (Mc.Mission.IsGetReward(Data, TaskNodeIndex.CompletedAndNotGetReward))
            {
                OnReceiveClicked();
            }
            else if (Mc.Mission.IsGetReward(Data, TaskNodeIndex.InProgress))
            {
                var cfg = Mc.Tables.TbQuestPhase.GetOrDefault(Data);
                if (!string.IsNullOrEmpty(cfg?.OpenWin))
                {
                    OnGotoClicked();
                }
            }
        }

        private void OnGotoClicked()
        {
            var cfg = Mc.Tables.TbQuestPhase.GetOrDefault(Data);
            if (cfg != null && !string.IsNullOrEmpty(cfg.OpenWin))
            {
                Mc.Ui.OpenWindow(cfg.OpenWin);
            }
        }

        private void OnReceiveClicked()
        {
            Mc.Story.ReceiveRewards(Data);
        }

        private int[] tempRewardIds;
        private void OnItemRenderer(int index, GObject item)
        {
            if (tempRewardIds != null && tempRewardIds.Length > index)
            {
                var itemCfg = Mc.Story.GetItemCfg(tempRewardIds[index],out int rewardNum);
                if (itemCfg != null)
                {
                    var itemBinder = rewardBinders.Get(item);
                    var itemIcon = itemBinder.Icon_sizeChange as ComItemIcon;
                    itemIcon?.SetTepmlateData(itemCfg);
                    itemIcon?.SetValue(rewardNum);
                    itemIcon?.SetRewardState(Mc.Mission.IsCompleted(Data)
                        ? 1
                        : 0);
                    itemIcon?.EnableItemTipsClickAutoPosition(true);
                }
            }
        }

        public void SetData(long taskId)
        {
            Data = taskId;
            var cfg = Mc.Tables.TbQuestPhase.GetOrDefault(taskId);
            if (cfg != null)
            {
                binder.ProgressText.visible = cfg.EndConditionParameter[0] > 0;
                binder.Btn.visible = true;

                SafeUtil.SafeSetText(binder.Descript, ZString.Format(cfg.TaskDescribe, cfg.EndConditionParameter[0]));

                tempRewardIds = cfg.TaskRewardId;
                binder.RewardList.numItems = cfg.TaskRewardId.Length;
                if (Mc.Mission.IsGetReward(Data, TaskNodeIndex.CompletedAndNotGetReward))
                {//可领取奖励
                    button.enabled = true;
                    button.SetTitle(LanguageManager.GetTextConst(LanguageConst.Receive));
                    button.ShowRedDot(true);
                    SafeUtil.SafeSetText(binder.ProgressText, ZString.Format("{0}/{1}", cfg.EndConditionParameter[0], cfg.EndConditionParameter[0]));
                }else if (Mc.Mission.IsCompleted(Data))
                {//已领取
                    button.enabled = false;
                    button.SetTitle(LanguageManager.GetTextConst(LanguageConst.Received));
                    button.ShowRedDot(false);
                    SafeUtil.SafeSetText(binder.ProgressText, ZString.Format("{0}/{1}", cfg.EndConditionParameter[0], cfg.EndConditionParameter[0]));
                }
                else if(Mc.Mission.IsGetReward(Data, TaskNodeIndex.InProgress))
                {
                    long curProgress = Mc.Mission.GetTaskCount(taskId);
                    SafeUtil.SafeSetText(binder.ProgressText,
                        ZString.Format("{0}/{1}", curProgress, cfg.EndConditionParameter[0]));
                    button.ShowRedDot(false);
                    if (!string.IsNullOrEmpty(cfg.OpenWin))
                    {//跳转
                        button.touchable = true;
                        button.Enable = true;
                        button.SetTitle(LanguageManager.GetTextConst(LanguageConst.GoTo));
                    }
                    else
                    {
                        button.enabled = false;
                        button.SetTitle(LanguageManager.GetTextConst(LanguageConst.Doing));
                    }
                }
                else
                {
                    button.enabled = false;
                    button.SetTitle(LanguageManager.GetTextConst(LanguageConst.NotStart));
                    button.ShowRedDot(false);
                    SafeUtil.SafeSetText(binder.ProgressText,
                        ZString.Format("0/{0}", cfg.EndConditionParameter[0]));
                }
            }
        }
        public void OnEnable()
        {
        }

        public void OnDisable()
        {
        }

        public void OnDestroy()
        {
            rewardBinders.Clear();
        }
    }
}