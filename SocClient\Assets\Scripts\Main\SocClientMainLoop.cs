using Animation;
using Assets.Scripts.MicroServiceClient;
using Combat;
using Cysharp.Text;
using FairyGUI;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Systems;
using UnityEngine;
using UnityEngine.AzureSky;
using Utilities;
using WizardGames.Soc.AdaptivePerformance;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Common.Profile;
using WizardGames.Soc.Common.Syncronization;
using WizardGames.Soc.Common.Unity.Character;
using WizardGames.Soc.Common.Unity.Character.Utility;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Go;
using WizardGames.Soc.Common.Unity.Loader.CrashSight;
using WizardGames.Soc.Common.Unity.Loader.Logger;
using WizardGames.Soc.Common.Unity.Main;
using WizardGames.Soc.Common.Unity.Manager;
using WizardGames.Soc.Common.Unity.ObjPool;
using WizardGames.Soc.Common.Unity.SocAssetBundle.SocBundleRuntime;
using UnityEngine.Profiling;
using WizardGames.Soc.Common.Combat;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.Common.Weapon;
using WizardGames.Soc.Procedural;
using WizardGames.Soc.ShaderVariantManager;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocClient.ActionExecutor;
using WizardGames.Soc.SocClient.Animation;
using WizardGames.Soc.SocClient.Collection;
using WizardGames.Soc.SocClient.GoLoader;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Network;
using WizardGames.Soc.Common.Unity.Construction;
using WizardGames.Soc.Common.Unity.Loader;
using WizardGames.Soc.SocClient.Audio;
using WizardGames.Soc.Common.Main.Loop;
using WizardGames.Soc.Common.Synchronization;
using WizardGames.Soc.Common.SimpleCustom;

namespace WizardGames.Soc.SocClient.Main
{
    /// <summary>
    /// 客户端主循环
    /// </summary>
    [DefaultExecutionOrder(-4)]
    public class SocClientMainLoop : UnityMainLoop
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(SocClientMainLoop));

        protected override LoopType _loopType => LoopType.GameSpace;

        protected int allPhsicsTime;
        //缓存
        // private List<KeyValuePair<AnimationClip, AnimationClip>> clip_map = new List<KeyValuePair<AnimationClip, AnimationClip>>();

        /// <summary>
        /// 固定帧时间
        /// </summary>
        protected int FrameTime = 33;

        /// <summary>
        /// 记录下逻辑高帧率时间，防止开火战斗时候频繁切换
        /// </summary>
        private int HighFrameTimeChangeTime;

        /// <summary>
        /// 帧间隔剩余时间
        /// </summary>
        protected int TimeRemainder;

        /// <summary>
        /// 帧间隔剩余时间
        /// </summary>
        protected int PhysicsTimeRemainder;

        protected float lastLowMemoryCleanTime;

        protected static long lastNetWorkUpdateTs;

        /// <summary>
        /// 是否可以执行本地移动预判
        /// </summary>
        // private bool canPredictMove = false;
        private long lastTouchTime;

        private bool isNoTouch = true;

        private int horseRealTimeDelta;

        // private string[] newbiePreUI = {"UiCraft", "UiReputationReward", "UiKatyushaMini", "UiInventoryOtherside", "UiOtherSide", "UiCartoon",
        //                                 "UiMainMap","UiTaskTips","UIConstructionInfoTipsPopup","UiItemTip","UiTerritoryCenter",
        //                                 "UiItemTips","UiDamageScreenEffect","UiRemoteControl","UiCountDownPopTip","UiThrownTips","UiElectricInfo","UiFocusInfo","UiNpcTextTip","UiWorldFlag"};

        private string[] newbiePreUI = { };
        
        //可以提前opend且不报错的UI
        private string[] newbiePreOpenUI = { "UiItemTips", "UiRemoteControl", "UiCountDownPopTip", "UiElectricInfo", "UIConstructionInfoTipsPopup",
                                             "UiFocusInfo","UiWorldFlag","UiTaskTips"};

        /// <summary>
        /// 应用程序最大可使用内存，在OnLowMemory时记录，供UI做视频/Timeline播放的参考依据
        /// </summary>
        public static float AppMaxUsageMemForUIPlay { get; private set; } = -1;

        /// <summary>
        /// 脚本启动
        /// </summary>
        public override async void Start()
        {
            Mc.LoginStep.StepLog("[SocClientMainLoop] Start");

            Mc.Msg.AddListener(EventDefine.AfterAnimBegin, FireAfterAnimBeginSystem);

            ClientLadderAPI.Init();

            await PlayerLoader.Preload();
            //Application.targetFrameRate = 30;
            // 从大厅进入地图需要清理对象池
            GoPool.Init();
            //Profiler.ShowInUnityProfiler = true;

            //事件注册
            _ = ClientSnapshotReceiverDebugger.Instance;

            //注册主角加载完成的回调
            Mc.MyPlayer.MyPlayerLoadSuccess += Mc.LoginStep.OnMyPlayerLoadSuccess;
            McCommonUnity.Material.GetTerrainMaterial += MaterialGetter;


            Mc.System.InitSystem(new ClientSystemInitHandler());

            Mc.LoginStep.StepLog("[SocClientMainLoop] OnEnterWorld...");
            await Mc.Instance.OnEnterWorld();
            McCommonUnity.Scene.SetLoadingHintWithKey(LanguageConst.LoadAlwaysInMemAB, 25);
            //MC.mShareData.bWorldInited = true;
            Mc.LoginStep.StepLog("[SocClientMainLoop] AfterEnterWorld...");
            await Mc.Instance.AfterEnterWorld();

            //开始处理预加载资源
            Mc.LoginStep.StepLog("[SocClientMainLoop] PreloadAssets...");
            await PreloadAssets();

            // 检查Loadin过程中是否被顶号, 如果顶号, 返回登录界面
            var microSvr = MicroServiceClient.Instance;
            if (microSvr.IsKickOutInLoading)
            {
                Mc.LoginStep.StepLog("[SocClientMainLoop] Loading完毕, 发现被顶号, 返回登录界面...");
                microSvr.ProcessKickOut(true);
                return;
            }

            bool hasDevLogin = Application.isEditor || ApplicationConfig.Instance.NeedDeveloperLogin();
            bool hasMirrorID = !string.IsNullOrEmpty(Mc.Config.MirrorID) && ulong.TryParse(Mc.Config.MirrorID, out ulong mirrorID) && mirrorID > 0;
            bool skipCheckLobby = hasDevLogin && hasMirrorID;
            if (!skipCheckLobby)
            {
                // 允许大厅重连最大次数弹窗
                bool hasPopup = microSvr.NotifyGameLoadingState(false);
                // 检查Loading过程中大厅是否断线, 如果断线, 执行重连
                if (!hasPopup && !microSvr.IsConnected)
                {
                    if (microSvr.IsInReconnect)
                    {
                        Mc.LoginStep.StepLog("Loading完毕, 发现大厅正在重连, 等待大厅重连完毕...");
                    }
                    else
                    {
                        Mc.LoginStep.StepLog("Loading完毕, 发现大厅断开连接, 执行重连并等待大厅重连完毕...");
                        microSvr.ProcessReconnect();
                    }
                }
                while (!microSvr.IsConnected)
                {
                    await Task.Yield();
                }
            }

            Mc.LoginStep.StepLog("[SocClientMainLoop] 大厅连接正常，EnterGameSpace");
            EnterGameSpace();

            // 检查Loading过程中战斗服是否断线, 如果断线, 执行重连
            Mc.NetWorkMonitor.IsAllowToFastReconnect = true;
            var localService = Mc.LocalService;
            if (!localService.IsConnected)
            {
                Mc.LoginStep.StepLog("[SocClientMainLoop] Loading完毕, 发现World断开连接, 等待World重连");
                bool continueToLoading = Mc.NetWorkMonitor.DoFastReconnect();
                if (!continueToLoading)
                {
                    Mc.LoginStep.StepLog("[SocClientMainLoop] reconnect time out, back to lobby, skip loading...");
                    return;
                }
            }

            Mc.LoginStep.StepLog(ZString.Format("[SocClientMainLoop] Wait for connecting...IsConnected = {0}, IsLoadingSucess = {1}", localService.IsConnected, ProcessEntity.Instance.IsLoadingSucess));
            while (!localService.IsConnected || !ProcessEntity.Instance.IsLoadingSucess)
            {
                await Task.Yield();
            }

            //通知服务器客户端准备完毕
            Mc.LoginStep.StepLog("[SocClientMainLoop] 通知服务端 LoadingSuccess, 等待接收全量快照");
            using (LoadFullSnapshotProgress loadSnapshotProgress = new LoadFullSnapshotProgress())
            {
                if (!Mc.ObserverMode)
                {
                    ProcessEntity.Instance.RemoteCallLoadingSuccess(ERpcTarget.World);
                }
                else
                {
                    ProcessEntity.Instance.RemoteCallObserverLoadingSuccess(ERpcTarget.World);
                }

                // 大厅、战斗服都成功连接，等待全量快照
                Mc.LoginStep.StepLog("[SocClientMainLoop] wait for IsFullSnapshotReceived...");
                McCommonUnity.Scene.SetLoadingHintWithKey(LanguageConst.WaitForSnapshot, 45);
                while (!Mc.SnapshotReceiver.IsFullSnapshotReceived)
                {
                    if (Application.isEditor && !Application.isPlaying) return;
                    await Task.Yield();
                }

                Mc.LoginStep.StepLog("[SocClientMainLoop] wait for loadSnapshotProgress.IsDone...");
                loadSnapshotProgress.ProgressStart = 50;
                loadSnapshotProgress.ProgressTotal = 49;
                while (!loadSnapshotProgress.IsDone)
                {
                    McCommonUnity.Scene.SetLoadingHintWithKey(LanguageConst.WaitForSnapshot, loadSnapshotProgress.Progress);
                    await Task.Yield();
                }
            }

            Mc.LoginStep.StepLog("[SocClientMainLoop] PreloadCharacterBeltAsset...");
            await PreloadCharacterBeltAsset();
            //await PreloadMonsterAsset();
            Mc.LoginStep.StepLog("[SocClientMainLoop] PreloadCommonAssetsPath...");
            PreloadCommonAssetsPath();
            Mc.LoginStep.StepLog("[SocClientMainLoop] WorldLoadComplete...");
            await McCommonUnity.Scene.WorldLoadComplete();

            try
            {
                //向Simulator获取玩家周围的建筑，等生成完成之后再隐藏LoadingUI
                HideLoadingUI();
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("Fail: {0};{1}", ex.Message, ex.StackTrace);
            }

            McCommon.Instance.IsClient = true;
            logger.Info("[SocClientMainLoop] OnFullSnapshotReceived start!");
            try
            {
                Mc.LoginStep.OnFullSnapshotReceived();
                Mc.PowerOptimization.OnFullSnapshotReceived();
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("Fail: {0};{1}", ex.Message, ex.StackTrace);
            }
            logger.Info("[SocClientMainLoop] OnFullSnapshotReceived end!");
            Application.lowMemory += OnLowMemory;
#if POCO
            if (Mc.PocoMa != null)
            {
                this.gameObject.AddComponent<ProfilerRecorderData>();
            }
#endif
            if (Stage.avaliable)
            {
                Stage.inst.TouchInputDataSync -= Mc.Control.TouchInput.TouchDataSync;
                Stage.inst.TouchInputDataSync += Mc.Control.TouchInput.TouchDataSync;
                logger.Info("TouchInput.TouchDataSync挂载成功！");
            }
            else
            {
                logger.Error("TouchInput.TouchDataSync挂载失败！");
            }

            // 完全加载完成，开始统计性能
            Mc.Profiler.OnEnterWorldCompletely();
            McCommonUnity.Scene.SetLoadingHintWithKey(LanguageConst.WaitForStreamLoadFinish, 100);
            
            Mc.LoginStep.StepLog("[SocClientMainLoop] Start finished!");
        }

        public override void OnDestroy()
        {
            ClientLadderAPI.Destroy();
            Mc.Msg.RemoveListener(EventDefine.AfterAnimBegin, FireAfterAnimBeginSystem);
            PlayerLoader.Unload();

            if (Stage.avaliable)
            {
                Stage.inst.TouchInputDataSync -= Mc.Control.TouchInput.TouchDataSync;
                logger.Info("TouchInput.TouchDataSync卸载成功");
            }
            else
            {
                logger.Error("TouchInput.TouchDataSync卸载失败！"); ;
            }

            // ResDB.cachedObjDic.Clear();
            McCommon.Instance.IsClient = false;
            Application.lowMemory -= OnLowMemory;
            Mc.MyPlayer.MyPlayerLoadSuccess -= Mc.LoginStep.OnMyPlayerLoadSuccess;
            McCommonUnity.Material.GetTerrainMaterial -= MaterialGetter;

            HeldItemAPI.Dispose();

            base.OnDestroy();
        }

        protected void HideLoadingUI()
        {
            logger.Info("[SocClientMainLoop] HideLoadingUI");
            McCommonUnity.Scene.HideLoadingUI();
            //Mc.Ui.RemoveWindow("UiLoading");
        }

        #region Preload

        private void PreloadWeaponOrItem(long templateId, long skinId = 0)
        {
            logger.InfoFormat(">>>preload asset templateId:{0}", templateId);

            if (HeldItemUtility.GetPrefabPath(templateId, skinId, out string fp, out string tp) /*ClientHeldItemUtil.GetPrefabPath(templateId, out string fp, out string tp)*/)
            {
                if (!string.IsNullOrEmpty(fp))
                {
                    if (!GoPool.IsExist(fp))
                    {
                        GoPool.Init(fp, new PoolConfig<GameObject>(10));
                    }

                    var fpGo = GoPool.Get(fp);
                    logger.InfoFormat(">>>>preload fpGo:{0}", fpGo);
                    if (fpGo != null)
                        GoPool.Release(fp, fpGo);
                }

                if (!string.IsNullOrEmpty(tp))
                {
                    if (!GoPool.IsExist(tp))
                    {
                        GoPool.Init(tp, new PoolConfig<GameObject>(10));
                    }

                    var tpGo = GoPool.Get(tp);
                    logger.InfoFormat(">>>>preload tpGo:{0}", tpGo);
                    if (tpGo != null)
                        GoPool.Release(tp, tpGo);
                }
            }

            // if (HeldItemUtility.GetAnimatorPath(templateId, skinId, out string fpAnim, out string tpAnim) /*ClientHeldItemUtil.GetAnimPath(templateId, out string fpAnim, out string tpAnim)*/)
            // {
            //     // var fpAnimAsset = McCommonUnity.Res.provider.Load<UnityEngine.Object>(fpAnim + ".overrideController");
            //     // var tpAnimAsset = McCommonUnity.Res.provider.Load<UnityEngine.Object>(tpAnim + ".overrideController");
            //     var fpAnimAsset = McCommonUnity.Res.provider.LoadAsync<UnityEngine.Object>(fpAnim);
            //     //var tpAnimAsset = McCommonUnity.Res.provider.LoadAsync<UnityEngine.Object>(tpAnim);
            //     Debug.Log($">>>>preload fpAnimAsset:{fpAnimAsset}");
            //     //Debug.Log($">>>>preload tpAnimAsset:{tpAnimAsset}");
            // }
        }

        public async Task PreloadCharacterBeltAsset()
        {
            //获取主角快捷栏
            McCommonUnity.Scene.SetLoadingHintWithKey(LanguageConst.AssetShortcutsAssetPreload);
            if (Mc.CollectionItem != null && Mc.CollectionItem.InventoryCom != null)
            {
                var belt = Mc.CollectionItem.InventoryCom.ContainerBelt;
                foreach (var kvp in belt.Node)
                {
                    var idx = kvp.Key;
                    var itemNode = kvp.Value as BaseItemNode;
                    if (itemNode == null)
                        continue;
                    var templateId = itemNode.BizId;
                    PreloadWeaponOrItem(templateId, itemNode.SkinId);
                    await Task.Yield();
                }
            }

        }

        private HashSet<string> monsterAssetsPath = new HashSet<string>(16);

        public void PreloadMonsterAsset()
        {
            //获取怪物资源
            McCommonUnity.Scene.SetLoadingHintWithKey(LanguageConst.AssetMonsterAssetPreload);

            var dl = McCommonUnity.Tables.TbMonster.DataList;
            monsterAssetsPath.Clear();
            if (dl.Count > 0)
            {
                foreach (var mon in dl)
                {
                    if (!monsterAssetsPath.Contains(mon.PrefabPath))
                    {
                        monsterAssetsPath.Add(mon.PrefabPath);
                    }
                }
            }

            if (monsterAssetsPath.Count > 0)
            {
                foreach (var path in monsterAssetsPath)
                {
                    // var asset = McCommonUnity.Res.provider.LoadAsync<UnityEngine.Object>(path + ".prefab");
                    var go = GoPool.Get(path);
                    GoPool.Release(path, go);
                }
            }

            logger.Info(">>>>end preload monster asset");
        }

        private List<string> commonAssetsPath = new List<string>(16);

        public void PreloadCommonAssetsPath()
        {
            McCommonUnity.Scene.SetLoadingHintWithKey(LanguageConst.AssetCommonAssetPreload);
            if (commonAssetsPath.Count > 0)
            {
                foreach (var path in commonAssetsPath)
                {
                    var asset = UtilsLoad.LoadAssetSync<UnityEngine.Object>(path);
                }
            }

            logger.Info(">>>>end preload common asset");
        }

        /// <summary>
        /// 预加载资源
        /// </summary>
        /// <returns></returns>
        public async Task PreloadAssets()
        {
            logger.Info("[SocClientMainLoop] PreloadAssets start");
            
            SocAnimCache.Init();

            Dictionary<int, AnimationClip> clip_dict = new Dictionary<int, AnimationClip>(8);

            // if (PlayerConfig != null)
            // {
            //     //看资源是否初始化完成
            //     if (SocAnimCache.fp_original_clips == null || SocAnimCache.tp_original_clips == null)
            //     {
            //         //如果fp ctrl初始化完成，则初始化fp clips
            //         if (SocAnimCache.fp_original_clips == null && PlayerConfig.WeaponMainController)
            //         {
            //             var fp_weapon_over_ctrl = PlayerConfig.WeaponMainController as AnimatorOverrideController;
            //             if (fp_weapon_over_ctrl != null)
            //             {
            //                 fp_weapon_over_ctrl.GetOverrides(clip_map);
            //
            //                 //拿到override信息，依次赋值
            //                 //暂时只处理单层
            //                 SocAnimCache.fp_original_clips = new AnimationClip[SocConstantName.WeaponAnimStateCount];
            //                 SocAnimCache.fp_original_clipIds = new int[SocConstantName.WeaponAnimStateCount];
            //
            //                 for (int i = 0; i < clip_map.Count; i++)
            //                 {
            //                     var org_clip = clip_map[i].Key;
            //                     var org_clip_name = org_clip.name;
            //                     var split = org_clip_name.Split('_');
            //
            //                     if (org_clip_name == "Normal_idle")
            //                     {
            //                         SocAnimCache.fp_weapon_idle_clip = org_clip;
            //                         SocAnimCache.fp_weapon_idle_clipId = org_clip.GetInstanceID();
            //                     }
            //
            //                     //0-9的才加入
            //                     if (split.Length > 1 && int.TryParse(split[1], out int index) && index < SocConstantName.WeaponAnimStateCount)
            //                     {
            //                         clip_dict[index] = org_clip;
            //                     }
            //                 }
            //
            //                 for (int i = 0; i < SocConstantName.WeaponAnimStateCount; i++)
            //                 {
            //                     if (clip_dict.TryGetValue(i, out var clip))
            //                     {
            //                         SocAnimCache.fp_original_clips[i] = clip;
            //                         SocAnimCache.fp_original_clipIds[i] = clip.GetInstanceID();
            //                     }
            //                 }
            //             }
            //             else
            //             {
            //                 logger.Error("fp_weapon_over_ctrl is null");
            //             }
            //         }
            //
            //         //如果tp ctrl初始化完成，则初始化tp clips
            //         if (SocAnimCache.tp_original_clips == null && PlayerConfig.WeaponThirdMainController)
            //         {
            //             var tp_weapon_over_ctrl = PlayerConfig.WeaponThirdMainController as AnimatorOverrideController;
            //             if (tp_weapon_over_ctrl != null)
            //             {
            //                 tp_weapon_over_ctrl.GetOverrides(clip_map);
            //
            //                 //拿到override信息，依次赋值
            //                 //暂时只处理单层
            //                 SocAnimCache.tp_original_clips = new AnimationClip[SocConstantName.WeaponAnimStateCount];
            //                 SocAnimCache.tp_original_clipIds = new int[SocConstantName.WeaponAnimStateCount];
            //
            //                 clip_dict.Clear();
            //
            //                 for (int i = 0; i < clip_map.Count; i++)
            //                 {
            //                     var org_clip = clip_map[i].Key;
            //                     var org_clip_name = org_clip.name;
            //                     var split = org_clip_name.Split('_');
            //
            //                     if (org_clip_name == "Normal_idle")
            //                     {
            //                         SocAnimCache.tp_weapon_idle_clip = org_clip;
            //                         SocAnimCache.tp_weapon_idle_clipId = org_clip.GetInstanceID();
            //                     }
            //
            //                     //0-9的才加入
            //                     if (split.Length > 1 && int.TryParse(split[split.Length - 1], out int index) && index < SocConstantName.WeaponAnimStateCount)
            //                     {
            //                         clip_dict[index] = org_clip;
            //                     }
            //                 }
            //
            //                 for (int i = 0; i < SocConstantName.WeaponAnimStateCount; i++)
            //                 {
            //                     if (clip_dict.TryGetValue(i, out var clip))
            //                     {
            //                         SocAnimCache.tp_original_clips[i] = clip;
            //                         SocAnimCache.tp_original_clipIds[i] = clip.GetInstanceID();
            //                     }
            //                 }
            //             }
            //             else
            //             {
            //                 logger.Error("tp_weapon_over_ctrl is null");
            //             }
            //         }
            //     }
            // }
            // else
            // {
            //     logger.Error("PlayerConfig is null");
            // }

            //加载地图相关预加载项
            McCommonUnity.Scene.SetLoadingHintWithKey(LanguageConst.AssetMapAssetPreload);
            var sceneCfg = McCommonUnity.Scene.GetSceneConfig(Common.Unity.Scene.SceneName.GameSpace);
            if (sceneCfg.preloadItemIdClient != null)
            {
                foreach (var templateId in sceneCfg.preloadItemIdClient)
                {
                    PreloadWeaponOrItem(templateId);
                    await Task.Yield();
                }
            }
            
            logger.Info("[SocClientMainLoop] PreloadAssets sceneCfg.preloadItemIdClient done");

            //预加载的go
            McCommonUnity.Scene.SetLoadingHintWithKey(LanguageConst.AssetGoAssetPreload);
            if (sceneCfg.preloadGoInPoolClient != null)
            {
                foreach (var assetPath in sceneCfg.preloadGoInPoolClient)
                {
                    logger.InfoFormat(">>>>start preload go:{0}", assetPath);
                    var go = GoPool.Get(assetPath);
                    logger.InfoFormat(">>>>preload go:{0}, Go:{1}", assetPath, go);
                    GoPool.Release(assetPath, go);
                }
            }

            logger.Info("[SocClientMainLoop] PreloadAssets sceneCfg.preloadGoInPoolClient done");
            
            //预加载的asset
            McCommonUnity.Scene.SetLoadingHintWithKey(LanguageConst.AssetAssetPreload);
            if (sceneCfg.preloadAssetClient != null)
            {
                foreach (var assetPath in sceneCfg.preloadAssetClient)
                {
                    logger.InfoFormat(">>>>start preload asset:{0}", assetPath);
                    var asset = UtilsLoad.LoadAssetSync<UnityEngine.Object>(assetPath);
                    logger.InfoFormat(">>>>preload asset:{0}, asset:{1}", assetPath, asset);
                }
            }
            
            logger.Info("[SocClientMainLoop] PreloadAssets sceneCfg.preloadAssetClient done");

            //新手关需要预载UI界面，防止卡顿
            try
            {
                if (PlayHelper.IsNewbie)
                {
                    for (int i = 0; i < newbiePreUI.Length; i++)
                    {
                        Mc.Ui.LoadWindow(newbiePreUI[i]);
                        logger.InfoFormat("NewBie PreLoad UI: {0}", newbiePreUI[i]);
                    }

                    for (int i = 0; i < newbiePreOpenUI.Length; i++)
                    {
                        Mc.Ui.OpenWindow(newbiePreOpenUI[i]);
                        Mc.Ui.HideWindow(newbiePreOpenUI[i], true);
                        logger.InfoFormat("NewBie PreOpen UI: {0}", newbiePreOpenUI[i]);
                    }
                }
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("NewBie PreLoad UI Fail: {0}", ex.Message);
            }

            //新手关临时处理一下，后续要走异步加载
            if (PlayHelper.IsNewbie)
            {
                PreloadConstructionAssets();
            }

            //新手关引导线资源预加载
            try
            {
                if (PlayHelper.IsNewbie)
                {
                    Mc.GuideLine.PreloadEffectResource();
                }
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("Newbie Preload GuideLine Fail: {0}", ex.Message);
            }

            logger.Info("[SocClientMainLoop] PreloadAssets newbie done");
            
#if !(UNITY_IOS || UNITY_STANDALONE_OSX)
            PreLoadTeammateDefaultModel();
#endif

            if (PlayerPrefs.GetInt("PreloadGameObjects", 1) == 0)
            {
                return;
            }

            logger.Info("[SocClientMainLoop] PreloadAssets finished");
        }

        private void CachePreloadGameObjects(string prefabPath, int maxSize)
        {
            if (string.IsNullOrEmpty(prefabPath))
            {
                return;
            }
            List<GameObject> cacheGameObjects = new List<GameObject>();

            if (!GoPool.IsExist(prefabPath))
            {
                var goPool = GoPool.Init(prefabPath, new PoolConfig<GameObject>(16));
                for (int i = 0; i < maxSize; i++)
                {
                    cacheGameObjects.Add(goPool.Get());
                }

                for (int i = 0; i < maxSize; i++)
                {
                    goPool.Release(cacheGameObjects[i]);
                }
            }
        }

        #endregion

        private void PreLoadTeammateDefaultModel()
        {
            if (Mc.Tables.TbGlobalConfig == null || Mc.Tables.TbEquipAppearance == null)
            {
                return;
            }
            var info = Mc.Tables.TbEquipAppearance.GetOrDefault(Mc.Tables.TbGlobalConfig.UITeamDefaultModel);
            if (info == null)
            {
                return;
            }
            var pool = GoPool.GetOrCreatePool(info.TpPrefab);
            pool.CanClear = false;

            var go = pool.Get();
            pool.Release(go);
        }

        //新手关需要预加载建筑拟建体领地柜、地基、单扇门框门,todo临时处理，后续建筑拟建体需要要走异步加载
        private void PreloadConstructionAssets()
        {
            List<string> preloadGoPaths = new();
            //墙体正反面资源
            preloadGoPaths.Add(Mc.Tables.TbConstructionConstantConfig.FaceShowNodePrefabPath);
            //地基、领地柜、单扇门框门Template
            CommonConstructionUtils.GetPartTemplateConfig((int)PartType.Foundation);
            CommonConstructionUtils.GetPartTemplateConfig((int)PartType.ToolCupboard);
            CommonConstructionUtils.GetPartTemplateConfig((int)PartType.WallDoorway);

            //地基拟建体
            var foundationPath = CommonConstructionUtils.GetSeparatePrefabPathByType((int)PartType.Foundation, EConstructionPrefabType.Prebuild, 0);
            if (!string.IsNullOrEmpty(foundationPath))
            {
                preloadGoPaths.Add(foundationPath);
            }
            //领地柜拟建体
            var toolCupboardPath = CommonConstructionUtils.GetSeparatePrefabPathByType((int)PartType.ToolCupboard, EConstructionPrefabType.Prebuild, 0);
            if (!string.IsNullOrEmpty(toolCupboardPath))
            {
                preloadGoPaths.Add(toolCupboardPath);
            }

            //单扇门框门拟建体
            var combo1Path = CommonConstructionUtils.GetComboGroupPrebuildPathById(1);
            if (!string.IsNullOrEmpty(combo1Path))
            {
                preloadGoPaths.Add(combo1Path);
            }

            foreach (string preloadGoPath in preloadGoPaths)
            {
                var pool = GoPool.GetOrCreatePool(preloadGoPath);
                pool.CanClear = false;
                var go = pool.Get();
                pool.Release(go);
            }
            preloadGoPaths.Clear();
        }


        /// <summary>
        /// 循环更新
        /// </summary>
        public override void Update()
        {
            base.Update();
#if UNITY_EDITOR
            if (Input.GetKeyDown(KeyCode.F4))
            {
                WizardGames.Soc.Common.Unity.LagCompensationDebugger.OpenDebugger<WizardGames.Soc.SocClient.Auxiliary.ClientLagCompensationDebugger>(!Common.Unity.LagCompensationDebugger.IsDebuggerOpen);
            }

            if (Input.GetKeyDown(KeyCode.F6) && Mc.Timeline.IsPlaying)
            {
                Mc.Timeline.JumpToEnd();
            }

            if (Input.GetKeyDown(KeyCode.F5))
            {
                PlayerStateConflictParser.DataDirty = true;
                PlayerStateConflictParser.InitData();
            }
            if (Input.GetKeyDown(KeyCode.F7))
            {
                WizardGames.Soc.SocClient.Utility.InteractionUtil.StopInteract();
            }
            // if (Input.GetKeyDown(KeyCode.F11))
            // {
            //     Mc.Ui.OpenWindow("UiScreenSave");
            // }
            // if (Input.GetKeyDown(KeyCode.F12))
            // {
            //     Mc.Ui.HideWindow("UiScreenSave");
            // }
            // if (Input.GetKeyDown(KeyCode.Z))
            // {
            //     if (Mc.MyPlayer.PlayerBoneManager.useAnim)
            //     {
            //         Mc.MyPlayer.PlayerBoneManager.useAnim = false;
            //     }
            //     else
            //     {
            //         Mc.MyPlayer.PlayerBoneManager.useAnim = true;
            //     }
            //     Debug.LogError($"useAnim :{Mc.MyPlayer.PlayerBoneManager.useAnim }");
            // }
#endif
        }

        /// <summary>
        /// 该系列函数在使用时要注意，Action必须不能捕获参数，否则会生成闭包，导致GC
        /// </summary>
        protected void ProfileAndExecute(EProfileFunc mark, Action act)
        {
            try
            {
                ProfilerApi.BeginSample(mark);
                act();
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("ProfileAndExecute {0} with exception {1}", mark, ex);
            }
            finally
            {
                ProfilerApi.EndSample(mark);
            }
        }

        /// <summary>
        /// 该系列函数在使用时要注意，Action必须不能捕获参数，否则会生成闭包，导致GC
        /// </summary>
        protected void ProfileAndExecute<T1>(EProfileFunc mark, Action<T1> act, T1 param1)
        {
            try
            {
                ProfilerApi.BeginSample(mark);
                act(param1);
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("ProfileAndExecute {0} with exception {1}", mark, ex);
            }
            finally
            {
                ProfilerApi.EndSample(mark);
            }
        }

        /// <summary>
        /// 该系列函数在使用时要注意，Action必须不能捕获参数，否则会生成闭包，导致GC
        /// </summary>
        protected void ProfileAndExecute<T1, T2>(EProfileFunc mark, Action<T1, T2> act, T1 param1, T2 param2)
        {
            try
            {
                ProfilerApi.BeginSample(mark);
                act(param1, param2);
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("ProfileAndExecute {0} with exception {1}", mark, ex);
            }
            finally
            {
                ProfilerApi.EndSample(mark);
            }
        }

        protected void ProfileAndExecute<T1, T2, T3>(EProfileFunc mark, Action<T1, T2, T3> act, T1 param1, T2 param2, T3 param3)
        {
            try
            {
                ProfilerApi.BeginSample(mark);
                act(param1, param2, param3);
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("ProfileAndExecute {0} with exception {1}", mark, ex);
            }
            finally
            {
                ProfilerApi.EndSample(mark);
            }
        }

        /// <summary>
        /// 该系列函数在使用时要注意，Action必须不能捕获参数，否则会生成闭包，导致GC
        /// </summary>
        protected R ProfileAndExecute<R, T1, T2, T3, T4>(EProfileFunc mark, Func<T1, T2, T3, T4, R> act, T1 param1, T2 param2, T3 param3, T4 param4)
        {
            try
            {
                ProfilerApi.BeginSample(mark);
                return act(param1, param2, param3, param4);
            }
            catch (Exception ex)
            {
                logger.ErrorFormat("ProfileAndExecute {0} with exception {1}", mark, ex);
                return default;
            }
            finally
            {
                ProfilerApi.EndSample(mark);
            }
        }

        /// <summary>
        /// to do 新增update的刷新周期
        /// </summary>
        protected override void OnLateUpdate()
        {
            ProfilerApi.BeginSample(EProfileFunc.Client_PreLateUpdate_LateUpdate);
            if (Mc.Camera is { AttachNewTransform: true } && Mc.Camera.NewTransform != null)
            {
                var transform = Mc.Camera.SceneCamera.transform;
                transform.position = Mc.Camera.NewTransform.position;
                transform.rotation = Mc.Camera.NewTransform.rotation;
            }

            base.OnLateUpdate();

            ProfileAndExecute(EProfileFunc.LateUpdate_AI, LateUpdate_AI);

            //WeaponAccessoryGos：人形怪在LateUpdateAi中会更新transform，人形怪武器配件的更新要用到武器最新的position，因此放在LateUpdateAi之后


            ProfileAndExecute(EProfileFunc.LateUpdate_SystemOnCmd, LateUpdate_SystemOnCmd, changeWorld);


            ProfileAndExecute(EProfileFunc.LateUpdate_System, LateUpdate_System, changeWorld);


            ProfileAndExecute(EProfileFunc.LateUpdate_MyPlayer, LateUpdate_MyPlayer, this);

            ProfileAndExecute(EProfileFunc.LateUpdate_Photo, LateUpdate_Photo);

            ProfileAndExecute(EProfileFunc.LateUpdate_Admin, LateUpdate_Admin);

            ProfileAndExecute(EProfileFunc.LateUpdate_NetworkMonitor, LateUpdate_NetWorkMonitor);
            ProfilerApi.EndSample(EProfileFunc.Client_PreLateUpdate_LateUpdate);
        }

        private void FireAfterAnimBeginSystem()
        {
            //依赖动画begin的得在这里跑，在update和lateupdate之间
            ProfileAndExecute(EProfileFunc.Client_PreLateUpdate_AfterAnimBegin, AfterAnimBegin_System, changeWorld);
        }

        public override void OnFrameEnd()
        {
            base.OnFrameEnd();
            McCommon.LocationBasedEvent.ClearEventQueue();
        }

        /// <summary>
        /// 无上限循环
        /// </summary>
        /// <param name="timeSinceLastUpdate">与上一帧的时间间隔</param>
        /// <param name="targetFps">目标Fps</param>
        public override void OnFpsUnlimitedUpdate(int timeSinceLastUpdate, int timeToNextUpdate, int targetFps)
        {
            horseRealTimeDelta += timeSinceLastUpdate;

            if (McCommon.System != null && McCommon.System.Context != null)
            {
                McCommon.System.Context.FrameNumber = Time.frameCount;
            }

            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_UpdateService, OnFpsUnlimitedUpdate_UpdateService, timeSinceLastUpdate);

            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_Timer, OnFpsUnlimitedUpdate_UpdateTimer);

            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_SnapshotReceiver, OnFpsUnlimitedUpdate_SnapshotReceiver);

            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_Input, OnFpsUnlimitedUpdate_Input, timeSinceLastUpdate);


            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_UI, OnFpsUnlimitedUpdate_UI, timeSinceLastUpdate);


            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_SnapshotEvent, OnFpsUnlimitedUpdate_SnapshotEvent);

            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_Construction_RenderUpdate, OnFpsUnlimitedUpdate_Construction_RenderUpdate);

            ProfileAndExecute(EProfileFunc.OnFixedUnlimitedUpdate_WorldResource_RenderUpdate, OnFixedUnlimitedUpdate_WorldResource_RenderUpdate);

            // ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_Misc, OnFpsUnlimitedUpdate_Misc);
            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_System_Update, OnFpsUnlimitedUpdate_System_Update, timeSinceLastUpdate, changeWorld);

            var frameTime = ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_OnFixedFps30Update, OnFpsUnlimitedUpdate_OnFixedFps30Update, timeSinceLastUpdate, timeToNextUpdate, targetFps, this);


            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate, OnFpsUnlimitedUpdate_CameraAndPlayerUpdate, timeSinceLastUpdate, changeWorld);

            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_PredictGoUpdate, OnFpsUnlimitedUpdate_PredictGoUpdate, timeSinceLastUpdate);

            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_NewbieLevelUpdate, OnFpsUnlimitedUpdate_NewbieLevelUpdate, timeSinceLastUpdate);

            #region 角色和相机渲染帧后处理这些，否则会有抖动的表现

            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_ParabolaAndCheckPredictError, OnFpsUnlimitedUpdate_ParabolaAndCheckPredictError);

            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_RenderUserCmdSystemUpdate, OnFpsUnlimitedUpdate_RenderUserCmdSystemUpdate, changeWorld);

            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_SystemOnRenderUpdate, OnFpsUnlimitedUpdate_SystemOnRenderUpdate, timeSinceLastUpdate, changeWorld);

            #endregion


            var do30Update = frameTime > 0;
            if (do30Update)
            {
                ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_SendCmd, OnFpsUnlimitedUpdate_SendCmd, this, frameTime);
            }

            // ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_LocalSnapshotEvent, OnFpsUnlimitedUpdate_LocalSnapshotEvent);

            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_ObjRender, OnFpsUnlimitedUpdate_ObjRender);

            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_EntityCareStates, OnFpsUnlimitedUpdate_EntityCareStates);

            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_IndicatorUpdate, OnFpsUnlimitedUpdate_IndicatorUpdate);

            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_SettingUpdate, OnFpsUnlimitedUpdate_SettingUpdate);

            //ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_LightTrigger, OnFpsUnlimitedUpdate_LightTrigger);

            ProfileAndExecute(EProfileFunc.OnFpsUnlimitedUpdate_MgrThermalUpdate, OnFpsUnlimitedUpdate_Thermal);

            try
            {
                if (Mc.MyPlayer != null && Mc.MyPlayer.MyEntityId > 0)
                {
                    if (Input.touchCount > 0 || Input.anyKeyDown)
                    {
                        lastTouchTime = Mc.Time.ServerWorldTime;
                        if (isNoTouch && Mc.MyPlayer.MyEntityServer != null)
                        {
                            //发送操作屏幕
                            Mc.MyPlayer.MyEntityServer.RemoteCallClientResumedControl(ERpcTarget.World);
                        }

                        isNoTouch = false;
                    }
                    else if (Mc.Time.ServerWorldTime - lastTouchTime > 60000)
                    {
                        //发送未操作屏幕
                        if (!isNoTouch && Mc.MyPlayer.MyEntityServer != null)
                        {
                            Mc.MyPlayer.MyEntityServer.RemoteCallClientStopControl(ERpcTarget.World);
                        }

                        isNoTouch = true;
                    }
                }
            }
            catch (Exception e)
            {
                logger.ErrorFormat("OnFpsUnlimitedUpdate last Others with exception {0}", e);
            }
        }

        /// <summary>
        /// 相机和角色刷新
        /// </summary>
        /// <param name="timeSinceLastUpdate"></param>
        private static void CameraAndPlayerUpdate(int timeSinceLastUpdate)
        {
            // Smooth Punch
            // Mc.Camera.Controller.UpdateSmoothPunch();

            // 一些清理操作
            // Mc.MyPlayer.BeforeUpdateMyPlayer(timeSinceLastUpdate);
            
            // 根据输入操作，直接更新本地角色旋转和坐标
            ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_UpdateMyPlayer);
            Mc.MyPlayer.UpdateMyPlayer(timeSinceLastUpdate);
            ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_UpdateMyPlayer);

            // 摄像机跟随
            ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_CameraRenderUpdate);
            Mc.Camera.RenderUpdate(Mc.UserCmd.NowCmd, timeSinceLastUpdate, Mc.MyPlayer.FpPlayerGo?.FpCameraLocator);
            ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_CameraRenderUpdate);

            // 相机debug，必须放到相机更新后去刷，不然不准
            Mc.MyPlayer.UpdateVehicleCameraDebug();
        }

        /// <summary>
        /// 从OnFixedFps30Update中分离出来的，只有OnFixedFps30Update执行逻辑，这里才会执行
        /// </summary>
        /// <param name="interval">帧间隔</param>
        private void SendCmd(int interval)
        {
            if (Mc.MyPlayer.preloadFinish)
            {
#if UNITY_EDITOR
                // 作弊模拟，覆盖cmd
                if (Mc.MyPlayer != null)
                    Mc.MyPlayer.cheatingTool.Cheating();
#endif
                ProfilerApi.BeginSample(EProfileFunc.SendCmd_SendUserCmd);
                Mc.UserCmd.SendUserCmd(Mc.Time.ServerWorldTime, Mc.Time.RenderWorldTime);
                ProfilerApi.EndSample(EProfileFunc.SendCmd_SendUserCmd);
            }

            //Debug.Log("30 update interval："+interval+", rednerTime:"+Mc.Time.RenderWorldTime+", cmd seq:"+sequ);
            ProfilerApi.BeginSample(EProfileFunc.SendCmd_SystemOnUpdate);
            Mc.System.OnUpdate(Mc.System.Context, interval, changeWorld);
            ProfilerApi.EndSample(EProfileFunc.SendCmd_SystemOnUpdate);

            //保存操作历史记录 移动到UserCmdSaveMoveSystem
            ProfilerApi.BeginSample(EProfileFunc.SendCmd_SaveUserCmdResultHistory);
            Mc.Predict?.SaveUserCmdResultHistory();
            ProfilerApi.EndSample(EProfileFunc.SendCmd_SaveUserCmdResultHistory);
            
            if (Mc.ObserverMode)
            {
                Mc.UserCmd.CreateReviewCmd();
            }
        }

        protected int TickCount = 0;
        protected int TickTimeTotal = 0;

        /// <summary>
        /// 计算平均帧 判断帧率是否低于固定帧
        /// </summary>
        protected int avgTimeSince = 0;
        /// <summary>
        ///
        /// </summary>
        /// <param name="timeSinceLastUpdate"></param>
        /// <param name="timeToNextUpdate"></param>
        /// <param name="targetFps"></param>
        /// <returns>帧间隔时间</returns>
        public virtual int OnFixedFps30Update(int timeSinceLastUpdate, int timeToNextUpdate, int targetFps)
        {
            if (!Mc.SnapshotReceiver.IsFullSnapshotReceived)
            {
                return -1; //全量snapshot没收到，不执行逻辑
            }
            timeSinceLastUpdate = Math.Clamp(timeSinceLastUpdate, 0, 100); //防止加载一帧卡顿导致平均帧率变化太大
            //计算帧时间
            ClientDynamicLogicFrameTime(timeSinceLastUpdate, Mc.UserCmd.NowCmd);
            TimeRemainder = Math.Clamp(TimeRemainder + timeSinceLastUpdate, 0, 200); //防止帧间隔太长;
            PhysicsTimeRemainder = Math.Clamp(PhysicsTimeRemainder + timeSinceLastUpdate, 0, 200); //防止帧间隔太长;
            int physicsUpdateCount = 0;
            BaseMountableGo mountableGo = null;
            if (Physics.simulationMode == SimulationMode.Script)
            {
                const int MAX_FRAME_COUNT = 2;
                int physicsFrameTime = Math.Clamp(FrameTime, 33, 50) ;//物理帧不能低于20帧50ms 不然会穿地形
                var tempPhysicsTimeRemainder = PhysicsTimeRemainder;
                allPhsicsTime = 0;
                for (var i = 0; i < MAX_FRAME_COUNT; i++)
                {
                    //防止物理无限补帧
                    if (tempPhysicsTimeRemainder < physicsFrameTime)
                    {
                        break;
                    }
                    allPhsicsTime += physicsFrameTime;
                    tempPhysicsTimeRemainder -= physicsFrameTime;
                }

                for (var i = 0; i < MAX_FRAME_COUNT; i++)
                {//防止物理无限补帧
                    if (PhysicsTimeRemainder < physicsFrameTime)
                    {
                        break;
                    }
                    ProfilerApi.BeginSample(EProfileFunc.OnFixedFps30Update_MountPredictMove);
                    Mc.MyPlayer.PredictMountableMove(FrameTime, horseRealTimeDelta, Mc.UserCmd.NowCmd, 1, i, allPhsicsTime, out mountableGo);
                    ProfilerApi.EndSample(EProfileFunc.OnFixedFps30Update_MountPredictMove);
                    physicsUpdateCount++;
                    ProfilerApi.BeginSample(EProfileFunc.Unity_PhysicsSimulator);
                    Physics.Simulate(physicsFrameTime * 0.001f);
                    ProfilerApi.EndSample(EProfileFunc.Unity_PhysicsSimulator);
                    PhysicsTimeRemainder -= physicsFrameTime;
                }
            }

            if (physicsUpdateCount > 0)
            {
                if (mountableGo != null)
                {
                    var parent = mountableGo.ParentGo;
                    var simulateTime = physicsUpdateCount * FrameTime;
                    Mc.UserCmd.NowCmd.CarDataPhyxSimulateTime = simulateTime;
                    parent.RecordPosAndRot(simulateTime, Mc.UserCmd.NowCmd.Sequence);
                }
                Mc.Msg.FireMsg(EventDefine.PhysicsSimulate);
            }
            //logger.ErrorFormat("OnFixedFps30Update TimeRemainder:{0},timeSinceLastUpdate:{1},FrameTime:{2},physicsUpdateCount:{3},frame:{4}", TimeRemainder, timeSinceLastUpdate, FrameTime, physicsUpdateCount, Time.frameCount);

            if (physicsUpdateCount == 0 || TimeRemainder < FrameTime)
            {
                return -1;
            }
            TimeRemainder -= FrameTime;

            ProfileAndExecute(EProfileFunc.OnFixedFps30Update_UI, OnFixedFps30Update_UI, FrameTime);


            ProfileAndExecute(EProfileFunc.OnFixedFps30Update_Construction_LogicUpdate, OnFixedFps30Update_Construction_LogicUpdate);

            ProfileAndExecute(EProfileFunc.OnFixedFps30Update_PredictMove, OnFixedFps30Update_PredictMove, FrameTime, horseRealTimeDelta, physicsUpdateCount);

            ProfileAndExecute(EProfileFunc.OnFixedFps30Update_UserCmdSystemGroup, OnFixedFps30Update_UserCmdSystemGroup);

            ProfileAndExecute(EProfileFunc.OnFixedFps30Update_PostUserCmdSystemGroup, OnFixedFps30Update_PostUserCmdSystemGroup, changeWorld);

            ProfileAndExecute(EProfileFunc.OnFixedFps30Update_UpdateAiAnimation30Fps, OnFixedFps30Update_UpdateAiAnimation30Fps);

            horseRealTimeDelta = 0;
            return FrameTime;
        }

        private void ClientDynamicLogicFrameTime(int timeSinceLastUpdate, UserCmd cmd)
        {
            var oldFrameTime = FrameTime;
            TickCount++;
            TickTimeTotal += timeSinceLastUpdate;
            bool calculateAvgTimeSince= TickTimeTotal > 1000;
            if (calculateAvgTimeSince)
            {
                avgTimeSince = TickTimeTotal / TickCount;
                TickCount = 0;
                TickTimeTotal = 0;
            }
            var localPlayer = Mc.MyPlayer.MyEntityLocal;
            var playerLogicParams = Mc.MyPlayer.playerLogicParams;
            if (playerLogicParams == null || localPlayer == null) return;
            var speed = new Vector3(localPlayer.SpeedX, localPlayer.SpeedY, localPlayer.SpeedZ).magnitude;
            bool cmdInoutMove = cmd.MoveBackward || cmd.MoveForward || cmd.MoveLeft || cmd.MoveRight || cmd.Fire1;
            bool isAds = localPlayer.AdsState == PlayerAdsStateEnum.AdsOn;
            var isCrouch = cmd.Crouch && !Mc.UserCmd.LastCmd.Crouch;
            if (localPlayer.IsHorseDriver)
            {
                if (Mc.MyPlayer.HorseGoPredict != null && Mc.MyPlayer.HorseGoPredict.LogicParams!=null && Mc.MyPlayer.HorseGoPredict.LogicParams.HorseEntity!=null)
                {
                    var horseEntity = Mc.MyPlayer.HorseGoPredict.LogicParams.HorseEntity;
                    speed = new Vector3(horseEntity.VelocityX, horseEntity.VelocityY, horseEntity.VelocityZ).magnitude;
                }
            }

            bool lowFrameTime = !isCrouch &&!isAds && !cmdInoutMove && speed.Equals(0)
                                && (!localPlayer.IsDriver || localPlayer.IsHorseDriver )&& playerLogicParams.PlatformId == 0;
            //判断是否需要降帧 
            if (localPlayer.IsDead)
            {
                if (oldFrameTime != McCommon.LOGIC_FRAME_5_TIME)
                {
                    FrameTime = McCommon.LOGIC_FRAME_5_TIME;
#if !PUBLISH

                    logger.InfoFormat("FrameTimeChange:{0}, {1}",oldFrameTime, McCommon.LOGIC_FRAME_5_TIME);
#endif
                }
            }
            else if (FrameTime <= McCommon.LOGIC_FRAME_20_TIME) 
            {
                HighFrameTimeChangeTime += timeSinceLastUpdate;
                if (lowFrameTime && HighFrameTimeChangeTime > 1000)
                {//1秒后才进入降低帧率防止开火时候逻辑帧跳变频繁 
                    FrameTime = McCommon.LOGIC_FRAME_10_TIME;
#if !PUBLISH
                    if (oldFrameTime != McCommon.LOGIC_FRAME_10_TIME)
                    {
                        logger.InfoFormat("FrameTimeChange:{0},{1}",oldFrameTime, McCommon.LOGIC_FRAME_10_TIME);
                    }
#endif
                }
                else
                {
                    if (calculateAvgTimeSince)
                    {
                        CheckDynamicFrameTime();
#if !PUBLISH
                        if (oldFrameTime != FrameTime)
                        {
                            logger.InfoFormat("FrameTimeChange:{0},{1}",oldFrameTime, FrameTime);
                        }
#endif
                    }
                }
            }
            else
            {
                if (!lowFrameTime)
                {
                    HighFrameTimeChangeTime = 0;
                    CheckDynamicFrameTime();
#if !PUBLISH
                    if (oldFrameTime != FrameTime)
                    {
                        logger.InfoFormat("FrameTimeChange:{0},{1}",oldFrameTime, FrameTime);
                    }
#endif
                }
            }
        }

        private void CheckDynamicFrameTime()
        {
            if (Mc.Instance.ClientLogicFrameTime > McCommon.LOGIC_FRAME_30_TIME)
            {
                FrameTime = Mc.Instance.ClientLogicFrameTime;
            }
            else
            {
                //判断合适的帧间隔，最低20帧 FrameTime = 50
                if (avgTimeSince > McCommon.LOGIC_FRAME_30_TIME)
                {
                    FrameTime = McCommon.LOGIC_FRAME_20_TIME;
                }
                else
                {
                    FrameTime = McCommon.LOGIC_FRAME_30_TIME;
                }
            }
        }

        protected readonly ClientChangeWorld changeWorld = new ClientChangeWorld();

        // private float render_last_render_time;
        // private long render_last_server_time;

        public override void OnFps30Update(int timeSinceLastUpdate, int timeToNextUpdate, int targetFps)
        {
            base.OnFps30Update(timeSinceLastUpdate, timeToNextUpdate, targetFps);
            //更新合批事件

            ProfileAndExecute(EProfileFunc.OnFps30Update_Msg, OnFps30Update_Msg);

            ProfileAndExecute(EProfileFunc.OnFps30Update_GatherItemPickable, OnFps30Update_GatherItemPickable, timeSinceLastUpdate);

            ProfileAndExecute(EProfileFunc.OnFps30Update_EntityInstiante, OnFps30Update_EntityInstiante, timeSinceLastUpdate);

            ProfileAndExecute(EProfileFunc.OnFps30Update_Monster, OnFps30Update_Monster);

            // 刷新资源信息
            ProfileAndExecute(EProfileFunc.OnFps30Update_Res, OnFps30Update_Res);

            // 语音大厅刷新
            ProfilerApi.BeginSample(EProfileFunc.OnFps30Update_Voice);
            if (Mc.Voice != null)
            {
                Mc.Voice.Update(timeSinceLastUpdate);
            }
            ProfilerApi.EndSample(EProfileFunc.OnFps30Update_Voice);
        }

        public override void OnFps10Update(int timeSinceLastUpdate, int timeToNextUpdate, int targetFps)
        {
            base.OnFps10Update(timeSinceLastUpdate, timeToNextUpdate, targetFps);


            ProfileAndExecute(EProfileFunc.OnFps10Update_AIUpdate, OnFps10Update_AIUpdate);


            ProfileAndExecute(EProfileFunc.OnFps10Update_MonumentAndCorpse, OnFps10Update_MonumentAndCorpse);


            ProfileAndExecute(EProfileFunc.OnFps10Update_Monster, OnFps10Update_Monster);

            ProfileAndExecute(EProfileFunc.OnFps10Update_IO, OnFps10Update_IO);

            ProfileAndExecute(EProfileFunc.OnFps10Update_SYS, OnFps10Update_SYS, timeSinceLastUpdate, changeWorld);

            ProfileAndExecute(EProfileFunc.OnFps10Update_UI, OnFps10Update_UI, timeSinceLastUpdate);

            ProfileAndExecute(EProfileFunc.OnFps10Update_PowerOptimization, OnFps10Update_PowerOptimization);

            ProfileAndExecute(EProfileFunc.OnFps10Update_Marker, OnFps10Update_Marker);

            ProfileAndExecute(EProfileFunc.OnFps10Update_WorldFlag, OnFps10Update_WorldFlag, timeSinceLastUpdate);

            ProfileAndExecute(EProfileFunc.OnFps10Update_Enhance, OnFps10Update_Enhance, timeSinceLastUpdate);


            ProfileAndExecute(EProfileFunc.OnFps10Update_DynLight, OnFps10Update_DynLight);

            ProfileAndExecute(EProfileFunc.OnFps10Update_Water, OnFps10Update_Water);
            ProfileAndExecute(EProfileFunc.OnFps10Update_UiHudElemPlayerStatus, OnFps10Update_HudMsg);


            ProfileAndExecute(EProfileFunc.OnFps10Update_VehicleTrigger, OnFps10Update_VehicleTrigger);


            ProfileAndExecute(EProfileFunc.OnFps10Update_Signifiance, OnFps10Update_Signifiance);

            ProfileAndExecute(EProfileFunc.OnFps10Update_Other, OnFps10Update_Other);
           
        }

        public override void OnFps2Update(int timeSinceLastUpdate, int timeToNextUpdate, int targetFps)
        {
            base.OnFps2Update(timeSinceLastUpdate, timeToNextUpdate, targetFps);

            ProfileAndExecute(EProfileFunc.OnFps2Update_Marker, OnFps2Update_Marker);

            ProfileAndExecute(EProfileFunc.OnFps2Update_UI, OnFps2Update_UI, timeSinceLastUpdate);

            ProfileAndExecute(EProfileFunc.OnFps2Update_Monster, OnFps2Update_Monster);

            ProfileAndExecute(EProfileFunc.OnFps2Update_PermCenter, OnFps2Update_PermCenter);

            ProfileAndExecute(EProfileFunc.OnFps2Update_QuickActBubble, OnFps2Update_QuickActBubble, timeSinceLastUpdate);
        }

        public override void OnFps1Update(int timeSinceLastUpdate, int timeToNextUpdate, int targetFps)
        {
            base.OnFps1Update(timeSinceLastUpdate, timeToNextUpdate, targetFps);
            if (null != Mc.LocalClient)
            {
                var rst = Mc.Time.PingRequest();
                if (rst)
                {
                    Mc.MyPlayer.MyEntityServer?.RemoteCallPing(ERpcTarget.Simulator);
                }
            }
            if (Mc.Report != null)
            {
                Mc.Report.UpdateOn1Fps(timeSinceLastUpdate);
            }

            //SDK Update
            Mc.SDK.OnFps1UpdateUI(timeSinceLastUpdate);

            ProfileAndExecute(EProfileFunc.OnFps1Update_Go, OnFps1Update_Go);

            ProfileAndExecute(EProfileFunc.OnFps1Update_Monster, OnFps1Update_Monster);

            ProfileAndExecute(EProfileFunc.OnFps1Update_Marker, OnFps1Update_Marker);

            ProfileAndExecute(EProfileFunc.OnFps1Update_Guide, OnFps1Update_Guide);

            // 资源卸载刷新，调整到OnFps30Update中
            // ProfileAndExecute(EProfileFunc.OnFps1Update_Res, OnFps1Update_Res);

            //内存监控  是否启动缓存池回收
            DeviceMemoryMonitor();

            ProfileAndExecute(EProfileFunc.OnFps1Update_UI, OnFps1Update_UI, timeSinceLastUpdate);

            ProfileAndExecute(EProfileFunc.OnFps1Update_PowerOptimization, OnFps1Update_PowerOptimization);
            ProfileAndExecute(EProfileFunc.OnFps1Update_PermCenter, OnFps1Update_PermCenter);

            ProfileAndExecute(EProfileFunc.OnFps1Update_TreeControl, OnFps1Update_TreeControl);

            ProfileAndExecute(EProfileFunc.OnFps1Update_MonumentPreventBuildings, OnFps1Update_MonumentPreventBuildings);

            ProfileAndExecute(EProfileFunc.OnFps1Update_ReportData, OnFps1Update_ReportData);
            ProfileAndExecute(EProfileFunc.OnFps1Update_WireControl, OnFps1Update_WireControl);
            ProfileAndExecute(EProfileFunc.OnFps1Update_ClientSetting, OnFps1Update_Setting);
            ProfileAndExecute(EProfileFunc.OnFps1Update_Thermal, OnFps1Update_Thermal);
            ProfileAndExecute(EProfileFunc.OnFps1Update_BattleReportCheck, OnFps1Update_BattleReportCheck);
        }

        public override void OnAfterUpdate(int timeSinceLastUpdate, int timeToNextUpdate, int targetFps)
        {

            ProfileAndExecute(EProfileFunc.OnAfterUpdate_AimAdsorb, OnAfterUpdate_AimAdsorb, timeSinceLastUpdate);

            ProfileAndExecute(EProfileFunc.OnAfterUpdate_System, OnAfterUpdate_System, timeSinceLastUpdate, changeWorld);
            ProfileAndExecute(EProfileFunc.OnAfterUpdate_WireControl, OnAfterUpdate_WireControl);
        }

        private void FixedUpdate()
        {
            ProfileAndExecute(EProfileFunc.Client_FixedUpdate, Client_FixedUpdate);
        }

        //场景材质获取代理
        public static int MaterialGetter(Vector3 pos)
        {
            if (TerrainMeta.SplatMap == null)
                return 0;
            return TerrainMeta.SplatMap.GetSplatMaxIndex(pos);
        }

        private static void UpdateWorldTime()
        {
            /*
            var serverLifeMSec = BaseBonusRocketGo.RocketCurrentMSec;
            var config = Mc.Tables.TbSurprisePlayConstConfig;
            var nightStartMSec = (config.RocketLaunchMinute - config.EnterNightIntervalMinute) * 60000;
            var nightEndMSec = (config.RocketLaunchMinute + config.LeaveNightIntervalMinute) * 60000;
            // Debug.LogError($"UpdateWorldTime:{(serverLifeMSec - config.RocketLaunchMinute*60000)/1000}");
            if (serverLifeMSec >= nightStartMSec && serverLifeMSec <= nightEndMSec)
            {
                AzureAPI.SetTime(22.6f);
                return;
            }
            */

            DayNightShiftEntity.Instance.Update();
            // 正常玩法需要更新时间

            if (Mc.Config.IsSyncTime && !PlayHelper.IsNewbie)
            {
                AzureAPI.SetTime(DayNightShiftEntity.Instance.HourFloat);
            }
        }

        // 更新CrashSight上报信息
        private static WritableString positionStr = new WritableString(40);
        public static void UpdateCrashSight()
        {
            if (Mc.MyPlayer == null || Mc.MyPlayer.MyEntityLocal == null)
            {
                return;
            }

            CrashSightWrapper.AddSceneData("CurrentWeaponId", Mc.MyPlayer.MyEntityLocal.CurrentWeaponId.ToString());
            positionStr.Clear();
            positionStr.AppendFormat("{0},{1},", Mc.MyPlayer.MyEntityLocal.PosX, Mc.MyPlayer.MyEntityLocal.PosY);
            positionStr.Append(Mc.MyPlayer.MyEntityLocal.PosZ);
            CrashSightWrapper.AddSceneData("Player Position", positionStr);

            CrashSightWrapper.AddSceneData("CurStepInfo", Mc.LoginStep.CurStepInfo);
        }

        private void DeviceMemoryMonitor()
        {
            // var systemMemory = Mc.Indicator.Memory.GetSystemMemory();
            // var mb = systemMemory / 1024 / 1024;
            // if (!GoPool.IsAutoCollect)
            // {
            //     GoPool.IsAutoCollect = true;
            //     Debug.Log($">>>>SystemMemory:{systemMemory} enable pool auto release");
            // }
            GoPool.Update();
        }

        public override Task ExitWorld()
        {
#if !PUBLISH
            isNoTouch = true;
#endif
            return base.ExitWorld();
        }

#if ENABLE_ONGUI || UNITY_EDITOR
        private void OnGUI()
        {
            Mc.Guide.OnGUI();
        }
#endif
        public void OnApplicationPause(bool pauseStatus)
        {
            //游戏切回前台判断
            var state = Platform.Instance.GetFoldScreenState();
            if (Mc.Ui.foldScreenState != state)
            {
                logger.InfoFormat("UnitySendMessage OnFoldChange  {0}", state);
                Mc.Ui.foldScreenState = state;
                Mc.Msg.FireMsg(EventDefine.UiFoldScreenChange, state);
            }

            Mc.PowerOptimization?.OnApplicationPause(pauseStatus);

            // 通知网络服务处理应用暂停
            Mc.LocalService?.OnApplicationPause(pauseStatus);

        Mc.LoginStep.StepLog(ZString.Format("[SocClientMainLoop] OnApplicationPause, status = {0}", pauseStatus));
            if (LogCollector.Instance != null)
                LogCollector.Instance.Flush();

            LogHelper.Flush();

            SocShaderVariantManager.SaveAndUploadCompiledVariants();
#if !PUBLISH
            AudioEventUsageTracker.SaveAndUploadUsedEvents();
#endif
#if UNITY_IOS
            // iOS高刷屏存在BUG，在50帧模式下切换后台/锁屏后再切回游戏，会导致屏幕刷新率异常处于48Hz/30Hz，进而导致游戏帧率异常
            // 可以通过设置帧率为60并延迟一帧设回原帧率来解决这个问题
            if (!pauseStatus)
            {
                Mc.SettingClient.DelayRefreshFrameRate();
            }
#endif
        }

        public void OnApplicationQuit()
        {
            Mc.LoginStep.StepLog("[SocClientMainLoop] OnApplicationQuit");

            //需要正常释放native内存，这部分在system中分配了，不然检查那边会出错
            if (Mc.System != null)
            {
                Mc.System.ClearSystems();
            }
            Mc.Go?.DelayedInst.Clear();
            if (LogCollector.Instance != null)
                LogCollector.Instance.Flush();

            LogHelper.Flush();
            
            SocShaderVariantManager.SaveAndUploadCompiledVariants();
#if !PUBLISH
            AudioEventUsageTracker.SaveAndUploadUsedEvents();
#endif
            Mc.Profiler.OnApplicationQuit();
        }

        public void OnLowMemory()
        {
            // Mc.LoginStep.StepLog("OnLowMemory");
            float curUsageMem = PerformanceData.GetMemoryUsage();
            if (curUsageMem < AppMaxUsageMemForUIPlay || AppMaxUsageMemForUIPlay <= 0)
            {
                AppMaxUsageMemForUIPlay = curUsageMem;
                logger.ReleaseCriticalFormat("[SocClientMainLoop] Set AppMaxUsageMemForUIPlay To {0} MB", AppMaxUsageMemForUIPlay);
            }
            GoPool.OnLowMemory();
            var now = Time.time;
            if (now - lastLowMemoryCleanTime > 60)
            {
                lastLowMemoryCleanTime = now;
                Mc.Ui.OnLowMemory();
                CommonConstructionUtils.OnLowMemory();
            }
        }

        #region UpdateFuncs
        private static Action LateUpdate_AI = () =>
        {
            Mc.Go?.LateUpdateAi();
        };
        private static Action<ClientChangeWorld> LateUpdate_SystemOnCmd = (ClientChangeWorld cw) =>
        {
            Mc.System?.OnCmdLateUpdate(Mc.System.Context, Mc.MyPlayer.MyEntityLocal, Mc.UserCmd.NowCmd, cw);
        };
        public static Action<ClientChangeWorld> LateUpdate_System = (ClientChangeWorld cw) =>
        {
            Mc.System?.OnLateUpdate(Mc.System.Context, (int)(Time.deltaTime * 1000), cw);
        };

        private static Action<ClientChangeWorld> AfterAnimBegin_System = (ClientChangeWorld cw) =>
        {
            Mc.System?.OnAfterAnimBegin(Mc.System.Context, (int)(Time.deltaTime * 1000), cw);
        };

        private static Action<SocClientMainLoop> LateUpdate_MyPlayer = (SocClientMainLoop mainloop) =>
        {
            Mc.MyPlayer?.LateUpdate(Mc.Camera.SceneCamera);
            //因为有些system也会刷新玩家位置，需要在system跑完以后在处理
            Mc.AccessoryManagers?.LateUpdate(Time.deltaTime);
            // 摄像机跟随
            //if(Mc.MyPlayer.MyEntityLocal.IsOnMount)
            Transform locator = Mc.MyPlayer.FpPlayerGo?.FpCameraLocator;

            // Timeline镜头归还
            if (Mc.Timeline != null)
            {
                if (Mc.Timeline.ControlCamera)
                {
                    locator = Mc.Timeline.CameraLocator;
                }
                else if (Mc.Timeline.UpdateEndingCamera(Time.deltaTime))
                {
                    locator = Mc.Timeline.CameraLocatorTransition;
                }
            }

            int phyTime = mainloop.allPhsicsTime == 0 ? mainloop.FrameTime : mainloop.allPhsicsTime;
            int interval = Mathf.Min(phyTime, (int)(Time.deltaTime * 1000));            
            Mc.Camera?.LateUpdate(interval, locator);

            if (PlayHelper.IsNewbie && Mc.MyPlayer?.TpPlayerGo != null)
            {
                Mc.GuideLine?.Update();
            }
        };

        private static Action LateUpdate_Photo = () =>
        {
            Mc.Photo?.LateUpdate();
        };

        private static Action LateUpdate_Admin = () =>
        {
            Mc.Admin?.LateUpdate();
        };

        private static Action LateUpdate_NetWorkMonitor = () =>
        {
            Mc.NetWorkMonitor.OnLateUpdate();
        };
        private static Action<int> OnFpsUnlimitedUpdate_UpdateService = (int deltaTime) =>
        {
            McCommonUnity.ThreadTask.Update();
            // 世界时间更新
            Mc.Time.Update(deltaTime);

            // 保底机制，如果一秒还没更新，就放开
            bool isBlockedOverLimit = TimeStampUtil.GetRawTimestampMsec() > lastNetWorkUpdateTs + 1000;
            if (isBlockedOverLimit)
            {
                logger.Info("OnFpsUnlimitedUpdate_UpdateService isBlockedOverLimit cancel Block");
                Mc.LocalService.BlockNetworkUpdate = false;
            }
            if (!Mc.LocalService.BlockNetworkUpdate)
            {
                Mc.LocalService.UpdateService(true);
                lastNetWorkUpdateTs = TimeStampUtil.GetRawTimestampMsec();
            }

            // 更新昼夜时间
            UpdateWorldTime();

            OceanManager.SetWaveTime(Mc.Time.RenderWorldTime);

            McCommonUnity.ThreadTask.Update();
            McCommonUnity.WebSocket.Update();
            
            PlayerFootContactCheck.BeginCheck(Mc.System.Context);
        };

        private static Action OnFpsUnlimitedUpdate_UpdateTimer = () =>
        {
            if (ProcessEntity.LastTimeStamp == 0)
            {
                Mc.TimerWheel.Update(1);
                Mc.TimerWheelPersistent.Update(1);
                ProcessEntity.LastTimeStamp = TimeStampUtil.GetRawTimestampMsec();
            }
            else
            {
                var now = TimeStampUtil.GetRawTimestampMsec();
                var delta = now - ProcessEntity.LastTimeStamp;
                if (delta < 0)
                {
                    logger.ErrorFormat("ServerWorldTime backwards! {0} {1}", now, ProcessEntity.LastTimeStamp);
                    Mc.TimerWheel.Update(10);
                    Mc.TimerWheelPersistent.Update(10);
                }
                else
                {
                    if (delta > 5000)
                    {
                        logger.InfoFormat("TimerWheel delta {0}", delta);
                    }
                    uint deltaTime = (uint)delta;
                    Mc.TimerWheel.Update(deltaTime);
                    Mc.TimerWheelPersistent.Update(deltaTime);
                    ProcessEntity.LastTimeStamp = now;
                }
            }
        };

        public static Action OnFpsUnlimitedUpdate_SnapshotReceiver = () =>
        {
            // 快照更新
            Mc.SnapshotReceiver.Update(Mc.Time.RenderWorldTime);
        };
        private static Action<int> OnFpsUnlimitedUpdate_Input = (int deltaTime) =>
        {
            // 提前于输入相关业务逻辑处理UI事件
            if (Stage.avaliable)
            {
                Stage.inst.HandleEventsOutside();
            }
#if !STANDALONE_REAL_PC
            // 更新触摸输入
            Mc.Control.TouchInput.Update(deltaTime / 1000f);
#endif            
            // 更新输入
            Mc.Control.CollectInput();

            if (Mc.ObserverMode && ObserverEntity.Instance != null)
            {
                long observerId = ObserverEntity.Instance.PossessionPlayerId;
                if (observerId > 0)
                {
                    var observerEntity = Mc.Entity.GetPlayerEntity(observerId);
                    if (observerEntity != null && Mc.Go.TyrGetGo(observerId, out ClientPlayerGo observerGo))
                    { //viewYaw 平滑插值
                        var smoothComp = observerGo.SmoothComp;
                        float frameInterval = Mathf.Max(smoothComp.FrameInterval, 33);
                        float factor = Mathf.Clamp01((Mc.Time.RenderWorldTime - smoothComp.LastServerTime) /
                                                     frameInterval);
                        Mc.Control.RotationControl.Yaw = SmoothAlgorithm.AngleLerp(smoothComp.LastViewYaw, smoothComp.ViewYaw, factor);
                        Mc.Control.RotationControl.Pitch =  Mathf.Lerp(smoothComp.LastViewPitch, smoothComp.ViewPitch, factor);
                    }
                }
                Mc.UserCmd.NowCmd.ContinuesCheck(Mc.UserCmd.LastCmd);
                Mc.MyPlayer.ReviewMove(Mc.UserCmd.NowCmd);
                ObserverEntity.Instance.SetTransform(Mc.MyPlayer.MyEntityLocal.PosX, Mc.MyPlayer.MyEntityLocal.PosY, Mc.MyPlayer.MyEntityLocal.PosZ, Mc.MyPlayer.MyEntityLocal.RotateY);
            }
        };
        private static Action<int> OnFpsUnlimitedUpdate_UI = (int deltaTime) =>
        {
            // 更新UI
            Mc.Ui.OnFpsUnlimitedUpdateUI(deltaTime);

            //更新头像下载器
            Mc.PlatformPlayerInfo.OnFpsUnlimitedUpdate(deltaTime);
        };
        private static Action OnFpsUnlimitedUpdate_SnapshotEvent = () =>
        {
            // 解析快照事件
            Mc.UserCmd.NowCmd.ContinuesCheck(Mc.UserCmd.LastCmd);
        };

        //private static Action OnFpsUnlimitedUpdate_LightTrigger = () =>
        //{
        //    Mc.LightTrigger.Update();
        //};
        public static Action OnFpsUnlimitedUpdate_Construction_RenderUpdate = () =>
        {
            // 建筑渲染
            Mc.Construction.RenderUpdate();
            CommonConstructionUtils.CheckDisposeCacheConfig(); // 轮询释放缓存
        };
        // private static Action OnFpsUnlimitedUpdate_Misc = () =>
        // {
        //     // 服务端callback
        //     ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_Misc_CollectionCallback);
        //     ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_Misc_CollectionCallback);
        //
        //     ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_Misc_RenderUpdateBeforeCmd);
        //     Mc.Camera.RenderUpdateBeforeCmd(Mc.UserCmd.NowCmd);
        //     ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_Misc_RenderUpdateBeforeCmd);
        //
        // };
        private static Action<int, ClientChangeWorld> OnFpsUnlimitedUpdate_System_Update = (int deltaTime,
            ClientChangeWorld cw
        ) =>
        {
            Mc.System.OnFirstGroupUpdate(Mc.System.Context, deltaTime, cw);
            if (Mc.MyPlayer.MyEntityLocal != null)
            {
                float dt = deltaTime * 0.001f;
                Mc.MyPlayer.smoothRenderTime += deltaTime;
                Mc.Control.RotationControl.CorrectPitchYaw(Mc.UserCmd.NowCmd, dt);

                if (!Mc.ObserverMode)
                {
                    //logger.ErrorFormat("smoothRenderTime set :{0} delta:{1},frame:{2}", Mc.MyPlayer.smoothRenderTime, deltaTime, Time.frameCount);
                    foreach (var system in Mc.System.PreUserCmdSystemGroup.Systems)
                    {
                        system.OnUpdate(Mc.System.Context, Mc.MyPlayer.MyEntityLocal, Mc.UserCmd.NowCmd, cw);
                    }
                }

                Mc.MyPlayer.CorrectCmdViewPitchYaw(Mc.UserCmd.NowCmd, dt);
                Mc.MyPlayer.CorrectCmdShootPitchYaw(Mc.UserCmd.NowCmd, dt);
                //当帧的输入朝向
                Mc.MyPlayer.MyEntityLocal.AimYaw = Mc.UserCmd.NowCmd.Yaw;
            }
        };
        private static Func<int, int, int, SocClientMainLoop, int> OnFpsUnlimitedUpdate_OnFixedFps30Update =
            (int deltaTime, int timeToNext, int fps, SocClientMainLoop mainLoop) =>
            {
                return mainLoop.OnFixedFps30Update(deltaTime, timeToNext, fps);
            };
        private static Action<int, ClientChangeWorld> OnFpsUnlimitedUpdate_CameraAndPlayerUpdate = (
            int deltaTime,
            ClientChangeWorld cw
        ) =>
        {
            try
            {
                //Mc.Control.RotationControl.UpdateAfterPreSmooth(Mc.UserCmd.NowCmd);
                ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_WeaponFxControllerUpdate);
                Mc.WeaponFxController?.Update(deltaTime * 0.001f);
                ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_WeaponFxControllerUpdate);
                ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_AccessoryManagersUpdate);
                Mc.AccessoryManagers?.Update(deltaTime * 0.001f);
                ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_AccessoryManagersUpdate);
                //相机之前的system
                ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_OnBeforeCameraRenderUpdate);
                Mc.System.OnBeforeCameraRenderUpdate(Mc.System.Context, deltaTime, cw);
                ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_OnBeforeCameraRenderUpdate);
                
            }
            catch (Exception e)
            {
                logger.ErrorFormat("OnFpsUnlimitedUpdate_CameraAndPlayerUpdate:{0}", e.ToString());
            }

            CameraAndPlayerUpdate(deltaTime);

            ProfilerApi.BeginSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_UpdateLight);
            Mc.Light?.Update(deltaTime);
            ProfilerApi.EndSample(EProfileFunc.OnFpsUnlimitedUpdate_CameraAndPlayerUpdate_UpdateLight);
        };

        private static Action<int> OnFpsUnlimitedUpdate_PredictGoUpdate = (
            int deltaTime
        ) =>
        {
            Mc.MyPlayer.UpdatePredictGo(deltaTime);
        };

        private static Action<int> OnFpsUnlimitedUpdate_NewbieLevelUpdate = (
            int deltaTime
        ) =>
        {
            Mc.Timeline?.Update(deltaTime);
            Mc.NewbieLevel?.Update(deltaTime);
        };

        private static Action OnFpsUnlimitedUpdate_ParabolaAndCheckPredictError = () =>
        {
            // 判断预测失败物体删除
            Mc.Go.CheckPredictEntityError(Mc.SnapshotReceiver.NowIncremental?.CmdSequenceAck ?? -1);
        };
        private static Action<ClientChangeWorld> OnFpsUnlimitedUpdate_RenderUserCmdSystemUpdate = (
            ClientChangeWorld cw
        ) =>
        {
            if (!Mc.ObserverMode && Mc.MyPlayer.MyEntityLocal != null)
            {
                foreach (var system in Mc.System.RenderUserCmdSystemGroup.Systems)
                {
                    system.OnUpdate(Mc.System.Context, Mc.MyPlayer.MyEntityLocal, Mc.UserCmd.NowCmd, cw);
                }
            }
        };
        private static Action<int, ClientChangeWorld> OnFpsUnlimitedUpdate_SystemOnRenderUpdate = (
            int deltaTime,
            ClientChangeWorld cw
        ) =>
        {
            // RenderSystemGroup
            Mc.System.OnRenderUpdate(Mc.System.Context, deltaTime, cw);
        };

        private static Action<SocClientMainLoop, int> OnFpsUnlimitedUpdate_SendCmd = (
            SocClientMainLoop loop,
            int ft
        ) =>
        {
            loop.SendCmd(ft);
        };
        private static Action OnFpsUnlimitedUpdate_LocalSnapshotEvent = () =>
        {
        };
        public static Action OnFpsUnlimitedUpdate_ObjRender = () =>
        {
            // Instance渲染更新
            Mc.ObjectRenderer.Update();
        };
        private static Action OnFpsUnlimitedUpdate_EntityCareStates = () =>
        {
            Mc.EntityCareStates?.UnlimitUpdate();
        };
        private static Action OnFpsUnlimitedUpdate_IndicatorUpdate = () =>
        {
            Mc.Indicator.Update();
        };
        private static Action OnFpsUnlimitedUpdate_SettingUpdate = () =>
        {
            Mc.SettingClient.Update();
        };
        protected static Action<int> OnFixedFps30Update_UI = (int deltaTime) =>
        {
            //更新UI
            Mc.Ui.OnFps30UpdateUI(deltaTime);
        };
        public static Action OnFixedFps30Update_Construction_LogicUpdate = () =>
        {
            //建筑logic
            Mc.Construction.LogicUpdate();
        };

        protected static Action OnFixedUnlimitedUpdate_WorldResource_RenderUpdate = () =>
        {
            //道具丢弃效果logic
            Mc.WorldResource?.RenderUpdate();
        };
        protected static Action<int, int, int> OnFixedFps30Update_PredictMove = (int ft, int timeSinceLastUpdate, int physicsUpdateCount) =>
        {
            if (!Mc.ObserverMode)
            {
                //logger.Info($"PrePredictMove, new:{ Mc.SnapshotReceiver.NewReceivedSnapshotSequence},now:{Mc.SnapshotReceiver.NowSnapshotSequence}");
                Mc.UserCmd.PrePredictMove(ft, Mc.SnapshotReceiver.NowSnapshotSequence);
                // Debug.LogFormat("logic render delta:{0},logic server delta :{1},timeSinceLastUpdate:{2}",(r_dt * 1000),l_dt,timeSinceLastUpdate);
                Mc.UserCmd.NowCmd.ContinuesCheck(Mc.UserCmd.LastCmd);

                // Mc.UserCmd.NowCmd.LastWorldTime = Mc.Time.ServerWorldTime - timeSinceLastUpdate;
                // Mc.UserCmd.NowCmd.SendWorldTime = Mc.Time.ServerWorldTime;
                Mc.UserCmd.NowCmd.SendRenderTime = Mc.Time.RenderWorldTime;
                Mc.UserCmd.NowCmd.ClientSmoothRenderTime = Mc.MyPlayer.smoothRenderTime;
                // Mc.UserCmd.NowCmd.NextDesiredWorldTime = Mc.UserCmd.NowCmd.SendWorldTime + timeToNextUpdate;
                //本地预判移动
                Mc.MyPlayer.PreCmdChange(Mc.UserCmd.NowCmd);

                Mc.MyPlayer.PredictMove(Mc.UserCmd.NowCmd);

                Mc.MyPlayer.PredictHorse(Mc.UserCmd.NowCmd);

                Mc.MyPlayer.PredictParachute(Mc.UserCmd.NowCmd);

                // Mc.MyPlayer.PredictMountableMove(ft, timeSinceLastUpdate, Mc.UserCmd.NowCmd, physicsUpdateCount);

                //应用状态机修改后的cmd数据
                Mc.MyPlayer.AfterCmdChange(Mc.UserCmd.NowCmd);


                //Mc.AimSnap.Update(timeSinceLastUpdate * 0.001f);
                //Mc.AimRange.Update(timeSinceLastUpdate * 0.001f);//下面相机操作会用到cmd.pitch 和yaw

                //相机操作
                //Mc.Camera.LogicUpdate(Mc.UserCmd.NowCmd);
            }
        };
        protected static Action OnFixedFps30Update_UserCmdSystemGroup = () =>
        {
            if (Mc.MyPlayer.MyEntityLocal != null)
            {
                foreach (var system in Mc.System.UserCmdSystemGroup.Systems)
                {
                    system.OnUpdate(Mc.System.Context, Mc.MyPlayer.MyEntityLocal, Mc.UserCmd.NowCmd, null);
                }
            }
        };
        protected static Action<ClientChangeWorld> OnFixedFps30Update_PostUserCmdSystemGroup = (
            ClientChangeWorld cw
        ) =>
        {
            if (Mc.MyPlayer.MyEntityLocal != null)
            {
                foreach (var system in Mc.System.PostUserCmdSystemGroup.Systems)
                {
                    system.OnUpdate(Mc.System.Context, Mc.MyPlayer.MyEntityLocal, Mc.UserCmd.NowCmd, cw);
                }
            }
        };
        protected static Action OnFixedFps30Update_UpdateAiAnimation30Fps = () =>
        {
            Mc.Go.UpdateAiAnimation30Fps(Mc.Time.ServerWorldTime);
        };
        public static Action OnFps30Update_Msg = () =>
        {
            Mc.Msg.DoUpdate();
        };

        protected static Action<int> OnFps30Update_GatherItemPickable = (int deltaTime) =>
        {
            Mc.GatherItemPickable?.Fps30Update(deltaTime);
        };
        public static Action<int> OnFps30Update_EntityInstiante = (int deltaTime) =>
        {
            Mc.Entity.OnFps30Update(deltaTime);
            Mc.Go.UpdateOn30Fps(deltaTime);
        };
        protected static Action OnFps30Update_Monster = () =>
        {
            // Mc.Monster.Fps30Update();
        };
        private static Action OnFps10Update_AIUpdate = () =>
        {
            //更新Ai动画
            Mc.Go.UpdateAiAnimation10Fps(Mc.Time.ServerWorldTime);
        };
        private static Action OnFps10Update_MonumentAndCorpse = () =>
        {
            Mc.Go.UpdateOn10Fps(Mc.Time.ServerWorldTime);
        };
        private static Action OnFps10Update_Monster = () =>
        {
            // Mc.Monster.Fps10Update();
        };
        private static Action OnFps10Update_IO = () =>
        {
            // Mc.ioEntityMgr.Update();
        };
        public static Action<int, ClientChangeWorld> OnFps10Update_SYS = (
            int deltaTime,
            ClientChangeWorld cw
        ) =>
        {
            Mc.System.OnFps10Update(Mc.System.Context, deltaTime, cw);
        };
        private static Action<int> OnFps10Update_WorldFlag = (int deltaTime) =>
        {
            Mc.WorldFlag?.OnFps10Update(deltaTime);
        };
        private static Action<int> OnFps10Update_UI = (int deltaTime) =>
        {
            //更新UI
            Mc.Ui.OnFps10UpdateUI(deltaTime);
        };


        private static Action OnFps1Update_BattleReportCheck = () =>
        {
            Mc.BattleReport?.OnFps1Update();

        };

        private static Action OnFps10Update_PowerOptimization = () =>
        {
            Mc.PowerOptimization.OnFps10Update();

        };
        private static Action OnFps10Update_Marker = () =>
        {
            Mc.Marker?.Fps10Update();
        };
        private static Action<int> OnFps10Update_Enhance = (int deltaTime) =>
        {
            Mc.EnhancedDisplayTip?.OnFps10Update(deltaTime);
        };
        private static Action OnFps10Update_DynLight = () =>
        {
            //Mc.DynamicLight.Update();
        };
        private static Action OnFps1Update_Guide = () =>
        {
            Mc.Guide.Update();
        };
        private static Action OnFps10Update_VehicleTrigger = () =>
        {
            Mc.Vehicle?.UpdateVehicleTrigger();
        };
        private static Action OnFps10Update_Signifiance = () =>
        {
            Mc.SignifianceRes?.OnFps10Update();
            Mc.SignifianceDbgDraw?.OnFps10Update();
        };
        private static Action OnFps10Update_Water = () =>
        {
            Mc.Water.UpdateWaterStateByRaycast();
        };
        private static Action OnFps10Update_HudMsg = () =>
        {
            Mc.HudMsg?.Update();
        };
        private static Action OnFps10Update_Other = () =>
        {
            AdaptivePerformanceManager.Instance.Update();
        };
        private static Action OnFps2Update_Marker = () =>
        {
            //Mc.Marker.Fps2Update();
        };
        private static Action<int> OnFps2Update_UI = (int deltaTime) =>
        {
            //更新UI
            Mc.Ui.OnFps2UpdateUI(deltaTime);
        };
        private static Action OnFps2Update_Monster = () =>
        {
            // Mc.Monster.Fps2Update();
        };
        private static Action OnFps2Update_PermCenter = () =>
        {
            Mc.PermCenter?.OnFps2Update();
        };
        private static Action<int> OnFps2Update_QuickActBubble = (int dt) =>
        {
            Mc.QuickActBubble?.OnFps2Update(dt);
        };
        public static Action OnFps1Update_Go = () =>
        {
            Mc.Go.UpdateOn1Fps(McCommon.Time.ServerWorldTime);
        };
        private static Action OnFps1Update_Monster = () =>
        {
            // Mc.Monster.Fps1Update();
        };
        private static Action OnFps1Update_Marker = () =>
        {
            //Mc.Marker.Fps1Update();
        };

        public static Action OnFps30Update_Res = () =>
        {
            McCommonUnity.Res.Update();
            // Mc.PlayerRes.Update();
        };
        private static Action<int> OnFps1Update_UI = (int deltaTime) =>
        {
            var t = CpuTick.CurTick();
            //更新UI
            Mc.Ui.OnFps1UpdateUI(deltaTime);
            Mc.Ui.AddUIFps1CustomProfiler("Fps1_UI", t);
        };
        private static Action OnFps1Update_PowerOptimization = () =>
        {
            //更新节能模式
            Mc.PowerOptimization.OnFps1Update();
        };
        private static Action OnFps1Update_PermCenter = () =>
        {
            Mc.PermCenter?.OnFps1Update();
        };
        private static Action OnFps1Update_TreeControl = () =>
        {
            Mc.TreeControl?.OnFps1Update();
        };
        private static Action OnFps1Update_MonumentPreventBuildings = () =>
        {
            Mc.MonumentPreventBuildings?.OnFps1Update();
        };
        private static Action OnFps1Update_ReportData = () =>
        {
            //上报性能数据
            Mc.Indicator?.ReportData();

#if ENABLE_CRASHSIGHT
            // 上报CrashSight信息
            UpdateCrashSight();
#endif
        };

        private static Action OnFps1Update_Setting = () =>
                                                     {
                                                         Mc.SettingClient?.OnFps1Update();
                                                     };
        private static Action<int> OnAfterUpdate_AimAdsorb = (int deltaTime) =>
        {
            float delta = deltaTime * 0.001f;
            // Mc.AimSnap.Update(delta);
            //Mc.AimRange.Update(delta);
        };
        public static Action<int, ClientChangeWorld> OnAfterUpdate_System = (
            int deltaTime,
            ClientChangeWorld cw
        ) =>
        {
            Mc.System.OnAfterLoopUpdate(Mc.System.Context, deltaTime, cw);
        };

        public static Action Client_FixedUpdate = () =>
        {
            /* 在物理帧执行的系统*/
            Mc.System.OnFixedUpdate(Mc.System.Context, (int)(Time.fixedDeltaTime * 1000), null);
        };
        private static Action OnFps1Update_WireControl = () =>
        {
            Mc.WireControl?.OnFps1Update();
        };
        private static Action OnAfterUpdate_WireControl = () =>
        {
            Mc.WireControl?.OnAfterUpdate();
        };
        private static Action OnFps1Update_Thermal = () =>
                                                                     {
                                                                         Mc.Thermal.OnFps1Update();
                                                                     };
        private static Action OnFpsUnlimitedUpdate_Thermal = () =>
                                                     {
                                                         Mc.Thermal.OnFpsUnlimitedUpdate();
                                                     };
        #endregion
    }
}