﻿using Soc.Common.Unity.PlatformResOpt;
using UnityEngine;

namespace PackTool
{
    [CreateAssetMenu(fileName = "PlatformAlwaysPackRule", menuName = "平台资产配置/规则/永远会打包的资产规则", order = 0)]
    public class PlatformAlwaysPackRule : PlatformPackPrefabSearchRule
    {
        public override bool ShouldExecute(PackInputParams packInputParams)
        {
            if (!base.ShouldExecute(packInputParams))
            {
                return false;
            }
            var buildAssetPath = packInputParams.processSourcePath;
            GameObject targetGo = null;
            if (packInputParams.assetTarget is GameObject)
            {
                targetGo = packInputParams.assetTarget as GameObject;
            }
            else if (packInputParams.assetTarget is Component comp)
            {
                targetGo = comp.gameObject;
            }
            
            if (targetGo == null)
            {
                ErrorFormat(packInputParams.context,"<PackPrefabRule> 资源不是gameobject:{0}", buildAssetPath);
                return false;
            }
            
            return true;
        }
        
        public override int ExecuteMainRule(PackInputParams packInputParams)
        {
            return (int)EPlatformErrorCode.Suc;
        }

        public override int TryApplyCache(PackInputParams packInputParams)
        {
            if (!ShouldExecute(packInputParams))
            {
                return (int)EPlatformErrorCode.ContinueCode;
            }
            
            if (packInputParams.context.processStage != EPlatformProcessStage.CI_Apply)
            {
                //永远重新打包
                return (int)EPlatformErrorCode.BreakCode;
            }

            return base.TryApplyCache(packInputParams);
        }
    }
}