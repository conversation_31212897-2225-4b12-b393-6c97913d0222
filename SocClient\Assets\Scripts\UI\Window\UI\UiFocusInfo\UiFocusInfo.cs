﻿using Construction.BuildMode;
using Construction.Util;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data.IOInteractiveEnum;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Unity.Go;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Systems;
using WizardGames.Soc.SocClient.Go;
using WizardGames.Soc.Common.Profile;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.Common.UtcTime;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.SocClient.Construction.BuildMode;

namespace WizardGames.Soc.SocClient.Ui
{
    /// <summary>
    /// 血条信息展示界面
    /// </summary>
    public class UiFocusInfo : WindowComBase,IUiFps30Update
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(UiFocusInfo));

        #region 变量引用

        /// <summary>
        /// 上一次更新，瞄准的目标的记录（屏幕中心 ui）
        /// </summary>
        private static long preEntityFocusId = 0;

        public static bool HasFocusInfo => preEntityFocusId > 0;

        /// <summary>
        /// 上一次更新，瞄准的目标的记录（hp flag ui）
        /// </summary>
        private long preHpFlagEntityId;

        /// <summary>
        /// 上一次更新，瞄准的目标的记录（label flag ui）
        /// </summary>
        private long preLabelFlagEntityId;

        /// <summary>
        /// 是否已经完成了整个UI的初始化
        /// </summary>
        private bool isInitialized = false;

        /// <summary>
        /// 视觉焦点信息 ui样式
        /// </summary>
        private Dictionary<int, UiFocusInfoBase> uiFocusInfoDic;

        /// <summary>
        /// 当前显示的ui样式列表
        /// </summary>
        private List<UiFocusInfoBase> curUiFocusInfoList;

        /// <summary>
        /// 火车显示的瞄准ui类型
        /// </summary>
        private readonly int[] mTrainFocusTypes = { 3 };

        private readonly int[] digEntityFocusTypes = { 1 };

        /// <summary>
        /// 帧率限制，降低帧率的倍数
        /// </summary>
        private readonly int frameLimit = 3;

        /// <summary>
        /// 帧率计数
        /// </summary>
        private int frameCount = 0;

        public static bool ForceUpdate = false;
        

        #endregion


        protected override void OnInit()
        {
            base.OnInit();

            uiFocusInfoDic = new Dictionary<int, UiFocusInfoBase>();
            curUiFocusInfoList = new List<UiFocusInfoBase>();

            var node = ContentPane.GetChild("root").asCom.GetChild("content").asCom;

            var uiLabel = new UiFocusInfoLabel();
            uiLabel.Init(node.GetChild("label_style").asLoader);

            var uiBar = new UiFocusInfoBar();
            uiBar.Init(node.GetChild("bar_style").asLoader);

            var uiLabelBar = new UiFocusInfoLabelBar();
            uiLabelBar.Init(node.GetChild("label_bar_style").asLoader);

            var uiIcon = new UiFocusInfoIcon();
            uiIcon.Init(node.GetChild("icon_style").asLoader);

            var uiPlantWater = new UiFocusInfoPlantWater();
            uiPlantWater.Init(node.GetChild("plantWaterValue").asLoader);
            
            var uiContainerInfo = new UiFocusInfoContainer();
            uiContainerInfo.Init(node.GetChild("containerInfo").asLoader);

            // 建筑恢复倒计时信息
            var uiPartRecover = new UiFocusInfoPartRecover();
            uiPartRecover.Init(node.GetChild("labelPartRecoverInfo").asLoader);

            // 输入功率/自身耗电功率
            var uiConsumption = new UiFocusInfoPartConsumption();
            uiConsumption.Init(node.GetChild("consumptionStyle").asLoader);

            // 巡查工具信息
            var uiObserverMode = new UiFocusInfoObserverMode();
            uiObserverMode.Init(node.GetChild("observerModeInfo").asLoader);
            
            // 新版hud血条
            var uiComHudBloodBar = new UiComHudBloodBar();
            uiComHudBloodBar.Init(node.GetChild("blood_bar").asLoader);
            
            // 新版hud名字信息
            var uiComHudNameInfo = new UiComNameInfo();
            uiComHudNameInfo.Init(node.GetChild("name_info").asLoader);
            
            uiFocusInfoDic.Add(1, uiLabel);
            uiFocusInfoDic.Add(2, uiBar);
            uiFocusInfoDic.Add(3, uiLabelBar);
            uiFocusInfoDic.Add(4, uiIcon);
            uiFocusInfoDic.Add(5, uiPlantWater);
            uiFocusInfoDic.Add(6, uiPartRecover);
            uiFocusInfoDic.Add(7, uiConsumption);
            uiFocusInfoDic.Add(8, uiContainerInfo);
            uiFocusInfoDic.Add(9, uiObserverMode);
            uiFocusInfoDic.Add(10, uiComHudBloodBar);
            uiFocusInfoDic.Add(11, uiComHudNameInfo);

            isInitialized = true;
        }


        public override void OnDestroy()
        {
            foreach (var (_, uiFocusInfo) in uiFocusInfoDic)
            {
                uiFocusInfo.Destroy();
            }

            base.OnDestroy();
        }


        protected override void OnEnable()
        {
            base.OnEnable();
            Mc.Raycast?.RegisterRaycastEntityTypeAction(OnUpdate);
        }


        protected override void OnDisable()
        {
            base.OnDisable();
            Mc.Raycast?.UnregisterRaycastEntityTypeAction(OnUpdate);

            preEntityFocusId = 0;
            preHpFlagEntityId = 0;
            preLabelFlagEntityId = 0;

            Mc.Msg.FireMsg(EventDefine.CloseFocusInfoNotice);
        }


        private void OnUpdate(List<HitObjData> hitObjDataList, HitObjData obstacle)
        {
            if (!isInitialized)
            {
                return;
            }

            frameCount++;
            if (frameCount % frameLimit != 0)
            {
                // 降低更新频率
                return;
            }

            frameCount = 0;

            HitObjData hitObjData = Mc.FocusInfo.GetBaseEntity(hitObjDataList, obstacle);
            IEntity focusEntity = null;
            if (hitObjData != null)
            {
                focusEntity = hitObjData.entity;
            }

            if (!Mc.ObserverMode)
            {
                ShowFocusInfo(focusEntity, hitObjData);
            }
            else
            {
                ObserverShowFocusInfo(focusEntity, hitObjData);
            }
        }

        private void ShowFocusInfo(IEntity focusEntity, HitObjData hitObjData)
        {
            IEntity hpFlagEntity = null;
            IEntity labelFlagEntity = null;
            IEntityGo entityGo = null;
            Mc.MyPlayer.MyEntityLocal.CurrentFocusedEntityId = 0;

            if (focusEntity != null)
            {
                hpFlagEntity = focusEntity;
                labelFlagEntity = focusEntity;
                
                // 瞄准血条显示
                Mc.Msg.FireMsgAtOnce(EventDefine.AimShowHp, -1.0f, focusEntity);
                Mc.MyPlayer.MyEntityLocal.CurrentFocusedEntityId = focusEntity.EntityId;
                Mc.Go.Gos.TryGetValue(focusEntity.EntityId, out entityGo);

                // 是否需要显示跟随血条
                var isShowHpFlag = false;
                var entityTypeStr = EntityTypeId.EntityType2Name[focusEntity.EntityType];
                if (Mc.Entity.EntityTypeStrToTableIdDic.TryGetValue(entityTypeStr, out var gunId))
                {
                    var entityData = Mc.Tables.TbGlobalEntity.GetOrDefault(gunId);
                    if (entityData != null && entityData.HitShowHP)
                    {
                        isShowHpFlag = true;
                    }
                }

                // 是否需要显示跟随文本
                var isShowLabelFlag = false;
                var ioEntity = focusEntity as IOEntity;
                if (ioEntity != null)
                {
                    var ioConfig = Mc.Tables.TbIOInteractive.GetOrDefault(ioEntity.TemplateId);
                    if (ioConfig.IOType == IOType.SimpleSwitch)
                    {
                        // 开关
                        isShowLabelFlag = true;
                    }
                }

                if (!isShowLabelFlag)
                {
                    var elevatorEntity = focusEntity as ElevatorEntity;
                    if (elevatorEntity != null)
                    {
                        var isIsMainElevator = entityGo != null && entityGo is ElevatorGo elevatorGo &&
                                               elevatorGo.IsMainElevator();
                        var isElevatorInsideBtn = elevatorEntity.ComponentType ==
                                                  (int)BaseElevatorGo.EElevatorTypeEnum.InsideBtn;
                        var isElevatorOutsideBtn = elevatorEntity.ComponentType ==
                                                   (int)BaseElevatorGo.EElevatorTypeEnum.OutsideBtn;
                        if (!isIsMainElevator && (isElevatorInsideBtn || isElevatorOutsideBtn))
                        {
                            // 电梯按钮
                            isShowLabelFlag = true;
                        }
                    }
                }

                float distance = hitObjData.distance;
                // #51974  【建造系统】回退血条显示，当前版本允许显示2个血条
                /*if (entityGo != null)
                {
                    distance = Vector3.Distance(Mc.MyPlayer.GetMyCamera.transform.position, entityGo.MainTransform.position);
                }*/

                if (isShowHpFlag)
                {
                    // 需要显示跟随的血条，距离远的时候不显示
                    if (distance > Mc.Tables.TbGlobalConfig.AimHPBarModSwitchDistance)
                    {
                        focusEntity = null;
                    }

                    if (distance > Mc.Tables.TbGlobalConfig.ShowNameDistance)
                    {
                        hpFlagEntity = null;
                    }

                    // 保证只显示一个
                    if (focusEntity != null)
                    {
                        hpFlagEntity = null;
                    }
                }
                else
                {
                    // 不需要显示跟随的血条，只需要判断 全局表ShowNameDistance的距离
                    hpFlagEntity = null;

                    if (distance > Mc.Tables.TbGlobalConfig.ShowNameDistance)
                    {
                        focusEntity = null;
                    }
                }

                if (!isShowLabelFlag || distance > Mc.Tables.TbGlobalConfig.ShowNameDistance)
                {
                    labelFlagEntity = null;
                }
            }

            ProfilerApi.BeginSample(EProfileFunc.SendCmd_UiHudElemFocusInfoUpdate_HandleFocusEntity);
            GameObject hitGo = hitObjData != null ? hitObjData.hitGo : null;
            HandleFocusEntity(focusEntity, hitGo);
            ProfilerApi.EndSample(EProfileFunc.SendCmd_UiHudElemFocusInfoUpdate_HandleFocusEntity);
            ProfilerApi.BeginSample(EProfileFunc.SendCmd_UiHudElemFocusInfoUpdate_HandleHpFlagEntity);
            HandleHpFlagEntity(hpFlagEntity);
            ProfilerApi.EndSample(EProfileFunc.SendCmd_UiHudElemFocusInfoUpdate_HandleHpFlagEntity);
            ProfilerApi.BeginSample(EProfileFunc.SendCmd_UiHudElemFocusInfoUpdate_HandleLabelFlagEntity);
            HandleLabelFlagEntity(labelFlagEntity);
            ProfilerApi.EndSample(EProfileFunc.SendCmd_UiHudElemFocusInfoUpdate_HandleLabelFlagEntity);
        }

        private void ObserverShowFocusInfo(IEntity focusEntity, HitObjData hitObjData)
        {
            IEntity hpFlagEntity = null;
            IEntity labelFlagEntity = null;
            IEntityGo entityGo = null;
            Mc.MyPlayer.MyEntityLocal.CurrentFocusedEntityId = 0;

            if (focusEntity != null)
            {
                // bool isShowHpFlag =  hitObjData.distance < 100;
                // if (isShowHpFlag)
                // {
                //     hpFlagEntity = focusEntity;
                // }
                // 瞄准血条显示
                Mc.Msg.FireMsgAtOnce(EventDefine.AimShowHp, -1.0f, focusEntity);
                Mc.MyPlayer.MyEntityLocal.CurrentFocusedEntityId = focusEntity.EntityId;
                Mc.Go.Gos.TryGetValue(focusEntity.EntityId, out entityGo);
            }

            ProfilerApi.BeginSample(EProfileFunc.SendCmd_UiHudElemFocusInfoUpdate_HandleFocusEntity);
            GameObject hitGo = hitObjData != null ? hitObjData.hitGo : null;
            ObserverHandleFocusEntity(focusEntity, hitGo);
            // HandleHpFlagEntity(hpFlagEntity);
            ProfilerApi.EndSample(EProfileFunc.SendCmd_UiHudElemFocusInfoUpdate_HandleFocusEntity);
        }


        /// <summary>
        /// 处理当前瞄准的Entity
        /// </summary>
        /// <param name="focusEntity"></param>
        /// <param name="hitGo"></param>
        private void HandleFocusEntity(IEntity focusEntity, GameObject hitGo)
        {
            if (focusEntity != null)
            {
                if (preEntityFocusId != focusEntity.EntityId || ForceUpdate)
                {
                    ForceUpdate = false;
                    // 隐藏上一个
                    HidePreInfo(true);

                    // 新瞄准对象
                    curUiFocusInfoList.Clear();

                    switch (focusEntity.EntityType)
                    {
                        case EntityTypeId.PlayerEntity:
                            HandlePlayerEntityFocus(focusEntity);
                            break;

                        case EntityTypeId.MonsterEntity:
                            HandleMonsterEntityFocus(focusEntity);
                            break;

                        case EntityTypeId.CollectableEntity:
                            HandleCollectableEntityFocus(focusEntity);
                            break;

                        case EntityTypeId.VehicleEntity:
                        case EntityTypeId.ModularCarEntity:
                        case EntityTypeId.VehicleModuleCustom:
                            HandleVehicleEntityFocus(focusEntity);
                            break;

                        case EntityTypeId.PartEntity:
                            HandlePartEntityFocus(focusEntity);
                            break;
                        case EntityTypeId.TrapEntity:
                            HandleTrapEntityFocus(focusEntity);
                            break;
                        case EntityTypeId.BoxEntity:
                        case EntityTypeId.AirdropEntity:
                            HandleBoxEntityFocus(focusEntity);
                            break;

                        case EntityTypeId.CorpseEntity:
                            HandleCorpseEntityFocus(focusEntity);
                            break;

                        case EntityTypeId.IOEntity:
                            HandleIOEntityFocus(focusEntity);
                            break;

                        case EntityTypeId.ElevatorEntity:
                            // 电梯需求只要交互列表，不需要名字，因此本段暂时注释
                            // HandleElevatorEntityFocus(focusEntity);
                            break;

                        case EntityTypeId.HorseEntity:
                            HandleHorseEntityFocus(focusEntity);
                            break;

                        case EntityTypeId.TrainBarricadeEntity:
                            SetUIFocusInfo(mTrainFocusTypes, focusEntity as TrainBarricadeEntity);
                            break;

                        case EntityTypeId.TrainCarEntity:
                            HandleTrainCarEntityFocus(focusEntity);
                            break;

                        case EntityTypeId.DigEntity:
                            HandleDigEntityFocus(focusEntity, hitGo);
                            break;

                        case EntityTypeId.StorageDebrisEntity:
                            HandleStorageDebrisEntityFocus(focusEntity);
                            break;
                        default:
                            break;
                    }
                }
                else
                {
                    // 瞄准对象未改变
                    for (var i = 0; i < curUiFocusInfoList.Count; i++)
                    {
                        curUiFocusInfoList[i].Update(focusEntity);

                        var monsterEntity = focusEntity as MonsterEntity;
                        if (monsterEntity != null && monsterEntity.Hp <= 0)
                        {
                            HidePreInfo();
                        }
                    }
                }
            }
            else
            {
                HidePreInfo();
            }

            preEntityFocusId = focusEntity != null ? focusEntity.EntityId : 0;
        }


        /// <summary>
        /// 处理当前瞄准的Entity
        /// </summary>
        /// <param name="focusEntity"></param>
        /// <param name="hitGo"></param>
        private void ObserverHandleFocusEntity(IEntity focusEntity, GameObject hitGo)
        {
            if (focusEntity != null)
            {
                if (preEntityFocusId != focusEntity.EntityId)
                {
                    // 隐藏上一个
                    HidePreInfo(true);
                    // 新瞄准对象
                    curUiFocusInfoList.Clear();

                    if (focusEntity is PlayerEntity)
                    {
                        // todo 读表获取样式
                        int[] types = { 10, 11};
                        SetUIFocusInfo(types, focusEntity);
                    }

                    if (focusEntity is PartEntity)
                    {
                        int[] types = { 9 };
                        SetUIFocusInfo(types, focusEntity);
                    }
                }else{
                    int style = 9;
                    if (uiFocusInfoDic.ContainsKey(style))
                    {
                        var uiStyle = uiFocusInfoDic[style];
                        uiStyle.Update(focusEntity);
                    }
                }
            }
            else
            {
                HidePreInfo();
            }

            preEntityFocusId = focusEntity != null ? focusEntity.EntityId : 0;
        }

        /// <summary>
        /// 处理需要显示跟随血条的Entity
        /// </summary>
        /// <param name="hpFlagEntity"></param>
        private void HandleHpFlagEntity(IEntity hpFlagEntity)
        {
            if (MgrWorldFlag.usenewbar) return;
            var preHpFlagEntity = Mc.Entity.GetEntity(preHpFlagEntityId);
            if (hpFlagEntity != null)
            {
                if (preHpFlagEntityId != hpFlagEntity.EntityId)
                {
                    if (preHpFlagEntity != null)
                    {
                        Mc.WorldFlag.SetFocusShowHp(preHpFlagEntityId, false);
                    }

                    var worldFlagWin = Mc.Ui.GetWindow("UiWorldFlag");
                    if (worldFlagWin == null || !worldFlagWin.IsActive)
                    {
                        long hpFlagEntityId = hpFlagEntity.EntityId;
                        Mc.Ui.OpenWindow("UiWorldFlag", (win) =>
                        {
                            win.touchable = false;
                            Mc.WorldFlag.SetFocusShowHp(hpFlagEntityId, true);
                        });
                    }
                    else
                    {
                        Mc.WorldFlag.SetFocusShowHp(hpFlagEntity.EntityId, true);
                    }
                }
                else
                {
                    // 瞄准对象未改变
                    var monsterEntity = hpFlagEntity as MonsterEntity;
                    if (monsterEntity != null)
                    {
                        if (monsterEntity.Hp <= 0)
                        {
                            Mc.WorldFlag.SetFocusShowHp(hpFlagEntity.EntityId, false);
                        }
                    }
                }
            }
            else
            {
                if (preHpFlagEntity != null)
                {
                    Mc.WorldFlag.SetFocusShowHp(preHpFlagEntityId, false);
                }
            }

            preHpFlagEntityId = hpFlagEntity != null ? hpFlagEntity.EntityId : 0;
        }
        
        /// <summary>
        /// 处理需要显示跟随文本的Entity
        /// </summary>
        /// <param name="labelFlagEntity"></param>
        private void HandleLabelFlagEntity(IEntity labelFlagEntity)
        {
            var preLabelFlagEntity = Mc.Entity.GetEntity(preLabelFlagEntityId);
            if (labelFlagEntity != null)
            {
                if (preLabelFlagEntityId != labelFlagEntity.EntityId)
                {
                    try
                    {
                        if (preLabelFlagEntity != null)
                        {
                            Mc.WorldFlag.SetFocusShowLabel(preLabelFlagEntityId, false);
                        }

                        var worldFlagWin = Mc.Ui.GetWindow("UiWorldFlag");
                        if (worldFlagWin == null || !worldFlagWin.IsActive)
                        {
                            long labelFlagEntityId = labelFlagEntity.EntityId;
                            Mc.Ui.OpenWindow("UiWorldFlag", (win) =>
                            {
                                win.touchable = false;
                                Mc.WorldFlag.SetFocusShowLabel(labelFlagEntityId, true);
                            });
                        }
                        else
                        {
                            Mc.WorldFlag.SetFocusShowLabel(labelFlagEntity.EntityId, true);
                        }
                    }
                    catch (System.Exception e)
                    {
                        logger.Error(e);
                    }
                }
            }
            else
            {
                if (preLabelFlagEntity != null)
                {
                    Mc.WorldFlag.SetFocusShowLabel(preLabelFlagEntityId, false);
                }
            }

            preLabelFlagEntityId = labelFlagEntity != null ? labelFlagEntity.EntityId : 0;
        }


        /// <summary>
        /// 玩家瞄准处理
        /// </summary>
        /// <param name="baseEntity"></param>
        private void HandlePlayerEntityFocus(IEntity baseEntity)
        {
            if (baseEntity is not PlayerEntity playerEntity || playerEntity.IsHitableAndDead())
            {
                return;
            }

            // todo 读表获取样式
            int[] types = { 2 };
            SetUIFocusInfo(types, playerEntity);

            // 打开后可查看其他玩家名字
            if (Mc.SettingClient.canSeeOthers)
            {
                int[] gmTypes = { 1 };
                SetUIFocusInfo(gmTypes, playerEntity);
            }
        }


        /// <summary>
        /// 怪物瞄准处理
        /// </summary>
        /// <param name="baseEntity"></param>
        private void HandleMonsterEntityFocus(IEntity baseEntity)
        {
            if (baseEntity is not MonsterEntity monsterEntity || monsterEntity.Hp <= 0)
            {
                return;
            }

            var monsterData = Mc.Tables.TbMonsterOtherField.GetOrDefault(monsterEntity.TemplateId);
            if (monsterData == null || !monsterData.IsShowUI)
            {
                return;
            }

            var types = UiTypeTransform(monsterData.UIType);
            for (int i = 0; i < types.Length; i++)
            {
                SetUIFocusInfo(types, monsterEntity);
            }
        }


        /// <summary>
        /// 采集物瞄准处理
        /// </summary>
        /// <param name="baseEntity"></param>
        private void HandleCollectableEntityFocus(IEntity baseEntity)
        {
            if (baseEntity is not CollectableEntity collectableEntity)
            {
                return;
            }

            var collectableData = Mc.Tables.TbGatherResourcesCollectable.GetOrDefault(collectableEntity.TemplateId);
            if (collectableData == null || !collectableData.IsShowUI)
            {
                return;
            }

            var types = UiTypeTransform(collectableData.UIType);
            for (int i = 0; i < types.Length; i++)
            {
                SetUIFocusInfo(types, collectableEntity);
            }
        }


        /// <summary>
        /// 载具瞄准处理
        /// </summary>
        /// <param name="baseEntity"></param>
        private void HandleVehicleEntityFocus(IEntity baseEntity)
        {
            if (baseEntity is not IBaseVehicleEntity vehicleEntity ||
                Mc.MyPlayer.MyEntityLocal.MountID == baseEntity.EntityId)
            {
                return;
            }

            //var modularCarGo = Mc.Go.GetGo(Mc.MyPlayer.MyEntityLocal.MountID) as BaseVehicleModuleGo;
            //if (modularCarGo!=null)
            //{
            //    var moduleGo = (ClientModularCarGo)modularCarGo.ModularCarGo;
            //    if (moduleGo!=null)
            //    {
            //        foreach (var item in moduleGo.VehicleModuleGoes)
            //        {
            //            if (item.Value.EntityId == baseEntity.EntityId)
            //            {
            //                return;
            //            }
            //        }
            //    }
            //}
            // todo 读表获取样式
            bool ishelicopter = vehicleEntity.VehicleType == (int)Common.Data.VehicleType.AttackHelicopter;
            if (ishelicopter)
            {
                SetUIFocusInfo(new int[]{3}, baseEntity);
            }
            else
            {
                SetUIFocusInfo(new int[] { 3, 8 }, baseEntity);
            }
           
        }


        /// <summary>
        /// 马瞄准处理
        /// </summary>
        /// <param name="baseEntity"></param>
        private void HandleHorseEntityFocus(IEntity baseEntity)
        {
            if (baseEntity is not HorseEntity horseEntity || Mc.MyPlayer.MyEntityLocal.MountID == baseEntity.EntityId)
            {
                return;
            }

            if (horseEntity.Hp <= 0)
            {
                return;
            }

            // todo 读表获取样式
            int[] types = { 3 };
            SetUIFocusInfo(types, horseEntity);
        }
        
        /// <summary>
        /// 处理火车Entity聚焦
        /// </summary>
        /// <param name="baseEntity"></param>
        private void HandleTrainCarEntityFocus(IEntity baseEntity)
        {
            if (baseEntity is not TrainCarEntity || Mc.MyPlayer.MyEntityLocal.MountID == baseEntity.EntityId)
                return;
            
            if (baseEntity is ITemplateEntity templateEntity)
            {
                var tb = Mc.Tables.TbVehicleInfo.GetOrDefault(templateEntity.TemplateId);
                if (tb != null && tb.UseingFuelTank)
                {
                    int[] types = { 3, 8 };
                    SetUIFocusInfo(types, baseEntity as TrainCarEntity);
                    return;
                }
            }
            
            SetUIFocusInfo(mTrainFocusTypes, baseEntity as TrainCarEntity);
        }


        /// <summary>
        /// 建筑瞄准处理
        /// </summary>
        /// <param name="baseEntity"></param>
        private void HandlePartEntityFocus(IEntity baseEntity)
        {
            if (baseEntity is not PartEntity partEntity)
            {
                return;
            }


            // todo 读表获取样式
            int[] types = { 1, 6};
            SetUIFocusInfo(types, partEntity);

            if (partEntity.SpawnType != 2 && partEntity.HasComponent(EComponentIdEnum.Damageable))
            {
                int[] bloodStripTypes = { 3 };
                SetUIFocusInfo(bloodStripTypes, partEntity);
            }

            // if (partEntity.TemplateId == (int)PartType.SmallGenerator)
            // {
            //     int icon = 4;
            //     if (partEntity.SpawnType != 2 && uiFocusInfoDic.ContainsKey(icon))
            //     {
            //         var uiStyle = uiFocusInfoDic[icon];
            //         uiStyle.Show();
            //         uiStyle.SetInfo(partEntity);
            //         curUiFocusInfoList.Add(uiStyle);
            //     }
            // }
            
            if (partEntity.TemplateId == (int)PartType.SmallPlanterBox ||
                partEntity.TemplateId == (int)PartType.LargePlanterBox)
            {
                int plantWaterValue = 5;
                if (partEntity.SpawnType != 2 && uiFocusInfoDic.TryGetValue(plantWaterValue, out var uiStyle))
                {
                    uiStyle.Show();
                    uiStyle.SetInfo(partEntity);
                    curUiFocusInfoList.Add(uiStyle);
                }
            }

            bool isShow = false;
            var unit = partEntity?.GetComponent<ElectricBaseComponent>(EComponentIdEnum.ElectricBase)?.ElectricC;
            if (unit != null)
            {
                if (unit.Config.Type == (int)EElectricDeviceType.Battery ||
                    (unit.Config.Type == (int)EElectricDeviceType.Consumer && unit.Config.Consumption != 0))
                {
                    isShow = true;
                }
            }

            if (!isShow)
            {
                isShow = ClientConstructionUtils.CanShowEditLimitTimeInfo(partEntity);
            }
            
            if (isShow||
                partEntity.TemplateId == (int)PartType.FlameTurret ||
                partEntity.TemplateId == (int)PartType.ShotgunTrap ||
                partEntity.TemplateId == (int)PartType.SmallGenerator ||
                
                partEntity.TemplateId == (int)PartType.WaterBarrel || 
                partEntity.TemplateId == (int)PartType.SmallWaterCatcher || 
                partEntity.TemplateId == (int)PartType.LargeWaterCatcher ||
                partEntity.TemplateId == (int)PartType.WaterPump ||
                partEntity.TemplateId == (int)PartType.PoweredWaterPurifier ||
                
                partEntity.TemplateId == (int)PartType.SamSite ||
                partEntity.TemplateId == (int)PartType.AutoTurret ||
                
                partEntity.TemplateId == (int)PartType.Lantern
                )
            {
                int uiContainerInfo = 8;
                if (partEntity.SpawnType != 2 && uiFocusInfoDic.TryGetValue(uiContainerInfo, out var uiStyle))
                {
                    uiStyle.Show();
                    uiStyle.SetInfo(partEntity);
                    curUiFocusInfoList.Add(uiStyle);
                }
            }

            var electricalItemConfig = Mc.Tables.TbElectricalItem.GetOrDefault(partEntity.TemplateId);
            if (electricalItemConfig != null)
            {
                int consumptionStyle = 7;
                if (partEntity.SpawnType != 2 && uiFocusInfoDic.TryGetValue(consumptionStyle, out var uiStyle))
                {
                    uiStyle.Show();
                    uiStyle.SetInfo(partEntity);
                    curUiFocusInfoList.Add(uiStyle);
                }
            }
        }

        /// <summary>
        /// 建筑瞄准处理
        /// </summary>
        /// <param name="baseEntity"></param>
        private void HandleStorageDebrisEntityFocus(IEntity baseEntity)
        {
            if (baseEntity is not StorageDebrisEntity storageDebrisEntity)
            {
                return;
            }

            int[] types = { 8 };
            SetUIFocusInfo(types, storageDebrisEntity);
        }

        private void HandleTrapEntityFocus(IEntity baseEntity)
        {
            if (baseEntity is not TrapEntity trapEntity)
            {
                return;
            }

            int[] types = { 3 };
            SetUIFocusInfo(types, trapEntity);
        }


        /// <summary>
        /// 箱子瞄准处理
        /// </summary>
        /// <param name="baseEntity"></param>
        private void HandleBoxEntityFocus(IEntity baseEntity)
        {
            if (baseEntity is not BoxEntity boxEntity)
            {
                return;
            }

            var boxData = Mc.Tables.TbTreasureBox.GetOrDefault(boxEntity.TemplateId);
            if (boxData == null || !boxData.IsShowUI)
            {
                return;
            }

            var types = UiTypeTransform(boxData.UIType);
            SetUIFocusInfo(types, boxEntity);
        }


        /// <summary>
        /// 尸体瞄准处理
        /// </summary>
        /// <param name="baseEntity"></param>
        private void HandleCorpseEntityFocus(IEntity baseEntity)
        {
            if (baseEntity is not CorpseEntity corpseEntity)
            {
                return;
            }

            var config = Mc.Tables.TbCorpse.GetOrDefault(corpseEntity.TemplateId);
            if (config == null || !config.IsShowUI)
            {
                return;
            }

            int[] types = UiTypeTransform(config.UIType);
            SetUIFocusInfo(types, corpseEntity);
        }


        /// <summary>
        /// io交互物瞄准处理
        /// </summary>
        /// <param name="baseEntity"></param>
        private void HandleIOEntityFocus(IEntity baseEntity)
        {
            if (baseEntity is not IOEntity ioEntity)
            {
                return;
            }

            var ioGo = Mc.Go.GetGo(ioEntity.EntityId) as IOGo;
            if (ioGo == null)
            {
                return;
            }

            var interactiveIds = ioGo.GetInteractiveID();
            if (interactiveIds == null || interactiveIds.Count == 0)
            {
                return;
            }

            int[] types = { 1 };
            SetUIFocusInfo(types, ioEntity);
        }


        /// <summary>
        /// 电梯瞄准处理
        /// </summary>
        /// <param name="baseEntity"></param>
        private void HandleElevatorEntityFocus(IEntity baseEntity)
        {
            if (baseEntity is not ElevatorEntity elevatorEntity)
            {
                return;
            }

            if (Mc.Go.GetGo(elevatorEntity.EntityId) is not ElevatorGo elevatorGo)
            {
                return;
            }

            var interactiveIds = elevatorGo.GetInteractiveID();
            if (interactiveIds.Count == 0)
            {
                return;
            }

            int[] types = { 1 };
            SetUIFocusInfo(types, elevatorEntity);
        }


        private void HandleDigEntityFocus(IEntity baseEntity, GameObject hitGo)
        {
            if (baseEntity is not DigEntity digEntity)
            {
                return;
            }

            for (int i = 0; i < digEntityFocusTypes.Length; i++)
            {
                if (!uiFocusInfoDic.TryGetValue(digEntityFocusTypes[i], out var uiStyle))
                {
                    continue;
                }

                uiStyle.Show();

                if (uiStyle is UiFocusInfoLabel uiLabelStyle)
                {
                    uiLabelStyle.SetInfoForDigEntity(digEntity, hitGo);
                }

                curUiFocusInfoList.Add(uiStyle);
            }
        }


        private void SetUIFocusInfo(int[] types, IEntity entityBase)
        {
            for (int i = 0; i < types.Length; i++)
            {
                var type = types[i];
                if (type == 3 || type == 2)
                    type = 10;
                if(type == 1)type = 11;
                if (!uiFocusInfoDic.TryGetValue(type, out var uiStyle))
                {
                    continue;
                }

                uiStyle.Show();
                uiStyle.SetInfo(entityBase);
                curUiFocusInfoList.Add(uiStyle);
            }
        }


        /// <summary>
        /// 延时隐藏
        /// </summary>
        /// <param name="immediately">立刻隐藏</param>
        public void HidePreInfo(bool immediately = false)
        {
            if (immediately)
            {
                // 立刻隐藏
                for (int i = 0; i < curUiFocusInfoList.Count; i++)
                {
                    var uiInfo = curUiFocusInfoList[i];
                    if (!uiInfo.isShow)
                    {
                        continue;
                    }

                    uiInfo.Hide();
                }
            }
            else
            {
                // 延时一段时间后隐藏
                for (int i = 0; i < curUiFocusInfoList.Count; i++)
                {
                    var uiInfo = curUiFocusInfoList[i];
                    if (!uiInfo.isShow)
                    {
                        continue;
                    }

                    if (Time.time - uiInfo.finalUpdateTime < uiInfo.hideDelayTime)
                    {
                        continue;
                    }

                    uiInfo.Hide();
                }
            }
        }


        /// <summary>
        /// 显示界面
        /// </summary>
        public void ShowInfo()
        {
            ContentPane.visible = true;
        }


        /// <summary>
        /// ui类型转换
        /// </summary>
        /// <returns></returns>
        private int[] UiTypeTransform(int uiType)
        {
            int[] typeArray;
            switch (uiType)
            {
                case 1:
                    typeArray = new int[] { 1 };
                    break;

                case 2:
                    typeArray = new int[] { 2 };
                    break;

                case 3:
                    typeArray = new int[] { 1, 3 };
                    break;

                default:
                    typeArray = new int[] { uiType };
                    break;
            }

            return typeArray;
        }

        public void OnFps30Update(float dt)
        {
            int uiContainerInfo = 8;
            if (uiFocusInfoDic.TryGetValue(uiContainerInfo, out var uiStyle))
            {
                UiFocusInfoContainer confContainer = uiStyle as UiFocusInfoContainer;
                if (confContainer!=null && confContainer.isShow)
                {
                    confContainer.OnFps30Update(dt);
                }
            }
        }
    }
}