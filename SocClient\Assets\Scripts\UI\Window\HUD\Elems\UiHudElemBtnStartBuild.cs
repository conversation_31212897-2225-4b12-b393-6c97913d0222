using FairyGUI;
using System.Collections.Generic;
using System.Diagnostics;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Common.Profile;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.SDK;
using WizardGames.Soc.SocClient.Audio;
using WizardGames.Soc.SocClient.Construction;
using WizardGames.Soc.SocClient.Control;
using WizardGames.Soc.SocClient.Manager;
using Debug = UnityEngine.Debug;

namespace WizardGames.Soc.SocClient.Ui
{
    /// <summary>
    /// 开始建造按钮
    /// </summary>
    public partial class UiHudElemBtnStartBuild : UiHudElem
    {
        /// <summary>
        /// Hud面板
        /// </summary>
        private UiHud hudBoard;
        private Controller ctrlStyle;
        private Controller ctrlIcon;
        private long refreshIconTimer;
        private GButton button;
        private UiHudElemShortcuts elemShortcuts => UiHud.GetElem<UiHudElemShortcuts>(214) ;

        protected override void OnCreate(GComponent node)
        {
            base.OnCreate(node);
            hudBoard = board as UiHud;
            ctrlStyle = node.GetController("style");
            ctrlIcon = node.GetController("iconCtrl");
            button = node.asButton;
            node.SetSoundClick(Mc.Tables.TbUiAudio.UniversalButtonClick, ctx => OnClick());
        }

        protected override EResponseLevel ResposeLevel { get; } = EResponseLevel.OnUi;
        protected override ActionName HotKey {get{return ActionName.OpenBuild; } }
        protected override void OnHotKeyAction()
        {
            if (!Mc.Construction.IsInBuildModeWithoutShortcuts)
            {
                HotKeyUtils.InvokeAllEsc();
            }
            button.onClick.Call();
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            Mc.Ui.RemoveEscStack(EEscCloseName.BuildPanel);

            if (!Mc.Ui.IsInGame) return;

            Mc.Msg.RemoveListener(EventDefine.UpdateSingleMission, TriggerGuide);
            Mc.Msg.RemoveListener(EventDefine.UpdateTrackMissionId, TriggerGuide);
            Mc.Msg.RemoveListener<bool>(EventDefine.AidingOtherEvent, Forbid);
        }

        private void RefreshIconStyle()
        {
            if (refreshIconTimer > 0) Mc.TimerWheel.CancelTimer(refreshIconTimer);
            refreshIconTimer = Mc.TimerWheel.AddTimerOnce(100, RefreshIconTimerCallback);
        }

        private void RefreshIconTimerCallback(long timerId, object data = null, bool delete = false)
        {
            refreshIconTimer = 0;
            ctrlIcon.SetSelectedPage(Mc.Construction.IsInBuildModeWithoutShortcuts ? "exit" : "enter");
            if (Mc.Construction.IsInBuildModeWithoutShortcuts)
            {
                Mc.Ui.AddEscStack(EEscCloseName.BuildPanel, () =>
                {
                    button.onClick.Call();
                    return true;
                }, LayerIdConst.HUD);
            }
            else
            {
                Mc.Ui.RemoveEscStack(EEscCloseName.BuildPanel);
            }

            SetUpdateModeIfLock(FGUIUpdateType.LockAfterShow);
            // 检查引导触发
            TriggerGuide();
        }

        protected override void OnEnable()
        {
            base.OnEnable();

            if (!Mc.Ui.IsInGame) return;

            var myEntity = Mc.MyPlayer?.MyEntityLocal;
            if (myEntity != null)
            {
                Forbid(myEntity.IsAidingOther);
            }

            RefreshIconStyle();

            Mc.Msg.AddListener(EventDefine.UpdateSingleMission, TriggerGuide);
            Mc.Msg.AddListener(EventDefine.UpdateTrackMissionId, TriggerGuide);
            Mc.Msg.AddListener<bool>(EventDefine.AidingOtherEvent, Forbid);
            TriggerGuide();

            OnEnable_Platform();
        }

        public override void Show()
        {
            base.Show();
            if (!IsInEdit) RefreshIconStyle();
        }

        private void OnClick()
        {
            if (!IsGameLogicValid)
                return;
            var playerEntity = Mc.MyPlayer.MyEntityLocal;
            if ( playerEntity.IsWounded ||
                playerEntity.IsDead || playerEntity.IsAidingOther ||
                playerEntity.IsDying || playerEntity.IsCrawl || playerEntity.IsIncapacitated)
                return;

            if (!Mc.Construction.IsInBuildMode || Mc.Construction.IsShortcutsDeployMode)
            {
                Mc.Construction.EnterFirstBuildMode();
            }
            else
            {
                Mc.LocalLog.AddLog(EButtonId.LeftExitConstruction, EButtonGroup.ConstructionGroup4);
                Mc.Construction.ExitBuildMode(); 
            }
            RefreshIconStyle();
        }
        
        private void Forbid(bool forbidden)
        {
            if (!IsElemEnable) return;
            ctrlStyle.SetSelectedPage(forbidden?"forbid": "normal");
        }

        public override bool CanGuideState(string state)
        {
            if (null == ctrlIcon) return false;
            return ctrlIcon.selectedPage.Equals(state);
        }

        private void TriggerGuide()
        {
            if (PlayHelper.IsNewbie)
            {
                if (CanGuideState("exit")) 
                {
                    Mc.Mission.TryGuideIfHasMisson(2074);
                }
                return;
            }
            if (Mc.Mission != null && Mc.Mission.IsNewbieTracked)
            {
                ProfilerApi.BeginSample(EProfileFunc.Client_UiHudElemBtnStartBuild_TriggerGuide);
                if (!CanGuideState("enter"))
                {
                    ProfilerApi.EndSample(EProfileFunc.Client_UiHudElemBtnStartBuild_TriggerGuide);
                    return;
                }
                var missionCfg = Mc.Tables.TbQuestPhase.GetOrDefault(Mc.Mission.NewbieRemoteData.Id);
                if (missionCfg == null)
                {
                    ProfilerApi.EndSample(EProfileFunc.Client_UiHudElemBtnStartBuild_TriggerGuide);
                    return;
                }
                switch (missionCfg.GuideId) 
                {
                    case 110:
                        Mc.Msg.FireMsg(EventDefine.TryGuideInterface, missionCfg.GuideId, 2040);
                        break;
                    case 129:
                        Mc.Msg.FireMsg(EventDefine.TryGuideInterface, missionCfg.GuideId, 1290);
                        break;
                    case 153:
                        Mc.Msg.FireMsg(EventDefine.TryGuideInterface, missionCfg.GuideId, 1530);
                        break;
                    case 142:
                        Mc.Msg.FireMsg(EventDefine.TryGuideInterface, missionCfg.GuideId, -1);
                        break;
                    case 111:
                        Mc.Msg.FireMsg(EventDefine.TryGuideInterface, missionCfg.GuideId, 11111);
                        break;
                    case 162:
                        Mc.Msg.FireMsg(EventDefine.TryGuideInterface, missionCfg.GuideId, 1801);
                        break;
                }
                ProfilerApi.EndSample(EProfileFunc.Client_UiHudElemBtnStartBuild_TriggerGuide);
            }
        }
        public override void StartEdit()
        {
            base.StartEdit();
            var hotArea = TryGetNode()?.GetChild("hotArea").asCom;
            if (hotArea != null)
            {
                hotArea.touchable = false;
            }
        }
        public override void EndEdit(bool needToFalseIsInEditor = true)
        {
            base.EndEdit(needToFalseIsInEditor);
            var hotArea = TryGetNode()?.GetChild("hotArea").asCom;
            if (hotArea != null)
            {
                hotArea.touchable = true;
            }
        }
    }
}
