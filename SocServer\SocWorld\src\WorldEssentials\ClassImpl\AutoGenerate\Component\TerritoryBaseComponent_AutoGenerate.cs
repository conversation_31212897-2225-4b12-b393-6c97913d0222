using System;
using System.Buffers;
using System.Collections.Generic;
using SimpleJSON;
using MessagePack;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Framework.Types;
using WizardGames.Soc.Common.Framework.Const;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Entity;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.SocWorld.Framework;
namespace WizardGames.Soc.Share.Framework
{
    public static partial class ContainerDecoder
    {
        public static object DeserializeTerritoryBaseComponentContainer(ref MessagePackReader reader, int propertyId, ESerializeMode mode)
        {
            switch (propertyId)
            {
                case TerritoryBaseComponent.PropertyIds.LOC_LIST:
                    {
                        return ListOfArrayDataSet.GetFromPool(ref reader, mode, 254460052);
                    }
                case TerritoryBaseComponent.PropertyIds.NORMAL_PART_SET:
                    {
                        return new BasicTypeHashSet<long>(ref reader, mode);
                    }
                case TerritoryBaseComponent.PropertyIds.GAP_LINK_INFO_DIC:
                    {
                        return DictOfArrayDataSet.GetFromPool(ref reader, mode, 223016299);
                    }
                case TerritoryBaseComponent.PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD:
                    {
                        return new BasicTypeList<long>(ref reader, mode);
                    }
                case TerritoryBaseComponent.PropertyIds.DEBRIS_ENTITY_IDS:
                    {
                        return new BasicTypeHashSet<long>(ref reader, mode);
                    }
                default: return null;
            }
        }
    }
    public static partial class ShadowContainerDecoder
    {
        public static SyncContainerBase DeserializeTerritoryBaseComponentContainer(ref MessagePackReader reader, int propertyId, long entityId)
        {
            switch (propertyId)
            {
                case TerritoryBaseComponent.PropertyIds.LOC_LIST:
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            return null;
                        }
                        return SyncList.GetFromPool(ref reader, 254460052, entityId);
                    }
                case TerritoryBaseComponent.PropertyIds.NORMAL_PART_SET:
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            return null;
                        }
                        return SyncBasicTypeContainer.GetFromPool(new BasicTypeHashSet<long>(ref reader, ESerializeMode.All));
                    }
                case TerritoryBaseComponent.PropertyIds.GAP_LINK_INFO_DIC:
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            return null;
                        }
                        return SyncDictionary.GetFromPool(ref reader, 223016299, entityId);
                    }
                case TerritoryBaseComponent.PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD:
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            return null;
                        }
                        return SyncBasicTypeContainer.GetFromPool(new BasicTypeList<long>(ref reader, ESerializeMode.All));
                    }
                case TerritoryBaseComponent.PropertyIds.DEBRIS_ENTITY_IDS:
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            return null;
                        }
                        return SyncBasicTypeContainer.GetFromPool(new BasicTypeHashSet<long>(ref reader, ESerializeMode.All));
                    }
                default: return null;
            }
        }
    }
}
namespace WizardGames.Soc.Common.Component
{
    public partial class TerritoryBaseComponent
    {
#if UNITY_5_6_OR_NEWER
        [UnityEngine.Scripting.Preserve]public const int CLASS_HASH = 1465637299;
#else
        public const int CLASS_HASH = 1465637299;
#endif
        public static int StaticClassHash = CLASS_HASH;
        public override int GetClassHash() => CLASS_HASH;
        public const string TYPE_NAME = "TerritoryBaseComponent";
        public override string GetTypeName() => TYPE_NAME;
        public override int Id => (int)EComponentIdEnum.TerritoryBase;
        public TerritoryBaseComponent() { }
        public TerritoryBaseComponent(ref MessagePackReader reader, ESerializeMode mode) : base(ref reader, mode) { }
        public TerritoryBaseComponent(bool useless, JSONNode json) : base(useless, json) { }
        public override ESyncRange SyncRange{ get; } = ESyncRange.All;
#pragma warning disable CS0108
        /// <summary>
        /// 属性Id
        /// </summary>
        public static class PropertyIds
        {
            public const int PART_ENTITY_ID = 0;
            public const int GRID_ID = 1;
            public const int SPAWN_TYPE = 2;
            public const int LOC_LIST = 3;
            public const int CREATE_TIME_STAMP = 4;
            public const int CREATOR_ROLE_ID = 5;
            public const int NORMAL_PART_SET = 6;
            public const int NORMAL_PART_COUNT = 7;
            public const int MOVED_TIMES = 8;
            public const int PART_LIMIT_INFO = 9;
            public const int LAST_BATCH_UPGRADE_TS = 10;
            public const int GAP_LINK_INFO_DIC = 11;
            public const int PARTS_DESTROY_WITH_TOOLCUP_BOARD = 12;
            public const int DEBRIS_ENTITY_IDS = 13;
            public const int TOOL_CUPBOARD_DEBRIS_DESTROY_TS = 14;
        }
#pragma warning restore CS0108
        public static readonly int[] PropertyInfoArray = new int[]
        {
            PropertyIds.PART_ENTITY_ID, PropertyIds.GRID_ID, PropertyIds.SPAWN_TYPE, PropertyIds.LOC_LIST, PropertyIds.CREATE_TIME_STAMP, PropertyIds.CREATOR_ROLE_ID, PropertyIds.NORMAL_PART_SET, PropertyIds.NORMAL_PART_COUNT, PropertyIds.MOVED_TIMES, PropertyIds.PART_LIMIT_INFO, PropertyIds.LAST_BATCH_UPGRADE_TS, PropertyIds.GAP_LINK_INFO_DIC, PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD, PropertyIds.DEBRIS_ENTITY_IDS, PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS
        };
        public static readonly int[] PropertyTypeArray = new int[]
        {
            2, 2, 1, 13, 2, 11, 13, 1, 1, 12, 2, 13, 13, 13, 10
        };
        public static readonly int[] PersistentPropertyIdArray = new int[]
        {
            PropertyIds.PART_ENTITY_ID, PropertyIds.GRID_ID, PropertyIds.SPAWN_TYPE, PropertyIds.LOC_LIST, PropertyIds.CREATE_TIME_STAMP, PropertyIds.CREATOR_ROLE_ID, PropertyIds.NORMAL_PART_SET, PropertyIds.NORMAL_PART_COUNT, PropertyIds.MOVED_TIMES, PropertyIds.LAST_BATCH_UPGRADE_TS, PropertyIds.GAP_LINK_INFO_DIC, PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD, PropertyIds.DEBRIS_ENTITY_IDS, PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS
        };
        public static readonly HashSet<int> PersistentPropertyIds = new ()
        {
            PropertyIds.PART_ENTITY_ID, PropertyIds.GRID_ID, PropertyIds.SPAWN_TYPE, PropertyIds.LOC_LIST, PropertyIds.CREATE_TIME_STAMP, PropertyIds.CREATOR_ROLE_ID, PropertyIds.NORMAL_PART_SET, PropertyIds.NORMAL_PART_COUNT, PropertyIds.MOVED_TIMES, PropertyIds.LAST_BATCH_UPGRADE_TS, PropertyIds.GAP_LINK_INFO_DIC, PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD, PropertyIds.DEBRIS_ENTITY_IDS, PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS
        };
        public static readonly int[] OwnClientPropertyInfoArray = new int[]
        {
            PropertyIds.PART_ENTITY_ID, PropertyIds.SPAWN_TYPE, PropertyIds.LOC_LIST, PropertyIds.CREATE_TIME_STAMP, PropertyIds.CREATOR_ROLE_ID, PropertyIds.NORMAL_PART_COUNT, PropertyIds.MOVED_TIMES, PropertyIds.PART_LIMIT_INFO, PropertyIds.LAST_BATCH_UPGRADE_TS, PropertyIds.GAP_LINK_INFO_DIC, PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS
        };
        public static readonly int[] OtherClientPropertyInfoArray = new int[]
        {
            PropertyIds.PART_ENTITY_ID, PropertyIds.SPAWN_TYPE, PropertyIds.LOC_LIST, PropertyIds.CREATE_TIME_STAMP, PropertyIds.CREATOR_ROLE_ID, PropertyIds.NORMAL_PART_COUNT, PropertyIds.MOVED_TIMES, PropertyIds.PART_LIMIT_INFO, PropertyIds.LAST_BATCH_UPGRADE_TS, PropertyIds.GAP_LINK_INFO_DIC, PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS
        };
        public static readonly int[] ClientsPropertyInfoArray = new int[]
        {
            (TypeDefine.LONG << 16) | PropertyIds.PART_ENTITY_ID, (TypeDefine.INT << 16) | PropertyIds.SPAWN_TYPE, (TypeDefine.CONTAINER << 16) | PropertyIds.LOC_LIST, (TypeDefine.LONG << 16) | PropertyIds.CREATE_TIME_STAMP, (TypeDefine.ULONG << 16) | PropertyIds.CREATOR_ROLE_ID, (TypeDefine.INT << 16) | PropertyIds.NORMAL_PART_COUNT, (TypeDefine.INT << 16) | PropertyIds.MOVED_TIMES, (TypeDefine.CUSTOM_TYPE << 16) | PropertyIds.PART_LIMIT_INFO, (TypeDefine.LONG << 16) | PropertyIds.LAST_BATCH_UPGRADE_TS, (TypeDefine.CONTAINER << 16) | PropertyIds.GAP_LINK_INFO_DIC, (TypeDefine.UINT << 16) | PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS
        };
        public static readonly int[] UnityDsPropertyInfoArray = new int[]
        {
            PropertyIds.PART_ENTITY_ID, PropertyIds.GRID_ID, PropertyIds.SPAWN_TYPE, PropertyIds.LOC_LIST, PropertyIds.CREATE_TIME_STAMP, PropertyIds.CREATOR_ROLE_ID, PropertyIds.NORMAL_PART_COUNT, PropertyIds.PART_LIMIT_INFO, PropertyIds.GAP_LINK_INFO_DIC, PropertyIds.DEBRIS_ENTITY_IDS
        };
        public static readonly int[] CustomHashValueArray = new int[]
        {
            0, 0, 0, 0, 0, 0, 0, 0, 0, 2140723870, 0, 0, 0, 0, 0
        };
        public static readonly ESyncRange[] SyncRangeArray = new ESyncRange[]
        {
            ESyncRange.All, ESyncRange.UnityDs, ESyncRange.All, ESyncRange.All, ESyncRange.All, ESyncRange.All, ESyncRange.LocalOnly, ESyncRange.All, ESyncRange.Clients, ESyncRange.All, ESyncRange.Clients, ESyncRange.All, ESyncRange.LocalOnly, ESyncRange.UnityDs, ESyncRange.Clients
        };
        public static readonly bool[] SyncDelayArray = new bool[]
        {
            false, false, false, false, false, false, false, false, false, false, false, false, false, false, false
        };
        public static readonly int[] LodArray = new int[]
        {
            9, 0, 9, 9, 9, 9, 0, 9, 9, 9, 9, 9, 0, 0, 9
        };
        public static readonly string[] PropNameArray = new string[]
        {
            "PartEntityId", "GridId", "SpawnType", "LocList", "CreateTimeStamp", "CreatorRoleId", "NormalPartSet", "NormalPartCount", "MovedTimes", "PartLimitInfo", "LastBatchUpgradeTs", "GapLinkInfoDic", "PartsDestroyWithToolcupBoard", "DebrisEntityIds", "ToolCupboardDebrisDestroyTs"
        };
        public static readonly int[] PropId2Index = new int[]
        {
            0, 1, 2, 0 + 10000, 3, 4, 1 + 10000, 5, 6, 2 + 10000, 7, 3 + 10000, 4 + 10000, 5 + 10000, 8
        };
        public static readonly int[] RefIndexInfoArray = new int[]
        {
            PropertyIds.LOC_LIST, PropertyIds.NORMAL_PART_SET, PropertyIds.PART_LIMIT_INFO, PropertyIds.GAP_LINK_INFO_DIC, PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD, PropertyIds.DEBRIS_ENTITY_IDS
        };
        public const int VALUE_TYPE_COUNT = 9;
        public const int REF_TYPE_COUNT = 6;
        public const int AVAILABLE_LODS = 9;
        public const int CLASS_LOD = 9;
        /// <summary>
        /// 领地柜实体Id
        /// </summary>
        public long PartEntityId
        {
            get => _partEntityId;
            set
            {
                if (_partEntityId == value) return;
                var oldValue = _partEntityId;
                _partEntityId = value;
                TransactionAssignBackup(PropertyIds.PART_ENTITY_ID, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.PART_ENTITY_ID, value)) return;
                TryInvokeFieldChange(PropertyIds.PART_ENTITY_ID, oldValue, value);
            }
        }
        /// <summary>
        /// 所述格子Entity的Id
        /// </summary>
        public long GridId
        {
            get => _gridId;
            set
            {
                if (_gridId == value) return;
                var oldValue = _gridId;
                _gridId = value;
                TransactionAssignBackup(PropertyIds.GRID_ID, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.GRID_ID, value)) return;
                TryInvokeFieldChange(PropertyIds.GRID_ID, oldValue, value);
            }
        }
        /// <summary>
        /// 刷新类型
        /// </summary>
        public int SpawnType
        {
            get => _spawnType;
            set
            {
                if (_spawnType == value) return;
                var oldValue = _spawnType;
                _spawnType = value;
                TransactionAssignBackup(PropertyIds.SPAWN_TYPE, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.SPAWN_TYPE, value)) return;
                TryInvokeFieldChange(PropertyIds.SPAWN_TYPE, oldValue, value);
            }
        }
        /// <summary>
        /// 位置信息
        /// </summary>
        public CustomTypeList<TerritoryCenterLocationInfo> LocList
        {
            get => _locList;
            set
            {
                if (_locList == value) return;
                var oldValue = _locList;
                _locList?.ClearParentInfo();
                _locList = value;
                value?.SetParentInfo(this, PropertyIds.LOC_LIST);
                TransactionAssignBackup(PropertyIds.LOC_LIST, oldValue);
                var ctx = new DiffContext() {TemplateHashValue = 254460052};
                if (!SyncPropValue(ref ctx, PropertyIds.LOC_LIST, value)) return;
                TryInvokeFieldChange(PropertyIds.LOC_LIST, oldValue, value);
            }
        }
        /// <summary>
        /// 创建时间
        /// </summary>
        public long CreateTimeStamp
        {
            get => _createTimeStamp;
            set
            {
                if (_createTimeStamp == value) return;
                var oldValue = _createTimeStamp;
                _createTimeStamp = value;
                TransactionAssignBackup(PropertyIds.CREATE_TIME_STAMP, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.CREATE_TIME_STAMP, value)) return;
                TryInvokeFieldChange(PropertyIds.CREATE_TIME_STAMP, oldValue, value);
            }
        }
        /// <summary>
        /// 创建者
        /// </summary>
        public ulong CreatorRoleId
        {
            get => _creatorRoleId;
            set
            {
                if (_creatorRoleId == value) return;
                var oldValue = _creatorRoleId;
                _creatorRoleId = value;
                TransactionAssignBackup(PropertyIds.CREATOR_ROLE_ID, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.CREATOR_ROLE_ID, value)) return;
                TryInvokeFieldChange(PropertyIds.CREATOR_ROLE_ID, oldValue, value);
            }
        }
        /// <summary>
        /// 该领地管辖的PartEntity的Id
        /// </summary>
        public BasicTypeHashSet<long> NormalPartSet
        {
            get => _normalPartSet;
            set
            {
                if (_normalPartSet == value) return;
                var oldValue = _normalPartSet;
                _normalPartSet?.ClearParentInfo();
                _normalPartSet = value;
                value?.SetParentInfo(this, PropertyIds.NORMAL_PART_SET);
                TransactionAssignBackup(PropertyIds.NORMAL_PART_SET, oldValue);
                var ctx = new DiffContext() {TemplateHashValue = -1};
                if (!SyncPropValue(ref ctx, PropertyIds.NORMAL_PART_SET, value)) return;
                TryInvokeFieldChange(PropertyIds.NORMAL_PART_SET, oldValue, value);
            }
        }
        /// <summary>
        /// 该领地管辖的PartEntity的数量
        /// </summary>
        public int NormalPartCount
        {
            get => _normalPartCount;
            set
            {
                if (_normalPartCount == value) return;
                var oldValue = _normalPartCount;
                _normalPartCount = value;
                TransactionAssignBackup(PropertyIds.NORMAL_PART_COUNT, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.NORMAL_PART_COUNT, value)) return;
                TryInvokeFieldChange(PropertyIds.NORMAL_PART_COUNT, oldValue, value);
            }
        }
        /// <summary>
        /// 移动次数
        /// </summary>
        public int MovedTimes
        {
            get => _movedTimes;
            set
            {
                if (_movedTimes == value) return;
                var oldValue = _movedTimes;
                _movedTimes = value;
                TransactionAssignBackup(PropertyIds.MOVED_TIMES, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.MOVED_TIMES, value)) return;
                TryInvokeFieldChange(PropertyIds.MOVED_TIMES, oldValue, value);
            }
        }
        /// <summary>
        /// 个人建筑数量
        /// </summary>
        public PartLimitInfo PartLimitInfo
        {
            get => _partLimitInfo;
            set
            {
                if (_partLimitInfo == value) return;
                var oldValue = _partLimitInfo;
                _partLimitInfo?.ClearParentInfo();
                _partLimitInfo = value;
                value?.SetParentInfo(this, PropertyIds.PART_LIMIT_INFO);
                TransactionAssignBackup(PropertyIds.PART_LIMIT_INFO, oldValue);
                var ctx = new DiffContext() {TemplateHashValue = 2140723870};
                if (!SyncPropValue(ref ctx, PropertyIds.PART_LIMIT_INFO, value)) return;
                TryInvokeFieldChange(PropertyIds.PART_LIMIT_INFO, oldValue, value);
            }
        }
        // 一键升级
        /// <summary>
        /// 上一次一键升级时间
        /// </summary>
        public long LastBatchUpgradeTs
        {
            get => _lastBatchUpgradeTs;
            set
            {
                if (_lastBatchUpgradeTs == value) return;
                var oldValue = _lastBatchUpgradeTs;
                _lastBatchUpgradeTs = value;
                TransactionAssignBackup(PropertyIds.LAST_BATCH_UPGRADE_TS, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.LAST_BATCH_UPGRADE_TS, value)) return;
                TryInvokeFieldChange(PropertyIds.LAST_BATCH_UPGRADE_TS, oldValue, value);
            }
        }
        /// <summary>
        /// 当前管辖的美缝数量
        /// </summary>
        public CustomValueDictionary<long, ConstructionGapLinkInfo> GapLinkInfoDic
        {
            get => _gapLinkInfoDic;
            set
            {
                if (_gapLinkInfoDic == value) return;
                var oldValue = _gapLinkInfoDic;
                _gapLinkInfoDic?.ClearParentInfo();
                _gapLinkInfoDic = value;
                value?.SetParentInfo(this, PropertyIds.GAP_LINK_INFO_DIC);
                TransactionAssignBackup(PropertyIds.GAP_LINK_INFO_DIC, oldValue);
                var ctx = new DiffContext() {TemplateHashValue = 223016299};
                if (!SyncPropValue(ref ctx, PropertyIds.GAP_LINK_INFO_DIC, value)) return;
                TryInvokeFieldChange(PropertyIds.GAP_LINK_INFO_DIC, oldValue, value);
            }
        }
        /// <summary>
        /// 需要与工具柜一起摧毁的建筑Id
        /// </summary>
        public BasicTypeList<long> PartsDestroyWithToolcupBoard
        {
            get => _partsDestroyWithToolcupBoard;
            set
            {
                if (_partsDestroyWithToolcupBoard == value) return;
                var oldValue = _partsDestroyWithToolcupBoard;
                _partsDestroyWithToolcupBoard?.ClearParentInfo();
                _partsDestroyWithToolcupBoard = value;
                value?.SetParentInfo(this, PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD);
                TransactionAssignBackup(PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD, oldValue);
                var ctx = new DiffContext() {TemplateHashValue = -1};
                if (!SyncPropValue(ref ctx, PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD, value)) return;
                TryInvokeFieldChange(PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD, oldValue, value);
            }
        }
        /// <summary>
        /// 领地残骸EntityIds
        /// </summary>
        public BasicTypeHashSet<long> DebrisEntityIds
        {
            get => _debrisEntityIds;
            set
            {
                if (_debrisEntityIds == value) return;
                var oldValue = _debrisEntityIds;
                _debrisEntityIds?.ClearParentInfo();
                _debrisEntityIds = value;
                value?.SetParentInfo(this, PropertyIds.DEBRIS_ENTITY_IDS);
                TransactionAssignBackup(PropertyIds.DEBRIS_ENTITY_IDS, oldValue);
                var ctx = new DiffContext() {TemplateHashValue = -1};
                if (!SyncPropValue(ref ctx, PropertyIds.DEBRIS_ENTITY_IDS, value)) return;
                TryInvokeFieldChange(PropertyIds.DEBRIS_ENTITY_IDS, oldValue, value);
            }
        }
        /// <summary>
        /// 领地残骸消失时间戳 ProcessEntity.Instance.SecSinceStartup
        /// </summary>
        public uint ToolCupboardDebrisDestroyTs
        {
            get => _toolCupboardDebrisDestroyTs;
            set
            {
                if (_toolCupboardDebrisDestroyTs == value) return;
                var oldValue = _toolCupboardDebrisDestroyTs;
                _toolCupboardDebrisDestroyTs = value;
                TransactionAssignBackup(PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS, oldValue);
                var ctx = new DiffContext();
                if (!SyncPropValue(ref ctx, PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS, value)) return;
                TryInvokeFieldChange(PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS, oldValue, value);
            }
        }
        public override TypeBase SyncUpdateFieldValue(ref EntitySingleDeltaPropertySegment seg, ReadOnlySequence<byte> buffer, int currentIndex)
        {
            var propertyId = seg.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.PART_ENTITY_ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.PART_ENTITY_ID, newValue);
                    var oldValue = _partEntityId;
                    _partEntityId = newValue;
                    TryInvokeFieldChange(0, oldValue, _partEntityId);
                    return this;
                }
                case PropertyIds.GRID_ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.GRID_ID, newValue);
                    var oldValue = _gridId;
                    _gridId = newValue;
                    TryInvokeFieldChange(1, oldValue, _gridId);
                    return this;
                }
                case PropertyIds.SPAWN_TYPE:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.INT) throw new Exception($"Type mismatch, expecting int got {seg.BasicType.Type}");
#endif
                    int newValue = seg.BasicType.IntValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.SPAWN_TYPE, newValue);
                    var oldValue = _spawnType;
                    _spawnType = newValue;
                    TryInvokeFieldChange(2, oldValue, _spawnType);
                    return this;
                }
                case PropertyIds.LOC_LIST:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _locList?.ClearParentInfo();
                            _locList?.ResetWhenDeserialize();
                            CustomTypeList<TerritoryCenterLocationInfo> newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = new(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = 254460052, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                            SyncPropValueInSyncUpdate(ref ctx, PropertyIds.LOC_LIST, newValue);
                            var oldValue = _locList;
                            _locList = newValue;
                            _locList?.SetParentInfo(this, PropertyIds.LOC_LIST);
                            TryInvokeFieldChange(3, oldValue, _locList);
                            return this;
                        }
                        else
                        {
                            return _locList.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                case PropertyIds.CREATE_TIME_STAMP:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.CREATE_TIME_STAMP, newValue);
                    var oldValue = _createTimeStamp;
                    _createTimeStamp = newValue;
                    TryInvokeFieldChange(4, oldValue, _createTimeStamp);
                    return this;
                }
                case PropertyIds.CREATOR_ROLE_ID:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.ULONG) throw new Exception($"Type mismatch, expecting ulong got {seg.BasicType.Type}");
#endif
                    ulong newValue = seg.BasicType.ULongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.CREATOR_ROLE_ID, newValue);
                    var oldValue = _creatorRoleId;
                    _creatorRoleId = newValue;
                    TryInvokeFieldChange(5, oldValue, _creatorRoleId);
                    return this;
                }
                case PropertyIds.NORMAL_PART_SET:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _normalPartSet?.ClearParentInfo();
                            _normalPartSet?.ResetWhenDeserialize();
                            BasicTypeHashSet<long> newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = new(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = -1, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                            SyncPropValueInSyncUpdate(ref ctx, PropertyIds.NORMAL_PART_SET, newValue);
                            var oldValue = _normalPartSet;
                            _normalPartSet = newValue;
                            _normalPartSet?.SetParentInfo(this, PropertyIds.NORMAL_PART_SET);
                            TryInvokeFieldChange(6, oldValue, _normalPartSet);
                            return this;
                        }
                        else
                        {
                            return _normalPartSet.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                case PropertyIds.NORMAL_PART_COUNT:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.INT) throw new Exception($"Type mismatch, expecting int got {seg.BasicType.Type}");
#endif
                    int newValue = seg.BasicType.IntValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.NORMAL_PART_COUNT, newValue);
                    var oldValue = _normalPartCount;
                    _normalPartCount = newValue;
                    TryInvokeFieldChange(7, oldValue, _normalPartCount);
                    return this;
                }
                case PropertyIds.MOVED_TIMES:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.INT) throw new Exception($"Type mismatch, expecting int got {seg.BasicType.Type}");
#endif
                    int newValue = seg.BasicType.IntValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.MOVED_TIMES, newValue);
                    var oldValue = _movedTimes;
                    _movedTimes = newValue;
                    TryInvokeFieldChange(8, oldValue, _movedTimes);
                    return this;
                }
                case PropertyIds.PART_LIMIT_INFO:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _partLimitInfo?.ClearParentInfo();
                            PartLimitInfo newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = CustomTypeHelper.DeserializeCustomTypeBase<PartLimitInfo>(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = 2140723870, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                            SyncPropValueInSyncUpdate(ref ctx, PropertyIds.PART_LIMIT_INFO, newValue);
                            var oldValue = _partLimitInfo;
                            _partLimitInfo = newValue;
                            _partLimitInfo?.SetParentInfo(this, PropertyIds.PART_LIMIT_INFO);
                            TryInvokeFieldChange(9, oldValue, _partLimitInfo);
                            return this;
                        }
                        else
                        {
                            return _partLimitInfo.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                case PropertyIds.LAST_BATCH_UPGRADE_TS:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.LONG) throw new Exception($"Type mismatch, expecting long got {seg.BasicType.Type}");
#endif
                    long newValue = seg.BasicType.LongValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.LAST_BATCH_UPGRADE_TS, newValue);
                    var oldValue = _lastBatchUpgradeTs;
                    _lastBatchUpgradeTs = newValue;
                    TryInvokeFieldChange(10, oldValue, _lastBatchUpgradeTs);
                    return this;
                }
                case PropertyIds.GAP_LINK_INFO_DIC:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _gapLinkInfoDic?.ClearParentInfo();
                            _gapLinkInfoDic?.ResetWhenDeserialize();
                            CustomValueDictionary<long, ConstructionGapLinkInfo> newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = new(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = 223016299, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                            SyncPropValueInSyncUpdate(ref ctx, PropertyIds.GAP_LINK_INFO_DIC, newValue);
                            var oldValue = _gapLinkInfoDic;
                            _gapLinkInfoDic = newValue;
                            _gapLinkInfoDic?.SetParentInfo(this, PropertyIds.GAP_LINK_INFO_DIC);
                            TryInvokeFieldChange(11, oldValue, _gapLinkInfoDic);
                            return this;
                        }
                        else
                        {
                            return _gapLinkInfoDic.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                case PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _partsDestroyWithToolcupBoard?.ClearParentInfo();
                            _partsDestroyWithToolcupBoard?.ResetWhenDeserialize();
                            BasicTypeList<long> newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = new(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = -1, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                            SyncPropValueInSyncUpdate(ref ctx, PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD, newValue);
                            var oldValue = _partsDestroyWithToolcupBoard;
                            _partsDestroyWithToolcupBoard = newValue;
                            _partsDestroyWithToolcupBoard?.SetParentInfo(this, PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD);
                            TryInvokeFieldChange(12, oldValue, _partsDestroyWithToolcupBoard);
                            return this;
                        }
                        else
                        {
                            return _partsDestroyWithToolcupBoard.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                case PropertyIds.DEBRIS_ENTITY_IDS:
                    {
                        if (currentIndex == seg.PathLen - 1)
                        {
                            _debrisEntityIds?.ClearParentInfo();
                            _debrisEntityIds?.ResetWhenDeserialize();
                            BasicTypeHashSet<long> newValue;
                            if (seg.BasicType.Type == TypeDefine.NULL)
                            {
                                newValue = null;
                            }
                            else
                            {
                                var reader = new MessagePackReader(buffer.Slice(seg.StartIndex, seg.Len));
                                newValue = new(ref reader, seg.SerializeMode);
                            }
                            var ctx = new DiffContext() { TemplateHashValue = -1, ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                            SyncPropValueInSyncUpdate(ref ctx, PropertyIds.DEBRIS_ENTITY_IDS, newValue);
                            var oldValue = _debrisEntityIds;
                            _debrisEntityIds = newValue;
                            _debrisEntityIds?.SetParentInfo(this, PropertyIds.DEBRIS_ENTITY_IDS);
                            TryInvokeFieldChange(13, oldValue, _debrisEntityIds);
                            return this;
                        }
                        else
                        {
                            return _debrisEntityIds.SyncUpdateFieldValue(ref seg, buffer, currentIndex + 1);
                        }
                    }
                case PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS:
                {
#if DEBUG
                    if (seg.BasicType.Type != TypeDefine.UINT) throw new Exception($"Type mismatch, expecting uint got {seg.BasicType.Type}");
#endif
                    uint newValue = seg.BasicType.UIntValue;
                    var ctx = new DiffContext() { ChangeFromServiceRole = seg.FromServiceRole, Path = seg.Path, PathLen = seg.PathLen, RootObj = seg.RootObj };
                    SyncPropValueInSyncUpdate(ref ctx, PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS, newValue);
                    var oldValue = _toolCupboardDebrisDestroyTs;
                    _toolCupboardDebrisDestroyTs = newValue;
                    TryInvokeFieldChange(14, oldValue, _toolCupboardDebrisDestroyTs);
                    return this;
                }
                default: return this;
            }
        }
        internal override bool LoadUpdateDiff(EntityDiff diff, int currentIndex, ref MessagePackReader reader)
        {
            var propertyId = diff.Path[currentIndex];
            switch (propertyId)
            {
                case PropertyIds.PART_ENTITY_ID:
                {
                    _partEntityId = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.GRID_ID:
                {
                    _gridId = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.SPAWN_TYPE:
                {
                    _spawnType = reader.ReadInt32();
                    return true;
                }
                case PropertyIds.LOC_LIST:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _locList = null;
                        }
                        else
                        {
                            _locList = new CustomTypeList<TerritoryCenterLocationInfo>(ref reader, ESerializeMode.Db);
                            _locList?.SetParentInfo(this, PropertyIds.LOC_LIST);
                        }
                    }
                    else
                    {
                        return _locList.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                case PropertyIds.CREATE_TIME_STAMP:
                {
                    _createTimeStamp = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.CREATOR_ROLE_ID:
                {
                    _creatorRoleId = reader.ReadUInt64();
                    return true;
                }
                case PropertyIds.NORMAL_PART_SET:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _normalPartSet = null;
                        }
                        else
                        {
                            _normalPartSet = new BasicTypeHashSet<long>(ref reader, ESerializeMode.Db);
                            _normalPartSet?.SetParentInfo(this, PropertyIds.NORMAL_PART_SET);
                        }
                    }
                    else
                    {
                        return _normalPartSet.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                case PropertyIds.NORMAL_PART_COUNT:
                {
                    _normalPartCount = reader.ReadInt32();
                    return true;
                }
                case PropertyIds.MOVED_TIMES:
                {
                    _movedTimes = reader.ReadInt32();
                    return true;
                }
                case PropertyIds.LAST_BATCH_UPGRADE_TS:
                {
                    _lastBatchUpgradeTs = reader.ReadInt64();
                    return true;
                }
                case PropertyIds.GAP_LINK_INFO_DIC:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _gapLinkInfoDic = null;
                        }
                        else
                        {
                            _gapLinkInfoDic = new CustomValueDictionary<long, ConstructionGapLinkInfo>(ref reader, ESerializeMode.Db);
                            _gapLinkInfoDic?.SetParentInfo(this, PropertyIds.GAP_LINK_INFO_DIC);
                        }
                    }
                    else
                    {
                        return _gapLinkInfoDic.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                case PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _partsDestroyWithToolcupBoard = null;
                        }
                        else
                        {
                            _partsDestroyWithToolcupBoard = new BasicTypeList<long>(ref reader, ESerializeMode.Db);
                            _partsDestroyWithToolcupBoard?.SetParentInfo(this, PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD);
                        }
                    }
                    else
                    {
                        return _partsDestroyWithToolcupBoard.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                case PropertyIds.DEBRIS_ENTITY_IDS:
                {
                    if (currentIndex == diff.PathLen - 1)
                    {
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _debrisEntityIds = null;
                        }
                        else
                        {
                            _debrisEntityIds = new BasicTypeHashSet<long>(ref reader, ESerializeMode.Db);
                            _debrisEntityIds?.SetParentInfo(this, PropertyIds.DEBRIS_ENTITY_IDS);
                        }
                    }
                    else
                    {
                        return _debrisEntityIds.LoadUpdateDiff(diff, currentIndex + 1, ref reader);
                    }
                    return true;
                }
                case PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS:
                {
                    _toolCupboardDebrisDestroyTs = reader.ReadUInt32();
                    return true;
                }
                default: return false;
            }
        }
        protected override void RecoverPropValueInner(int propertyId, PropValueUnion valueUnion)
        {
            switch (propertyId)
            {
                case PropertyIds.PART_ENTITY_ID:
                {
                    _partEntityId = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.GRID_ID:
                {
                    _gridId = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.SPAWN_TYPE:
                {
                    _spawnType = valueUnion.SimpleValue.IntValue;
                    break;
                }
                case PropertyIds.LOC_LIST:
                {
                    _locList = valueUnion.CustomTypeValue as CustomTypeList<TerritoryCenterLocationInfo>;
                    break;
                }
                case PropertyIds.CREATE_TIME_STAMP:
                {
                    _createTimeStamp = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.CREATOR_ROLE_ID:
                {
                    _creatorRoleId = valueUnion.SimpleValue.ULongValue;
                    break;
                }
                case PropertyIds.NORMAL_PART_SET:
                {
                    _normalPartSet = valueUnion.CustomTypeValue as BasicTypeHashSet<long>;
                    break;
                }
                case PropertyIds.NORMAL_PART_COUNT:
                {
                    _normalPartCount = valueUnion.SimpleValue.IntValue;
                    break;
                }
                case PropertyIds.MOVED_TIMES:
                {
                    _movedTimes = valueUnion.SimpleValue.IntValue;
                    break;
                }
                case PropertyIds.PART_LIMIT_INFO:
                {
                    _partLimitInfo = valueUnion.CustomTypeValue as PartLimitInfo;
                    break;
                }
                case PropertyIds.LAST_BATCH_UPGRADE_TS:
                {
                    _lastBatchUpgradeTs = valueUnion.SimpleValue.LongValue;
                    break;
                }
                case PropertyIds.GAP_LINK_INFO_DIC:
                {
                    _gapLinkInfoDic = valueUnion.CustomTypeValue as CustomValueDictionary<long, ConstructionGapLinkInfo>;
                    break;
                }
                case PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD:
                {
                    _partsDestroyWithToolcupBoard = valueUnion.CustomTypeValue as BasicTypeList<long>;
                    break;
                }
                case PropertyIds.DEBRIS_ENTITY_IDS:
                {
                    _debrisEntityIds = valueUnion.CustomTypeValue as BasicTypeHashSet<long>;
                    break;
                }
                case PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS:
                {
                    _toolCupboardDebrisDestroyTs = valueUnion.SimpleValue.UIntValue;
                    break;
                }
                default: break;
            }
        }
        public override void DeserializeCore(ref MessagePackReader reader, ESerializeMode mode)
        {
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                if (reader.NextCode == ISerializeType.EndSignal[0])
                {
                    break;
                }
                switch (properties[i] & 0xffff)
                {
                case PropertyIds.PART_ENTITY_ID:
                    _partEntityId = reader.ReadInt64();
                    break;
                case PropertyIds.GRID_ID:
                    _gridId = reader.ReadInt64();
                    break;
                case PropertyIds.SPAWN_TYPE:
                    _spawnType = reader.ReadInt32();
                    break;
                case PropertyIds.LOC_LIST:
                    {
                        if (_locList != null)
                        {
                            _locList.ClearParentInfo();
                            _locList.ResetWhenDeserialize();
                        }
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _locList = null;
                            continue;
                        }
                        _locList = new(ref reader, mode);
                        _locList.SetParentInfo(this, PropertyIds.LOC_LIST);
                    }
                    break;
                case PropertyIds.CREATE_TIME_STAMP:
                    _createTimeStamp = reader.ReadInt64();
                    break;
                case PropertyIds.CREATOR_ROLE_ID:
                    _creatorRoleId = reader.ReadUInt64();
                    break;
                case PropertyIds.NORMAL_PART_SET:
                    {
                        if (_normalPartSet != null)
                        {
                            _normalPartSet.ClearParentInfo();
                            _normalPartSet.ResetWhenDeserialize();
                        }
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _normalPartSet = null;
                            continue;
                        }
                        _normalPartSet = new(ref reader, mode);
                        _normalPartSet.SetParentInfo(this, PropertyIds.NORMAL_PART_SET);
                    }
                    break;
                case PropertyIds.NORMAL_PART_COUNT:
                    _normalPartCount = reader.ReadInt32();
                    break;
                case PropertyIds.MOVED_TIMES:
                    _movedTimes = reader.ReadInt32();
                    break;
                case PropertyIds.PART_LIMIT_INFO:
                    {
                        _partLimitInfo?.ClearParentInfo();
                        _partLimitInfo = CustomTypeHelper.DeserializeCustomTypeBase<PartLimitInfo>(ref reader, mode);
                        _partLimitInfo?.SetParentInfo(this, PropertyIds.PART_LIMIT_INFO);
                    }
                    break;
                case PropertyIds.LAST_BATCH_UPGRADE_TS:
                    _lastBatchUpgradeTs = reader.ReadInt64();
                    break;
                case PropertyIds.GAP_LINK_INFO_DIC:
                    {
                        if (_gapLinkInfoDic != null)
                        {
                            _gapLinkInfoDic.ClearParentInfo();
                            _gapLinkInfoDic.ResetWhenDeserialize();
                        }
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _gapLinkInfoDic = null;
                            continue;
                        }
                        _gapLinkInfoDic = new(ref reader, mode);
                        _gapLinkInfoDic.SetParentInfo(this, PropertyIds.GAP_LINK_INFO_DIC);
                    }
                    break;
                case PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD:
                    {
                        if (_partsDestroyWithToolcupBoard != null)
                        {
                            _partsDestroyWithToolcupBoard.ClearParentInfo();
                            _partsDestroyWithToolcupBoard.ResetWhenDeserialize();
                        }
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _partsDestroyWithToolcupBoard = null;
                            continue;
                        }
                        _partsDestroyWithToolcupBoard = new(ref reader, mode);
                        _partsDestroyWithToolcupBoard.SetParentInfo(this, PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD);
                    }
                    break;
                case PropertyIds.DEBRIS_ENTITY_IDS:
                    {
                        if (_debrisEntityIds != null)
                        {
                            _debrisEntityIds.ClearParentInfo();
                            _debrisEntityIds.ResetWhenDeserialize();
                        }
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            _debrisEntityIds = null;
                            continue;
                        }
                        _debrisEntityIds = new(ref reader, mode);
                        _debrisEntityIds.SetParentInfo(this, PropertyIds.DEBRIS_ENTITY_IDS);
                    }
                    break;
                case PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS:
                    _toolCupboardDebrisDestroyTs = reader.ReadUInt32();
                    break;
                    default: break;
                }
            }
            if (reader.NextCode == ISerializeType.EndSignal[0])
            {
                reader.ReadRaw(ISerializeType.END_SIGNAL_LENGTH);
            }
        }
        public override void SerializeCore(ref MessagePackWriter writer, ESerializeMode mode, int templateHashValue)
        {
            if (templateHashValue == CLASS_HASH) writer.Write(0);
            else writer.Write(CLASS_HASH);
            var properties = GetPropertyListFromMode(mode);
            for (var i = 0; i < properties.Length; i++)
            {
                switch (properties[i] & 0xffff)
                {
                    case PropertyIds.PART_ENTITY_ID:
                        writer.Write(_partEntityId);
                        break;
                    case PropertyIds.GRID_ID:
                        writer.Write(_gridId);
                        break;
                    case PropertyIds.SPAWN_TYPE:
                        writer.Write(_spawnType);
                        break;
                    case PropertyIds.LOC_LIST:
                        if (_locList == null)
                            writer.WriteNil();
                        else
                            _locList.SerializeCore(ref writer, mode, 254460052);
                        break;
                    case PropertyIds.CREATE_TIME_STAMP:
                        writer.Write(_createTimeStamp);
                        break;
                    case PropertyIds.CREATOR_ROLE_ID:
                        writer.Write(_creatorRoleId);
                        break;
                    case PropertyIds.NORMAL_PART_SET:
                        if (_normalPartSet == null)
                            writer.WriteNil();
                        else
                            _normalPartSet.SerializeCore(ref writer, mode, -1);
                        break;
                    case PropertyIds.NORMAL_PART_COUNT:
                        writer.Write(_normalPartCount);
                        break;
                    case PropertyIds.MOVED_TIMES:
                        writer.Write(_movedTimes);
                        break;
                    case PropertyIds.PART_LIMIT_INFO:
                        if (_partLimitInfo == null)
                            writer.WriteNil();
                        else
                            _partLimitInfo.SerializeCore(ref writer, mode, 2140723870);
                        break;
                    case PropertyIds.LAST_BATCH_UPGRADE_TS:
                        writer.Write(_lastBatchUpgradeTs);
                        break;
                    case PropertyIds.GAP_LINK_INFO_DIC:
                        if (_gapLinkInfoDic == null)
                            writer.WriteNil();
                        else
                            _gapLinkInfoDic.SerializeCore(ref writer, mode, 223016299);
                        break;
                    case PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD:
                        if (_partsDestroyWithToolcupBoard == null)
                            writer.WriteNil();
                        else
                            _partsDestroyWithToolcupBoard.SerializeCore(ref writer, mode, -1);
                        break;
                    case PropertyIds.DEBRIS_ENTITY_IDS:
                        if (_debrisEntityIds == null)
                            writer.WriteNil();
                        else
                            _debrisEntityIds.SerializeCore(ref writer, mode, -1);
                        break;
                    case PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS:
                        writer.Write(_toolCupboardDebrisDestroyTs);
                        break;
                    default: break;
                }
            }
            writer.WriteRaw(ISerializeType.EndSignal);
        }
        public override JSONNode ToEJson()
        {
            var jsonObj = new JSONObject();
            jsonObj["Hash"] = CLASS_HASH;
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                switch (propertyId)
                {
                    case PropertyIds.PART_ENTITY_ID:
                        jsonObj["PartEntityId"] = PartEntityId;
                        break;
                    case PropertyIds.GRID_ID:
                        jsonObj["GridId"] = GridId;
                        break;
                    case PropertyIds.SPAWN_TYPE:
                        jsonObj["SpawnType"] = SpawnType;
                        break;
                    case PropertyIds.LOC_LIST:
                        if (LocList != null) jsonObj["LocList"] = LocList.ToEJson();
                        break;
                    case PropertyIds.CREATE_TIME_STAMP:
                        jsonObj["CreateTimeStamp"] = CreateTimeStamp;
                        break;
                    case PropertyIds.CREATOR_ROLE_ID:
                        jsonObj["CreatorRoleId"] = CreatorRoleId;
                        break;
                    case PropertyIds.NORMAL_PART_SET:
                        if (NormalPartSet != null) jsonObj["NormalPartSet"] = NormalPartSet.ToEJson();
                        break;
                    case PropertyIds.NORMAL_PART_COUNT:
                        jsonObj["NormalPartCount"] = NormalPartCount;
                        break;
                    case PropertyIds.MOVED_TIMES:
                        jsonObj["MovedTimes"] = MovedTimes;
                        break;
                    case PropertyIds.LAST_BATCH_UPGRADE_TS:
                        jsonObj["LastBatchUpgradeTs"] = LastBatchUpgradeTs;
                        break;
                    case PropertyIds.GAP_LINK_INFO_DIC:
                        if (GapLinkInfoDic != null) jsonObj["GapLinkInfoDic"] = GapLinkInfoDic.ToEJson();
                        break;
                    case PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD:
                        if (PartsDestroyWithToolcupBoard != null) jsonObj["PartsDestroyWithToolcupBoard"] = PartsDestroyWithToolcupBoard.ToEJson();
                        break;
                    case PropertyIds.DEBRIS_ENTITY_IDS:
                        if (DebrisEntityIds != null) jsonObj["DebrisEntityIds"] = DebrisEntityIds.ToEJson();
                        break;
                    case PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS:
                        jsonObj["ToolCupboardDebrisDestroyTs"] = ToolCupboardDebrisDestroyTs;
                        break;
                    default: break;
                }
            }
            return jsonObj;
        }
        public override void FromEJson(JSONNode json)
        {
            foreach (var propertyId in PersistentPropertyIdArray)
            {
                UpdateFromJson(json, propertyId);
            }
        }
        public override void UpdateFromJson(JSONNode json, int propertyId)
        {
            if (propertyId < 0 || propertyId > PropNameArray.Length)
                return;
            var key = PropNameArray[propertyId];
            if (json.HasKey(key))
            {
                switch (propertyId)
                {
                    case PropertyIds.PART_ENTITY_ID:
                        if (json.HasKey("PartEntityId"))
                        {
                            if(json["PartEntityId"].Tag == JSONNodeType.String && json["PartEntityId"].Value.StartsWith("NumberLong"))
                                _partEntityId = long.Parse(json["PartEntityId"].Value.Substring(11, json["PartEntityId"].Value.Length - 12));
                            else
                                _partEntityId = json["PartEntityId"].AsLong;
                        }
                        break;
                    case PropertyIds.GRID_ID:
                        if (json.HasKey("GridId"))
                        {
                            if(json["GridId"].Tag == JSONNodeType.String && json["GridId"].Value.StartsWith("NumberLong"))
                                _gridId = long.Parse(json["GridId"].Value.Substring(11, json["GridId"].Value.Length - 12));
                            else
                                _gridId = json["GridId"].AsLong;
                        }
                        break;
                    case PropertyIds.SPAWN_TYPE:
                        if (json.HasKey("SpawnType"))
                        {
                            _spawnType = json["SpawnType"].AsInt;
                        }
                        break;
                    case PropertyIds.LOC_LIST:
                    {
                        if (_locList != null)
                        {
                            _locList.ClearParentInfo();
                            _locList.ResetWhenDeserialize();
                        }
                        if (json.HasKey("LocList"))
                        {
                            _locList = new(json["LocList"]);
                            _locList.SetParentInfo(this, PropertyIds.LOC_LIST);
                        }
                    }
                    break;
                    case PropertyIds.CREATE_TIME_STAMP:
                        if (json.HasKey("CreateTimeStamp"))
                        {
                            if(json["CreateTimeStamp"].Tag == JSONNodeType.String && json["CreateTimeStamp"].Value.StartsWith("NumberLong"))
                                _createTimeStamp = long.Parse(json["CreateTimeStamp"].Value.Substring(11, json["CreateTimeStamp"].Value.Length - 12));
                            else
                                _createTimeStamp = json["CreateTimeStamp"].AsLong;
                        }
                        break;
                    case PropertyIds.CREATOR_ROLE_ID:
                        if (json.HasKey("CreatorRoleId"))
                        {
                            _creatorRoleId = json["CreatorRoleId"].AsULong;
                        }
                        break;
                    case PropertyIds.NORMAL_PART_SET:
                    {
                        if (_normalPartSet != null)
                        {
                            _normalPartSet.ClearParentInfo();
                            _normalPartSet.ResetWhenDeserialize();
                        }
                        if (json.HasKey("NormalPartSet"))
                        {
                            _normalPartSet = new(json["NormalPartSet"]);
                            _normalPartSet.SetParentInfo(this, PropertyIds.NORMAL_PART_SET);
                        }
                    }
                    break;
                    case PropertyIds.NORMAL_PART_COUNT:
                        if (json.HasKey("NormalPartCount"))
                        {
                            _normalPartCount = json["NormalPartCount"].AsInt;
                        }
                        break;
                    case PropertyIds.MOVED_TIMES:
                        if (json.HasKey("MovedTimes"))
                        {
                            _movedTimes = json["MovedTimes"].AsInt;
                        }
                        break;
                    case PropertyIds.LAST_BATCH_UPGRADE_TS:
                        if (json.HasKey("LastBatchUpgradeTs"))
                        {
                            if(json["LastBatchUpgradeTs"].Tag == JSONNodeType.String && json["LastBatchUpgradeTs"].Value.StartsWith("NumberLong"))
                                _lastBatchUpgradeTs = long.Parse(json["LastBatchUpgradeTs"].Value.Substring(11, json["LastBatchUpgradeTs"].Value.Length - 12));
                            else
                                _lastBatchUpgradeTs = json["LastBatchUpgradeTs"].AsLong;
                        }
                        break;
                    case PropertyIds.GAP_LINK_INFO_DIC:
                    {
                        if (_gapLinkInfoDic != null)
                        {
                            _gapLinkInfoDic.ClearParentInfo();
                            _gapLinkInfoDic.ResetWhenDeserialize();
                        }
                        if (json.HasKey("GapLinkInfoDic"))
                        {
                            _gapLinkInfoDic = new(json["GapLinkInfoDic"]);
                            _gapLinkInfoDic.SetParentInfo(this, PropertyIds.GAP_LINK_INFO_DIC);
                        }
                    }
                    break;
                    case PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD:
                    {
                        if (_partsDestroyWithToolcupBoard != null)
                        {
                            _partsDestroyWithToolcupBoard.ClearParentInfo();
                            _partsDestroyWithToolcupBoard.ResetWhenDeserialize();
                        }
                        if (json.HasKey("PartsDestroyWithToolcupBoard"))
                        {
                            _partsDestroyWithToolcupBoard = new(json["PartsDestroyWithToolcupBoard"]);
                            _partsDestroyWithToolcupBoard.SetParentInfo(this, PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD);
                        }
                    }
                    break;
                    case PropertyIds.DEBRIS_ENTITY_IDS:
                    {
                        if (_debrisEntityIds != null)
                        {
                            _debrisEntityIds.ClearParentInfo();
                            _debrisEntityIds.ResetWhenDeserialize();
                        }
                        if (json.HasKey("DebrisEntityIds"))
                        {
                            _debrisEntityIds = new(json["DebrisEntityIds"]);
                            _debrisEntityIds.SetParentInfo(this, PropertyIds.DEBRIS_ENTITY_IDS);
                        }
                    }
                    break;
                    case PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS:
                        if (json.HasKey("ToolCupboardDebrisDestroyTs"))
                        {
                            _toolCupboardDebrisDestroyTs = json["ToolCupboardDebrisDestroyTs"].AsUInt;
                        }
                        break;
                    default: break;
                }
            }
        }
        public override string DiffToString(ref MessagePackReader reader, long?[] path, int pathLen, System.Text.StringBuilder pathString, int depth)
        {
            var index = (int)CustomTypeHelper.GetCurrentIndex(depth, path);
            switch (index)
            {
                case PropertyIds.PART_ENTITY_ID:
                    return $"{pathString}.PartEntityId Set to {reader.ReadInt64()}";
                case PropertyIds.GRID_ID:
                    return $"{pathString}.GridId Set to {reader.ReadInt64()}";
                case PropertyIds.SPAWN_TYPE:
                    return $"{pathString}.SpawnType Set to {reader.ReadInt32()}";
                case PropertyIds.LOC_LIST:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = new CustomTypeList<TerritoryCenterLocationInfo>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.LocList Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".LocList");
                        return LocList.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                case PropertyIds.CREATE_TIME_STAMP:
                    return $"{pathString}.CreateTimeStamp Set to {reader.ReadInt64()}";
                case PropertyIds.CREATOR_ROLE_ID:
                    return $"{pathString}.CreatorRoleId Set to {reader.ReadUInt64()}";
                case PropertyIds.NORMAL_PART_SET:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = new BasicTypeHashSet<long>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.NormalPartSet Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".NormalPartSet");
                        return NormalPartSet.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                case PropertyIds.NORMAL_PART_COUNT:
                    return $"{pathString}.NormalPartCount Set to {reader.ReadInt32()}";
                case PropertyIds.MOVED_TIMES:
                    return $"{pathString}.MovedTimes Set to {reader.ReadInt32()}";
                case PropertyIds.PART_LIMIT_INFO:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = CustomTypeHelper.DeserializeCustomTypeBase<PartLimitInfo>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.PartLimitInfo Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".PartLimitInfo");
                        return PartLimitInfo.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                case PropertyIds.LAST_BATCH_UPGRADE_TS:
                    return $"{pathString}.LastBatchUpgradeTs Set to {reader.ReadInt64()}";
                case PropertyIds.GAP_LINK_INFO_DIC:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = new CustomValueDictionary<long, ConstructionGapLinkInfo>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.GapLinkInfoDic Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".GapLinkInfoDic");
                        return GapLinkInfoDic.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                case PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = new BasicTypeList<long>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.PartsDestroyWithToolcupBoard Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".PartsDestroyWithToolcupBoard");
                        return PartsDestroyWithToolcupBoard.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                case PropertyIds.DEBRIS_ENTITY_IDS:
                    if (depth == pathLen - 1)
                    {
                        string result;
                        if (reader.IsNil)
                        {
                            reader.ReadNil();
                            result = "null";
                        }
                        else
                        {
                            var temp = new BasicTypeHashSet<long>(ref reader, ESerializeMode.All);
                            result = temp.ToPrettyString();
                        }
                        return $"{pathString}.DebrisEntityIds Set to {result}";
                    }
                    else
                    {
                        pathString.Append(".DebrisEntityIds");
                        return DebrisEntityIds.DiffToString(ref reader, path, pathLen, pathString, depth + 1);
                    }
                case PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS:
                    return $"{pathString}.ToolCupboardDebrisDestroyTs Set to {reader.ReadUInt32()}";
                default: break;
            }
            return "DiffToString failed!";
        }
        public override object ToDetailedDict()
        {
            var output = new Dictionary<string, object>();
            output.Add("PartEntityId", DebugDetailHelper.ToDetailObject(this.PartEntityId));
            output.Add("GridId", DebugDetailHelper.ToDetailObject(this.GridId));
            output.Add("SpawnType", DebugDetailHelper.ToDetailObject(this.SpawnType));
            output.Add("LocList", DebugDetailHelper.ToDetailObject(this.LocList));
            output.Add("CreateTimeStamp", DebugDetailHelper.ToDetailObject(this.CreateTimeStamp));
            output.Add("CreatorRoleId", DebugDetailHelper.ToDetailObject(this.CreatorRoleId));
            output.Add("NormalPartSet", DebugDetailHelper.ToDetailObject(this.NormalPartSet));
            output.Add("NormalPartCount", DebugDetailHelper.ToDetailObject(this.NormalPartCount));
            output.Add("MovedTimes", DebugDetailHelper.ToDetailObject(this.MovedTimes));
            output.Add("PartLimitInfo", DebugDetailHelper.ToDetailObject(this.PartLimitInfo));
            output.Add("LastBatchUpgradeTs", DebugDetailHelper.ToDetailObject(this.LastBatchUpgradeTs));
            output.Add("GapLinkInfoDic", DebugDetailHelper.ToDetailObject(this.GapLinkInfoDic));
            output.Add("PartsDestroyWithToolcupBoard", DebugDetailHelper.ToDetailObject(this.PartsDestroyWithToolcupBoard));
            output.Add("DebrisEntityIds", DebugDetailHelper.ToDetailObject(this.DebrisEntityIds));
            output.Add("ToolCupboardDebrisDestroyTs", DebugDetailHelper.ToDetailObject(this.ToolCupboardDebrisDestroyTs));
            return output;
        }
        public override void TestGetPropertyInfo(long propertyId, out string propertyName, out string propertyValue, out TypeBase propertyBase, out bool propertyValueIsDefault)
        {
            propertyBase = null;
            propertyName = null;
            propertyValue = null;
            propertyValueIsDefault = false;
            switch (propertyId)
            {
                case PropertyIds.PART_ENTITY_ID:
                    propertyName = "PartEntityId";
                    propertyValue = _partEntityId.ToString();
                    propertyValueIsDefault = _partEntityId == default;
                    break;
                case PropertyIds.GRID_ID:
                    propertyName = "GridId";
                    propertyValue = _gridId.ToString();
                    propertyValueIsDefault = _gridId == default;
                    break;
                case PropertyIds.SPAWN_TYPE:
                    propertyName = "SpawnType";
                    propertyValue = _spawnType.ToString();
                    propertyValueIsDefault = _spawnType == default;
                    break;
                case PropertyIds.LOC_LIST:
                    propertyName = "LocList";
                    propertyBase = _locList;
                    break;
                case PropertyIds.CREATE_TIME_STAMP:
                    propertyName = "CreateTimeStamp";
                    propertyValue = _createTimeStamp.ToString();
                    propertyValueIsDefault = _createTimeStamp == default;
                    break;
                case PropertyIds.CREATOR_ROLE_ID:
                    propertyName = "CreatorRoleId";
                    propertyValue = _creatorRoleId.ToString();
                    propertyValueIsDefault = _creatorRoleId == default;
                    break;
                case PropertyIds.NORMAL_PART_SET:
                    propertyName = "NormalPartSet";
                    propertyBase = _normalPartSet;
                    break;
                case PropertyIds.NORMAL_PART_COUNT:
                    propertyName = "NormalPartCount";
                    propertyValue = _normalPartCount.ToString();
                    propertyValueIsDefault = _normalPartCount == default;
                    break;
                case PropertyIds.MOVED_TIMES:
                    propertyName = "MovedTimes";
                    propertyValue = _movedTimes.ToString();
                    propertyValueIsDefault = _movedTimes == default;
                    break;
                case PropertyIds.PART_LIMIT_INFO:
                    propertyName = "PartLimitInfo";
                    propertyBase = _partLimitInfo;
                    break;
                case PropertyIds.LAST_BATCH_UPGRADE_TS:
                    propertyName = "LastBatchUpgradeTs";
                    propertyValue = _lastBatchUpgradeTs.ToString();
                    propertyValueIsDefault = _lastBatchUpgradeTs == default;
                    break;
                case PropertyIds.GAP_LINK_INFO_DIC:
                    propertyName = "GapLinkInfoDic";
                    propertyBase = _gapLinkInfoDic;
                    break;
                case PropertyIds.PARTS_DESTROY_WITH_TOOLCUP_BOARD:
                    propertyName = "PartsDestroyWithToolcupBoard";
                    propertyBase = _partsDestroyWithToolcupBoard;
                    break;
                case PropertyIds.DEBRIS_ENTITY_IDS:
                    propertyName = "DebrisEntityIds";
                    propertyBase = _debrisEntityIds;
                    break;
                case PropertyIds.TOOL_CUPBOARD_DEBRIS_DESTROY_TS:
                    propertyName = "ToolCupboardDebrisDestroyTs";
                    propertyValue = _toolCupboardDebrisDestroyTs.ToString();
                    propertyValueIsDefault = _toolCupboardDebrisDestroyTs == default;
                    break;
                default: break;
            }
        }
    }
}