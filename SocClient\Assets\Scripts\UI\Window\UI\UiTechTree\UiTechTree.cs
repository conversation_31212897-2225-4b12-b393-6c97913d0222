using Construction.Util;
using System.Collections.Generic;
using Cysharp.Text;
using FairyGUI;
using UnityEngine;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.Const;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.Data.DataItem;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Play;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Technology;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.SocClient.Control;
using WizardGames.Soc.SocClient.Ui.Utils;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.SocClient.Ui
{
    public partial class UiTechTree : WindowComBase
    {
        private static SocLogger logger { get; } = LogHelper.GetLogger("Technology");

        private ComTopBar topBar;
        private ComCommonNavBar navBar;


        /// <summary>
        /// 当前打开的工作台等级（科技二级页签）
        /// </summary>
        private int curLevel = 0;

        /// <summary>
        /// 科技树结点信息面板
        /// </summary>
        public GTechTreeInfo TechTreeInfo { get; private set; }

        /// <summary>
        /// 科技树打开方式
        /// </summary>
        public EUiTechTreeOpenSource Source { get; private set; } = EUiTechTreeOpenSource.None;

        /// <summary>
        /// 正在操作的工作台Id
        /// </summary>
        public long OperatingWorkbenchId { get; private set; } = 0;

        /// <summary>
        /// 正在操作的工作台等级
        /// </summary>
        public static long OperatingWorkbenchLevel { get; private set; } = 0;

        /// <summary>
        /// 正在操作的领地柜中枢Id
        /// </summary>
        public long OperatingHubId { get; private set; } = 0;

        /// <summary>
        /// 从别的地方跳转过来的蓝图id，需要选中包含这个id的node
        /// </summary>
        public static long SelectBluePrintId { get; private set; } = 0;

        //---2024.10.27需求迭代后新增的变量
        /// <summary>
        /// 系统_科技系别表数据
        /// </summary>
        private static List<TechnologySystem> dataList;

        /// <summary>
        /// 系统_科技等级表数据
        /// </summary>
        private static List<TechnologyLevel> levelList;

        /// <summary>
        /// 缓存一个当前废料的数量，给Node用
        /// </summary>
        public static int curConsumeScrapCount = 0;

        /// <summary>
        /// 导航栏页签数据
        /// </summary>
        private List<NavBarData> tabDataLst;

        /// <summary>
        /// 科技树节点list
        /// </summary>
        private GList treeNodeGList;

        /// <summary>
        /// 当前系别科技树配置节点数据
        /// </summary>
        private Dictionary<int, List<Common.Data.DataItem.Technology>> techNodeDataDic = new();

        //记录当前需要调整渲染层级的nodeid
        private Dictionary<long, ESelectStatus> needSetOrderIdDic = new();

        /// <summary>
        /// 记录下当前系别每个一级页签的二级页签NodeTree带标题的那个index，用在滑动停止时计算是否需要自动切换二级页签
        /// </summary>
        private List<int> recodListHeadIndexList = new();

        /// <summary>
        /// 当前系别科技树每个节点数据
        /// </summary>
        private Dictionary<long, GTechTreeNode> treeNodes = new();

        /// <summary>
        /// 当前选中的科技树节点
        /// </summary>
        private static long curSelectTreeNodeId = 0;
        
        /// <summary>
        /// 记录一下当前选中的一级页签id
        /// </summary>
        private int curFirstTabId = -1;
        //计时器id
        private long secTimer = -1;
        
        //是否需要选中第一个未解锁的节点
        private bool isSelectFirstUnlocked = true;
        
        private bool isUseRecordSelectNodeId = false;


        private GButton btnPre;
        private GButton btnNext;
        private Controller btnPreCtrl;
        private Controller btnNextCtrl;
        
        private UiTechtreeFindNode uiTechtreeFindNode;
        
        private Controller listTypeCtrl;
        
        private readonly int maxRow = 4;

        private GComponent content;
        protected override void OnInit()
        {
            base.OnInit();
            content = ComRoot.GetChild("content").asCom;
            listTypeCtrl = content.GetController("listType");

            uiTechtreeFindNode ??= new UiTechtreeFindNode();
            uiTechtreeFindNode.Init(content);

            TechTreeInfo = content.GetChild("comTechTreeInfoPanel") as GTechTreeInfo;

            topBar = ComRoot.GetChild("topBar") as ComTopBar;
            topBar.Title = LanguageManager.GetTextConst(LanguageConst.TechTree);
            topBar.SetBackButtonClicked(OnEscClose);
            topBar.AddBottomDataList(new()
            {
                new()
                {
                    Id = 1001,
                    Action = (_) => { btnPre.onClick.Call(); },
                },
                new()
                {
                    Id = 1002,
                    Action = (_) => { btnNext.onClick.Call(); },
                },
            });
            topBar.SetCurrency(new()
            {
                new()
                {
                    Icon = FGUIResPathDef.REPUTATION_ICON_08,
                    TextFunc = () => ZString.Format(LanguageManager.Instance.GetText(LanguageConst.Level),
                        Mc.MyPlayer.MyEntityServer
                            .GetComponent<ReputationComponent>(EComponentIdEnum.ReputationComponentId)
                            ?.ReputationLevel),
                    VisibleFunc = () => PlayHelper.IsReputationPlay,
                    OnShowTips = () =>
                    {
                        var obj = topBar.GetChild("coinList").asCom.GetChildAt(0);
                        Vector2 pos = obj.LocalToGlobal(new Vector3(0, obj.height*0.75f));
                        var tips = ZString.Format(LanguageManager.Instance.GetText(LanguageConst.ReputationLevel), 
                            Mc.MyPlayer.MyEntityServer
                            .GetComponent<ReputationComponent>(EComponentIdEnum.ReputationComponentId)
                            ?.ReputationLevel);
                        UiDescTips.Show(tips, pos, false, true, useHover:true);
                    },
                    OnHideTips = () =>
                    {
                        UiDescTips.Hide();
                    }
                },
                new()
                {
                    Icon = ZString.Concat(FGUIResPathDef.ASSET_ITEM_ICON_PREFIX, MgrTechnology.ScrapItemId),
                    TextFunc = () => Mc.CollectionItem.GetAmount(MgrTechnology.ScrapItemId).ToString(),
                    OnShowTips = () =>
                    {
                        var obj = topBar.GetChild("coinList").asCom.GetChildAt(1);
                        Vector2 pos = obj.LocalToGlobal(new Vector3(0, obj.height*0.75f));
                        var tips = ZString.Format("拥有废料数量{0}", Mc.CollectionItem.GetAmount(MgrTechnology.ScrapItemId));
                        UiDescTips.Show(tips, pos, false, true, useHover:true);
                    },
                    OnHideTips = () =>
                    {
                        UiDescTips.Hide();
                    },
#if POCO
                    PocoKey = "TechTreeCostItemCountText",
#endif
                }
            });

            navBar = new();
            navBar.Init(ComRoot.GetChild("navBar").asCom, true);
            navBar.AlwaysUnfold = false;
            navBar.SetRedCustomUrl(FGUIResPathDef.RED_DOT_WARNING);
            navBar.SetRedType(ENavTabRedDotType.custom);
            navBar.LinkTopBar(topBar, true);
            navBar.SetVirtual();

            treeNodeGList = content.GetChild("treeNodeList").asList;
            treeNodeGList.SetVirtual();
            treeNodeGList.itemRenderer = RenderTreeNodeItem;
            treeNodeGList.scrollPane.onScrollEnd.Remove(OnTreeNodelistScrollEnd);
            treeNodeGList.scrollPane.onScrollEnd.Add(OnTreeNodelistScrollEnd);

            btnPre = content.GetChild("btnPre").asButton;
            btnPre.onClick.Set(OnMovePreNode);
            btnPreCtrl = btnPre.GetController("status");
            btnNext = content.GetChild("btnNext").asButton;
            btnNext.onClick.Set(OnMoveNextNode);
            btnNextCtrl = btnNext.GetController("status");

            onClick.Set(UiItemTips.HideTips);

            Mc.Tables.TbTechnologySystem.RegisterDataUpdateEvent(ClearTechCache);

            InitNavTabNew();

        }


        #region 生命周期函数

        protected override void OnEnable()
        {
            base.OnEnable();
            topBar.OnEnable();
            TechTreeInfo.OnEnable();

            Mc.Msg.AddListener(EventDefine.EnterDyingState, Close);
            Mc.Msg.AddListener(EventDefine.ReputationLevelChange, SetReputationLevel);
            Mc.Msg.AddListener(EventDefine.UTechTreeNodeStateUpdate, UnLockTechNodeAck);

            SetReputationLevel();
        }



        protected override void OnDisable()
        {
            base.OnDisable();
            topBar.OnDisable();
            TechTreeInfo.OnDisable();
            //每次关闭界面需要重置的属性
            OperatingHubId = 0;
            Source = EUiTechTreeOpenSource.None;

            Mc.Msg.RemoveListener(EventDefine.EnterDyingState, Close);
            Mc.Msg.RemoveListener(EventDefine.ReputationLevelChange, SetReputationLevel);
            Mc.Msg.RemoveListener(EventDefine.UTechTreeNodeStateUpdate, UnLockTechNodeAck);

        }

        public override void OnDestroy()
        {
            base.OnDestroy();
            if (treeNodeGList != null && treeNodeGList.scrollPane != null)
            {
                treeNodeGList.scrollPane.onScrollEnd.Remove(OnTreeNodelistScrollEnd);
            }

            //界面销毁时需要重置的属性
            curLevel = 0;
            SelectBluePrintId = 0;
            Mc.Tables.TbTechnologySystem.UnregisterDataUpdateEvent(ClearTechCache);
            if (secTimer != -1)
            {
                Mc.TimerWheel.CancelTimer(secTimer);
                secTimer = -1;
            }
        }

        #endregion

        public int GetClosestIndexToCenter(GList list)
        {
            // 获取当前列表的可见区域
            float centerY = list.y + list.height / 2;

            int closestIndex = -1;
            float closestDistance = float.MaxValue;

            var passPosY = GRoot.inst.height / 2;
            // 遍历当前可见的列表项
            for (int i = 0; i < list.numChildren; i++)
            {
                // 获取每个项的显示对象
                GObject item = list.GetChildAt(i);
        
                // 计算每个项的中心 Y 坐标
                var itemCenterY = GRoot.inst.GlobalToLocal(item.LocalToGlobal(Vector2.zero));

                // 计算距离
                float distance = Mathf.Abs(itemCenterY.y - passPosY);

                // 更新最接近的索引
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closestIndex = i;
                }
            }

            return list.ChildIndexToItemIndex(closestIndex);
        }

        /// <summary>
        /// 科技树虚拟列表滑动停止事件
        /// </summary>
        public void OnTreeNodelistScrollEnd(EventContext context)
        {
            var firstTabId = navBar.GetCurSelectFirstTabId();
            if (firstTabId == dataList[0].Id ||recodListHeadIndexList.Count == 0) return;
            var centerIndex = GetClosestIndexToCenter(treeNodeGList);
            var secIndex = recodListHeadIndexList[1];
            var threeIndex = recodListHeadIndexList[2];
            if (centerIndex < secIndex && centerIndex >= 0) //滑动到第一个页签
            {
                var secTabId = navBar.GetCurSelectSecTabId();
                var secTransIndex = GetSecTabIndexByTabId(firstTabId, secTabId);
                if (secTransIndex != levelList[0].Id)//切换到等级一的页签
                {
                    int nextSecTabId = GetSecTabIdByIndex(firstTabId, levelList[0].Id);
                    navBar.SetSelectSubTab(firstTabId, nextSecTabId);
                }
            }
            else if (centerIndex < threeIndex && centerIndex >= secIndex)//滑动到第二个页签
            {
                var secTabId = navBar.GetCurSelectSecTabId();
                var secTransIndex = GetSecTabIndexByTabId(firstTabId, secTabId);
                if (secTransIndex != levelList[1].Id ) //切换到等级二的页签
                {
                    int nextSecTabId = GetSecTabIdByIndex(firstTabId, levelList[1].Id);
                    navBar.SetSelectSubTab(firstTabId, nextSecTabId);
                }
            }
            else if (centerIndex >= threeIndex) //滑动到第三个页签
            {
                var secTabId = navBar.GetCurSelectSecTabId();
                var secTransIndex = GetSecTabIndexByTabId(firstTabId, secTabId);
                if (secTransIndex != levelList[2].Id)//切换到等级三的页签
                {
                    int nextSecTabId = GetSecTabIdByIndex(firstTabId, levelList[2].Id);
                    navBar.SetSelectSubTab(firstTabId, nextSecTabId);
                }
            }
        }
        

        /// <summary>
        /// 通过firstTabId和secTabId转换二级页签的index
        /// </summary>
        /// <param name="firstTabId"></param>
        /// <param name="secTabId"></param>
        /// <returns></returns>
        public int GetSecTabIndexByTabId(int firstTabId, int secTabId)
        {
            return secTabId - firstTabId * 1000 - 1000;
        }

        /// <summary>
        /// 通过一级页签和二级页签的index设定二级页签的tabId，目的是为了避免一级页签id和二级页签id重复
        /// </summary>
        /// <param name="firstTabId"></param>
        /// <param name="secIndex"></param>
        /// <returns></returns>
        public int GetSecTabIdByIndex(int firstTabId, int secIndex)
        {
            return firstTabId * 1000 + secIndex + 1000;
        }

        
        public void RenderTreeNodeItem(int index, GObject obj)
        {
            var item = obj.asCom;
            if (item == null) return;
            if (!techNodeDataDic.TryGetValue(index, out var techList))
            {
                return;
            }
            //每一行节点的ui
            var titleCtrl = item.GetController("titleCtrl");
            var icon = item.GetChild("icon").asLoader;
            var title = item.GetChild("title").asLabel;
            var redTxt = item.GetChild("redTxt").asRichTextField;
            redTxt.visible = false;
            var firstNode = techList[0];
            var isShowTitle = firstNode.IsTitle && firstNode.GroupId != 0;
            titleCtrl.SetSelectedIndex(isShowTitle ? 0 : 1);
            if (isShowTitle)
            {
                var config = Mc.Tables.TbTechnologyLevel.GetOrDefault(firstNode.GroupId);
                icon.url = config.CraftIcon;
                title.text = config.Name;
                redTxt.visible = firstNode.GroupId > OperatingWorkbenchLevel;
                var txt = LanguageManager.GetTextConst(LanguageConst.TechTreeTips1);
                if (firstNode.GroupId == 2)
                {
                    txt = LanguageManager.GetTextConst(LanguageConst.TechTreeTips2);
                }
                else if (firstNode.GroupId == 3)
                {
                    txt = LanguageManager.GetTextConst(LanguageConst.TechTreeTips3);
                }

                redTxt.text = txt;
            }
#if POCO
            obj.PocoRegister(ZString.Format("TechTreeNodeGroupIndex{0}", index));
#endif
            for (int i = 0; i < maxRow; i++)
            {
                var nodeObj = item.GetChild(ZString.Concat("node", (i + 1).ToString())) as GTechTreeNode ;
                if(nodeObj == null) continue;
                nodeObj.visible = false;
                for (int j = 0; j < techList.Count; j++)
                {
                    var nodeConfig = techList[j];
                    var gPosY = nodeConfig.GPos[1];
                    if (gPosY != i)
                    {
                        continue;
                    }
                    nodeObj.visible = true;
                    nodeObj.Initialize(nodeConfig);
                
                    if(treeNodes.TryGetValue(nodeConfig.Id, out var _))
                    {
                        treeNodes[nodeConfig.Id] = nodeObj;
                    }
                    else
                    {
                        treeNodes.Add(nodeConfig.Id, nodeObj);
                    }
                    if(needSetOrderIdDic.TryGetValue(nodeConfig.Id, out var status) )
                    {
                        nodeObj.SetSelectStatusAndOrder(status);
                    }

#if POCO
                    nodeObj.PocoRegister(ZString.Format("TechTreeNode{0}_{1},TechTreeNode{2}", index, i, nodeConfig.Id));
#endif
                    nodeObj.onClick.Set(() =>
                    {
                        if (curSelectTreeNodeId != nodeConfig.Id)
                        {
                            OnRefreshSelectNode(nodeConfig.Id);
                            Mc.Audio.PlayAudioEvent(null, Mc.Tables.TbUiAudio.UniversalButtonClick);
                        }
                    });
                }
                
            }

        }

        /// <summary>
        /// 跳转选中
        /// </summary>
        /// <param name="id"></param>
        /// <param name="system"></param>
        /// <param name="groupId"></param>
        /// <param name="posX"></param>
        /// <param name="posY"></param>
        public void OnJumpToTreeNode(long id, int system, int groupId, bool isJumpImediately = false)
        {
            if (curSelectTreeNodeId != id || isJumpImediately)
            {
                //navBar.IsCallbackEveryClick = true;
                if (system == 0)
                {
                    navBar.ScrollAndSelect(system);
                }
                else
                {
                    var secIndex = GetSecTabIdByIndex(system, groupId);
                    navBar.ScrollAndSelect(system,secIndex);
                }
                
            }
        }

        /// <summary>
        /// 点击某个科技节点回调
        /// </summary>
        /// <param name="treeNodeId"></param>
        /// /// <param name="isJump">绕过相等的比较，用在选中相同的节点时数据刷新验证</param>
        public void OnClickNode(long treeNodeId, bool isJump = false)
        {
            if (treeNodes.TryGetValue(treeNodeId, out var node))
            {
                if (curSelectTreeNodeId != treeNodeId || isJump)
                {
                    if (curSelectTreeNodeId != 0 && treeNodes.TryGetValue(curSelectTreeNodeId, out var curNode))
                    {
                        curNode.ResetNodeSelectStatus();
                    }

                    curSelectTreeNodeId = treeNodeId;
                    if (treeNodeId == node.Config.Id)
                    {
                        node.SetSelectStatusAndOrder(ESelectStatus.Selected);
                    }
                    RefreshBtnPreAndNextStatus();
                    
                    var parentId = node.ParentID;
                    SetParentNodeSelectStatus(parentId,ESelectStatus.SelectedByTheWay);
                }
                TechTreeInfo.OpenInfoPanel(node);
            }
        }
        
        public void SetParentNodeSelectStatus(long nodeId, ESelectStatus status, int maxDepth = 100)
        {
            int depth = 0;
            while (nodeId != -1 && depth < maxDepth)
            {
                if (treeNodes.TryGetValue(nodeId, out var paTreeNode))
                {
                    if (nodeId != paTreeNode.Config.Id)
                    {
                        break;
                    }
                    paTreeNode.SetSelectStatusAndOrder(status);
                }

                if (paTreeNode == null) break;
                nodeId = paTreeNode.ParentID; // 更新 nodeId 为父节点的 ID
                depth++; // 增加深度计数
            }

            if (depth >= maxDepth)// 防止无限循环
            {
                logger.Info("Maximum depth reached, stopping traversal to prevent potential infinite loop.");
            }
        }

        /// <summary>
        /// 重置所有节点的选中状态
        /// </summary>
        public void ResetTreeNodeSelectStatus()
        {
            foreach (var (nodeId, node) in treeNodes)
            {
                node.ResetNodeSelectStatus();
                node.sortingOrder = 0;
            }
        }

        /// <summary>
        /// 设置选中节点所有父节点的渲染层级
        /// </summary>
        /// <param name="nodeId"></param>
        public void SetSelectNodeParentSortOrder(long nodeId, int maxDepth = 100)
        {
            int depth = 0;
            long id = nodeId;
            needSetOrderIdDic.Clear();
            needSetOrderIdDic.Add(id, ESelectStatus.Selected);
            while (depth < maxDepth)
            {
                var config = Mc.Tables.TbTechnology.GetOrDefault(id);
                if(config.ParentId == null || config.ParentId.Length == 0)
                {
                    break;
                }
                id = config.ParentId[0];
                if (id != 0 && !needSetOrderIdDic.ContainsKey(id))
                {
                    needSetOrderIdDic.Add(id, ESelectStatus.SelectedByTheWay);
                }
                depth++;
            }
        }


        /// <summary>
        /// 初始化导航栏
        /// </summary>
        public void InitNavTabNew()
        {
            tabDataLst = new List<NavBarData>();
            dataList ??= new List<TechnologySystem>(Mc.Tables.TbTechnologySystem.DataMap.Values);
            levelList ??= new List<TechnologyLevel>(Mc.Tables.TbTechnologyLevel.DataMap.Values);
            navBar.SetClickBack((data,btnClicked) =>
            {
                if (data.TabStyle == ETabStyle.Primary)
                {
                    OnClickFirstTab(data);
                }
                else if (data.TabId != 0 && data.TabStyle == ETabStyle.Secondary)
                {
                    OnClickSecTab(data);
                }
            });
            
            for (int i = 0; i < dataList.Count; i++)
            {
                int id = dataList[i].Id;
                string subName = dataList[i].Name;
                string icon = dataList[i].CraftIcon;
                NavBarData firstTab = new NavBarData(subName, id, icon,ENavIconType.BigIcon);
#if POCO
                firstTab.TextPocoKeyFormat = "TechTreeTabText{0}";
                firstTab.ButtonPocoKeyFormat = "TechTreeTabButton{0}";
#endif
                tabDataLst.Add(firstTab);
                navBar.AddTabData(firstTab);
                if (id == 0)
                {
                    firstTab.SetFirstTabShowTriangle(false);
                    continue;
                }

                for (int j = 0; j < levelList.Count; j++)
                {
                    var type = GetSecTabIdByIndex(id, levelList[j].Id);
                    var secName = levelList[j].Name;
                    var secIcon = levelList[j].CraftIcon;
                    NavBarData secTab = new NavBarData(secName, type, secIcon,ENavIconType.SmallIcon);
#if POCO
                    secTab.TextPocoKeyFormat = "TechTreeSubTabText{0}";
                    secTab.ButtonPocoKeyFormat = "TechTreeSubTabButton{0}";
#endif
                    secTab.IsShowRedDot = id < OperatingWorkbenchLevel;
                    secTab.IsTranslucent = id < OperatingWorkbenchLevel;
                    navBar.AddTabData(secTab, id);
                }
            }
            SetNavTabRedDot();
            int mainId = 0;
            int subId = -1;
            if(curSelectTreeNodeId != 0)
            {
                mainId = Mc.Tables.TbTechnology.GetOrDefault(curSelectTreeNodeId).System;
                subId = Mc.Tables.TbTechnology.GetOrDefault(curSelectTreeNodeId).GroupId;
                isUseRecordSelectNodeId = true;
            }
            navBar.ScrollAndSelect(mainId,subId);
            
        }

        /// <summary>
        /// 点击二级页签回调
        /// </summary>
        /// <param name="data"></param>
        public void OnClickSecTab(NavBarData data)
        {
            var secIndex = GetSecTabIndexByTabId(data.Parent.TabId, data.TabId);
            curLevel = secIndex;
            //列表滑动到指定二级页签那个区域
            ScrollToSecTabNode(data.Parent.TabId, secIndex);
            if (secTimer != -1)
            {
                Mc.TimerWheel.CancelTimer(secTimer);
                secTimer = -1;
            }
            secTimer = Mc.TimerWheel.AddTimerOnce(50, (_, data, delete) =>
            {
                if (SelectBluePrintId != 0)
                {
                    var id = Mc.Technology.GetTechIdByBluePrintId(SelectBluePrintId);
                    if(treeNodes.TryGetValue(id, out var _) && curSelectTreeNodeId != id)
                    {
                        OnRefreshSelectNode(id);
                        SelectBluePrintId = 0;
                    }
                    
                }
                else
                {
                    if (uiTechtreeFindNode.findNodeId != 0)
                    {
                        OnRefreshSelectNode(uiTechtreeFindNode.findNodeId);
                        uiTechtreeFindNode.findNodeId = 0;
                    }
                    else
                    {
                        if (isUseRecordSelectNodeId)
                        {
                            OnClickNode(curSelectTreeNodeId, true);
                            SetSelectNodeParentSortOrder(curSelectTreeNodeId);
                            isUseRecordSelectNodeId = false;
                        }
                        else
                        {
                            SetDefaultSelectNode(secIndex);
                        }
                        
                    }
                }
            });
            if (!Mc.Technology.CanSupportUnlockNode(curLevel,Source,OperatingHubId) && Source != EUiTechTreeOpenSource.None)
            {
                Mc.Technology.ShowCanNotSupportCommonTip(curLevel);
            }
            if (navBar.GetCurSelectFirstTabId() == 1)
            {
                if(Mc.Mission == null || Mc.Mission.HasMission == false) return;
                var missionCfg = Mc.Tables.TbQuestPhase.GetOrDefault(Mc.Mission.NewbieRemoteData.Id);
                if (missionCfg != null && missionCfg.GuideId == 154)
                {
                    var guideData = Mc.Guide.GetGuideData(missionCfg.GuideId);
                    if (guideData != null && guideData.CurGuideStepId == 1541)
                    {
                        Mc.Guide.FinishViewGuideStep(154, 1541);
                        Mc.Msg.FireMsgAtOnce(EventDefine.TryGuideInterface, 154, 1542);
                    }
                }
            }
            
        }
        /// <summary>
        /// 点击一级页签回调
        /// </summary>
        /// <param name="data"></param> 
        public void OnClickFirstTab(NavBarData data)
        {
            if (curFirstTabId != data.TabId)
            {
                OnRefreshTechtree(data.TabId);
                curFirstTabId = data.TabId;
            }
            listTypeCtrl.SetSelectedIndex(0);
            if (data.TabId == dataList[0].Id)//只针对手工科技做处理
            {
                listTypeCtrl.SetSelectedIndex(1);
                ScrollToSecTabNode(data.TabId);
                if (SelectBluePrintId != 0 )
                {
                    var id = Mc.Technology.GetTechIdByBluePrintId(SelectBluePrintId);
                    if(treeNodes.TryGetValue(id, out var _))
                    {
                        OnRefreshSelectNode(id);
                    }
                    else
                    {
                        SetDefaultSelectNode();
                    }
                    SelectBluePrintId = 0;
                }
                else
                {
                    if (uiTechtreeFindNode.findNodeId != 0)
                    {
                        OnRefreshSelectNode(uiTechtreeFindNode.findNodeId);
                        uiTechtreeFindNode.findNodeId = 0;
                    }
                    else
                    {
                        if (isUseRecordSelectNodeId)
                        {
                            OnClickNode(curSelectTreeNodeId, true);
                            SetSelectNodeParentSortOrder(curSelectTreeNodeId);
                            isUseRecordSelectNodeId = false;
                        }
                        else
                        {
                            SetDefaultSelectNode(0);//看看是否有可解锁的节点，选中第一个可解锁的节点
                        }
                    }
                }
                
                curLevel = 0;
            }
        }

        /// <summary>
        /// 设置二级页签红点
        /// </summary>
        public void SetNavTabRedDot()
        {
            navBar.IsHideFirstTabRedDot = true;
            RefreshRedDot();
        }

        /// <summary>
        /// 刷新二级页签红点
        /// </summary>
        public void RefreshRedDot()
        {
            var firstTabId = navBar.GetCurSelectFirstTabId();
            var curSecNavDataDic = navBar.GetTabData(firstTabId)?.ChildDic;
            if(curSecNavDataDic == null) return;
            foreach (var (id,navBarrData) in curSecNavDataDic)
            {
                var secIndex = GetSecTabIndexByTabId(firstTabId, id);
                navBarrData.IsShowRedDot = secIndex > OperatingWorkbenchLevel;
                navBarrData.IsTranslucent = secIndex > OperatingWorkbenchLevel;
            }
        }

        /// 根据点击的二级页签，树节点开始滑动到二级页签对应的第一行
        public void ScrollToSecTabNode(int firstTabId, int secTabId = -1)
        {
            int index = int.MaxValue;
            if(SelectBluePrintId != 0 )
            {
                var id = Mc.Technology.GetTechIdByBluePrintId(SelectBluePrintId);
                index = GetTechtreeListIndexByNodeId(id);
            }
            else
            {
                if(uiTechtreeFindNode.findNodeId != 0)
                {
                    index = GetTechtreeListIndexByNodeId(uiTechtreeFindNode.findNodeId);
                }
                else
                {
                    if(secTabId == -1)
                    {
                        index = GetTechtreeListIndexByFirstTabId(firstTabId);
                    }
                    else
                    {
                        index = GetTechtreeListIndexByTabId(firstTabId, secTabId);
                    }
                }
            }
            if (index != int.MaxValue)
            {
                treeNodeGList.ScrollToView(index,false,true, new Vector2(0, ScrollOffset));
            }
        }
        
        /// 根据一级页签和二级页签id获取在techNodeDataDic中的index
        public int GetTechtreeListIndexByNodeId(long nodeId)
        {
            int index = 0;
            foreach (var tech in Mc.Tables.TbTechnology.DataMap.Values)
            {
                if (nodeId == tech.Id)
                {
                    index = tech.GPos[0];
                }
            }

            return index;
        }

        /// 根据一级页签和二级页签id获取在techNodeDataDic中的index
        public int GetTechtreeListIndexByTabId(int firstTabId, int secTabId)
        {
            int index = int.MaxValue;
            foreach (var tech in Mc.Tables.TbTechnology.DataMap.Values)
            {
                if (tech.GroupId == secTabId && tech.System == firstTabId)
                {
                    if (index > tech.GPos[0]) //取最小的那一行当作滑动的位置
                    {
                        index = tech.GPos[0];
                    }
                }
            }

            return index;
        }
        
        /// 根据一级页签id获取在techNodeDataDic中的index,目前只有手工科技使用
        public int GetTechtreeListIndexByFirstTabId(int firstTabId)
        {
            int index = int.MaxValue;
            foreach (var tech in Mc.Tables.TbTechnology.DataMap.Values)
            {
                if (tech.System == firstTabId)
                {
                    if (index > tech.GPos[0]) //取最小的那一行当作滑动的位置
                    {
                        index = tech.GPos[0];
                    }
                }
            }

            return index;
        }

        /// <summary>
        /// 设置默认选中二级页签顺位第一个可解锁节点
        /// </summary>
        public void SetDefaultSelectNode(int secLevel = -1)
        {
            long id = 0;
            if (isSelectFirstUnlocked) //没有选中的节点，选中第一个可解锁,除了第一次进科技界面有纪录的节点或者跳转的节点，其他都要先查第一个可解锁节点
            {
                int posX = int.MaxValue;
                int posY = int.MaxValue;
                for (int i = 0; i < techNodeDataDic.Count; i++)
                {
                    var techList = techNodeDataDic[i];
                    for (int j = 0; j < techList.Count; j++)
                    {
                        var techNode = techList[j];
                        if (secLevel == techNode.GroupId)
                        {
                            var status = Mc.Technology.GetTechNodeStatus(techNode.Id);
                            if (status == TechnologyUnlockStatus.NotUnlock)
                            {
                                var consume = Mc.Technology.GetUnlockConsume(techNode.BlueprintIds, techNode.IngredientsNum);
                                if (curConsumeScrapCount < consume) continue;
                                // var config = Mc.Tables.TbTechnology.GetOrDefault(techNode.Id);
                                if (techNode.GPos == null || techNode.GPos.Length < 2) continue;
                                if(secLevel != -1 && techNode.GroupId != secLevel) continue;
                                if (posX > techNode.GPos[0])
                                {
                                    posX = techNode.GPos[0];
                                }

                                if (posX == techNode.GPos[0] && posY > techNode.GPos[1])
                                {
                                    posY = techNode.GPos[1];
                                    id = techNode.Id;
                                }
                            }
                        }
                    }
                }
            }
            else
            {
                isSelectFirstUnlocked = true;
            }
            if (id != 0)
            {
                OnRefreshSelectNode(id);
            }
            else //没有可解锁的节点，选中第一个节点
            {
                long defaultId = 0;
                int x = int.MaxValue;
                int y = int.MaxValue;
                for (int i = 0; i < techNodeDataDic.Count; i++)
                {
                    var techList = techNodeDataDic[i];
                    for (int j = 0; j < techList.Count; j++)
                    {
                        var techNode = techList[j];
                        if (secLevel == techNode.GroupId)
                        {
                            if (techNode.GPos == null || techNode.GPos.Length < 2 ) continue;
                            if (x >= techNode.GPos[0])
                            {
                                x = techNode.GPos[0];
                            }

                            if (x == techNode.GPos[0] && y >= techNode.GPos[1])
                            {
                                y = techNode.GPos[1];
                                defaultId = techNode.Id;
                            }
                        }
                    }
                }

                if (defaultId != 0)
                {
                    OnRefreshSelectNode(defaultId);
                }
            }
            
        }

        /// <summary>
        /// 移动到下一个节点
        /// </summary>
        public void OnMoveNextNode()
        {
            var config = Mc.Tables.TbTechnology.GetOrDefault(curSelectTreeNodeId);
            var posX = config.GPos[0];
            var posY = config.GPos[1];
            if (techNodeDataDic.TryGetValue(posX, out var techList))
            {
                var index = techList.FindIndex((tech) => tech.GPos[1] == posY);
                if (index + 1 <= techList.Count - 1) //下一个节点还没超过这一行的最后一个节点
                {
                    var nextNode = techList[index + 1];
                    if (nextNode != null)
                    {
                        var id = nextNode.Id;
                        treeNodeGList.ScrollToView(posX);
                        OnRefreshSelectNode(id);
                    }
                }
                else //下一个节点超过了这一行的最后一个节点
                {
                    if (techNodeDataDic.TryGetValue(posX + 1, out var nextTechList)) //再判断是否是最后一行的最后一个节点
                    {
                        var nextNode = nextTechList[0];
                        if (nextNode != null)
                        {
                            var id = nextNode.Id;
                            treeNodeGList.ScrollToView(posX + 1);
                            OnRefreshSelectNode(id);
                        }
                    }
                }
                RefreshBtnPreAndNextStatus();
            }

        }

        /// <summary>
        /// 移动到上一个节点
        /// </summary>
        public void OnMovePreNode()
        {
            var config = Mc.Tables.TbTechnology.GetOrDefault(curSelectTreeNodeId);
            var posX = config.GPos[0];
            var posY = config.GPos[1];
            if (techNodeDataDic.TryGetValue(posX, out var techList))
            {
                var index = techList.FindIndex((tech) => tech.GPos[1] == posY);
                if (index - 1 >= 0) //上一个节点还没超过这一行的第一个节点
                {
                    var preNode = techList[index - 1];
                    if (preNode != null)
                    {
                        var id = preNode.Id;
                        treeNodeGList.ScrollToView(posX);
                        OnRefreshSelectNode(id);
                    }
                }
                else //上一个节点超过了这一行的第一个节点
                {
                    if (techNodeDataDic.TryGetValue(posX - 1, out var preTechList)) //再判断是否是第一行的第一个节点
                    {
                        var preNode = preTechList[preTechList.Count - 1];
                        if (preNode != null)
                        {
                            var id = preNode.Id;
                            treeNodeGList.ScrollToView(posX - 1);
                            OnRefreshSelectNode(id);
                        }
                    }
                }

                RefreshBtnPreAndNextStatus();
            }
        }

        /// <summary>
        /// 刷新左右切换按钮状态
        /// </summary>
        public void RefreshBtnPreAndNextStatus()
        {
            btnPreCtrl.SetSelectedIndex(0);
            btnNextCtrl.SetSelectedIndex(0);
            if (treeNodes.TryGetValue(curSelectTreeNodeId, out var node))
            {
                var posX = node.GposX;
                var posY = node.GposY;
                //是否是第一行的第一个节点
                if (posX == 0)
                {
                    if (techNodeDataDic.TryGetValue(posX, out var techList))
                    {
                        var index = techList.FindIndex((tech) => tech.GPos[1] == posY);
                        if (index == 0)
                        {
                            btnPreCtrl.SetSelectedIndex(1);
                            return;
                        }
                    }
                }
                //是否是最后一行的最后一个节点
                if (posX == techNodeDataDic.Count - 1)
                {
                    if (techNodeDataDic.TryGetValue(posX, out var techList))
                    {
                        var index = techList.FindIndex((tech) => tech.GPos[1] == posY);
                        if (index == techList.Count - 1)
                        {
                            btnNextCtrl.SetSelectedIndex(1);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 刷新选中的节点信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="posY"></param>
        public void OnRefreshSelectNode(long id)
        {
            if (curSelectTreeNodeId == id ) return;
            ResetTreeNodeSelectStatus();
            OnClickNode(id);
            SetSelectNodeParentSortOrder(id);
        }

        /// <summary>
        /// 刷新肥料数量
        /// </summary>
        public void RefreshScrapCount()
        {
            curConsumeScrapCount = Mc.CollectionItem.GetAmount(MgrTechnology.ScrapItemId);
            topBar.RefreshCurrency();
        }

        private static void ClearTechCache()
        {
            // 热更新清缓存
            dataList = null;
            levelList = null;
        }
        
        /// <summary>
        /// 打开科技树
        /// </summary>
        /// <param name="homePageIndex">二级页签索引，以前的一级二级三级工作台</param>
        /// <param name="source">打开来源</param>
        /// <param name="opHubEntityId">若打开来源为中枢，代表中枢的entityId</param>
        public static void Open(int homePageIndex = 0, EUiTechTreeOpenSource source = EUiTechTreeOpenSource.Workbench,
            long opHubEntityId = 0, long bluePrintId = 0)
        {
            homePageIndex = Mathf.Clamp(homePageIndex, 0, WorkBenchUtil.HighestLevel);
            //这里不走WindowComBase的回调函数，因为会缓存，在跳转打开多个界面的时候会导致界面缓存数据被覆盖
            if (Mc.Ui.IsWindowOpen("UiTechTree"))
            {
                Mc.Ui.GetWindow("UiTechTree", (layer, win ) =>
                {
                    var uiTechTree = win as UiTechTree;
                    uiTechTree.OnAfterTechTreeEnable(homePageIndex, source, opHubEntityId, bluePrintId);
                });
            }
            else
            {
                Mc.Ui.OpenWindow("UiTechTree", (win) =>
                {
                    var uiTechTree = win as UiTechTree;
                    uiTechTree.OnAfterTechTreeEnable(homePageIndex, source, opHubEntityId, bluePrintId);
                });

            }


        }

        /// <summary>
        /// 打开科技树
        /// </summary>
        /// <param name="bluePrintId">要找的蓝图id</param>
        public static void OpenByBluePrintId(long bluePrintId = 0)
        {
            Open(0, EUiTechTreeOpenSource.Workbench, 0, bluePrintId);
        }


        /// <summary>
        /// 外界调用关闭科技界面的方法
        /// </summary>
        public override void OnEscClose(EventContext context)
        {
            Close();
        }

        public static void Close()
        {
            if (!Mc.Ui.IsWindowOpen("UiTechTree")) return;
            var win = GetWindow();
            if (win != null)
            {
                win.PlaySound(false, OperatingWorkbenchLevel);
                // win.TechTreeInfo.CloseInfoPanel();
                win.HideSelf();
            }
        }

        /// <summary>
        /// 获取科技树窗口
        /// </summary>
        /// <returns></returns>
        public static UiTechTree GetWindow()
        {
            return Mc.Ui.GetWindowT<UiTechTree>("UiTechTree");
        }

        public void UnLockTechNodeAck()
        {
            if (treeNodes.TryGetValue(curSelectTreeNodeId, out var node))
            {
                var parentId = node.ID;
                int maxDepth = 100;
                int depth = 0;
                while (parentId != -1 && depth < maxDepth)
                {
                    if (treeNodes.TryGetValue(parentId, out var paTreeNode) && paTreeNode.Status != TechnologyUnlockStatus.AlreadyUnlock)
                    {
                        paTreeNode.PlayUnlockTransition();
                    }
                    if(paTreeNode == null) break;
                    parentId = paTreeNode.ParentID;
                    depth++;
                }
            }
            TechTreeInfo.PlayClickAnimation(() =>
            {
                RefreshNodeInfo();
            });
        }

        public void RefreshNodeInfo()
        {
            RefreshScrapCount();
            if (treeNodes.TryGetValue(curSelectTreeNodeId, out var node))
            {
                var parentId = node.ID;
                int maxDepth = 100;
                int depth = 0;
                while (parentId != -1 && depth < maxDepth)
                {
                    if (treeNodes.TryGetValue(parentId, out var paTreeNode))
                    {
                        paTreeNode.SetLockStatus();
                        parentId = paTreeNode.ParentID;
                    }
                    depth++;
                }
                
                TechTreeInfo.OpenInfoPanel(node);
                RefreshChildLineColor();
                foreach (var item in treeNodes)
                {
                    item.Value.RefreshInfo();
                }
            }

        }

        /// <summary>
        /// 更新子节点的连线颜色
        /// </summary>
        public void RefreshChildLineColor()
        {
            if (treeNodes.TryGetValue(curSelectTreeNodeId, out var _))
            {
                foreach (var item in treeNodes)
                {
                    if(item.Key != curSelectTreeNodeId && item.Value.ParentID == curSelectTreeNodeId)
                    {
                        item.Value.UpdateLineColor();
                    }
                }
            }
        }

        /// <summary>
        /// 设置声望等级
        /// </summary>
        private void SetReputationLevel()
        {
            if (PlayHelper.IsReputationPlay)
            {
                var comp = Mc.MyPlayer.MyEntityServer.GetComponent<ReputationComponent>(EComponentIdEnum.ReputationComponentId);
                if (comp != null)
                {
                    topBar.RefreshCurrency();
                }
            }
        }

        /// <summary>
        /// 科技树打开时的刷新
        /// </summary>
        /// <param name="level"></param>
        private void OnAfterTechTreeEnable(int homePageIndex, EUiTechTreeOpenSource source, long opHubEntityId,long bluePrintId)
        {
            Source = source;
            OperatingHubId = opHubEntityId;
            SelectBluePrintId = bluePrintId;
            curLevel = homePageIndex;

            Mc.Technology.GetOperatingWorkbench(source, opHubEntityId, out int maxLv, out long entityId);
            OperatingWorkbenchLevel = maxLv;
            OperatingWorkbenchId = entityId;

            PlaySound(true, OperatingWorkbenchLevel);
            RefreshRedDot();
            navBar.Refresh();
            GetTabIdByBluePrintId(out int firstTabId, out int secTabIndex);
            if(SelectBluePrintId != 0)
            {
                isSelectFirstUnlocked = false;
                //navBar.IsCallbackEveryClick = true;
                if(secTabIndex != 0)
                {
                    var secTabId = GetSecTabIdByIndex(firstTabId, secTabIndex);
                    navBar.ScrollAndSelect(firstTabId, secTabId);
                }
                else
                {
                    navBar.ScrollAndSelect(firstTabId);
                }
            } 
            else if (curSelectTreeNodeId != 0) //这种情况是在关闭科技树界面时，选中了某个节点，再次打开时，选中的节点还是之前选中的节点，但是要主动刷新节点的状态
            {
                // isSelectFirstUnlocked = false;
                var data = Mc.Tables.TbTechnology.GetOrDefault(curSelectTreeNodeId);
                OnRefreshTechtree(data.System);
                isUseRecordSelectNodeId = true;
                OnJumpToTreeNode(curSelectTreeNodeId,data.System,data.GroupId, true);
                treeNodeGList.ScrollToView(data.GPos[0],false,true);
                OnClickNode(curSelectTreeNodeId, true);
            }
            
            RefreshNodeInfo();
        }

        /// <summary>
        /// 根据SelectBluePrintId得到对应得一级和二级页签
        /// </summary>
        public void GetTabIdByBluePrintId(out int firstTabId, out int secTabIndex)
        {
            firstTabId = 0;
            secTabIndex = 0;
            if (SelectBluePrintId != 0)
            {
                foreach (var VARIABLE in Mc.Tables.TbTechnology.DataMap.Values)
                {
                    var blueprintIds = VARIABLE.BlueprintIds;
                    bool isFind = false;
                    for (int i = 0; i < blueprintIds.Length; i++)
                    {
                        if (SelectBluePrintId == blueprintIds[i])
                        {
                            isFind = true;
                            firstTabId = VARIABLE.System;
                            secTabIndex = VARIABLE.GroupId;
                            break;
                        }
                    }

                    if (isFind)
                    {
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 不同页签播放不同声音
        /// </summary>
        /// <param name="open"></param>
        /// <param name="level"></param>
        public void PlaySound(bool open, long level)
        {
            string soundName = string.Empty;
            if (open)
            {
                switch (level)
                {
                    case 1:
                        soundName = Mc.Tables.TbUiAudio.TechTreeLevelOneOpen;
                        break;
                    case 2:
                        soundName = Mc.Tables.TbUiAudio.TechTreeLevelTwoOpen;
                        break;
                    case 3:
                        soundName = Mc.Tables.TbUiAudio.TechTreeLevelThreeOpen;
                        break;
                    default:
                        soundName = Mc.Tables.TbUiAudio.TechTreeLevelOneOpen;
                        break;
                }
            }
            else
            {
                switch (level)
                {
                    case 1:
                        soundName = Mc.Tables.TbUiAudio.TechTreeLevelOneClose;
                        break;
                    case 2:
                        soundName = Mc.Tables.TbUiAudio.TechTreeLevelTwoClose;
                        break;
                    case 3:
                        soundName = Mc.Tables.TbUiAudio.TechTreeLevelThreeClose;
                        break;
                    default:
                        soundName = Mc.Tables.TbUiAudio.TechTreeLevelOneClose;
                        break;
                }
            }

            if (!string.IsNullOrEmpty(soundName))
                Mc.Audio.PlayAudioEvent(null, soundName);
        }

        /// <summary>
        /// 生成科技树节点，这个方法只用在一级页签
        /// 从TbTechnology里的system作为一级页签，拿到所有一级页签的数据
        /// </summary>
        /// <param name="firstTabId"></param>
        private void OnRefreshTechtree(int firstTabId, int secTabId = -1)
        {
            //Mc.Audio.PlayAudioEvent(null, Mc.Tables.TbUiAudio.UniversalButtonClick2);
            techNodeDataDic.Clear();
            treeNodes.Clear();
            recodListHeadIndexList.Clear();

            foreach (var tech in Mc.Tables.TbTechnology.DataMap.Values)
            {
                if (!tech.Isuse || tech.System != firstTabId) continue;

                var pos = tech.GPos;
                var posX = pos[0];
                var posY = pos[1];
                if (secTabId != -1 && tech.GroupId != secTabId) continue;
                //记录下每个一级页签的二级页签NodeTree带标题的那个index
                if (tech.IsTitle && !recodListHeadIndexList.Contains(posX))
                {
                    recodListHeadIndexList.Add(posX);
                }

                if (!techNodeDataDic.ContainsKey(posX))
                {
                    List<Common.Data.DataItem.Technology> techList = new();
                    techList.Add(tech);
                    techNodeDataDic.Add(posX, techList);
                }
                else
                {
                    var techList = techNodeDataDic[posX];
                    // 手动插入确认 PosY 顺序
                    int insertIndex = techList.FindIndex(t => t.GPos[1] > posY);
                    if (insertIndex == -1)
                    {
                        // 如果没有找到更大的 PosY，就在末尾插入
                        techList.Add(tech);
                    }
                    else
                    {
                        // 在找到的索引处插入
                        techList.Insert(insertIndex, tech);
                    }
                }
            }

            SetNavTabRedDot();
            treeNodeGList.numItems = techNodeDataDic.Count;

        }

        /// <summary>
        /// 当前工作台是否支持解锁
        /// </summary>
        /// <returns></returns>
        public bool CanSupportUnlockNode()
        {
            return curLevel <= OperatingWorkbenchLevel;
        }

        
        /// 新手引导获取指引对象
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public override GObject GetGuideObjByID(long id)
        {
            if (treeNodes != null && treeNodes.ContainsKey(id))
            {
                var node = treeNodes[id];
                return node;
            }
            return base.GetGuideObjByID(id);
        }
    }

}
   