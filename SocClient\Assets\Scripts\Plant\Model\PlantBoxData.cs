using Assets.Scripts.Plant;
using System.Collections.Generic;
using WizardGames.SocConst.Soc.Const;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Data.Plant;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Utility;

namespace WizardGames.Soc.SocClient.Plant
{
    public sealed class PlantBoxData : IRecyclable
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(PlantBoxData));
        public Dictionary<long, PlantData> plantDic = new Dictionary<long, PlantData>();
        BasicTypeList<int> manualLst = new BasicTypeList<int>();
        public PlantBoxConfig plantBoxCfg;
        public long EntityId { get; private set; }
        public int Water { get; private set; }

        public void Register(PlantBoxComponent data)
        {
            if (data == null) return;
            plantBoxCfg = Mc.Tables.TbPlantBoxConfig.GetOrDefault(data.ParentPartEntity.TemplateId);
            EntityId = data.ParentId;
            Water = data.Water;
            UpdatePlants(data);
        }

        public PlantData GetPlantData(long idx)
        {
            if (plantDic.TryGetValue(idx, out var rt))
            {
                return rt;
            }
            return null;
        }

        public int GetPlantDataIndex(PlantData plantData)
        {
            var plantSlot = (int)plantDic.Keys.FirstOrDefaultWithoutLinq((index) => plantDic[index] == plantData);
            return plantSlot;
        }

        public bool HasAnyPlant()
        {
            return plantDic.Count > 0;
        }

        public bool CanManure(bool showFailedTips = false)
        {
            if (Mc.CollectionItem.InventoryCom.GetAmount(ItemConst.Manure) == 0)
            {
                //背包中没有肥料，无法施肥
                if (showFailedTips) Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.NoManureInBackpack);
                return false;
            }
            //当前种植箱中没有需要施肥的作物，请稍后再试
            int tipsId = 22065; 
            if (plantDic.Count == 0)
            {
                //种植箱中当前未种植任何作物，无法施肥
                tipsId = 22064;
            }
            foreach (var item in plantDic.Values)
            {
                if (item.CanManure())
                {
                    return true;
                }
            }
            if (showFailedTips) Mc.MsgTips.ShowRealtimeWeakTip(tipsId);
            return false;
        }

        public bool CanWater(bool showFailedTips = false)
        {
            int tipsId = 0;
            if (Mc.Plant.TotalWater == 0)
            {
                //当前背包中没有足够的水资源
                tipsId = 22066;
            }
            if (Water >= plantBoxCfg.WaterLimit)
            {
                //当前种植箱水量已满，无法再浇水
                tipsId = 22067;
            }
            if (tipsId != 0)
            {
                if (showFailedTips) Mc.MsgTips.ShowRealtimeWeakTip(tipsId);
                return false;
            }

            return true;
        }

        public bool CanHarvest()
        {
            foreach (var item in plantDic.Values)
            {
                if (item.Stage == PlantStage.Harvest)
                {
                    return true;
                }
            }
            return false;
        }

        public bool CanSeed(bool showFailedTips = false)
        {
            if (plantBoxCfg != null)
            {
                if (plantDic.Values.Count >= plantBoxCfg.Capacity)
                {
                    //当前种植箱已被种满，无法播种
                    int tipsId = 22069; 
                    if (showFailedTips) Mc.MsgTips.ShowRealtimeWeakTip(tipsId);
                    return false;
                }
                return true;
            }
            return false;
        }

        public void UpdatePlants(PlantBoxComponent plantBox)
        {
            if (plantBox == null || plantBox.Root == null) return;
            var plantsNode = plantBox.Root.GetChildNode(StorageContainerConst.PlantBoxSlot) as ItemContainerNode;

            foreach (var item in plantDic.Values)
            {
                Pool.Release(item);
            }
            plantDic.Clear();
            foreach (var item in plantsNode)
            {
                if (item.Value is PlantNode plant)
                {
                    var plantData = Pool.Get<PlantData>();
                    plantData.Refresh(plant);
                    plantDic[plant.Index] = plantData;
                }
            }
            Mc.Msg.FireMsg(EventDefine.UpdatePlantsInfo, EntityId);
        }

        public void UpdatePlantsWithoutMessage(PlantBoxComponent plantBox)
        {
            if (plantBox == null || plantBox.Root == null) return;
            var plantsNode = plantBox.Root.GetChildNode(StorageContainerConst.PlantBoxSlot) as ItemContainerNode;

            foreach (var item in plantDic.Values)
            {
                Pool.Release(item);
            }
            plantDic.Clear();
            foreach (var item in plantsNode)
            {
                if (item.Value is PlantNode plant)
                {
                    var plantData = Pool.Get<PlantData>();
                    plantData.Refresh(plant);
                    plantDic[plant.Index] = plantData;
                }
            }
        }

        public void UpdateWater(int water)
        {
            Water = water;
            Mc.Msg.FireMsg(EventDefine.UpdatePlantWater, EntityId);
        }

        /// <summary>
        /// 请求种植
        /// </summary>
        /// <param name="srcIdx">背包位置</param>
        /// <param name="dstIdx">种植箱位置</param>
        public void RequestPlant(List<Alpha3PlantArgs> argList)
        {
            var partEntity = EntityManager.Instance.GetEntity(EntityId) as PartEntity;
            var plantBox = partEntity.GetComponent<PlantBoxComponent>(EComponentIdEnum.PlantBox);
            if (plantBox != null)
            {
                Mc.Audio.PlayAudioEvent(null, "Int_Seed_Plant");
                plantBox.RemoteCallPlant(ERpcTarget.World, argList);

                foreach (var plantArg in argList)
                {
                    logger.InfoFormat("[RequestPlant] collectionId:{0}, srcIndex {1}, dstIndex {2}", EntityId, plantArg.SrcIndex, plantArg.DstIndex);
                }
            }
        }

        /// <summary>
        /// 请求施肥
        /// </summary>
        /// <param name="idx">种植箱位置</param>
        public void RequestManure(List<int> slots)
        {
            manualLst.Clear();
            if (slots == null) return;
            var partEntity = EntityManager.Instance.GetEntity(EntityId) as PartEntity;
            var plantBox = partEntity.GetComponent<PlantBoxComponent>(EComponentIdEnum.PlantBox);
            if (plantBox != null)
            {
                string debug = "";
                foreach (var item in slots)
                {
                    manualLst.Add(item);
                    debug = debug+ " "+item.ToString();
                }
                plantBox.RemoteCallAddManure(ERpcTarget.World, manualLst);
                logger.InfoFormat("[RequestManure] collectionId:{0} idx: {1}", EntityId, slots);
            }
        }

        /// <summary>
        /// 请求浇水
        /// </summary>
        public void RequestWater()
        {
            if (Mc.Plant.TotalWater == 0)
            {
                //您背包中没有水资源，浇水失败
                Mc.MsgTips.ShowRealtimeWeakTip(23058);
                return;
            }

            var partEntity = EntityManager.Instance.GetEntity(EntityId) as PartEntity;
            var plantBox = partEntity.GetComponent<PlantBoxComponent>(EComponentIdEnum.PlantBox);
            if (plantBox != null)
            {
                //InteractionUtil.TryInteract(PlayerInteractiveId.Watering, EntityId);
                plantBox.RemoteCallAddWater(ERpcTarget.World);
                logger.InfoFormat("[RequestWater] collectionId:{0}", EntityId);
            }

        }

        /// <summary>
        /// 请求收获
        /// </summary>
        /// <param name="idx">种植箱位置</param>
        public void RequestHarvest(int idx)
        {
            var partEntity = EntityManager.Instance.GetEntity(EntityId) as PartEntity;
            var plantBox = partEntity.GetComponent<PlantBoxComponent>(EComponentIdEnum.PlantBox);
            if (plantBox != null)
            {
                Mc.Audio.PlayAudioEvent(null, "Svl_Pick_Plant");
                plantBox.RemoteCallHarvest(ERpcTarget.World, idx);
                logger.InfoFormat("[RequestHarvest] collectionId:{0} idx: {1}", EntityId, idx);
            }
        }

        /// <summary>
        /// 请求移除
        /// </summary>
        /// <param name="idx">种植箱位置</param>
        public void RequestRemove(int idx)
        {
            var partEntity = EntityManager.Instance.GetEntity(EntityId) as PartEntity;
            var plantBox = partEntity.GetComponent<PlantBoxComponent>(EComponentIdEnum.PlantBox);
            if (plantBox != null)
            {
                plantBox.RemoteCallRemove(ERpcTarget.World, idx);
                logger.InfoFormat("[RequestRemove] collectionId:{0} idx: {1}", EntityId, idx);
            }
        }

        /// <summary>
        /// 开始小游戏
        /// </summary>
        /// <param name="index">种植箱位置</param>
        /// <param name="selectIndexes">选中两个植物杂交的位置</param>
        public void RequestHybridizeStart(long index, BasicTypeList<int> selectIndexes)
        {
            var partEntity = EntityManager.Instance.GetEntity(EntityId) as PartEntity;
            var plantBox = partEntity.GetComponent<PlantBoxComponent>(EComponentIdEnum.PlantBox);
            if (plantBox != null)
            {
                plantBox.RemoteCallManualHybridizeStart(ERpcTarget.World, index, selectIndexes);
                logger.InfoFormat("[RequestHybridizeStart] collectionId:{0} index: {1}", EntityId, index);
            }
        }

        /// <summary>
        /// 提交小游戏结果
        /// </summary>
        /// <param name="magicNum">校验值</param>
        /// <param name="pointIndexes">植物种植箱的位置</param>
        /// <param name="pointValues">每种基因加点数量</param>
        /// <param name="result">小游戏结果</param>
        public void RequestHybridizeFinish(int magicNum, BasicTypeList<int> pointIndexes, BasicTypeList<int> pointValues, int result)
        {
            var partEntity = EntityManager.Instance.GetEntity(EntityId) as PartEntity;
            var plantBox = partEntity.GetComponent<PlantBoxComponent>(EComponentIdEnum.PlantBox);
            if (plantBox != null)
            {
                plantBox.RemoteCallManualHybridizeFinish(ERpcTarget.World, magicNum, pointIndexes, pointValues, result);
                logger.InfoFormat("[RequestHybridizeFinish] collectionId:{0} magicNum: {1}", EntityId, magicNum);
            }
        }

        public void OnGet()
        {
            EntityId = -1;
            Water = 0;
            plantBoxCfg = null;
        }

        public void OnRelease()
        {
            foreach (var item in plantDic.Values)
            {
                Pool.Release(item);
            }
            plantDic.Clear();
        }

        public void OnDestroy() { }
    }
}
