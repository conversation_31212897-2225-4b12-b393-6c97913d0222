﻿using System;
using System.Buffers;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using WizardGames.Soc.Common.Algorithm;
using WizardGames.Soc.Common.Character;
using WizardGames.Soc.Common.Const;
using WizardGames.Soc.Common.Construction;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data.DataItem;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Extension;
using WizardGames.Soc.Common.Framework.Algorithm;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.SimpleCustom;
using WizardGames.Soc.Common.TimerWheel;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.Share.Framework.Event;
using WizardGames.Soc.Share.Framework.Network;
using WizardGames.Soc.Share.Game.NodeSystem;
using WizardGames.Soc.SocWorld;
using WizardGames.Soc.SocWorld.Ability;
using WizardGames.Soc.SocWorld.Consts;
using WizardGames.Soc.SocWorld.CustomType.Node;
using WizardGames.Soc.SocWorld.CustomType.RootNode;
using WizardGames.Soc.SocWorld.Event;
using WizardGames.Soc.SocWorld.Framework;
using WizardGames.Soc.SocWorld.Framework.Event;
using WizardGames.Soc.SocWorld.Mongo;
using WizardGames.Soc.SocWorld.NodeSystem;
using WizardGames.Soc.SocWorld.NodeSystem.VirtualNode;
using WizardGames.Soc.SocWorld.WorldCommon;
using WizardGames.SocConst.Soc.Const;

namespace WizardGames.Soc.Common.Component
{
    [HotfixClass]
    public static partial class PlayerInventoryComponentHotfix
    {
        static PlayerInventoryComponentHotfix()
        {
            EntityStaticCallback<PlayerEntity>.AddStaticCallback<OnGetClientEvent>((pe, _) =>
            {
                pe.ComponentInventory.GetEnterInventoryItemsFromLobby();
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<KickPlayerEvent>((pe, _) =>
            {
                pe.ComponentInventory.PushInventoryItemsToLobby();
            });

            EntityStaticCallback<PlayerEntity>.AddStaticCallback<OnLoseClientEvent>((pe, _) =>
            {
                pe.ComponentInventory.SyncWeaponInfoFromEmbedded();
            });

        }

        private static SocLogger logger = LogHelper.GetLogger(typeof(PlayerInventoryComponentHotfix));
        private static List<long> AnywhereInPlayerInventory = NodePathConst.AnywhereInPlayerInventory.ToList();
        private static readonly IList<long> PlayerBackpackPath = new ConstList<long>(NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Wear, (long)EquipPart.ExtraPack - 1);
        private static readonly IList<long> OtherPlayerBackpackPath = new ConstList<long>(NodeSystemType.OtherPlayerInventory, PlayerInventoryNodeIndex.Wear, (long)EquipPart.ExtraPack - 1);
        private static readonly List<BaseItemNode> tempItemListForExtraPack = new();
        private static readonly List<BaseItemNode> tempItemListForOriginPack = new();
        private static readonly Dictionary<long, int> oversizeDict = new();
        private static readonly List<int> packageDropIds = new();
        private static readonly List<DropParam> packageDropReward = new();
        /// <summary>
        /// 捡东西丢东西的音效广播距离
        /// </summary>
        private const int PICK_DROP_SOUND_BROADCAST_RANGE = 20;

        [Hotfix]
        private static void Init(this PlayerInventoryComponent self)
        {
            self.EnterItems = new();
            self.HasEnterInventoryItems = new();
            self.DropItems = new();
            self.ProtectedItems = new();
            self.SystemRoot = new PlayerInventoryRootNode();
            self.Root.RegisterSystemRootNode(self.SystemRoot, NodeSystemType.OtherPlayerInventory);

            //背包容器节点
            var containerMainNode = new ItemContainerNode(ContainerConst.Main, PlayerInventoryNodeIndex.Main, NodeConst.MainItemContainerNodeId);
            self.SystemRoot.AddChildNode(containerMainNode);
            var containerBeltNode = new ItemContainerNode(ContainerConst.Belt, PlayerInventoryNodeIndex.Belt, NodeConst.BeltItemContainerNodeId);
            self.SystemRoot.AddChildNode(containerBeltNode);
            var containerWearNode = new ItemContainerNode(ContainerConst.Wear, PlayerInventoryNodeIndex.Wear, NodeConst.WearItemContainerNodeId);
            self.SystemRoot.AddChildNode(containerWearNode);
        }

        [Hotfix]
        private static void InitFromDb(this PlayerInventoryComponent self)
        {
            self.HasEnterInventoryItems = new();
            self.Root.RegisterSystemRootNode(self.SystemRoot, NodeSystemType.OtherPlayerInventory);
        }

        [Hotfix(AbilityCallback = true)]
        private static void OnCheckLootingAbility(this PlayerInventoryComponent self, CheckLootingAbility ability) => ability.RetVal = self.OnCheckLooting(ability.Args);
        [Hotfix(AbilityCallback = true)]
        private static void OnStartLootingAbility(this PlayerInventoryComponent self, StartLootingAbility ability) => self.OnStartLooting(ability.Args);
        [Hotfix(AbilityCallback = true)]
        private static void OnStopLootingAbility(this PlayerInventoryComponent self, StopLootingAbility ability) => self.OnStopLooting(ability.Args);

        [Hotfix]
        private static void PostInit(this PlayerInventoryComponent self, bool isLoadFromDb)
        {
            self.SystemRoot.SetVirtualNodeGetter((SystemRootNode sysRoot, long index) =>
            {
                return index switch
                {
                    PlayerInventoryNodeIndex.VirtualAll
                    or PlayerInventoryNodeIndex.VirtualMain
                    or PlayerInventoryNodeIndex.VirtualWear
                    or PlayerInventoryNodeIndex.VirtualBelt => new DirOfOverallStackNode(sysRoot, index),
                    PlayerInventoryNodeIndex.VirtualAllItemManufacturing => new DirOfOverallStackNodeForManufactruing(sysRoot, index),
                    _ => null,
                };
            });

            self.AddAbility(OnCheckLootingAbilityHotfix);
            self.AddAbility(OnStartLootingAbilityHotfix);
            self.AddAbility(OnStopLootingAbilityHotfix);
            self.ListenEvent(PickAndDropSoundHotfix);
            self.ListenEvent(StartLootingEventCallbackHotfix);
#if !SOC_WORLD_TEST
            if (!isLoadFromDb)
            {
                self.AddRebornItem();
            }
#endif
        }

        [Hotfix(EventCallback = true)]
        public static void OnPlayerStopLootingEvent(this PlayerInventoryComponent self, PlayerStopLootingEvent _)
        {
            if (self.reloadSession != null && self.reloadSession.BulletId != 0 && self.Root.GetNodeById(self.reloadSession.BulletId) == null)
            {
                self.InterruptInventoryReloadHotfix(true);
            }
        }

        public static void OnPlayerDie(this PlayerInventoryComponent self)
        {
            foreach (var (_, node) in self.SystemRoot)
            {
                if (node is not ItemContainerNode inventoryNode) continue;
                if (inventoryNode.Id == NodeConst.BeltItemContainerNodeId)
                {
                    foreach (var (_, innerNode) in inventoryNode)
                    {
                        if (innerNode is not BaseItemNode itemNode) continue;
                        var path = itemNode.GetPath();
                        if (ItemUtility.IsLinkEntityPath(path))
                            self.SyncInfoFromEntity(itemNode);
                    }
                }
            }
        }

        [Hotfix(NeedWrapper = true)]
        public static bool AddRebornItem(this PlayerInventoryComponent self)
        {
            using var _ = NodeOpContext.GetNew(OpContextConst.ADD_REBORN_ITEM);
            var rebornItem = McCommon.Tables.TbRebornData.GetOrDefault(self.RebornTemplateId).RebornItem;
            foreach (var itemData in rebornItem)
            {
                var result = self.Root.MergeNode(new NodeOpByBizId(itemData.ItemId, itemData.Count));
                if (result != EOpCode.Success) return false;
                else self.HasEnterInventoryItems.Add(itemData.ItemId);
            }
            return true;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool CalcIgnoreTips(this PlayerInventoryComponent self)
        {
            if (self.LootingEntity is PartEntity part)
            {
                return part.BaseComponent.BuildingConfig.PickupRequaireEmptyContainer == 1;
            }
            return false;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool IsInLootingRange(this PlayerInventoryComponent self, List<long> path)
        {
            if (path.Count < 1) return false;
            var targetSystemType = path[0];
            if (targetSystemType == NodeSystemType.PlayerInventory || targetSystemType == NodeSystemType.SeedBackpackSystem) return true;
            if (self.Root.MountedSystemIds.Contains(targetSystemType) && self.Player.ComponentLooting.InLootingRange(self.Player.ComponentLooting.CurrentLootingEntity, false)) return true;
            return false;
        }

        public static bool CanOperatePath(this PlayerInventoryComponent self, List<long> path)
        {
            // 检查距离
            if (!self.IsInLootingRange(path)) return false;
            // 检查路径是否可以操作

            var lootingEntity = self.Player.ComponentLooting.CurrentLootingEntity;
            // 马的装备栏只能通过特定接口操作，其余的操作都不允许
            if (lootingEntity is HorseEntity horseEntity && path.Count > 1 && path[1] == HorseContainerIndex.Equip)
            {
                return false;
            }

            return true;
        }

        public static EOpCode DoMoveItemToPath(this PlayerInventoryComponent self, BaseItemNode srcItemNode, List<long> srcPath, List<long> targetPath)
        {
            EOpCode code = EOpCode.ServerInternalError;
            var ctx = NodeOpContext.GetCurrent().SetBattleItemFlowType(EBattleItemFlowType.NoChange);
            do
            {
                using var transaction = EntityTransaction.Start($"DoMoveItemToPath-{self.RoleId}-{srcPath.PrintPath()}-{srcItemNode.BizId}-{targetPath.PrintPath()}");
                ctx.NotChange = srcPath[0] == targetPath[0];
                if (PathUtil.IsPathInOtherPlayerOrAutoTurret(srcPath) || PathUtil.IsPathInOtherPlayerOrAutoTurret(targetPath))
                {
                    ctx.SetBattleItemFlowType(EBattleItemFlowType.Change);
                }

                //获取位置上的物品
                code = srcItemNode.RequireSelf();
                if (code != EOpCode.Success)
                {
                    transaction.Rollback($"[DoMoveItemToPath] Require source item fail. RoleId {self.RoleId}, Path: {string.Join(".", srcPath)} -> {string.Join(".", targetPath)}, Code: {code}");
                    break;
                }

                //加入到目标位置中
                (code, _) = self.MoveItemIntoContainer(srcItemNode, srcPath, targetPath);
                //如果没完全合并，还剩下一点，就将剩下的物品放回
                if (code == EOpCode.ItemSystemStillNeedMerge)
                {
                    transaction.Commit();
                    code = EOpCode.Success; // 移动了一部分也当作成功了
                }
                else if (code == EOpCode.Success)
                {
                    transaction.Commit();
                }
                //其他问题，返回失败
                else
                {
                    if (code == EOpCode.ItemSystemFull || code == EOpCode.ItemSystemCannotAccept || code == EOpCode.ItemSystemCannotStack) transaction.IsLogInfoWhenRollback = true;
                    transaction.Rollback($"[DoMoveItemToPath] Merge source item fail. RoleId {self.RoleId}, Path: {string.Join(".", srcPath)} -> {string.Join(".", targetPath)}, Code: {code}");
                }
            } while (false);
            return code;
        }

        private static void MoveItemInSnapshotInterval(this PlayerInventoryComponent self, List<long> srcPath, List<long> targetPath, string source, bool ignoreTips)
        {
            if (self.Root.GetNodeByPath(srcPath) is not BaseItemNode srcItemNode)
            {
                return;
            }

            if (ItemUtility.IsLinkEntityPath(srcPath))
            {
                self.SyncInfoFromEntity(srcItemNode);
            }

            self.Logger.Info($"MoveItemInSnapshotInterval {srcPath.Print()} {targetPath.Print()}");
            using var _ = NodeOpContext.GetNew(source).SetOpRoleId(self.RoleId).SetIgnoreOpTips(ignoreTips);
            var code = self.DoMoveItemToPath(srcItemNode, srcPath, targetPath);
            self.RemoteCallMoveItemToPathAck(ERpcTarget.OwnClient, srcPath, targetPath, (int)code);
        }

        public static void MoveItemToPathInner(this PlayerInventoryComponent self, BaseItemNode srcItemNode, List<long> srcPath, List<long> targetPath, string source)
        {
            if (NodeHelper.IsPathInvaild(targetPath))
            {
                self.Logger.Error($"[MoveItemToPathInner] Target path invaild. RoleId {self.RoleId}, Path: {string.Join(".", targetPath)}");
                return;
            }

            if (NodeMisc.IsReloadingItem(srcItemNode.ParentId, self.ParentId))
            {
                // todo@lite 把2013导一个常量
                self.Player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, 2013);
                return;
            }

            bool ignoreTips = false;
            if (PathUtil.IsChangingEquip(srcPath, targetPath))
            {
                source = OpContextConst.EQUIP;
            }
            else if (PathUtil.IsPathInSafetyBox(srcPath) || PathUtil.IsPathInSafetyBox(targetPath))
            {
                source = OpContextConst.CUPBOARD_SAFE;
            }
            else if (source == "")
            {
                if (!PathUtil.IsPathInInventory(srcPath) || !PathUtil.IsPathInInventory(targetPath))
                {
                    source = self.GetOpContextWhenLooting(self.LootingEntity);
                }
                else source = "MoveItemToPath";
            }

            using var ctx = NodeOpContext.GetNew(source).SetOpRoleId(self.RoleId).SetIgnoreOpTips(ignoreTips);
            //特殊处理背包移动
            if (srcItemNode is ExtraPackItemNode extraPack)
            {
                if (ItemUtility.IsPathPointingWearingExtraPack(srcPath) || ItemUtility.IsPathPointingWearingExtraPack(targetPath))
                {
                    self.Logger.Info($"[MoveItemToPath] path point to extra pack slot in wear container. Move to MoveExtraPack. RoleId {self.RoleId}, Src: {string.Join(".", srcPath)}, Tar: {string.Join(".", targetPath)}");
                    self.MoveExtraPackInner(srcPath, targetPath, false);
                    return;
                }

                if (PathUtil.PathEqual(targetPath, NodePathConst.AnywhereInPlayerInventory) && self.Root.GetNodeByPath(PlayerBackpackPath) is not ExtraPackItemNode)
                {
                    self.MoveExtraPackInner(srcPath, new List<long> { NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Wear, (long)EquipPart.ExtraPack - 1 }, false);
                    return;
                }
                if (PathUtil.PathEqual(targetPath, NodePathConst.AnywhereInOtherPlayerInventory) && self.Root.GetNodeByPath(OtherPlayerBackpackPath) is not ExtraPackItemNode)
                {
                    self.MoveExtraPackInner(srcPath, new List<long> { NodeSystemType.OtherPlayerInventory, PlayerInventoryNodeIndex.Wear, (long)EquipPart.ExtraPack - 1 }, false);
                    return;
                }
            }

            // 含有耐久或子弹的道具需要帧间执行，从entity同步数据
            if (ItemUtility.IsLinkEntityPath(srcPath))
            {
                ProcessEntity.Instance.AddSnapshotIntervalLogicCallback(self.ParentId, (entity) => (entity as PlayerEntity).GetComponent<PlayerInventoryComponent>(EComponentIdEnum.PlayerInventoryComponentId)?.MoveItemInSnapshotInterval(srcPath, targetPath, source, ignoreTips));
                return;
            }

            var code = self.DoMoveItemToPath(srcItemNode, srcPath, targetPath);

            if (code == EOpCode.ItemSystemCannotInput)
            {
                self.Player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, CommonTipConst.ItemsCannotBeDeposited);
            }
            self.RemoteCallMoveItemToPathAck(ERpcTarget.OwnClient, srcPath, targetPath, (int)code);
        }

        private static void SyncInfoFromEntity(this PlayerInventoryComponent self, BaseItemNode itemNode)
        {
            if (EmbeddedCustomManager.Instance.GetEmbedded(itemNode.Id) is not IItemEntity itemEntity)
            {
                self.Logger.Warn($"Trying to get info from entity, but IItemEntity {itemNode.Id} not found on {self.ParentId}");
                return;
            }

            if (itemNode is IHaveBulletItemNode ihbin)
            {
                if (itemEntity is not IHaveBulletEntity bulletEntity)
                {
                    self.Logger.Warn($"Trying to get info from entity, but IHaveBulletEntity {itemNode.Id} not found on {self.ParentId}");
                }
                else
                {
                    ihbin.BulletBizId = bulletEntity.UsingAmmoId;
                    ihbin.BulletAmount = bulletEntity.Clips;
                    ihbin.SubClipConsume = bulletEntity.SubClipConsume;
                }
            }
        }

        public static EOpCode ExchangeItemInner(this PlayerInventoryComponent self, List<long> srcPath, List<long> targetPath)
        {
            EOpCode code = EOpCode.ServerInternalError;
            using (var transaction = EntityTransaction.Start($"ExchangeItem-{self.RoleId}-{srcPath.PrintPath()}-{targetPath.PrintPath()}"))
            {
                (code, var resultStr) = self.ExchangeItemInTransaction(srcPath, targetPath);
                if (code == EOpCode.Success) transaction.Commit();

                else
                {
                    if (code == EOpCode.ItemSystemCannotAccept) transaction.IsLogInfoWhenRollback = true;
                    transaction.Rollback(resultStr);
                }
            }
            return code;
        }

        private static (EOpCode, string) ExchangeItemInTransaction(this PlayerInventoryComponent self, List<long> srcPath, List<long> targetPath)
        {
            //取出双方物品
            var ctx = NodeOpContext.GetCurrent().SetOpRoleId(self.RoleId).SetBattleItemFlowType(EBattleItemFlowType.NoChange);
            if (PathUtil.IsPathInOtherPlayerOrAutoTurret(srcPath) || PathUtil.IsPathInOtherPlayerOrAutoTurret(targetPath))
            {
                ctx.SetBattleItemFlowType(EBattleItemFlowType.Change);
            }
            ctx.NotChange = srcPath[0] == targetPath[0];
            var code = self.Root.RequireNode(srcPath);
            if (code != EOpCode.Success)
            {
                return (code, $"[ExchangeItem] Require source item fail. RoleId {self.RoleId}, Path: {srcPath.PrintPath()}, Code: {code}");
            }
            var srcRequireNode = ctx.RequireList.Last();

            code = self.Root.RequireNode(targetPath);
            if (code != EOpCode.Success)
            {
                return (code, $"[ExchangeItem] Require target item fail. RoleId {self.RoleId}, Path: {targetPath.PrintPath()}, Code: {code}");
            }
            var tarRequireNode = ctx.RequireList.Last();

            // 强制同步，避免出现时序问题
            self.RemoteCallEmptyRpc(ERpcTarget.OwnClient);

            //往对方格子里装入
            var code1 = self.Root.MergeNode(new NodeOpByIndex(targetPath, new ExistingNode(srcRequireNode)));
            var code2 = self.Root.MergeNode(new NodeOpByIndex(srcPath, new ExistingNode(tarRequireNode)));

            //处理没完全合并的问题，放回到系统内任意位置，如果出现了其他问题，直接回滚
            if (code1 == EOpCode.ItemSystemStillNeedMerge || code1 == EOpCode.ItemSystemFull)
            {
                code = self.Root.MergeNode(
                    new NodeOpByIndex(srcPath.First(), NodePathConst.Anywhere).WithDetail(new ExistingNode(srcRequireNode)));
                //放回一定要求完全放回，否则就是失败
                if (code != EOpCode.Success)
                {
                    return (code, $"[ExchangeItem] Return Merge source item fail. RoleId {self.RoleId}, Path: {srcPath.First()}.{NodePathConst.Anywhere}, Code: {code}");
                }
            }
            else if (code1 != EOpCode.Success)
            {
                return (code1, $"[ExchangeItem] Merge source item fail. RoleId {self.RoleId}, Path: {targetPath.PrintPath()}, Code: {code1}");
            }
            if (code2 == EOpCode.ItemSystemStillNeedMerge || code2 == EOpCode.ItemSystemFull)
            {
                code = self.Root.MergeNode(
                    new NodeOpByIndex(targetPath.First(), NodePathConst.Anywhere).WithDetail(new ExistingNode(tarRequireNode)));
                //放回一定要求完全放回，否则就是失败
                if (code != EOpCode.Success)
                {
                    return (code, $"[ExchangeItem] Return Merge target item fail. RoleId {self.RoleId}, Path: {targetPath.First()}.{NodePathConst.Anywhere}, Code: {code})");
                }
            }
            else if (code2 != EOpCode.Success)
            {
                return (code2, $"[ExchangeItem] Merge target item fail. RoleId {self.RoleId}, Path: {srcPath.PrintPath()}, Code: {code2}");
            }

            //一切处理完毕，返回成功
            return (EOpCode.Success, "");
        }

        internal static EOpCode SplitItemBase(this PlayerInventoryComponent self, List<long> srcPath, bool toMine, int amount)
        {
            var itemNode = self.Root.GetNodeByPath(srcPath) as StackableItemNode;
            if (itemNode == null) return EOpCode.NodeNotExist;

            if (amount > itemNode.Count)
            {
                self.Logger.Info($"SplitItem Fail! count illegal! {amount} > {itemNode.Count}");
                return EOpCode.CountNotEnough;
            }

            var source = "SplitItem";
            var ignoreTips = false;
            if (PathUtil.IsPathInSafetyBox(srcPath))
            {
                source = OpContextConst.CUPBOARD_SAFE;
            }
            else if (!PathUtil.IsPathInSeedBackPack(srcPath) && (PathUtil.IsPathInInventory(srcPath) ^ toMine))
            {
                ignoreTips = self.CalcIgnoreTips();
                source = self.GetOpContextWhenLooting(self.LootingEntity);
            }
            using var ctx = NodeOpContext.GetNew(source).SetOpRoleId(self.RoleId).SetIgnoreOpTips(ignoreTips).SetItemNodeBelongRoleId(itemNode.BelongRoleId).SetBattleItemFlowType(EBattleItemFlowType.NoChange);
            using var transaction = EntityTransaction.Start($"SplitItem-{itemNode.Id}-{amount}-{toMine}");

            bool rejectMerge = !(toMine ^ (PathUtil.IsPathInInventory(srcPath))) || PathUtil.IsPathInSeedBackPack(srcPath);
            ctx.NotChange = rejectMerge;

            var nodeOp = new NodeOpByBizId(itemNode.BizId, amount);
            if (rejectMerge) nodeOp.RejectMerge();
            nodeOp.CloneSource(itemNode);

            if (!toMine)
            {
                var lootEnt = self.LootingEntity;
                // 兼容代码
                var lootingSystemId = self.Player.GetComponent<PlayerLootingComponent>(EComponentIdEnum.PlayerLootingComponentId).GetCurrentLootingSystemId();
                if (lootingSystemId == 0) return EOpCode.NotLootingContainer;
                nodeOp.WithOverrideSystem(lootingSystemId);
            }
            else
            {
                nodeOp.WithOverrideSystem(PathUtil.IsPathInSeedBackPack(srcPath) ? NodeSystemType.SeedBackpackSystem : NodeSystemType.PlayerInventory);
                if (PathUtil.IsPathInOtherPlayerOrAutoTurret(srcPath))
                {
                    ctx.ItemNodeFlowType = EBattleItemFlowType.Change;
                }
            }

            var ret = self.Root.RequireNode(itemNode, amount);
            if (ret != EOpCode.Success)
            {
                transaction.Rollback($"RequireNode fail {ret}");
                return ret;
            }

            nodeOp.Options.OversizeOp = EOversizeOptionEnum.Drop;
            var code = self.Root.MergeNode(nodeOp);
            if (code != EOpCode.Success)
            {
                transaction.Rollback($"Merge fail {code}");
                return code;
            }
            transaction.Commit();
            return EOpCode.Success;
        }

        public static void DropItemInner(this PlayerInventoryComponent self, List<long> itemPath, int count, bool hasConfirm, float vx, float vy, float vz)
        {
            if (count <= 0) return;

            if (self.Root.GetNodeByPath(itemPath) is not BaseItemNode itemNode)
            {
                return;
            }

            if (ItemUtility.IsPathPointingWearingExtraPack(itemPath))
            {
                self.DropExtraPack(itemPath, hasConfirm);
                return;
            }

            // 丢弃物管控
            if (!DropItemControl(self))
            {
                self.Logger.Debug($"[DropItem] DropItemControl failed. Player: {self.Player.RoleId}");
                return;
            }

            // 含有耐久或子弹的道具需要帧间执行，从entity同步数据
            if (ItemUtility.IsLinkEntityPath(itemPath))
            {
                ProcessEntity.Instance.AddSnapshotIntervalLogicCallback(self.ParentId, (entity) => (entity as PlayerEntity).GetComponent<PlayerInventoryComponent>(EComponentIdEnum.PlayerInventoryComponentId)?.DropItemInSnapshotInterval(itemPath, count, vx, vy, vz));
                return;
            }
            self.Logger.Info($"DropItem {itemNode.Id} {count}");
            using var _ = NodeOpContext.GetNew(OpContextConst.PICK_UP).SetOpRoleId(self.Player.RoleId).SetDropOperation(true);
            self.DoDropItem(itemPath, count, vx, vy, vz);
        }

        private static void DoDropItem(this PlayerInventoryComponent self, List<long> itemPath, int count, float velocityX, float velocityY, float velocityZ)
        {
            var ctx = NodeOpContext.GetCurrent().SetBattleItemFlowType(EBattleItemFlowType.NoChange);
            var code = self.Root.RequireNode(new NodeOpByIndex(itemPath, new IntCount(count)));
            if (code == EOpCode.Success)
            {
                var requiredNode = ctx.RequireList.Last() as BaseItemNode;
                requiredNode.BelongRoleId = self.Player.RoleId;
                requiredNode.Drop(self.SystemRoot, velocityX, velocityY, velocityZ);
                self.Logger.Info($"[DropItemInner]Success. Player: {self.Player.RoleId}, Path: {string.Join('.', itemPath)}, Item: {requiredNode.Id}");
            }
            else
            {
                self.Logger.Warn($"[DropItemInner]Failed with code {code}. Player: {self.Player.RoleId}, Path: {string.Join('.', itemPath)}");
            }
        }

        static int doDeductAmmo(this PlayerInventoryComponent self, EntityTransaction current, BaseItemNode srcItemNode, int maxReloadAmount, long bulletBizId, out int deductedAmount)
        {
            deductedAmount = 0;
            if (srcItemNode.GetCount() > maxReloadAmount)
            {
                var ret = self.Root.RequireNode(srcItemNode, maxReloadAmount);
                if (ret != EOpCode.Success)
                {
                    self.Logger.Error($"RequireNode {srcItemNode} fail {ret}");
                    current.Rollback($"{ret}");
                    return 1;
                }
                deductedAmount += maxReloadAmount;
            }
            else
            {
                deductedAmount += srcItemNode.GetCount();
                var ret = self.Root.RequireNode(srcItemNode);
                if (ret != EOpCode.Success)
                {
                    self.Logger.Error($"RequireNode {srcItemNode} fail {ret}");
                    current.Rollback($"{ret}");
                    return 1;
                }

                // 再看看还有没有子弹了
                var bulletInInv = self.Root.GetNodeValByPath(NodePathConst.VPlayerInventoryMain, bulletBizId);
                if (bulletInInv > 0)
                {
                    var amount = Math.Min(bulletInInv, maxReloadAmount - deductedAmount);
                    ret = self.Root.RequireNode(bulletBizId, amount);
                    if (ret == EOpCode.Success)
                    {
                        deductedAmount += amount;
                    }
                    else
                    {
                        self.Logger.Warn($"Reload require more bullet fail {ret}");
                    }
                }
            }
            return 0;
        }

        private static List<PartEntity> queryPartResult = new();

        private static bool IsNeedNearWorkbench(this PlayerInventoryComponent self, IHaveBulletItemNode ihbin)
        {
            if (null == ihbin)
            {
                return false;
            }

            if (!McCommon.Tables.TbGunBase.DataMap.TryGetValue(ihbin.ItemConfig.Id, out var config))
            {
                return false;
            }

            return config.ChangeClipNearWorkbench;
        }

        private static bool IsNearWorkbench(this PlayerInventoryComponent self)
        {
            queryPartResult.Clear();
            var raduis = McCommon.Tables.TbGlobalConfig.WorkBenchSearchDistance;

            EntityManager.Instance.OctreeManager.FindPartEntityByBoundingBox(queryPartResult, new Bounds(new Vector3(self.Player.PosX, self.Player.PosY, self.Player.PosZ), new Vector3(raduis, raduis, raduis)));
            if (queryPartResult.Count <= 0)
            {
                return false;
            }

            foreach (var part in queryPartResult)
            {
                if (part.TemplateId == (long)PartType.Tier1WorkBench
                    || part.TemplateId == (long)PartType.Tier2WorkBench
                    || part.TemplateId == (long)PartType.Tier3WorkBench
                    || part.TemplateId == (long)PartType.Tier1WorkBench_v1
                    || part.TemplateId == (long)PartType.Tier2WorkBench_v1
                    || part.TemplateId == (long)PartType.Tier3WorkBench_v1)
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 返回false表示继续检查交换逻辑，返回true表示请求被背包换弹拦截，无需继续检查
        /// </summary>
        private static bool TryReloadInsideInventory(this PlayerInventoryComponent self, BaseItemNode srcItemNode, BaseItemNode targetItemNode)
        {
            // 正在做背包换弹时跳过这些逻辑判断，按正常的exchange走
            if (self.reloadSession != null) return false;
            // 目标必须是武器
            if (targetItemNode is not IHaveBulletItemNode ihbin) return false;
            // 武器必须在主背包
            if (ihbin.ParentId != NodeConst.MainItemContainerNodeId) return false;

            var pe = self.Player;
            // 手持道具不能通过背包reload
            if (ihbin.ParentId == NodeConst.BeltItemContainerNodeId && pe.CurrentWeaponId == ihbin.Id)
                return false;

            var bulletBizId = srcItemNode.BizId;
            // src必须是可以装备到target上的子弹
            if (!ihbin.IsBulletIdAllowed(bulletBizId)) return false;

            int clipCapacity;
            if (!ihbin.IsBulletIdAllowed(srcItemNode.BizId)) return false;

            // 同种子弹，且弹药已满，就不触发换弹
            if (ihbin.ParentId == NodeConst.BeltItemContainerNodeId)
            {
                // 已装备的，找entity数据
                // 是不准确的，最终还是要看simulator
                if (pe.GetWeaponByEntityId(ihbin.Id) is WeaponCustom we)
                {
                    clipCapacity = we.ClipCapacity;
                    if (we.Clips >= clipCapacity && we.UsingAmmoId == srcItemNode.BizId) return false;
                }
                else if (pe.GetCustomTypeById(ihbin.Id) is MeleeCustom me)
                {
                    clipCapacity = me.ClipCapacity;
                    if (me.Clips >= clipCapacity && me.UsingAmmoId == srcItemNode.BizId) return false;
                }
                else
                {
                    self.Logger.Warn($"IHaveBulletItemNode {ihbin.Id} not found on entity");
                    return false;
                }
            }
            else
            {
                clipCapacity = ihbin.ClipCapacity;
                if (ihbin.BulletAmount >= clipCapacity && ihbin.BulletBizId == srcItemNode.BizId) return false;
            }

            // 检查背包换弹是否需要靠近工作台
            if (self.IsNeedNearWorkbench(ihbin) && !self.IsNearWorkbench())
            {
                self.Player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, CommonTipConst.ReloadNearWorkbench);
                return true;
            }

            // 这里往下就返回true，不走exchange逻辑了
            using var __ = NodeOpContext.GetNew(OpContextConst.INVENTORY_RELOAD).SetOpRoleId(self.RoleId).SetBattleItemFlowType(EBattleItemFlowType.NoChange);

            long bulletId = 0;
            var deductedAmount = 0;
            // 先扣除子弹
            int maxReloadAmount;
            if (ihbin.BulletBizId == bulletBizId)
                maxReloadAmount = clipCapacity - ihbin.BulletAmount;
            else
                maxReloadAmount = clipCapacity;

            if (maxReloadAmount <= 0)
            {
                self.Logger.Info("Cannot reload any more");
                return true;
            }

            using var transaction = EntityTransaction.Start($"{OpContextConst.INVENTORY_RELOAD}-{self.RoleId}-{srcItemNode.Id}-{targetItemNode.Id}");
            // 扣除子弹
            var ret = doDeductAmmo(self, transaction, srcItemNode, maxReloadAmount, bulletBizId, out deductedAmount);
            if (ret == 1) return true;

            // 扣除后看看背包是否放得下添加上的子弹
            if (ihbin.BulletBizId != bulletBizId && ihbin.BulletAmount > 0)
            {
                // 检测背包是否满
                // 如果子弹满了，直接弹提示不让换弹
                var code = self.Root.MergeNode(ihbin.BulletBizId, ihbin.BulletAmount);
                transaction.Rollback("try reload", false);
                if (code != EOpCode.Success)
                {
                    self.Player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, 3008);
                    // 不再执行交换逻辑
                    return true;
                }

                using var transaction2 = EntityTransaction.Start($"{OpContextConst.INVENTORY_RELOAD}-{self.RoleId}-{srcItemNode.Id}-{targetItemNode.Id}");
                ret = doDeductAmmo(self, transaction2, srcItemNode, maxReloadAmount, bulletBizId, out deductedAmount);
                if (ret != 0) return true;
                else transaction2.Commit();
            }
            else
            {
                transaction.Commit();
                // bulletId = 0;
            }

            self.Logger.Info($"Start inventory reload {bulletBizId} {targetItemNode.Id}");
            self.reloadSession = new()
            {
                BulletId = bulletId, // 未扣除时bulletId含有有效值，否则为0
                BulletBizId = bulletBizId,
                WeaponId = targetItemNode.Id,
                DeductedAmount = deductedAmount,
                TimerId = 0,
            };

            if (bulletId != 0)
                ItemContainerNode.AddReloadingItem(bulletId, self.ParentId);

            ItemContainerNode.AddReloadingItem(targetItemNode.Id, self.ParentId);
            var reloadTime = ihbin.ExpectReloadTime(deductedAmount);
            self.RemoteCallStartInventoryReload(ERpcTarget.OwnClient, srcItemNode.Id, targetItemNode.Id, reloadTime);
            self.reloadSession.TimerId = self.AddTimerOnce((uint)reloadTime, DoInventoryReloadHotfix);
            return true;
        }

        private static EOpCode TakeOffClothInnerTransaction(this PlayerInventoryComponent self, BaseItemNode itemNode)
        {
            NodeOpContext.GetCurrent().SetBattleItemFlowType(EBattleItemFlowType.NoChange);
            var code = self.Root.RequireNode(itemNode);
            if (code != EOpCode.Success) return code;

            code = self.Root.MergeNode(new NodeOpByIndex(NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Main, NodePathConst.Anywhere).WithDetail(new ExistingNode(itemNode)));
            if (code == EOpCode.Success) return EOpCode.Success;

            return self.Root.MergeNode(new NodeOpByIndex(NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Belt, NodePathConst.Anywhere).WithDetail(new ExistingNode(itemNode)));
        }

        private static void ExchangeItemInSnapshotInterval(this PlayerInventoryComponent self, List<long> srcPath, List<long> targetPath, string source, bool ignoreTips)
        {
            if (self.Root.GetNodeByPath(srcPath) is not BaseItemNode srcItemNode)
            {
                return;
            }
            if (self.Root.GetNodeByPath(targetPath) is not BaseItemNode targetItemNode)
            {
                return;
            }

            if (ItemUtility.IsLinkEntityPath(srcPath))
            {
                self.SyncInfoFromEntity(srcItemNode);
            }
            if (ItemUtility.IsLinkEntityPath(targetPath))
            {
                self.SyncInfoFromEntity(targetItemNode);
            }

            self.Logger.Info($"ExchangeItemInSnapshotInterval {srcPath.PrintPath()} <=> {targetPath.PrintPath()} {srcItemNode.Id} {targetItemNode.Id}");
            using var _ = NodeOpContext.GetNew(source).SetOpRoleId(self.RoleId).SetIgnoreOpTips(ignoreTips);
            var code = self.ExchangeItemInner(srcPath, targetPath);
            self.RemoteCallExchangeItemAck(ERpcTarget.OwnClient, srcPath, targetPath, (int)code);
        }

        [RpcHandler(ExposeToClient = true)]
        public static void ClientInterruptReload(this PlayerInventoryComponent self) => self.InterruptInventoryReload(false);

        [Hotfix(NeedWrapper = true)]
        public static void InterruptInventoryReload(this PlayerInventoryComponent self, bool notify)
        {
            if (self.reloadSession == null)
                return;

            var rs = self.reloadSession;
            self.reloadSession = null;
            if (rs.BulletId == 0)
            {
                NodeOpContext.GetCurrent().SetItemNodeBelongRoleId(self.Player.RoleId);
                var opData = new NodeOpByBizId(rs.BulletBizId, rs.DeductedAmount).WithOption(EOversizeOptionEnum.Drop);
                var ret = self.Root.MergeNode(opData);
                if (ret != EOpCode.Success) self.Logger.Warn($"InterruptInventoryReload give back ammo fail {ret}");
            }
            else
                ItemContainerNode.RemoveReloadingItem(rs.BulletId, self.ParentId);

            ItemContainerNode.RemoveReloadingItem(rs.WeaponId, self.ParentId);

            self.SafeCancelTimer(ref rs.TimerId);
            if (notify)
                self.RemoteCallInterruptInventoryReload(ERpcTarget.OwnClient, rs.BulletBizId, rs.WeaponId);
        }

        [Hotfix(TimerCallback = true)]
        private static void DoInventoryReload(this PlayerInventoryComponent self, long _)
        {
            var rs = self.reloadSession;
            self.reloadSession = null;

            ItemContainerNode.RemoveReloadingItem(rs.WeaponId, self.ParentId);
            if (rs.BulletId != 0)
            {
                ItemContainerNode.RemoveReloadingItem(rs.BulletId, self.ParentId);
                // 发Simulator执行换弹
                self.Logger.Info($"Inventory reload to simulator {rs.BulletBizId} {rs.BulletId}");
                self.Player.RemoteCallOnInventoryChangeBullet(ERpcTarget.Simulator, rs.WeaponId, rs.BulletBizId, rs.BulletId);
                return;
            }

            var targetNode = self.Root.GetNodeById(rs.WeaponId) as BaseItemNode;
            var haveBulletItem = targetNode as IHaveBulletItemNode;

            using var __ = NodeOpContext.GetNew(OpContextConst.INVENTORY_RELOAD).SetOpRoleId(self.RoleId).SetItemNodeBelongRoleId(targetNode.BelongRoleId);
            var targetBulletAmount = rs.DeductedAmount;
            // 返还子弹
            if (haveBulletItem.BulletAmount > 0)
            {
                if (haveBulletItem.BulletBizId != rs.BulletBizId)
                {
                    var opData = new NodeOpByBizId(haveBulletItem.BulletBizId, haveBulletItem.BulletAmount).WithOption(EOversizeOptionEnum.Drop);
                    var ret = self.Root.MergeNode(opData);
                    if (ret != EOpCode.Success)
                    {
                        self.Logger.Warn($"InterruptInventoryReload give back ammo fail {ret}");
                    }
                }
                else
                {
                    targetBulletAmount += haveBulletItem.BulletAmount;
                }
            }

            // 装载子弹
            self.Logger.Info($"Inventory reload finish {rs.BulletBizId} {rs.DeductedAmount}");
            haveBulletItem.BulletBizId = (int)rs.BulletBizId;
            haveBulletItem.BulletAmount = targetBulletAmount;

            if (self.Player.CurrentNetPeerId > 0)
            {
                self.Player.RemoteCallPopItemMessageDoubleParam(ERpcTarget.OwnClient, CommonTipConst.LoadWeaponRelated, targetNode.BizId, rs.BulletBizId);
                self.Player.RemoteCallHandWeaponReloadFinish(ERpcTarget.OwnClient, ProcessEntity.Instance.Sequence);
            }
        }

        private static void DropItemInSnapshotInterval(this PlayerInventoryComponent self, List<long> itemPath, int count, float vx, float vy, float vz)
        {
            if (self.Root.GetNodeByPath(itemPath) is not BaseItemNode itemNode)
            {
                return;
            }
            self.Logger.Info($"DropItemInSnapshotInterval {itemNode.Id}");
            self.SyncInfoFromEntity(itemNode);
            using var _ = NodeOpContext.GetNew(OpContextConst.PICK_UP).SetOpRoleId(self.RoleId).SetDropOperation(true);
            self.DoDropItem(itemPath, count, vx, vy, vz);
        }

        private static (EOpCode, int) MoveItemIntoContainer(this PlayerInventoryComponent self, BaseItemNode srcItemNode, List<long> srcPath, List<long> targetPath)
        {
            var ctx = NodeOpContext.GetCurrent();
            //加入到目标位置中
            var originCount = srcItemNode.GetCount();
            var code = self.Root.MergeNode(new NodeOpByIndex(targetPath, new ExistingNode(srcItemNode)));
            //如果没完全合并，还剩下一点，就将剩下的物品放回
            if (code == EOpCode.ItemSystemStillNeedMerge || code == EOpCode.ItemSystemFull)
            {
                var moved = originCount - srcItemNode.GetCount();
                ctx.SetServerOperation(true);
                code = self.Root.MergeNode(new NodeOpByIndex(srcPath, new ExistingNode(srcItemNode)));
                ctx.SetServerOperation(false);
                // moved == 0 说明没有merge, 返回失败
                if (code == EOpCode.Success)
                {
                    if (moved == 0) return (EOpCode.ItemSystemFull, 0);
                    return (EOpCode.ItemSystemStillNeedMerge, moved);
                }
                //放回一定要求完全放回，否则就是失败
                return (code, 0);
            }
            else if (code == EOpCode.Success) return (EOpCode.Success, originCount);
            return (code, 0);
        }

        private static string GetOpContextWhenLooting(this PlayerInventoryComponent self, EntityBase entity)
        {
            var source = OpContextConst.LOOTING;
            if (null == entity) return source;

            StorageComponent comp = null;
            comp = entity.AbilityInvoke<GetStorageCompAbility, StorageComponent>(new GetStorageCompAbility());

            if (comp is OvenComponent)
            {
                source = OpContextConst.LOOTING_OVEN;
            }
            else if (comp is AutoSyncBoxComponent autoSyncBoxComponent)
            {
                if (autoSyncBoxComponent is CorpseBoxComponent)
                {
                    source = OpContextConst.LOOTING_PLAYER_DROP_REWARD_BOX;
                }
                else if (McCommon.Tables.TbMonster.GetOrDefault(autoSyncBoxComponent.CorpseHostTemplateId) != null)
                {
                    source = OpContextConst.LOOTING_MONSTER_DROP_REWARD_BOX;
                }
                else if (autoSyncBoxComponent.TemplateId == McCommon.Tables.TbConstructionConstantConfig.ItemDropBoxId)
                {
                    source = OpContextConst.LOOTING_BUILDING_DROP_REWARD_BOX;
                }
            }
            else if (comp is BoxComponent box && box.IsSpawn)
            {
                source = OpContextConst.LOOTING_SPAWN_BOX_OR_AIRDROP_BOX;
            }
            else if (comp is PlantBoxComponent)
            {
                source = OpContextConst.LOOTING_PLANT_BOX;
            }
            else if (comp is DecomposeMachineComponent)
            {
                source = OpContextConst.LOOTING_DECOMPOSER;
            }
            else if (comp is ComposterComponent)
            {
                source = OpContextConst.LOOTING_COMPOSTER;
            }
            else if (comp is ResearchBenchComponent)
            {
                source = OpContextConst.LOOTING_RESEARCH_BENCH;
            }
            else if (entity is DigEntity dig)
            {
                switch (dig.TemplateId)
                {
                    case DigComponent.FUEL_DIG:
                        {
                            source = OpContextConst.LOOTING_FUEL_DIG;
                            break;
                        }
                    case DigComponent.MINING_DIG:
                        {
                            source = OpContextConst.LOOTING_MINING_DIG;
                            break;
                        }
                    case DigComponent.QUARRY_DIG:
                        {
                            source = OpContextConst.LOOTING_QUARRY_DIG;
                            break;
                        }
                    case DigComponent.SULFUR_DIG:
                        {
                            source = OpContextConst.LOOTING_SULFUR_DIG;
                            break;
                        }
                    default: break;
                }
            }
            return source;
        }

        private static bool TransferToOthersideCheck(this PlayerInventoryComponent self, long bizId, int maxCount, List<long> srcPath, List<long> targetPath)
        {
            if (srcPath == null || targetPath == null) return false;
            if (maxCount <= 0) return false;
            if (PathUtil.PathEqual(srcPath, targetPath)) return false;
            if (!self.IsInLootingRange(srcPath) || !self.IsInLootingRange(targetPath)) return false;
            if (!McCommon.Tables.TbItemConfig.DataMap.ContainsKey(bizId)) return false;
            if (srcPath[0] == NodeSystemType.SafetyBox || targetPath[0] == NodeSystemType.SafetyBox) return false;
            if (self.Root.GetNodeByPath(srcPath) is not ItemContainerNode) return false;
            if (targetPath.Count != 3 && targetPath.Count != 2) return false;
            return true;
        }

        private static bool TransferToInventoryCheck(this PlayerInventoryComponent self, long bizId, int maxCount, List<long> srcPath, List<long> targetPath)
        {
            if (srcPath == null || targetPath == null) return false;
            if (maxCount <= 0) return false;
            if (PathUtil.PathEqual(srcPath, targetPath)) return false;
            if (!self.IsInLootingRange(srcPath) || !self.IsInLootingRange(targetPath)) return false;
            if (!McCommon.Tables.TbItemConfig.DataMap.ContainsKey(bizId)) return false;
            if (srcPath[0] == NodeSystemType.SafetyBox || targetPath[0] == NodeSystemType.SafetyBox) return false;
            if (self.Root.GetNodeByPath(srcPath) is not StackableItemNode) return false;
            var targetNode = self.Root.GetNodeByPath(targetPath);
            if (targetNode is not ItemContainerNode && targetNode.Id != NodeSystemType.PlayerInventory) return false;
            return true;
        }

        private static List<long> tempSearchPlayers = new();
        public static void PickUp(this PlayerInventoryComponent self, List<long> path, List<long> toPath, string source)
        {
            if (self.Root.GetNodeByPath(path) is not BaseItemNode itemNode)
            {
                // 不管，客户端乱传参数
                return;
            }
            self.Logger.Info($"PickUp {path.PrintPath()}");

            var oldParent = itemNode.ParentId;
            var oldAmount = itemNode.GetCount();
            var bizId = itemNode.BizId;
            self.MoveItemToPathInner(itemNode, path, toPath, source);

            // 道具被挪动了一定就是拾取成功了
            // 道具数量变化, 但是没有挪动, 也算拾取成功
            if (itemNode.ParentId != oldParent || itemNode.GetCount() != oldAmount)
            {
                var pickEvent = PickAndDropSuccessEvent.NewEvent();
                pickEvent.SourceId = self.ParentId;
                pickEvent.ItemTemplateId = bizId;
                pickEvent.PosX = self.Player.PosX;
                pickEvent.PosY = self.Player.PosY;
                pickEvent.PosZ = self.Player.PosZ;

                self.PickAndDropSound(new PickSoundEvent(pickEvent, false));
                self.RemoteCallPickCollectable(ERpcTarget.OwnClient, path[2]);
            }
        }

        [Hotfix(EventCallback = true)]
        public static void PickAndDropSound(this PlayerInventoryComponent self, PickSoundEvent soundEvent)
        {
            var player = self.Player;
            QuadTreeManager.Instance.SearchNearbyEntities(tempSearchPlayers, new Vector3(player.PosX, player.PosY, player.PosZ), PICK_DROP_SOUND_BROADCAST_RANGE);
            foreach (var playerId in tempSearchPlayers)
            {
                if (soundEvent.ExceptSelf && playerId == player.EntityId) continue;
                var p = EntityManager.Instance.GetPlayerEntity(playerId);
                if (p != null && p.HasClient)
                {
                    p.RemoteCallPickAndDropSound(ERpcTarget.OwnClient, soundEvent.PickEvent);
                }
            }
        }

        [Hotfix(EventCallback = true)]
        public static void StartLootingEventCallback(this PlayerInventoryComponent self, PlayerStartLootingEvent playerStartLootingEvent)
        {
            var player = self.Player;
            var templateId = playerStartLootingEvent.TemplateId;
            var treasureConfig = McCommon.Tables.TbTreasureBox.GetOrDefault(templateId);
            if (treasureConfig == null) return;
            var pickEvent = PickAndDropSuccessEvent.NewEvent();
            pickEvent.SourceId = self.ParentId;
            pickEvent.ItemTemplateId = templateId;
            pickEvent.PosX = self.Player.PosX;
            pickEvent.PosY = self.Player.PosY;
            pickEvent.PosZ = self.Player.PosZ;
            self.PickAndDropSound(new PickSoundEvent(pickEvent, true));
        }

        public static void GiveBackItems(this PlayerInventoryComponent self, Dictionary<long, int> consumes)
        {
            foreach (var (bizId, count) in consumes)
            {
                var opData = new NodeOpByIndex(NodePathConst.AnywhereInPlayerInventory, new BizIdWithCount(bizId, count)).WithOption(EOversizeOptionEnum.Drop);
                var ret = self.Root.MergeNode(opData);
                if (ret != EOpCode.Success)
                {
                    self.Logger.Error($"GiveBackItems merge {bizId} {count} {ret}");
                }
            }
        }

        /// <summary>
        /// 道具从一个路径移动到另一个路径
        /// </summary>
        /// <param name="srcPath">道具源路径</param>
        /// <param name="targetPath">道具目标路径</param>
        [RpcHandler(ExposeToClient = true, CallInterval = 0.1f)]
        static public void MoveItemToPath(this PlayerInventoryComponent self, List<long> srcPath, List<long> targetPath)
        {
            if (srcPath == null || targetPath == null) return;
            if (PathUtil.PathEqual(srcPath, targetPath)) return;
            if (!self.CanOperatePath(srcPath) || !self.CanOperatePath(targetPath)) return;
            var srcItem = self.Root.GetNodeByPath(srcPath);
            if (srcItem is BaseItemNode bin)
            {
                self.Logger.Info($"MoveItemToPath {srcPath.PrintPath()} {targetPath.PrintPath()}");
                self.MoveItemToPathInner(bin, srcPath, targetPath, "");
            }
            // else 不管，客户端乱传参数
        }

        /// <summary>
        /// 道具从一个路径移动到另一个路径，带二次确认
        /// 可用于扩展背包
        /// </summary>
        /// <param name="srcPath">道具源路径</param>
        /// <param name="targetPath">道具目标路径</param>
        [RpcHandler(ExposeToClient = true, CallInterval = 0.1f)]
        static public void MoveItemToPathWithConfirm(this PlayerInventoryComponent self, List<long> srcPath, List<long> targetPath)
        {
            if (srcPath == null || targetPath == null) return;
            if (PathUtil.PathEqual(srcPath, targetPath)) return;
            if (!self.CanOperatePath(srcPath) || !self.CanOperatePath(targetPath)) return;
            if (ItemUtility.IsPathPointingWearingExtraPack(srcPath) || ItemUtility.IsPathPointingWearingExtraPack(targetPath))
            {
                self.Logger.Info($"[MoveItemToPath] path point to extra pack slot in wear container. Move to MoveExtraPack. RoleId {self.RoleId}, Src: {string.Join(".", srcPath)}, Tar: {string.Join(".", targetPath)}");
                self.MoveExtraPackInner(srcPath, targetPath, true);
            }
        }

        /// <summary>
        /// 交换道具
        /// </summary>
        /// <param name="srcPath">道具路径</param>
        /// <param name="targetPath">交换道具路径</param>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void ExchangeItem(this PlayerInventoryComponent self, List<long> srcPath, List<long> targetPath)
        {
            if (srcPath == null || targetPath == null) return;
            if (PathUtil.PathEqual(srcPath, targetPath)) return;
            if (!self.CanOperatePath(srcPath) || !self.CanOperatePath(targetPath)) return;

            if (NodeHelper.IsPathInvaild(srcPath))
            {
                return;
            }
            if (NodeHelper.IsPathInvaild(targetPath))
            {
                return;
            }

            if (self.Root.GetNodeByPath(srcPath) is not BaseItemNode srcItemNode)
            {
                return;
            }
            if (self.Root.GetNodeByPath(targetPath) is not BaseItemNode targetItemNode)
            {
                return;
            }

            EOpCode code;
            do
            {
                if (self.TryReloadInsideInventory(srcItemNode, targetItemNode))
                {
                    code = EOpCode.EntitySideHandle;
                    break;
                }

                if (targetItemNode.GetParent() is not IItemContainerNode iicn)
                {
                    self.Logger.Error($"{LogHelper.NON_PUBLISH_REPORT} Target parent is not IItemContainerNode. Path: {targetPath.PrintPath()}, {targetItemNode.GetParent()}");
                    return;
                }

                // 不允许身上和地上交换东西
                if (srcPath[0] == NodeSystemType.PickUpSystem || targetPath[0] == NodeSystemType.PickUpSystem)
                {
                    return;
                }

                var source = "ExchangeItem";
                var ignoreTips = false;
                if (PathUtil.CalcIsLooting(srcPath, targetPath))
                {
                    source = OpContextConst.LOOTING;
                    ignoreTips = self.CalcIgnoreTips();
                }

                using var ctx = NodeOpContext.GetNew(source).SetOpRoleId(self.RoleId).SetIgnoreOpTips(ignoreTips);
                code = ItemSystemRootNodeHotfix.ItemContainerConfigCheck(iicn, srcItemNode, ctx);
                if (code != EOpCode.Success) break;
                code = self.SystemRoot.MoveInToSlotChecker(iicn, srcItemNode, ctx, targetPath.Last());
                if (code != EOpCode.Success) break;

                if (ItemUtility.IsPathPointingWearingExtraPack(srcPath) || ItemUtility.IsPathPointingWearingExtraPack(targetPath))
                {
                    code = self.MoveExtraPackInner(srcPath, targetPath, false);
                    break;
                }

                // 含有耐久或子弹的道具需要帧间执行，从entity同步数据
                var srcIsBeltWeapon = ItemUtility.IsLinkEntityPath(srcPath);
                var tarIsBeltWeapon = ItemUtility.IsLinkEntityPath(targetPath);
                if (srcIsBeltWeapon || tarIsBeltWeapon)
                {
                    ProcessEntity.Instance.AddSnapshotIntervalLogicCallback(self.ParentId,
                        (entity) => (entity as PlayerEntity).GetComponent<PlayerInventoryComponent>(EComponentIdEnum.PlayerInventoryComponentId).ExchangeItemInSnapshotInterval(srcPath, targetPath, source, ignoreTips));
                    code = EOpCode.EntitySideHandle;
                    break;
                }

                self.Logger.Info($"ExchangeItemBase {srcPath.PrintPath()} <=> {targetPath.PrintPath()} {srcItemNode.Id} {targetItemNode.Id}");
                code = self.ExchangeItemInner(srcPath, targetPath);
            } while (false);

            if (code != EOpCode.EntitySideHandle)
                self.RemoteCallExchangeItemAck(ERpcTarget.OwnClient, srcPath, targetPath, (int)code);
        }

        /// <summary>
        /// 拆分道具，待废弃
        /// </summary>
        /// <param name="srcPath">源路径</param>
        /// <param name="toMine">是否拆分到自己身上，false为异端容器（如有）</param>
        /// <param name="amount">数量</param>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void SplitItem(this PlayerInventoryComponent self, List<long> srcPath, bool toMine, int amount)
        {
            if (amount <= 0) return;
            if (!self.CanOperatePath(srcPath)) return;
            self.SplitItemBase(srcPath, toMine, amount);
        }

        /// <summary>
        /// 拆分道具
        /// </summary>
        /// <param name="srcPath">源路径</param>
        /// <param name="toMine">是否拆分到自己身上，false为异端容器（如有）</param>
        /// <param name="amount">数量</param>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void SplitItemToPath(this PlayerInventoryComponent self, List<long> srcPath, List<long> dstPath, int amount)
        {
            if (srcPath == null || dstPath == null) return;
            if (amount <= 0) return;

            if (self.Root.GetNodeByPath(srcPath) is not StackableItemNode itemNode)
                return;

            if (PathUtil.PathEqual(srcPath, dstPath)) return;
            if (!self.CanOperatePath(srcPath) || !self.CanOperatePath(dstPath)) return;

            var source = "SplitItem";
            var ignoreTips = false;
            if (PathUtil.CalcIsLooting(srcPath, dstPath))
            {
                source = OpContextConst.LOOTING;
                ignoreTips = self.CalcIgnoreTips();
            }
            using var ctx = NodeOpContext.GetNew(source).SetOpRoleId(self.RoleId).SetIgnoreOpTips(ignoreTips).SetItemNodeBelongRoleId(itemNode.BelongRoleId);
            using var transaction = EntityTransaction.Start($"SplitItemToPath-{itemNode.Id}-{amount}-{string.Join(".", dstPath)}");
            if (PathUtil.IsPathInOtherPlayerOrAutoTurret(srcPath) || PathUtil.IsPathInOtherPlayerOrAutoTurret(dstPath))
            {
                ctx.ItemNodeFlowType = EBattleItemFlowType.Change;
            }

            var ret = self.Root.RequireNode(itemNode, amount);
            if (ret != EOpCode.Success)
            {
                transaction.Rollback($"RequireNode fail {ret}");
                return;
            }
            var nodeOp = new NodeOpByIndex(dstPath, new BizIdWithCount(itemNode.BizId, amount)).WithCloneSource(itemNode);
            if (PathUtil.ContainerEqual(srcPath, dstPath)) nodeOp.RejectMerge();
            ret = self.Root.MergeNode(nodeOp);
            if (ret == EOpCode.Success)
            {
                transaction.Commit();
            }
            else
            {
                transaction.Rollback($"MergeNode fail {ret}");
            }
        }

        /// <summary>
        /// 整理主背包
        /// </summary>
        /// <param name="_"></param>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void SortInventory(this PlayerInventoryComponent self)
        {
            var path = new List<long>() { NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Main };
            if (self.Root.GetNodeByPath(path) is not ItemContainerNode itemContainer)
            {
                self.Logger.Error($"[SortInventory]Path is not IItemContainerNode. RoleId: {self.Player.RoleId}, Path: {path.PrintPath()}");
                return;
            }
            using var _ = NodeOpContext.GetNew("SortInventory");
            self.Player.SortContainer(itemContainer);
            if (self.LootingEntity != null)
            {
                if (self.LootingEntity.GetComponent<RootNodeComponent>(EComponentIdEnum.RootNodeComponent) is RootNodeComponent root)
                {
                    foreach (var (_, sn) in root)
                    {
                        if (sn is not SystemRootNode system) continue;
                        foreach (var (_, node) in system)
                        {
                            if (node is ItemContainerNode icn && icn.ContainerConfig.SupportSort == 1)
                            {
                                self.Player.SortContainer(icn);
                            }
                        }
                    }
                }
            }

            self.Logger.Info($"[SortInventory]Sort container success.");
            self.Player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, (int)ErrorCode.SuccessSortInventory);
        }

        /// <summary>
        /// 丢弃道具
        /// </summary>
        /// <param name="_"></param>
        /// <param name="itemPath">道具路径</param>
        /// <param name="count">数量</param>
        [RpcHandler(ExposeToClient = true, CallInterval = .1f)]
        public static void DropItem(this PlayerInventoryComponent self, List<long> itemPath, int count)
        {
            if (!self.CanOperatePath(itemPath)) return;
            self.DropItemInner(itemPath, count, false, 0, 0, 0);
        }

        /// <summary>
        /// 丢弃道具，并且已经是二次确认后
        /// 可用于扩展背包
        /// </summary>
        /// <param name="itemPath">道具路径</param>
        /// <param name="count">数量</param>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void DropItemWithConfirm(this PlayerInventoryComponent self, List<long> itemPath, int count)
        {
            if (!self.CanOperatePath(itemPath)) return;
            self.DropItemInner(itemPath, count, true, 0, 0, 0);
        }

        /// <summary>
        /// 倒水
        /// </summary>
        /// <param name="itemPath"></param>
        /// <param name="velocityX"></param>
        /// <param name="velocityY"></param>
        /// <param name="velocityZ"></param>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void PourWater(this PlayerInventoryComponent self, long itemId, float velocityX, float velocityY, float velocityZ)
        {
            var itemNode = self.Root.GetNodeById(itemId) as BaseItemNode;
            if (itemNode == null)
            {
                self.Logger.Error($"[PourWater] itemId {itemId} ");
                return;
            }

            var count = itemNode.GetCount();
            if (count <= 0)
            {
                self.Logger.Error($"[PourWater] count {count}");
                return;
            }

            var expectedCostCount = (int)Math.Ceiling(count * McCommon.Tables.TbPlantConstConfig.PourPercentage);
            expectedCostCount = expectedCostCount > count ? count : expectedCostCount;
            if (expectedCostCount <= 0)
            {
                self.Logger.Warn($"[PourWater] expectedCostCount {expectedCostCount}");
                return;
            }

            using var _ = NodeOpContext.GetNew("PourWater").SetOpRoleId(self.Player.RoleId).SetDropOperation(true);
            self.DoDropItem(itemNode.GetPath(), expectedCostCount, velocityX, velocityY, velocityZ);
        }

        /// <summary>
        /// 枪械Node专用，卸子弹
        /// </summary>
        /// <param name="_"></param>
        /// <param name="itemUid"></param>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void UnloadBullet(this PlayerInventoryComponent self, long itemUid)
        {
            if (self.Root.GetNodeById(itemUid) is not BaseItemNode itemNode || itemNode is not IHaveBulletItemNode bulletItemNode)
            {
                self.Logger.Info($"UnloadBullet Fail! itemNode is null! roleId:{self.Player.RoleId} itemUid:{itemUid}");
                return;
            }

            long updateEntityId = 0;
            if (ItemUtility.IsLinkEntityPath(itemNode.GetPath()))
            {
                var ownerCol = itemNode.GetOwnerComponent().ParentEntity;
                if (ownerCol == self.Player || (ownerCol is PartEntity monster && monster.TemplateId == (long)PartType.AutoTurret))
                {
                    self.Logger.Warn("客户端发错了，belt或炮台上卸子弹直接发simulator");
                    return;
                }
                else if (ownerCol is PlayerEntity otherPlayer)
                {
                    if (otherPlayer.IsOnline)
                    {
                        self.Logger.Warn("别人在线，不能卸别人的子弹");
                        return;
                    }
                    else
                    {
                        updateEntityId = otherPlayer.EntityId;
                    }
                }
            }

            using var ctx = NodeOpContext.GetNew(OpContextConst.UNLOAD_BULLET).SetOpRoleId(self.Player.RoleId).SetItemNodeBelongRoleId(itemNode.BelongRoleId);
            using var transaction = EntityTransaction.Start($"UnloadBullet-{self.Player.RoleId}-{itemNode.GetPath().PrintPath()}-{itemNode.BizId}");
            var code = self.Root.MergeNode(new NodeOpByBizId(bulletItemNode.BulletBizId, bulletItemNode.BulletAmount));
            if (code != EOpCode.Success)
            {
                transaction.Rollback(OpCodeReason.Reason[code]);
                return;
            }
            bulletItemNode.BulletAmount = 0;
            transaction.Commit();

            if (updateEntityId != 0)
            {
                var pe = EntityManager.Instance.GetEntity(updateEntityId) as PlayerEntity;
                var ihbe = pe?.GetByEntityId<IHaveBulletEntity>(itemUid);
                if (ihbe == null) self.Logger.Warn($"Unload other player bullet, but other have bullet entity {updateEntityId} {pe} {itemUid} {ihbe} not found");
                else
                {
                    ihbe.Clips = 0;
                }
            }
        }

        /// <summary>
        /// 移动背包
        /// </summary>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void ForceMoveExtraPack(this PlayerInventoryComponent self, List<long> srcPath, List<long> targetPath)
        {
            if (srcPath == null || targetPath == null) return;
            if (PathUtil.PathEqual(srcPath, targetPath)) return;
            if (!self.CanOperatePath(srcPath) || !self.CanOperatePath(targetPath)) return;

            var logSource = "ForceMoveExtraPack";
            var ignoreTips = false;
            if (PathUtil.CalcIsLooting(srcPath, targetPath))
            {
                logSource = OpContextConst.LOOTING;
                ignoreTips = self.CalcIgnoreTips();
            }
            using var _ = NodeOpContext.GetNew(logSource).SetOpRoleId(self.Player.RoleId).SetIgnoreOpTips(ignoreTips);
            self.MoveExtraPackInner(srcPath, targetPath, true);
        }

        /// <summary>
        /// 在容器之间批量转移同一种道具
        /// 用于从自己身上转移到异端容器
        /// </summary>
        /// <param name="bizId"></param>
        /// <param name="maxCount"></param>
        /// <param name="srcPath"></param>
        /// <param name="targetPath"></param>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void TransferItemBetweenContainers(this PlayerInventoryComponent self, long bizId, int maxCount, List<long> srcPath, List<long> targetPath)
        {
            if (!self.TransferToOthersideCheck(bizId, maxCount, srcPath, targetPath)) return;
            var src = self.Root.GetNodeByPath(srcPath) as ItemContainerNode;
            srcPath.Add(NodePathConst.AnyIndex);
            // 移动到某个容器里，就添加为三级路径
            // 如果本身就是三级路径了，什么也不做
            if (targetPath.Count == 2) targetPath.Add(NodePathConst.AnyIndex);
            using var ctx = NodeOpContext.GetNew(OpContextConst.LOOTING).SetBattleItemFlowType(EBattleItemFlowType.NoChange);
            using var transaction = EntityTransaction.Start($"TransferItemBetweenContainers-{bizId}-{maxCount}");
            if (PathUtil.IsPathInOtherPlayerOrAutoTurret(srcPath) || PathUtil.IsPathInOtherPlayerOrAutoTurret(targetPath))
            {
                ctx.SetBattleItemFlowType(EBattleItemFlowType.Change);
            }
            var left = maxCount;
            int movedCount;
            foreach (var (index, item) in src)
            {
                if (item.BizId != bizId || item is not BaseItemNode bin) continue;
                var count = bin.GetCount();
                if (count <= left)
                {
                    srcPath[^1] = index;
                    var code = bin.RequireSelf();
                    if (code != EOpCode.Success)
                    {
                        transaction.Rollback($"RequireSelf fail {code}");
                        return;
                    }
                    (code, movedCount) = self.MoveItemIntoContainer(bin, srcPath, targetPath);
                    if (code == EOpCode.ItemSystemStillNeedMerge || code == EOpCode.ItemSystemFull)
                    {
                        left -= movedCount;
                        break;
                    }
                    else if (code == EOpCode.Success)
                    {
                        left -= count;
                        if (left <= 0) break;
                    }
                    else
                    {
                        transaction.Rollback($"MoveItemIntoContainer fail {code}");
                        return;
                    }
                }
                else
                {
                    var code = self.Root.RequireNode(bin, left);
                    if (code != EOpCode.Success)
                    {
                        transaction.Rollback($"RequireNode fail {code}");
                        return;
                    }
                    srcPath[^1] = index;
                    var splitItem = NodeSystemHelper.CloneItem(bin, left);
                    splitItem.BelongRoleId = bin.BelongRoleId;
                    (code, movedCount) = self.MoveItemIntoContainer(splitItem, srcPath, targetPath);
                    if (code == EOpCode.Success || code == EOpCode.ItemSystemStillNeedMerge || code == EOpCode.ItemSystemFull)
                    {
                        left -= movedCount;
                        break;
                    }
                    else
                    {
                        transaction.Rollback($"MoveItemIntoContainer fail {code}");
                        return;
                    }
                }
            }

            // 如果是给自动炮台或者防空炮添加弹药需要弹Tips
            var tempTargetPath = new List<long>();
            tempTargetPath.AddRange(targetPath);
            if (tempTargetPath.Count > 2)
            {
                tempTargetPath.RemoveAt(2);
                var node = self.Root.GetNodeByPath(tempTargetPath) as ItemContainerNode;
                if (null != node)
                {
                    if (node.BizId == ContainerConst.AutoTurretBullet || node.BizId == ContainerConst.FLAK_TURRET_BULLET)
                    {
                        self.Player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, CommonTipConst.AddBulletSuccss);
                    }
                }
            }

            self.Logger.Info($"TransferItemBetweenContainers left={left} maxCount={maxCount}");
            transaction.Commit();
            self.RemoteCallTransferItemBetweenContainersAck(ERpcTarget.OwnClient, bizId, maxCount, srcPath, targetPath);
        }

        /// <summary>
        /// 尝试在容器之间批量转移同一种道具
        /// 如放不下，会弹二次确认框。如withConfirm放不下，会丢弃
        /// </summary>
        /// <param name="bizId"></param>
        /// <param name="maxCount"></param>
        /// <param name="srcPath"></param>
        /// <param name="targetPath"></param>
        /// <param name="withConfirm"></param>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void TryTransferItemBetweenContainers(this PlayerInventoryComponent self, long bizId, int maxCount, List<long> srcPath, List<long> targetPath, bool withConfirm)
        {
            if (!self.TransferToInventoryCheck(bizId, maxCount, srcPath, targetPath)) return;

            targetPath.Add(NodePathConst.Anywhere);
            var item = self.Root.GetNodeByPath(srcPath) as StackableItemNode;

            var mark = self.GetOpContextWhenLooting(self.LootingEntity);
            using var ctx = NodeOpContext.GetNew(mark).SetBattleItemFlowType(EBattleItemFlowType.NoChange).SetItemNodeBelongRoleId(item.BelongRoleId); ;
            using var transaction = EntityTransaction.Start($"TransferItemBetweenContainers-{bizId}-{maxCount}");
            if (PathUtil.IsPathInOtherPlayerOrAutoTurret(srcPath) || PathUtil.IsPathInOtherPlayerOrAutoTurret(targetPath))
            {
                ctx.SetBattleItemFlowType(EBattleItemFlowType.Change);
            }
            var left = maxCount;
            var count = item.GetCount();
            if (count <= left)
            {
                var code = item.RequireSelf();
                if (code != EOpCode.Success)
                {
                    transaction.Rollback($"RequireSelf fail {code}");
                    return;
                }

                var nodeOp = new NodeOpByIndex(targetPath, new ExistingNode(item));
                if (withConfirm)
                    nodeOp.WithOption(EOversizeOptionEnum.Drop);
                code = self.Root.MergeNode(nodeOp);
                if (code == EOpCode.Success)
                {
                    self.Logger.Info($"TransferItemBetweenContainers left={left} maxCount={maxCount}");
                    transaction.Commit();
                }
                else
                {
                    transaction.Rollback($"TryTransferItemBetweenContainers fail {code}");
                    self.RemoteCallTryTransferTargetContainerFull(ERpcTarget.OwnClient, srcPath, maxCount);
                }
            }
            else
            {
                var code = self.Root.RequireNode(item, left);
                if (code != EOpCode.Success)
                {
                    transaction.Rollback($"RequireNode fail {code}");
                    return;
                }

                var nodeOp = new NodeOpByIndex(targetPath, new BizIdWithCount(item.BizId, left));
                if (withConfirm)
                    nodeOp.WithOption(EOversizeOptionEnum.Drop);
                code = self.Root.MergeNode(nodeOp);
                if (code == EOpCode.Success)
                {
                    self.Logger.Info($"TransferItemBetweenContainers left=0 maxCount={maxCount}");
                    transaction.Commit();
                }
                else
                {
                    transaction.Rollback($"TryTransferItemBetweenContainers fail {code}");
                    self.RemoteCallTryTransferTargetContainerFull(ERpcTarget.OwnClient, srcPath, maxCount);
                }
            }
        }

        /// <summary>
        /// 卸下衣服
        /// </summary>
        /// <param name="_"></param>
        /// <param name="itemUid">衣服id</param>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        static public void TakeOffCloth(this PlayerInventoryComponent self, long itemUid)
        {
            if (self.Root.GetNodeById(itemUid) is not UniqueItemNode itemNode || itemNode.ParentId != NodeConst.WearItemContainerNodeId)
            {
                return;
            }

            using var _ = NodeOpContext.GetNew(OpContextConst.TAKE_OFF_CLOTH).SetOpRoleId(self.Player.RoleId);
            if (itemNode is ExtraPackItemNode)
            {
                self.MoveExtraPackInner(itemNode.GetPath(), new List<long> { NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Main, NodePathConst.Anywhere }, false);
                return;
            }

            using var transaction = EntityTransaction.Start($"TakeOffCloth-{self.Player.RoleId}-{itemNode.GetPath().PrintPath()}");
            var code = self.TakeOffClothInnerTransaction(itemNode);
            if (code == EOpCode.Success) transaction.Commit();
            else transaction.Rollback(OpCodeReason.Reason[code]);
        }

        /// <summary>
        /// 从指定的Entity中获得一个物品
        /// </summary>
        static public void GetItemWithoutLooting(this PlayerInventoryComponent self, long entityId, long itemUid)
        {
            if (self.Root.HasMounted)
            {
                self.Logger.Info($"{self.ParentId} GetItemWithoutLooting when looting");
                return;
            }

            if (self.Player.ComponentLooting.CheckLooting(entityId, false, out var targetEntity) != EOpCode.Success)
            {
                self.Logger.Info($"[GetItemWithoutLooting]Target cannot looting. EntityId:{entityId}");
                return;
            }

            if (targetEntity is not BoxEntity)
            {
                self.Logger.Error($"{LogHelper.NON_PUBLISH_REPORT} GetItemWithoutLooting only available for BoxEntity, entity id:{entityId}, entity type:{targetEntity.GetTypeName()}");
                return;
            }

            if (targetEntity.GetComponent(EComponentIdEnum.Box) is not AutoSyncBoxComponent boxComp)
            {
                self.Logger.Error($"{LogHelper.NON_PUBLISH_REPORT} GetItemWithoutLooting only available for AutoSyncBoxComponent, entity id:{entityId}");
                return;
            }

            if (targetEntity.GetComponent(EComponentIdEnum.RootNodeComponent) is not RootNodeComponent targetRoot)
            {
                self.Logger.Info($"[GetItemWithoutLooting]Target has no RootNodeComponent. EntityId:{entityId}");
                return;
            }

            if (targetRoot.GetNodeById(itemUid) is not BaseItemNode itemNode)
            {
                self.Logger.Info($"[GetItemWithoutLooting]Target is not item. RoleId: {self.Player.RoleId}, ColletionId:{entityId}, ItemUid: {itemUid}");
                return;
            }

            List<long> targetPath;
            if (!BizId2SystemTypeConst.BizId2SystemType.TryGetValue(itemNode.BizId, out var systemId))
            {
                self.Logger.Info($"[GetItemWithoutLooting]Target system id is not find. RoleId: {self.Player.RoleId}, ColletionId:{entityId}, ItemUid: {itemUid}, item bizId:{itemNode.BizId}");
                return;
            }

            targetPath = new List<long>() { systemId, NodePathConst.Anywhere };
            self.Logger.Info($"GetItemWithoutLooting {entityId} {itemUid}");
            self.Root.MountOtherRoot(targetRoot, targetRoot.LootableSystemIds);
            try
            {
                var mark = self.GetOpContextWhenLooting(targetEntity);
                self.MoveItemToPathInner(itemNode, itemNode.GetPath(), targetPath, mark);

                var hasLootedBefore = boxComp.LootedPlayerIds.Contains(self.EntityId);
                var isCorpseBox = boxComp is CorpseBoxComponent;
                EntityStaticCallback<PlayerEntity>.InvokeAllEventCallback(self.Player, new PlayerLootingBoxEvent(itemNode.BizId, itemNode.GetCount(), self.EntityId, targetEntity.EntityId, hasLootedBefore, isCorpseBox, boxEntity.PlayerId));
                if (!hasLootedBefore)
                {
                    boxComp.LootedPlayerIds.Add(self.Player.EntityId);
                }
            }
            catch (Exception e)
            {
                self.Logger.Error($"GetItemWithoutLooting with exception {e}");
            }
            finally
            {
                self.Root.UnmountOtherRoot(targetRoot.LootableSystemIds.Keys);
            }
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private static EOpCode TransactionFailWithCode(EntityTransaction transaction, string reason, EOpCode code)
        {
            transaction.Rollback($"{reason} {code}");
            return code;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private static EOpCode TransactionFailWithCodeNoWarning(EntityTransaction transaction, string reason, EOpCode code)
        {
            transaction.IsLogInfoWhenRollback = true;
            transaction.Rollback($"{reason} {code}");
            return code;
        }

        // [RpcHandler(ExposeToClient = true)]
        // public static void WaterBottleDrink(this PlayerInventoryComponent self, long bottleId, int amount)
        // {
        //     if (amount <= 0) return;
        //     var player = self.Player;
        //     if (self.Root.GetNodeById(bottleId) is not WaterBottleItemNode waterBottleNode)
        //     {
        //         self.Logger.Error($"[WaterBottleDrink] cannot get water bottle! collectionId: {player.EntityId}, bottleId: {bottleId}");
        //         return;
        //     }
        //     if (waterBottleNode.GetChild(0) is not StackableItemNode waterNode)
        //     {
        //         self.Logger.Warn($"[WaterBottleDrink] water bottle is empty! collectionId: {player.EntityId}, bottleId: {bottleId}");
        //         return;
        //     }
        //
        //     using var ctx = NodeOpContext.GetNew(OpContextConst.DRINK_WATER).SetOpRoleId(player.RoleId);
        //     EOpCode code;
        //
        //     code = self.Root.RequireNode(new NodeOpByIndex(waterNode.GetPath(), new IntCount(amount)));
        //     if (code != EOpCode.Success)
        //     {
        //         self.Logger.Warn($"[WaterBottleDrink] require failed {code}");
        //         return;
        //     }
        //
        //     var requireNode = ctx.RequireList.First() as StackableItemNode;
        //     player.RemoteCallDoDrinkWater(ERpcTarget.Simulator, player.EntityId, requireNode.BizId, requireNode.Count);
        //     self.Logger.Info($"[WaterBottleDrink]Success. collectionId: [{player.EntityId}], waterId: [{requireNode.BizId}], count: [{requireNode.Count}].");
        // }

        [RpcHandler(ExposeToClient = true, CallInterval = 0.5f)]
        public static void PartDrinkWater(this PlayerInventoryComponent self, long partId, int idx)
        {
            var player = self.Player;
            // 交互列表喝水不走权限检测

            var partEntity = EntityManager.Instance.GetEntity(partId) as PartEntity;
            if (null == partEntity) return;
            var storageComp = partEntity.FindStorageComp();
            if (storageComp is WaterFacilityComponent facilityComp)
            {
                var waterCount = facilityComp.GetWaterCount(idx);
                var drinkNum = Mathf.Min(50, waterCount.Item2);

                if (drinkNum == 0)
                {
                    player.RemoteCallTipsWithConstructionName(ERpcTarget.OwnClient, 1911, partId);
                    return;
                }

                var nodeId = facilityComp.Root?.GetChild(idx)?.Id ?? 0;
                facilityComp.DrinkWater(nodeId, drinkNum);
            }
        }

        /// <summary>
        /// 使用道具
        /// </summary>
        /// <param name="_"></param>
        /// <param name="itemUid">道具uid</param>
        /// <param name="count">数量</param>
        [RpcHandler(ExposeToClient = true, CallInterval = 0.1f)]
        public static void UseItem(this PlayerInventoryComponent self, long entityId, long itemUid, int count)
        {
            if (count <= 0) return;

            var root = self.Root;
            var player = self.Player;
            var entity = EntityManager.Instance.GetEntity(entityId);
            if (entity == null)
            {
                self.Logger.LogInputError($"UseItem Fail! entity is null! roleId:{player.RoleId} entityId:{entityId} itemUid:{itemUid}");
                return;
            }
            RootNodeComponent host = null;
            NodeBase node;
            // 不是自己身上的道具 && 不是Looting实体上的道具 && 不是地上的道具 就不给使用
            if (entity == self.ParentEntity)
            {
                host = self.Root;
                node = host.GetNodeById(itemUid);
            }
            else if (entity == player.ComponentLooting.CurrentLootingEntity)
            {
                host = entity.GetComponent<RootNodeComponent>(EComponentIdEnum.RootNodeComponent);
                node = host?.GetNodeById(itemUid);
            }
            else if (entity is IPositionEntity posEntity && (entity is CollectableEntity || entity is SceneItemEntity))
            {
                // 允许2m的容错
                var maxDistance = McCommon.Tables.TbGlobalConfig.ParallelDetection + 2;
                if (Mathf.Abs(player.PosX - posEntity.PosX) > maxDistance || Mathf.Abs(player.PosY - posEntity.PosY) > maxDistance || Mathf.Abs(player.PosZ - posEntity.PosZ) > maxDistance)
                {
                    self.Logger.Warn($"UseItem Fail! too far away! roleId:{player.RoleId} entityId:{entityId} itemUid:{itemUid}");
                    return;
                }
                host = entity.GetComponent<RootNodeComponent>(EComponentIdEnum.RootNodeComponent);
                node = host?.GetNodeById(itemUid);
            }
            else
            {
                self.Logger.Error($"UseItem Fail! entity is not expected! roleId:{player.RoleId} entityId:{entityId} itemUid:{itemUid}");
                return;
            }

            if (node is not BaseItemNode itemNode)
            {
                self.Logger.InfoFormat("UseItem Fail! itemNode is null! roleId:{0} itemUid:{1}", player.RoleId, itemUid);
                return;
            }

            //统计基本数据
            var bizId = itemNode.BizId;
            var itemConfig = McCommon.Tables.TbItemConfig.GetOrDefault(bizId);
            if (itemConfig == null)
            {
                self.Logger.Error($"id:{bizId}, fail to get item config");
                return;
            }
            var playerEntity = self.Player;
            var blueprintComponent = playerEntity.GetComponent<BlueprintComponent>(EComponentIdEnum.BlueprintComponentId);

            var source = OpContextConst.USE_ITEM;
            if (itemNode.HasFlag(ItemFlags.CanEquipped))
            {
                source = OpContextConst.EQUIP;
            }

            using var _ = NodeOpContext.GetNew(source).SetOpRoleId(playerEntity.RoleId);
            if (ItemHelper.IsLearnableBlueprintItemId(bizId))
            {
                blueprintComponent.UseBlueprintItem(itemConfig, itemNode);
            }
            else if (itemNode.HasFlag(ItemFlags.Eatable))
            {
                self.UseItemForEat(itemNode);
            }
            else if (itemNode.HasFlag(ItemFlags.CanEquipped))
            {
                self.UseItemForEquip(itemNode, host);
            }
            else if (itemConfig.Manufacturing == Manufacturing.Consumables && itemConfig.SecondaryClassification == SecondaryClassification.TreasureMap)
            {
                self.UseItemForBoxTask(itemNode);
            }
            else if ((itemConfig.Manufacturing == ItemType.Expendable || itemConfig.Manufacturing == ItemType.Misc) && itemConfig.OperationType == ItemOperationType.SwitchModel)
            {
                self.UseItemForSwitchModel(itemNode);
            }
            else if (bizId == ItemConst.IronBucket)
            {
                // 支持铁桶快捷切到手上 用于浇水
                self.UseItemForSwitchModel(itemNode);
            }
            else
            {
                self.Logger.Error($"UseItem Fail! item not expected roleId:{player.RoleId} itemUid:{itemUid} bizId:{bizId} count {count}");
            }
        }

        /// <summary>
        /// 开启礼包
        /// </summary>
        /// <param name="_"></param>
        /// <param name="itemUid">礼包道具ID</param>
        /// <param name="selectDropId">可选的掉落ID，如果礼包不可选为0</param>
        [RpcHandler(ExposeToClient = true, CallInterval = 1.0f)]
        public static void OpenPackage(this PlayerInventoryComponent self, long itemUid, int selectDropId)
        {
            if (self.Root.GetNodeById(itemUid) is not BaseItemNode itemNode)
            {
                self.Logger.Warn($"NonPublishReport [OpenPackage] cannot get package, item id: {itemUid}");
                self.RemoteCallOpenPackageAck(ERpcTarget.OwnClient, (int)EOpCode.ItemNotExisted);
                return;
            }

            if (!McCommon.Tables.TbItemPackages.DataMap.TryGetValue(itemNode.BizId, out var packageConfig))
            {
                self.Logger.Error($"[OpenPackage] item node:{itemNode} is not package");
                self.RemoteCallOpenPackageAck(ERpcTarget.OwnClient, (int)EOpCode.ConfigNotFound);
                return;
            }

            var mainContainer = self.Root.GetNodeByPath(NodePathConst.PlayerInventoryMain) as ItemContainerNode;
            var remian = mainContainer.Capacity - mainContainer.ChildCount;
            var require = packageConfig.EmptyCell - (itemNode.GetCount() > 1 ? 0 : 1);

            if (remian < require)
            {
                self.Logger.Info($"[OpenPackage] item node:{itemNode} empty cell is not enough");
                self.RemoteCallOpenPackageAck(ERpcTarget.OwnClient, (int)EOpCode.ItemSystemFull);
                return;
            }

            packageDropIds.Clear();
            if (packageConfig.Optional != 0)
            {
                if (selectDropId <= 0)
                {
                    self.Logger.Warn($"NonPublishReport [OpenPackage] item node:{itemNode} is not support select drop");
                    self.RemoteCallOpenPackageAck(ERpcTarget.OwnClient, (int)EOpCode.ConfigNotFound);
                    return;
                }

                bool isContains = false;
                foreach (var dropId in packageConfig.DropIDList)
                {
                    if (dropId == selectDropId)
                    {
                        isContains = true;
                        break;
                    }
                }

                if (!isContains)
                {
                    self.Logger.Error($"[OpenPackage] item node:{itemNode} is not contain select drop id:{selectDropId}");
                    self.RemoteCallOpenPackageAck(ERpcTarget.OwnClient, (int)EOpCode.ConfigNotFound);
                    return;
                }

                packageDropIds.Add(selectDropId);
            }
            else
            {
                packageDropIds.AddRange(packageConfig.DropIDList);
            }

            using var transaction = EntityTransaction.Start($"OpenPackage-{self.RoleId}-{itemNode}");
            using var ctx = NodeOpContext.GetNew(OpContextConst.PACKAGE);

            var opCode = self.Root.RequireNode(new NodeOpByExisting(itemNode, 1));
            if (opCode != EOpCode.Success)
            {
                transaction.Rollback(OpCodeReason.Reason[opCode]);
                self.RemoteCallOpenPackageAck(ERpcTarget.OwnClient, (int)opCode);
                return;
            }

            packageDropReward.Clear();
            foreach (var dropId in packageDropIds)
            {
                var opList = DropUtil.GetRewardByDropIdAndPlayer(dropId, self.ParentId);
                if (opList.Count > 0)
                {
                    packageDropReward.AddRange(opList);
                }
                else
                {
                    self.Logger.Warn($"[OpenPackage] item node:{itemNode} drop id:{dropId} is empty");
                }
            }

            if (packageDropReward.Count <= 0)
            {
                self.Logger.Error($"[OpenPackage] item node:{itemNode} select drop id:{selectDropId} is empty");
            }

            foreach (var item in packageDropReward)
            {
                var id = item.ItemId;
                var count = item.Amount;
                opCode = self.Root.MergeNode(id, count, item.ItemParam);
                if (opCode != EOpCode.Success)
                {
                    self.Logger.Error($"[OpenPackage] MergeNode failed. RoleId:{self.RoleId}, item node:{itemNode}, code:{opCode}");
                    transaction.Rollback(OpCodeReason.Reason[opCode]);
                    self.RemoteCallOpenPackageAck(ERpcTarget.OwnClient, (int)opCode);
                    return;
                }
                else
                {
                    self.Logger.Info($"[OpenPackage] MergeNode success. RoleId:{self.RoleId}, item id:{id}, count:{count}");
                }
            }

            transaction.Commit();
            self.Logger.Info($"[OpenPackage] item node:{itemNode} select drop id:{selectDropId}, open package success");
        }

        private static void UseItemForEat(this PlayerInventoryComponent self, BaseItemNode itemNode)
        {
            var bizId = itemNode.BizId;
            var now = DateTimeOffset.Now.ToUnixTimeMilliseconds();
            var foodConfig = McCommon.Tables.TbFood.GetOrDefault(bizId);
            var root = self.Root;
            var player = self.Player;

            if (self.LastEatTime >= now)
            {
                self.Logger.Info($"Eat food fail, in cooldown. RoleId: {player.RoleId} {itemNode}");
                return;
            }

            using var transaction = EntityTransaction.Start($"UseItem.EatFood-{player.RoleId}-{itemNode}");
            var ctx = NodeOpContext.GetCurrent();
            ctx.SetTriggerBeltAutoSupply(true);
            var code = root.RequireNode(new NodeOpByExisting(itemNode, 1));
            if (code != EOpCode.Success)
            {
                transaction.Rollback(OpCodeReason.Reason[code]);
                return;
            }

            foreach (var itemCount in foodConfig.AfterItem)
            {
                code = root.MergeNode(
                    new NodeOpByBizId(itemCount.ItemId, itemCount.Count).WithOption(EOversizeOptionEnum.Drop));
                if (code != EOpCode.Success)
                {
                    transaction.Rollback(OpCodeReason.Reason[code]);
                    return;
                }
            }
            self.LastEatTime = now + foodConfig.AttackAgainTime;
            transaction.Commit();

            foreach (var buffId in foodConfig.AfterBuff)
            {
                code = root.MergeNode(buffId, 1);
                if (code != EOpCode.Success)
                {
                    self.Logger.Info($"eat food add buff fail with code {code}");
                }
            }

            EntityStaticCallback<PlayerEntity>.InvokeAllEventCallback(player, new UseItemEvent(bizId, 1, player.EntityId));
            player.RemoteCallEatFood(ERpcTarget.Simulator, player.EntityId, bizId);
        }

        private static void UseItemForEquip(this PlayerInventoryComponent self, BaseItemNode itemNode, RootNodeComponent host)
        {
            var bizId = itemNode.BizId;
            var roleId = self.Player.RoleId;
            var root = self.Root;
            self.Logger.Info($"UseItemForEquip-{roleId}-{itemNode}");

            var srcPath = itemNode.GetPath();
            if (host.ParentEntity is PlayerEntity pnc && pnc.EntityId != self.Player.EntityId)
            {
                srcPath[0] = NodeSystemType.OtherPlayerInventory;
            }
            else if (host.ParentEntity is SceneItemEntity sie)
            {
                srcPath[0] = NodeSystemType.PickUpSystem;
                srcPath[1] = sie.EntityId;
            }

            if (itemNode is ExtraPackItemNode)
            {
                self.MoveExtraPackInner(srcPath, new List<long> { NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Wear, (long)EquipPart.ExtraPack - 1 }, false);
                return;
            }

            var inventoryNode = self.Root.GetSystemById(NodeSystemType.PlayerInventory) as IDirectoryNode;
            var targetContainerNode = inventoryNode.GetChildNode(PlayerInventoryNodeIndex.Wear) as ItemContainerNode;
            var conflictItems = targetContainerNode.GetConflictClothes(bizId);
            if (conflictItems.Count == 0)
            {
                self.DoMoveItemToPath(itemNode, srcPath, new List<long>() { NodeSystemType.PlayerInventory, PlayerInventoryNodeIndex.Wear, NodePathConst.Anywhere });
            }
            else if ((host.ParentEntity is SceneItemEntity) || (itemNode.GetParent() is ItemContainerNode icn && icn.HasFlag(ItemContainerFlag.CannotInput)))
            {
                using var transaction = EntityTransaction.Start($"UseItem.WearClothOnGround-{roleId}-{itemNode}-{bizId}");
                using var ctx = NodeOpContext.GetCurrent().SetBattleItemFlowType(EBattleItemFlowType.NoChange);
                EOpCode code;
                foreach (var item in conflictItems)
                {
                    code = item.RequireSelf();
                    if (code != EOpCode.Success)
                    {
                        TransactionFailWithCode(transaction, "require fail", code);
                        return;
                    }
                }
                code = itemNode.RequireSelf();
                if (code != EOpCode.Success)
                {
                    TransactionFailWithCode(transaction, "require fail", code);
                    return;
                }
                code = root.MergeNode(new NodeOpByIndex(NodePathConst.AnywhereInPlayerInventoryWear).WithDetail(new ExistingNode(itemNode)));
                if (code != EOpCode.Success)
                {
                    TransactionFailWithCode(transaction, "wear fail", code);
                    return;
                }
                foreach (var item in conflictItems)
                {
                    code = root.MergeNode(new NodeOpByIndex(NodePathConst.AnywhereInPlayerInventoryMain).WithDetail(new ExistingNode(item)));
                    if (code != EOpCode.Success)
                    {
                        TransactionFailWithCodeNoWarning(transaction, "merge conflict wear fail", code);
                        return;
                    }
                }
                transaction.Commit();
            }
            else
            {
                using var transaction = EntityTransaction.Start($"UseItem.WearCloth-{roleId}-{itemNode}-{bizId}");
                EOpCode code;
                for (var index = 1; index < conflictItems.Count; ++index)
                {
                    code = self.TakeOffClothInnerTransaction(conflictItems[index]);
                    if (code != EOpCode.Success)
                    {
                        transaction.Rollback(OpCodeReason.Reason[code]);
                        return;
                    }
                }
                (code, var resultStr) = self.ExchangeItemInTransaction(conflictItems[0].GetPath(), srcPath);
                if (code == EOpCode.Success) transaction.Commit();
                else transaction.Rollback(resultStr, false);
            }
        }

        private static void UseItemForBoxTask(this PlayerInventoryComponent self, BaseItemNode itemNode)
        {
            var player = self.Player;
            if (!TaskUtil.IsExplorationMode())
            {
                self.Logger.Error($"UseItemForBoxTask fail, not in bounty or exploration mode. roleId: {player.RoleId} {itemNode.Id}");
                return;
            }

            var root = self.Root;
            var taskContainer = root.GetNodeByPath(NodeSystemType.TaskSystem, PlayerTaskContainerIndex.Explore) as ExploreTaskContainer;
            if (null == taskContainer)
            {
                self.Logger.Error($"UseItemForBoxTask fail, not in bounty or exploration mode. roleId: {player.RoleId} {itemNode.Id}");
                return;
            }

            self.Logger.Info($"UseItem.Random BoxTask-{player.RoleId}-{itemNode}");

            NodeOpContext.GetCurrent().SetTriggerBeltAutoSupply(true);
            using var transaction = EntityTransaction.Start($"UseItem.UseItemForBoxTask-{player.RoleId}-{itemNode}");

            var code = root.RequireNode(new NodeOpByExisting(itemNode, 1));
            if (code != EOpCode.Success)
            {
                self.Logger.Info($"UseItemForBoxTask fail with code {code}");
                transaction.Rollback("UseItemForBoxTask fail", false);
                return;
            }

            if (!taskContainer.AddExploreTask(BoxTaskType.ItemMode, itemNode.BizId, out var msgId))
            {
                transaction.Rollback("Random task fail", false);
            }
            else
            {
                transaction.Commit();
            }

            if (msgId != 0)
            {
                player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, msgId);
            }
        }

        private static void UseItemForSwitchModel(this PlayerInventoryComponent self, BaseItemNode itemNode)
        {
            var host = itemNode.GetRootNode().GetHostComponent().ParentRef;
            var player = self.Player;
            if (host != self.ParentRef)
            {
                self.Logger.LogInputError($"UseItemForSwitchModel fail! host is not self! roleId:{player.RoleId} itemUid:{itemNode.Id} bizId {itemNode.BizId}");
                return;
            }
            if (player != null && player.CustomItemHiddenUse != null && (player.CustomItemHiddenUse as IItemEntity).ItemUid == itemNode.Id)
            {
                self.Logger.LogInputError($"UseItemForSwitchModel fail! item is in use! roleId:{player.RoleId} itemUid:{itemNode.Id} bizId {itemNode.BizId}");
                return;
            }

            // 快捷栏直接转发
            // if (parentNode.Id == NodeConst.BeltItemContainerNodeId)
            // {
            //     W2SRemoteCall.Instance.WorldUseItem((int)itemNode.Index);
            // }
            // else
            // {
            //     ItemSystem.LoadUnloadItem(parentNode, itemNode, true, (int)HoldItemIndex.ItemHiddenUse);
            //     W2SRemoteCall.Instance.WorldUseItem((int)HoldItemIndex.ItemHiddenUse);
            // }
            // 快捷栏不发rpc，客户端直接处理
            if (itemNode.ParentId != NodeConst.BeltItemContainerNodeId)
            {
                var cnt = itemNode.GetCount();
                // 这里存在异步过程 需要将数据copy一份 不然会有问题
                using var writer = CustomTypeHelper.GetDisposableArrayWriter();
                var serializedItem = new System.Buffers.ReadOnlySequence<byte>(writer.SerializeCustomType(itemNode, Framework.Const.ESerializeMode.UnityDs).ToArray());
                RpcEntity.Instance.RemoteCallCreateItemEntity(ERpcTarget.Simulator, ProcessEntity.Instance.Sequence + 1,
                    self.EntityId, serializedItem, cnt, (int)HoldItemIndex.ItemHiddenUse);
            }
        }

        public static void UseItemDone(this PlayerInventoryComponent self, long itemUid, long bizId)
        {
            // 无论如何 都清掉隐藏槽位
            if (self.Player != null && self.Player.CustomItemHiddenUse != null && itemUid == (self.Player.CustomItemHiddenUse as IItemEntity).ItemUid)
            {
                self.Player.RemoteCallUnloadHiddenItemUse(ERpcTarget.Simulator);
            }

            var itemConfig = McCommon.Tables.TbItemConfig.GetOrDefault(bizId);
            if (null == itemConfig)
            {
                self.Logger.Warn($"UseItemDone Fail! itemNode is null! roleId:{self.Player.RoleId} itemUid:{itemUid} bizId {bizId} item is WaterBottle");
                RpcEntity.Instance.RemoteCallUseItemDoneAck(ERpcTarget.Simulator, itemUid, bizId, 0, (int)EOpCode.ItemNotExisted);
                return;
            }

            var itemNode = self.Player.Root.GetNodeById(itemUid) as BaseItemNode;
            long requireBizId = bizId;
            int requireCount = 1;

            EOpCode ret;
            if (itemConfig.Manufacturing == ItemType.Expendable && itemConfig.SecondaryClassification == SecondaryClassification.BeeBuzzSignalCard)
            {
                ret = self.UseBeeBuzzSignalCardDone(itemNode, bizId);
            }
            else if (bizId == ItemConst.WaterBottleItemId)
            {
                ret = self.UseWaterBottleDone(itemNode, bizId, ref requireBizId, ref requireCount);
            }
            else
            {
                ret = self.UseCommonItemDone(itemNode, bizId);
            }

            if (ret == EOpCode.Success)
            {
                EntityStaticCallback<PlayerEntity>.InvokeAllEventCallback(self.Player, new UseItemEvent(requireBizId, requireCount, self.ParentId));
            }

            self.Logger.Info($"UseItemDone, roleId:{self.Player.RoleId} itemUid:{itemUid} bizId:{bizId} requireBizId {requireBizId} {requireCount} code:{ret}");
            RpcEntity.Instance.RemoteCallUseItemDoneAck(ERpcTarget.Simulator, itemUid, requireBizId, requireCount, (int)ret);
        }

        private static EOpCode UseBeeBuzzSignalCardDone(this PlayerInventoryComponent self, BaseItemNode cardItemNode, long bizId)
        {
            if (null == cardItemNode)
            {
                logger.Error($"[UseBeeBuzzSignalCardDone] card is not exist");
                self.Player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, CommonTipConst.BeeBuzzWrongMonument);
                return EOpCode.ItemNotExisted;
            }

            var itemUid = cardItemNode.Id;
            var ret = BeeBuzzUtil.CheckUseBeeBuzzSignalCard(self.Player, itemUid);
            if (ret != EOpCode.Success)
            {
                logger.Error($"[UseBeeBuzzSignalCardDone] CheckUseBeeBuzzSignalCard failed, card uid:{itemUid}, card biz id:{cardItemNode.BizId}, ret:{ret}");
                self.Player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, CommonTipConst.BeeBuzzWrongMonument);
                return ret;
            }

            var beeBuzzTemplateId = BeeBuzzGroupEntityHotfix.GetBeeBuzzTemplateId(self.Player.MonumentId, McCommon.Tables.TbBeeBuzzConstant.BeeBuzzNoPlayerSpawnRange);
            if (beeBuzzTemplateId == 0)
            {
                logger.Error($"[UseBeeBuzzSignalCardDone] card bizid: {cardItemNode.BizId} monument id:{self.Player.MonumentId} can not find any spawn point.");
                self.Player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, CommonTipConst.BeeBuzzExceedSummonLimit);
                return EOpCode.BeeBuzzCardSpwanNotMeetingConditions;
            }

            using var _ = NodeOpContext.GetNew("UseBeeBuzzSignalCardDone").SetTriggerBeltAutoSupply(true);
            ret = self.Root.RequireNode(new NodeOpByExisting(cardItemNode));
            if (ret != EOpCode.Success)
            {
                logger.Error($"[UseBeeBuzzSignalCardDone] card uid: {itemUid}, card biz id:{cardItemNode.BizId}, ret:{ret} require rode fail");
                self.Player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, CommonTipConst.BeeBuzzWrongMonument);
                return ret;
            }

            var groupEntity = new BeeBuzzGroupEntity(beeBuzzTemplateId, self.Player.MonumentId, self.Player.TeamId > 0 ? self.Player.TeamId : self.EntityId);
            EntityManager.Instance.AddEntity(groupEntity);
            logger.Info($"[UseBeeBuzzSignalCardDone] card uid: {itemUid}, card biz id:{cardItemNode.BizId}, monument id:{self.Player.MonumentId} bee buzz:{groupEntity.EntityId}, groupEntity{groupEntity.EntityId}, new group create");
            return ret;
        }

        private static EOpCode UseWaterBottleDone(this PlayerInventoryComponent self, BaseItemNode itemNode, long bizId, ref long requireBizId, ref int requireCount)
        {
            using var ctx = NodeOpContext.GetNew(OpContextConst.USE_ITEM);
            if (itemNode is not WaterBottleItemNode waterBottle)
            {
                return EOpCode.ItemNotExisted;
            }

            var waterNode = waterBottle.GetChild(0) as StackableItemNode;
            if (waterNode == null)
            {
                return EOpCode.ItemNotExisted;
            }

            requireBizId = waterNode.BizId;
            var drinkLimit = McCommon.Tables.TbGlobalConfig.DrinkChangeWater;
            requireCount = waterNode.Count >= drinkLimit ? drinkLimit : waterNode.Count;

            return self.Player.Root.RequireNode(new NodeOpByIndex(waterNode.GetPath(), new IntCount(requireCount)));
        }

        private static EOpCode UseCommonItemDone(this PlayerInventoryComponent self, BaseItemNode itemNode, long bizId)
        {
            using var ctx = NodeOpContext.GetNew(OpContextConst.USE_ITEM);
            if (itemNode == null)
            {
                return self.Player.Root.RequireNode(new NodeOpByBizId(bizId, 1));
            }

            ctx.SetTriggerBeltAutoSupply(true);
            return self.Player.Root.RequireNode(new NodeOpByIndex(itemNode.GetPath(), new IntCount(1)));
        }

        [RpcHandler(ExposeToClient = true)]
        public static void RechargeItem(this PlayerInventoryComponent self, long itemUid)
        {
            var player = self.Player;
            NodeBase node = player.Root.GetNodeById(itemUid);
            if (node is not BaseItemNode itemNode)
            {
                self.Logger.Info($"[RechargeItem]ItemNode is null! roleId:{player.RoleId}, itemUid:{itemUid}");
                return;
            }

            if (!itemNode.HasFlag(ItemFlags.Rechargeable))
            {
                self.Logger.Error($"[RechargeItem]ItemNode without flag Rechargeable! roleId:{player.RoleId}, itemUid:{itemUid}, bizId: {itemNode.BizId}");
                return;
            }

            itemNode.MaxCondition = ItemUtility.FormulaCalcNewMaxCondition(
                itemNode.Condition, itemNode.MaxCondition, McCommon.Tables.TbGlobalConfig.RechargeConditionLoss);
            itemNode.Condition = itemNode.MaxCondition;
            self.Logger.Info($"[RechargeItem]Success! roleId:{player.RoleId}, itemUid:{itemUid}");
        }

        public static void GetEnterInventoryItemsFromLobby(this PlayerInventoryComponent self)
        {
            if (!McCommon.Tables.TbGlobalConfig.ItemIntroductionNotice) return;
            var player = self.Player;
            if (player.IsSoftOffline) return;
            // todo@bxl 这里如果没有及时返回数据，玩家就下线了，会导致数据丢失
            MongoProxy.Find(MongoCollectionName.INVENTTORY_ITEMS, player.RoleId, player.EntityId, (id, item) =>
            {
                if (item == null) return;

                var inventoryItems = item as CustomType.InventoryItems;
                if (inventoryItems == null)
                {
                    self.Logger.Error($"[GetEnterInventoryItemsFromLobby] get hashset fail! {player.EntityId}");
                    return;
                }

                foreach (var itemId in inventoryItems.ItemSet)
                {
                    self.HasEnterInventoryItems.Add(itemId);
                }
                // 对于玩家下线后获取的新物品, 登录的时候需要提示
                foreach (var offlineEnterItem in self.EnterItems)
                {
                    if (!inventoryItems.EnterItems.Contains(offlineEnterItem))
                    {
                        self.RemoteCallNewItemTips(ERpcTarget.OwnClient, offlineEnterItem);
                        inventoryItems.EnterItems.Add(offlineEnterItem);
                        self.IsEnterNewItem = true;
                    }
                }
                foreach (var itemId in inventoryItems.EnterItems)
                {
                    self.EnterItems.Add(itemId);
                }
                self.DbInventoryItems = inventoryItems;
                self.Logger.Info($"[GetEnterInventoryItemsFromLobby] item count {self.HasEnterInventoryItems.Count} {self.EnterItems.Count}");
            });
            self.IsEnterNewItem = false;
        }

        public static void PushInventoryItemsToLobby(this PlayerInventoryComponent self)
        {
            if (!McCommon.Tables.TbGlobalConfig.ItemIntroductionNotice) return;
            if (!self.IsEnterNewItem) return;
            if (self.DbInventoryItems == null)
            {
                self.DbInventoryItems = new Common.CustomType.InventoryItems();
            }

            foreach (var itemId in self.HasEnterInventoryItems)
            {
                self.DbInventoryItems.ItemSet.Add(itemId);
            }
            foreach (var itemId in self.EnterItems)
            {
                self.DbInventoryItems.EnterItems.Add(itemId);
            }
            var jsonData = self.DbInventoryItems.ToEJson();
            MongoProxy.UpdateOrInsert(MongoCollectionName.INVENTTORY_ITEMS, self.Player.RoleId, jsonData);
            self.HasEnterInventoryItems.Clear();
            self.DbInventoryItems = null;
        }

        [RpcHandler(ExposeToClient = true)]
        public static void UpdateEnterInventoryItems(this PlayerInventoryComponent self, List<long> items)
        {
            if (!McCommon.Tables.TbGlobalConfig.ItemIntroductionNotice) return;
            foreach (var item in items)
            {
                var itemConfig = McCommon.Tables.TbItemConfig.GetOrDefault(item);
                if (itemConfig == null)
                {
                    self.Logger.Warn($"[UpdateEnterInventoryItems] item config not found! {item}");
                    continue;
                }
                if (!itemConfig.FirstGetNotify)
                {
                    self.Logger.Warn($"[UpdateEnterInventoryItems] item not allow enter first get notify inventory! {item}");
                    continue;
                }
                self.HasEnterInventoryItems.Add(item);
            }
        }

        [RpcHandler(ExposeToClient = true)]
        public static void ChangeItemSkin(this PlayerInventoryComponent self, long itemUId, long skinUId)
        {
            var root = self.Root;
            var player = self.Player;
            var itemNode = root.GetNodeById(itemUId) as BaseItemNode;
            if (itemNode == null)
            {
                self.Logger.Info($"temUid:{itemUId}, itemNode is null");
                return;
            }

            long skinId = 0;
            if (skinUId != 0)
            {
                if (root.GetNodeById(skinUId) is not SkinNode skinNode)
                {
                    self.Logger.Info($"skinUId:{skinUId}, skinNode is null");
                    return;
                }

                if (skinNode.IsExpired())
                {
                    self.Logger.Info($"skinId:{skinUId}, skinUId:{skinUId}, skin expired");
                    return;
                }

                var skinConfig = McCommon.Tables.TbSkinConfig.GetOrDefault(skinNode.BizId);
                if (skinConfig == null)
                {
                    self.Logger.Info($"skin bizId:{skinNode.BizId}, fail to get skin config");
                    return;
                }

                if (skinConfig.ItemId != itemNode.BizId)
                {
                    self.Logger.Info($"config item id:{skinConfig.ItemId} != Item node bizId:{itemNode.BizId}");
                    return;
                }

                skinId = skinNode.BizId;
            }


            var parentNode = itemNode.GetParentNode() as ItemContainerNode;
            if (parentNode == null)
            {
                self.Logger.Error($"UseItemForSwitchModel fail! parentNode is null! roleId:{player.RoleId} itemUid:{itemNode.Id} bizId {itemNode.BizId}");
                return;
            }

            if (itemNode is WeaponAccessoryItemNode waItemNode)
            {
                self.Logger.Info($"can not change accessory item node");
                return;
            }

            var oldSkinId = itemNode.SkinId;
            itemNode.SkinId = skinId;

            // 更改武器皮肤,把配件一起换掉
            if (itemNode is WeaponItemNode weaponNode)
            {
                if (skinId == 0)
                {
                    foreach (var child in weaponNode.GetChildren())
                    {
                        var childNode = weaponNode.GetChildNode(child.Key);
                        if (childNode is WeaponAccessoryItemNode waNode)
                        {
                            waNode.SkinId = 0;
                        }
                    }
                }
                else
                {
                    var skinConfig = McCommon.Tables.TbSkinConfig.GetOrDefault(skinId);
                    if (skinConfig != null)
                    {
                        foreach (var accessory in skinConfig.Bindlist)
                        {
                            var accessorySkinConfig = McCommon.Tables.TbSkinConfig.GetOrDefault(accessory);
                            foreach (var child in weaponNode.GetChildren())
                            {
                                var childNode = weaponNode.GetChildNode(child.Key);
                                if (childNode is WeaponAccessoryItemNode waNode)
                                {
                                    if (waNode.BizId == accessorySkinConfig.ItemId)
                                    {
                                        waNode.SkinId = accessorySkinConfig.Id;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (parentNode.Id == NodeConst.WearItemContainerNodeId || parentNode.Id == NodeConst.BeltItemContainerNodeId)
            {
                RpcEntity.Instance.RemoteCallChangeWeaponItemEntitySkin(ERpcTarget.Simulator, ProcessEntity.Instance.Sequence, player.EntityId, itemNode.Id, (int)itemNode.Index, skinId);
                if (parentNode.Id == NodeConst.WearItemContainerNodeId)
                {
                    player.Trigger(new PlayerEquipmentChangeEvent(player.RoleId));
                }
                else if (parentNode.Id == NodeConst.BeltItemContainerNodeId)
                {
                    player.Trigger(new PlayerWeaponChangeEvent(player.RoleId));
                }
            }

            self.RemoteCallChangeSkinResult(ERpcTarget.OwnClient, 0);
            self.Logger.Info($"player:{player.RoleId}, itemUid:{itemUId}, old skin:{oldSkinId} change skin:{skinId}");
        }

        public static CustomTypeList<EquipmentDisplayData> GetEquipmentDisplayData(this PlayerInventoryComponent self)
        {
            var equipmentDisplayDatas = new CustomTypeList<EquipmentDisplayData>();
            var wearContainer = self.SystemRoot.GetChild(PlayerInventoryNodeIndex.Wear) as ItemContainerNode;
            foreach (var (_, node) in wearContainer)
            {
                if (node is not BaseItemNode itemNode) continue;
                equipmentDisplayDatas.Add(new EquipmentDisplayData(itemNode.BizId, itemNode.SkinId));
            }
            return equipmentDisplayDatas;
        }

        public static CustomTypeList<WeaponDisplayData> GetWeaponDisplayData(this PlayerInventoryComponent self)
        {
            var weaponDisplayDatas = new CustomTypeList<WeaponDisplayData>();
            var beltContainer = self.SystemRoot.GetChild(PlayerInventoryNodeIndex.Belt) as ItemContainerNode;

            foreach (var (_, node) in beltContainer)
            {
                if (node is not BaseItemNode itemNode) continue;
                if (node.Index != (int)HoldItemIndex.Item7 && node.Index != (int)HoldItemIndex.Item8) continue;
                if (itemNode is not WeaponItemNode weaponNode) continue;
                var weaponDisplayData = new WeaponDisplayData
                {
                    HoldItemIndex = (int)weaponNode.Index,
                    TableId = weaponNode.BizId,
                    SkinId = weaponNode.SkinId,
                };
                foreach (var (_, accessory) in weaponNode)
                {
                    if (accessory is not WeaponAccessoryItemNode accessoryItemNode) continue;
                    weaponDisplayData.AccessoryDisplayDatas.Add(new AccessoryDisplayData(accessoryItemNode.BizId, accessoryItemNode.SkinId));
                }
                weaponDisplayDatas.Add(weaponDisplayData);
            }
            return weaponDisplayDatas;
        }

        private static EOpCode OnCheckLooting(this PlayerInventoryComponent self, CheckLootingAbilityArgs _)
        {
            if (self.Player.IsOnline && !self.Player.CanLooting())
            {
                return EOpCode.ContainerCannotLooting;
            }

            // 玩家安全区下线，不能被掠夺
            if ((self.Player.IsOffline && self.Player.SafetyAreaState <= (int)ESafeAreaType.Inside) || self.Player.CampingTentComp.CampingTentEntityId > 0)
            {
                return EOpCode.PlayerInSafeArea;
            }

            return EOpCode.Success;
        }

        private static void OnStartLooting(this PlayerInventoryComponent self, StartLootingAbilityArgs _)
        {
            RpcEntity.Instance.RemoteCallOnNotifyPlayerLooting(ERpcTarget.Simulator, self.ParentId, true);
        }

        private static void OnStopLooting(this PlayerInventoryComponent self, StopLootingAbilityArgs _)
        {
            RpcEntity.Instance.RemoteCallOnNotifyPlayerLooting(ERpcTarget.Simulator, self.ParentId, self.Root.CurrentLootingMeEntityIds.Count > 0);
        }

        /// <summary>
        /// 独立完成一个业务，记录了日志
        /// 外面可不判返回值使用
        /// </summary>
        private static EOpCode MoveExtraPackInner(this PlayerInventoryComponent self, List<long> srcPath, List<long> targetPath, bool isForce)
        {
            var root = self.Root;
            var srcItem = root.GetNodeByPath(srcPath);
            if (srcItem is VirtualNodeBase) srcItem = null;
            var targetItem = root.GetNodeByPath(targetPath);
            if (targetItem is VirtualNodeBase) targetItem = null;
            var srcPathIsExtraPack = ItemUtility.IsPathPointingLiveWearingExtraPack(srcPath);
            var targetPathIsExtraPack = ItemUtility.IsPathPointingLiveWearingExtraPack(targetPath);

            // 不允许将角色自己装备的扩展背包脱到角色自己装备的扩展背包格子中
            if (srcPathIsExtraPack && targetPath.Count >= 3 && srcPath[0] == targetPath[0] && srcItem is ExtraPackItemNode srcPackNode)
            {
                var mainInv = srcPackNode.GetRootNode().GetNodeById(NodeConst.MainItemContainerNodeId) as ItemContainerNode;
                var startIndex = mainInv.Capacity - srcPackNode.ContainerConfig.Manufacturing;
                if (targetPath[2] >= startIndex)
                {
                    return EOpCode.InvalidPath;
                }
            }

            // a 容器中的背包到装备栏（未装备背包）
            // b 可写容器中含道具的背包到装备栏（已有背包）
            // c 不可写容器中含道具的背包到装备栏（已有背包）
            // d 装备栏的背包到容器空格
            // e 装备栏的背包到容器中的背包上
            // f 装备栏到另一个活人的装备栏，对方已有背包
            // g 装备栏到另一个活人的装备栏，对方没有背包
            // h 容器到另一个活人的装备栏，对方没有背包
            // i 容器到另一个活人的装备栏，对方已有背包

            // 保持拖动背包到装备栏上。如果拖动装备栏的背包到容器的背包道具上，就反过来理解这件事
            // 确保src是别人，target是自己
            if (srcPathIsExtraPack && !targetPathIsExtraPack && targetItem is ExtraPackItemNode)
            {
                (srcPath, targetPath) = (targetPath, srcPath);
                (srcItem, targetItem) = (targetItem, srcItem);
                (srcPathIsExtraPack, targetPathIsExtraPack) = (targetPathIsExtraPack, srcPathIsExtraPack);
                NodeOpContext.GetCurrent().SetSwapPath(true);
                // 情况e变成了b、c
            }

            if (targetPathIsExtraPack && srcPathIsExtraPack)
            {
                if (srcItem is not ExtraPackItemNode srcPack) return EOpCode.ArgsError;
                if (targetItem is ExtraPackItemNode targetPack)
                {
                    // 情况f
                    // 换成src是别人，target是自己
                    if (targetPath[0] == NodeSystemType.OtherPlayerInventory)
                    {
                        (srcPath, targetPath) = (targetPath, srcPath);
                        (srcPack, targetPack) = (targetPack, srcPack);
                    }
                    return self.SwapExtraPackBetweenPlayers(srcPack, targetPack, srcPath, targetPath, isForce);
                }
                else
                {
                    // 情况g，装备栏拖动
                    return self.MoveExtraPackBetweenPlayers(srcPack, srcPath, targetPath, isForce);
                }
            }
            else if ((srcPathIsExtraPack || ItemUtility.IsPathPointingWearingExtraPack(srcPath)) && !targetPathIsExtraPack && targetItem == null)
            {
                // 脱下，情况d
                if (srcItem is not ExtraPackItemNode srcPack) return EOpCode.ArgsError;
                return self.TakeoffExtraPack(srcPack, targetPath);
            }
            else if (targetPathIsExtraPack && srcItem is ExtraPackItemNode)
            {
                //情况a、b、c、h、i
                var srcPack = srcItem as ExtraPackItemNode;
                // 把背包移动到了装备栏
                if (targetPath[0] == NodeSystemType.PlayerInventory)
                {
                    NodeOpContext.GetCurrent().SetIgnoreOpTips(true);
                    var currentBackpack = root.GetNodeByPath(PlayerBackpackPath) as ExtraPackItemNode;
                    if (currentBackpack == null)
                    {
                        // 纯装备，情况a
                        return self.EquipExtraPack(srcPack);
                    }
                    else
                    {
                        // 更换，情况b、c
                        return self.ExchangeExtraPack(srcPack, currentBackpack, srcPath, targetPath, isForce);
                    }
                }
                else if (targetPath[0] == NodeSystemType.OtherPlayerInventory)
                {
                    // 情况h、i

                    using var transaction = EntityTransaction.Start($"EquipExtraPackForOther-{self.Player.RoleId}-{srcPack}");
                    var targetPack = root.GetNodeByPath(OtherPlayerBackpackPath) as ExtraPackItemNode;
                    if (targetPack == null)
                    {
                        var code = srcPack.RequireSelf();
                        if (code != EOpCode.Success) return TransactionFailWithCode(transaction, "require fail", code);
                        var op = new NodeOpByIndex(targetPath, new ExistingNode(srcPack));
                        code = root.MergeNode(op);
                        if (code == EOpCode.Success) { transaction.Commit(); return EOpCode.Success; }
                        else return TransactionFailWithCode(transaction, "merge fail", code);
                    }
                    else
                    {
                        var code = srcPack.RequireSelf();
                        if (code != EOpCode.Success) return code;

                        code = RemoveExtraPackFromSrc(targetPack, transaction, tempItemListForOriginPack);
                        if (code != EOpCode.Success) return code;

                        var op = new NodeOpByIndex(srcPath, new ExistingNode(targetPack));
                        code = root.MergeNode(op);
                        if (code != EOpCode.Success) return TransactionFailWithCode(transaction, "merge fail", code);

                        op = new NodeOpByIndex(targetPath, new ExistingNode(srcPack));
                        code = root.MergeNode(op);

                        if (code != EOpCode.Success) return TransactionFailWithCode(transaction, "merge other fail", code);

                        self.TryMoveContentsToPath(transaction, NodePathConst.AnywhereInPlayerInventory, tempItemListForOriginPack);
                        if (tempItemListForOriginPack.Count == 0)
                        {
                            transaction.Commit();
                            return EOpCode.Success;
                        }
                        return TransactionFailWithCodeNoWarning(transaction, "inv full", EOpCode.ItemSystemStillNeedMerge);
                    }
                }
                return EOpCode.ServerInternalError;
            }
            else
                return EOpCode.ArgsError;
        }

        private static EOpCode EquipExtraPack(this PlayerInventoryComponent self, ExtraPackItemNode srcPack)
        {
            var root = self.Root;
            var mainInv = root.GetNodeByPath(NodePathConst.PlayerInventoryMain) as ItemContainerNode;
            var mainInvCapacity = mainInv.Capacity;

            using var transaction = EntityTransaction.Start($"EquipExtraPack-{self.Player.RoleId}-{srcPack}");

            // 从原处删除，包括扩容背包
            var code = RemoveExtraPackFromSrc(srcPack, transaction, tempItemListForExtraPack);
            if (code != EOpCode.Success) return code;

            var opData = new NodeOpByIndex(PlayerBackpackPath, new ExistingNode(srcPack));
            // 装背包。装之前记录下原capacity
            code = root.MergeNode(opData);
            if (code != EOpCode.Success) return TransactionFailWithCode(transaction, "Wear extra pack fail", code);

            if (mainInv.Capacity < mainInvCapacity)
            {
                logger.Error($"Extrapack does not add capacity to main inventory {srcPack.BizId}");
                return EOpCode.ServerInternalError;
            }
            // 装道具
            code = self.MoveExtraPackContentsToMainInv(mainInvCapacity, transaction, mainInv, false, tempItemListForExtraPack);
            tempItemListForExtraPack.Clear();
            if (code == EOpCode.Success) transaction.Commit();
            return code;
        }

        private static EOpCode SwapExtraPackBetweenPlayers(this PlayerInventoryComponent self, ExtraPackItemNode srcPack, ExtraPackItemNode targetPack, List<long> srcPath, List<long> targetPath, bool isForce)
        {
            var root = self.Root;
            var roleId = self.Player.RoleId;
            using var transaction = EntityTransaction.Start($"ExchangeExtraPack-{roleId}-{srcPack}-{targetPack}");

            // 从原处删除，包括扩容背包。东西在tempItemListForExtraPack里
            var code = RemoveExtraPackFromSrc(srcPack, transaction, tempItemListForExtraPack);
            if (code != EOpCode.Success) return code;

            code = RemoveExtraPackFromSrc(targetPack, transaction, tempItemListForOriginPack);
            if (code != EOpCode.Success) return code;

            var opData = new NodeOpByIndex(PlayerBackpackPath, new ExistingNode(srcPack));

            var mainInv = root.GetNodeByPath(NodePathConst.PlayerInventoryMain) as ItemContainerNode;
            var mainInvCapacity = mainInv.Capacity;
            // 装背包。装之前记录下原capacity
            code = root.MergeNode(opData);
            if (code != EOpCode.Success) return TransactionFailWithCode(transaction, "Wear extra pack fail", code);

            opData = new NodeOpByIndex(OtherPlayerBackpackPath, new ExistingNode(targetPack));
            code = root.MergeNode(opData);
            if (code != EOpCode.Success) return TransactionFailWithCode(transaction, "Otherplayer wear extra pack fail", code);

            // 塞*2
            tempItemListForExtraPack.AddRange(tempItemListForOriginPack);
            tempItemListForOriginPack.Clear();

            self.TryMoveContentsToMainInv(mainInvCapacity, transaction, mainInv, tempItemListForExtraPack);
            if (tempItemListForExtraPack.Count > 0) self.TryMoveContentsToPath(transaction, NodePathConst.AnywhereInPlayerInventory, tempItemListForExtraPack);

            code = self.ExtraPackBetweenPlayersCommon(transaction, srcPath, targetPath, tempItemListForExtraPack, isForce);
            if (code == EOpCode.Success) transaction.Commit();
            return code;
        }

        private static EOpCode MoveExtraPackBetweenPlayers(this PlayerInventoryComponent self, ExtraPackItemNode srcPack, List<long> srcPath, List<long> targetPath, bool isForce)
        {
            var root = self.Root;
            using var transaction = EntityTransaction.Start($"MoveExtraPack-{self.Player.RoleId}-{srcPack}-{targetPath.PrintPath()}");

            var code = RemoveExtraPackFromSrc(srcPack, transaction, tempItemListForExtraPack);
            if (code != EOpCode.Success) return code;

            var opData = new NodeOpByIndex(targetPath, new ExistingNode(srcPack));
            var mainInv = root.GetNodeByPath(NodePathConst.PlayerInventoryMain) as ItemContainerNode;
            var mainInvCapacity = mainInv.Capacity;

            code = root.MergeNode(opData);
            // 这里不一定是自己装，也可以给别人装
            if (code != EOpCode.Success) return TransactionFailWithCode(transaction, "Wear extra pack fail", code);
            // 东西永远往自己的背包里放
            self.TryMoveContentsToMainInv(mainInvCapacity, transaction, mainInv, tempItemListForExtraPack);
            if (tempItemListForExtraPack.Count > 0) self.TryMoveContentsToPath(transaction, NodePathConst.AnywhereInPlayerInventory, tempItemListForExtraPack);

            code = self.ExtraPackBetweenPlayersCommon(transaction, srcPath, targetPath, tempItemListForExtraPack, isForce);
            if (code == EOpCode.Success) transaction.Commit();
            return code;
        }

        private static EOpCode ExtraPackBetweenPlayersCommon(this PlayerInventoryComponent self, EntityTransaction transaction, List<long> srcPath, List<long> targetPath, List<BaseItemNode> itemList, bool isForce)
        {
            if (itemList.Count == 0) return EOpCode.Success;
            if (isForce)
            {
                self.TryMoveContentsToPath(transaction, NodePathConst.AnywhereInOtherPlayerInventory, itemList);
                if (itemList.Count == 0) return EOpCode.Success;
                // 不会发生
                var itemLeft = itemList.Count;
                itemList.Clear();
                return TransactionFailWithCode(transaction, $"Exchange extra pack item cannot fill other player inventory {itemLeft}", EOpCode.ItemSystemStillNeedMerge);
            }
            else
            {
                oversizeDict.Clear();
                StatOverflowItems(itemList, oversizeDict);
                transaction.IsLogInfoWhenRollback = true;
                transaction.Rollback(OpCodeReason.Reason[EOpCode.ItemSystemStillNeedMerge]);
                self.RemoteCallMoveExtraPackAck(ERpcTarget.OwnClient, srcPath, targetPath, (int)EOpCode.ItemSystemStillNeedMerge, oversizeDict);
                oversizeDict.Clear();
                itemList.Clear();
                return EOpCode.ItemSystemStillNeedMerge;
            }
        }

        private static EOpCode ExchangeExtraPack(this PlayerInventoryComponent self, ExtraPackItemNode srcPack, ExtraPackItemNode currentBackpack, List<long> srcPath, List<long> targetPath, bool isForce)
        {
            var root = self.Root;
            var player = self.Player;
            using var transaction = EntityTransaction.Start($"ReplaceExtraPack-{player.RoleId}-{srcPack}-{currentBackpack}");

            // 从原处删除，包括扩容背包。东西在tempItemListForExtraPack里
            var code = RemoveExtraPackFromSrc(srcPack, transaction, tempItemListForExtraPack);
            if (code != EOpCode.Success) return code;

            code = RemoveExtraPackFromSrc(currentBackpack, transaction, tempItemListForOriginPack);
            if (code != EOpCode.Success) return code;

            var opData = new NodeOpByIndex(PlayerBackpackPath, new ExistingNode(srcPack));

            var mainInv = root.GetNodeByPath(NodePathConst.PlayerInventoryMain) as ItemContainerNode;
            var mainInvCapacity = mainInv.Capacity;
            // 装背包。装之前记录下原capacity
            code = root.MergeNode(opData);
            if (code != EOpCode.Success) return TransactionFailWithCode(transaction, "Wear extra pack fail", code);

            opData = new NodeOpByIndex(srcPath, new ExistingNode(currentBackpack));
            code = root.MergeNode(opData);
            if (code == EOpCode.ItemSystemCannotInput)
            {
                opData = new NodeOpByIndex(NodePathConst.AnywhereInPlayerInventoryMain, new ExistingNode(currentBackpack));
                // 对方容器不可写，情况c
                code = root.MergeNode(opData);
            }
            if (code != EOpCode.Success) return TransactionFailWithCode(transaction, "merge origin pack fail", code);
            // 装转移而来的道具
            tempItemListForExtraPack.AddRange(tempItemListForOriginPack);
            tempItemListForOriginPack.Clear();

            self.TryMoveContentsToMainInv(mainInvCapacity, transaction, mainInv, tempItemListForExtraPack);
            if (tempItemListForExtraPack.Count > 0) self.TryMoveContentsToPath(transaction, NodePathConst.AnywhereInPlayerInventory, tempItemListForExtraPack);
            if (tempItemListForExtraPack.Count == 0)
            {
                self.RemoteCallMoveExtraPackAck(ERpcTarget.OwnClient, srcPath, targetPath, (int)EOpCode.Success, null);
                transaction.Commit();
                return EOpCode.Success;
            }

            if (isForce)
            {
                if (srcPath[0] == NodeSystemType.PickUpSystem || (currentBackpack.GetParent() is ItemContainerNode icn && icn.HasFlag(ItemContainerFlag.CannotInput)))
                {
                    transaction.Commit();
                    if (tempItemListForExtraPack.Contains(currentBackpack))
                    {
                        var i = 0;
                        // 地上丢的背包东西要放扩展背包里
                        foreach (var item in tempItemListForExtraPack)
                        {
                            if (item != currentBackpack)
                            {
                                item.Index = i++;
                                currentBackpack.AddDetachedNode(item);
                            }
                        }
                        currentBackpack.Drop(new Vector3(player.PosX, player.PosY, player.PosZ), Vector3.Zero, player.RoleId);
                    }
                    else
                    {
                        foreach (var item in tempItemListForExtraPack)
                        {
                            item.Drop(new Vector3(player.PosX, player.PosY, player.PosZ), Vector3.Zero, player.RoleId);
                        }
                    }
                    return EOpCode.Success;
                }
                else
                {
                    var dstPath = new List<long> { srcPath[0], NodePathConst.Anywhere };
                    foreach (var item in tempItemListForExtraPack)
                    {
                        opData = new NodeOpByIndex(dstPath, new ExistingNode(item)).WithOption(EOversizeOptionEnum.Drop);
                        var ret = root.MergeNode(opData);
                        if (ret != EOpCode.Success)
                        {
                            TransactionFailWithCode(transaction, "Merge node with drop failed", code);
                            return ret;
                        }
                    }
                    transaction.Commit();
                    return EOpCode.Success;
                }
            }
            else
            {
                oversizeDict.Clear();
                StatOverflowItems(tempItemListForExtraPack, oversizeDict);
                transaction.IsLogInfoWhenRollback = true;
                transaction.Rollback(OpCodeReason.Reason[EOpCode.ItemSystemStillNeedMerge]);

                if (!NodeOpContext.GetCurrent().IsSwapPath)
                {
                    self.RemoteCallMoveExtraPackAck(ERpcTarget.OwnClient, srcPath, targetPath, (int)EOpCode.ItemSystemStillNeedMerge, oversizeDict);
                }
                else
                {
                    self.RemoteCallMoveExtraPackAck(ERpcTarget.OwnClient, targetPath, srcPath, (int)EOpCode.ItemSystemStillNeedMerge, oversizeDict);
                }

                oversizeDict.Clear();
                return EOpCode.ItemSystemStillNeedMerge;
            }
        }

        private static EOpCode RemoveExtraPackFromSrc(ExtraPackItemNode srcPack, EntityTransaction transaction, List<BaseItemNode> contents)
        {
            EOpCode code;
            // 整理有多少道具需要挪过来
            contents.Clear();

            if (srcPack.ParentId == NodeConst.WearItemContainerNodeId) // 穿在人身上
            {

                var mainInv = srcPack.GetRootNode().GetNodeById(NodeConst.MainItemContainerNodeId) as ItemContainerNode;
                var startIndex = mainInv.Capacity - srcPack.ContainerConfig.Manufacturing;
                for (var i = startIndex; i < mainInv.Capacity; i++)
                {
                    var c = mainInv.GetChild(i);
                    if (c == null) continue;
                    else if (c is BaseItemNode bin) contents.Add(bin);
                    else logger.Warn($"Item {c} in extra pack {srcPack} is not BaseItemNode");
                }
            }
            else if (srcPack.ParentId == NodeConst.CorpseWearNodeId) // 穿在尸体身上
            {
                var mainInv = srcPack.GetRootNode().GetNodeById(NodeConst.CorpseMainNodeId) as ItemContainerNode;
                var startIndex = mainInv.Capacity - srcPack.ContainerConfig.Manufacturing;
                for (var i = startIndex; i < mainInv.Capacity; i++)
                {
                    var c = mainInv.GetChild(i);
                    if (c == null) continue;
                    else if (c is BaseItemNode bin) contents.Add(bin);
                    else logger.Warn($"Item {c} in extra pack {srcPack} is not BaseItemNode");
                }
            }
            else // 也许是地上，里面就有东西。也许是容器里，里面就不可能有东西
            {
                foreach (var (_, item) in srcPack)
                {
                    if (item is BaseItemNode bin) contents.Add(bin);
                    else logger.Warn($"Item {item} in extra pack {srcPack} is not BaseItemNode");
                }
            }

            contents.Add(srcPack);

            // 删除
            foreach (var item in contents)
            {
                code = item.RequireSelf();
                if (code != EOpCode.Success) return TransactionFailWithCode(transaction, $"Consume inner item {item} fail", code);
            }
            contents.RemoveAt(contents.Count - 1); // 删掉srcPack
            return EOpCode.Success;
        }

        private static EOpCode MoveExtraPackContentsToMainInv(this PlayerInventoryComponent self, int startIndex, EntityTransaction transaction, ItemContainerNode inv, bool dropIfExceeds, List<BaseItemNode> contents)
        {
            NodeOpByIndex opData;
            EOpCode code;
            IList<long> mergePath = new List<long>(NodePathConst.PlayerInventoryMain);
            mergePath.Add(startIndex);
            foreach (var item in tempItemListForExtraPack)
            {
                if (startIndex > inv.Capacity) mergePath = NodePathConst.AnywhereInPlayerInventoryMain;
                else mergePath[mergePath.Count - 1] = startIndex++;
                opData = new NodeOpByIndex(mergePath, new ExistingNode(item));
                if (dropIfExceeds) opData.WithOption(EOversizeOptionEnum.Drop);
                code = self.Root.MergeNode(opData);
                if (code != EOpCode.Success) return TransactionFailWithCodeNoWarning(transaction, "Merge pack contents fail", code);
            }
            return EOpCode.Success;
        }

        /// <summary>
        /// 未移动成功的留在contents里
        /// </summary>
        /// <param name="self"></param>
        /// <param name="startIndex"></param>
        /// <param name="transaction"></param>
        /// <param name="inv"></param>
        /// <param name="contents"></param>
        private static void TryMoveContentsToMainInv(this PlayerInventoryComponent self, int startIndex, EntityTransaction transaction, ItemContainerNode inv, List<BaseItemNode> contents)
        {
            NodeOpByIndex opData;
            List<long> mergePath;
            if (inv.GetRootNode().GetHostComponent() == self)
                mergePath = new(NodePathConst.PlayerInventoryMain);
            else
                mergePath = new() { NodeSystemType.OtherPlayerInventory, PlayerInventoryNodeIndex.Main };
            mergePath.Add(startIndex);
            contents.Reverse();
            var i = contents.Count;
            while (--i >= 0)
            {
                if (startIndex >= inv.Capacity) mergePath[mergePath.Count - 1] = -1;
                else mergePath[mergePath.Count - 1] = startIndex++;
                opData = new NodeOpByIndex(mergePath, new ExistingNode(contents[i]));
                var code = self.Root.MergeNode(opData);
                if (code == EOpCode.Success) contents.RemoveAt(i);
            }
        }

        /// <summary>
        /// 未移动成功的留在contents里
        /// </summary>
        /// <param name="self"></param>
        /// <param name="transaction"></param>
        /// <param name="path"></param>
        /// <param name="contents"></param>
        private static void TryMoveContentsToPath(this PlayerInventoryComponent self, EntityTransaction transaction, IList<long> path, List<BaseItemNode> contents)
        {
            NodeOpByIndex opData;
            var i = contents.Count;
            while (--i >= 0)
            {
                opData = new NodeOpByIndex(path, new ExistingNode(contents[i]));
                var code = self.Root.MergeNode(opData);
                if (code == EOpCode.Success) contents.RemoveAt(i);
            }
        }

        private static void StatOverflowItems(List<BaseItemNode> itemList, Dictionary<long, int> bizId2Count)
        {
            foreach (var item in itemList)
            {
                oversizeDict[item.BizId] = oversizeDict.GetValueOrDefault(item.BizId, 0) + item.GetCount();
            }
        }

        private static EOpCode TakeoffExtraPack(this PlayerInventoryComponent self, ExtraPackItemNode srcPack, List<long> targetPath)
        {
            var root = self.Root;
            using var transaction = EntityTransaction.Start($"UnequipExtraPack-{self.Player.RoleId}-{srcPack}");
            RemoveExtraPackFromSrc(srcPack, transaction, tempItemListForExtraPack);

            var opData = new NodeOpByIndex(targetPath, new ExistingNode(srcPack));
            var code = root.MergeNode(opData);
            if (code != EOpCode.Success)
            {
                transaction.Rollback($"Unequip extra pack fail {code}");
                self.Player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, CommonTipConst.BackpackFull);
                return code;
            }

            var mainInv = root.GetNodeByPath(NodePathConst.PlayerInventoryMain) as ItemContainerNode;
            var mainInvCapacity = mainInv.Capacity;
            // 不管如何，东西往我身上加
            self.TryMoveContentsToMainInv(mainInvCapacity, transaction, mainInv, tempItemListForExtraPack);
            if (tempItemListForExtraPack.Count > 0) self.TryMoveContentsToPath(transaction, NodePathConst.AnywhereInPlayerInventory, tempItemListForExtraPack);
            if (tempItemListForExtraPack.Count == 0)
            {
                transaction.Commit();
                return EOpCode.Success;
            }
            else
            {
                transaction.Rollback($"Inv full", false);
                self.Player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, CommonTipConst.BackpackFull);
                return EOpCode.ItemSystemFull;
            }
        }

        private static void DropExtraPack(this PlayerInventoryComponent self, List<long> itemPath, bool isForce)
        {
            var player = self.Player;
            var roleId = player.RoleId;
            var root = self.Root;
            NodeOpContext.GetCurrent().SetOpRoleId(roleId);
            oversizeDict.Clear();
            using var transaction = EntityTransaction.Start($"DropExtraPack-{roleId}-{itemPath.PrintPath()}-{isForce}");
            if (!isForce) transaction.IsLogInfoWhenRollback = true;

            EOpCode code;
            do
            {
                code = self.SystemRoot.RequireExtraPack(itemPath, out var srcPack, out var srcItems);
                if (code != EOpCode.Success)
                {
                    transaction.Rollback($"[DropExtraPack]Drop Pack Failed with code {code}. Player: {roleId}, Path: {string.Join('.', itemPath)}");
                    break;
                }
                var oversizeItems = new List<BaseItemNode>();
                foreach (var item in srcItems)
                {
                    code = root.MergeNode(new NodeOpByIndex(NodePathConst.AnywhereInPlayerInventory, new ExistingNode(item)));
                    if (code != EOpCode.Success) oversizeItems.Add(item);
                }
                if (!isForce && oversizeItems.Count > 0)
                {
                    code = EOpCode.ItemSystemStillNeedMerge;
                    foreach (var item in oversizeItems)
                    {
                        if (!oversizeDict.ContainsKey(item.BizId)) oversizeDict[item.BizId] = 0;
                        oversizeDict[item.BizId] += item.GetCount();
                    }
                    transaction.IsLogInfoWhenRollback = true;
                    transaction.Rollback(OpCodeReason.Reason[EOpCode.ItemSystemStillNeedMerge]);
                    break;
                }
                code = EOpCode.Success;
                transaction.Commit();

                var index = 0;
                foreach (var item in oversizeItems)
                {
                    item.Index = index++;
                    srcPack.AddDetachedNode(item);
                }
                srcPack.Drop(new Vector3(player.PosX, player.PosY, player.PosZ), Vector3.Zero, roleId);
                self.Logger.Info($"[DropExtraPack]Success. Player: {roleId}, Path: {string.Join('.', itemPath)}, Item: {srcPack.Id}");
            }
            while (false);

            if (!isForce && code != EOpCode.Success)
            {
                self.RemoteCallDropExtraPackAck(ERpcTarget.OwnClient, itemPath, (int)code, oversizeDict);
            }
            oversizeDict.Clear();
        }

        private static bool DropItemControl(PlayerInventoryComponent self)
        {
            if (self.RecentDropItemCount >= McCommon.Tables.TbEntityCount.GetMaxRecentDropItemCount())
            {
                if (self.CurrentDropItemCount >= McCommon.Tables.TbEntityCount.GetMaxCurrentDropItemCount())
                {
                    // 超出限制，不允许丢弃
                    return false;
                }

                self.CurrentDropItemCount++;
                if (self.CurrentDropItemCounterTimer == TimerWheelConst.INVALID_TIMER_ID)
                {
                    self.CurrentDropItemCounterTimer = self.AddTimerOnce(1000, OnResetCurrentDropItemCounterHotfix);
                }
            }

            self.RecentDropItemCount++;
            if (self.RecentDropItemCounterTimer == TimerWheelConst.INVALID_TIMER_ID)
            {
                self.RecentDropItemCounterTimer = self.AddTimerOnce(60 * 1000, OnResetCurrentDropItemCounterHotfix);
            }
            else
            {
                if (self.RecentDropItemCount == McCommon.Tables.TbEntityCount.GetMaxRecentDropItemCount())
                {
                    self.SafeCancelTimer(ref self.RecentDropItemCounterTimer);
                    self.RecentDropItemCounterTimer = self.AddTimerOnce(60 * 1000, OnResetCurrentDropItemCounterHotfix);
                }
            }
            return true;
        }

        [Hotfix(TimerCallback = true)]
        public static void OnResetRecentDropItemCounter(this PlayerInventoryComponent self, long timerId)
        {
            self.RecentDropItemCount = 0;
            self.RecentDropItemCounterTimer = TimerWheelConst.INVALID_TIMER_ID;
        }
        [Hotfix(TimerCallback = true)]
        public static void OnResetCurrentDropItemCounter(this PlayerInventoryComponent self, long timerId)
        {
            self.CurrentDropItemCount = 0;
            self.CurrentDropItemCounterTimer = TimerWheelConst.INVALID_TIMER_ID;
        }

        /// <summary>
        /// 设置物品频率
        /// </summary>
        /// <param name="self"></param>
        /// <param name="itemId">物品唯一id</param>
        /// <param name="freq">要设置的频率</param>
        [RpcHandler(ExposeToClient = true, CallInterval = 0.2f)]
        private static void SetItemFrequency(this PlayerInventoryComponent self, long itemId, int freq)
        {
            var baseItem = self.Root.GetNodeById(itemId);
            if (baseItem == null)
            {
                self.Logger.Warn($"NonPublishReport [SetItemFrequency] itemId {itemId} not found");
                return;
            }
            var itemWithFreq = baseItem as IItemWithFrequency;
            if (itemWithFreq == null)
            {
                self.Logger.Warn($"NonPublishReport [SetItemFrequency] itemId {itemId} is not IItemWithFrequency, {baseItem.ToPrettyString()}");
                return;
            }
            if (freq != RFTransmitterNode.NON_FREQ && RFTransmitterNodeHotfix.IsInvalidFrequency(freq))
            {
                self.Logger.Warn($"NonPublishReport [SetItemFrequency] invalid freq {freq}");
                return;
            }
            itemWithFreq.Frequency = freq;
        }

        /// <summary>
        /// 设置引爆方式
        /// </summary>
        /// <param name="self"></param>
        /// <param name="heldItemId">手持物id</param>
        /// <param name="isRFDetonation">是否是射频引爆</param>
        [RpcHandler(ExposeToClient = true)]
        private static void SetDetonationMode(this PlayerInventoryComponent self, long heldItemId, bool isRFDetonation)
        {
            var rfDetonationEntity = self.Player.GetHeldItemByEntityId(heldItemId) as IRFDetonationItemEntity;
            if (rfDetonationEntity == null)
            {
                self.Logger.Warn($"{LogHelper.NON_PUBLISH_REPORT} [SetDetonationMode] heldItemId {heldItemId} is not IRFDetonationEntity");
                return;
            }
            var baseItem = self.Root.GetNodeById(rfDetonationEntity.ItemUid);
            if (baseItem == null)
            {
                self.Logger.Error($"[SetDetonationMode] heldItemId {heldItemId}, ItemUid {rfDetonationEntity.ItemUid} not found");
                return;
            }
            var bombNode = baseItem as TimeBombNode;
            if (bombNode == null)
            {
                self.Logger.Error($"[SetDetonationMode] heldItemId {heldItemId}, ItemUid {rfDetonationEntity.ItemUid} is not TimeBombNode, {baseItem.ToPrettyString()}");
                return;
            }

            rfDetonationEntity.IsRFDetonation = isRFDetonation;
            bombNode.IsRFDetonation = isRFDetonation;
            if (bombNode.IsRFDetonation)
            {
                if (bombNode.Frequency == RFTransmitterNode.NON_FREQ)
                {
                    bombNode.Frequency = RFTransmitterNodeHotfix.RandomFrequency();
                }
            }

        }

        /// <summary>
        /// 频率关联
        /// </summary>
        /// <param name="self"></param>
        /// <param name="itemId">道具id；其他道具的频率设置为和它一致</param>
        [RpcHandler(ExposeToClient = true, CallInterval = 1)]
        private static void FrequencyAssiciate(this PlayerInventoryComponent self, List<long> assiciateIdList, long itemId)
        {
            var baseItem = self.Root.GetNodeById(itemId);
            if (baseItem == null)
            {
                self.Logger.Warn($"{LogHelper.NON_PUBLISH_REPORT} [FrequencyAssiciate] itemId {itemId} not found");
                return;
            }

            if (!self.IsValidAssociateOperation(baseItem, out var fromReceiver))
            {
                return;
            }

            foreach (long associateId in assiciateIdList)
            {
                var associateNode = self.Root.GetNodeById(associateId) as IItemWithFrequency;
                if (associateNode == null)
                {
                    self.Logger.Error($"{LogHelper.NON_PUBLISH_REPORT} [FrequencyAssiciate] associateId {associateId} not found");
                    return; // 不用continue，放置客户端伪造数据，循环空转
                }
                self.AssociateFrequency(fromReceiver, associateNode, (baseItem as IItemWithFrequency).Frequency);
            }

            self.Player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, CommonTipConst.FREQ_ASSOCATE_SUC);
        }

        /// <summary>
        /// 取消频率关联
        /// </summary>
        /// <param name="self"></param>
        /// <param name="cancelItemId">道具唯一id；和该道具频率相同的道具，都设置频率为空</param>
        /// <param name="itemIdList">道具id，都设置频率为空</param>
        [RpcHandler(ExposeToClient = true, CallInterval = 1)]
        private static void CancelFrequencyAssiciate(this PlayerInventoryComponent self, List<long> itemIdList, long cancelItemId)
        {
            var cancelBaseItem = self.Root.GetNodeById(cancelItemId);
            if (cancelBaseItem == null)
            {
                self.Logger.Error($"{LogHelper.NON_PUBLISH_REPORT} [CancelFrequencyAssiciate] itemId {cancelItemId} not found");
                return;
            }

            if (!self.IsValidAssociateOperation(cancelBaseItem, out var receiver))
            {
                return;
            }

            foreach (long itemId in itemIdList)
            {
                var baseItem = self.Root.GetNodeById(itemId) as IItemWithFrequency;
                if (baseItem == null)
                {
                    self.Logger.Error($"{LogHelper.NON_PUBLISH_REPORT} [CancelFrequencyAssiciate] itemId {itemId} not found");
                    return; // 不用continue，放置客户端伪造数据，循环空转
                }

                self.CancelAssociateFrequency(receiver, baseItem, (baseItem as IItemWithFrequency).Frequency);
            }

            self.Player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, CommonTipConst.CANCEL_FREQ_ASSOCATE_SUC);
        }

        private static bool IsValidAssociateOperation(this PlayerInventoryComponent self, NodeBase baseItem, out bool fromReceiver)
        {
            fromReceiver = false;
            var itemWithFreq = baseItem as IItemWithFrequency;
            if (itemWithFreq == null)
            {
                self.Logger.Warn($"{LogHelper.NON_PUBLISH_REPORT} [IsValidAssociateOperation] is not IItemWithFrequency, {baseItem.ToPrettyString()}");
                return false;
            }
            if (itemWithFreq.Frequency < 0)
            {
                self.Player.RemoteCallPopMessageWithNoParams(ERpcTarget.OwnClient, 0, CommonTipConst.ASSOCATE_FAIL_NO_FREQ);
                return false;
            }

            if (itemWithFreq is IItemRFReceiver)
            {
                if (itemWithFreq is TimeBombNode timeBomb && !timeBomb.IsRFDetonation)
                {
                    self.Logger.Warn($"{LogHelper.NON_PUBLISH_REPORT} [IsValidAssociateOperation] timeBomb {timeBomb.Id} is not RF detonation");
                    return false;
                }
                fromReceiver = true;
            }
            else if (itemWithFreq is IItemRFTransmitter)
            {
                fromReceiver = false;
            }
            else
            {
                self.Logger.Warn($"{LogHelper.NON_PUBLISH_REPORT} [IsValidAssociateOperation] is {baseItem.ToPrettyString()}");
                return false;
            }
            return true;
        }

        private static void AssociateFrequency(this PlayerInventoryComponent self, bool fromReceiver, IItemWithFrequency node, int freq)
        {
            if (fromReceiver)
            {
                if (node is IItemRFTransmitter transmitter)
                    transmitter.Frequency = freq;
                else
                {
                    self.Logger.Warn($"{LogHelper.NON_PUBLISH_REPORT}, [AssociateFrequency] ignored, not transmitter");
                }
            }
            else
            {
                if (node is IItemRFReceiver receiver)
                {
                    if (receiver is TimeBombNode timeBomb)
                    {
                        timeBomb.IsRFDetonation = true;
                        var rfDetonationEntity = self.Player.GetHeldItemByEntityId(timeBomb.Id) as IRFDetonationItemEntity;
                        if (rfDetonationEntity != null)
                        {
                            rfDetonationEntity.IsRFDetonation = true;
                        }
                    }
                    receiver.Frequency = freq;
                }
                else
                {
                    self.Logger.Warn($"{LogHelper.NON_PUBLISH_REPORT}, [AssociateFrequency] ignored, not receiver");
                }
            }
        }

        private static void CancelAssociateFrequency(this PlayerInventoryComponent self, bool fromReceiver, IItemWithFrequency node, int freq)
        {
            if (fromReceiver)
            {
                if (node is IItemRFTransmitter transmitter && transmitter.Frequency == freq)
                {
                    transmitter.Frequency = RFTransmitterNode.NON_FREQ;
                }
                else
                {
                    self.Logger.Warn($"{LogHelper.NON_PUBLISH_REPORT}, [CancelAssociateFrequency] ignored, not transmitter or freq diff, node freq {node.Frequency}, target freq {freq}");
                }
            }
            else
            {
                if (node is IItemRFReceiver receiver && receiver.Frequency == freq)
                {
                    if (receiver is TimeBombNode timeBomb)
                    {
                        timeBomb.IsRFDetonation = false;
                        var rfDetonationEntity = self.Player.GetHeldItemByEntityId(timeBomb.Id) as IRFDetonationItemEntity;
                        if (rfDetonationEntity != null)
                        {
                            rfDetonationEntity.IsRFDetonation = false;
                        }
                    }
                    receiver.Frequency = RFTransmitterNode.NON_FREQ;
                }
                else
                {
                    self.Logger.Warn($"{LogHelper.NON_PUBLISH_REPORT}, [CancelAssociateFrequency] ignored, not receiver or freq diff, node freq {node.Frequency}, target freq {freq}");
                }
            }
        }

        /// <summary>
        /// 缓存死亡掉落物品和保护物品
        /// </summary>
        /// <param name="self"></param>
        /// <param name="dropItems"></param>
        public static void CacheDropItems(this PlayerInventoryComponent self, CustomTypeList<BaseItemNode> dropItems)
        {
            self.DropItems = dropItems;
            self.ProtectedItems.Clear();
            var containerIdList = new List<long>()
            {
                NodeConst.MainItemContainerNodeId,
                NodeConst.BeltItemContainerNodeId,
                NodeConst.WearItemContainerNodeId,
                NodeConst.SeedBackpackContainerNodeId
            };
            foreach (var containerId in containerIdList)
            {
                var container = self.Root.GetNodeById(containerId) as ItemContainerNode;
                if (container == null) continue;
                foreach (var (_, node) in container)
                {
                    if (node is BaseItemNode itemNode)
                    {
                        self.ProtectedItems.Add(NodeSystemHelper.CloneItem(itemNode));
                    }
                }
            }
            self.Logger.InfoFormat("[CacheDropItems] {0} ----------------- {1}", self.DropItems.ToEJson().ToString(), self.ProtectedItems.ToEJson().ToString());
        }

        private static void SyncWeaponInfoFromEmbedded(this PlayerInventoryComponent self)
        {
            var belt = self.SystemRoot.GetChild(PlayerInventoryNodeIndex.Belt) as ItemContainerNode;
            foreach (var (_, item) in belt)
            {
                if (item is not BaseItemNode bin) continue;
                if (item is IHaveBulletItemNode)
                {
                    self.SyncInfoFromEntity(bin);
                }
            }
        }
    }
}
