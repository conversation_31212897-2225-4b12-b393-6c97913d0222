﻿
using Assets.Scripts.MicroServiceClient;
using SimpleJSON;
using System;
using System.Collections.Generic;
using UnityEditor;
using WizardGames.Soc.Common.Const;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Data.resource;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocClient.Http;
using WizardGames.SocConst.Soc.Const;
using static WizardGames.Soc.SocClient.Ui.GSkinOptionTip;

namespace WizardGames.Soc.SocClient.Manager
{
    /// <summary>
    /// 局内换肤接口类
    /// 局内默认继续使用大厅的皮肤缓存数据，做增量更新
    /// </summary>
    class WorldSkinService : ISkinService
    {
        private readonly string LobbySkinServiceRoot = "playergrowth/equipment";
        private SocLogger logger = LogHelper.GetLogger(typeof(WorldSkinService));
        //SkinNode缓存 key为业务id（BizId）
        private Dictionary<long, SkinNode> skinNodeCache;
        //SkinRootNode
        private SystemRootNode skinRootNode;
        //装备皮肤回调
        private Action skinBatchChangeCallback;
        //收藏回调
        private Action<long> skinStarCallback;
        //背包内物品换肤回调
        public Action inventoryItemSkinChangeCallback;
        private BaseItemNode currentItemNode;
        /// <summary>
        /// 大厅 - 局内皮肤类型适配
        /// </summary>
        private Dictionary<ESkinType, ENUMSkinType> skinTypeAdapter = new()
        {
            /*局内皮肤类型
            1=枪械皮肤
            2=近战武器皮肤
            3=装备皮肤
            4=摆件皮肤
            5=建筑结构皮肤
            6=载具皮肤
            */
            {ESkinType.Gun,ENUMSkinType.Weapon},
            {ESkinType.CloseWeapon,ENUMSkinType.Weapon},
            {ESkinType.Equipment,ENUMSkinType.Armor},
            {ESkinType.Furniture,ENUMSkinType.Furniture},
            {ESkinType.Building,ENUMSkinType.Building},
            {ESkinType.Carrier,ENUMSkinType.Vehicle}
        };


        public SkinNode GetSkinNodeBySkinId(long skinId)
        {
            return Mc.MyPlayer.MyEntityServer.SkinComp.GetSkin(skinId);
        }
        public void OnEnabled()
        {
            //建立缓存，用于处理nodeId，业务Id的映射
            skinNodeCache = new();
            this.skinRootNode = Mc.MyPlayer.MyEntityServer.SkinComp.SystemRoot;
            this.skinRootNode.SubscribeAnyUpdateCallback(this.RefreshSkinNodeCache);
            this.RefreshSkinNodeCache();
            //事件
            Mc.Msg.AddListener<int>(EventDefine.BuildSkinBatchChange, this.OnBuildSkinBatchChange);
        }


        public void OnDisabled()
        {
            skinNodeCache.Clear();
            skinNodeCache = null;
            this.skinRootNode?.UnsubscribeAnyUpdateCallback(this.RefreshSkinNodeCache);
            //事件
            Mc.Msg.RemoveListener<int>(EventDefine.BuildSkinBatchChange, this.OnBuildSkinBatchChange);
        }

        /// <summary>
        /// 批量换肤
        /// </summary>
        /// <param name="result"></param>
        private void OnBuildSkinBatchChange(int result)
        {
            if (result == (int)EBatchChangeSkinCode.Suc)
            {
                if (skinBatchChangeCallback != null)
                {
                    skinBatchChangeCallback();
                }
            }
            skinBatchChangeCallback = null;
        }
        //皮肤节点更新
        private void RefreshSkinNodeCache()
        {
            foreach (var item in skinNodeCache)
            {
                item.Value.UnSubscribePropertyChange<long>(SkinNode.PropertyIds.STARRED_TIME, this.OnStarredStatusChanged);
            }
            skinNodeCache.Clear();
            var containerNode = skinRootNode.GetChildNode(SkinContainerIndex.AllSkin) as DirectoryNode;
            foreach (var (_, node) in containerNode)
            {
                if (node is not SkinNode skinNode)
                {
                    logger.ErrorFormat("node is not skin node, bizId {0}, nodeId {1}", node.BizId, node.Id);
                    continue;
                }
                skinNode.SubscribePropertyChange<long>(SkinNode.PropertyIds.STARRED_TIME, this.OnStarredStatusChanged);
                skinNodeCache.Add(skinNode.BizId, skinNode);
                //更新Mgr
                Mc.LobbySkin.AddOrUpdateSkinData(this.CreateSkinDataByNode(skinNode));
            }
        }
        /// <summary>
        /// 根据SkinNode创建CommonSkinData
        /// 皮肤类型目前有差异，枚举需要适配一下
        /// B1保持现状，B2调整
        /// </summary>
        /// <param name="node"></param>
        /// <returns></returns>
        private CommonSkinData CreateSkinDataByNode(SkinNode node)
        {
            ENUMSkinType skinType = ENUMSkinType.Building;
            long confHostItemID;
            //除套装外的皮肤都在此表
            var conf = Mc.Tables.TBSkin.GetOrDefault((int)node.BizId);
            if (conf != null)
            {
                skinType = conf.SkinType;
                confHostItemID = conf.OwnerID;
            }
            //套装皮肤
            else
            {
                var suitConf = Mc.Tables.TbBuildingCoreSkin.GetOrDefault(node.BizId);
                confHostItemID = suitConf?.SkinTexture ?? 0;
            }
            var data = new CommonSkinData(node.BizId, skinType, confHostItemID);
            data.StarredTime = node.StarredTime;
            data.GotTime = node.GotTime;
            data.HostItemID = node.HostItemId;
            return data;
        }
        /// <summary>
        /// 收藏状态变更
        /// </summary>
        /// <param name="base"></param>
        /// <param name="oldValue"></param>
        /// <param name="newValue"></param>
        private void OnStarredStatusChanged(CustomTypeBase @base, long oldValue, long newValue)
        {
            //更新缓存数据
            SkinNode skinNode = @base as SkinNode;
            Mc.LobbySkin.GetSkinData(skinNode.BizId).StarredTime = newValue;
            //回调
            if (this.skinStarCallback != null)
            {
                this.skinStarCallback(newValue);
                this.skinStarCallback = null;
            }
        }

        /// <summary>
        /// 背包更新回调
        /// </summary>
        private void OnInventoryUpdateCallback(CustomTypeBase customTypeBase, long oldId, long newId)
        {
            if (this.inventoryItemSkinChangeCallback != null)
            {
                this.inventoryItemSkinChangeCallback();
                this.inventoryItemSkinChangeCallback = null;
            }
            this.currentItemNode.UnSubscribePropertyChange<long>(BaseItemNode.PropertyIds.SKIN_ID, OnInventoryUpdateCallback);
        }
        /// <summary>
        /// 收藏皮肤
        /// </summary>
        /// <param name="skinId"></param>
        /// <param name="callback"></param>
        /// <param name="cancel"></param>
        public void CollectSkins(long skinId, Action<long> callback, bool cancel = false)
        {
            this.skinStarCallback = callback;
            if (!cancel)
            {
                Mc.MyPlayer.MyEntityServer.SkinComp.RemoteCallStarSkin(ERpcTarget.World, this.GetSkinNodeBySkinId(skinId).Id);
            }
            else
            {
                Mc.MyPlayer.MyEntityServer.SkinComp.RemoteCallUnStarSkin(ERpcTarget.World, this.GetSkinNodeBySkinId(skinId).Id);
            }
        }
        /// <summary>
        /// 装备皮肤
        /// </summary>
        /// <param name="skinId"></param>
        /// <param name="hostItemId"></param>
        /// <param name="callback"></param>
        /// <param name="cancel"></param>
        public void EquipSkin(long skinId, long hostItemId, Action callback, bool cancel = false)
        {
            if (cancel)//卸下皮肤
            {
                Mc.MyPlayer.MyEntityServer.SkinComp.RemoteCallSetDefaultItemSkinId(ERpcTarget.World, hostItemId, 0);
            }
            else//装备皮肤
            {
                Mc.MyPlayer.MyEntityServer.SkinComp.RemoteCallSetDefaultItemSkinId(ERpcTarget.World, hostItemId, skinId);
            }
            JSONObject param = new();
            param.Add("hostItemID", hostItemId);
            param.Add("skinID", skinId);
            var path = $"{LobbySkinServiceRoot}/equipskin";
            if (cancel)
            {
                path = $"{LobbySkinServiceRoot}/unequipskin";
            }
            MicroServiceClient.Instance.CallLobbyPost(path, EHttpReqModule.LobbySkin, (respons) =>
            {
                callback();
            }, param);


            //callback?.Invoke();
        }
        public void EquipBuildSkin(long skinId, long hostItemId, Action callback, bool cancel = false, bool changeDoor = false)
        {
            var node = this.GetSkinNodeBySkinId(skinId);
            var data = Mc.LobbySkin.GetSkinData(skinId);
            this.skinBatchChangeCallback = callback;
            //套装，批量换肤
            if (data.SkinType == ENUMSkinType.Building)
            {
                var curTerrEntId = (Mc.LootCollection.CurLootingEntity as PartEntity).TerritoryId;
                var curTerrEnt = Mc.Entity.GetEntity(curTerrEntId) as TerritoryEntity;
                if (cancel)
                {
                    curTerrEnt.SkinComp.RemoteCallRequestChangeSkinBatch(ERpcTarget.World, (int)data.ConfHostItemID, 0, changeDoor);
                }
                else
                {
                    curTerrEnt.SkinComp.RemoteCallRequestChangeSkinBatch(ERpcTarget.World, (int)data.ConfHostItemID, node.Id, changeDoor);
                }
            }
        }
        /// <summary>
        /// 背包内物品换肤
        /// </summary>
        /// <param name="skinNodeId"></param>
        /// <param name="itemNode"></param>
        /// <param name="callback"></param>
        public void ChangeItemSkin(long skinNodeId, BaseItemNode itemNode, Action callback)
        {
            this.inventoryItemSkinChangeCallback = callback;
            this.currentItemNode = itemNode;
            itemNode.SubscribePropertyChange<long>(BaseItemNode.PropertyIds.SKIN_ID, OnInventoryUpdateCallback);
            Mc.CollectionItem.InventoryCom.RemoteCallChangeItemSkin(ERpcTarget.World, itemNode.Id, skinNodeId);
        }
        public void GetArmorSkins(Action<List<CommonSkinData>> callback)
        {
            //无需请求
        }

        public void GetBuildingSkins(Action<List<CommonSkinData>> callback)
        {
            //无需请求
        }

        public void GetDoorSkins(Action<List<CommonSkinData>> callback)
        {
            //无需请求
        }

        public void GetFurnitureSkins(Action<List<CommonSkinData>> callback)
        {
            //无需请求
        }

        public void GetWeaponSkins(Action<List<CommonSkinData>> callback)
        {
            //无需请求

        }

        void ISkinService.RequireAllCustomPlan(Action callback)
        {
            //throw new NotImplementedException();
        }

        void ISkinService.RequireAllCostumeData(Action callback)
        {
            //throw new NotImplementedException();
        }

        void ISkinService.RequireOptionAndAllPlan(Action callback)
        {
            //throw new NotImplementedException();
        }

        void ISkinService.PostUpdataPlan(Dictionary<int, int> costomPlan, Dictionary<int, int> equipPlan, int planId, Action callBack)
        {
            //throw new NotImplementedException();
        }

        void ISkinService.PostUseConstomPlan(int useId, Action callBack)
        {
            //throw new NotImplementedException();
        }

        void ISkinService.PostUpdataOption(Dictionary<int, bool> optionDic, Action callBack)
        {
            //局内更新
            BasicValueDictionary<int, bool> worldOption = new BasicValueDictionary<int, bool>();
            foreach (var item in optionDic)
            {
                //只更新局内设置
                if (Mc.Tables.TBRoleShowOption.GetOrDefault(item.Key).Type == (int)EShowType.Game)
                {
                    worldOption.Add(item.Key, item.Value);
                }
            }
            Mc.MyPlayer.MyEntityServer.SkinComp.RemoteCallOverrideCostumeOptions(ERpcTarget.World, worldOption);
            //大厅更新
            JSONObject param = new();
            JSONObject optationNode = new JSONObject();

            foreach (var item in optionDic)
            {
                optationNode.Add(item.Key.ToString(), item.Value);
            }

            param.Add("roleShowOptions", optationNode);
            MicroServiceClient.Instance.CallLobbyPost("playergrowth/equipment/updateroleshowoption", EHttpReqModule.LobbySkin,
                (respon) =>
                {
                    Mc.LobbySkin.SetOptionData(optionDic);
                    callBack?.Invoke();
                },
                body: param);
        }

        void ISkinService.RequirePlayerAllCustomPlan(string roleId, Action<Dictionary<int, Dictionary<int, int>>, Dictionary<int, Dictionary<int, int>>> callback)
        {
            var param = new Dictionary<string, string>()
            {
                ["roleID"] = roleId,
            };
            MicroServiceClient.Instance.CallLobbyGet("playergrowth/equipment/getcustomoutfitplansbyroleid", EHttpReqModule.LobbyPlayerInfo, param, callback: (respons) =>
            {
                Dictionary<int, Dictionary<int, int>> allEquipPlan = new Dictionary<int, Dictionary<int, int>>();
                Dictionary<int, Dictionary<int, int>> allCostumePlan = new Dictionary<int, Dictionary<int, int>>();
                JSONNode outfitPlans = respons["outfitPlans"];
                int useId = respons["usedID"];

                var planCfgDataList = Mc.Tables.TBCostumePlan.DataList;
                for (int i = 1; i < planCfgDataList.Count; i++)
                {
                    JSONNode planData = outfitPlans[planCfgDataList[i].PlanID.ToString()];
                    if (planData != null)
                    {
                        JSONNode costumePlan = planData["costumeSlotEquipInfos"];
                        Dictionary<int, int> costumePlanDic = new Dictionary<int, int>();
                        foreach (var item in costumePlan)
                        {
                            costumePlanDic.Add(int.Parse(item.Key), item.Value);
                        }
                        JSONNode equipPlan = planData["equipmentSlotEquipInfos"];
                        Dictionary<int, int> equipPlanDic = new Dictionary<int, int>();
                        foreach (var item in equipPlan)
                        {
                            equipPlanDic.Add(int.Parse(item.Key), item.Value);
                        }
                        allCostumePlan.Add(planCfgDataList[i].PlanID, costumePlanDic);
                        allEquipPlan.Add(planCfgDataList[i].PlanID, equipPlanDic);
                    }
                }
                callback?.Invoke(allCostumePlan, allEquipPlan);
            });
        }

        public void RequireGestureInfo()
        {
            //throw new NotImplementedException();
        }
        public void RequireSpraysInfo()
        {

        }
        public void PostSaveEmoteConfig(List<int> skinIds, ENUMSkinType skinType)
        {

        }
    }
}