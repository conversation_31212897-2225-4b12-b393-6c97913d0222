using Assets.Scripts.UI.Window.UI.UiMission;
using Cysharp.Text;
using FairyGUI;
using System;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.Common.Data.Play;
using WizardGames.Soc.Common.Manager;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.ObjPool;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Unity.Ui;
using WizardGames.Soc.SocClient.Audio;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Ui.Binder.GameBuzzOperation;
using WizardGames.Soc.SocClient.Ui.Utils;
using WizardGames.SocConst.Soc.Const;
using WizardGames.Soc.Common.Play;
namespace WizardGames.Soc.SocClient.Ui
{
    /// <summary>
    /// 行动拓展
    /// </summary>
    public class UiActionExpansion : WindowComBase, IUiFps1Update
    {
        public static SocLogger logger = LogHelper.GetLogger(typeof(UiActionExpansion));

        #region 绑定的UI组件
        private UiActionExpansionBinder binder;
        #endregion
        UiBuzzOperation uiBuzzOperation;
        UiTreasureHuntTask uiTreasureHuntTask;
        UiDailyMission uiDailyMission;
        /// <summary>
        /// 页签数据列表
        /// </summary>
        private List<MissionTabData> tabDataList = new List<MissionTabData>();
        private Dictionary<MissionTabType, int> missionType2TabIndex = new Dictionary<MissionTabType, int>(4);
        private ComCommonNavBar navBar;
        private ComTopBar topBar;


        // 当前选中的页签索引
        private int selectedTabIndex = 0;
        
        private PoiTaskContainer taskContainer => Mc.MyPlayer.MyEntityServer.TaskComponent?.SystemRoot.GetChild(PlayerTaskContainerIndex.Poi) as PoiTaskContainer;
        
        /// <summary>
        /// 初始化
        /// </summary>
        protected override void OnInit()
        {
            base.OnInit();
            CanTouchWhileAnim = true;
            binder = new UiActionExpansionBinder(ContentPane);
            uiBuzzOperation = new UiBuzzOperation();
            uiTreasureHuntTask = new UiTreasureHuntTask();
            uiDailyMission = new UiDailyMission();

            topBar = binder.Root.TopBar.asCom as ComTopBar;
            topBar.SetBackButtonClicked(OnEscClose);
            topBar.SetCurrency(new List<CurrencyInfo>()
            {
                new()
                {
                    Icon = FGUIResPathDef.ACTION_EXPANSION_TOKEN_02,
                    TextFunc = () => taskContainer?.PoiTaskClue > 0
                        ? ZString.Format("{0}/{1}", taskContainer?.PoiTaskClue,
                            McCommon.Tables.TbQuestConst.MaxCountClue)
                        : ZString.Format("[color=#A92E23]{0}[/color]/{1}",
                            taskContainer?.PoiTaskClue, McCommon.Tables.TbQuestConst.MaxCountClue),
                    VisibleFunc = () => binder.CtrlTabIndex.selectedIndex == 0,
                    OnShowTips = () =>
                    {
                        var obj = topBar.CoinList?.GetChildAt(0);
                        if (obj != null)
                        {
                            Vector2 pos = obj.LocalToGlobal(new Vector3(0, obj.height));
                            var tips = LanguageManager.GetTextConst(LanguageConst.TreasureMissionPropExplanation);
                            UiDescTips.Show(tips, pos, false, useHover: true);
                        }
                    },
                    OnHideTips = UiDescTips.Hide,
#if POCO
                    PocoKey = "TopBarTreasureHuntCoinText",
#endif
                }
            });

            uiBuzzOperation.OnInit(binder.Root.Content.UiBuzzOperation);
            uiTreasureHuntTask.OnInit(binder.Root.Content.UiTreasurehuntTask, topBar);
            uiDailyMission.OnInit(binder.Root.Content.UiDailyMission, topBar);

            navBar = new();
            navBar.Init(binder.Root.NavBar.asCom,false);
            navBar.LinkTopBar(topBar, true);
            
            navBar.SetClickBack((data, btnClicked) =>
            {
                uiTreasureHuntTask.ClearChooseIcon();
                UiItemTips.HideTips();
                var index = navBar.GetCurSelectFirstTabId();
                if (selectedTabIndex == index) return;
                selectedTabIndex = index;
                ShowActionPanel(selectedTabIndex);
                binder.TransShow_anim_01.Play();
            });
            LoadTabData();

            navBar.ScrollAndSelect(selectedTabIndex);

            MgrAudio.PlayAudioEventAsync("UI_Common_Open", null, null);

            selectedTabIndex = 0;
            ShowActionPanel(selectedTabIndex);
        }

        public void JumpToTab(int tabIndex)
        {
            selectedTabIndex = tabIndex;
            ShowActionPanel(selectedTabIndex);
            navBar.ScrollAndSelect(selectedTabIndex);
            //tabs.LstNavBtns.RefreshVirtualList();
        }

        public void JumpToTabByMissionType(MissionTabType missionTabType)
        {
            if(missionType2TabIndex.ContainsKey(missionTabType))
            {
                int tabIndex = missionType2TabIndex[missionTabType];
                JumpToTab(tabIndex);
            }
        }

        public void JumpToDaily(int missionId) 
        {
            JumpToTab(0);
            uiDailyMission?.JumpToMission(missionId);
        }


        private void ShowActionPanel(int selectedTabIndex)
        {
            if (tabDataList.Count == 0)
            {
                binder.CtrlTabIndex.SetSelectedIndex(3);//3:默认全隐藏
                return;
            }
            var type = tabDataList[selectedTabIndex].TabType;
            binder.CtrlTabIndex.SetSelectedIndex(ctrlIdx[tabDataList[selectedTabIndex].TabType]);

            topBar.RefreshCurrency();
            uiTreasureHuntTask?.SetIsShowing(tabDataList[selectedTabIndex].TabType == MissionTabType.Treasure);
            if (tabDataList[selectedTabIndex].TabType == MissionTabType.BeeBuzz) Mc.Msg.FireMsg(EventDefine.TryGuideInterface, 515, 5150);
        }


        #region 页签相关
        /// <summary>
        /// 读取90-玩法_任务表中的页签表，非全屏窗口且开启的加入页签显示
        /// </summary>
        public void LoadTabData()
        {
            foreach (MissionTab missionTab in Mc.Tables.TbMissionTab.DataList)
            {
                if (missionTab.IsTagInHalfScreen || !missionTab.Enable)
                {
                    continue;
                }

                if (!UiMissionView.PlayIdCheck(missionTab))
                {
                    continue;
                }

                if ((missionTab.TagType == MissionTabType.Treasure || missionTab.TagType == MissionTabType.BeeBuzz) && PlayHelper.IsNewbieTeaching)
                    continue;

                MissionTabData tabData = Pool.Get<MissionTabData>();
                tabData.Init(missionTab);
                tabDataList.Add(tabData);
            }

            tabDataList.Sort((a, b) => a.Priority.CompareTo(b.Priority));
            missionType2TabIndex.Clear();
            for (int i = 0; i < tabDataList.Count; i++)
            {
                var tab = tabDataList[i];
                if(!missionType2TabIndex.ContainsKey(tab.TabType))
                {
                    missionType2TabIndex.Add(tab.TabType, i);
                }
                NavBarData navBarData = new(tabDataList[i].Name, i, tabDataList[i].Icon, ENavIconType.SmallIcon)
                {
                    RedDotType = redType[tabDataList[i].TabType],
                };
#if POCO
                if (tab.TabType == MissionTabType.Treasure)
                {
                    navBarData.ButtonPocoKeyFormat = "NavBarTreasureHuntBtn";
                }
#endif
                navBar.AddTabData(navBarData);
            }
        }
        #endregion

        public static Dictionary<MissionTabType, RedDotType> redType = new Dictionary<MissionTabType, RedDotType>()
        {
            { MissionTabType.Treasure, RedDotType.Task_ActionExpansion_TreasureTask } ,
            { MissionTabType.BeeBuzz, RedDotType.Task_ActionExpansion_BeeBuzzTask },
            { MissionTabType.Daily, RedDotType.Task_DailyTab }
        };

        public static Dictionary<MissionTabType, int> ctrlIdx = new Dictionary<MissionTabType, int>()
        {
            { MissionTabType.Treasure, 0 } ,
            { MissionTabType.BeeBuzz, 1 },
            { MissionTabType.Daily, 2 }
        };

        protected override void OnEnable()
        {
            base.OnEnable();
            topBar.OnEnable();
            uiTreasureHuntTask.OnEnable();
            uiDailyMission.OnEnable();
        }

        protected override void OnDisable()
        {
            topBar.OnDisable();
            UiItemTips.HideTips();
            uiTreasureHuntTask.OnDisable();
            uiDailyMission.OnDisable();
        }

        public override void OnDestroy()
        {
            base.OnDestroy();
            uiBuzzOperation?.OnDestory();
        }

        public void RefreshTabs()
        {
            navBar.Refresh();
        }
        public static UiActionExpansion Open()
        {
            var win = Mc.Ui.OpenWindowT<UiActionExpansion>("UiActionExpansion");
            return win;
        }
        public override void OnEscClose(EventContext context)
        {
            UiItemTips.HideTips();
            closeCb?.Invoke();
            RemoveSelf();
        }

        public void OnFps1Update(float dt) 
        {
            uiDailyMission.OnFps1Update(dt);
        }

        public Action closeCb = null;
    }

}