using Cysharp.Text;
using Newtonsoft.Json;
using Sirenix.Utilities;
#if POCO
using SocRpcPoco;
#endif
using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;
using System.Linq;
#if DEVELOPMENT_BUILD
using UnityEngine.Profiling;
using WizardGames.Soc.Common.Unity.Construction;
#endif
using WizardGames.Soc.Common.Profile;
using WizardGames.Soc.Common.Unity.Extension;
using WizardGames.Soc.Common.Unity.Loader;
using WizardGames.Soc.Common.Unity.Loader.Logger;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.Common.Unity.Utility;

#if !DISABLE_UWA_SDK
using WizardGames.Soc.SocClient.UWA;
using Unity.Jobs.LowLevel.Unsafe;
#endif
using WizardGames.Soc.SocClient;
using UnityEngine.SceneManagement;
using WizardGames.Soc.Common.ObjPool;
using System.IO.Enumeration;
#if UNITY_EDITOR
using UnityEditor.TestTools.CodeCoverage;
#endif

//自动化测试通用接口，与poco解耦。作为自动化测试与实际项目的中间层实现
public enum ESocAutoTestState
{
    none = 0,
    start = 1,
    settag = 2,
    end = 3,
    upload = 4,
    uploadend = 5,
}
public enum ESocAutoMemroyDevState
{
    none = 0,
    start = 1,
    upload = 2,
}
[Serializable]
public class Metadata
{
    public string FileName;
    public string FileDevice;
    public string FileData;
    public string FileTime;
}
public class Uploaddata
{
    public string time_path_first;
    public string time_path_second;
    public string device_name;
    public string[] step_names;
    public int[] profiler_frame_indices;
    public bool include_spike_snapshot;
}
public class XcodeAttachRequest
{
    public string bundle_id;
    public string xcode_proj;
}
public class MemgraphRequest
{
    public string memgraphFileName;
    public string memgraphFileFolder;
}
public class SocAutoTestManager
{
    public static SocLogger logger = LogHelper.GetLogger(typeof(SocAutoTestManager));
    //public static void PlayGame()
    //{
    //    try
    //    {
    //        Scene appEntryScene = SceneManager.GetSceneByPath("Assets/Scenes/ResUpdateEntry.unity");

    //        if (!appEntryScene.isLoaded)
    //        {
    //            UnityEditor.SceneManagement.EditorSceneManager.OpenScene("Assets/Scenes/ResUpdateEntry.unity", UnityEditor.SceneManagement.OpenSceneMode.Single);
    //        }

    //        // 启动游戏
    //        UnityEditor.EditorApplication.EnterPlaymode();
    //        PlayerPrefs.SetInt("AutoTest", 1);

    //    }
    //    catch (Exception ex)
    //    {
    //        UnityEngine.Debug.LogError(ex.ToString());
    //    }
    //}

    public static ESocAutoTestState SocAutoTestState = ESocAutoTestState.none;
    //public static string url = "*************";
    //public static string url_test = "192.168.186.139";
    public static string GetCodeVersion()
    {
        var parts = SocAppContext.Client_Version.Split('.');
        if (parts.Length > 3)
            return parts[3];
        else
            return SocAppContext.Client_Version;
    }
    public static int uwaState = 0;
    public static string GetResVersion()
    {
        return "";//暂时没有，先留个接口
    }

    public static void StartUWA(string notes,int UWAMode)
    {
#if !DISABLE_UWA_SDK
        UWAEngine.Start((UWAEngine.Mode)UWAMode);
        uwaState = 1;
        SetUWANote(notes);
#endif
    }
    public static void SetUWANote(string notes)
    {
#if !DISABLE_UWA_SDK
        string packname = PerformanceData.GetAppVersionType();
        string tag = "手动";
#if POCO
        tag = "自动";
#endif
        string newnotes = $"{DateTime.Now.ToString("yyyyMMdd")}_{tag}_{packname}_{notes}";
        var accountName = Mc.Config.accountName;
        accountName = accountName.Trim();
        UWAEngine.Note(newnotes+"_"+accountName);
#endif
    }
    public static void EndUWA(string time = "")
    {
#if !DISABLE_UWA_SDK
        UWAEngine.Stop();
        uwaState = 2;
#endif
        PerformanceData.StopFrmaeCount(time);
        if(EnableDevProfiler)
            SetProfilerEnd();
    }
    public static void UploadUWA(Action<bool, string> callback = null)
    {
        uwaState = 3;
#if !DISABLE_UWA_SDK && !UNITY_EDITOR
        UWAEngine.Upload(callback, Mc.Profiler.UwaApi._settings.User, Mc.Profiler.UwaApi._settings.Password, Mc.Profiler.UwaApi._settings.ProjectName, Mc.Profiler.UwaApi._settings.TimeLimitS);
#else
        callback?.Invoke(true, string.Empty);
#endif

    }
    public static void UpLoadUwaCallBack_state(bool success, string msg)
    {
        uwaState = 4;
    }
    public static string UWAUploadState()
    {
        if(uwaState == 0)
        {
            return "uwa测试未开始";
        }
        if (uwaState == 1)
        {
            return "uwa测试进行中";
        }
        if (uwaState == 2)
        {
            return "uwa测试结束等待上传";
        }
        if (uwaState == 3)
        {
            return "uwa测试结果上传中";
        }
        if (uwaState == 4)
        {
            return "uwa测试结果上传完成";
        }
        return "uwa测试未开始";
    }
    public static void SetUWATag(string tag)
    {
#if !DISABLE_UWA_SDK
        UWAEngine.Tag(tag);
#endif
        if (EnableDevProfiler)
        {
            SetProfilerEnd();
            SetProfilerBegin(tag);
        }

    }
    public static void SetProfilerClear()
    {
        ProfilerApi.SetAllEnd();
    }
    public static string UploadPos(float posX, float posZ)
    {
#if !DISABLE_UWA_SDK
        UWAEngine.LogValue("PlayerPosX", posX);
        UWAEngine.LogValue("PlayerPosY", posZ);
        return "上传坐标成功";
#else
        return string.Empty;
#endif
    }

    public static string SetUiProfilerThrs(float mgrUiCostTimeThr, float fps1UpdateCostTimeThr, float uiEventcostTimeThrMs) 
    {
        Mc.Profiler.SetUIProfilerMgrUiCostTimeThr(mgrUiCostTimeThr);
        Mc.Profiler.SetUIProfilerFps1UpdateCostTimeThr(fps1UpdateCostTimeThr);
        Mc.Profiler.SetUiEventProfilerCostTimeThr(uiEventcostTimeThrMs);
        return "UiProfiler设置阈值成功";
    }

    public static Dictionary<string,string> devProfilerPaths = new Dictionary<string, string>();
    public static void SetProfilerBegin(string tag)
    {
#if DEVELOPMENT_BUILD
        if(devProfilerPaths.ContainsKey(tag))
        {
            return;
        }
        Profiler.maxUsedMemory = 256 * 1024 * 1024;
        string date = DateTime.Now.ToLocalTime().ToString("yy-MM-dd-HH-mm-ss");
        Profiler.logFile = Path.Combine(PerformanceData.InitCachePath(), string.Format("{0}_runtimeprofiler_{1}.data", SystemInfo.deviceName,date));
        devProfilerPaths.Add(tag,Profiler.logFile);
        Profiler.enableBinaryLog = true;
        Profiler.enabled = true;
#endif
    }
    public static void SetProfilerEnd()
    {
#if DEVELOPMENT_BUILD
        var curpath = Profiler.logFile;
        Profiler.enabled = false;
        Profiler.logFile = "";
#endif
    }

    public static void StartUITimes()
    {
        Mc.Ui.ClearWinOpenCostRec();
    }

    public static string EndUITimes()
    {
        Dictionary<string, float> info = Mc.Ui.GetWinOpenCostRec();
        //初始化目录存储
        string root_folder = PerformanceData.InitCachePath();
        string date = DateTime.Now.ToLocalTime().ToString("yy-MM-dd-HH-mm-ss");
        string log_file = Path.Combine(root_folder, string.Format("WinOpenCostRec_{0}.txt", date));

        string readText = "";
        Utf16ValueStringBuilder sb = new Utf16ValueStringBuilder(false);
        try
        {
            foreach (var item in info)
            {
                sb.AppendFormat("{0} : {1}\n", item.Key, item.Value);
            }
            readText = sb.ToString();
        }
        finally
        {
            sb.Dispose();
        }
        using (StreamWriter sw = new StreamWriter(log_file))
        {
            sw.Write(readText);
        }
        return readText;
    }

    public static void UploadRecord(string stepname)
    {
        string log_file = Path.Combine(PerformanceData.InitCachePath(), string.Format("profile_{0}.txt", stepname));
        using (var client = new HttpClient())
        {
            var fileInfo = new FileInfo(log_file);
            var fileData = File.ReadAllText(log_file);
            var time = string.Format("Weekly_profiler/{0}/{1}/{2}", DateTime.Now.ToString("yyyy"), DateTime.Now.ToString("MM"), DateTime.Now.ToString("yyyyMMdd")); ;
            var fileName = string.Format("{0}.txt",fileInfo.Name.Replace(".txt", ""));

            var metadata = new Metadata();
            metadata.FileName = fileName;
            metadata.FileDevice = SystemInfo.deviceName;
            metadata.FileData = fileData;
            metadata.FileTime = time;
            var metadataJson = JsonUtility.ToJson(metadata);
            //var metadataJson = JsonConvert.SerializeObject(metadata);
            var Content = new StringContent(metadataJson, System.Text.Encoding.UTF8, "application/json");

            client.Timeout = TimeSpan.FromMinutes(5);
            var result = client.PostAsync("http://**************:9021", Content).Result;
            if (!result.IsSuccessStatusCode)
            {
                logger.ErrorFormat("{0}上传数据失败{1}", fileName, result.StatusCode);
            }
        }
    }

    //自动化流程相关，上传部分为测试结束阶段，不影响性能，有大量数据拼接，防止主线程卡住，在多线程中处理
    //上传操作为异步，poco等待时允许多次发送校验信息，等待数据处理完毕，结束完整测试流程
    public static List<string> StepNames = new List<string>();
    public static string deviceName = SystemInfo.deviceName;
    public static string timepathfirst = "";
    public static string timepathsecond = "";
    static bool IsUploading = false;
    static string filedata_dc = "";
    static string filedata_error = "";
    static string filedata_averagefps = "";
    static string filedata_memory = "";
    static string filedata_common = "";
    static string filedata_UI = "";
    static string filedata_fps = "";
    static string device_str = "";
    static Task<string> UpLoadResult;
    public static Dictionary<string,int> profilertag_frameindesdic = new Dictionary<string, int>();
    static bool isUWAUploadend = false;
    static string root_folder = "";
    static string pool_monitor_folder = string.Empty;
    static string spike_snapshot_folder = string.Empty;
    static int framerate = 0;

    public static bool EnableDelayedTaskProfiler = false;
    public static bool EnableUiProfiler = false;
    public static bool EnableDevProfiler = false;

    public static string StartPerformanceData(string casename,string stepname,int uwamode,string devicename = "")
    {
        if(SocAutoTestState >= ESocAutoTestState.start)
        {
            return "测试正在进行中，无需重复开启";
        }
        if(string.IsNullOrEmpty(stepname))
        {
            return "自动化启动未传入初始标签名，请检查用例是否正确";
        }

        timepathfirst = string.Format("{0}/{1}/{2}/{3}", DateTime.Now.ToString("yyyy"), DateTime.Now.ToString("MM"), DateTime.Now.ToString("yyyyMMdd"), casename);
        timepathsecond = DateTime.Now.ToString("HHmmss");
        deviceName = string.IsNullOrEmpty(devicename) ? SystemInfo.deviceName: devicename;
        deviceName = string.Format("{0}/{1}", deviceName.Replace(" ","_"), timepathsecond);
        root_folder = PerformanceData.InitCachePath();
        SocAutoTestState = ESocAutoTestState.start;
        StepNames.Clear();
        framerate = Application.targetFrameRate;

#if NEWLOG
        NoGcLogRecieveThread.Open();//日志接收线程
#endif
        var go = new GameObject("AutoTest");
        var mgr = go.AddComponent<AutoTestMono>();
        GameObject.DontDestroyOnLoad(go);

        PerformanceDataManager.StartPerfMonitor();
        StartUWA(casename, uwamode);
        PerformanceData.StartFrmaeCount(stepname);
        SetUWATag(stepname);
#if ENABLE_SPIKE_SNAPSHOT
        Mc.Profiler.Snapshot.SetDirectoryName(casename);
        spike_snapshot_folder = Mc.Profiler.Snapshot.SpikeDir;
#endif
        PoolMonitor.Instance.SetFileDirectoryName(casename);
        pool_monitor_folder = PoolMonitor.Instance.FileDirectory;
        StartUITimes();
#if ENABLE_PERF_SIGHT
        Mc.PerfSight.NeedAutoFin = false;
        Mc.PerfSight.BeginLevel(casename);
        Mc.PerfSight.ChangeTag(stepname,MgrPerfSight.ETagType.stepTag);
        if (Mc.Indicator != null)
        {
            Mc.Indicator.IndicatorEnabled = true;
            Mc.Indicator.SnapshotPool.IsEnabled = true; // 上传缓存池计数
        }
#endif
        SocPerfTestUtil.isGetSkyPerInfo = true;

        profilertag_frameindesdic.Add(string.Format("profiler_{0}", stepname), PerformanceData.frameindex);
        StepNames.Add(stepname);
        return "自动化性能数据采集开始";
    }
    public static string SetPerformanceDataTag(string tag)
    {
        SocAutoTestState = ESocAutoTestState.settag;
        if (StepNames.Count < 1)
        {
            //开启阶段未标记tag，属于非自动化分阶段流程，此处不处理
            return "开启阶段未标记tag，属于非自动化分阶段流程";
        }
        string lasttag = StepNames[StepNames.Count - 1];
        if (lasttag == tag)
        {
            //重复标记tag，不处理，默认为是断线重发
            return "重复标记tag,确认是否断线重发";
        }
        SetUWATag(tag);
        PerformanceData.GetstepaverageFps(tag,lasttag);//tag-1

        if(EnableDelayedTaskProfiler)
            Mc.Profiler.EnableDelayedTaskProfiler(false);
        if (EnableUiProfiler)
            Mc.Profiler.EnableUIProfiler(false);

        ProfilerApi.EndRecord(false, false, lasttag);//tag-1
        // 输出缓存池计数分析文件
        PoolElementCountWriter.Write("pool_" + lasttag, PerformanceData.frameindex);
        if (EnableDelayedTaskProfiler)
            Mc.Profiler.EnableDelayedTaskProfiler(true);
        if (EnableUiProfiler)
            Mc.Profiler.EnableUIProfiler(true);

        profilertag_frameindesdic.Add(string.Format("profiler_{0}", tag), PerformanceData.frameindex);
        StepNames.Add(tag);
#if ENABLE_PERF_SIGHT
        Mc.PerfSight.ChangeTag(tag, MgrPerfSight.ETagType.stepTag);
#endif
        return "tag标记成功";
    }
    public static string SetPerformanceDataClearTag()
    {
        ProfilerApi.BeginRecord(false);
        return "自动化性能数据中间步骤起始";
    }
    public static string EndPerformanceData()
    {
        if (SocAutoTestState >= ESocAutoTestState.end)
        {
            return "测试已经结束，无需重复结束";
        }
        SocAutoTestState = ESocAutoTestState.end;
        PerformanceDataManager.StopPerfMonitor();

        string lasttag = StepNames[StepNames.Count - 1];
        PerformanceData.GetstepaverageFps("",lasttag);

        if (EnableDelayedTaskProfiler)
            Mc.Profiler.EnableDelayedTaskProfiler(false);
        if (EnableUiProfiler)
            Mc.Profiler.EnableUIProfiler(false);

        ProfilerApi.EndRecord(false, true, lasttag);
        // 输出缓存池计数分析文件
        PoolElementCountWriter.Write("pool_" + lasttag, PerformanceData.frameindex);
        filedata_UI = EndUITimes();
        

        EndUWA();
#if !UNITY_IOS
        UploadUWA(UpLoadUwaCallBack);
#endif
#if ENABLE_PERF_SIGHT
        Mc.PerfSight.EndLevel();
        if (Mc.Indicator != null)
        {
            Mc.Indicator.IndicatorEnabled = false;
            Mc.Indicator.SnapshotPool.IsEnabled = false;
        }
#endif
        SocPerfTestUtil.isGetSkyPerInfo = false;
        WriteData();
        return "自动化性能数据采集结束";
    }
    public static string UploadUwaDataForIos()
    {
#if UNITY_IOS
        UploadUWA(UpLoadUwaCallBack);
        return "自动化性能数据采集IOS_UWA数据上传";
#endif
        return "非ios无需上传";
    }
    static List<Dictionary<string, long>> sendMsg = new List<Dictionary<string, long>>();
    public static void WriteData()
    {
        //dc
        sendMsg = new List<Dictionary<string, long>>(SocPerfTestUtil.SkyPerforInfoList);
        //dc
        filedata_dc = JsonConvert.SerializeObject(sendMsg);

        //other 先获取数据写入设备存档
        filedata_error = LogCollector.Instance.errorCount.ToString();
        filedata_averagefps = JsonConvert.SerializeObject(PerformanceData.GetResultInfo());
        filedata_fps = framerate.ToString();

        device_str = GetDeviceInfo();

        try
        {
            var dc_file = Path.Combine(root_folder, string.Format("{0}_DC", timepathsecond));
            if (File.Exists(dc_file))
            {
                File.Delete(dc_file);
            }
            using (FileStream file = File.Open(dc_file, FileMode.Create, FileAccess.Write))
            {
                StreamWriter writer = new StreamWriter(file);
                writer.Write(filedata_dc);
                writer.Flush();
                writer.Close();
                file.Close();
            }
        }
        catch (Exception e)
        {
            logger.ErrorFormat("DC数据写入文件失败", e.Message);
        }

        try
        {
            var other_file = Path.Combine(root_folder, string.Format("{0}_Other", timepathsecond));
            if (File.Exists(other_file))
            {
                File.Delete(other_file);
            }
            using (FileStream file = File.Open(other_file, FileMode.Create, FileAccess.Write))
            {
                StreamWriter writer = new StreamWriter(file);
                writer.WriteLine(filedata_averagefps);
                writer.WriteLine(filedata_error);
                writer.WriteLine(filedata_fps);
                writer.Flush();
                writer.Close();
                file.Close();
            }
        }
        catch (Exception e)
        {
            logger.ErrorFormat("Other数据写入文件失败", e.Message);
        }

        try 
        {
            var other_file = Path.Combine(root_folder, string.Format("{0}_DeviceInfo.json", timepathsecond));
            if (File.Exists(other_file))
            {
                File.Delete(other_file);
            }
            using (FileStream file = File.Open(other_file, FileMode.Create, FileAccess.Write))
            {
                StreamWriter writer = new StreamWriter(file);
                writer.WriteLine(device_str);
                writer.Flush();
                writer.Close();
                file.Close();
            }
        }
        catch(Exception e)
        {
            logger.ErrorFormat("Device数据写入文件失败", e.Message);
        }
    }
    public static string GetDeviceInfo()
    {
        var deviceInfo = Mc.DeviceLevel.DeviceInfo;
        Dictionary<string, string> deviceDic = new Dictionary<string, string>() {
            {"device_name", deviceInfo.DeviceName},
            {"device_model", deviceInfo.DeviceModel},
            {"CPU", deviceInfo.ProcessorType},
            {"GPU", deviceInfo.GraphicsName},
            {"机型分档", Mc.DeviceLevel.DeviceLevel.ToString()},
        };

        return JsonConvert.SerializeObject(deviceDic);
    }

    public static string GetPerformanceDataState()
    {
        return SocAutoTestState.ToString();
    }
    public static string UploadPerformanceData()
    {
        if (SocAutoTestState == ESocAutoTestState.upload)
        {
            return "已经开始数据上传中....请稍等";
        }
        if (SocAutoTestState != ESocAutoTestState.end)
        {
            return "测试未结束，请在结束后发起上传数据的请求";
        }
        SocAutoTestState = ESocAutoTestState.upload;

        var uploadThread = new Thread(StartUpload);
        uploadThread.Name = "UploadThread";
        uploadThread.Start();

        return "数据开始上传了...";
    }
    public static void UpLoadUwaCallBack(bool success, string msg)
    {
        isUWAUploadend = true;
    }
    public static void StartUpload()
    {
        UpLoadResult = UploadPerformanceDataAsync();
    }
    public static string GetUploadPerformanceDataState()
    {
        if (UpLoadResult == null)
        {
            if (SocAutoTestState == ESocAutoTestState.end)//如果测试结束了，但是数据还未上传，自动发起上传数据请求
            {
                UploadPerformanceData();
            }
            else if (SocAutoTestState == ESocAutoTestState.upload)//如果已经是上传状态，逻辑出问题了 可以等等再请求下数据
            {
                return "数据上传中....请稍等";
            }
            return "未发起上传数据请求";
        }
        if (isUWAUploadend == false)//uwa上传未结束
        {
            return "数据上传中....请稍等";
        }
        if (SocAutoTestState == ESocAutoTestState.uploadend)//uwa上传未结束
        {
            return "上传已经完成了";
        }
        if (UpLoadResult.IsCompleted)
        {
            string result = UpLoadResult.Result;
            SocAutoTestState = ESocAutoTestState.uploadend;
            if (result.Contains("timed out"))
            {
                return "测试结果：数据连接超时了，去文件服找找数据";
            }
            if(result == "数据上传中....请稍等")
                return result;
            return $"测试结果：{result}";
        }
        return "数据上传中....请稍等";
    }
    public static async Task<string> UploadPerformanceDataAsync()
    {
        if (IsUploading)
        {
            return "数据上传中....请稍等";
        }
        IsUploading = true;
        string resulturl = "";
        try
        {
            StringBuilder sb = new StringBuilder();
            //StringBuilder sb_sql = new StringBuilder();
            //sb_sql.Append("{\"case_data\": [");

            if (sendMsg.Count <= 0)
                sendMsg = new List<Dictionary<string, long>>(SocPerfTestUtil.SkyPerforInfoList);
            //dc
            if (string.IsNullOrEmpty(filedata_dc))
            {
                //dc
                filedata_dc = JsonConvert.SerializeObject(sendMsg);
            }

            Dictionary<string, List<long>> dc = new Dictionary<string, List<long>>();
            foreach (var item in sendMsg)
            {
                foreach (var key in item.Keys)
                {
                    if (dc.ContainsKey(key))
                    {
                        dc[key].Add(item[key]);
                    }
                    else
                    {
                        dc.Add(key, new List<long> { item[key] });
                    }
                }
            }

            //other 先获取数据写入设备存档
            if (string.IsNullOrEmpty(filedata_error))
                filedata_error = LogCollector.Instance.errorCount.ToString();
            if (string.IsNullOrEmpty(filedata_averagefps))
                filedata_averagefps = JsonConvert.SerializeObject(PerformanceData.GetResultInfo());
            if (string.IsNullOrEmpty(filedata_fps))
                filedata_fps = framerate.ToString();
            if (string.IsNullOrEmpty(device_str))
                device_str = GetDeviceInfo();

            //开始上传文件服
            //da
            if (!string.IsNullOrEmpty(filedata_dc))
            {
                string dcpath = UpLoadDataAsyncByData(filedata_dc, "DC").Result;
                SocPerfTestUtil.ClearSkyPerforInfoList();
                SetSqlData(ref sb, "DC", dcpath);
                //SetSqlData(ref sb_sql, "DC", dcpath, DealFileData(dc));
            }

            //profiler 
            List<string> path = new List<string>();
            foreach (var step in StepNames)
            {
                string log_file = Path.Combine(root_folder, string.Format("profile_{0}.txt", step));
                path.Add(UpLoadDataAsync(log_file).Result);
            }
            await SplitDataAsync();

            ///获取服务器解析后的数据
            for (int i = 0; i < StepNames.Count; i++)
            {
                string url_profiler = path[i].Replace(".txt", "_analyze.csv");
                string data = GetDataFromFileAsync(url_profiler).Result;
                logger.InfoFormat("url:{0},frameindex:{1},title:{2}", url_profiler, profilertag_frameindesdic[string.Format("profiler_{0}", StepNames[i])], StepNames[i]);
                SetSqlData(ref sb, StepNames[i], url_profiler);
               // SetSqlData(ref sb_sql, string.Format("profiler_{0}", StepNames[i]), url_profiler, SplitCsvData_profiler(data));
            }
            ///获取服务器解析后的数据sum
            if (path.Count > 1)
            {
                string sum_analyze = $"http://*************:9200/{timepathfirst}/{deviceName}/{string.Format("{0}_Sum_analyze.csv", timepathsecond)}";
                SetSqlData(ref sb, "Sum_profiler_analyze", sum_analyze);
                string filedata_sum_analyze = GetDataFromFileAsync(sum_analyze).Result;
               // SetSqlData(ref sb_sql, "Sum_profiler_analyze", sum_analyze, SplitCsvData_profiler(filedata_sum_analyze));
            }
            else if(path.Count == 1)
            {
                string url_profiler = path[0].Replace(".txt", "_analyze.csv");
                string data = GetDataFromFileAsync(url_profiler).Result;
              //  SetSqlData(ref sb_sql, "Sum_profiler_analyze", url_profiler, SplitCsvData_profiler(data));
            }

            //common
            string url = UpLoadDataAsync(PerformanceData.log_file).Result;
            filedata_common = GetDataFromFileAsync(url).Result;
            SetSqlData(ref sb, "common", url);
          //  SetSqlData(ref sb_sql, "common", url, SplitCsvData(filedata_common));

            //memory
            //url = UpLoadDataAsync(PerformanceData.log_file_mem).Result;
            //filedata_memory = GetDataFromFileAsync(url).Result;
            //SetSqlData(ref sb, "memory",url);
           // SetSqlData(ref sb_sql, "memory", url, SplitCsvData_mem(filedata_memory));

            //other
            url = UpLoadDataAsyncByData(filedata_error, "errorcount").Result;
            SetSqlData(ref sb, "errorcount",url);
           // SetSqlData(ref sb_sql, "errorcount", url, filedata_error, true);

            //DeviceInfo
            url = UpLoadDataAsyncByData(device_str, "DeviceInfo").Result;
            SetSqlData(ref sb, "DeviceInfo", url);
          //  SetSqlData(ref sb_sql, "DeviceInfo", url, device_str, true);

            //Application.targetFrameRate 
            url = UpLoadDataAsyncByData(filedata_fps, "fpsrate").Result;
            SetSqlData(ref sb, "fpsrate", url);
           // SetSqlData(ref sb_sql, "fpsrate", url, filedata_fps, true);

            string poolDir = pool_monitor_folder;
            if (!string.IsNullOrEmpty(poolDir) && Directory.Exists(poolDir))
            {
                // 上传缓存池数量分析文件
                logger.Info("开始上传缓存池数量分析文件");
                var poolFileNames = Directory.GetFiles(poolDir, "*.txt");
                for (int i = 0; i < poolFileNames.Length; ++i)
                {
                    var poolUrl = UpLoadDataAsync(poolFileNames[i]).Result;
                    var poolName = Path.GetFileNameWithoutExtension(poolFileNames[i]);
                    SetSqlData(ref sb, poolName, poolUrl);
              //      SetSqlData(ref sb_sql, poolName, poolUrl, "0", true);
                    //logger.InfoFormat("上传数据从:{0}到:{1}", poolFileNames[i], poolUrl);
                }
                logger.Info("完成上传缓存池数量分析文件");
            }
            bool includeSpikeSnapshot = false;
#if ENABLE_SPIKE_SNAPSHOT
            string spike_snapshot_dir = spike_snapshot_folder;
            if (Mc.Profiler.Snapshot.IsEnabled && !string.IsNullOrEmpty(spike_snapshot_dir))
            {
                // 上传毛刺快照
                logger.Info("开始上传毛刺快照");
                string uploadUrl = uploadUrl_4;
                var fileDevice = deviceName + "/JankScreensots";
                var fileNames = Directory.GetFiles(spike_snapshot_dir, "*.png");
                Dictionary<string, string> nameToUrlMap = new Dictionary<string, string>(fileNames.Length);
                for (int i = 0; i < fileNames.Length; i++)
                {
                    var jankUrl = UpLoadFileAsync(fileNames[i], fileDevice, uploadUrl).Result;
                    if (string.IsNullOrEmpty(jankUrl))
                        continue;

                    var name = Path.GetFileNameWithoutExtension(fileNames[i]);
                    nameToUrlMap.Add(name, jankUrl);
                }
                logger.Info("完成上传毛刺截图");

                string jankName = "janks";
                string jankContent = LitJson.JsonMapper.ToJson(nameToUrlMap);
                string jankDictUrl = UpLoadDataAsyncByData(jankContent, jankName).Result;
                
                SetSqlData(ref sb, jankName, jankDictUrl);
                //SetSqlData(ref sb_sql, jankName, jankDictUrl, "0", true);
                logger.Info("完成上传毛刺快照");
                includeSpikeSnapshot = true;
            }
#endif

            url = UpLoadDataAsyncByData(filedata_averagefps, "averagefps").Result;
            SetSqlData(ref sb, "averagefps",url);
            //SetSqlData(ref sb_sql, "averagefps", url, filedata_averagefps, false,true);

            //入库数据上传
           // sb_sql.Append("]}");
           // url = UpLoadDataAsyncByData(sb_sql.ToString(), "sqldata1").Result;
            url = RequestCreateSqlData(includeSpikeSnapshot).Result;
            SetSqlData(ref sb, "sqldata", url);

            resulturl = UpLoadDataAsyncByData(sb.ToString(), "Result").Result;

            //log上传
#if NEWLOG
            NoGcLogRecieveThread.Close();
            if(!string.IsNullOrEmpty(NoGcLogRecieveThread.logFilePath))
                url = UpLoadDataAsync(NoGcLogRecieveThread.logFilePath).Result;
#endif

            string dirPath = "";
            //dev profiler
            foreach (var item in devProfilerPaths)
            {
                string FileName = string.Format("{0}_DEVprofiler.data.raw", item.Key);
                url = UploadFileInChunks(item.Value, FileName).Result;
                dirPath = url.Replace(FileName,"");
                //SetSqlData(ref sb, item.Key, url);
            }
            logger.Info(devProfilerPaths.Count + " ----- " + dirPath);
            //split gc alloc
            if (devProfilerPaths.Count > 0 && !string.IsNullOrEmpty(dirPath))
            {
                using (var httpClient = new HttpClient())
                {
                    var json = $"{{\"dir\": \"{dirPath.Replace("\\", "/").Replace("\"", "\\\"")}\"}}";
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    logger.Info(content.ToString());
                    try
                    {
                        var response = await httpClient.PostAsync("http://*************:8700/analyze", content);
                        var result = await response.Content.ReadAsStringAsync();
                        logger.InfoFormat("服务器返回状态: {0}", response.StatusCode);
                        logger.InfoFormat(result);
                    }
                    catch (Exception ex)
                    {
                        logger.ErrorFormat("请求失败: {0}", ex.Message);
                    }
                }
            }

            //UI
            url = UpLoadDataAsyncByData(filedata_UI, "WinOpenCostRec").Result;
        }
        catch (Exception e)
        {
            return $"数据上传失败{e.Message}/n{e.StackTrace}";
        }

        IsUploading = false;
        return $"数据上传完成:{resulturl}";
    }
    //请求服务器生成入库数据
    public static async Task<string> RequestCreateSqlData(bool includeSpikeSnapshot = false)
    {
        try
        {
            // 检查必要参数是否已设置
            if (string.IsNullOrEmpty(timepathfirst) || string.IsNullOrEmpty(timepathsecond) || string.IsNullOrEmpty(deviceName))
            {
                return "错误：缺少必要的时间路径或设备名称参数";
            }

            if (StepNames == null || StepNames.Count == 0)
            {
                return "错误：缺少步骤名称列表";
            }

            // 构建请求数据
            //var requestData = new
            //{
            //    time_path_first = timepathfirst,
            //    time_path_second = timepathsecond,
            //    device_name = deviceName,
            //    step_names = StepNames.ToArray(),
            //    profiler_frame_indices = profilertag_frameindesdic,
            //    include_spike_snapshot = includeSpikeSnapshot
            //};
            var requestData = new Uploaddata();
            requestData.time_path_first = timepathfirst;
            requestData.time_path_second = timepathsecond;
            requestData.device_name = deviceName;
            requestData.step_names = StepNames.ToArray();
            requestData.profiler_frame_indices = profilertag_frameindesdic.Values.ToArray();
            //requestData.profiler_frame_indices = profilertag_frameindesdic;
            requestData.include_spike_snapshot = includeSpikeSnapshot;

            // 序列化请求数据
            string jsonData = JsonUtility.ToJson(requestData);

            // 发送HTTP请求到Python服务器
            using (var client = new HttpClient())
            {
                client.Timeout = TimeSpan.FromMinutes(2);

                // 设置请求内容
                var content = new StringContent(jsonData, System.Text.Encoding.UTF8, "application/json");

                // 发送POST请求到Python服务器
                string serverUrl = "http://*************:9201/generate";
                var response = await client.PostAsync(serverUrl, content);

                if (response.IsSuccessStatusCode)
                {
                    string responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<dynamic>(responseContent);
                    logger.InfoFormat("SQL数据生成结果: {0}", responseContent);
                    string filenames = string.Format("{0}_{1}.txt", timepathsecond, "sqldata");
                    return $"http://*************:9200/{timepathfirst}/{deviceName}/{filenames}"; ;
                    //if (result.status == "success")
                    //{
                    //    logger.InfoFormat("SQL数据生成成功: {0}", result.message);
                    //    return $"SQL数据生成成功: {result.filename}";
                    //}
                    //else
                    //{
                    //    logger.ErrorFormat("SQL数据生成失败: {0}", result.error);
                    //    return $"SQL数据生成失败: {result.error}";
                    //}
                }
                else
                {
                    string errorContent = await response.Content.ReadAsStringAsync();
                    logger.ErrorFormat("HTTP请求失败: {0} - {1}", response.StatusCode, errorContent);
                    return $"HTTP请求失败: {response.StatusCode} - {errorContent}";
                }
            }
        }
        catch (HttpRequestException ex)
        {
            logger.ErrorFormat("网络请求异常: {0}", ex.Message);
            return $"网络请求异常: {ex.Message}";
        }
        catch (TaskCanceledException ex)
        {
            logger.ErrorFormat("请求超时: {0}", ex.Message);
            return $"请求超时: {ex.Message}";
        }
        catch (Exception ex)
        {
            logger.ErrorFormat("生成SQL数据时发生异常: {0}", ex.Message);
            return $"生成SQL数据时发生异常: {ex.Message}";
        }
    }
    //处理数据为入库的格式
    public static void SetSqlData(ref StringBuilder sb,string title,string url)
    {
        sb.AppendLine($"title:{title},origin_url:{url}");
    }
    public static void SetSqlData(ref StringBuilder sb, string title, string url,string data,bool isinttype=false,bool isend = false)
    {
        string datastr;
        if (isinttype)
        {
            sb.Append("{\"type\": \"int\", \"start_time\": ");
        }
        else
        {
            sb.Append("{\"type\": \"json\", \"start_time\": ");
        }
        if(profilertag_frameindesdic.ContainsKey(title))
        {
            sb.Append(profilertag_frameindesdic[title]);
        }
        else
        {
            sb.Append(0);
        }
        sb.Append(", \"end_time\": ");
        sb.Append(0);
        sb.Append(", \"tags\": [], \"title\": \"");
        sb.Append(title);
        sb.Append("\", \"origin_url\": \"");
        sb.Append(url);
        sb.Append("\", \"data\": ");
        sb.Append(data);
        if(isend)
        { 
            sb.Append("}");
        }
        else
        {
            sb.Append("},");

        }
    }
    //private static readonly HttpClient _client = new HttpClient();
    public static async Task<string> GetDataFromFileAsync(string path)
    {
        //return _client.GetStringAsync(path).Result;
        using (var client = new HttpClient())
        {
            return client.GetStringAsync(path).Result;
        }
    }
    static string uploadUrl_1 = "http://*************:9001";
    static string uploadUrl_4 = "http://*************:9031"; // 用于上传需要转码的文件
    public static async Task<string> UpLoadDataAsync(string filePath,bool restart = false)
    {
        using (var client = new HttpClient())
        {
            var fileInfo = new FileInfo(filePath);
            var fileData = File.ReadAllText(filePath);
            var time = timepathfirst;
            var timespan = timepathsecond;
            var fileNames = string.Format("{0}_{1}.txt", timespan, fileInfo.Name.Replace(".txt", ""));

            var metadata = new Metadata();
            metadata.FileName = fileNames;
            metadata.FileDevice = deviceName;
            metadata.FileData = fileData;
            metadata.FileTime = time;
            //var metadata = new
            //{
            //    FileName = fileName,
            //    FileDevice = deviceName,
            //    FileData = fileData,
            //    FileTime = time
            //};
            var metadataJson = JsonUtility.ToJson(metadata);
            //var metadataJson = JsonConvert.SerializeObject(metadata);
            var Content = new StringContent(metadataJson, System.Text.Encoding.UTF8, "application/json");

            client.Timeout = TimeSpan.FromMinutes(5);
            var result = await client.PostAsync(uploadUrl_1, Content);
            if (!result.IsSuccessStatusCode && !restart)
            {
                await UpLoadDataAsync(filePath, restart);//失败再传一次，传不上就算了
            }
            else if(!result.IsSuccessStatusCode)
            {
                logger.ErrorFormat("{0}上传数据失败{1}", fileNames, result.StatusCode);
            }
            return $"http://*************:9200/{time}/{deviceName}/{fileNames}";

        }
    }
    public static async Task<string> UpLoadDataAsyncByData(string filedata,string name, bool restart = false)
    {
        try
        {
            using (var client = new HttpClient())
            {
                var fileData = filedata;
                var time = timepathfirst;
                var fileName = string.Format("{0}_{1}.txt", timepathsecond, name);

                var metadata = new Metadata();
                metadata.FileName = fileName;
                metadata.FileDevice = deviceName;
                metadata.FileData = fileData;
                metadata.FileTime = time;
                //var metadata = new
                //{
                //    FileName = fileName,
                //    FileDevice = deviceName,
                //    FileData = fileData,
                //    FileTime = time
                //};
                var metadataJson = JsonUtility.ToJson(metadata);
                //var metadataJson = JsonConvert.SerializeObject(metadata);
                if(string.IsNullOrEmpty(metadataJson))
                {
                    return $"{name}_数据为空";
                }

                var Content = new StringContent(metadataJson, System.Text.Encoding.UTF8, "application/json");

                client.Timeout = TimeSpan.FromMinutes(5);
                var result = await client.PostAsync(uploadUrl_1, Content);
                if (!result.IsSuccessStatusCode && !restart)
                {
                    await UpLoadDataAsyncByData(filedata, name, restart);//失败再传一次，传不上就算了
                }
                else if (!result.IsSuccessStatusCode)
                {
                    logger.ErrorFormat("{0}上传数据失败{1}", fileName, result.StatusCode);
                }
                return $"http://*************:9200/{time}/{deviceName}/{fileName}";
            }
        }
        catch (HttpRequestException e)
        {
            logger.ErrorFormat("上传数据失败{0}", e.Message);
            return e.InnerException.Message;
        }
    }
    public static async Task<string> UpLoadFileAsync(string filePath, string fileDevice, string uploadUrl, bool restart = false)
    {
        using (var client = new HttpClient())
        {
            var time = timepathfirst;
            var fileName = Path.GetFileName(filePath);
            var fileData = Convert.ToBase64String(File.ReadAllBytes(filePath));
            var metadata = new Metadata();
            metadata.FileName = fileName;
            metadata.FileDevice = fileDevice;
            metadata.FileData = fileData;
            metadata.FileTime = time;
            var metadataJson = JsonUtility.ToJson(metadata);
            /*var metadata = new
            {
                FileName = fileName,
                FileDevice = fileDevice,
                FileData = fileData,
                FileTime = time,
                ContentType = "image/png" // 添加内容类型
            };
            var metadataJson = JsonConvert.SerializeObject(metadata);*/

            /*var fileData = File.ReadAllText(filePath);
            Dictionary<string, string> metadata = new Dictionary<string, string>(4)
            {
                { "FileData", fileData },
                { "FileDevice", fileDevice },
                { "FileTime", time },
                { "FileName", fileName }
            };
            string metadataJson = LitJson.JsonMapper.ToJson(metadata);*/
            using var content = new StringContent(metadataJson, System.Text.Encoding.UTF8, "application/json");

            client.Timeout = TimeSpan.FromSeconds(10);
            try
            {
                var result = await client.PostAsync(uploadUrl, content);

                if (!result.IsSuccessStatusCode && restart)
                {
                    return await UpLoadFileAsync(filePath, fileDevice, uploadUrl, false);//失败再传一次，传不上就算了
                }
                else if(!result.IsSuccessStatusCode)
                {
                    logger.ErrorFormat("{0}上传数据失败{1}", fileName, result.StatusCode);
                    return string.Empty;
                }
            }
            catch (System.Exception e)
            {
                logger.ErrorFormat("{0}上传数据异常:{1}", fileName, e);
                return string.Empty;
            }

            return $"http://*************:9200/{time}/{fileDevice}/{fileName}";

        }
    }
    public static async Task SplitDataAsync()//profiler split
    {
        using (var client = new HttpClient())
        {
            var metadata = new Metadata();
            metadata.FileData = timepathfirst + "/" + deviceName;
            metadata.FileTime = timepathsecond;

            //var metadata = new
            //{
            //    FileData = timepathfirst + "/" + deviceName,
            //    FileTime = timepathsecond
            //};
            var metadataJson = JsonUtility.ToJson(metadata);
            //var metadataJson = JsonConvert.SerializeObject(metadata);

            var Content = new StringContent(metadataJson, System.Text.Encoding.UTF8, "application/json");

            client.Timeout = TimeSpan.FromMinutes(20);
            await client.PostAsync("http://*************:9003", Content);
        }
    }
    public static string SetPerformanceDataClear()
    {
        if(!isUWAUploadend && SocAutoTestState != ESocAutoTestState.none)
        {
            EndUWA();
        }
        if(UpLoadResult != null)
        {
            UpLoadResult.Dispose();
            UpLoadResult = null;
        }
        StepNames.Clear();
        SocAutoTestState = ESocAutoTestState.none;
        isUWAUploadend = false;
        IsUploading = false;
        EnableDelayedTaskProfiler = false;
        EnableUiProfiler = false;
        return "自动化性能数据清除成功";
    }
    //入库数据字符串拼接
    public static string DealFileData(Dictionary<string, List<string>> data)
    {
        StringBuilder sb = new StringBuilder();
        sb.Append("{");
        int max = data.Keys.Count;
        int index = 0;
        foreach (var item in data)
        {
            index++;
            sb.Append("\"");
            sb.Append(item.Key);
            sb.Append("\": [");
            List<string> strings = item.Value;
            for(int i =0;i< strings.Count;i++)
            {
                var value = strings[i];
                sb.Append($"\"{value}\"");
                if(i< strings.Count-1)
                    sb.Append(",");
            }
            sb.Append("]");
            if (index < max)
            {
                sb.Append(",");
            }
        }
        sb.Append("}");
        return sb.ToString();
    }
    public static string DealFileData(Dictionary<string, List<long>> data)
    {
        StringBuilder sb = new StringBuilder();
        sb.Append("{");
        int max = data.Keys.Count;
        int index = 0;
        foreach (var item in data)
        {
            index++;
            sb.Append("\"");
            sb.Append(item.Key);
            sb.Append("\": [");
            int maxvalue = item.Value.Count;
            int indexvalue = 0;
            foreach (var value in item.Value)
            {
                indexvalue++;
                sb.Append(value);
                if (indexvalue < maxvalue)
                    sb.Append(",");
            }
            sb.Append("]");
            if (index < max)
            {
                sb.Append(",");
            }
        }
        sb.Append("}");
        return sb.ToString();
    }
    public static string SplitCsvData_mem(string filedata)
    {
        string[] lines = filedata.Split('\n');
        Dictionary<string, List<string>> keyValuePairs = new Dictionary<string, List<string>>();
        foreach (var line in lines)
        {
            if (line == "")
                continue;
            string[] items = line.Split(';');
            foreach (var item in items)
            {
                string[] subitems = item.Split(':');
                if (subitems.Length < 2)
                    continue;
                if (keyValuePairs.ContainsKey(subitems[0]))
                {
                    keyValuePairs[subitems[0]].Add(subitems[1]);
                }
                else
                {
                    keyValuePairs.Add(subitems[0], new List<string> { subitems[1] });
                }
            }
        }
        return DealFileData(keyValuePairs);
    }
    static List<string> keys = new List<string>() { "fps", "mem", "cpu", "gpu", "gputime" ,"frameindex"};
    public static string SplitCsvData(string filedata)
    {
        string[] lines = filedata.Split('\n');
        Dictionary<string, List<string>> keyValuePairs = new Dictionary<string, List<string>>();
        for(int i =1;i< lines.Length;i++)
        {
            var line = lines[i];
            if (line == "")
                continue;
            string[] items = line.Split(',');
            if (items.Length < 5)
                continue;
            for(int j = 0;j<keys.Count;j++)
            {
                string key = keys[j];
                if (keyValuePairs.ContainsKey(key))
                {
                    keyValuePairs[key].Add(items[j]);
                }
                else
                {
                    keyValuePairs.Add(key, new List<string> { items[j]});
                }
            }
        }
        return DealFileData(keyValuePairs);
    }
    public static string SplitCsvData_profiler(string filedata)
    {
        string[] lines = filedata.Split('\n');
        Dictionary<string, List<long>> keyValuePairs = new Dictionary<string, List<long>>();
        List<string> firstkeys = new List<string>() //需要维护
        { 
            "Anim", 
            "UI", 
            "Net", 
            "Effect", 
            "Render", 
            "Audio", 
            "Physics", 
            "Battle team", 
            "System team",
            "Pve team",
            "Arch team",
            "PocoMgr",
            "AnimBegin",
            "AnimEnd",
            "AnimSpecial",
            "PhysicsDetails",
            "gcdetailslist",
            "custom"
        };
        StringBuilder sb = new StringBuilder();
        string appdstr = "";
        string allstr = "{";
        for (int i = 1; i < lines.Length; i++)
        {
            var line = lines[i];
            if (line == "")
                continue;
            string[] items = line.Split(',');
            if (items.Length < 2)
                continue;
            var stipvalue = items.Length < 3 ? "0":items[2].Replace("\n", "").Trim();
            if(string.IsNullOrEmpty(stipvalue))
            {
                stipvalue = "0";
            }
            if (string.IsNullOrEmpty(items[1]))
            {
                stipvalue = "0";
            }

            var title = items[0];

            if (title == "PocoMgr")
            {
                title = "poco_mgr";
            }
            if (title == "GpuTime")
            {
                title = "GPUTime";
            }
            if (title == "其他")
            {
                title = "others";
            }

            if (!firstkeys.Contains(items[0]))
            {
                if (title == "Unity_WaitForLastPresent")//特殊处理
                {
                    string item = $"\"{title}\": {{\"ave_cost\": {stipvalue}, \"cost_per_frame\": {items[1].Trim()}}}";
                    allstr += $"\"wait_ldp\": {{\"ave_cost_total_time\": {stipvalue}, \"cost_per_frame_total_time\": {items[1].Trim()},{item}}},";
                    //allstr += $"\"wait_ldp\": {{\"ave_cost_total_time\": {items[1]}, \"cost_per_frame_total_time\": {stipvalue}, \"Unity_WaitForLastPresent\": {{\"ave_cost\": {items[1]}, \"cost_per_frame\": {stipvalue}}},";
                }
                else
                {
                    if (sb.Length == 0)
                    {
                        sb.Append($"\"{title}\": {{\"ave_cost\": {stipvalue}, \"cost_per_frame\": {items[1].Trim()}}}");
                    }
                    else
                    {
                        sb.Append($", \"{title}\": {{\"ave_cost\": {stipvalue}, \"cost_per_frame\": {items[1].Trim()}}}");
                    }
                }
            }
            else
            {
                string scendstr = sb.ToString();
                if (title == "custom")//特殊处理
                {
                    if (string.IsNullOrEmpty(scendstr))
                    {
                        appdstr = $"\"{title}\": {{\"AveValue\": {stipvalue}, \"ValuePerFrame\": {items[1].Trim()}}},";
                    }
                    else
                    {
                        scendstr.Replace("ave_cost", "AveValue").Replace("cost_per_frame", "ValuePerFrame");
                        appdstr = $"\"{title}\": {{\"AveValue_total\": {stipvalue}, \"ValuePerFrame_total\": {items[1].Trim()},{scendstr}}},";
                    }
                }
                else
                {
                    if (string.IsNullOrEmpty(scendstr))
                    {
                        appdstr = $"\"{title}\": {{\"ave_cost_total_time\": {stipvalue}, \"cost_per_frame_total_time\": {items[1].Trim()}}},";
                    }
                    else
                    {
                        appdstr = $"\"{title}\": {{\"ave_cost_total_time\": {stipvalue}, \"cost_per_frame_total_time\": {items[1].Trim()},{scendstr}}},";
                    }
                }
                allstr += appdstr;
                sb.Clear();
                appdstr = "";
            }
        }
        allstr += sb.ToString();
        allstr += "}";
        return allstr;
    }


    //代码覆盖率自动上传
    static Task<string> UpLoadCodeCoverageResult;
    public static void UploadCodeCoverage(string dictpath)
    {
        var arg1 = string.Format("{0}/{1}/{2}/{3}", DateTime.Now.ToString("yyyy"), DateTime.Now.ToString("MM"), DateTime.Now.ToString("yyyyMMdd"), "CodeCoverageResult");
        var arg2 = DateTime.Now.ToString("HHmmss");
        
        UpLoadCodeCoverageResult = UploadFileInChunks_for_CodeCoverage(dictpath, arg1, arg2);
    }
    public static async Task<string> UploadFileInChunks_for_CodeCoverage(string dictpath,string arg1,string arg2)
    {
        if(!Directory.Exists(dictpath))
        {
            return "文件夹不存在";
        }
        using (var client = new HttpClient())
        {
            var form = new MultipartFormDataContent();
            form.Add(new StringContent($"{arg1}/{SystemInfo.deviceName}/{arg2}"), "path");
            foreach (var filePath in Directory.GetFiles(dictpath))
            {
                var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                var fileContent = new StreamContent(fileStream);
                var fileName = Path.GetFileName(filePath);
                form.Add(fileContent, "files", fileName);
            }

            var response = await client.PostAsync("http://**************:9041/upload", form);
            if (response.IsSuccessStatusCode)
            {
                return $"http://**************:9200/{arg1}/{SystemInfo.deviceName}/{arg2}/index.html";
            }
            else
            {
               return $"上传失败:{response.ReasonPhrase}";
            }
            
        }
    }
    public static string GetUploadCodeCoverageState()
    {
        if (UpLoadCodeCoverageResult == null)
        {
            return "未发起上传数据请求";
        }
        if (UpLoadCodeCoverageResult.IsCompleted)
        {
            string result = UpLoadCodeCoverageResult.Result;
            return $"测试结果：{result}";
        }
        return "数据上传中....请稍等";
    }

    //ios内存自动上传与解析
    public static async Task<string> UploadFileInChunks_for_IosMemory(string dictpath, string arg1, string arg2)
    {
        if (!Directory.Exists(dictpath))
        {
            return "文件夹不存在";
        }

        string day = DateTime.Now.ToString("yyyyMMdd");
        using (var client = new HttpClient())
        {
            var form = new MultipartFormDataContent();
            form.Add(new StringContent($"{arg1}/{SystemInfo.deviceName}/{arg2}"), "path");
            foreach (var sencondPath in Directory.GetDirectories(dictpath))
            {
                if (!sencondPath.Contains(day))
                {
                    continue;
                }

                foreach (var filePath in Directory.GetFiles(sencondPath))
                {
                    var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                    var fileContent = new StreamContent(fileStream);
                    var fileName = Path.GetFileName(filePath);
                    form.Add(fileContent, "files", fileName);
                }
            }

            var response = await client.PostAsync("http://*************:9013/upload", form);
            if (response.IsSuccessStatusCode)
            {
                return $"http://*************:9200/{arg1}/{SystemInfo.deviceName}/{arg2}";
            }
            else
            {
                return $"上传失败:{response.ReasonPhrase}";
            }

        }
    }
    public static async Task SplitMemoryDataAsync(string arg1,string arg2)//profiler split
    {
        using (var client = new HttpClient())
        {
            var metadata = new Metadata();
            metadata.FileData = arg1;
            metadata.FileTime = arg2;
            
            var metadataJson = JsonUtility.ToJson(metadata);

            var Content = new StringContent(metadataJson, System.Text.Encoding.UTF8, "application/json");

            client.Timeout = TimeSpan.FromMinutes(5);
            await client.PostAsync("http://*************:9003", Content);
        }
    }

    //截帧获取内存信息
    public static ESocAutoMemroyDevState SocAutoMemroyDevState = ESocAutoMemroyDevState.none;
    static Task<string> UpLoadMemoryDevResult;
    static Task<string> UpLoadMemoryDevResult_release;
    static Task<string> MemoryReleaseStateCounter_;
    public static List<string> snappaths = new List<string>();
    //public static string deviceName_release;
    public static int releaseMemoryProfiler = 0;
    public static bool releaseMemorysnapcomplete = false;

#if UNITY_IOS && Memmory_graph
    public static string serverurl = "http://192.168.209.160:6000";
    public static string bundleId = "SOCClient";
    public static string xcodeProjpath = "";
    public static string clickPos = "762,953";
    public static string saveFolder = "TEST";
    public static bool attachsuc = false;
    public static async Task<string> AttachXcodeAsync()
    {
        try
        {
            using (var client = new HttpClient())
            {
                // 创建请求数据对象
                var requestData = new XcodeAttachRequest
                {
                    bundle_id = bundleId,
                    xcode_proj = xcodeProjpath
                };

                // 序列化为JSON
                string jsonBody = JsonUtility.ToJson(requestData);

                // 创建HTTP内容
                var content = new StringContent(jsonBody, Encoding.UTF8, "application/json");

                // 发送POST请求
                HttpResponseMessage response = await client.PostAsync($"{serverurl}/xcode_attach", content);

                // 读取响应内容
                string responseBody = await response.Content.ReadAsStringAsync();
                if(responseBody.Contains("OK"))
                {
                    attachsuc = true;
                }
                logger.Error(responseBody);
                return responseBody;
            }
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }

    public static async Task<string> SetXcodeConfig(string url, string xcodeprojpath)
    {
        serverurl = url;
        xcodeProjpath = xcodeprojpath;
        attachsuc = false;
        string result = await AttachXcodeAsync();
        saveFolder =  DateTime.Now.ToString("yyMMdd_HHmmss");
        return $"memgraph配置设置成功  serverurl:{serverurl};xcodeProjpath:{xcodeProjpath};result:{result}";
    }
    public static async Task RequestMemoryGraphAsync(string memgraphFileName,string memgraphFileFolder,string casename, string devicename)
    {
        var requestData = new MemgraphRequest
        {
            memgraphFileName = memgraphFileName,
            memgraphFileFolder = memgraphFileFolder
        };

        string jsonBody = JsonUtility.ToJson(requestData);
        var content = new StringContent(jsonBody, Encoding.UTF8, "application/json");

        try
        {
            using (var client = new HttpClient())
            {
                client.Timeout = TimeSpan.FromMinutes(20);
                var response = await client.PostAsync($"{serverurl}/memory_graph", content);
                var responseString = await response.Content.ReadAsStringAsync();

                logger.InfoFormat("Status: {0}",response.StatusCode);
                logger.InfoFormat("Response: {0}",responseString);
                await Task.Delay(1000);
                StartMemoryRelease_memorypro(casename, devicename);
            }
        }
        catch (TaskCanceledException ex)
        {
            logger.ErrorFormat("TaskCanceledException: {0}", ex.ToString());
            if (ex.CancellationToken.IsCancellationRequested)
                logger.Error("请求被主动取消");
            else
                logger.Error("请求超时");
        }
        catch (Exception ex)
        {
            logger.ErrorFormat("Error: {0}", ex.Message);
        }
    }
#endif
    public static async Task<string> StartMemoryRelease(string casename, string devicename)
    {
        if (MemoryReleaseStateCounter_ != null)
        {
            return "前一次内存截取未完成，请等待";
        }
        releaseMemorysnapcomplete = false;
        string result = "内存快照开始";
#if UNITY_IOS && Memmory_graph
        //memorygraph截取 向mac服请求
        await RequestMemoryGraphAsync(casename+".memgraph",saveFolder,casename, devicename);
#else
        result = StartMemoryRelease_memorypro(casename, devicename);
#endif
        return result;

    }
    public static string StartMemoryRelease_memorypro(string casename, string devicename)
    {
        timepathsecond = DateTime.Now.ToString("HHmmss");
        timepathfirst = string.Format("{0}/{1}/{2}/{3}", DateTime.Now.ToString("yyyy"), DateTime.Now.ToString("MM"), DateTime.Now.ToString("yyyyMMdd"), casename); //string.Format("{0}/{1}", DateTime.Now.ToString("yyyyMMdd"), casename);
        deviceName = string.IsNullOrEmpty(devicename) ? SystemInfo.deviceName : devicename;

        deviceName = string.Format("{0}/{1}", deviceName.Replace(" ", "_"), timepathsecond);

        snappaths.Add(PerformanceData.SnapShot());
        releaseMemorysnapcomplete = true;

        return "内存快照开始";
    }
    public static string GetMemoryReleaseState()
    {
        if(!releaseMemorysnapcomplete)
        {
            if(MemoryReleaseStateCounter_ == null)
            {
                MemoryReleaseStateCounter_ = MemoryReleaseStateCounter();
            }
            if (MemoryReleaseStateCounter_.IsCompleted)
            {
                releaseMemorysnapcomplete = true;
                MemoryReleaseStateCounter_ = null;
                return "截取已完成";
            }
            return "截取未完成等待中";
        }
        else
        {
            MemoryReleaseStateCounter_ = null;
            return "截取已完成";
        }
    }
    public static async Task<string> MemoryReleaseStateCounter()
    {
        int count = 0;
        while (!releaseMemorysnapcomplete && count < 20)
        {
            await Task.Delay(TimeSpan.FromMinutes(1));
            count++;
        }
        return "截取已完成";
    }
    public static string StartMemmoryDev(string casename,string devicename,int release = 1)
    {
        if(SocAutoMemroyDevState > ESocAutoMemroyDevState.none)
        {
            return "内存快照正在截取";
        }
        timepathsecond = DateTime.Now.ToString("HHmmss");
        timepathfirst = string.Format("{0}/{1}/{2}/{3}", DateTime.Now.ToString("yyyy"), DateTime.Now.ToString("MM"), DateTime.Now.ToString("yyyyMMdd"), casename); //string.Format("{0}/{1}", DateTime.Now.ToString("yyyyMMdd"), casename);
        deviceName = string.IsNullOrEmpty(devicename) ? SystemInfo.deviceName : devicename;
        //deviceName_release = string.Format("{0}/{1}/raw", deviceName.Replace(" ", "_"), timepathsecond);

        SocAutoMemroyDevState = ESocAutoMemroyDevState.start;
        releaseMemoryProfiler = release;
        if (releaseMemoryProfiler == 0)
        {
#if DEVELOPMENT_BUILD
            deviceName = string.Format("{0}/{1}", deviceName.Replace(" ", "_"), timepathsecond);
#if OOM_TEST
            OOMControl.StartOOM();
#endif
            SocAutoMemroyDevState = ESocAutoMemroyDevState.start;
            StartUWA($"{casename}_devmemory",0);
            snappaths.Add(PerformanceData.SnapShot());
#endif
        }
        else
        {
            deviceName = string.Format("{0}/{1}/raw", deviceName.Replace(" ", "_"), timepathsecond);
        }
        return "内存快照开始";
    }
    public static string DumpMemory(string tag)
    {
#if ENABLE_MALLOC_AUTOHOOK
         Mc.SocMemory.DumpGcMemory(tag);
#endif
        return "内存dump";
    }
    static string memorypath = "";
    public static string StopMemmoryDev()
    {
        if (SocAutoMemroyDevState == ESocAutoMemroyDevState.none)
        {
            return "内存快照未开始";
        }
        if (SocAutoMemroyDevState == ESocAutoMemroyDevState.upload)
        {
            return "内存快照正在结束中，请稍等";
        }
        SocAutoMemroyDevState = ESocAutoMemroyDevState.upload;
        if (releaseMemoryProfiler == 1)
        {
#if ENABLE_MALLOC_AUTOHOOK
            MgrSocMemoryProfiler.EndMallocHook();
#if UNITY_ANDROID || UNITY_IOS
            memorypath = Application.persistentDataPath + "/Memory";
#else
            memorypath = Application.dataPath + "/Memory";
#endif

            var uploadThread = new Thread(UploadMemoryDev_release);
            uploadThread.Name = "UploadMemoryDev_releaseThread";
            uploadThread.Start();
#endif
        }
        else
        {
#if DEVELOPMENT_BUILD
#if OOM_TEST
            OOMControl.EndOOM();
#if UNITY_IOS
            var arg2 = timepathsecond + "/IOSMemory";//DateTime.Now.ToString("HHmmss/IOSMemory");
            var oompath = Application.temporaryCachePath.Replace("Caches", "OOMDetector");
            var url = UploadFileInChunks_for_IosMemory(oompath, timepathfirst, arg2).Result;
            //await SplitMemoryDataAsync(timepathfirst, arg2);//自动解析，缺少符号表
#endif
#endif
            snappaths.Add(PerformanceData.SnapShot());

            EndUWA();
            UploadUWA();

            var uploadThread1 = new Thread(UploadMemoryDev);
            uploadThread1.Name = "UploadMemoryDevThread";
            uploadThread1.Start();
#endif
        }
            return "内存快照上传解析中，请稍等";
    }
    public static void UploadMemoryDev()
    {
        UpLoadMemoryDevResult = UploadFileInChunks(snappaths);
    }
    public static void UploadMemoryDev_release()
    {
        UpLoadMemoryDevResult_release = UploadFileInChunks_release(memorypath);
    }
    public static string GetUploadMemoryDevState(string index)
    {
        if(SocAutoMemroyDevState != ESocAutoMemroyDevState.upload)
        {
            return "未发起上传数据请求";
        }
        if (index == "0")
        {
#if DEVELOPMENT_BUILD
            if (UpLoadMemoryDevResult == null)
            {
                return "数据上传中....请稍等";
            }
            else
            {
                return $"测试结果：{UpLoadMemoryDevResult.Result}";
            }
#endif
            return "没有需要上传的数据";
        }
        else if (index == "1")
        {
            if (UpLoadMemoryDevResult_release == null)
            {
                return "数据上传中....请稍等";
            }
            else
            {
                return $"测试结果：{UpLoadMemoryDevResult_release.Result}";
            }
        }
        else
        {
            return "数据上传中....请稍等";
        }   
        return "数据上传中....请稍等";
    }
    public static string EndMemmoryDev()
    {
        if (SocAutoMemroyDevState == ESocAutoMemroyDevState.none)
        {
            return "内存快照未开始";
        }
        snappaths.Clear();
        SocAutoMemroyDevState = ESocAutoMemroyDevState.none;
        return "内存快照已经结束";
    }
    public static async Task<string> UploadFileInChunks(List<string> filePath)
    {
        string result = "";
        string url = "";
        string dic = $"http://*************:9200/{timepathfirst}/{deviceName}/";
        url = $"{dic},";
        string[] filename = new string[filePath.Count];
        for (int i =0;i<filePath.Count;i++)
        {
            var path= filePath[i];
            var fileInfo = new FileInfo(path);
            var temp = UploadFileInChunks(path, fileInfo.Name).Result;
            filename[i] = fileInfo.Name;
            url += temp + ",";
            url += temp.Replace(".snap", ".snap_split.csv") + ",";
            result += string.Format("{0}/{1}/{2}.", timepathfirst, deviceName, fileInfo.Name);
            if(i<filePath.Count-1)
                result += ",";
        }
        url += $"{dic}{filename[0]}_Compare_{filename[1]}.csv,";
        url += $"{dic}{filename[0]}_OutData_{filename[1]}.csv";
        //split
        using (var client = new HttpClient())
        {
            var Content = new StringContent(result, System.Text.Encoding.UTF8, "application/json");

            try
            {
                client.Timeout = TimeSpan.FromMinutes(3);
                var splitresult = client.PostAsync("http://*************:9103/split", Content).Result;
            }
            catch (Exception e)
            {
                logger.InfoFormat("内存快照对比信息{0}", e.Message);
            }
        }
        await Task.Delay(1000);
        return url;
    }
    public static async Task<string> UploadFileInChunks_release(string dicPath)
    {
        await Task.Delay(10000);//延时1秒，等待文件生成
        int Platformtag = 64;
#if UNITY_ANDROID || UNITY_IOS
        Platformtag = 64;
#else
        Platformtag = 32;
#endif
        string dic = $"{timepathfirst}/{deviceName},{Platformtag}";
        //获取文件夹下所有文件
        string[] filename = Directory.GetFiles(dicPath);
        for (int i = 0; i < filename.Length; i++)
        {
            var path = filename[i];
            var fileInfo = new FileInfo(path);
            var temp = UploadFileInChunks(path, fileInfo.Name).Result;
        }
        //split
        using (var client = new HttpClient())
        {
            var Content = new StringContent(dic.Replace("/raw", ""), System.Text.Encoding.UTF8, "application/json");

            try
            {
                client.Timeout = TimeSpan.FromMinutes(3);
                var splitresult = client.PostAsync("http://*************:9103/split_release", Content).Result;
            }
            catch (Exception e)
            {
                logger.InfoFormat("内存解析信息{0}", e.Message);
            }
        }
        await Task.Delay(1000);
        var resultlink = $"http://*************:9200/{timepathfirst}/{deviceName}/";
        resultlink = resultlink.Replace("/raw", "/memory_translate");
        return resultlink;
    }

    public static async Task<string> UploadFileInChunks(string filePath,string name)
    {
        using (var client = new HttpClient())
        {
            var chunkSize = 1024 * 1024; // 1MB chunk size

            try
            {
                var fileInfo = new FileInfo(filePath);
                var timespan = timepathsecond;
                var fileName = name;
                var fileSize = fileInfo.Length;

                var metadata = new
                {
                    FileName = fileName,
                    FileSize = fileSize,
                    FileDevice = deviceName,
                    FileTime = timepathfirst
                };
                var metadataJson = JsonConvert.SerializeObject(metadata);
                var metadataContent = new StringContent(metadataJson, System.Text.Encoding.UTF8, "application/json");

                var metadataResponse = await client.PostAsync("http://*************:9102/metadata", metadataContent);
                metadataResponse.EnsureSuccessStatusCode();

                using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    var buffer = new byte[chunkSize];
                    int bytesRead;
                    while ((bytesRead = fileStream.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        var content = new ByteArrayContent(buffer, 0, bytesRead);
                        //var formData = new MultipartFormDataContent();
                        //formData.Add(content);
                        var uploadResponse = await client.PostAsync("http://*************:9102/upload", content);
                        uploadResponse.EnsureSuccessStatusCode();
                    }
                }
                return $"http://*************:9200/{timepathfirst}/{deviceName}/{fileName}";
            }
            catch (Exception e)
            {
                return e.Message;
            }
        }
    }

#if POCO
    public static string Clean()
    {
        string cleanResult = $"SocAutoTestManager::StepNames={StepNames?.Count}|snappaths={snappaths?.Count}|profilertag_frameindesdic={profilertag_frameindesdic?.Count}|sendMsg={sendMsg?.Count}|";
        StepNames?.Clear();
        snappaths?.Clear();
        profilertag_frameindesdic?.Clear();

        UpLoadResult = null;
        UpLoadMemoryDevResult = null;

        // 设置字符串为空字符串
        timepathfirst = "";
        timepathsecond = "";
        filedata_dc = "";
        filedata_error = "";
        filedata_averagefps = "";
        filedata_memory = "";
        filedata_common = "";
        filedata_UI = "";
        filedata_fps = "";
        root_folder = "";

        SocAutoMemroyDevState = ESocAutoMemroyDevState.none;
        sendMsg?.Clear();
        return cleanResult;
    }
#endif

}

public class SocPerfTestUtil
{
    public static SocLogger logger = LogHelper.GetLogger(typeof(SocPerfTestUtil));

    public static List<Dictionary<string, long>> SkyPerforInfoList = new List<Dictionary<string, long>> { };
    public static bool isGetSkyPerInfo = false;
    public static bool isGMWin = false;
    public static string GMWinStr = "";

    public static string ClearSkyPerforInfoList()
    {
        string cleanResult = $"Utils::SkyPerforInfoList={SkyPerforInfoList?.Count}|";
        SkyPerforInfoList.Clear();
        return cleanResult;
    }

    public static object PerfDataMonitor(List<string> param)
    {
        object data = null;
        string Type = param[0];
        string note = param[1];
        switch (Type)
        {
            case "open":
                string tag = param.Count > 2 ? param[2] : "";
                string devicename = param.Count > 3 ? param[3] : "";
                int uwamode = param.Count > 4 ? int.Parse(param[4]) : 0;
                data = SocAutoTestManager.StartPerformanceData(note, tag, uwamode, devicename);
                //data = "自动化性能数据采集开始" || "测试正在进行中，无需重复开启";
                break;
            case "close":
                data = SocAutoTestManager.EndPerformanceData();
                //data:"测试已经结束，无需重复结束"|| "自动化性能数据采集结束" ;
                break;
            case "cleartag":
                data = SocAutoTestManager.SetPerformanceDataClearTag();
                //data = "自动化性能数据中间步骤起始";
                break;
            case "tag":
                data = SocAutoTestManager.SetPerformanceDataTag(note);
                //data = "tag标记成功" || "开启阶段未标记tag，属于非自动化分阶段流程" || "重复标记tag,确认是否断线重发";
                break;
            case "upload":
                data = SocAutoTestManager.UploadPerformanceData();//此处为异步上传，不会阻塞主线程。poco可以间隔发送请求进度
                                                                  //data: "数据上传中....请稍等"
                break;
            case "upload_result":
                data = SocAutoTestManager.GetUploadPerformanceDataState();
                //data:"未发起上传数据请求"|"数据上传中....请稍等"|"测试结果：数据连接超时了，去文件服找找数据"|"测试结果：{result}"
                break;
            case "clear":
                //可能存在一个包跑多个用例的情况，手动做一下清理
                data = SocAutoTestManager.SetPerformanceDataClear();
                break;
            case "state":
                //获取当前性能数据采集状态，如果断线或者任意在客户端未关闭或者重启的情况下，都可以通过这个接口获取当前状态
                data = SocAutoTestManager.GetPerformanceDataState();
                break;
            case "EnableDelayedTaskProfiler":
                data = SocAutoTestManager.EnableDelayedTaskProfiler = note == "0" ? false : true;
                break;
            case "EnableUiProfiler":
                data = SocAutoTestManager.EnableUiProfiler = note == "0" ? false : true;
                break;
            case "UWA_IOSUpload":
                data = SocAutoTestManager.UploadUwaDataForIos();
                break;
            case "upload_pos":
                float posX = float.Parse(param.Count > 2 ? param[2] : "");
                float posZ = float.Parse(param.Count > 3 ? param[3] : "");
                data = SocAutoTestManager.UploadPos(posX, posZ);
                break;
            case "UiProfilerThr":
                float mgrUiCostTimeThr = float.Parse(param.Count > 2 ? param[2] : "");
                float fps1UpdateCostTimeThr = float.Parse(param.Count > 3 ? param[3] : "");
                float uiEventcostTimeThrMs = float.Parse(param.Count > 4 ? param[4] : "");
                data = SocAutoTestManager.SetUiProfilerThrs(mgrUiCostTimeThr, fps1UpdateCostTimeThr, uiEventcostTimeThrMs);
                break;
            case "DevprofilerOpen":
                data = SocAutoTestManager.EnableDevProfiler = note == "0" ? false : true;
                break;
            case "uwasetting":
                //PerfDataMonitor
                //参数0：uwasetting
                //参数1:open:1 close:0 upload:2 state:3 config：4
                //参数2：uwamode_ ： Overview = 0,  Mono = 1,  Resources = 2,   Lua = 3,  Gpu = 4,Unset = 5,
                //参数3：Overview 模式具体测试模式，0 表示自定义模式，1 表示极简模式，2 表示CPU模式，3 表示内存模式
                int settingtag = int.Parse(note);
                int uwamode_ = param.Count > 2 ? int.Parse(param[2]) : 0;
                string step = param.Count > 3 ? param[3] : "";
                int overviewmode = param.Count > 4 ? int.Parse(param[4]) : 0;
                if (settingtag == 1)
                {
                    SocAutoTestManager.StartUWA(step, uwamode_);
                    data = "开启uwa测试";
                }
                else if(settingtag == 0)
                {
#if !DISABLE_UWA_SDK
                    UWAEngine.Stop();
#endif
                    data = "结束uwa测试";
                }
                else if(settingtag == 2)
                {
                    SocAutoTestManager.UploadUWA(SocAutoTestManager.UpLoadUwaCallBack_state);
                    data = "上传uwa测试";
                }
                else if (settingtag == 3)
                {
                    data = SocAutoTestManager.UWAUploadState();
                }
                else if (settingtag == 4)
                {
                    string config = $@"{{
                            ""overview.mode"": {overviewmode},
                            ""overview.engine_cpu_stack"": true,
                            ""overview.lua_cpu_stack"": true,
                            ""overview.lua_mem_stack"": true,
                            ""overview.time_line"": true,
                            ""overview.stack_detail"": 1,
                            ""overview.unity_api"": true,
                            ""overview.lua"": true,
                            ""overview.lua_dump_step"": 0,
                            ""overview.resources"": true,
                            ""overview.unity_loading"": true,
                            ""mono.mono_dump_step"": 0,
                            ""resources.unity_loading"": true,
                            ""lua.lua_dump_step"": 0,
                            ""lua.lua_cpu_stack"": true,
                            ""lua.lua_mem_stack"": true,
                            ""gpu.texture_analysis"": true,
                            ""gpu.mesh_analysis"": true,
                            ""screen.enable"": true,
                            ""screen.interval"": 60
                        }}";

#if !DISABLE_UWA_SDK
                    if (!UnityEngine.Debug.isDebugBuild)
                        config = UWASettingPresets.DefaultReleaseUWAConfigWithoutVerson;
                    UWAEngine.SetConfig(config);
#endif
                }
                break;
        }
        string result = data.ToString();
        logger.Info(result);
        Mc.MsgTips.ShowDebugRealtimeWeakTip(result);
        return new ResultInfo("获取完毕", data, true);
    }
    public static object RecordingMonitor(List<string> param)
    {
        object data = null;
#if UNITY_EDITOR
        string Type = param[0];
        switch (Type)
        {
            case "start":
                CodeCoverage.StartRecording();
                data = "CodeCoverage开始录制";
                break;
            case "stop":
                CodeCoverage.StopRecording();
                data = "CodeCoverage结束录制";
                break;
            case "state":
                bool wasgenerated = CodeCoverage.ReportWasGenerated();
                data = wasgenerated ? "CodeCoverage已生成" : "CodeCoverage未生成";
                break;
            case "upload":
                string dictpath = CodeCoverage.GetCoverageReportPath();
                SocAutoTestManager.UploadCodeCoverage(dictpath);
                data = "CodeCoverage上传中";
                break;
            case "uploadstate":
                data = SocAutoTestManager.GetUploadCodeCoverageState();
                break;
        }
        return new ResultInfo("获取完毕", data, true);
#endif
        return new ResultInfo("获取完毕", "非editor暂不支持", false);
    }

    public static object MemoryMonitor(List<string> param)
    {
        object data = null;
        string Type = param[0];
        switch (Type)
        {
            case "start":
                string casename = param[1];
                string devicename = param.Count > 3 ? param[3] : "";
                int isrelease = param.Count > 4 ? int.Parse(param[4]) : 1;
                data = SocAutoTestManager.StartMemmoryDev(casename, devicename, isrelease);
                //data = "内存快照正在截取" || "内存快照开始";
                break;
            case "memgraph_config":
#if UNITY_IOS && Memmory_graph
                string url = param[1];
                string xcodepath = param[2];
                SocAutoTestManager.SetXcodeConfig(url,xcodepath);
                data = "memgraph配置设置中，请等待";
#else
                data = "非Memmory_graph包无需设置配置";
#endif
                break;
            case "memgraph_config_result":
#if UNITY_IOS && Memmory_graph
                if(SocAutoTestManager.attachsuc)
                {
                    data = "memgraph配置设置成功";
                }
                else
                {
                    data = "memgraph配置设置中，请等待";
                }
#else
                data = "非Memmory_graph包";
#endif
                break;
            case "Start_step":
                string casename1 = param[1];
                string devicename1 = param.Count > 3 ? param[3] : "";
                SocAutoTestManager.StartMemoryRelease(casename1, devicename1);
                data = "内存快照开始";
                //data:"前一次内存截取未完成，请等待" || "内存快照开始"
                break;
            case "Start_step_result":
                data = SocAutoTestManager.GetMemoryReleaseState();
                //data:"截取已完成" || "截取未完成等待中"
                break;
            case "dump":
                string tagname = param[1];
                data = SocAutoTestManager.DumpMemory(tagname);
                //data:"内存dump";
                break;
            case "stop":
                data = SocAutoTestManager.StopMemmoryDev();
                //data:"内存快照未开始"|| "内存快照上传解析中，请稍等" ;
                break;
            case "stop_result":
                string resultindex = "0";
                if (param.Count > 1)
                {
                    resultindex = param[1];
                }
                data = SocAutoTestManager.GetUploadMemoryDevState(resultindex);
                //data:"未发起上传数据请求" || "数据上传中....请稍等" || "测试结果：{url}"
                break;
            case "clear":
                data = SocAutoTestManager.EndMemmoryDev();
                //data: "内存快照未开始"|| "内存快照已经结束";
                break;
        }
        string result = data.ToString();
        logger.Info(result);
        Mc.MsgTips.ShowDebugRealtimeWeakTip(result);
        return new ResultInfo("获取完毕", data, true);
        //return new ResultInfo("非dev模式无法获取", "", false);
    }
}

