using FairyGUI;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using WizardGames.Soc.Common.Component;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Entity;
using WizardGames.Soc.Common.Framework.Network;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Share.Framework;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Plant;
using WizardGames.Soc.SocClient.Ui.Utils;

namespace WizardGames.Soc.SocClient.Ui
{
    public enum EPageType
    {
        None = -1,
        Default = 0,
        Seed,
        Water,
        Manure,
        Harvest,
        Hybrid,
        PlantDetail,
    }
    public class UiPlantOperationSubPanel:UiTabBasePanel
    {
        public override ETabType TabType => ETabType.PlantOperation;
        
        private GComponent comWateringBar;
        private GProgressBar wateringBar;

        private GComponent comManualBar;
        private GProgressBar manualBar;
        private GTextField plantBoxWaterTxt;
        private Controller rightWindownCtrl;
        public Controller RightWindownCtrl=>rightWindownCtrl;
        
        private Dictionary<EPageType, UiPlantBasePage> plantPageDic = new Dictionary<EPageType, UiPlantBasePage>();
        public Dictionary<EPageType, UiPlantBasePage> PlantPageDic => plantPageDic;

        private PlantBoxData curPlantBox;
        public PlantBoxData CurPlantBox => curPlantBox;
        
        private EPageType lastPageType = EPageType.None;
        public EPageType LastPageType =>lastPageType;
        
        private bool isRegister;
        
        private PlantEffectEmitter m_effectEmitter;
        
        private PlantModelPreview plantModelPreview;
        public PlantModelPreview PlantModelPreview => plantModelPreview;
        
        public override void OnInit(UiPlantMain uiPlantMain)
        {
            base.OnInit(uiPlantMain);
            
            GComponent gRoot = uiPlantMain.ComRoot.GetChild("content").asCom.GetChild("ComPlantOperationSubPanel").asCom;
            GLoader loader3D = gRoot.GetChild("loader_3d").asLoader;
            plantModelPreview = PlantModelPreview.Create(loader3D,this);
            GGraph fullScreenHolder = gRoot.GetChild("fullScreenEffectHolder").asGraph;
            m_effectEmitter = new PlantEffectEmitter(fullScreenHolder, plantModelPreview.comShowItemModel);

            rightWindownCtrl = gRoot.GetController("rightWindowState");
            rightWindownCtrl.onChanged.Add(OnChangePage);
            
            plantBoxWaterTxt = gRoot.GetChild("comWaterInfo").asCom.GetChild("water_num").asTextField;
            
            comWateringBar = gRoot.GetChild("comWateringBar").asCom;
            comWateringBar.visible = false;
            wateringBar = comWateringBar.GetChild("bar").asProgress;
            
            comManualBar = gRoot.GetChild("comManualBar").asCom;
            manualBar = comManualBar.GetChild("bar").asProgress;
            comManualBar.visible = false;
            
            var defaultCom = gRoot.GetChild("default_page").asCom;
            var plantDefault = UiPlantDefaultPage.Create(defaultCom,this);
            plantPageDic.Add(plantDefault.PageType, plantDefault);
            var seedCom = gRoot.GetChild("seed_page").asCom;
            var plantSeed = UiPlantSeedPage.Create(seedCom,this);
            plantPageDic.Add(plantSeed.PageType, plantSeed);
            var manureCom = gRoot.GetChild("manure_page").asCom;
            var plantManure = UiPlantManurePage.Create(manureCom,this);
            plantPageDic.Add(plantManure.PageType, plantManure);
            var detailCom = gRoot.GetChild("detail_page").asCom;
            var plantDetail = UiPlantDetailPage.Create(detailCom,this);
            plantPageDic.Add(plantDetail.PageType, plantDetail);
            
            isRegister = false;
        }

        public override void Show()
        {
            base.Show();
            
            curPlantBox = Mc.Plant?.GetPlantBoxData(uiPlantMain.curEntityId);
            if (curPlantBox == null)
            {
                UiPlantMain.CloseWindow();
                return;
            }

            RegisterEvent();
            lastPageType = EPageType.None;
            if (rightWindownCtrl != null) 
                rightWindownCtrl.SetSelectedIndex(-1);
            
            plantModelPreview.Show();
            
            ChangePage(uiPlantMain.targetPageType);
            
            RefreshPlants(curPlantBox.EntityId, true);
            OnUpdatePlantWater(curPlantBox.EntityId);

            if (uiPlantMain.curInstanceItem != null && uiPlantMain.targetPageType == EPageType.Seed)
            {
                var seedPage = plantPageDic[EPageType.Seed] as UiPlantSeedPage;
                seedPage.SelectSeed(uiPlantMain.curInstanceItem.Id);
            }
        }
        public override void Hide()
        {
            base.Hide();
            
            if (plantPageDic.TryGetValue(lastPageType, out var lastOp))
            {
                lastOp.Disable();
            }
            m_effectEmitter.OnDisable();
            plantModelPreview.Hide();
            lastPageType = EPageType.None;
            UnRegisterEvent();
        }
        
        public override void OnFps10Update()
        {
            base.OnFps10Update();
            plantModelPreview?.OnFps10Update();
        }
        
        public override void OnDestroy()
        {
            base.OnDestroy();
            plantModelPreview.OnDestroy();
            
            if (plantPageDic != null)
            {
                foreach (var item in plantPageDic.Values)
                {
                    item.Dispose();
                }
                plantPageDic.Clear();
            }

            comWateringBar.visible = false;
            comManualBar.visible = false;
            isRegister = false;
        }
        
        private void RegisterEvent()
        {
            if (isRegister == false)
            {
                plantModelPreview.RegisterEvent();
                Mc.Msg.AddListener<long>(EventDefine.UpdatePlantWater, OnUpdatePlantWater);
                // Mc.Msg.AddListener<EPageType>(EventDefine.OnChangePlantMainPage, ChangePage);
                Mc.Msg.AddListener<long>(EventDefine.UpdatePlantsInfo, OnUpdatePlantsInfo);
                Mc.Msg.AddListener<long, int>(EventDefine.WaterSuccess, OnWaterSuccess);
                Mc.Msg.AddListener<long>(EventDefine.ManureSuccess, OnManualSuccess);
                isRegister = true;
            }
        }
        
        private void UnRegisterEvent()
        {
            if (isRegister)
            {
                plantModelPreview.UnRegisterEvent();
                Mc.Msg.RemoveListener<long>(EventDefine.UpdatePlantWater, OnUpdatePlantWater);
                // Mc.Msg.RemoveListener<EPageType>(EventDefine.OnChangePlantMainPage, ChangePage);
                Mc.Msg.RemoveListener<long>(EventDefine.UpdatePlantsInfo, OnUpdatePlantsInfo);
                Mc.Msg.RemoveListener<long, int>(EventDefine.WaterSuccess, OnWaterSuccess);
                Mc.Msg.RemoveListener<long>(EventDefine.ManureSuccess, OnManualSuccess);
                isRegister = false;
            }
        }
        
        public void OnUpdatePlantWater(long collectionId)
        {
            if (curPlantBox == null || curPlantBox.EntityId != collectionId) return;
            //{0}毫升
            SafeUtil.SafeSetText(plantBoxWaterTxt, SafeUtil.SafeStringFormat(LanguageManager.GetTextConst(LanguageConst.UnitWater), $"{curPlantBox.Water} / {curPlantBox.plantBoxCfg.WaterLimit}"));
            
            plantModelPreview.RefreshPlants();
        }
        
        public void ChangePage(EPageType page)
        {
            if (rightWindownCtrl != null)
            {
                rightWindownCtrl.selectedIndex = (int)page;
            }
        }

        private void OnChangePage()
        {
            if (Enum.TryParse<EPageType>(rightWindownCtrl.selectedIndex.ToString(), out var pageType) && lastPageType != pageType)
            {
                if (plantPageDic.TryGetValue(lastPageType, out var lastOp))
                {
                    lastOp.Disable();
                }
                
                if (plantPageDic.TryGetValue(pageType, out var op))
                {
                    op.Enable(curPlantBox);
                    op.Refresh();
                }

                uiPlantMain.UpdateNavBarTouchableState(pageType);
                plantModelPreview.OnChangePage(pageType);

                lastPageType = pageType;
            }
        }
        private void OnUpdatePlantsInfo(long entityId)
        {
            RefreshPlants(entityId,false);
        }

        public void RefreshPlants(long entityId,bool isRefreshData = false)
        {
            if (curPlantBox != null && curPlantBox.EntityId == entityId)
            {
                curPlantBox = Mc.Plant.GetPlantBoxData(entityId);
                if (isRefreshData)
                {
                    var partEntity = EntityManager.Instance.GetEntity(entityId) as PartEntity;
                    var plantBox = partEntity.GetComponent<PlantBoxComponent>(EComponentIdEnum.PlantBox);
                    curPlantBox.UpdatePlantsWithoutMessage(plantBox);
                }

                plantModelPreview.RefreshPlants(curPlantBox);
                RefreshPage();
            }
        }

        private void RefreshPage()
        {
            if (rightWindownCtrl != null && curPlantBox != null)
            {
                if (Enum.TryParse<EPageType>(rightWindownCtrl.selectedIndex.ToString(), out var pageType))
                {
                    if (plantPageDic.TryGetValue(pageType, out var op))
                    {
                        op.Refresh();
                    }
                }
            }
        }
        
        private void OnWaterSuccess(long collectionId, int value)
        {
            if (collectionId == curPlantBox.EntityId)
            {
                comWateringBar.visible = true;
                wateringBar.value = 0;
                wateringBar.TweenValue(100, 2f).OnComplete(() =>
                {
                    comWateringBar.visible = false;
                    wateringBar.value = 0;
                });
            }

            //播放浇水特效
            m_effectEmitter.PlayWaterEffect();
        }

        private void OnManualSuccess(long collectionId)
        {
            if (curPlantBox != null && curPlantBox.EntityId == collectionId)
            {
                comManualBar.visible = true;
                manualBar.value = 0;
                manualBar.TweenValue(100, 2f).OnComplete(() =>
                {
                    comManualBar.visible = false;
                    manualBar.value = 0;
                });
            }

            //播放施肥特效
            m_effectEmitter.PlayFertilizeEffect();
        }
    }
}
