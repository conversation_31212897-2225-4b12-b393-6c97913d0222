using FairyGUI;
using GraphicsTest;
using System.Collections.Generic;
using System.Diagnostics;
using UnityEngine;
using UnityEngine.InputSystem;
using Utilities;
using WizardGames.Soc.Common.Data;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.Ui;
using Debug = UnityEngine.Debug;
#if UNIT_TEST
using SocRpcTools;
#endif

namespace WizardGames.Soc.SocClient.Control
{
    public partial class MgrControl : MgrControlBase
    {
        /// <summary>
        /// 是否打开移动端视角控制
        /// </summary>
        public bool IsOpenUIViewCtl { get; set; }

        /// <summary>
        /// 是否需要手动关闭移动端视角控制（true是根据IsOpenUIViewCtl判断，false就是关闭）
        /// </summary>
        public bool IsCloseUIViewCtlSelf { get; set; }

        /// <summary>
        /// 小眼睛
        /// </summary>
        public bool IsLittleEyeEnable;

        /// <summary>
        /// 控制系统总开关
        /// </summary>
        public bool ControlEnabled { get; private set; } = false;

        public bool RotateEnabled { get; private set; } = true;

        /// <summary>
        /// 触碰控制开关(影响虚拟按键、joystick、拖动转向控制的开启/禁用)
        /// </summary>
        public bool TouchControlEnabled { get; set; } = false;

        /// <summary>
        /// 按键控制开关(影响鼠标、键盘控制的开启/禁用)
        /// </summary>
        public bool KeyControlEnabled { get; set; } = false;
        public MoveControl MoveControl
        {
            get
            {
               return Mc.InputSystem.MoveControl;
            }
        }

        public RotationControl RotationControl
        {
            get
            {
                return Mc.InputSystem.RotationControl;
            }
        }

        public TouchInput TouchInput
        {
            get
            {
                _touchInput ??= new TouchInput();
                return _touchInput;
            }
        }


        public TouchInput _touchInput = new TouchInput();

        
        /// <summary>
        /// 不能被屏蔽的动作，用于在光标呼出时依然响应
        /// </summary>
        public List<ActionName> UnblockableActionNames = new List<ActionName>();
        public override void Init()
        {
            base.Init();
            switch (Application.platform)
            {
                case RuntimePlatform.WindowsEditor:
                case RuntimePlatform.WindowsPlayer:
                    KeyControlEnabled = true;
                    TouchControlEnabled = true;
                    IsOpenUIViewCtl = false;
                    IsCloseUIViewCtlSelf = false;
                    break;
                case RuntimePlatform.Android:
                case RuntimePlatform.IPhonePlayer:
                    TouchControlEnabled = true;
                    IsOpenUIViewCtl = true;
                    IsCloseUIViewCtlSelf = false;
                    break;
            }
            InitUnblockableActionNames();
            _touchInput.Init();
        }

        public override void Destroy()
        {
            foreach (var cfg in Mc.Tables.TbActionRegister.DataList)
            {
                if (MgrInputSystem.CheckPlatform(cfg))
                {
                    if (cfg.IsGameGroup())
                    {
                        switch (cfg.ControlType)
                        {
                            case EControlType.MainAction:
                                Mc.InputSystem.UnBindActionExecutor(cfg.ActionName, Mc.InputSystem.MainActionControl);
                                break;
                            case EControlType.Move:
                                Mc.InputSystem.UnBindActionExecutor(cfg.ActionName, Mc.InputSystem.MoveControl);
                                break;
                            case EControlType.Rotation:
                                Mc.InputSystem.UnBindActionExecutor(cfg.ActionName, Mc.InputSystem.RotationControl);
                                break;
                        }
                    }
                }
            }
            base.Destroy();
            Mc.InputSystem.RotationControl.OnDestory();
        }
        
        private void InitUnblockableActionNames()
        {
            UnblockableActionNames.Clear();
            UnblockableActionNames.Add(ActionName.CursorState);
        }
        
        public bool  IsActionBlocked()
        {
            return Mc.Ui.BlockInputCount > 0;
        }

        /// <summary>
        /// 绑定动作执行者
        /// </summary>
        protected override void DoBind()
        {
            base.DoBind();
            foreach (var cfg in Mc.Tables.TbActionRegister.DataList)
            {
                if(MgrInputSystem.CheckPlatform(cfg))
                {
                    if (cfg.IsGameGroup())
                    {
                        switch (cfg.ControlType)
                        {
                            case EControlType.MainAction:
                                Mc.InputSystem.BindActionExecutor(cfg.ActionName, Mc.InputSystem.MainActionControl);
                                break;
                            case EControlType.Move:
                                Mc.InputSystem.BindActionExecutor(cfg.ActionName, Mc.InputSystem.MoveControl);
                                break;
                            case EControlType.Rotation:
                                Mc.InputSystem.BindActionExecutor(cfg.ActionName, Mc.InputSystem.RotationControl);
                                break;
                        }
                    }
                }
            }
        }
        private void CollectPcMoveInput()
        {
            if (Mc.InputSystem.CurrentDevice != Device.Pc) return;
            if(!KeyControlEnabled) return;
            //正在输入
            if(Stage.inst.focus is InputTextField) return;
            var inputPos = Mc.InputSystem.InputSystemControl.InputSystemAction.Move.ReadValue<Vector2>();
#if UNITY_EDITOR || STANDALONE_REAL_PC          
            if (inputPos.magnitude == 0 && MoveControl.UiJoystickInput) return;//手游模式ui开启
#endif
    
            if (inputPos.magnitude > 0)
            {
                var angle = Vector2.Angle(inputPos, Vector2.up);
                var length = inputPos.magnitude;
                if ( angle <= 6)
                {
                    inputPos.x = 0;
                    inputPos = inputPos.normalized * length;
                    angle=0;
                }else if ( angle >= 180 - 6)
                {
                    inputPos.x = 0;
                    inputPos = inputPos.normalized * length;
                    angle = 180;
                }else if( (angle <= 90 + 6) && (angle >= 90 - 6))
                {
                    inputPos.y = 0;
                    inputPos = inputPos.normalized * length;
                    angle= inputPos.x> 0 ? 90 : 270;
                }
                else if (inputPos.x < 0)
                {
                    angle = 360 - angle;
                }

                MoveState moveState = MoveState.Run;
                ActionName vertical = ActionName.Mantle;
                ActionName horizontal = ActionName.Mantle;
                if (angle > 280 | angle < 80)
                {
                    horizontal = ActionName.MoveForward;
                }

                if (angle < 350 & angle > 190 )
                {
                    vertical = ActionName.MoveLeft;
                }

                if (angle < 260 & angle > 100)
                {
                    horizontal = ActionName.MoveBackward;
                }

                if (angle < 170 & angle > 10)
                {
                    vertical = ActionName.MoveRight;
                }

                MoveControl.UpdateJoystickData(inputPos,angle, vertical, horizontal, moveState, false, inputPos.magnitude);
            }
            else
            {
                MoveControl.UpdateJoystickData(inputPos,0, ActionName.MoveForward, ActionName.MoveLeft, MoveState.Idle, true, 0);
            }
        }
        

        /// <summary>
        /// 无上限帧数限制，收集操作输入
        /// </summary>
        public virtual void CollectInput()
        {
            if (Mc.UserCmd.NowCmd == null || activedActions.Count == 0)
                return;
            var cmd = Mc.UserCmd.NowCmd;
#if UNIT_TEST //QA的测试代码，跑兼容性脚本用的
            if (Mc.AutoUnit != null)
            {
                SkyAutoTools.SetCmd(ref cmd);
            }
#endif
            //清空冲突标志
            ControlAction.ClearKeyConflict();
            //PC 键鼠和手柄移动输入
            CollectPcMoveInput();
            //刷新当前帧的光标状态
            Mc.Ui.UpdateCursorState();
            //刷新当前帧的最大Group
            HotKeyUtils.UpdateMaxKeyGruop();
            executeList.Clear();
            executeList.AddRange(activedActions);
            //从激活的动作中遍历、执行
            for (int i = 0; i < executeList.Count; i++)
            {
                var action = executeList[i];
                //光标激活时，只响应光标按键
                if (IsActionBlocked() && !UnblockableActionNames.Contains(action.Name))
                    continue;
                action.Execute(cmd);
            }
            //采样渲染帧的Yaw 和Pitch 区分逻辑帧的cmd使用
            // Mc.UserCmd.RenderYaw = cmd.Yaw;
            // Mc.UserCmd.RenderPitch = cmd.Pitch;
            // cmd.ViewPitch = cmd.Pitch;
            // cmd.ViewYaw = cmd.Yaw;
            // cmd.ReloadAmmoId = Mc.UserCmd.ClientReloadAmmoId;
            // cmd.ReloadAmmoNodeId = Mc.UserCmd.ClientReloadAmmoNodeId;
            Mc.Construction.Update(); //建筑交互临时先放这里 todo：@ldm
            Mc.Construction.InputUpdate();
            // SocDebug.ReloadLog("CollectInput：{0}，{1}",cmd.ReloadAmmoId ,Time.frameCount);
            //临时处理
#if UNITY_EDITOR
            if (!IsCloseUIViewCtlSelf && !UiInventory.IsOpen && !UiPlantMain.IsOpen && KeyControlEnabled)
#else
            if (!IsOpenUIViewCtl && !IsCloseUIViewCtlSelf && !UiInventory.IsOpen && !UiPlantMain.IsOpen && KeyControlEnabled)
#endif
            {
                //bool CtrlSwitch = Input.GetKey(KeyCode.LeftAlt) == Mc.Ui.CursorCtrlSwitch; // PC 端按了左alt且未切换视角控制模式或切换了视角控制模式后未按左alt
                bool littleEyeEnable = Mc.MyPlayer.LittleEyeEnable();
                var gamePad = Gamepad.current;
                var v2C = Mc.InputSystem.InputSystemControl.InputSystemAction.Look.ReadValue<Vector2>();
                if (gamePad != null)
                {//手柄输入只有0.1 需要乘一个系数，插上手柄后鼠标输入也会变成0.1
                    if (v2C.magnitude > 1)
                    {
                        v2C.Normalize();
                    }
                    v2C *= 10;
                }
                //临时处理
                if (!littleEyeEnable && !Mc.Ui.CursorVisible && RotateEnabled)
                {
                    // 如果自由相机，则拦截玩家输入，传给自由相机
                    if (Mc.Photo != null && Mc.Photo.IsBlockTpRotationInput())
                    {
                        Mc.Photo.CameraRotate(new Vector2 (v2C.x, v2C.y));
                    }
                    else
                    {
                        Mc.InputSystem.RotationControl.CollectInput(new Vector2(v2C.x, v2C.y), true);
                    }
                }
                Mc.InputSystem.RotationControl.CollectInputLittleEye1(new Vector2(v2C.x, v2C.y));
            }
    
        }

        /// <summary>
        /// 禁用控制系统
        /// </summary>
        public void DisableControl()
        {
            SetActionGroup(EActionGroup.None);
            ControlEnabled = false;
        }

        /// <summary>
        /// 开启控制系统。
        /// 默认开启Normal控制组
        /// </summary>
        /// <param name="actionGroup"></param>
        public void EnableControl(EActionGroup actionGroup = EActionGroup.Normal)
        {
            SetActionGroup(actionGroup);
            ControlEnabled = true;
        }

        public void DisableRotate()
        {
            RotateEnabled = false;
        }

        public void EnableRotate()
        {
            RotateEnabled = true;
        }

        /// <summary>
        /// 注意: 不要在AddHotKey、ComKeyTips的回调中调用，容易无限递归
        /// </summary>
        /// <param name="actionName"></param>
        public void ExecuteActionAtName(ActionName actionName)
        {
            if (Mc.MyPlayer.IsEscapeFromStuckLoading)
                return;
            var action = Mc.InputSystem.GetActionAtName(actionName);
            action?.SetUiKeyPressed();
        }

        /// <summary>
        /// 换成鼠标左键发fire2
        /// </summary>
        /// <param name="mouseClickFire2">左键点击发fire2</param>
        public void ChangeControlMap(bool mouseClickFire2)
        {
            if (mouseClickFire2)
            {
                //ModifyMouseKey(ActionName.Fire1, null, null);
                //ModifyMouseKey(ActionName.Fire2, new List<MouseKeyName> { MouseKeyName.Left },
                //    new List<KeyResponse> { KeyResponse.Press });
                Mc.InputSystem.ModifyUiVirtualKey(ActionName.Fire2, KeyResponse.Press);
            }
            else
            {

                //ModifyMouseKey(ActionName.Fire1, new List<MouseKeyName> { MouseKeyName.Left },
                //   new List<KeyResponse> { KeyResponse.Press });
                //ModifyMouseKey(ActionName.Fire2, null, null);
                Mc.InputSystem.ModifyUiVirtualKey(ActionName.Fire1, KeyResponse.Press);
                // Mc.Control.ModifyUiVirtualKey(ActionName.Fire2, null);
            }

            EnableControl();
        }

        /// <summary>
        /// 禁止右键操作
        /// </summary>
        /// <param name="isBan"></param>
        public void ChangeKeyMapToDefault()
        {
            // ModifyMouseKey(ActionName.Fire1, new List<MouseKeyName> { MouseKeyName.Left },
            //     new List<KeyResponse> { KeyResponse.Press });
            // ModifyMouseKey(ActionName.Fire2, new List<MouseKeyName> { MouseKeyName.Right },
            //     new List<KeyResponse> { KeyResponse.PressDown });
            Mc.InputSystem.ModifyUiVirtualKey(ActionName.Fire1, KeyResponse.Press);
            Mc.InputSystem.ModifyUiVirtualKey(ActionName.Fire2, KeyResponse.PressDown);

            // Mc.Control.EnableControl();
        }


        /// <summary>
        /// 自动奔跑时往前移动不会被屏蔽
        /// </summary>
        public void OnRunLockChanged(bool value)
        {
            if (value)
            {
                UnblockableActionNames.Add(ActionName.MoveForward);
            }
            else
            {
                if (UnblockableActionNames.Contains(ActionName.MoveForward))
                {
                    UnblockableActionNames.Remove(ActionName.MoveForward);
                }
            }
        }
        
#if SOC_CLIENT

        /// <summary>
        /// 释放非实例化对象
        /// </summary>
        protected override void ReleaseNonInstanceAssets()
        {
            if (-1 != RotationControl.ControlCurveInstanceId)
            {
                UtilsLoad.UnloadAssetSync(RotationControl.ControlCurveInstanceId);
            }
        }
#endif

    }
}