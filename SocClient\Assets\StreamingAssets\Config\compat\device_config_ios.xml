<?xml version="1.0" encoding="utf-8"?>
<PlatformConfig version="2002">
	<Config name="-whitedevice">
		<!-- 苹果系列机型 -->
		<ConditionGroup type="and">
			<Condition sub="Brand" op="==" val="Apple"/>
			<ConditionGroup type="or">	
				<Condition sub="DeviceName" op="contains" val="A14"/> <!-- 苹果 12 -->
				<Condition sub="DeviceName" op="contains" val="A15"/> <!-- 苹果 13 -->
				<Condition sub="DeviceName" op="contains" val="A16"/> <!-- 苹果 14 -->
				<Condition sub="DeviceName" op="contains" val="A17 Pro"/> <!-- 苹果 15 Pro -->
				<Condition sub="DeviceName" op="contains" val="A18"/> <!-- 苹果 16 -->
				<Condition sub="DeviceName" op="contains" val="A18 Pro"/> <!-- 苹果 16 Pro -->
				<Condition sub="DeviceName" op="contains" val="M"/> <!-- 苹果 M1 / M2 / M3 /M4-->
			</ConditionGroup>
			<Condition sub="MaxRam" op=">=" val="4200"/> <!-- 内存大于等于6GB -->
		</ConditionGroup>	
	</Config>
</PlatformConfig>