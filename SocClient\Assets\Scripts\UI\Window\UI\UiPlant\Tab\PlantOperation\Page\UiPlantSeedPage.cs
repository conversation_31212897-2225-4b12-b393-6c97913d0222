using FairyGUI;
using System;
using System.Collections.Generic;
using WizardGames.Soc.Common.NodeSystem;
using WizardGames.Soc.Common.CustomType;
using WizardGames.Soc.Common.Unity.Event;
using WizardGames.Soc.Common.Utility;
using WizardGames.Soc.SocClient.Data;
using WizardGames.Soc.SocClient.Manager;
using WizardGames.Soc.SocClient.PersistentData;
using WizardGames.Soc.SocClient.Plant;
using WizardGames.Soc.SocClient.Ui.Utils;
using UnityEngine;
#if POCO
using Cysharp.Text;
#endif
namespace WizardGames.Soc.SocClient.Ui
{
    public class UiPlantSeedPage : UiPlantBasePage
    {
        private static SocLogger logger = LogHelper.GetLogger(typeof(UiPlantSeedPage));
        public override EPageType PageType => EPageType.Seed;

        private GButton closeBtn;
        private GButton seedBtn;

        public UiPlantBox2D uiPlantBox2D;
        private GList seedLst;
        private List<PlantSeedData> seedDataLst;
        private Dictionary<GObject, Dictionary<string, GObject>> seedGODic = new Dictionary<GObject, Dictionary<string, GObject>>();

        private PlantBoxData curPlantBox;
        private bool hasSetSelected;

        private Controller m_seedBtnCtrl;

        private int lastSelectedIndex;

        public static UiPlantBasePage Create(GComponent com,UiPlantOperationSubPanel uiPlantOperationSubPanel)
        {
            UiPlantBasePage rt = new UiPlantSeedPage();
            rt.Init(com,uiPlantOperationSubPanel);
            return rt;
        }

        protected override void OnInit(GComponent com, UiPlantOperationSubPanel uiPlantOperationSubPanel)
        {
            base.OnInit(com, uiPlantOperationSubPanel);
            var plantBox2D = com.GetChild("plant_box").asCom;
            uiPlantBox2D = UiPlantBox2D.Create(plantBox2D, uiPlantOperationSubPanel,EPageType.Seed);

            seedLst = com.GetChild("list").asList;
            if (seedLst != null)
            {
                seedLst.itemRenderer = OnRenderSeedItem;
                seedLst.onClickItem.Set(OnClickSeedLstItemIcon);
            }

            seedBtn = com.GetChild("seed_btn").asButton;
            seedBtn.onClick.Set(OnClickSeedBtn);
            m_seedBtnCtrl = seedBtn.GetController("enable");
            
            closeBtn = com.GetChild("close_btn").asButton;
            closeBtn.onClick.Set(OnClickClose);

        }

        protected override void OnEnable(object data)
        {
            base.OnEnable(data);
            Mc.Plant.ResetPrePlant();
            curPlantBox = data as PlantBoxData;
            hasSetSelected = false;
            RefreshPlants();
            RefreshSeeds();
            Input.multiTouchEnabled = false;
            Mc.Msg.AddListener<long>(EventDefine.PlantSuccess, OnPlantSuccess);
            Mc.Msg.AddListener<int, long, long>(EventDefine.PrePlant, OnPrePlant);
            Mc.Msg.AddListener<int, long>(EventDefine.CancelPrePlant, OnCancelPrePlant);
            Mc.Msg.AddListener<int>(EventDefine.PlantPageSelectedSlot, OnPlantPageSelectedSlot);
        }

        protected override void OnRefresh()
        {
            RefreshPlants();
        }

        protected override void OnDisable()
        {
            Input.multiTouchEnabled = true;
            Mc.Plant.ResetPrePlant();
            Mc.Msg.RemoveListener<long>(EventDefine.PlantSuccess, OnPlantSuccess);
            Mc.Msg.RemoveListener<int, long, long>(EventDefine.PrePlant, OnPrePlant);
            Mc.Msg.RemoveListener<int, long>(EventDefine.CancelPrePlant, OnCancelPrePlant);
            Mc.Msg.RemoveListener<int>(EventDefine.PlantPageSelectedSlot, OnPlantPageSelectedSlot);
            base.OnDisable();
        }

        protected override void OnDispose()
        {
            base.OnDispose();
            uiPlantBox2D?.Release();
        }

        public void SelectSeed(long instanceId)
        {
            for (int i = 0; i < seedDataLst.Count; i++)
            {
                if (seedDataLst[i].Seed.Id == instanceId)
                {
                    seedLst.selectedIndex = i + 1;//因为第一个是不选中任何种子，所以对应的index需要加1
                    seedLst.ScrollToView(seedLst.selectedIndex);
                }
            }
            m_seedBtnCtrl.SetSelectedIndex(seedLst.selectedIndex == 0 ? 0 : 1);
        }

        public void SetLastSelectedIndex(int curIndex)
        {
            uiPlantBox2D.SetLastSelectedIndex(curIndex);
        }

        public int GetPrevSelectedIndex()
        {
            return uiPlantBox2D.lastSelectedIndex;
        }

        private void RefreshPlants()
        {
            uiPlantBox2D?.Refresh(curPlantBox, !hasSetSelected);
            hasSetSelected = true;
        }

        private void RefreshSeeds()
        {
            seedDataLst = Mc.Plant.GetSeeds();
            if (seedDataLst == null) return;

            seedLst.numItems = seedDataLst.Count + 1;
            seedLst.selectedIndex = 0;
            seedLst.ScrollToView(seedLst.selectedIndex);

            m_seedBtnCtrl.SetSelectedIndex(seedLst.selectedIndex == 0 ? 0 : 1);
        }

        private void OnRenderSeedItem(int index, GObject obj)
        {
            if (obj == null) return;
            if (!seedGODic.ContainsKey(obj))
            {
                var comObj = obj.asCom;
                if (comObj == null) return;
                seedGODic.Add(obj, new Dictionary<string, GObject>());
                seedGODic[obj]["name"] = comObj.GetChild("name");
                seedGODic[obj]["seed_drag_com"] = comObj.GetChild("seed_drag_com");
                seedGODic[obj]["no_seed"] = comObj.GetChild("no_seed");
                seedGODic[obj]["pre_plant_mask"] = comObj.GetChild("pre_plant_mask");
                seedGODic[obj]["owned"] = comObj.GetChild("n8");
                seedGODic[obj]["geneTxt"] = comObj.GetChild("geneTxt");
                seedGODic[obj]["num"] = comObj.GetChild("num");
            }
            var objDic = seedGODic[obj];
            if (objDic == null) return;
            var info = index == 0 ? null : (seedDataLst != null && index - 1 < seedDataLst.Count) ? seedDataLst[index - 1] : null;
            if (seedGODic[obj].TryGetValue("name", out var nameTxt) && nameTxt != null)
            {
                if (info != null)
                {
                    SafeUtil.SafeSetText(nameTxt, info.Seed.ItemName);
                }
                else
                {
                    SafeUtil.SafeSetText(nameTxt, LanguageManager.GetTextConst(LanguageConst.PlantNoSeed));
                }
            }
#if POCO
            obj.PocoRegister(ZString.Format("PlantMainSeed{0},PlantMainSeedIndex{1}", info?.Seed.Id, index));
#endif
            if (objDic.TryGetValue("seed_drag_com", out var seedDrag) && seedDrag != null)
            {
                var comSeed = seedDrag as ComItemIcon;
                if (info != null && comSeed != null && info.Seed != null)
                {
                    SafeUtil.SafeSetVisible(comSeed, true);
                    comSeed.SetInstData(info.Seed);
                    comSeed.SetIconSize(new Vector2(140,140));
                    comSeed.SetValue(info.SeedAmount(Mc.Plant.GetPrePlantSeedId(index) == info.Seed.Id).ToString());
                    comSeed.OnIconDragStart = (item) =>
                    {
                        //预种植数量小于等于0时不应该拖拽
                        if (info.SeedAmount() <= 0)
                        {
                            //种子已被用于之前格子的预种植，无法选中
                            Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.PrePlantSeedOccupied);
                            return false;
                        }

                        if (info != null && info.Seed != null)
                        {
                            SelectSeed(info.Seed.Id);

                            item.customData = info.Seed;
                        }
                        return true;
                    };

                    comSeed.SetHorizontalDragSlow();
                }
                else
                {
                    SafeUtil.SafeSetVisible(comSeed, false);
                }
            }

            if (objDic.TryGetValue("no_seed", out var noSeed) && noSeed != null)
            {
                if (info != null && info.Seed != null)
                {
                    SafeUtil.SafeSetVisible(noSeed, false);
                }
                else
                {
                    SafeUtil.SafeSetVisible(noSeed, true);
                }
            }

            if (objDic.TryGetValue("owned", out var owned) && owned != null)
            {
                if (info != null && info.Seed != null)
                {
                    SafeUtil.SafeSetVisible(owned, true);
                }
                else
                {
                    SafeUtil.SafeSetVisible(owned, false);
                }
            }
            if (objDic.TryGetValue("geneTxt", out var geneTxt) && geneTxt != null)
            {
                if (info != null && info.Seed != null)
                {
                    SafeUtil.SafeSetVisible(geneTxt, true);
                    if (seedDrag != null)
                    {
                        var comSeed = seedDrag as ComItemIcon;
                        if (comSeed != null)
                        {
                            SafeUtil.SafeSetText(geneTxt, comSeed.TextSeedGen.text);
                            // comSeed.TextSeedGen.text = "";
                        }
                    }
                }
                else
                {
                    SafeUtil.SafeSetVisible(geneTxt, false);
                }
            }
            if (objDic.TryGetValue("num", out var num) && num != null)
            {
                if (info != null && info.Seed != null)
                {
                    SafeUtil.SafeSetVisible(num, true);
                    if (seedDrag != null)
                    {
                        var comSeed = seedDrag as ComItemIcon;
                        if (comSeed != null)
                        {
                            SafeUtil.SafeSetText(num, comSeed.TextValue.text);
                            // comSeed.TextValue.text = "";
                        }
                    }
                }
                else
                {
                    SafeUtil.SafeSetVisible(num, false);
                }
            }

            if (objDic.TryGetValue("pre_plant_mask", out var prePlantMask) && prePlantMask != null)
            {
                SafeUtil.SafeSetVisible(prePlantMask, false);
            }
        }

        private void OnCancelPrePlant(int slot, long seedId)
        {
            for (var i = 0; i < seedDataLst.Count; ++i)
            {
                if (seedDataLst[i].Seed.Id == seedId)
                {
                    var childIndex = seedLst.ItemIndexToChildIndex(i + 1);
                    var obj = seedLst.GetChildAt(childIndex);
                    var comDict = seedGODic[obj];
                    var icon = comDict["seed_drag_com"] as ComItemIcon;

                    icon.SetValue(seedDataLst[i].SeedAmount(Mc.Plant.GetPrePlantSeedId(slot) == seedId).ToString());
                    
                    RefreshCount(comDict, icon);
                    break;
                }
            }
        }

        private void OnPrePlant(int index, long seedId, long instanceId)
        {
            if (instanceId == 0)
            {
                return;
            }

            //增加点中之后的数量
            for (var i = 0; i < seedDataLst.Count; ++i)
            {
                int childIndex = seedLst.ItemIndexToChildIndex(i + 1);
                var obj = seedLst.GetChildAt(childIndex);
                var comDict = seedGODic[obj];
                ComItemIcon icon = comDict["seed_drag_com"] as ComItemIcon;

                icon.SetValue(seedDataLst[i].SeedAmount(Mc.Plant.GetPrePlantSeedId(index) == seedDataLst[i].Seed.Id).ToString());
                SafeUtil.SafeSetVisible(comDict["pre_plant_mask"], seedDataLst[i].SeedAmount(Mc.Plant.GetPrePlantSeedId(index) == seedDataLst[i].Seed.Id) <= 0);
                RefreshCount(comDict, icon);
            }
        }

        private void RefreshCount(Dictionary<string,GObject> comDict,ComItemIcon icon)
        {
            
            if (comDict.TryGetValue("num", out var num) && num != null)
            {
                if (icon != null)
                {
                    SafeUtil.SafeSetText(num, icon.TextValue.text);
                    // icon.TextValue.text = "";
                }
            }
        }

        private void OnClickClose()
        {
            Mc.Audio.PlayAudioEvent(null, "UI_Click_02");
            uiPlantOperationSubPanel.ChangePage(EPageType.Default);
            // Mc.Msg.FireMsg(EventDefine.OnChangePlantMainPage, EPageType.Default);
        }

        private void OnClickSeedBtn()
        {
            Mc.Audio.PlayAudioEvent(null, "UI_Click_01");
            if (curPlantBox == null) return;
            if (seedLst == null)
            {
                return;
            }

            if (seedLst.selectedIndex == 0)
            {
                //请先选择种子。
                Mc.MsgTips.ShowRealtimeWeakTip(23113);
                return;
            }

            var seed = seedDataLst[seedLst.selectedIndex - 1];
            if (seed == null || seed.Seed == null) return;
            if (uiPlantBox2D == null) return;
            var selectedIdxed = uiPlantBox2D.GetSelected();

            if (selectedIdxed.Count > 0)
            {
                Mc.Plant.RequestPlant(curPlantBox);
                var commonPersistData = Mc.PersistentData.GetGameSaveData<CommonGamePersistentData>(PersistentDataType.Common);
                if (commonPersistData != null && commonPersistData.lastUseSeedId != seed.Seed.BizId)
                {
                    commonPersistData.lastUseSeedId = seed.Seed.BizId;
                    Mc.PersistentData.SaveGameData(PersistentDataType.Common);
                }
            }
        }

        private void OnClickSeedLstItemIcon()
        {
            if (UiItemIconDragDrop.IsDragging) return;
            PlantSeedData seed = null;
            if (seedLst.selectedIndex > 0)
            {
                seed = seedDataLst[seedLst.selectedIndex - 1];

                if (seed.SeedAmount() <= 0)
                {
                    //种子已被用于之前格子的预种植，无法选中
                    Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.PrePlantSeedOccupied);
                    seedLst.selectedIndex = lastSelectedIndex;
                    return;
                }
            }

            if (seedBtn != null)
            {
                m_seedBtnCtrl.SetSelectedIndex(seedLst.selectedIndex == 0 ? 0 : 1);
            }

            if (uiPlantBox2D != null)
            {
                if (seed != null)
                {
                    if (seed.Seed != null)
                    {
                        Mc.Plant.PrePlant(uiPlantBox2D.plantLst.selectedIndex, seed.Seed.BizId, seed.Seed.Id);
                    }
                }
                else
                {
                    Mc.Plant.CancelPrePlant(uiPlantBox2D.plantLst.selectedIndex);
                }
            }
            lastSelectedIndex = seedLst.selectedIndex;
        }

        private void OnPlantSuccess(long collectionId)
        {
            if (curPlantBox != null && curPlantBox.EntityId == collectionId)
            {
                OnClickClose();
            }
        }

        private void OnPlantPageSelectedSlot(int slot)
        {
            if (UiItemIconDragDrop.IsDragging) return;

            if (seedLst != null)
            {
                seedLst.selectedIndex = 0;
                if (seedLst.numItems > 0)
                {
                    seedLst.ScrollToView(seedLst.selectedIndex);
                }
            }
            if (m_seedBtnCtrl != null)
            {
                m_seedBtnCtrl.SetSelectedIndex(0);
            }

            for (var i = 0; i < seedDataLst.Count; ++i)
            {
                var childIndex = seedLst.ItemIndexToChildIndex(i + 1);
                var obj = seedLst.GetChildAt(childIndex);
                var comDict = seedGODic[obj];
                ComItemIcon icon = comDict["seed_drag_com"] as ComItemIcon;
                icon.SetValue(seedDataLst[i].SeedAmount(Mc.Plant.GetPrePlantSeedId(slot) == seedDataLst[i].Seed.Id).ToString());
                SafeUtil.SafeSetVisible(comDict["pre_plant_mask"], seedDataLst[i].SeedAmount(Mc.Plant.GetPrePlantSeedId(slot) == seedDataLst[i].Seed.Id) <= 0);
                
                RefreshCount(comDict, icon);
            }

            var seed = seedLst.selectedIndex > 0 ? seedDataLst[seedLst.selectedIndex - 1] : null;
            if (seed != null)
            {
                if (seed.SeedAmount(Mc.Plant.GetPrePlantSeedId(slot) == seed.Seed.Id) <= 0)
                {
                    //种子已被用于之前格子的预种植，无法选中
                    Mc.MsgTips.ShowRealtimeWeakTip(CommonTipConst.PrePlantSeedOccupied);
                    return;
                }

                if (seed.Seed != null)
                {
                    Mc.Plant.PrePlant(slot, seed.Seed.BizId, seed.Seed.Id);
                }
            }
            else
            {
                Mc.Plant.CancelPrePlant(slot);
            }
        }
    }
}
